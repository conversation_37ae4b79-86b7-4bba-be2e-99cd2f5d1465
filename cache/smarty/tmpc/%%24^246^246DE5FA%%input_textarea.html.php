<?php /* Smarty version 2.6.33, created on 2023-07-13 16:44:05
         compiled from input_textarea.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'math', 'input_textarea.html', 43, false),array('function', 'help', 'input_textarea.html', 69, false),array('modifier', 'default', 'input_textarea.html', 67, false),array('modifier', 'strip_tags', 'input_textarea.html', 85, false),array('modifier', 'escape', 'input_textarea.html', 85, false),)), $this); ?>
<?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone']): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] )): ?><?php echo '100%'; ?><?php elseif (is_numeric ( $this->_tpl_vars['width'] )): ?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => "x - y",'x' => $this->_tpl_vars['width'],'y' => 2), $this);?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>
<?php if ($this->_tpl_vars['index']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo $this->_tpl_vars['height']; ?>
px<?php elseif ($this->_tpl_vars['height']): ?><?php echo $this->_tpl_vars['height']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden'] || ( $this->_tpl_vars['calculate'] > 1 && $this->_tpl_vars['width'] === '0' )): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>

        <textarea
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="areabox<?php if ($this->_tpl_vars['readonly']): ?> readonly<?php endif; ?><?php if ($this->_tpl_vars['class_name']): ?> <?php echo $this->_tpl_vars['class_name']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
      style="<?php if ($this->_tpl_vars['hidden']): ?>display: none;<?php elseif ($this->_tpl_vars['width']): ?>width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      <?php if ($this->_tpl_vars['show_placeholder']): ?>
        placeholder="<?php if ($this->_tpl_vars['show_placeholder'] === 'label'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php elseif ($this->_tpl_vars['show_placeholder'] === 'help'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['help'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>"
      <?php endif; ?>
      <?php if ($this->_tpl_vars['onkeydown'] || ! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?>
        onkeydown="<?php if ($this->_tpl_vars['onkeydown']): ?><?php echo $this->_tpl_vars['onkeydown']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeydown']; ?>
;<?php endif; ?>"
      <?php endif; ?>
      <?php if ($this->_tpl_vars['restrict']): ?>
        onkeypress="return changeKey(this, event, <?php echo $this->_tpl_vars['restrict']; ?>
);"
      <?php elseif ($this->_tpl_vars['onkeypress'] || ! empty ( $this->_tpl_vars['js_methods']['onkeypress'] )): ?>
        onkeypress="<?php if ($this->_tpl_vars['onkeypress']): ?><?php echo $this->_tpl_vars['onkeypress']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeypress'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeypress']; ?>
;<?php endif; ?>"
      <?php endif; ?>
      <?php if ($this->_tpl_vars['onkeyup'] || ! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?>
        onkeyup="<?php if ($this->_tpl_vars['onkeyup']): ?><?php echo $this->_tpl_vars['onkeyup']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeyup']; ?>
;<?php endif; ?>"
      <?php endif; ?>
      <?php if ($this->_tpl_vars['onchange'] || ! empty ( $this->_tpl_vars['js_methods']['onchange'] )): ?>
        onchange="<?php if ($this->_tpl_vars['onchange']): ?><?php echo $this->_tpl_vars['onchange']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onchange'] )): ?><?php echo $this->_tpl_vars['js_methods']['onchange']; ?>
;<?php endif; ?>"
      <?php endif; ?>
      onfocus="highlight(this);<?php if (! empty ( $this->_tpl_vars['js_methods']['onfocus'] )): ?><?php echo $this->_tpl_vars['js_methods']['onfocus']; ?>
<?php endif; ?>"
      onblur="unhighlight(this);<?php if (! empty ( $this->_tpl_vars['js_methods']['onblur'] )): ?><?php echo $this->_tpl_vars['js_methods']['onblur']; ?>
<?php endif; ?>"
      <?php $_from = $this->_tpl_vars['js_methods']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['method'] => $this->_tpl_vars['func']):
?>
        <?php if ($this->_tpl_vars['func'] && $this->_tpl_vars['method'] && $this->_tpl_vars['method'] != 'onkeydown' && $this->_tpl_vars['method'] != 'onkeypress' && $this->_tpl_vars['method'] != 'onkeyup' && $this->_tpl_vars['method'] != 'onchange' && $this->_tpl_vars['method'] != 'onfocus' && $this->_tpl_vars['method'] != 'onblur'): ?>
          <?php echo $this->_tpl_vars['method']; ?>
="<?php echo $this->_tpl_vars['func']; ?>
"
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
      <?php if ($this->_tpl_vars['readonly']): ?> readonly="readonly"<?php endif; ?>
      <?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
    <?php if ($this->_tpl_vars['calculate'] > 0 && $this->_tpl_vars['calculate'] != '2'): ?>
      <button type="button" <?php if ($this->_tpl_vars['readonly'] && $this->_tpl_vars['calculate'] == '1'): ?>name="calc_<?php echo $this->_tpl_vars['name']; ?>
" id="calc_<?php echo $this->_tpl_vars['name']; ?>
"<?php else: ?>name="a_calc_<?php echo $this->_tpl_vars['name']; ?>
" id="a_calc_<?php echo $this->_tpl_vars['name']; ?>
"<?php endif; ?> onclick="calc(this, <?php echo $this->_tpl_vars['var_id']; ?>
)" class="button"<?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?>><?php if ($this->_tpl_vars['description']): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calculate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></button>
    <?php endif; ?>

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => $this->_tpl_vars['custom_id'],'name' => $this->_tpl_vars['name'],'back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>