<?php /* Smarty version 2.6.33, created on 2023-07-13 16:42:48
         compiled from titlebar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'titlebar.html', 35, false),array('modifier', 'escape', 'titlebar.html', 42, false),array('function', 'popup', 'titlebar.html', 42, false),)), $this); ?>
<?php if ($this->_tpl_vars['validLogin']): ?>
          <div class="m_logout">
          <!-- Begin Logout Bar -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'logout.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <!-- End Welcome Bar -->
          </div>
<?php endif; ?>

          <div class="m_welcome">
          <!-- Begin Welcome Bar -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'welcome.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <!-- End Welcome Bar -->
          </div>

          <div class="m_navbar">
          <!-- Begin Navigation Bar -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "navbar.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <!-- End Navigation Bar -->
          </div>

          <!-- Begin Lang Menu -->
          <?php if (is_array ( $this->_tpl_vars['lang_menu'] ) && count ( $this->_tpl_vars['lang_menu'] ) > 1): ?>
            <div class="m_lang_menu<?php if ($this->_tpl_vars['include_keyboard_inputs_toggler']): ?> floatr<?php endif; ?>">
              <?php $_from = $this->_tpl_vars['lang_menu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['supported_lang']):
        $this->_foreach['i']['iteration']++;
?>
                <a href="<?php echo $this->_tpl_vars['supported_lang']['url']; ?>
"<?php if ($this->_tpl_vars['supported_lang']['selected']): ?> class="selected"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['supported_lang']['lang']; ?>
.png" width="16" height="11" border="0" alt="<?php echo $this->_tpl_vars['supported_lang']['i18n']; ?>
" title="<?php echo $this->_tpl_vars['supported_lang']['i18n']; ?>
"<?php if (! $this->_tpl_vars['supported_lang']['selected']): ?> class="dimmed"<?php endif; ?> /></a>
              <?php endforeach; endif; unset($_from); ?>
            </div>
          <?php else: ?>
            &nbsp;
          <?php endif; ?>
          <!-- End Lang Menu -->

          <!-- Begin Alternative Keyboard Change Menu -->
          <?php if ($this->_tpl_vars['include_keyboard_inputs_toggler'] && $this->_tpl_vars['prefered_keyboard_inputs']): ?>
            <div class="langLink<?php if ($_COOKIE['molang']): ?> <?php echo $_COOKIE['molang']; ?>
<?php endif; ?>"><?php echo ((is_array($_tmp=@$_COOKIE['molang'])) ? $this->_run_mod_handler('default', true, $_tmp, 'OFF') : smarty_modifier_default($_tmp, 'OFF')); ?>
</div>
            <input type="hidden" id="prefered_keyboard_inputs" name="prefered_keyboard_inputs" value="<?php echo $this->_tpl_vars['prefered_keyboard_inputs']; ?>
" />
          <?php endif; ?>
          <!-- End Alternative Keyboard Change Menu -->

          <!-- Icon to show if e-mails are turned off -->
          <?php if ($this->_tpl_vars['emails_off']): ?>
            <div class="m_emailing floatr" <?php echo smarty_function_popup(array('text' => $this->_config[0]['vars']['emailing_is_off'],'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
></div>
          <?php endif; ?>

          <div class="m_stopwatchbar" id="m_stopwatchbar">
            <!-- Stopwatch Info Bar -->
            <span id="m_stopwatchbar_box">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "stopwatchbar.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </span>
            <!-- End Stopwatch Bar -->
          </div>

          <div class="m_lockbar" id="m_lockbar">
            <!-- Begin Lock Info Bar -->
            <!--  Loaded with AJAX -->
            <!-- End Lock Bar -->
          </div>