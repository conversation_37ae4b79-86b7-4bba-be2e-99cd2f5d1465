<?php /* Smarty version 2.6.33, created on 2023-07-13 16:43:49
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 13, false),array('modifier', 'numerate', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 72, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 91, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 257, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 257, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 42, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 103, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/view.html', 103, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['document']->get('id'); ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              <?php if ($this->_tpl_vars['document']->checkPermissions('addtimesheet')): ?>
                <div class="abs_div stopwatch_div" style="visibility: hidden;">
                  <button type="button" name="stopwatch" id="stopwatch" class="button" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['stop_watch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="confirmAction('stop_watch', function(el) { stopWatch(el, 'document', <?php echo $this->_tpl_vars['document']->get('id'); ?>
); }, this)" style="<?php if (! $this->_tpl_vars['document']->get('startwatch')): ?>display: none;<?php endif; ?>"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
stopwatch.png" width="16" height="16" alt="" border="0" /></button><button type="button" name="startwatch" id="startwatch" class="button" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['start_watch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="confirmAction('start_watch', function(el) { startWatch(el, 'document', <?php echo $this->_tpl_vars['document']->get('id'); ?>
); }, this)" style="<?php if ($this->_tpl_vars['document']->get('startwatch')): ?>display: none;<?php endif; ?>"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
startwatch.png" width="16" height="16" alt="" border="0" /></button>
                </div>
              <?php endif; ?>
              <?php $this->assign('layouts_vars', $this->_tpl_vars['document']->get('vars')); ?>
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <?php $_from = $this->_tpl_vars['document']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>
                <?php if ($this->_tpl_vars['layout']['view']): ?>

                <?php if ($this->_tpl_vars['layout']['system'] || array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <tr<?php if (! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                    </div>
                    <div class="layout_switch" <?php if ($this->_tpl_vars['layout']['system']): ?>onclick="toggleViewLayouts(this)" id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch"<?php else: ?>onclick="toggleLayouts(this)" id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_switch"<?php endif; ?>>
                      <a name="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                    </div>
                  </td>
                </tr>
                <?php endif; ?>

                <?php if ($this->_tpl_vars['lkey'] == 'status'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <div class="documents_status <?php echo $this->_tpl_vars['document']->get('status'); ?>
">
                      <?php if ($this->_tpl_vars['document']->get('status') == 'opened'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_status_opened'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php elseif ($this->_tpl_vars['document']->get('status') == 'locked'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_status_locked'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php elseif ($this->_tpl_vars['document']->get('status') == 'closed'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_status_closed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['document']->get('substatus_name')): ?>
                        &raquo; <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('substatus_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['document']->checkPermissions('setstatus')): ?>
                      <a href="#" onclick="toggleActionOptions($('setstatus_action')); return false;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'full_num'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['document']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['document']->get('direction'))); ?>

                    <input type="hidden" value="<?php echo $this->_tpl_vars['document']->get('num'); ?>
" name="num" />
                    <input type="hidden" value="<?php echo $this->_tpl_vars['document']->get('full_num'); ?>
" name="full_num" />
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'custom_num'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('custom_num'))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['document']->get('date')): ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
                    <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>
</span>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <a href="<?php echo $this->_tpl_vars['customer_view_link']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                    <?php if ($this->_tpl_vars['document']->get('branch') && $this->_tpl_vars['customer_branch']): ?>
                      <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('documents_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                      <span<?php if (! $this->_tpl_vars['customer_branch']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                    <?php endif; ?>
                    <?php if ($this->_tpl_vars['document']->get('contact_person') && $this->_tpl_vars['contact_person']): ?>
                      <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['documents_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                      <span<?php if (! $this->_tpl_vars['contact_person']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['document']->get('trademark')): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures&amp;nomenclatures=view&amp;view=<?php echo $this->_tpl_vars['document']->get('trademark'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'contract'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['document']->get('contract')): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['document']->get('contract'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('contract_custom_label'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('contract_custom_label'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['project']): ?>
                      <a href="<?php echo $this->_tpl_vars['project_view_link']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['project'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['project'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'office'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['office']): ?>
                      <?php if (! $this->_tpl_vars['office']->isActivated()): ?>
                      <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php echo ((is_array($_tmp=$this->_tpl_vars['office']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['office']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'employee'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['employee']): ?>
                      <?php if (! $this->_tpl_vars['employee']->isDeleted() && $this->_tpl_vars['employee']->isActivated()): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php else: ?>
                        <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*<?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      <?php endif; ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'media'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['media']): ?>
                      <?php if (! $this->_tpl_vars['media']->isActivated()): ?>
                      <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php echo ((is_array($_tmp=$this->_tpl_vars['media']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['media']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'deadline'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['document']->get('deadline')): ?>
                      <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
                        <?php ob_start(); ?>
                          <?php echo $this->_config[0]['vars']['documents_expired_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>
                        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired', ob_get_contents());ob_end_clean(); ?>
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" class="t_info_image" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
                      <?php endif; ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_no_deadline'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'validity_term'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['document']->get('validity_term')): ?>
                      <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
                        <?php ob_start(); ?>
                          <?php echo $this->_config[0]['vars']['documents_expired_validity_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>
                        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired_validity_term', ob_get_contents());ob_end_clean(); ?>
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" class="t_info_image" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_expired_validity_term'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_validity_term_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
                      <?php endif; ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_no_validity_term'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'referers'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['document']->get('referers')): ?>
                      <?php $_from = $this->_tpl_vars['document']->get('referers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
                        <?php echo $this->_foreach['i']['iteration']; ?>
. <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
<?php if ($this->_tpl_vars['ref']['archived_by']): ?>&amp;archive=1<?php endif; ?>" target="_blank"><?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['full_num'])) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['ref']['direction']) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['ref']['direction'])); ?>
&nbsp;&nbsp;<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 (<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['type_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)</a><br />
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['department']): ?>
                      <?php if (! $this->_tpl_vars['department']->isActivated()): ?>
                        <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      <?php else: ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'assign_info' && $this->_tpl_vars['currentUser']->get('is_portal')): ?>
                <?php if (in_array ( 'owner' , $this->_tpl_vars['settings_assign'] )): ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label' => 'assign_owner'), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  <?php $_from = $this->_tpl_vars['document']->get('assignments_owner'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assign']):
        $this->_foreach['i']['iteration']++;
?>
                    <?php echo $this->_foreach['i']['iteration']; ?>
. <?php echo ((is_array($_tmp=$this->_tpl_vars['assign']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
                  <?php endforeach; endif; unset($_from); ?>
                  </td>
                </tr>
                <?php endif; ?>
                <?php if (in_array ( 'responsible' , $this->_tpl_vars['settings_assign'] )): ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label' => 'assign_responsible'), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  <?php $_from = $this->_tpl_vars['document']->get('assignments_responsible'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assign']):
        $this->_foreach['i']['iteration']++;
?>
                    <?php echo $this->_foreach['i']['iteration']; ?>
. <?php echo ((is_array($_tmp=$this->_tpl_vars['assign']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
                  <?php endforeach; endif; unset($_from); ?>
                  </td>
                </tr>
                <?php endif; ?>
                <?php if (in_array ( 'observer' , $this->_tpl_vars['settings_assign'] )): ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label' => 'assign_observer'), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  <?php $_from = $this->_tpl_vars['document']->get('assignments_observer'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assign']):
        $this->_foreach['i']['iteration']++;
?>
                    <?php echo $this->_foreach['i']['iteration']; ?>
. <?php echo ((is_array($_tmp=$this->_tpl_vars['assign']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
                  <?php endforeach; endif; unset($_from); ?>
                  </td>
                </tr>
                <?php endif; ?>
                <?php if (in_array ( 'decision' , $this->_tpl_vars['settings_assign'] )): ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label' => 'assign_decision'), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  <?php $_from = $this->_tpl_vars['document']->get('assignments_decision'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assign']):
        $this->_foreach['i']['iteration']++;
?>
                    <?php echo $this->_foreach['i']['iteration']; ?>
. <?php echo ((is_array($_tmp=$this->_tpl_vars['assign']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
                  <?php endforeach; endif; unset($_from); ?>
                  </td>
                </tr>
                <?php endif; ?>
                <?php elseif (array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <!-- Document Additional Vars -->
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_view_vars.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['lkey'] == 'attachments' && $this->_tpl_vars['currentUser']->get('is_portal') && $this->_tpl_vars['document']->get('attachments')): ?>
                <!-- Document attachments -->
                <tr>
                  <td colspan="3" class="nopadding">
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_attachments.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php endif; ?>

                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
                <?php if ($this->_tpl_vars['document']->get('buttons') || $this->_tpl_vars['document']->get('transform_optgroups')): ?>
                  <tr>
                    <td colspan="3">&nbsp;</td>
                  </tr>

                  <tr>
                    <td colspan="3">
                      <?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('buttons')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['document']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('transform_optgroups')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['document']->get('transform_optgroups'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optgroups']):
?><?php echo ''; ?><?php $_from = $this->_tpl_vars['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo '<input type="button" value="'; ?><?php echo $this->_tpl_vars['button']['label']; ?><?php echo '" name="btn" class="button" onclick="window.open(\''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=transformations&amp;transformations='; ?><?php echo $this->_tpl_vars['document']->get('id'); ?><?php echo '&amp;operation=transform&amp;transform='; ?><?php echo $this->_tpl_vars['button']['option_value']; ?><?php echo '\', \''; ?><?php if ($this->_tpl_vars['button']['settings']['target']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['button']['settings']['target']; ?><?php echo ''; ?><?php else: ?><?php echo '_self'; ?><?php endif; ?><?php echo '\');" />'; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

                    </td>
                  </tr>
                <?php endif; ?>
              </table>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
    </td>
    <?php if (isset ( $this->_tpl_vars['side_panels'] )): ?>
    <td class="side_panel_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_side_panel_options.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."side_panels_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
    <?php endif; ?>
  </tr>
</table>