<?php $_config_vars = array (
  'nomenclatures_counters' => 'Броячи за номенклатури',
  'nomenclatures_counters_name' => 'Име',
  'nomenclatures_counters_formula' => 'Формула',
  'nomenclatures_counters_description' => 'Описание',
  'nomenclatures_counters_next_number' => 'Следващ номер',
  'nomenclatures_counters_count_nomenclatures' => 'Брой номенклатури',
  'nomenclatures_counters_types_used' => 'Използван в типове',
  'nomenclatures_counters_status' => 'Статус',
  'nomenclatures_counters_status_active' => 'Активен',
  'nomenclatures_counters_status_inactive' => 'Неактивен',
  'nomenclatures_counters_added_by' => 'Добавен от',
  'nomenclatures_counters_modified_by' => 'Променен от',
  'nomenclatures_counters_added' => 'Добавен на',
  'nomenclatures_counters_modified' => 'Променен на',
  'nomenclatures_counters_add' => 'Добавяне на брояч за номенклатури',
  'nomenclatures_counters_edit' => 'Редакция на брояч за номенклатури',
  'nomenclatures_counters_view' => 'Разглеждане на брояч за номенклатури',
  'nomenclatures_counters_translate' => 'Превод на брояч за номенклатури',
  'message_nomenclatures_counters_add_success' => 'Данните за брояч бяха добавени успешно!',
  'message_nomenclatures_counters_edit_success' => 'Данните за брояч бяха редактирани успешно!',
  'message_nomenclatures_counters_translate_success' => 'Броячът беше успешно преведен!',
  'error_nomenclatures_counters_edit_failed' => 'Данните за брояч не бяха редактирани успешно:',
  'error_nomenclatures_counters_add_failed' => 'Данните за брояч не бяха добавени:',
  'error_nomenclatures_counters_translate_failed' => 'Броячът не беше успешно преведен:',
  'error_no_such_nomenclature_counter' => 'Нямате възможност да прегледате този запис!',
  'error_no_counter_name_specified' => 'Не сте въвели име!',
  'error_no_counter_formula_specified' => 'Не сте въвели формула!',
  'error_invalid_next_number' => 'Моля, въведете следващ номер за брояча, състоящ се само от цифри, и със стойност по-голяма от 0!',
  'error_no_types_used' => 'не е използван в нито един тип номенклатура',
  'nomenclatures_counters_formula_delimiter' => 'Разделител',
  'nomenclatures_counters_empty_delimiter' => 'без разделител',
  'nomenclatures_counters_formula_leading_zeroes' => 'Брой водещи нули',
  'nomenclatures_counters_formula_date_format' => 'формат',
  'nomenclatures_counters_formula_date_delimiter' => 'с разделител',
  'nomenclatures_counters_formula_date_format_year' => 'гггг',
  'nomenclatures_counters_formula_date_format_year_short' => 'гг',
  'nomenclatures_counters_formula_date_format_month' => 'мм',
  'nomenclatures_counters_formula_date_format_day' => 'дд',
  'nomenclatures_counters_formula_date_format1' => 'гггг',
  'nomenclatures_counters_formula_date_format2' => 'мм/гггг',
  'nomenclatures_counters_formula_date_format3' => 'мм/гг',
  'nomenclatures_counters_formula_date_format4' => 'гггг/мм',
  'nomenclatures_counters_formula_date_format5' => 'гг/мм',
  'nomenclatures_counters_formula_date_format6' => 'дд/мм/гггг',
  'nomenclatures_counters_formula_date_format7' => 'дд/мм/гг',
  'nomenclatures_counters_formula_date_format8' => 'мм/дд/гггг',
  'nomenclatures_counters_formula_date_format9' => 'мм/дд/гг',
  'nomenclatures_counters_formula_date_format10' => 'гггг/дд/мм',
  'nomenclatures_counters_formula_date_format11' => 'гг/дд/мм',
  'nomenclatures_counters_formula_date_format12' => 'гггг/мм/дд',
  'nomenclatures_counters_formula_date_format13' => 'гг/мм/дд',
  'nomenclatures_counters_formula_date_format14' => 'гг',
  'nomenclatures_counters_formula_date_format15' => 'ггг/мм',
  'nomenclatures_counters_formula_legend' => 'Легенда за попълването на формулата на брояча',
  'nomenclatures_counters_formula_prefix' => 'Префикс',
  'nomenclatures_counters_formula_num' => 'Номер на номенклатура',
  'nomenclatures_counters_formula_code_suffix' => 'Суфикс за код',
  'nomenclatures_counters_formula_user_code' => 'Код на потребител',
  'nomenclatures_counters_formula_added' => 'Дата на номенклатура',
  'nomenclatures_counters_formula_prefix_descr' => 'попълва се директно с 2-3 букви, например за артикул ART.',
  'nomenclatures_counters_formula_num_descr' => 'попълва поредния номер на номенклатура.',
  'nomenclatures_counters_formula_code_suffix_descr' => 'попълва суфикс за автоматично задаван код за типа на номенклатурата.',
  'nomenclatures_counters_formula_user_code_descr' => 'попълва кода на потребителя, създал номенклатурата.',
  'nomenclatures_counters_formula_added_descr' => 'дата на добавяне на номенклатурата.',
  'nomenclatures_counters_formula_note' => '<strong>ЗАБЕЛЕЖКА:</strong> Към формулата на брояча могат да се добавят <strong>само 5 елемента</strong>',
  'help_nomenclatures_counters_name' => '',
  'help_nomenclatures_counters_formula' => '',
  'help_nomenclatures_counters_count_nomenclatures' => '',
  'help_nomenclatures_counters_types_used' => '',
  'help_nomenclatures_counters_status' => '',
  'help_nomenclatures_counters_description' => '',
  'help_nomenclatures_counters_next_number' => 'Следващ номер. Използвайте това поле, за да зададете номер, от който да започва броячът.',
); ?>