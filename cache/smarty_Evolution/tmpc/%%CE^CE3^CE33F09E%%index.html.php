<?php /* Smarty version 2.6.33, created on 2025-06-13 17:11:44
         compiled from /var/www/Nzoom-Hella/_libs/modules/imports/templates/index.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/imports/templates/index.html', 9, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/imports/templates/index.html', 13, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/imports/templates/index.html', 14, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 20px;"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption" nowrap="nowrap" style="width: 500px;"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['imports_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>
      <?php $_from = $this->_tpl_vars['imports_types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['imports_type']):
        $this->_foreach['i']['iteration']++;
?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="t_border hright" nowrap="nowrap"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <td>
            <a href="#<?php echo $this->_tpl_vars['imports_type']->get('type'); ?>
" onclick="launchImportSelection('<?php echo $this->_tpl_vars['imports_type']->get('type'); ?>
')"><?php echo $this->_tpl_vars['imports_type']->get('name'); ?>
</a>
          </td>
        </tr>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="2"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="2"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>