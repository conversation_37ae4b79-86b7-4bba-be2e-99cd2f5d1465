<?php /* Smarty version 2.6.33, created on 2025-06-23 21:31:48
         compiled from view_bb.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'view_bb.html', 17, false),array('modifier', 'default', 'view_bb.html', 21, false),array('modifier', 'regex_replace', 'view_bb.html', 51, false),array('modifier', 'url2href', 'view_bb.html', 55, false),array('modifier', 'nl2br', 'view_bb.html', 63, false),array('modifier', 'date_format', 'view_bb.html', 65, false),array('modifier', 'encrypt', 'view_bb.html', 100, false),array('function', 'counter', 'view_bb.html', 23, false),array('function', 'help', 'view_bb.html', 30, false),array('function', 'getimagesize', 'view_bb.html', 99, false),array('function', 'json', 'view_bb.html', 114, false),)), $this); ?>
<?php if ($this->_tpl_vars['module'] == 'customers'): ?>
  <?php $this->assign('add_bb_vars', $this->_tpl_vars['customer']->get('add_bb_vars')); ?>
  <?php $this->assign('tmp_id', $this->_tpl_vars['customer']->get('id')); ?>
  <?php $this->assign('temp_bb_vars', $this->_tpl_vars['customer']->get('bb_vars')); ?>
<?php elseif ($this->_tpl_vars['module'] == 'nomenclatures'): ?>
    <?php $this->assign('add_bb_vars', $this->_tpl_vars['nomenclature']->get('add_bb_vars')); ?>
    <?php $this->assign('tmp_id', $this->_tpl_vars['nomenclature']->get('id')); ?>
    <?php $this->assign('temp_bb_vars', $this->_tpl_vars['nomenclature']->get('bb_vars')); ?>
<?php else: ?>
  <?php $this->assign('add_bb_vars', $this->_tpl_vars['document']->get('add_bb_vars')); ?>
  <?php $this->assign('tmp_id', $this->_tpl_vars['document']->get('id')); ?>
  <?php $this->assign('temp_bb_vars', $this->_tpl_vars['document']->get('bb_vars')); ?>
<?php endif; ?>
<tr>
  <td colspan="3">
    <div style="float: right;">
      <span class="pointer" onclick="toggleTableRowsAll('bb_table', 'expand')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['expand_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
      <span class="pointer" onclick="toggleTableRowsAll('bb_table', 'collapse')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['collapse_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
    </div>
    <br />
    <table class="t_grouping_table" width="<?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['t_width'])) ? $this->_run_mod_handler('default', true, $_tmp, '100%') : smarty_modifier_default($_tmp, '100%')); ?>
" id="bb_table">
      <tr>
    <?php echo smarty_function_counter(array('assign' => 'add_bb_vars_count','start' => 1), $this);?>

    <?php if ($this->_tpl_vars['transform_bb']): ?><?php echo smarty_function_counter(array('assign' => 'add_bb_vars_count'), $this);?>

      <th width="10"><input checked="checked" onclick="toggleCheckboxes(this, 'bb_ids', this.checked)" type="checkbox" name="checkall_bb" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></th>
    <?php endif; ?>
      <th><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
      <?php $_from = $this->_tpl_vars['add_bb_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['abbh'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['abbh']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['add_bb_var_name'] => $this->_tpl_vars['add_bb_var']):
        $this->_foreach['abbh']['iteration']++;
?>
        <?php if ($this->_tpl_vars['add_bb_var_name'] != 'bb_group'): ?>
        <th<?php if ($this->_tpl_vars['add_bb_var']['hidden']): ?> style="display: none;"<?php elseif ($this->_tpl_vars['add_bb_var']['width']): ?> width="<?php echo $this->_tpl_vars['add_bb_var']['width']; ?>
"<?php endif; ?>><?php if (! $this->_tpl_vars['add_bb_var']['hidden']): ?><?php echo smarty_function_counter(array('assign' => 'add_bb_vars_count'), $this);?>
<a name="error_<?php echo $this->_tpl_vars['add_bb_var_name']; ?>
"></a><label for="<?php echo $this->_tpl_vars['add_bb_var_name']; ?>
"<?php if ($this->_tpl_vars['messages']->getErrors('add_bb_var_name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['add_bb_var']['label'],'text_content' => $this->_tpl_vars['add_bb_var']['help']), $this);?>
</label><?php endif; ?></th>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    </tr>

    <?php $_from = $this->_tpl_vars['temp_bb_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['jj'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['jj']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['bb_var']):
        $this->_foreach['jj']['iteration']++;
?>
      <?php ob_start(); ?><?php echo $this->_tpl_vars['tmp_id']; ?>
,'<?php echo $this->_tpl_vars['module']; ?>
',<?php echo $this->_tpl_vars['bb_var']['id']; ?>
,1,<?php echo $this->_tpl_vars['bb_var']['meta_id']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('bb_change', ob_get_contents());ob_end_clean(); ?>
      <?php $this->assign('bb_id', $this->_tpl_vars['bb_var']['id']); ?>
      <?php $this->assign('layout_cookie_var', "row_".($this->_tpl_vars['bb_id'])."_box"); ?>
      <tr id="bb_row_<?php echo $this->_tpl_vars['bb_var']['id']; ?>
">
      <?php if ($this->_tpl_vars['transform_bb']): ?>
        <td><input type="checkbox" name="bb_ids[]" value="<?php echo $this->_tpl_vars['bb_var']['id']; ?>
" checked="checked" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" /></td>
      <?php endif; ?>
        <td nowrap="nowrap" class="pointer" onclick="toggleTableRow($('bb_row_<?php echo $this->_tpl_vars['bb_var']['id']; ?>
'))" width="25"><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['layout_cookie_var']] == 'off' || $this->_tpl_vars['var']['toggle'] == 'collapse' || $this->_tpl_vars['var']['toggle'] == 'first' && ! ($this->_foreach['jj']['iteration'] <= 1)): ?>expand<?php else: ?>collapse<?php endif; ?>"></div> <?php echo $this->_foreach['jj']['iteration']; ?>
</td>
        <?php $_from = $this->_tpl_vars['add_bb_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['abb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['abb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['add_bb_var_name'] => $this->_tpl_vars['add_bb_var']):
        $this->_foreach['abb']['iteration']++;
?>
          <?php if ($this->_tpl_vars['add_bb_var_name'] != 'bb_group'): ?>
          <td style="<?php if ($this->_tpl_vars['add_bb_var']['hidden']): ?>display: none;<?php endif; ?><?php if ($this->_tpl_vars['add_bb_var']['text_align']): ?>text-align: <?php echo $this->_tpl_vars['add_bb_var']['text_align']; ?>
;<?php endif; ?>">
            <?php if ($this->_tpl_vars['add_bb_var_name'] == 'bb_elements'): ?>
              <strong><?php echo $this->_tpl_vars['bb_var']['label']; ?>
</strong>
            <?php else: ?>
              <?php if ($this->_tpl_vars['add_bb_var']['type'] == 'text'): ?>
                <?php ob_start(); ?><?php if ($this->_tpl_vars['use_asterisk'] && preg_match ( '#^asteriskcall_(fax|phone|gsm).*$#' , $this->_tpl_vars['name'] )): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['name'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1') : smarty_modifier_regex_replace($_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1')); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('asterisk_contact', ob_get_contents());ob_end_clean(); ?>
                <?php if ($this->_tpl_vars['asterisk_contact']): ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => $this->_tpl_vars['asterisk_contact'],'number' => $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php else: ?>
                  <?php echo ((is_array($_tmp=$this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

                <?php endif; ?>
              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'autocompleter'): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']],'value_id' => $this->_tpl_vars['add_bb_var']['value_id'][$this->_tpl_vars['bb_id']],'view_mode_url' => $this->_tpl_vars['add_bb_var']['autocomplete']['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'textarea'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>

              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'date'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'datetime'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'time'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'dropdown'): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['add_bb_var'],'value' => $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']],'extended_value' => $this->_tpl_vars['add_bb_var']['extended_value'][$this->_tpl_vars['bb_id']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'radio'): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['add_bb_var'],'value' => $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']],'extended_value' => $this->_tpl_vars['add_bb_var']['extended_value'][$this->_tpl_vars['bb_id']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'checkbox_group'): ?>
                <?php if (is_array ( $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] ) && count ( $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] ) > 16): ?><div class="scroll_box" style="width:100%!important;"><?php endif; ?>
                <?php if ($this->_tpl_vars['add_bb_var']['options']): ?>
                  <?php $_from = $this->_tpl_vars['add_bb_var']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['mcb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['mcb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['mcb']['iteration']++;
?>
                    <?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] )): ?><?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! empty ( $this->_tpl_vars['add_bb_var']['options_align'] ) && $this->_tpl_vars['add_bb_var']['options_align'] == 'horizontal'): ?>&nbsp;<?php else: ?><br /><?php endif; ?><?php else: ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" /><?php endif; ?><?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                <?php elseif ($this->_tpl_vars['add_bb_var']['options']): ?>
                  <?php $_from = $this->_tpl_vars['add_bb_var']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optgroup_name'] => $this->_tpl_vars['optgroup']):
?>
                    <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['mcb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['mcb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['mcb']['iteration']++;
?>
                      <?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] )): ?><?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! empty ( $this->_tpl_vars['add_bb_var']['options_align'] ) && $this->_tpl_vars['add_bb_var']['options_align'] == 'horizontal'): ?>&nbsp;<?php else: ?><br /><?php endif; ?><?php else: ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" /><?php endif; ?><?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                  <?php endforeach; endif; unset($_from); ?>
                <?php endif; ?>
                <?php if (is_array ( $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] ) && count ( $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] ) > 16): ?></div><?php endif; ?>
              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'file_upload'): ?>
                <?php $this->assign('file_info', $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']]); ?>
                <?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?>
                  <?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?>
                    <?php if ($this->_tpl_vars['add_bb_var']['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?>
                      <?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?>

                      <img src="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=files&amp;files=viewfile&amp;viewfile=<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?><?php if ($this->_tpl_vars['add_bb_var']['thumb_width']): ?>&amp;maxwidth=<?php echo $this->_tpl_vars['add_bb_var']['thumb_width']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['add_bb_var']['thumb_height']): ?>&amp;maxheight=<?php echo $this->_tpl_vars['add_bb_var']['thumb_height']; ?>
<?php endif; ?>" onclick="showFullLBImage('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['tmp_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', '<?php echo $this->_tpl_vars['add_bb_var']['label']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['width']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['height']; ?>
')" />
                    <?php else: ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['tmp_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>" target="_blank"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['tmp_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                    <?php endif; ?>
                  <?php else: ?>
                    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
                    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
                  <?php endif; ?>
                <?php else: ?>
                  &nbsp;
                <?php endif; ?>
              <?php elseif ($this->_tpl_vars['add_bb_var']['type'] == 'map'): ?>
                <script type="text/javascript">
                  params_map_<?php echo $this->_tpl_vars['add_bb_var_name']; ?>
_<?php echo $this->_tpl_vars['bb_var']['id']; ?>
 = <?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['add_bb_var']['map_params'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))), $this);?>
;
                </script>
                <?php if ($this->_tpl_vars['add_bb_var']['map_params']['type'] == 'inline'): ?>
                  
                <?php else: ?>
                  <input type="image" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
map.png" width="16" height="16" alt="" class="image_map" border="0" onclick="showMap(this, params_map_<?php echo $this->_tpl_vars['add_bb_var_name']; ?>
_<?php echo $this->_tpl_vars['bb_var']['id']; ?>
, '<?php echo $this->_tpl_vars['add_bb_var']['label']; ?>
', ''); return false;" />
                <?php endif; ?>
              <?php else: ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

              <?php endif; ?>
            <?php endif; ?>
            <?php if (( $this->_tpl_vars['add_bb_var']['extended_value'][$this->_tpl_vars['bb_id']] || $this->_tpl_vars['add_bb_var']['extended_value'][$this->_tpl_vars['bb_id']] === '0' || $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] || $this->_tpl_vars['add_bb_var']['value'][$this->_tpl_vars['bb_id']] === '0' ) && $this->_tpl_vars['add_bb_var']['back_label']): ?><?php echo $this->_tpl_vars['add_bb_var']['back_label']; ?>
<?php endif; ?>
          </td>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      </tr>
      <tr id="row_<?php echo $this->_tpl_vars['bb_var']['id']; ?>
_box"<?php if ($_COOKIE[$this->_tpl_vars['layout_cookie_var']] == 'off' || $this->_tpl_vars['var']['toggle'] == 'collapse' || $this->_tpl_vars['var']['toggle'] == 'first' && ! ($this->_foreach['jj']['iteration'] <= 1)): ?> style="display: none"<?php endif; ?>>
        <td colspan="<?php echo $this->_tpl_vars['add_bb_vars_count']; ?>
">
          <table>
          <?php if ($this->_tpl_vars['bb_var']['type'] == 'gt2'): ?>
            <?php $this->assign('view_type_name', "_gt2_view.html"); ?>
            <?php $this->assign('table', $this->_tpl_vars['bb_var']); ?>
                    <tr>
                      <td colspan="3" class="t_table">
          <?php else: ?>
            <?php $this->assign('view_type_name', "view_".($this->_tpl_vars['bb_var']['type']).".html"); ?>
          <?php endif; ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['view_type_name'], 'smarty_include_vars' => array('var' => $this->_tpl_vars['bb_var'],'standalone' => false,'var_id' => $this->_tpl_vars['bb_var']['id'],'name' => $this->_tpl_vars['bb_var']['name'],'custom_id' => $this->_tpl_vars['varbb_']['custom_id'],'label' => $this->_tpl_vars['bb_var']['label'],'help' => $this->_tpl_vars['bb_var']['help'],'back_label' => $this->_tpl_vars['bb_var']['back_label'],'value' => $this->_tpl_vars['bb_var']['value'],'options' => $this->_tpl_vars['bb_var']['options'],'optgroups' => $this->_tpl_vars['bb_var']['optgroups'],'option_value' => $this->_tpl_vars['bb_var']['option_value'],'first_option_label' => $this->_tpl_vars['bb_var']['first_option_label'],'onclick' => $this->_tpl_vars['bb_var']['onclick'],'on_change' => $this->_tpl_vars['bb_var']['on_change'],'check' => $this->_tpl_vars['bb_var']['check'],'scrollable' => $this->_tpl_vars['bb_var']['scrollable'],'calculate' => $this->_tpl_vars['bb_var']['calculate'],'readonly' => $this->_tpl_vars['bb_var']['readonly'],'source' => $this->_tpl_vars['bb_var']['source'],'map_params' => $this->_tpl_vars['bb_var']['map_params'],'hidden' => $this->_tpl_vars['bb_var']['hidden'],'required' => $this->_tpl_vars['bb_var']['required'],'disabled' => $this->_tpl_vars['bb_var']['disabled'],'view_mode' => $this->_tpl_vars['bb_var']['view_mode'],'hide_label' => 1,'thumb_width' => $this->_tpl_vars['bb_var']['thumb_width'],'thumb_height' => $this->_tpl_vars['bb_var']['thumb_height'],'text_align' => $this->_tpl_vars['bb_var']['text_align'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php if ($this->_tpl_vars['bb_var']['type'] == 'gt2'): ?>
                      </td>
                    </tr>
          <?php endif; ?>
          </table>
        </td>
      </tr>
    <?php endforeach; endif; unset($_from); ?>
    </table>
  </td>
</tr>