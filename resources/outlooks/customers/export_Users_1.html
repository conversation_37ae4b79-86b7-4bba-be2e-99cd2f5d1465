    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.eik|default:#customers_eik#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.email|default:#customers_email#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#customers_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.ucn|default:#customers_ucn#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#customers_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.bic|default:#customers_bic#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.iban|default:#customers_iban#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.skype|default:#customers_skype#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.address|default:#customers_address#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.address_by_personal_id|default:#customers_address_by_personal_id#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.registration_address|default:#customers_registration_address#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.bank|default:#customers_bank#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.notes|default:#customers_notes#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.identity_valid|default:#customers_identity_valid#|escape}</th>
          <th nowrap="nowrap">{#customers_company_person#|escape}</th>
          <th nowrap="nowrap">{#customers_group#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added_by|default:#customers_added_by#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.othercontact|default:#customers_othercontact#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.country|default:#customers_country#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.in_dds|default:#customers_in_dds#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.code|default:#customers_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name_code|default:#customers_name_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.comments|default:#customers_comments#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.identity_date|default:#customers_identity_date#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.identity_by|default:#customers_identity_by#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.identity_num|default:#customers_identity_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.gsm|default:#customers_gsm#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.city|default:#customers_city#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.num|default:#customers_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.main_trademark|default:#customers_main_trademark#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.department|default:#customers_department#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.company_department|default:#customers_company_department#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.emails|default:#customers_emails#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.position|default:#customers_position#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.postal_code|default:#customers_postal_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.mol|default:#customers_mol#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.admit_VAT_credit|default:#customers_admit_VAT_credit#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.modified|default:#customers_modified#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.modified_by|default:#customers_modified_by#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.company_name|default:#customers_company_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#customers_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.phone|default:#customers_phone#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.web|default:#customers_web#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.fax|default:#customers_fax#|escape}</th>

        </tr>
      {foreach name='i' from=$customers item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="48-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('eik')|escape|default:"&nbsp;"}</td>
          <td{if !$single->get('email')} {/if}>
            {if is_array($single->get('email'))}
              {foreach from=$single->get('email') item='email' name='cdi'}
                {$email|escape|default:"&nbsp;"}{if !$smarty.foreach.cdi.last}, {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('ucn')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('bic')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('iban')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
          {if is_array($single->get('skype'))}
            {foreach from=$single->get('skype') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}, {/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('address')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('address_by_personal_id')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('registration_address')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('bank')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('notes')|escape|nl2br|url2href|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('identity_valid')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{if $single->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('group_name')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('added_by_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
          {if is_array($single->get('othercontact'))}
            {foreach from=$single->get('othercontact') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}, {/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('country_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('in_dds')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('code')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">&#91;{$single->get('code')|escape|default:"&nbsp;"}&#93; {$single->get('name')|escape|default:"&nbsp;"}{if $single->get('lastname')} {$single->get('lastname')}{/if}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td style="mso-number-format: \@;">
            
              {if $single->get('comments')}{$single->get('comments')}{/if}
            
            {if $single->checkPermissions('comments_add')}
            
              
            
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('identity_date')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('identity_by')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('identity_num')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('gsm') item='gsm' name='cdi'}
              {$gsm|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td style="mso-number-format: \@;">{$single->get('city')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('num')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('main_trademark_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('department_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('company_department')|escape|default:"&nbsp;"}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td style="mso-number-format: \@;">
            
              {if $single->get('emails')}{$single->get('emails')}{/if}
            
            {if $single->checkPermissions('emails_add')}
            
              
            
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('position')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('postal_code')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('mol')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">
            {if $single->get('admit_VAT_credit')}{#yes#}{else}{#no#}{/if}
          </td>
          <td nowrap="nowrap">{$single->get('modified')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('modified_by_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('company_name')|escape|default:"&nbsp;"}</td>
          
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('phone') item='phone' name='cdi'}
              {$phone|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td{if !$single->get('web')} {/if}>
            {if is_array($single->get('web'))}
              {foreach from=$single->get('web') item='web' name='cdi'}
                {$web|escape|default:"&nbsp;"}{if !$smarty.foreach.cdi.last}, {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('fax') item='fax' name='cdi'}
              {$fax|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="48">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
