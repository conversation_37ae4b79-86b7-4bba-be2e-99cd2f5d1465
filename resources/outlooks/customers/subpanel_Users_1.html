{capture assign='trademark_nomenclature'}{if preg_match('#trademark_nomenclature$#', $session_param)}1{else}0{/if}{/capture}
<style type="text/css">

</style>
{if !$trademark_nomenclature}
<form name="customers" action="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}" method="post" enctype="multipart/form-data">
{else}
  <div id="messages_container_{$session_param}">
    {if $messages->getErrors()}{include file='message.html' display="error" items=$messages->getErrors()}{/if}
    {if $messages->getMessages()}{include file='message.html' display="message" items=$messages->getMessages()}{/if}
    {if $messages->getWarnings()}{include file='message.html' display="warning" items=$messages->getWarnings()}{/if}
  </div>
  {if $currentUser->checkRights($module, 'manage_outlooks')}
  <div style="position: relative;">
    <div class="action_tabs action_tabs_submenu_upper_right">
      <ul id="subpanel_actions_right_{counter start=1 name='menu_counter_upper_right' assign='subpanel_action_upper_right_menu_num' print=true}" class="zpHideOnLoad">
        <li title="{#manage_outlooks#}">
          <input type="image" src="{$theme->imagesUrl}manage_outlooks.png" width="16" height="16" alt="" title="{#manage_outlooks#}" border="0" onclick="showAvailableOutlookOptions('{$module}|{$controller}', '{$custom_template_options.name}', '{$custom_template_options.value}'); return false;" />
        </li>
      </ul>
      <script type="text/javascript">
        new Zapatec.Menu({ldelim}source: 'subpanel_actions_right_{$subpanel_action_upper_right_menu_num}',
                          hideDelay: 100,
                          theme: 'nzoom'{rdelim});
      </script>
    </div>
  </div>
  {/if}
  <script type="text/javascript">Effect.Pulsate($('messages_container_{$session_param}'), {ldelim}pulses: 1{rdelim});</script>
{/if}
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
  <tr>
    {if $trademark_nomenclature}
    <th class="t_caption t_border" width="15">&nbsp;</th>
    <th class="t_caption t_border" width="15" style="text-align: center;"><span class="help" {help label_content=#main_trademark# popup_only=1}>&nbsp;</span></th>
    {/if}
    <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></th>
          <th class="t_caption t_border {$sort.eik.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.eik.link} onclick="{$sort.eik.link}"{/if}>{$basic_vars_labels.eik|default:#customers_eik#|escape}</div></th>
          <th class="t_caption t_border {$sort.email.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.email.link} onclick="{$sort.email.link}"{/if}>{$basic_vars_labels.email|default:#customers_email#|escape}</div></th>
          <th class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#customers_name#|escape}</div></th>
          <th class="t_caption t_border {$sort.ucn.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.ucn.link} onclick="{$sort.ucn.link}"{/if}>{$basic_vars_labels.ucn|default:#customers_ucn#|escape}</div></th>
          <th class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{$basic_vars_labels.type|default:#customers_type#|escape}</div></th>
          <th class="t_caption t_border {$sort.bic.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.bic.link} onclick="{$sort.bic.link}"{/if}>{$basic_vars_labels.bic|default:#customers_bic#|escape}</div></th>
          <th class="t_caption t_border {$sort.iban.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.iban.link} onclick="{$sort.iban.link}"{/if}>{$basic_vars_labels.iban|default:#customers_iban#|escape}</div></th>
          <th class="t_caption t_border {$sort.skype.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.skype.link} onclick="{$sort.skype.link}"{/if}>{$basic_vars_labels.skype|default:#customers_skype#|escape}</div></th>
          <th class="t_caption t_border {$sort.address.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.address.link} onclick="{$sort.address.link}"{/if}>{$basic_vars_labels.address|default:#customers_address#|escape}</div></th>
          <th class="t_caption t_border {$sort.address_by_personal_id.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.address_by_personal_id.link} onclick="{$sort.address_by_personal_id.link}"{/if}>{$basic_vars_labels.address_by_personal_id|default:#customers_address_by_personal_id#|escape}</div></th>
          <th class="t_caption t_border {$sort.registration_address.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.registration_address.link} onclick="{$sort.registration_address.link}"{/if}>{$basic_vars_labels.registration_address|default:#customers_registration_address#|escape}</div></th>
          <th class="t_caption t_border {$sort.bank.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.bank.link} onclick="{$sort.bank.link}"{/if}>{$basic_vars_labels.bank|default:#customers_bank#|escape}</div></th>
          <th class="t_caption t_border {$sort.notes.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.notes.link}">{$basic_vars_labels.notes|default:#customers_notes#|escape}</div></th>
          <th class="t_caption t_border {$sort.identity_valid.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.identity_valid.link} onclick="{$sort.identity_valid.link}"{/if}>{$basic_vars_labels.identity_valid|default:#customers_identity_valid#|escape}</div></th>
          <th class="t_caption t_border {$sort.is_company.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.is_company.link}">{#customers_company_person#|escape}</div></th>
          <th class="t_caption t_border {$sort.group.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.group.link}">{#customers_group#|escape}</div></th>
          <th class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{$basic_vars_labels.added|default:#added#|escape}</div></th>
          <th class="t_caption t_border {$sort.added_by.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.added_by.link} onclick="{$sort.added_by.link}"{/if}>{$basic_vars_labels.added_by|default:#customers_added_by#|escape}</div></th>
          <th class="t_caption t_border {$sort.othercontact.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.othercontact.link} onclick="{$sort.othercontact.link}"{/if}>{$basic_vars_labels.othercontact|default:#customers_othercontact#|escape}</div></th>
          <th class="t_caption t_border {$sort.country.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.country.link} onclick="{$sort.country.link}"{/if}>{$basic_vars_labels.country|default:#customers_country#|escape}</div></th>
          <th class="t_caption t_border {$sort.in_dds.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.in_dds.link} onclick="{$sort.in_dds.link}"{/if}>{$basic_vars_labels.in_dds|default:#customers_in_dds#|escape}</div></th>
          <th class="t_caption t_border {$sort.code.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.code.link} onclick="{$sort.code.link}"{/if}>{$basic_vars_labels.code|default:#customers_code#|escape}</div></th>
          <th class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name_code|default:#customers_name_code#|escape}</div></th>
          <th class="t_caption t_border {$sort.comments.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.comments.link} onclick="{$sort.comments.link}"{/if}>{$basic_vars_labels.comments|default:#customers_comments#|escape}</div></th>
          <th class="t_caption t_border {$sort.identity_date.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.identity_date.link} onclick="{$sort.identity_date.link}"{/if}>{$basic_vars_labels.identity_date|default:#customers_identity_date#|escape}</div></th>
          <th class="t_caption t_border {$sort.identity_by.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.identity_by.link} onclick="{$sort.identity_by.link}"{/if}>{$basic_vars_labels.identity_by|default:#customers_identity_by#|escape}</div></th>
          <th class="t_caption t_border {$sort.identity_num.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.identity_num.link} onclick="{$sort.identity_num.link}"{/if}>{$basic_vars_labels.identity_num|default:#customers_identity_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.gsm.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.gsm.link} onclick="{$sort.gsm.link}"{/if}>{$basic_vars_labels.gsm|default:#customers_gsm#|escape}</div></th>
          <th class="t_caption t_border {$sort.city.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.city.link} onclick="{$sort.city.link}"{/if}>{$basic_vars_labels.city|default:#customers_city#|escape}</div></th>
          <th class="t_caption t_border {$sort.num.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.num.link} onclick="{$sort.num.link}"{/if}>{$basic_vars_labels.num|default:#customers_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.main_trademark.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.main_trademark.link} onclick="{$sort.main_trademark.link}"{/if}>{$basic_vars_labels.main_trademark|default:#customers_main_trademark#|escape}</div></th>
          <th class="t_caption t_border {$sort.department.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.department.link}">{$basic_vars_labels.department|default:#customers_department#|escape}</div></th>
          <th class="t_caption t_border {$sort.company_department.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.company_department.link} onclick="{$sort.company_department.link}"{/if}>{$basic_vars_labels.company_department|default:#customers_company_department#|escape}</div></th>
          <th class="t_caption t_border {$sort.emails.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.emails.link} onclick="{$sort.emails.link}"{/if}>{$basic_vars_labels.emails|default:#customers_emails#|escape}</div></th>
          <th class="t_caption t_border {$sort.position.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.position.link} onclick="{$sort.position.link}"{/if}>{$basic_vars_labels.position|default:#customers_position#|escape}</div></th>
          <th class="t_caption t_border {$sort.postal_code.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.postal_code.link} onclick="{$sort.postal_code.link}"{/if}>{$basic_vars_labels.postal_code|default:#customers_postal_code#|escape}</div></th>
          <th class="t_caption t_border {$sort.mol.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.mol.link} onclick="{$sort.mol.link}"{/if}>{$basic_vars_labels.mol|default:#customers_mol#|escape}</div></th>
          <th class="t_caption t_border {$sort.admit_VAT_credit.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.admit_VAT_credit.link} onclick="{$sort.admit_VAT_credit.link}"{/if}>{$basic_vars_labels.admit_VAT_credit|default:#customers_admit_VAT_credit#|escape}</div></th>
          <th class="t_caption t_border {$sort.modified.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.modified.link} onclick="{$sort.modified.link}"{/if}>{$basic_vars_labels.modified|default:#customers_modified#|escape}</div></th>
          <th class="t_caption t_border {$sort.modified_by.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.modified_by.link} onclick="{$sort.modified_by.link}"{/if}>{$basic_vars_labels.modified_by|default:#customers_modified_by#|escape}</div></th>
          <th class="t_caption t_border {$sort.company_name.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.company_name.link} onclick="{$sort.company_name.link}"{/if}>{$basic_vars_labels.company_name|default:#customers_company_name#|escape}</div></th>
          <th class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.tags.link} onclick="{$sort.tags.link}"{/if}>{$basic_vars_labels.tags|default:#customers_tags#|escape}</div></th>
          <th class="t_caption t_border {$sort.phone.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.phone.link} onclick="{$sort.phone.link}"{/if}>{$basic_vars_labels.phone|default:#customers_phone#|escape}</div></th>
          <th class="t_caption t_border {$sort.web.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.web.link} onclick="{$sort.web.link}"{/if}>{$basic_vars_labels.web|default:#customers_web#|escape}</div></th>
          <th class="t_caption t_border {$sort.fax.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.fax.link} onclick="{$sort.fax.link}"{/if}>{$basic_vars_labels.fax|default:#customers_fax#|escape}</div></th>

    <th class="t_caption">&nbsp;</th>
  </tr>
{counter start=$pagination.start name='item_counter' print=false}
{array assign='background_colors'}
{foreach name='i' from=$customers item='single'}
      {strip}
      {assign var='salutation' value=''}
      {if !$single->get('is_company') && $single->get('salutation')}
        {assign var='layout_salutation' value=$single->getLayoutsDetails('salutation')}
        {if $layout_salutation.view}
          {capture assign='salutation'}salutation_{$single->get('salutation')}{/capture}
          {capture assign='salutation'}{$smarty.config.$salutation|escape} {/capture}
        {/if}
      {/if}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#customers_name#|escape}:</strong> {$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}<br />
        <strong>{$basic_vars_labels.type|default:#customers_type#|escape}:</strong> {$single->get('type_name')|escape} ({if $single->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if})<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {/strip}
      {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
      <div id="rf{$single->get('id')}" style="display: none">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</div>
{if !$single->checkPermissions('list')}
  <tr class="{cycle values='t_odd,t_even'}">
    {if $trademark_nomenclature}
    <td class="t_border">
      <input type="image" src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" onclick="return confirmAction('delete_row', function() {ldelim} updateTrademarks(null, {ldelim}action: 'delete', nomenclature: '{$single->get('id')}'{rdelim}); {rdelim}, this);" />
    </td>
    <td class="t_border">
      <input onclick="updateTrademarks(null, {ldelim}action: 'default', nomenclature: '{$single->get('id')}', the_element: this{rdelim});"
             type="checkbox"
             name='items[]'
             value="{$single->get('id')}"
             title="{#check_to_set_default#}"
             {if $single->get('is_default')}checked="checked"{/if} />
    </td>
    {/if}
    <td class="t_border hright dimmed" nowrap="nowrap">
      {counter name='item_counter' print=true}
    </td>
    <td colspan="45" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
    <td>
      {include file=`$theme->templatesDir`single_actions_list.html object=$single disabled='edit,delete,view'}
    </td>
  </tr>
{else}
  <tr {if !$background_style}class="{cycle values='t_odd,t_even'}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('archived_by')} attention{/if}{if $single->get('deleted_by')} t_deleted{/if}{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} {if $single->modelName eq 'Contract'}strike{else}t_strike{/if}{/if}{if $single->get('severity')} {$single->get('severity')}{/if}{if $trademark_nomenclature && !$single->get('parent_id')} attention{/if}"{else}class="t_row{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} t_strike{/if}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('deleted_by')} t_deleted{/if}"{if $single->isDefined('active') && $single->get('active') || !$single->isDefined('active')} {$background_style}{/if}{/if}>
    {if $trademark_nomenclature}
    <td class="t_border">
      <input type="image" src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" onclick="return confirmAction('delete_row', function() {ldelim} updateTrademarks(null, {ldelim}action: 'delete', nomenclature: '{$single->get('id')}'{rdelim}); {rdelim}, this);" />
    </td>
    <td class="t_border">
      <input onclick="updateTrademarks(null, {ldelim}action: 'default', nomenclature: '{$single->get('id')}', the_element: this{rdelim});"
             type="checkbox"
             name='items[]'
             value="{$single->get('id')}"
             title="{#check_to_set_default#}"
             {if $single->get('is_default')}checked="checked"{/if} />
    </td>
    {/if}
    <td class="t_border hright" nowrap="nowrap">
      {if $single->get('files_count')}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}&amp;{$controller}{else}&amp;{$module}{/if}=attachments&amp;attachments={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}"{if $link_target} target="{$link_target}"{/if}>
         <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
               onmouseover="showFiles(this, '{$module}', '{$controller}', {$single->get('id')}{if $single->get('archived_by')}, '', 1{/if})"
               onmouseout="mclosetime()" />
        </a>
      {/if}
      {counter name='item_counter' print=true}
    </td>
          <td class="t_border {$sort.eik.isSorted} {$row_link_class}"{$row_link}>{$single->get('eik')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.email.isSorted}{if !$single->get('email')} {$row_link_class}{/if}"{if !$single->get('email')} {$row_link}{/if}>
            {if is_array($single->get('email'))}
              {foreach from=$single->get('email') item='email' name='cdi'}
                <a href="mailto:{$email}" target="_self">{$email|escape|default:"&nbsp;"}</a>{if !$smarty.foreach.cdi.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$single->get('id')}">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</a></td>
          <td class="t_border {$sort.ucn.isSorted} {$row_link_class}"{$row_link}>{$single->get('ucn')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.type.isSorted} {$row_link_class}"{$row_link}>{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.bic.isSorted} {$row_link_class}"{$row_link}>{$single->get('bic')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.iban.isSorted} {$row_link_class}"{$row_link}>{$single->get('iban')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.skype.isSorted} {$row_link_class}"{$row_link}>
          {if is_array($single->get('skype'))}
            {foreach from=$single->get('skype') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}<br />{/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td class="t_border {$sort.address.isSorted} {$row_link_class}"{$row_link}>{$single->get('address')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.address_by_personal_id.isSorted} {$row_link_class}"{$row_link}>{$single->get('address_by_personal_id')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.registration_address.isSorted} {$row_link_class}"{$row_link}>{$single->get('registration_address')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.bank.isSorted} {$row_link_class}"{$row_link}>{$single->get('bank')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.notes.isSorted} {$row_link_class}"{$row_link}>{$single->get('notes')|escape|nl2br|url2href|default:"&nbsp;"}</td>
          <td class="t_border {$sort.identity_valid.isSorted} {$row_link_class}"{$row_link}>{$single->get('identity_valid')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.is_company.isSorted} {$row_link_class}"{$row_link}>{if $single->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if}</td>
          <td class="t_border {$sort.group.isSorted} {$row_link_class}"{$row_link}>{$single->get('group_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.added.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$single->get('added')|date_format:#date_short#|escape}</td>
          <td class="t_border {$sort.added_by.isSorted} {$row_link_class}"{$row_link}>{$single->get('added_by_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.othercontact.isSorted} {$row_link_class}"{$row_link}>
          {if is_array($single->get('othercontact'))}
            {foreach from=$single->get('othercontact') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}<br />{/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td class="t_border {$sort.country.isSorted} {$row_link_class}"{$row_link}>{$single->get('country_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.in_dds.isSorted} {$row_link_class}"{$row_link}>{$single->get('in_dds')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.code.isSorted} {$row_link_class}"{$row_link}>{$single->get('code')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module ne $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$action_param}=view&amp;view={$single->get('id')}"{if $link_target} target="{$link_target}"{/if}>&#91;{$single->get('code')|escape|default:"&nbsp;"}&#93; {$single->get('name')|escape|default:"&nbsp;"}{if $single->get('lastname')} {$single->get('lastname')}{/if}</a></td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td class="t_border {$sort.comments.isSorted}">
            <div class="t_occupy_cell has_inline_add hright comments comments_{$single->modelName|lower}_{$single->get('id')}{if $single->get('comments') gt 0 && $single->checkPermissions('comments')} pointer" onmouseenter="showCommunicationsInfo(this, 'comments', '{$_module}', '{$_controller}', {$single->get('id')}, '{$single->get('archived_by')}')" onmouseleave="mclosetime()" onclick="openHref('{$smarty.server.SCRIPT_NAME}?{$module_param}={$_module}{if $_controller ne $_module}&amp;controller={$_controller}{/if}&amp;{$_controller}=communications&amp;communications={$single->get('id')}&amp;communication_type=comments{if $single->get('archived_by')}&amp;archive=1{/if}', '_self', event){/if}">
              <span class="comments_total">{if $single->get('comments')}{$single->get('comments')}{/if}</span>
            </div>
            {if $single->checkPermissions('comments_add')}
            <div class="inline_add" onmouseover="Event.stop(event)">
              <a href="#" onclick="return loadInlineAddCommunication(event, 'comment', '{$_module}', '{$_controller}', {$single->get('id')});" title="{#add_comment#|escape}"></a>
            </div>
            {/if}
          </td>
          <td class="t_border {$sort.identity_date.isSorted} {$row_link_class}"{$row_link}>{$single->get('identity_date')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.identity_by.isSorted} {$row_link_class}"{$row_link}>{$single->get('identity_by')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.identity_num.isSorted} {$row_link_class}"{$row_link}>{$single->get('identity_num')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.gsm.isSorted}{if !$use_asterisk} {$row_link_class}{/if}"{if !$use_asterisk} {$row_link}{/if}>
            {foreach from=$single->get('gsm') item='gsm' name='cdi'}
              {if $use_asterisk && $gsm}
                {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='gsm' number=$gsm label=$smarty.config.customers_gsm}
              {else}
                {$gsm|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last}<br />{/if}
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td class="t_border {$sort.city.isSorted} {$row_link_class}"{$row_link}>{$single->get('city')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.num.isSorted} {$row_link_class}"{$row_link}>{$single->get('num')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.main_trademark.isSorted} {$row_link_class}"{$row_link}>{$single->get('main_trademark_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.department.isSorted} {$row_link_class}"{$row_link}>{$single->get('department_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.company_department.isSorted} {$row_link_class}"{$row_link}>{$single->get('company_department')|escape|default:"&nbsp;"}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td class="t_border {$sort.emails.isSorted}">
            <div class="t_occupy_cell has_inline_add hright emails emails_{$single->modelName|lower}_{$single->get('id')}{if $single->get('emails') gt 0 && $single->checkPermissions('emails')} pointer" onmouseenter="showCommunicationsInfo(this, 'emails', '{$_module}', '{$_controller}', {$single->get('id')}, '{$single->get('archived_by')}')" onmouseleave="mclosetime()" onclick="openHref('{$smarty.server.SCRIPT_NAME}?{$module_param}={$_module}{if $_controller ne $_module}&amp;controller={$_controller}{/if}&amp;{$_controller}=communications&amp;communications={$single->get('id')}&amp;communication_type=emails{if $single->get('archived_by')}&amp;archive=1{/if}', '_self', event){/if}">
              <span class="emails_total">{if $single->get('emails')}{$single->get('emails')}{/if}</span>
            </div>
            {if $single->checkPermissions('emails_add')}
            <div class="inline_add" onmouseover="Event.stop(event)">
              <a href="#" onclick="return loadInlineAddCommunication(event, 'email', '{$_module}', '{$_controller}', {$single->get('id')});" title="{#add_email#|escape}"></a>
            </div>
            {/if}
          </td>
          <td class="t_border {$sort.position.isSorted} {$row_link_class}"{$row_link}>{$single->get('position')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.postal_code.isSorted} {$row_link_class}"{$row_link}>{$single->get('postal_code')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.mol.isSorted} {$row_link_class}"{$row_link}>{$single->get('mol')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.admit_VAT_credit.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>
            {if $single->get('admit_VAT_credit')}{#yes#}{else}{#no#}{/if}
          </td>
          <td class="t_border {$sort.modified.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$single->get('modified')|date_format:#date_short#|escape}</td>
          <td class="t_border {$sort.modified_by.isSorted} {$row_link_class}"{$row_link}>{$single->get('modified_by_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.company_name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$single->get('id')}" title="{#view#|escape}: {$single->get('company_name')|escape}">{$single->get('company_name')|escape|default:"&nbsp;"}</a></td>
          
          {strip}
            {if preg_match('#^Finance_.*$#i', $single->modelName)}
              {assign var='_module' value='finance'}
              {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
            {else}
              {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
              {capture assign='_controller'}{/capture}
            {/if}
          {/strip}
          <td class="t_border {$sort.tags.isSorted}" {if $single->getModelTags() && $single->get('available_tags_count') gt 0 && $single->checkPermissions('tags_view') && $single->checkPermissions('tags_edit')} onclick="changeTags({$single->get('id')}, '{$_module}', '{$_controller}'{if $redirect_to_url && $update_target}, '{$redirect_to_url}' + ($$('#{$update_target} .page_menu_current_page').length ? $$('#{$update_target} .page_menu_current_page')[0].innerHTML : 1){/if})" style="cursor: pointer;" title="{#tags_change#|escape}"{/if}>
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.phone.isSorted}{if !$use_asterisk} {$row_link_class}{/if}"{if !$use_asterisk} {$row_link}{/if}>
            {foreach from=$single->get('phone') item='phone' name='cdi'}
              {if $use_asterisk && $phone}
                {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='phone' number=$phone label=$smarty.config.customers_phone}
              {else}
                {$phone|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last}<br />{/if}
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td class="t_border {$sort.web.isSorted}{if !$single->get('web')} {$row_link_class}{/if}"{if !$single->get('web')} {$row_link}{/if}>
            {if is_array($single->get('web'))}
              {foreach from=$single->get('web') item='web' name='cdi'}
                <a href="http://{$web}" target="_blank">{$web|escape|default:"&nbsp;"}</a>{if !$smarty.foreach.cdi.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.fax.isSorted}{if !$use_asterisk} {$row_link_class}{/if}"{if !$use_asterisk} {$row_link}{/if}>
            {foreach from=$single->get('fax') item='fax' name='cdi'}
              {if $use_asterisk && $fax}
                {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='fax' number=$fax label=$smarty.config.customers_fax}
              {else}
                {$fax|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last}<br />{/if}
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>

    <td class="hcenter" nowrap="nowrap">
      {include file=`$theme->templatesDir`single_actions_list.html object=$single}
    </td>
  </tr>
{/if}
{foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error" colspan="47">{#no_items_found#|escape}</td>
  </tr>
{/foreach}
  <tr>
    <td class="t_footer" colspan="47"></td>
  </tr>
</table>
{if ('')}
  {include file="`$theme->templatesDir`_severity_legend.html" prefix=''}
{/if}
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
{capture assign='search_link'}{$smarty.server.PHP_SELF}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$controller}=subpanel&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$search_link
  use_ajax=$use_ajax
  hide_selection_stats=true
  session_param=$session_param
}
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
{if !$trademark_nomenclature}
</form>
{/if}