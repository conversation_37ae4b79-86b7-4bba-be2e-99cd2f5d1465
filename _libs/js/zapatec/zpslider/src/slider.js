/*
 *
 * Copyright (c) 2004-2005 by Zapatec, Inc.
 * http://www.zapatec.com
 * 1700 MLK Way, Berkeley, California,
 * 94709, U.S.A.
 * All rights reserved.
 *
 *
 */
Zapatec.sliderPath = Zapatec.getPath("Zapatec.SliderWidget");

Zapatec.Button=function(objArgs){if(arguments.length==0){objArgs={};}
Zapatec.Button.SUPERconstructor.call(this,objArgs);};Zapatec.Button.id="Zapatec.Button";Zapatec.inherit(Zapatec.Button,Zapatec.Widget);Zapatec.Button.prototype.init=function(objArgs){this.config.image=null;this.config.width=null;this.config.height=null;this.config.className=null;this.config.style=null;this.config.text="";this.config.preloadImages=true;this.config.overStyle=null;this.config.overClass=null;this.config.overImage=null;this.config.overAction=null;this.config.outAction=null;this.config.downStyle=null;this.config.downClass=null;this.config.downImage=null;this.config.downAction=null;this.config.downActionDelay=500;this.config.repeatAcceleration=0.8;this.config.repeatStartSpeed=55;this.config.repeatMaxSpeed=5;this.config.clickAction=null;this.config.idPrefix=null;this.config.themePath=Zapatec.zapatecPath+"../zpextra/themes/button/";Zapatec.Button.SUPERclass.init.call(this,objArgs);if(!this.config.idPrefix){this.config.idPrefix="zpButton"+this.id;}
this.container=null;this.statusContainer=null;this.internalContainer=null;this.img=null;this.enabled=true;this.isPressed=false;this.createButton();};Zapatec.Button.prototype.createButton=function(){Zapatec.Utils.createProperty(this,'container',Zapatec.Utils.createElement("span"));this.container.id=this.config.idPrefix+"Container";Zapatec.Utils.createProperty(this,'statusContainer',Zapatec.Utils.createElement("span"));this.statusContainer.className="mouseOut";this.statusContainer.id=this.config.idPrefix+"Status";this.container.appendChild(this.statusContainer);Zapatec.Utils.createProperty(this,'internalContainer',Zapatec.Utils.createElement("span"));this.internalContainer.className="internalContainer";this.internalContainer.id=this.config.idPrefix+"Internal";this.statusContainer.appendChild(this.internalContainer);Zapatec.Utils.addClass(this.container,this.getClassName({prefix:"zpButton"+(this.config.image!=null?"Image":""),suffix:"Container"}));if(this.config.width!=null){this.internalContainer.style.width=this.config.width+"px";}
if(this.config.height!=null){this.internalContainer.style.width=this.config.height+"px";}
if(this.config.className!=null){Zapatec.Utils.addClass(this.internalContainer,this.config.className);}
if(this.config.style!=null){this.applyStyle(this.config.style);}
var self=this;Zapatec.Utils.createProperty(this.container,'onmouseover',function(ev){return self.onmouseover(ev,false);});Zapatec.Utils.createProperty(this.container,'onmouseout',function(ev){return self.onmouseout(ev);});Zapatec.Utils.createProperty(this.container,'onmousedown',function(ev){return self.onmousedown(ev);});Zapatec.Utils.createProperty(this.container,'onmouseup',function(ev){return self.onmouseover(ev,true);});Zapatec.Utils.addEvent(document,'mouseup',function(ev){self._mousedown=false;});Zapatec.Utils.createProperty(this.container,'onclick',function(ev){return self.onclick(ev);});if(this.config.image!=null){Zapatec.Utils.createProperty(this,'img',document.createElement("img"));this.img.src=this.config.image;this.img.alt=this.config.text;this.img.title=this.config.text;this.internalContainer.appendChild(this.img);}else{this.internalContainer.innerHTML=this.config.text;this.internalContainer.style.whiteSpace="nowrap";}
if(this.config.preloadImages==true){this.preloadImages();}};Zapatec.Button.prototype.onmouseover=function(ev,mouseup){if(!this.isEnabled()){return false;}
if(typeof(ev)=='undefined'){ev=window.event;}
if(mouseup){this._mousedown=false;}
this.toggleClass("mouseOver");if(this.config.image!=null&&this.config.overImage!=null){this.img.src=this.config.overImage;}
if(this.config.overClass!=null){Zapatec.Utils.addClass(this.internalContainer,this.config.overClass);}
if(this.config.overStyle!=null){this.applyStyle(this.config.overStyle);}
if(this._timeoutId){clearTimeout(this._timeoutId);}
if(this._mousedown){this._repeatDownAction();}
if(this.config.overAction!=null){return this.config.overAction(ev,this);}
return true;};Zapatec.Button.prototype.onmouseout=function(ev){if(!this.isEnabled()){return false;}
if(typeof(ev)=='undefined'){ev=window.event;}
if(!this.isPressed){this.toggleClass("mouseOut");if(this.config.image!=null){this.img.src=this.config.image;}
if(this.config.outClass!=null){Zapatec.Utils.addClass(this.internalContainer,this.config.outClass);}}
else{this.toggleClass("mouseDown");if(this.config.image!=null&&this.config.downImage!=null){this.img.src=this.config.downImage;}
if(this.config.downClass!=null){Zapatec.Utils.addClass(this.internalContainer,this.config.downClass);}}
if(this.config.style!=null){this.applyStyle(this.config.style);}
if(this._mousedown){this._pauseRepeat();}
if(this.config.outAction!=null){return this.config.outAction(ev,this);}
return true;};Zapatec.Button.prototype.onmousedown=function(ev){if(!this.isEnabled()){return false;}
var self=this;if(typeof(ev)=='undefined'){ev=window.event;}
this._mousedown=true;this.toggleClass("mouseDown");if(this.config.image!=null&&this.config.downImage!=null){this.img.src=this.config.downImage;}
if(this.config.downClass!=null){Zapatec.Utils.addClass(this.internalContainer,this.config.downClass);}
if(this.config.downStyle!=null){this.applyStyle(this.config.downStyle);}
if(this.config.downAction!=null){this._timeoutId=setTimeout(function(){self._repeatSpeed=self.config.repeatStartSpeed;self._repeatDownAction(ev);},this.config.downActionDelay);return this.config.downAction(ev,this);}
return true;};Zapatec.Button.prototype.onclick=function(ev){if(!this.isEnabled()){return false;}
if(typeof(ev)=='undefined'){ev=window.event;}
if(this._timeoutId){clearTimeout(this._timeoutId);}
this._timeoutId=null;this._mousedown=false;if(this.config.clickAction!=null){return this.config.clickAction(ev,this);}
return true;};Zapatec.Button.prototype._repeatDownAction=function(ev){if(this.config.downAction!=null){this.config.downAction(ev,this);}
if(this._repeatSpeed>this.config.repeatMaxSpeed){this._repeatSpeed=Math.round(this.config.repeatAcceleration*this._repeatSpeed);}
var self=this;this._timeoutId=setTimeout(function(){self._repeatDownAction();},this._repeatSpeed);};Zapatec.Button.prototype._pauseRepeat=function(){if(this._timeoutId){clearTimeout(this._timeoutId);this._timeoutId=null;}};Zapatec.Button.prototype.preloadImages=function(){if(this.config.image!=null){var images=[this.config.image];if(this.config.overImage!=null){images.push(this.config.overImage);}
if(this.config.downImage!=null){images.push(this.config.downImage);}
Zapatec.Transport.preloadImages({urls:images});}};Zapatec.Button.prototype.setPressed=function(isPressed){this.isPressed=isPressed;if(isPressed){this.toggleClass('mouseDown');}else{this.toggleClass('mouseOut');}};Zapatec.Button.prototype.toggleClass=function(className){Zapatec.Utils.removeClass(this.statusContainer,"mouseOver");Zapatec.Utils.removeClass(this.statusContainer,"mouseOut");Zapatec.Utils.removeClass(this.statusContainer,"mouseDown");Zapatec.Utils.removeClass(this.statusContainer,"disabled");Zapatec.Utils.removeClass(this.internalContainer,this.config.overClass);Zapatec.Utils.removeClass(this.internalContainer,this.config.downClass);if(className!=null){Zapatec.Utils.addClass(this.statusContainer,className);}};Zapatec.Button.prototype.getContainer=function(){return this.container;};Zapatec.Button.prototype.applyStyle=function(style){Zapatec.Utils.applyStyle(this.internalContainer,style);};Zapatec.Button.prototype.isEnabled=function(){return this.enabled;};Zapatec.Button.prototype.enable=function(){this.enabled=true;this.toggleClass("mouseOut");};Zapatec.Button.prototype.disable=function(){this.enabled=false;this.toggleClass("disabled");};Zapatec.Button.prototype.setText=function(text){this.internalContainer.innerHTML=text;};Zapatec.Button.prototype.getText=function(){return this.internalContainer.innerHTML;};Zapatec.Button.setup=function(elRef,config){elRef=Zapatec.Widget.getElementById(elRef);if(elRef==null){return null;}
if(config==null){config={};}
var nodeName=elRef.nodeName.toLowerCase();var oldOverAction=config.overAction!=null?config.overAction:function(){return true;};config.overAction=function(ev){return((elRef.onmouseover!=null?elRef.onmouseover.call(ev):true)&&oldOverAction(ev));};var oldOutAction=config.outAction!=null?config.outAction:function(){return true;};config.outAction=function(ev){return((elRef.onmouseout!=null?elRef.onmouseout.call(ev):true)&&oldOutAction(ev));};var oldDownAction=config.downAction!=null?config.downAction:function(){return true;};config.downAction=function(ev){return((elRef.onmousedown!=null?elRef.onmousedown.call(ev):true)&&oldDownAction(ev));};var oldClickAction=config.clickAction!=null?config.clickAction:function(){return true;};config.clickAction=function(ev){return((elRef.onclick!=null?elRef.onclick.call(ev):true)&&oldClickAction(ev));};var submitAction=function(ev){if(elRef.form!=null&&elRef.zpHidden==null){var hidden=document.createElement("input");hidden.type='hidden';hidden.name=elRef.name;hidden.value=elRef.value;hidden.style.display='none';Zapatec.Utils.insertAfter(elRef,hidden);elRef.zpHidden=hidden;}
if(elRef.form&&elRef.form.onSubmit!=null){elRef.form.onSubmit();}
return((elRef.onclick!=null?elRef.onclick.call(ev):true)&&oldClickAction(ev)&&(elRef.form!=null?elRef.form.submit():true));};if(nodeName=='button'){config.text=elRef.value;}else if(nodeName=='img'){config.image=elRef.src;config.text=elRef.title||elRef.title;}else if(nodeName=='div'||nodeName=='span'){config.text=elRef.innerHTML;}else if(nodeName=='input'){config.text=elRef.value;if(elRef.type.toLowerCase()=='image'){config.image=elRef.src;config.clickAction=submitAction;}else if(elRef.type.toLowerCase()=='button'){}else if(elRef.type.toLowerCase()=='submit'){config.clickAction=submitAction;}else if(elRef.type.toLowerCase()=='reset'){config.clickAction=function(ev){(elRef.onclick!=null?elRef.onclick.call(ev):true)&&oldClickAction(ev)&&(elRef.form!=null?elRef.form.reset():true)};}else{return null;}}else{return null;}
var button=new Zapatec.Button(config);Zapatec.Utils.insertAfter(elRef,button.getContainer());elRef.disabled=true;elRef.style.display='none';return button;};Zapatec.Button.setupAll=function(elRef,config){if(typeof(elRef)=='string'){elRef=document.getElementById(elRef);}
if(elRef==null){return null;}
var childs=elRef.all?elRef.all:elRef.getElementsByTagName("*");function cloneConfig(){var cfg={};for(var option in config){cfg[option]=config[option];}
return cfg;}
for(var ii=0;ii<childs.length;ii++){if(childs[ii].nodeType==1&&(childs[ii].nodeName.toLowerCase()=='button'||childs[ii].nodeName.toLowerCase()=='input'&&(childs[ii].type.toLowerCase()=='image'||childs[ii].type.toLowerCase()=='button'||childs[ii].type.toLowerCase()=='submit'||childs[ii].type.toLowerCase()=='reset'))){Zapatec.Button.setup(childs[ii],cloneConfig());}}};Zapatec.Button.prototype.destroy=function(){this.container.onmouseover=null;this.container.onmouseout=null;this.container.onmousedown=null;this.container.onmouseup=null;this.container.onclick=null;this.internalContainer=null;this.statusContainer=null;this.container=null;return null;};Zapatec.Array=function(arr){if(!Zapatec.isArray(arr)){var array=[];for(var i=0;i<arguments.length;++i){array.push(arguments[i]);}
arr=array;}
arr.clear=function(){Zapatec.Array.clear(this);};arr.compact=function(){var compact=Zapatec.Array.compact(this);return Zapatec.Array(compact);};arr.indexOf=function(value){return Zapatec.Array.indexOf(this,value);};arr.without=function(){var args=[].slice.call(arguments,0);args.unshift(this);var without=Zapatec.Array.without.apply(Zapatec.Array,args);return Zapatec.Array(without);};arr.remove=function(){var args=[].slice.call(arguments,0);args.unshift(this);var cut=Zapatec.Array.remove.apply(Zapatec.Array,args);return Zapatec.Array(cut);};arr.each=function(func,reverse){var result;for(var index=reverse?this.length-1:0;reverse?(index>=0):(index<this.length);reverse?--index:++index){if(typeof func=="function"){result=func(index,this[index],this);if(result=="break"){break;}}}
if(result=="break"){return false;}
return true;};arr.isZpArray=true;return arr;};Zapatec.Array.compact=function(arr){var newArr=[];for(var item=0;item<arr.length;++item){if(arr[item]!==null&&typeof arr[item]!="undefined"){newArr.push(arr[item]);}}
return newArr;};Zapatec.Array.clear=function(arr){arr.length=0;};Zapatec.Array.indexOf=function(arr,value){for(var item=0;item<arr.length;++item){if(arr[item]===value){return item;}}
return-1;};Zapatec.Array.without=function(arr){var newArr=[],without;for(var item=0;item<arr.length;++item){without=false;for(var value=1;value<arguments.length;++value){if(arr[item]===arguments[value]){without=true;break;}}
if(!without){newArr.push(arr[item]);}}
return newArr;};Zapatec.Array.remove=function(arr){var newArr=[],without,value,start=1;if(arguments[1]&&arguments[1].length&&typeof arguments[1]=="object"){args=arguments[1];start=0;}else{args=arguments;}
for(var item=0;item<arr.length;++item){without=false;for(value=start;value<args.length;++value){if(item===args[value]){without=true;break;}}
if(!without){newArr.push(arr[item]);}}
return newArr;};Zapatec.Hash=function(hash){if(!hash||typeof hash!="object"){hash={};}
hash.hashRemove=function(){var args=[].slice.call(arguments,0);args.unshift(this);var without=Zapatec.Hash.remove.apply(Zapatec.Hash,args);return Zapatec.Hash(without);};hash.hashEach=function(func){var result=null;for(var prop in this){if(prop=="hashRemove"||prop=="hashEach"||prop=="hashIsEmpty"||prop=="isZpHash"){continue;}
if(typeof Object.prototype[prop]!="undefined"){continue;}
result=func(prop,this[prop],this);if(result=="break"){break;}}
if(result=="break"){return false;}
return true;};hash.hashIsEmpty=function(){var empty=true;this.hashEach(function(){empty=false;return"break";});return empty;};hash.isZpHash=true;return hash;};Zapatec.Hash.remove=function(hash){var newHash={},without,value,start=1;if(arguments[1]&&arguments[1].length&&typeof arguments[1]=="object"){args=arguments[1];start=0;}else{args=arguments;}
for(var item in hash){without=false;for(value=start;value<args.length;++value){if(item===args[value]){without=true;break;}}
if(!without){newHash[item]=hash[item];}}
return newHash;};Zapatec.Hash.getByPath=function(hash,path){if(!path||typeof path!="string"){Zapatec.Log({description:"Not a path passed to Zapatec.Hash.getByPath function!",type:"warning"});return{result:false};}
var paths=path.split(".");if(!paths.length){Zapatec.Log({description:"Wrong path passed to Zapatec.Hash.getByPath function!",type:"warning"});return{result:false};}
var item=0;var value=hash;var name="";var scope=null;while(paths[item]){if(value===null||typeof value=="undefined"){Zapatec.Log({description:"Incorrect path passed to Zapatec.Hash.getByPath function!",type:"warning"});return{result:false};}
name=paths[item].replace(/(\(\)|\[[^\[\]]+\])+/,"");try{scope=value;value=value[name];}catch(e){Zapatec.Log({description:"Incorrect path passed to Zapatec.Hash.getByPath function!",type:"warning"});return{result:false};}
paths[item]=paths[item].replace(name,"");while(paths[item]!=""){name=paths[item].match(/(\(\)|\[[^\[\]]+\])/)[1];if(name&&/\(\)$/.test(name)){try{value=value.call(scope||value);scope=null;}catch(e){Zapatec.Log({description:"Incorrect path passed to Zapatec.Hash.getByPath function!",type:"warning"});return{result:false};}}else if(name&&/\[["']?[^\[\]"']+["']?\]$/.test(name)){try{value=value[name.match(/\[["']?([^\[\]"']+)["']?\]/)[1]];scope=null;}catch(e){Zapatec.Log({description:"Incorrect path passed to Zapatec.Hash.getByPath function!",type:"warning"});return{result:false};}}
paths[item]=paths[item].replace(name,"");}
++item;}
if(typeof value=="undefined"){Zapatec.Log({description:"Incorrect path passed to Zapatec.Hash.getByPath function!",type:"warning"});return{result:false};}
return{result:true,value:value};};Zapatec.Hash.setByPath=function(hash,path,val){if(!path||typeof path!="string"){Zapatec.Log({description:"Not a path passed to Zapatec.Hash.setByPath function!",type:"warning"});return false;}
var paths=path.split(".");if(!paths.length){Zapatec.Log({description:"Wrong path passed to Zapatec.Hash.setByPath function!",type:"warning"});return false;}
var lastItem=paths[paths.length-1];var obj=hash;var getPath=paths.slice(0,-1).join(".");var arrIndexReg=/\[[^\[\]]+\]$/;if(arrIndexReg.test(lastItem)){getPath+=(getPath==""?"":".")+lastItem.replace(arrIndexReg,"");lastItem=lastItem.match(/\[["']?([^\[\]"']+)["']?\]$/)[1];}
if(getPath!=""){var obj=Zapatec.Hash.getByPath(hash,getPath).value;}
try{obj[lastItem]=val;}catch(e){Zapatec.Log({description:"Incorrect path passed to Zapatec.Hash.setByPath function!",type:"warning"});return false;}
return true;};Zapatec.isHtmlElement=function(el){if(!el||el.nodeType!=1){return false;}
return true;};Zapatec.isSizableObj=function(obj){if(obj&&obj.hasInterface&&obj.hasInterface("Zapatec.Sizable")){return true;}
return false;};Zapatec.isMovableObj=function(obj){if(obj&&obj.hasInterface&&obj.hasInterface("Zapatec.Movable")){return true;}
return false;};Zapatec.isArray=function(arr){if(arr&&typeof arr=="object"&&arr.constructor==Array){return true;}
return false;};Zapatec.isDate=function(date){if(date&&typeof date=="object"&&date.constructor==Date){return true;}
return false;};Zapatec.SRProp=function(obj){this.obj=obj;this.savedProps=new Zapatec.Hash();Zapatec.Utils.createProperty(obj,"restorer",this);}
Zapatec.SRProp.prototype.getSavedProps=function(){return this.savedProps;};Zapatec.SRProp.prototype.getObject=function(){return this.obj;};Zapatec.SRProp.prototype.saveProp=function(propName){if(typeof propName!="string"){return false;}
var value=Zapatec.Hash.getByPath(this.getObject(),propName);if(value.result){if(typeof this.getProp(propName)!="undefined"){var prop=this.getSavedProps()[propName]=Zapatec.Array(this.getSavedProps()[propName]);prop.push(value.value);prop.combination=true;Zapatec.Log({description:"The property '"+propName+"' now contains more than one value!",type:"warning"});}else{this.getSavedProps()[propName]=value.value;}
return true;}else{return false;}}
Zapatec.SRProp.prototype.saveProps=function(){var result=[];for(var i=0;i<arguments.length;++i){if(this.saveProp(arguments[i])){result.push(arguments[i]);}}
return result;}
Zapatec.SRProp.prototype.restoreProp=function(propName){if(typeof propName!="string"||typeof this.getSavedProps()[propName]=="undefined"){return false;}
var prop=this.getSavedProps()[propName];var combination=false,nextSibling=null;if(Zapatec.isArray(prop)&&prop.combination){prop=prop[prop.length-1];combination=true;}
if(propName.match(/parentNode$/)!==null&&prop&&typeof prop=="object"&&prop.appendChild){nextSibling=this.getSavedProps()[propName.replace(/parentNode/,"nextSibling")]||null;if(nextSibling&&nextSibling.parentNode==prop){prop.insertBefore(this.getObject(),nextSibling);}else{prop.appendChild(this.getObject());}
this.savedProps=this.getSavedProps().hashRemove(propName.replace(/parentNode/,"nextSibling"));}else{if(!Zapatec.Hash.setByPath(this.getObject(),propName,prop)){return false;}}
if(!combination){this.savedProps=this.getSavedProps().hashRemove(propName);}else{prop=this.getSavedProps()[propName];this.getSavedProps()[propName]=Zapatec.Array.without(prop,prop.length-1);}
return true;}
Zapatec.SRProp.prototype.restoreProps=function(propName){var result=[];for(var i=0;i<arguments.length;++i){if(this.restoreProp(arguments[i])){result.push(arguments[i]);}}
return result;}
Zapatec.SRProp.prototype.restoreAll=function(){var self=this;this.getSavedProps().hashEach(function(i){self.restoreProp(i);});}
Zapatec.SRProp.prototype.getProp=function(propName){return this.getSavedProps()[propName];}
Zapatec.SRProp.prototype.isEmpty=function(){return this.getSavedProps().hashIsEmpty();};Zapatec.SRProp.prototype.destroy=function(){this.getObject().restorer=null;for(var iProp in this){this[iProp]=null;}
return null;};Zapatec.CommandEvent={};Zapatec.CommandEvent.fireEvent=function(strEvent){if(!this.events[strEvent]){return;}
var arrListeners=this.events[strEvent].listeners.slice();this._setReturnedValue(null);this._setEventPropagation(true);for(var iListener=0;iListener<arrListeners.length;iListener++){var arrArgs=[].slice.call(arguments,1);arrListeners[iListener].apply(this,arrArgs);var result=this._getReturnedValue();if(!this._getEventPropagation()){return result;}
if(result=="re-execute"){this.fireEvent(strEvent);break;}else if(result=="parent-re-execute"){return result;}}
return this._getReturnedValue();};Zapatec.CommandEvent.returnValue=function(val){this._setReturnedValue(val);};Zapatec.CommandEvent._setReturnedValue=function(val){this.returnedValue=val;};Zapatec.CommandEvent._getReturnedValue=function(){return this.returnedValue;};Zapatec.CommandEvent.stopPropagation=function(){this._setEventPropagation(false);};Zapatec.CommandEvent._setEventPropagation=function(on){this.eventPropagation=on;};Zapatec.CommandEvent._getEventPropagation=function(){return this.eventPropagation;};Zapatec.GlobalEvents=new Zapatec.EventDriven();Zapatec.implement(Zapatec.GlobalEvents,"Zapatec.CommandEvent");Zapatec.GlobalEvents.init();Zapatec.Utils.getWidth=function(el){if(!Zapatec.isHtmlElement(el)){return false;}
return el.offsetWidth};Zapatec.Utils.getHeight=function(el){if(!Zapatec.isHtmlElement(el)){return false;}
return el.offsetHeight};Zapatec.Utils.setWidth=function(el,width){width=Math.round(width);if(!Zapatec.isHtmlElement(el)||width<=0){return false;}
var oldWidth=el.style.width,newWidth;el.style.width=width+"px";if(Zapatec.Utils.getWidth(el)!=width){newWidth=width-(Zapatec.Utils.getWidth(el)-width);if(newWidth>0){el.style.width=newWidth+"px";if(Zapatec.Utils.getWidth(el)!=width){el.style.width=oldWidth;Zapatec.Log({description:"Can't set the width - "+width+"px!",type:"warning"});return false;}}else{el.style.width=oldWidth;Zapatec.Log({description:"Can't set the width - "+width+"px!",type:"warning"});return false;}}
return true;};Zapatec.Utils.setHeight=function(el,height){height=Math.round(height);if(!Zapatec.isHtmlElement(el)||height<=0){return false;}
var oldHeight=el.style.height,newHeight;el.style.height=height+"px";if(Zapatec.Utils.getHeight(el)!=height){newHeight=height-(Zapatec.Utils.getHeight(el)-height);if(newHeight>0){el.style.height=newHeight+"px";if(Zapatec.Utils.getHeight(el)!=height){el.style.height=oldHeight;Zapatec.Log({description:"Can't set the height - "+height+"px!",type:"warning"});return false;}}else{el.style.height=oldHeight;Zapatec.Log({description:"Can't set the height - "+height+"px!",type:"warning"});return false;}}
return true;};Zapatec.Utils.fixateWidth=function(el){return Zapatec.Utils.setWidth(el,Zapatec.Utils.getWidth(el));};Zapatec.Utils.fixateHeight=function(el){return Zapatec.Utils.setHeight(el,Zapatec.Utils.getHeight(el));};Zapatec.Utils.makeSafelySizable=function(el,restorer){if(el.sizable){return true;}
if(!Zapatec.isHtmlElement(el)){return false;}
if(!restorer){restorer=el.restorer;}
if(!restorer||!restorer.getObject||restorer.getObject()!=el){restorer=new Zapatec.SRProp(el);}
restorer.saveProps("style.width","style.height","style.overflow");Zapatec.Utils.fixateWidth(el);Zapatec.Utils.fixateHeight(el);var overflow=Zapatec.Utils.getStyleProperty(el,"overflow");if(overflow==""||overflow=="visible"){Zapatec.Log({description:"There is the chance that this element with overflow visible will not be sized correctly!",type:"warning"});}
el.sizable=true;return true;};Zapatec.Utils.restoreOfSizing=function(el){if(!el||!el.restorer||!el.sizable){return false;}
el.restorer.restoreProps("style.width","style.height","style.overflow");if(el.restorer.isEmpty()){el.restorer.destroy();}
el.sizable=false;return true;};Zapatec.Utils.makeSafelyMovable=function(el,within,restorer){if(!Zapatec.isHtmlElement(el)){return false;}
if(!within){within=document.body;}
if(el.within==within){return true;}
if(!restorer){restorer=el.restorer;}
if(!restorer||!restorer.getObject||restorer.getObject()!=el){restorer=new Zapatec.SRProp(el);}
el.within=within;if(within!=document.body&&within.style.position!="absolute"){restorer.saveProp("within.style.position");within.style.position="relative";}
if(within!=document.body){var pos1=Zapatec.Utils.getElementOffset(within);}else{var pos1={x:0,y:0};}
var pos2=Zapatec.Utils.getElementOffset(el);var x=pos2.x-pos1.x;var y=pos2.y-pos1.y;restorer.saveProps("style.left","style.top");el.style.left=x+"px";el.style.top=y+"px";if(el.style.position!="absolute"){restorer.saveProp("style.position");el.style.position="absolute";}
if(el.parentNode!=within){restorer.saveProps("parentNode","nextSibling");within.appendChild(el);}
restorer.saveProp("style.margin");el.style.margin="0px";return true;};Zapatec.Utils.moveTo=function(el,x,y){if(!Zapatec.isHtmlElement(el)){return false;}
var pos=null;if(Zapatec.FixateOnScreen.isRegistered(el)){pos=Zapatec.FixateOnScreen.correctCoordinates(x,y);if(Zapatec.is_ie&&!Zapatec.is_ie7){el.style.left=pos.x;el.style.top=pos.y;return true;}}else{pos={x:parseInt(x,10)+"px",y:parseInt(y,10)+"px"};}
if(x||x===0){el.style.left=pos.x;}
if(y||y===0){el.style.top=pos.y;}
return true;};Zapatec.Utils.getPos=function(el){if(!Zapatec.isHtmlElement(el)){return false;}
var pos=null;if(pos=Zapatec.FixateOnScreen.parseCoordinates(el)){return pos;}
return{x:el.offsetLeft,y:el.offsetTop}};Zapatec.Utils.moveFor=function(el,offsetX,offsetY){var oldPos=Zapatec.Utils.getPos(el);if(oldPos){return Zapatec.Utils.moveTo(el,oldPos.x+offsetX,oldPos.y+offsetY);}else{return false;}};Zapatec.Utils.restoreOfMove=function(el){if(!el.within)return false;el.restorer.restoreProps("style.position","parentNode","style.margin","nextSibling","within.style.position","style.left","style.top");if(el.restorer.isEmpty()){el.restorer.destroy();}
el.within=null;return true;};Zapatec.Utils.getElementsByAttribute=function(attribute,value,within,recursive,match){if(!attribute){return false;}
within=Zapatec.Widget.getElementById(within);within||(within=document.body);var element=within.firstChild;result=[];while(element){if(element[attribute]){if(typeof value=="undefined"||element[attribute]==value){result.push(element);}else if(match&&typeof element[attribute]=="string"&&element[attribute].indexOf(value)!=-1){result.push(element);}};if(recursive&&element.hasChildNodes()){result=result.concat(Zapatec.Utils.getElementsByAttribute(attribute,value,element,recursive,match));}
element=element.nextSibling};return Zapatec.Array(result);};Zapatec.Utils.applyToElements=function(constructor,elements,config,configOption){if(typeof constructor!="function"||!Zapatec.isArray(elements)){return false;}
elements=Zapatec.Array(elements);if(!configOption){configOption="container";}
if(!config||typeof config!="object"){config={};}
var result=Zapatec.Array();elements.each(function(index,element){config[configOption]=element;result.push(new constructor(config));});return result;};Zapatec.Utils.img2div=function(el){if(!Zapatec.isHtmlElement(el)){return null;}
if((/img/i).test(el.nodeName)){var div=document.createElement('div');var objImage=new Image();objImage.onload=function(){div.style.width=objImage.width+'px';div.style.height=objImage.height+'px';div.style.fontSize='0px';objImage.onload=null;};objImage.src=el.src;div.style.backgroundImage='url('+el.src+')';div.style.backgroundColor='transparent';var id=el.id;var className=el.className;el.parentNode.replaceChild(div,el);div.id=id;div.className=className;return div}else{return el}};Zapatec.Utils.getElementPath=function(element,parent){if(!Zapatec.isHtmlElement(element)||!Zapatec.isHtmlElement(parent)){return false;}
var res=[];var el=element;while(el&&el!=parent){var number=1;while(el.previousSibling){++number;el=el.previousSibling;}
res.unshift(number);el=el.parentNode;}
return res.join("-");};Zapatec.Utils.cover=Zapatec.Utils.createElement("div");Zapatec.Utils.cover.style.overflow="hidden";Zapatec.Utils.cover.style.backgroundImage="url("+Zapatec.zapatecPath+"zpempty.gif)";Zapatec.Utils.cover.style.display="none";Zapatec.Utils.cover.id="zpCoverControl";Zapatec.Utils.cover.show=function(zIndex,cursor,mouseMoveHandler,mouseUpHandler){if(!this.parentNode){document.body.appendChild(this);}
if(this.style.display!="none"){this.hide();}
this.style.display="block";Zapatec.Utils.makeSafelyMovable(this,null,document.body);var x=0;var y=0;y+=Zapatec.Utils.getPageScrollY();x+=Zapatec.Utils.getPageScrollX();Zapatec.Utils.moveTo(this,x,y);var dim=Zapatec.Utils.getWindowSize();this.style.width=dim.width+"px";this.style.height=dim.height+"px";Zapatec.FixateOnScreen.register(this);this.style.zIndex=zIndex;this.style.cursor=cursor;if(typeof mouseMoveHandler=="function"){Zapatec.Utils.addEvent(this,'mousemove',mouseMoveHandler);}
if(typeof mouseUpHandler=="function"){Zapatec.Utils.addEvent(this,'mouseup',mouseUpHandler);}
this.mouseMoveHandler=mouseMoveHandler;this.mouseUpHandler=mouseUpHandler;};Zapatec.Utils.cover.hide=function(){Zapatec.FixateOnScreen.unregister(this);if(typeof this.mouseMoveHandler=="function"){Zapatec.Utils.removeEvent(this,'mousemove',this.mouseMoveHandler);}
if(typeof this.mouseUpHandler=="function"){Zapatec.Utils.removeEvent(this,'mouseup',this.mouseUpHandler);}
this.mouseMoveHandler=null;this.mouseUpHandler=null;this.style.zIndex="";this.style.cursor="";this.style.display="none";};Zapatec.Movable={};Zapatec.Movable.setPosition=function(x,y){if(!this.isMovableSafely()){Zapatec.Log({description:"The object with ID '"+this.id+"' was not prepared for moving! Use obj.makeMovable() to do so!",type:"warning"});return false;}
var msgValue=null,moveConfig=this.getMoveConfig();if(x||x===0){msgValue=x;x=this._parseCoordinate(x,"x",moveConfig.moveLayer);if(!x&&x!==0){Zapatec.Log({description:"The X coordinate "+msgValue+" can not be set for object with ID '"+this.id+"'!",type:"warning"});return false;}}
if(y||y===0){msgValue=y;y=this._parseCoordinate(y,"y",moveConfig.moveLayer);if(!y&&y!==0){Zapatec.Log({description:"The Y coordinate "+msgValue+" can not be set for object with ID '"+this.id+"'!",type:"warning"});return false;}}
var elements=Zapatec.Array(this.getMovableElements());if(this.fireEvent("beforePositionChange",x,y)===false){return false;}
if(Zapatec.GlobalEvents.fireEvent("beforePositionChange",x,y,this)===false){return false;}
this._proceedElementsCoords(x,y,elements);if(this.isMoving()){this.fireEvent("onMove",x||this.getPosition().x,y||this.getPosition().y);Zapatec.GlobalEvents.fireEvent("onMove",x||this.getPosition().x,y||this.getPosition().y,this);}
this.fireEvent("onPositionChange",x||this.getPosition().x,y||this.getPosition().y);Zapatec.GlobalEvents.fireEvent("onPositionChange",x||this.getPosition().x,y||this.getPosition().y,this);return true;};Zapatec.Movable.setOrientedPosition=function(x,y){switch(this.getMoveConfig().orientation){case"vertical":{return this.setPosition(y,x);}
case"horizontal":{return this.setPosition(x,y);}}
return false;};Zapatec.Movable.setPagePosition=function(x,y){var moveConfig=this.getMoveConfig();if(moveConfig.moveLayer==document.body){return this.setPosition(x,y);}
var msgValue=null;if(x||x===0){msgValue=x;x=this._parseCoordinate(x,"x",document.body);if(!x&&x!==0){Zapatec.Log({description:"The X page coordinate "+msgValue+" can not be set for object with ID '"+this.id+"'!",type:"warning"});return false;}}
if(y||y===0){msgValue=y;y=this._parseCoordinate(y,"y",document.body);if(!y&&y!==0){Zapatec.Log({description:"The Y page coordinate "+msgValue+" can not be set for object with ID '"+this.id+"'!",type:"warning"});return false;}}
var pos=Zapatec.Utils.getElementOffset(moveConfig.moveLayer);return this.setPosition((x||x===0)?(x-pos.x):x,(y||y===0)?(y-pos.y):y);};Zapatec.Movable.setScreenPosition=function(x,y){var moveConfig=this.getMoveConfig();var msgValue=null;if(x||x===0){msgValue=x;x=this._parseCoordinate(x,"x",window);if(!x&&x!==0){Zapatec.Log({description:"The X screen coordinate "+msgValue+" can not be set for object with ID '"+this.id+"'!",type:"warning"});return false;}}
if(y||y===0){msgValue=y;y=this._parseCoordinate(y,"y",window);if(!y&&y!==0){Zapatec.Log({description:"The Y screen coordinate "+msgValue+" can not be set for object with ID '"+this.id+"'!",type:"warning"});return false;}}
if(moveConfig.moveLayer!=document.body){var pos=Zapatec.Utils.getElementOffset(moveConfig.moveLayer);}else{var pos={x:0,y:0};}
return this.setPosition((x||x===0)?(x-pos.x):x,(y||y===0)?(y-pos.y):y);};Zapatec.Movable.moveFor=function(offsetLeft,offsetTop){var pos=this.getPosition();return this.setPosition(offsetLeft==null?null:pos.x+offsetLeft,offsetTop==null?null:pos.y+offsetTop);};Zapatec.Movable.getPosition=function(){var el=this.getMovableMeasurement();if(Zapatec.isHtmlElement(el)||(el&&typeof el=="object"&&typeof el.x=="number"&&typeof el.y=="number")){return Zapatec.Utils.getPos(el)||el;}
Zapatec.Log({description:"Can't calculate position for object with ID '"+this.id+"'!",type:"warning"});return false;};Zapatec.Movable.getPagePosition=function(){var el=this.getMovableMeasurement();if(Zapatec.isHtmlElement(el)||(el&&typeof el=="object"&&typeof el.x=="number"&&typeof el.y=="number")){return Zapatec.Utils.getElementOffset(el)||el;}
Zapatec.Log({description:"Can't calculate screen position for object with ID '"+this.id+"'!",type:"warning"});return false;};Zapatec.Movable.getScreenPosition=function(){var pos=this.getPagePosition();pos.x-=Zapatec.Utils.getPageScrollX();pos.y-=Zapatec.Utils.getPageScrollY();return pos;};Zapatec.Movable.startMove=function(){if(!this.isMovableSafely()){Zapatec.Log({description:"The object with ID '"+this.id+"' was not prepared for moving! Use obj.makeMovable() to do so!",type:"warning"});return false;}
this.fireEvent("moveStart");Zapatec.GlobalEvents.fireEvent("moveStart",this);this._setMovingState(true);return true;};Zapatec.Movable.endMove=function(){if(!this.isMoving()){Zapatec.Log({description:"The moving for object with ID '"+this.id+"' was not started!",type:"warning"});return false;}
this.fireEvent("moveEnd");Zapatec.GlobalEvents.fireEvent("moveEnd",this);this._setMovingState(false);return true;};Zapatec.Movable.isMoving=function(){return this.movingState;};Zapatec.Movable._setMovingState=function(on){this.movingState=on;};Zapatec.Movable.isMovableSafely=function(){return this.safelyMovable;};Zapatec.Movable._setMovableSafely=function(on){this.safelyMovable=on;};Zapatec.Movable.makeMovable=function(){if(!this.requireInterface("Zapatec.CommandEvent")){return false;}
if(this.isMovableSafely()){return true;}
var elements=Zapatec.Array(this.getMovableElements()),self=this,moveConfig=this.getMoveConfig();success=elements.each(function(index,movable){if(Zapatec.isHtmlElement(movable)){if(moveConfig.preserveSizes&&!Zapatec.Utils.makeSafelySizable(movable)){return"break";}
if(!Zapatec.Utils.makeSafelyMovable(movable,moveConfig.moveLayer)){return"break";}
self.createProperty(movable,"moveObj",self);}else if(Zapatec.isMovableObj(movable)){if(!movable.makeMovable()){return"break";}}});if(!success){this.restoreOfMove();Zapatec.Log({description:"Can not make the object with ID '"+this.id+"' movable!"});return false;}
this._setMovableSafely(true);return true;};Zapatec.Movable.restoreOfMove=function(){if(!this.isMovableSafely()){return true;}
var elements=Zapatec.Array(this.getMovableElements());var self=this;elements.each(function(index,movable){if(Zapatec.isHtmlElement(movable)){Zapatec.Utils.restoreOfMove(movable);if(self.getMoveConfig().preserveSizes){Zapatec.Utils.restoreOfSizing(movable);}
movable.moveObj=null;}else if(Zapatec.isMovableObj(movable)){movable.restoreOfMove();}},true);this._setMovableSafely(false);return true;};Zapatec.Movable.getMovableMeasurement=function(){return this.getContainer();};Zapatec.Movable.getMoveConfig=function(){return this.getConfiguration();};Zapatec.Movable.setMoveConfig=function(config){this.reconfigure(config);};Zapatec.Movable.getMovableElements=function(){return this.getContainer();};Zapatec.Movable.getContainer=function(){return this.getMoveConfig().container;};Zapatec.Movable._parseCoordinate=function(coord,dimension,within){switch(true){case(typeof coord=="number"):{if(within==window){coord+=Zapatec.Utils["getPageScroll"+dimension.toUpperCase()]();}
break;}
case((/^\d+px$/).test(String(coord))):{coord=parseInt(coord,10);if(within==window){coord+=Zapatec.Utils["getPageScroll"+dimension.toUpperCase()]();}
break;}
case((/^(left|top|bottom|right|center)$/i).test(String(coord))):{coord=this._parseWordCoordinate(coord,dimension,within);break;}}
return this._canSetCoordinate(coord,dimension,within);};Zapatec.Movable._parseWordCoordinate=function(coord,dimension,within){if((/(left|right)/i).test(String(coord))&&dimension.toUpperCase()!="X"){return false;}
if((/(top|bottom)/i).test(String(coord))&&dimension.toUpperCase()!="Y"){return false;}
var dim=(dimension.toUpperCase()=="X")?"Left":"Top";var sizeDim=(dimension.toUpperCase()=="X")?"Width":"Height";var parsedCoord=0;var wSize=null;if(Zapatec.isHtmlElement(within)){parsedCoord=within["scroll"+dim];wSize=Zapatec.Utils["get"+sizeDim](within);}else if(within==window){parsedCoord=Zapatec.Utils["getPageScroll"+dimension.toUpperCase()]();wSize=Zapatec.Utils.getWindowSize()[sizeDim.toLowerCase()];}else if(within&&typeof within=="object"){parsedCoord+=within["scroll"+dim]||0;wSize=within[sizeDim.toLowerCase()];}else{return false;}
var measurement=this.getMovableMeasurement();var mSize=null;if(Zapatec.isHtmlElement(measurement)){mSize=Zapatec.Utils["get"+sizeDim](measurement);}else if(measurement&&typeof measurement=="object"){mSize=measurement[sizeDim.toLowerCase()];}
switch(coord){case"left":case"top":{break;}
case"LEFT":case"TOP":{parsedCoord-=mSize;break;}
case"right":case"bottom":{parsedCoord+=wSize-mSize;break;}
case"RIGHT":case"BOTTOM":{parsedCoord+=wSize;break;}
case"center":{parsedCoord+=Math.round((wSize-mSize)/2);break;}
case"CENTER":{parsedCoord+=Math.round(wSize/2);break;}
default:{parsedCoord=null;break;}}
if(!parsedCoord&&parsedCoord!==0){return false;}else{return parsedCoord;}};Zapatec.Movable._canSetCoordinate=function(coord,dimension,within){if(typeof coord!="number"){return false;}
var moveConfig=this.getMoveConfig();if(within!=moveConfig.moveLayer){return coord;}
var limitObj=moveConfig.limit;var dim=dimension.toUpperCase();var direction=moveConfig.direction.toLowerCase();var correction=this._getMovableShape();if(dim=="X"&&direction=="vertical"){return this._handleCoordOverflow(this.getPosition().x);}else if(dim=="Y"&&direction=="horizontal"){return this._handleCoordOverflow(this.getPosition().y);}
if((limitObj["min"+dim]||limitObj["min"+dim]===0)&&coord<limitObj["min"+dim]+correction["min"+dim]){return this._handleCoordOverflow(limitObj["min"+dim]+correction["min"+dim]);}
if((limitObj["max"+dim]||limitObj["max"+dim]===0)&&coord>limitObj["max"+dim]+correction["max"+dim]){return this._handleCoordOverflow(limitObj["max"+dim]+correction["max"+dim]);}
return coord;};Zapatec.Movable._handleCoordOverflow=function(limit,dimension){return false;};Zapatec.Movable._getMovableShape=function(){var measurement=this.getMovableMeasurement();var obj={minX:0,maxX:0,minY:0,maxY:0};switch(this.getMoveConfig().followShape){case(true):{if(!Zapatec.isHtmlElement(measurement)){return obj;}
obj.maxX=-Zapatec.Utils.getWidth(measurement);obj.maxY=-Zapatec.Utils.getHeight(measurement);return obj}
default:{return obj;}}};Zapatec.Movable.getMovingPoint=function(){return this.getScreenPosition();};Zapatec.Movable._proceedElementsCoords=function(x,y,elements){elements=Zapatec.Array(elements);var measurement=this.getMovableMeasurement();var pos=null;if(Zapatec.isHtmlElement(measurement)){pos=Zapatec.Utils.getPos(measurement);}else if(measurement&&typeof measurement=="object"){pos={x:(typeof measurement.x=="number")?measurement.x:null,y:(typeof measurement.y=="number")?measurement.y:null};if(!measurement.x||!measurement.y){pos=null;}}
elements.each(function(index,movable){var mX=x,mY=y;if(pos){var mPos=null;if(Zapatec.isHtmlElement(movable)){mPos=Zapatec.Utils.getPos(movable);}else if(Zapatec.isMovableObj(movable)){mPos=movable.getPosition();}else{return;}
if(x||x===0){mX=mPos.x+(x-pos.x);}
if(y||y===0){mY=mPos.y+(y-pos.y);}}
if(Zapatec.isHtmlElement(movable)){Zapatec.Utils.moveTo(movable,mX,mY);}else if(Zapatec.isMovableObj(movable)){movable.setPosition(mX,mY);}});};Zapatec.Movable.sortElementsByPath=function(elements){elements=Zapatec.Array(elements);elements.each(function(index,element){var path=Zapatec.Utils.getElementPath(element,document.body);if(!path){return;}
path=Zapatec.Array(path.split("-"));for(var i=index;i<elements.length;++i){var compPath=Zapatec.Utils.getElementPath(elements[i],document.body);if(!compPath){continue;}
compPath=Zapatec.Array(compPath.split("-"));path.each(function(k,pathPiece,whole){if(parseInt(pathPiece,10)>(parseInt(compPath[k],10)||0)){return"break";}else if(parseInt(pathPiece,10)<parseInt(compPath[k],10)){var el=elements[index]
elements[index]=elements[i];elements[i]=el;path=compPath;return"break";}});}});return true;};Zapatec.Utils.Movable=function(config){Zapatec.Utils.Movable.SUPERconstructor.call(this,config);};Zapatec.Utils.Movable.id="Zapatec.Utils.Movable";Zapatec.inherit(Zapatec.Utils.Movable,Zapatec.Widget);Zapatec.implement(Zapatec.Utils.Movable,"Zapatec.CommandEvent");Zapatec.implement(Zapatec.Utils.Movable,"Zapatec.Movable");Zapatec.Utils.Movable.prototype.init=function(config){Zapatec.Utils.Movable.SUPERclass.init.call(this,config);var self=this;var elements=null;if(this.getConfiguration().makeMovable){this.makeMovable();}else{elements=Zapatec.Array(this.getMovableElements());elements.each(function(index,movable){if(Zapatec.isHtmlElement(movable)){self.createProperty(movable,"moveObj",self);}});}};Zapatec.Utils.Movable.prototype.configure=function(config){this.defineConfigOption("synchronize",[]);this.defineConfigOption("container",null);this.defineConfigOption("limit",{minX:null,maxX:null,minY:null,maxY:null});this.defineConfigOption("direction","both");this.defineConfigOption("moveLayer",document.body);this.defineConfigOption("followShape","LT");this.defineConfigOption("preserveSizes",true);this.defineConfigOption("makeMovable",true);this.defineConfigOption("theme",null);Zapatec.Utils.Movable.SUPERclass.configure.call(this,config);config=this.getConfiguration();if(!config.limit||typeof config.limit!="object"){config.limit={minX:null,maxX:null,minY:null,maxY:null};}
var self=this;config.synchronize=Zapatec.Array(config.synchronize);config.synchronize.each(function(index,element){if(element===null){return;}
element=Zapatec.Widget.getElementById(element);element=Zapatec.Utils.img2div(element);if(!element){Zapatec.Log({description:"Wrong element in synchronize array for the movable object with ID '"+self.id+"'!"});}else{config.synchronize[index]=element;}});config.container=Zapatec.Widget.getElementById(config.container);config.container=Zapatec.Utils.img2div(config.container);if(!Zapatec.isHtmlElement(config.container)){Zapatec.Log({description:"Wrong element passed as container for the movable object with ID '"+self.id+"'!"});}
this.movableElements=Zapatec.Array(config.synchronize.concat(config.container));if(this.movableElements.length>1){this.sortElementsByPath(this.movableElements);}
if(!Zapatec.isHtmlElement(config.moveLayer=Zapatec.Widget.getElementById(config.moveLayer))){config.moveLayer=document.body;}};Zapatec.Utils.Movable.prototype.reconfigure=function(config){Zapatec.Utils.Movable.SUPERclass.reconfigure.call(this,config);};Zapatec.Utils.Movable.prototype.getMovableElements=function(){return this.movableElements;};Zapatec.Draggable={};Zapatec.Draggable.makeDraggable=function(){if(!this.requireInterface("Zapatec.Movable")){return false;}
if(!this.requireInterface("Zapatec.CommandEvent")){return false;}
var draggables=Zapatec.Array(this.getDraggableElements());var hooks=Zapatec.Array(this._getDraggableHooks());var self=this,result=false;var config=this.getDragConfig();if(config.method!="cut"){this.setDragConfig({eventCapture:false});}
var listenersObj=this._getRestorer().getSavedProps()["dragListeners"]={mousedown:function(ev){return self.dragStart(ev);},mousemove:function(ev){if(self.isDragging()){return self.dragMove(ev);}},mouseup:function(ev){if(self.isDragging()){return self.dragEnd(ev);}}};hooks.each(function(index,hook){if(!Zapatec.isHtmlElement(hook)){return;}
result=true;if(Zapatec.is_gecko){hook.style.setProperty("-moz-user-select","none","");}
Zapatec.Utils.addEvent(hook,'mousedown',listenersObj.mousedown);if(config.eventCapture){Zapatec.Utils.addEvent(hook,'mousemove',listenersObj.mousemove);Zapatec.Utils.addEvent(hook,'mouseup',listenersObj.mouseup);}});draggables.each(function(index,draggable){if(!Zapatec.isHtmlElement(draggable)){return;}
self.createProperty(draggable,"dragObj",self);});return result;};Zapatec.Draggable.dragStart=function(ev){if(!this.canDrag()){return true;}
ev=ev||window.event;var iButton=ev.button||ev.which;if(iButton>1){return false;}
var self=this;var config=this.getDragConfig();if(this.fireEvent("beforeDragInit",ev)===false){return true;}
if(Zapatec.GlobalEvents.fireEvent("beforeDragInit",ev,this)===false){return true;}
this._setDragging(true);if(config.eventCapture&&(config.method=="dummy"||config.method=="copy")){var draggables=Zapatec.Array(this.getDraggableElements());draggables.each(function(index,draggable){draggable.restorer.saveProp("style.zIndex");draggable.style.zIndex=2000001;});}
this._proceedDraggableElements("dragStart");var oPos=Zapatec.Utils.getMousePos(ev);var mouseX=oPos.pageX;var mouseY=oPos.pageY;this.makeMovable();this.startMove();var elements=Zapatec.Array(this.getMovableElements());elements.each(function(index,movable){if(Zapatec.isHtmlElement(movable)){movable.restorer.saveProp("style.zIndex");movable.style.zIndex=1000000+(parseInt(movable.style.zIndex,10)||0);self._proceedDragStyles(movable,"dragStart");if(config.eventCapture){movable.restorer.saveProp("style.cursor");movable.style.cursor="move";}}else if(Zapatec.isMovableObj(movable)){var elems=Zapatec.Array(movable.getMovableElements());elems.each(arguments.calee);}});Zapatec.Utils.cover.show(config.eventCapture?999999:2000000,"move",function(ev){return self.dragMove(ev);},function(ev){return self.dragEnd(ev);});this._setMovingPoint(mouseX,mouseY);this.fireEvent("onDragInit",ev);Zapatec.GlobalEvents.fireEvent("onDragInit",ev,this);if(config.stopEvent){return Zapatec.Utils.stopEvent(ev);}else{return true;}};Zapatec.Draggable.dragMove=function(ev){if(!this.isDragging()){return true;}
ev=ev||window.event;if(this.fireEvent("beforeDragMove",ev)===false){return true;}
if(Zapatec.GlobalEvents.fireEvent("beforeDragMove",ev,this)===false){return true;}
if(Zapatec.Utils.cover.style.zIndex!=2000000){var config=this.getDragConfig();if(config.eventCapture&&(config.method=="dummy"||config.method=="copy")){var draggables=Zapatec.Array(this.getDraggableElements());draggables.each(function(index,draggable){draggable.restorer.restoreProp("style.zIndex");});}
Zapatec.Utils.cover.style.zIndex=2000000;}
var oPos=Zapatec.Utils.getMousePos(ev);var mouseX=oPos.pageX;var mouseY=oPos.pageY;var movePoint=this.getMovingPoint();this.moveFor(mouseX-movePoint.x,mouseY-movePoint.y);this._setMovingPoint(mouseX,mouseY);this.fireEvent("onDragMove",ev);Zapatec.GlobalEvents.fireEvent("onDragMove",ev,this);return Zapatec.Utils.stopEvent(ev);};Zapatec.Draggable.dragEnd=function(ev){if(!this.isDragging()){return true;}
ev=ev||window.event;var self=this;if(this.fireEvent("beforeDragEnd",ev)===false){return true;}
if(Zapatec.GlobalEvents.fireEvent("beforeDragEnd",ev,this)===false){return true;}
var config=this.getDragConfig();if(config.eventCapture&&(config.method=="dummy"||config.method=="copy")){var draggables=Zapatec.Array(this.getDraggableElements());draggables.each(function(index,draggable){draggable.restorer.restoreProp("style.zIndex",true);});}
var elements=Zapatec.Array(this.getMovableElements());elements.each(function(index,movable){if(Zapatec.isHtmlElement(movable)){movable.restorer.restoreProp("style.zIndex");movable.restorer.restoreProp("style.cursor");self._proceedDragStyles(movable,"dragEnd");}else if(Zapatec.isMovableObj(movable)){var elems=Zapatec.Array(movable.getMovableElements());elems.each(arguments.calee);}});this._proceedDraggableElements("dragEnd");Zapatec.Utils.cover.hide();this._setMovingPoint(null,null);this._setDragging(false);this.endMove();this.fireEvent("onDragEnd",ev);Zapatec.GlobalEvents.fireEvent("onDragEnd",ev,this);return Zapatec.Utils.stopEvent(ev);};Zapatec.Draggable.restorePos=function(){this.restoreOfMove();};Zapatec.Draggable.restoreOfDrag=function(){var listenersObj=this._getRestorer().getSavedProps()["dragListeners"];if(!listenersObj){return false;}
this.restoreOfMove();var hooks=Zapatec.Array(this._getDraggableHooks());var draggables=Zapatec.Array(this.getDraggableElements());var self=this;var config=this.getDragConfig();hooks.each(function(index,hook){if(!Zapatec.isHtmlElement(hook)){return;}
if(Zapatec.is_gecko){hook.style.setProperty("-moz-user-select","","");}
Zapatec.Utils.removeEvent(hook,'mousedown',listenersObj.mousedown);if(config.eventCapture){Zapatec.Utils.removeEvent(hook,'mousemove',listenersObj.mousemove);Zapatec.Utils.removeEvent(hook,'mouseup',listenersObj.mouseup);}});draggables.each(function(index,draggable){if(!Zapatec.isHtmlElement(draggable)){return;}
draggable.dragObj=null;});return true;};Zapatec.Draggable.getDraggableElements=function(){return this.getContainer();};Zapatec.Draggable._getDraggableHooks=function(){return this.getContainer();};Zapatec.Draggable.getContainer=function(){return this.getDragConfig().container;};Zapatec.Draggable.isDragging=function(){return this.dragging;};Zapatec.Draggable.canDrag=function(){return this.canDrag;};Zapatec.Draggable._setCanDrag=function(on){this.canDrag=on;};Zapatec.Draggable.getDragConfig=function(){return this.getConfiguration();};Zapatec.Draggable.setDragConfig=function(config){this.reconfigure(config);};Zapatec.Draggable._setDragging=function(on){this.dragging=on;};Zapatec.Draggable._handleCoordOverflow=function(limit,dimension){if(!this.isDragging()){Zapatec.Movable._handleCoordOverflow.call(this,limit,dimension);}
return limit;};Zapatec.Draggable._getRestorer=function(){if(!this.restorer){this.restorer=new Zapatec.SRProp(this);}
return this.restorer;};Zapatec.Draggable._proceedDraggableElements=function(dragState){var config=this.getDragConfig(),restorer=this._getRestorer(),copies=null,measurement=null,self=this,listenersObj=restorer.getProp("dragListeners");function toggleEvents(action,hooks,listenersObj){hooks.each(function(index,hook){Zapatec.Utils[action+"Event"](hook,"mousedown",listenersObj.mousedown);if(config.eventCapture){Zapatec.Utils[action+"Event"](hook,'mousemove',listenersObj.mousemove);Zapatec.Utils[action+"Event"](hook,'mouseup',listenersObj.mouseup);}});}
switch(config.method){case"copy":case"dummy":{if(dragState=="dragStart"){var elements=Zapatec.Array(this.getDraggableElements());var hooks=Zapatec.Array(this._getDraggableHooks());copies=Zapatec.Array();toggleEvents("remove",hooks,listenersObj);elements.each(function(index,movable){if(Zapatec.isHtmlElement(movable)){var newNode=movable.cloneNode(config.copyChilds);newNode.dragObj=self;if(config.eventCapture){Zapatec.Utils.addEvent(newNode,'mousemove',listenersObj.mousemove);Zapatec.Utils.addEvent(newNode,'mouseup',listenersObj.mouseup);}
movable.parentNode.insertBefore(newNode,movable);newNode.style.visibility="visible";copies.push(newNode);if(movable==self.getMovableMeasurement()){measurement=newNode;}}else if(Zapatec.isMovableObj(movable)){var elems=Zapatec.Array(movable.getMovableElements());elems.each(arguments.calee);}});toggleEvents("add",hooks,listenersObj);if(!measurement){measurement=this.getMovableMeasurement();}
restorer.saveProp("getMovableElements");restorer.saveProp("isMovableSafely()");this._setMovableSafely(false);this.getMovableElements=function(resetArray){var arr=copies;copies=resetArray?null:copies;return arr;};restorer.saveProp("getMovableMeasurement");this.getMovableMeasurement=function(){return measurement;};}else if(dragState=="dragEnd"){var elements=Zapatec.Array(this.getMovableElements(true));elements.each(function(index,movable){if(config.method=="dummy"){movable.parentNode.removeChild(movable);}
movable.dragObj=null;if(config.eventCapture){Zapatec.Utils.removeEvent(movable,'mousemove',listenersObj.mousemove);Zapatec.Utils.removeEvent(movable,'mouseup',listenersObj.mouseup);}});this.restoreOfMove();restorer.restoreProp("getMovableElements");this._setMovableSafely(restorer.getProp("isMovableSafely()"));restorer.restoreProp("isMovableSafely()");restorer.restoreProp("getMovableMeasurement");}
break;}
default:{break;}}};Zapatec.Draggable._proceedDragStyles=function(movable,dragState){var config=this.getDragConfig();if(config.overwriteCSS){if(dragState=="dragStart"){movable.restorer.saveProp("className");movable.className=config.overwriteCSS;}else if(dragState=="dragEnd"){movable.restorer.restoreProp("className");}}
if(config.dragCSS){if(dragState=="dragStart"){Zapatec.Utils.addClass(movable,config.dragCSS);}else if(dragState=="dragEnd"){Zapatec.Utils.removeClass(movable,config.dragCSS);}}};Zapatec.Draggable._setMovingPoint=function(x,y){var movingPoint=this._getMovingPointObject();if(x===null||y===null){movingPoint.x=null;movingPoint.y=null;movingPoint.offsetX=null;movingPoint.offsetY=null;return;}
if(movingPoint.x===null||movingPoint.y===null){var pos=this.getPagePosition();movingPoint.x=x;movingPoint.y=y;movingPoint.offsetX=x-pos.x;movingPoint.offsetY=y-pos.y;}else{var pos=this.getPagePosition();movingPoint.x=pos.x+movingPoint.offsetX;movingPoint.y=pos.y+movingPoint.offsetY;}
return;};Zapatec.Draggable.getMovingPoint=function(){var movingPoint=this._getMovingPointObject();return{x:movingPoint.x,y:movingPoint.y};};Zapatec.Draggable._getMovingPointObject=function(){if(!this.movingPoint||typeof this.movingPoint!="object"){this.movingPoint={x:null,y:null,offsetX:null,offsetY:null};}
return this.movingPoint;};Zapatec.Utils.Draggable=function(config){if(arguments.length>1){var args=arguments[1];args.container=config;config=args;}
if(typeof config.left!="undefined"||typeof config.right!="undefined"||typeof config.top!="undefined"||typeof config.bottom!="undefined"){config.limit={minX:config.left,maxX:config.right,minY:config.top,maxY:config.bottom};}
if(config.dragLayer){config.moveLayer=config.dragLayer;}
if(!config.eventListeners){config.eventListeners={};}
if(config.beforeDragInit){config.eventListeners.beforeDragInit=config.beforeDragInit;}
if(config.beforeDragMove){config.eventListeners.beforeDragMove=config.beforeDragMove;}
if(config.beforeDragEnd){config.eventListeners.beforeDragEnd=config.beforeDragEnd;}
if(config.onDragInit){config.eventListeners.onDragInit=config.onDragInit;}
if(config.onDragMove){config.eventListeners.onDragMove=config.onDragMove;}
if(config.onDragEnd){config.eventListeners.onDragEnd=config.onDragEnd;}
if(config.stopEv){config.stopEvent=config.stopEv;}
config=Zapatec.Hash.remove(config,"left","top","right","bottom","dragLayer","beforeDragInit","beforeDragMove","beforeDragEnd","onDragInit","onDragMove","onDragEnd","stopEv");Zapatec.Utils.Draggable.SUPERconstructor.call(this,config);};Zapatec.Utils.Draggable.id="Zapatec.Utils.Draggable";Zapatec.inherit(Zapatec.Utils.Draggable,Zapatec.Utils.Movable);Zapatec.implement(Zapatec.Utils.Draggable,"Zapatec.Draggable");Zapatec.Utils.Draggable.prototype.init=function(config){Zapatec.Utils.Draggable.SUPERclass.init.call(this,config);this.makeDraggable();};Zapatec.Utils.Draggable.prototype.configure=function(config){this.defineConfigOption("method","cut");this.defineConfigOption("stopEvent",true);this.defineConfigOption("eventCapture",false);this.defineConfigOption("handler",null);this.defineConfigOption("dragCSS",null);this.defineConfigOption("overwriteCSS",null);this.defineConfigOption("copyChilds",true);this.defineConfigOption("makeMovable",false);Zapatec.Utils.Draggable.SUPERclass.configure.call(this,config);config=this.getConfiguration();if(Zapatec.is_opera){config.eventCapture=true;}
config.handler=Zapatec.Widget.getElementById(config.handler);config.handler=Zapatec.Utils.img2div(config.handler);if(!Zapatec.isHtmlElement(config.handler)){config.handler=config.container;}};Zapatec.Utils.Draggable.prototype.reconfigure=function(config){Zapatec.Utils.Draggable.SUPERclass.reconfigure.call(this,config);};Zapatec.Utils.Draggable.prototype.getDraggableElements=function(){return this.movableElements;};Zapatec.Utils.Draggable.prototype._getDraggableHooks=function(){return this.getConfiguration().handler;};Zapatec.Utils.initDragObjects=function(className,el,recursive,config){if(!className)return;var elements=Zapatec.Utils.getElementsByAttribute('className',className,el,recursive,true);return Zapatec.Utils.applyToElements(Zapatec.Utils.Draggable,elements,config);}
Zapatec.Slider=function(config){this.widgetType="slider";this.priorities={count:7,destroyed:0,created:1,inited:2,loaded:3,ready:5,hidden:6,shown:7};this.widgetState="created";this.tmp={};this.setDefaultState();if(!config.eventListeners){config.eventListeners={};}
if(config.onChange){config.eventListeners.onChange=config.onChange;}
if(config.newPosition){config.eventListeners.newPosition=config.newPosition;}
if(config.div){config.parent=config.div;}
config=Zapatec.Hash.remove(config,"div","onChange","newPosition");Zapatec.Slider.SUPERconstructor.call(this,config);this.restorer=new Zapatec.SRProp(this);this.create();};Zapatec.Slider.id="Zapatec.Slider";Zapatec.inherit(Zapatec.Slider,Zapatec.Widget);Zapatec.Slider.prototype.init=function(config){Zapatec.Slider.SUPERclass.init.call(this,config);this.changeState("inited");this.loadData({object:this,action:"loadTemplate"});return true;};Zapatec.Slider.prototype.configure=function(config){this.defineConfigOption("orientation","H");this.defineConfigOption("length",100);this.defineConfigOption("dual",false);this.defineConfigOption("parent",document.body);this.defineConfigOption("range",null);this.defineConfigOption("step",1);this.defineConfigOption("start",null);this.defineConfigOption("secondStart",null);this.defineConfigOption("buttonConfig",{});this.defineConfigOption("template",Zapatec.sliderPath+this.config.orientation+".html");this.defineConfigOption("themePath",Zapatec.sliderPath+"../themes/");this.defineConfigOption("langId",Zapatec.Slider.id);this.defineConfigOption("callbackSource",function(args){var slider=args.object;if(!slider||slider.widgetType!="slider"){return null;}
switch(args.action){case"loadTemplate":{return{source:slider.getConfiguration().template+"?"+Math.random(),sourceType:"html/url"};}}
return null;});Zapatec.Slider.SUPERclass.configure.call(this,config);config=this.getConfiguration();if(!config.range){config.range=[0,config.length];}
if(!config.start&&config.start!==0){config.start=config.range[0];}
if(!config.secondStart&&config.secondStart!==0){config.secondStart=config.range[1];}
if(config.orientation!="H"){config.template=Zapatec.sliderPath+config.orientation+".html";}
if(!config.buttonConfig||typeof config.buttonConfig!="object"){config.buttonConfig={};}};Zapatec.Slider.prototype.reconfigure=function(config){Zapatec.Slider.SUPERclass.reconfigure.call(this,config);};Zapatec.Slider.prototype.loadDataHtml=function(el){if(this.parseDom(el)){this.changeState("loaded");}};Zapatec.Slider.prototype.create=function(){var self=this;if(!this.fireOnState("body_loaded",function(){this.create();})||!this.fireOnState("loaded",function(){this.create();})){return;}
this.config.parent=Zapatec.Widget.getElementById(this.config.parent);if(!Zapatec.isHtmlElement(this.config.parent)){alert(this.getMessage('createWrongParent'));return false}
Zapatec.Utils.addClass(this.container,this.getClassName({prefix:"zpSlider",suffix:"Container"}));function replaceButton(but,idPrefix,func){var config=Zapatec.Utils.clone(self.getConfiguration().buttonConfig);config.className=but.className+" "+but.className+"Inactive";config.style=but.style.cssText;config.theme=null;config.idPrefix=idPrefix;config.downAction=func;var button=new Zapatec.Button(config);var nxtSbl=but.nextSibling;var par=but.parentNode;par.removeChild(but);par.insertBefore(button.getContainer(),nxtSbl);return button;}
this.btnLess=replaceButton(this.btnLess,"zpSlider"+self.id+"LessButton",function(){self.stepDown();});this.btnMore=replaceButton(this.btnMore,"zpSlider"+self.id+"MoreButton",function(){self.stepUp();});this.btnFirst=replaceButton(this.btnFirst,"zpSlider"+self.id+"FirstButton",function(){self.gotoFirst();});this.btnLast=replaceButton(this.btnLast,"zpSlider"+self.id+"LastButton",function(){self.gotoLast();});this.setElementState(this.firstSlider,"inactive");this.setElementState(this.secondSlider,"inactive");Zapatec.Utils.addEvent(this.scaleBg,"mousedown",function(ev){if(!self.config.dual){ev||(ev=window.event);var shift=null;var x=ev.layerX||ev.offsetX;var y=ev.layerY||ev.offsetY;if(self.config.orientation=="V"){shift=y-self.firstSlider.offsetHeight/2;}else{shift=x-self.firstSlider.offsetWidth/2;}
if(shift<0)shift=0;if(shift>self.config.length)shift=self.config.length;shift=shift*((self.config.range[1]-self.config.range[0])/self.config.length);self.setPos(shift);var pos=self.getPos();self.fireEvent("newPosition",pos.first);}});Zapatec.Utils.addEvent(this.firstSlider,"mouseover",function(ev){self.setElementState(self.firstSlider,"active");self.setElementState(self.secondSlider,"inactive");});Zapatec.Utils.addEvent(this.firstSlider,"mouseout",function(ev){if(self.firstSlider.dragObj.dragging||self.secondSlider.dragObj.dragging){return;}
self.setElementState(self.firstSlider,"inactive");});Zapatec.Utils.addEvent(this.firstSlider,"mousemove",function(ev){if(self.config.dual){var pos1=Zapatec.Utils.getPos(self.firstSlider,self.scaleBg);var pos2=Zapatec.Utils.getPos(self.secondSlider,self.scaleBg);if(self.config.orientation=="V"){var diff=pos2.y-pos1.y;var size=self.firstSlider.offsetHeight;var mouse=ev.layerY||ev.offsetY}else{var diff=pos2.x-pos1.x;var size=self.firstSlider.offsetWidth;var mouse=ev.layerX||ev.offsetX}
if(diff<size){var whole=diff+size;if(mouse>whole/2){self.setElementState(self.firstSlider,"inactive");self.setElementState(self.secondSlider,"active");}}}});Zapatec.Utils.addEvent(this.secondSlider,"mouseover",function(ev){self.setElementState(self.secondSlider,"active");self.setElementState(self.firstSlider,"inactive");});Zapatec.Utils.addEvent(this.secondSlider,"mouseout",function(ev){if(self.firstSlider.dragObj.dragging||self.secondSlider.dragObj.dragging){return;}
self.setElementState(self.secondSlider,"inactive");});Zapatec.Utils.addEvent(this.secondSlider,"mousemove",function(ev){if(self.config.dual){var pos1=Zapatec.Utils.getPos(self.firstSlider,self.scaleBg);var pos2=Zapatec.Utils.getPos(self.secondSlider,self.scaleBg);if(self.config.orientation=="V"){var diff=pos2.y-pos1.y;var size=self.secondSlider.offsetHeight;var mouse=ev.layerY||ev.offsetY}else{var diff=pos2.x-pos1.x;var size=self.secondSlider.offsetWidth;var mouse=ev.layerX||ev.offsetX}
if(diff<size){var whole=size-diff;if(mouse<whole/2){self.setElementState(self.secondSlider,"inactive");self.setElementState(self.firstSlider,"active");}}}});this.config.parent.appendChild(this.container);this.reconfig(true);this.changeState("ready");return true;};Zapatec.Slider.prototype.setDefaultState=function(){};Zapatec.Slider.prototype.setScaleLength=function(length){var rescale=false,pos=null;if(!this.fireOnState("ready",function(){this.setScaleLength(length);})){return;}
rescale=Boolean(typeof this.tmp.rangePiece=="undefined");if(this.config.orientation=="V"){Zapatec.Utils.setHeight(this.scaleBg,length+Zapatec.Utils.getHeight(this.firstSlider));}else{Zapatec.Utils.setWidth(this.scaleBg,length+Zapatec.Utils.getWidth(this.firstSlider));}
this.config.length=length;this._calcRangePiece();if(rescale){pos=this.getPos();this.fireEvent("onChange",pos.first,pos.second);}};Zapatec.Slider.prototype.setRange=function(iRangeLeft,iRangeRight){var pos=null;if(!this.fireOnState("ready",function(){this.setRange(iRangeLeft,iRangeRight);})){return;}
if(this.config.range[0]!=iRangeLeft||this.config.range[1]!=iRangeRight){pos=this.getPos();this.config.range=[iRangeLeft,iRangeRight];this._calcRangePiece();if(pos.first<iRangeLeft){pos.first=iRangeLeft;}
if(pos.second>iRangeRight){pos.second=iRangeRight;}
this.setPos(pos.first,pos.second);}};Zapatec.Slider.prototype.setPos=function(first,second){var realSecond=realFirst=0,x1=x2=y1=y2=0;if(!this.fireOnState("ready",function(){this.setPos(first,second);})){return;}
if(!this.config.dual){second=null;}
if((second||second===0)&&first>second){return false;}
if(first>this.config.range[1]||first<this.config.range[0]){return false;}
if(second&&(second>this.config.range[1]||(second<this.config.range[0]))){return false;}
this.pos1=first;this.pos2=second;realFirst=Math.round((first-this.config.range[0])*(this.config.length/(this.config.range[1]-this.config.range[0])));if(second||second===0){realSecond=Math.round((second-this.config.range[0])*(this.config.length/(this.config.range[1]-this.config.range[0])));if(realSecond===0){return false;}
if(realSecond>this.config.length){if(realSecond-this.config.length<1){realSecond=this.config.length;}else{return false;}}
if(realFirst>realSecond){realFirst=realSecond-1;}
if(realFirst==realSecond){return false;}}
if(realFirst<0||realFirst>this.config.length){return false;}
if(this.config.orientation=="V"){y1=realFirst;y2=realSecond;}else{x1=realFirst;x2=realSecond;}
if(!Zapatec.Utils.moveTo(this.firstSlider,x1,y1)){return false;}
if(second||second===0){if(!Zapatec.Utils.moveTo(this.secondSlider,x2,y2)){return false;}}
this.restrict();this.fireEvent("onChange",first,second);return true;};Zapatec.Slider.prototype.getSliderPos=function(){var pos1=Zapatec.Utils.getPos(this.firstSlider);var pos2=Zapatec.Utils.getPos(this.secondSlider);var res={};if(this.config.orientation=="V"){pos1=pos1.y;pos2=pos2.y;}else{pos1=pos1.x;pos2=pos2.x;}
res.first=pos1*this.tmp.rangePiece;if(this.config.dual){res.second=pos2*this.tmp.rangePiece;}else{res.second=null;}
return res;};Zapatec.Slider.prototype.stepUp=function(){if(this.config.dual){return false;}
if(!this.fireOnState("ready",function(){this.stepUp();})){return;}
var res=this.getPos();this.setPos(res.first+this.config.step);var pos=this.getPos();if(pos.first!=res.first){this.fireEvent("newPosition",pos.first);}};Zapatec.Slider.prototype.stepDown=function(){if(this.config.dual){return false;}
if(!this.fireOnState("ready",function(){this.stepDown();})){return;}
var res=this.getPos();this.setPos(res.first-this.config.step);var pos=this.getPos();if(pos.first!=res.first){this.fireEvent("newPosition",pos.first);}};Zapatec.Slider.prototype.gotoFirst=function(){if(this.config.dual){return false;}
if(!this.fireOnState("ready",function(){this.gotoFirst();})){return;}
var res=this.getPos();this.setPos(this.config.range[0]);var pos=this.getPos();if(pos.first!=res.first){this.fireEvent("newPosition",pos.first);}};Zapatec.Slider.prototype.gotoLast=function(){if(this.config.dual){return false;}
if(!this.fireOnState("ready",function(){this.gotoLast();})){return;}
var res=this.getPos();this.setPos(this.config.range[1]);var pos=this.getPos();if(pos.first!=res.first){this.fireEvent("newPosition",pos.first);}};Zapatec.Slider.prototype.getPos=function(){if(typeof this.pos1=='number'){return{first:this.pos1,second:this.pos2};}
var res=this.getSliderPos();res.first=Math.round(res.first/this.config.step)*this.config.step;if(res.second){res.second=Math.round(res.second/this.config.step)*this.config.step;}
res.first+=this.config.range[0];res.second+=this.config.range[0];return res;};Zapatec.Slider.prototype.restrict=function(){if(this.config.dual){if(this.firstSlider.dragObj&&this.secondSlider.dragObj){if(this.config.orientation=="V"){this.firstSlider.dragObj.reconfigure({limit:{minY:0,maxY:Zapatec.Utils.getPos(this.secondSlider).y-1}});this.secondSlider.dragObj.reconfigure({limit:{minY:Zapatec.Utils.getPos(this.firstSlider).y+1,maxY:this.config.length}});}else{this.firstSlider.dragObj.reconfigure({limit:{minX:0,maxX:Zapatec.Utils.getPos(this.secondSlider).x-1}});this.secondSlider.dragObj.reconfigure({limit:{minX:Zapatec.Utils.getPos(this.firstSlider).x+1,maxX:this.config.length}});}}else{return false;}}else{if(this.firstSlider.dragObj){if(this.config.orientation=="V"){this.firstSlider.dragObj.reconfigure({limit:{minY:0,maxY:this.config.length}});}else{this.firstSlider.dragObj.reconfigure({limit:{minX:0,maxX:this.config.length}});}}else{return false;}}
return true;};Zapatec.Slider.prototype.reset=function(min,max){this.setRange(min,max);this.setPos(min,max);};Zapatec.Slider.prototype.parseDom=function(el){var classes="";if(!el||typeof el!="object"||!el.nodeType){return false;}
if(el.className){classes=el.className.match(/area(\w+)/);el.win=this;if(classes){el.id="zpSlider"+this.id+classes[1];classes[1]=classes[1].charAt(0).toLowerCase()+classes[1].substring(1);this[classes[1]]=el;Zapatec.Utils.removeClass(el,classes[0]);}}
var child=el.firstChild;while(child){this.parseDom(child);child=child.nextSibling;}
return true;};Zapatec.Slider.prototype.setElementState=function(el,state){if(!Zapatec.isHtmlElement(el)||el.state==state){return false;}
if(el.state){Zapatec.Utils.removeClass(el,el.state);}
state=el.className+(state.charAt(0).toUpperCase()+state.slice(1));Zapatec.Utils.addClass(el,state);el.state=state;return true;};Zapatec.Slider.prototype.showButton=function(button){this.restorer.restoreProp(button+".getContainer().parentNode.style.display");};Zapatec.Slider.prototype.hideButton=function(button){this.restorer.saveProp(button+".getContainer().parentNode.style.display");this[button].getContainer().parentNode.style.display="none";};Zapatec.Slider.prototype._calcRangePiece=function(){var config=this.getConfiguration();this.tmp.rangePiece=(config.range[1]-config.range[0])/config.length;};Zapatec.Slider.prototype.reconfig=function(now){var self=this;if(!now&&!this.fireOnState("ready",function(){self.reconfig();})){return;}
var config=this.getConfiguration();if(config.dual){this.hideButton("btnLess");this.hideButton("btnMore");this.hideButton("btnFirst");this.hideButton("btnLast");this.restorer.restoreProp("secondSlider.style.display");}else{this.showButton("btnLess");this.showButton("btnMore");this.showButton("btnFirst");this.showButton("btnLast");this.restorer.saveProp("secondSlider.style.display");this.secondSlider.style.display="none";}
if(!this.firstSlider.dragObj){this.firstSlider.dragObj=new Zapatec.Utils.Draggable(this.firstSlider,{moveLayer:this.scaleBg,eventListeners:{onDragInit:function(){self.setElementState(self.firstSlider,"active");},onDragMove:function(){self.pos1=null;var pos=self.getPos();self.fireEvent("onChange",pos.first,pos.second);},onDragEnd:function(ev){self.restrict();var x=ev.clientX+Zapatec.Utils.getPageScrollX()||0;var y=ev.clientY+Zapatec.Utils.getPageScrollY()||0;var sliderPos=Zapatec.Utils.getAbsolutePos(self.firstSlider);if(x<sliderPos.x||y<sliderPos.y||x>sliderPos.x+self.firstSlider.offsetWidth||y>sliderPos.y+self.firstSlider.offsetHeight){self.setElementState(self.firstSlider,"inactive");}
self.pos1=null;var pos=self.getPos();self.fireEvent("newPosition",pos.first,pos.second);}},direction:(config.orientation=="V"?"vertical":"horizontal")});}
if(!this.secondSlider.dragObj){this.secondSlider.dragObj=new Zapatec.Utils.Draggable(this.secondSlider,{moveLayer:this.scaleBg,eventListeners:{onDragInit:function(){self.setElementState(self.secondSlider,"active");},onDragMove:function(){self.pos1=null;var pos=self.getPos();self.fireEvent("onChange",pos.first,pos.second);},onDragEnd:function(ev){self.restrict();var x=ev.clientX+Zapatec.Utils.getPageScrollX()||0;var y=ev.clientY+Zapatec.Utils.getPageScrollY()||0;var sliderPos=Zapatec.Utils.getAbsolutePos(self.secondSlider);if(x<sliderPos.x||y<sliderPos.y||x>sliderPos.x+self.secondSlider.offsetWidth||y>sliderPos.y+self.secondSlider.offsetHeight){self.setElementState(self.secondSlider,"inactive");}
self.pos1=null;var pos=self.getPos();self.fireEvent("newPosition",pos.first,pos.second);}},direction:(config.orientation=="V"?"vertical":"horizontal")});}
Zapatec.Utils.makeSafelyMovable(this.firstSlider,this.scaleBg,null);Zapatec.Utils.makeSafelyMovable(this.secondSlider,this.scaleBg,null);this.setScaleLength(config.length);this.setPos(config.start,config.secondStart);};Zapatec.Slider.prototype.fireOnState=function(state,func,first){var self=this;if(state=="body_loaded"){if(!this.stateReached("created")){return false;}
if(!Zapatec.windowLoaded){setTimeout(function(){func.call(self);},50);return false;}
return true;}
if(!this.stateReached(state)){if(!this.stateReached("created")){return false;}
this.addEventListener(state,func,first);return false;}else{return true;}};Zapatec.Slider.prototype.changeState=function(state){this.widgetState=state;this.fireEvent(state);return true;};Zapatec.Slider.prototype.stateReached=function(state){var currentState=this.priorities[this.widgetState]||(this.priorities[this.widgetState]!==0?this.priorities.count:0);state=this.priorities[state]||(this.priorities[state]!==0?this.priorities.count:0);if(state>currentState){return false;}
return true;};