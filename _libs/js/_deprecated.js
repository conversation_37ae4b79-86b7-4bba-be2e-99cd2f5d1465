/**
 * Here will be collected all the unused but left behind functionality so some day it can be hopefully removed
 */


/**
 * Update participants for event assignment
 *
 * @deprecated - used for old assignments of events - with filter pop-up
 * @param the_form - the current form elements
 * @param close - flag that defines whether to close the popup or not
 */
/*function updateEventAssign(element, module, close) {
    // var group_table_id = $('group_table').value;
    var group_table_id = 'table_assign';
    // var origin_table_type = $('table_type').value;
    // var autocomplete_fields = $('autocomplete_fields').value.split(',');

    var opt = {
        method: 'post',
        parameters: Form.serialize(element),
        onSuccess: function(t) {
            // alert(t.responseText);
            var participants = eval('(' + t.responseText + ')');
            processEventAssign(group_table_id, participants, fields, module, close);
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var fields =  new Array();
    var fields_count = fields.length;
    var fields_to_post = [];

    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=' + module + '&' + module + '=filter_' + module + '&fields='+fields_to_post.join(',');

    new Ajax.Request(url, opt);

    return true;
}*/

/**
 * Update participants for event assignment
 *
 * @deprecated - used for old assignments of events - with filter pop-up
 * @param element - the current form element
 * @param update_div - the div element where the new options will be put
 * @param close - flag that defines whether to close the popup or not
 */
/*function updateEventDefaultAssign(element, update_div_name, module, close) {
    var opt = {
        method: 'post',
        parameters: Form.serialize(element),
        onSuccess: function(t) {
            var participants = eval('(' + t.responseText + ')');
            // processEventAssign(group_table_id, participants, fields, module,
            // close);
            if (participants) {
                var update_div = window.opener.$(update_div_name);
                // defines the row
                var row = update_div_name.replace(/.*_([0-9]*)$/, '$1');
                var type_assignment = update_div_name.replace(/assigned_(.*)_[0-9]{0,}/, '$1');

                // check if there is other checkboxes in the selected div
                var existing_checkboxes = update_div.getElementsByTagName('INPUT');

                if (participants.length>0 && existing_checkboxes.length==0) {
                    update_div.innerHTML += '<span class="pointer" onclick="selectDeselectAllOptions(this, ' + "'" + 'event' + "', " + '1)" style="color: #666666;">' + i18n['labels']['all'] + '</span> | <span class="pointer" onclick="selectDeselectAllOptions(this, ' + "'" + 'event' + "', " + '0)" style="color: #666666;">' + i18n['labels']['none'] + '</span><br/>';
                }

                for(var i=0; i<participants.length; i++) {
                    // defines the checkbox field
                    var params = {type: 'checkbox',
                                  name: 'assign_' + type_assignment + '[' + row + '][]',
                                  custom_id: 'assign_' + type_assignment + '_' + participants[i].id + '_' + row,
                                  value: participants[i].id,
                                  context: window.opener,
                                  checked: true
                    };
                    // input the connected hidden button
                    createField(params, update_div);
                    var params = {type: 'hidden',
                                  name: 'assign_' + type_assignment + '_type[' + row + '][]',
                                  custom_id: 'assign_' + type_assignment + '_type_' + participants[i].id + '_' + row,
                                  value: participants[i].assignee_type,
                                  context: window.opener
                    };
                    createField(params, update_div);

                    // input a label
                    update_div.innerHTML += '<label for="assign_' + type_assignment + '_' + participants[i].id + '_' + row + '">' + participants[i].name + '</label>';

                    // input a break tag
                    update_div.innerHTML += '<br />';
                }
            }

            Effect.Fade('loading');
            if (close) {
                window.close();
            }
            window.opener.focus();
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=' + module + '&' + module + '=add_default_assigned_users';

    new Ajax.Request(url, opt);

    return true;
}*/

/**
 * Processed
 *
 * @deprecated - used for old assignments of events - with filter pop-up
 * @param table_id - id of the containing group variable
 * @param participants - javascript array of the participants found
 * @param participant_fields - participant fields
 * @param module - module
 * @param close - close window or not
 */
/*function processEventAssign(table_id, participants, participant_fields, module, close) {

    var participant_type = module.substring(0,module.length - 1);
    // get the group table element
    var tbl = window.opener.$(table_id);
    var fields = new Array();
    // get the fields count
    var fields_count = fields.length - 1;
    var iterator = 0;

    first_row = tbl.rows.length;
    for (var i in participants) {
        var participant = participants[i];
        iterator++;

        // define the last row number
        var last_row = tbl.rows.length - 1;
        var new_row = last_row + 1;
        // the table has two header rows
        var new_row_num = new_row - 2;

        table = table_id;

        // insert a new row in the table
        var row = tbl.insertRow(new_row);
        // change the class name
        addClass(row, ((new_row % 2) ? 't_odd' : 't_even'));
        addClass(row, 't_inactive');
        // row.id = table + '_' + new_row;

        // get the first column, which is system
        var cell_0 = row.insertCell(0);

        // the arguments for the special first column are prepared customly
        cell_0.align = 'right';
        cell_0.noWrap = 'nowrap';
        cell_0.innerHTML = '<img src="' + env.themeUrl + 'images/small/delete.png" height="12" width="12" onclick="confirmAction(\'delete_row\', function() { hideField(\'' + table + '\', \'' + new_row_num + '\', 3); }, this);" class="pointer" style="float: left;" /> ' + new_row_num + '</a>';
        addClass(cell_0, 't_border');

        // get the cells' count
        var cells_count = tbl.rows[last_row].cells.length;

        // change the fields' attributes in each cell
        for (var c = 1; c < cells_count; c++) {
            var cell = row.insertCell(c);
            if (c == 1) {
                icon_name = model;
                if (icon_name == 'user' && parseInt(participant['is_portal'])) {
                    icon_name += '_portal';
                }
                cell.innerHTML = '<img src="' + env.themeUrl + 'images/small/' + icon_name + '.png" height="12" width="12" border="0" alt="" title="'+i18n['labels'][icon_name]+'" /> ' + '\n' +
                participant[model + '_name'] + '\n' +
                '<input type="hidden" id="' + model + '_assign_' + new_row + '" name="' + model + '_assign[' + participant['participant_id'] + ']" value="' + participant['participant_id'] + '" />';
                addClass(cell, 't_border');
            } else if ( c == 2) {
                cell.innerHTML = '<span class="table_intervals" id="table_intervals_' + (last_row + 1 ) +'"></span>';
                addClass(cell, 't_border');
            } else {
                cell.innerHTML = '<img src="' + env.themeUrl + 'images/pending.png" height="12" width="12" border="0" alt="" class="floatl" title="' + i18n['labels']['invitation_not_sent'] + '" /> ' + '\n' +
                    '<div id="availability_' + (last_row + 1 ) +'" class="availability floatl">' + i18n['labels']['invitation_not_sent'] + '</div>';
            }

        }

    }

    var event_id = window.opener.$('id').value;
    var j=0;
    for (var i in participants) {
        var participant = participants[i];
        updateParticipantAvailability(window.opener.$('table_intervals_'+ (first_row + j)), participant_type, event_id, participant['participant_id']);
        j++;
    }

    if (close) {
        window.close();
    }

    window.opener.focus();

}*/

/**
 * update participant availability
 *
 * @param element - the current form elements
 * @param participant_type - user or customer
 * @param event_id - event id
 * @param participant_id - participant id
 */
/*function updateParticipantAvailability(element, participant_type, event_id, participant_id) {

    var opt = {
        asynchronous:false,
        onSuccess: function(t) {
            eval('var data = ' + t.responseText);
            element.innerHTML = data.availability_table;
            element_num = element.id.replace(/^.*_([0-9]*)$/, "$1");
            avalability_caption = window.opener.$('availability_' + element_num);
            avalability_caption.innerHTML = i18n['labels']['availability_' + data.availability];
            avalability_caption.className += ' ' + data.availability;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=events&events=ajax_check_availability&ajax_check_availability=' + event_id+ '&participant_type=' + participant_type + '&participant_id=' + participant_id;

    new Ajax.Request(url, opt);

    return true;
}*/
