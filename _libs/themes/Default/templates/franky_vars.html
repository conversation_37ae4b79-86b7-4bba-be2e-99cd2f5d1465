{if !empty($var.values) && count($var.values)}
{capture assign="default_action_columns"}print_columns{/capture}
{capture assign="action_columns"}print_columns_pattern{$pattern_id}{/capture}
{if !isset($var.$action_columns) && !empty($var.$default_action_columns)}
  {assign var='action_columns' value=$default_action_columns}
{/if}
{capture assign="table_fields"}table_fields{/capture}

<div class="t_caption2_title">{$var.label}</div>
<table border="1" cellpadding="5" cellspacing="0"><tr>
<th width="20" style="text-align:right">{#num#|escape}</th>
{foreach name='i' key='key' from=$var.names item='name'}
  {if !$var.hidden[$key] && (is_array($var.$action_columns) && in_array($name, $var.$action_columns) || !isset($var.$action_columns)) && (is_array($var.$table_fields) && in_array($name, $var.$table_fields) || !isset($var.$table_fields))}
    {capture assign='info'}{if $var.help[$key]}{$var.help[$key]}{else}{$var.labels[$key]}{/if}{/capture}
    <th{if $var.width[$key]} width="{$var.width[$key]}"{/if}>{help label_content=$var.labels[$key] text_content=$info}</th>
  {/if}
{/foreach}
</tr>
{foreach name='i' from=$var.values item='val' key='row_id'}
<tr id="num_row_{$row_id}">
<td style="text-align:right">{$smarty.foreach.i.iteration}</td>
{foreach key='key' from=$var.names item='name'}
  {if !$var.hidden[$key] && (is_array($var.$action_columns) && in_array($name, $var.$action_columns) || !isset($var.$action_columns)) && (is_array($var.$table_fields) && in_array($name, $var.$table_fields) || !isset($var.$table_fields))}
  <td>
    {if $var.types[$key] eq 'text' or $var.types[$key] eq 'textarea' or $var.types[$key] eq 'autocompleter'}
      {$val[$key]|escape|default:'&nbsp;'|nl2br}
    {elseif $var.types[$key] eq 'date'}
      {$val[$key]|date_format:#date_short#|default:'&nbsp;'|nl2br}
    {elseif $var.types[$key] eq 'datetime'}
      {$val[$key]|date_format:#date_mid#|default:'&nbsp;'|nl2br}
    {elseif $var.types[$key] eq 'time'}
      {$val[$key]|date_format:#time_short#|default:'&nbsp;'|nl2br}
    {elseif $var.types[$key] eq 'dropdown' or $var.types[$key] eq 'radio'}
      {if $var[$name].options}
        {foreach from=$var[$name].options item='option'}
          {if $option.option_value eq $val[$key]}{$option.label|escape|default:'&nbsp;'}{/if}
        {/foreach}
      {elseif $var[$name].optgroups}
        {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
          {foreach from=$optgroup item='option'}
            {if $option.option_value eq $val[$key]}{$option.label|escape|default:'&nbsp;'}{/if}
          {/foreach}
        {/foreach}
      {/if}
    {elseif $var.types[$key] eq 'checkbox_group'}
      {if $var[$name].options}
        {foreach from=$var[$name].options item='option'}
          {if @in_array($option.option_value,$val[$key])}{$option.label|escape|default:'&nbsp;'}<br />{/if}
        {/foreach}
      {elseif $var[$name].optgroups}
        {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
          {foreach from=$optgroup item='option'}
            {if @in_array($option.option_value,$val[$key])}{$option.label|escape|default:'&nbsp;'}<br />{/if}
          {/foreach}
        {/foreach}
      {/if}
    {elseif $var.types[$key] eq 'file_upload'}
      {$var[$name].value}
    {/if}
  </td>
  {/if}
{/foreach}
</tr>
{/foreach}
</table>
{/if}