{if $pagination.page eq 1}
    <table border="0" cellpadding="0" cellspacing="0" class="t_layout_table t_borderless">
      <tr>
        <td class="t_row hcenter" colspan="2"><span class="stronger">{$title|default:#history_activity#}</span></td>
      </tr>
{/if}
    {assign var='activity_date' value=''}
    {foreach name='i' from=$history item='event'}
      {if $activity_date ne $event.h_date|date_format:#date_iso_short#}
      {assign var='activity_date' value=$event.h_date|date_format:#date_iso_short#}
      <tr class="t_row activity_date activity_date_{$activity_date}">
        <td class="strong hcenter" colspan="2">
          {$activity_date|date_format:#date_short#|escape}
        </td>
      </tr>
      {/if}
      <tr id="history_{$event.h_id}" class="t_row pointer">
        <td class="activity_icon {$event.action_type}" title="{$event.action_type_name}"></td>
        <td class="t_bottom_border activity_data">
          <span class="strong legend clear">{$event.h_date|date_format:#time_full#|escape}</span><br />
          {$event.data|nl2br|url2href|default:"&nbsp;"}
          {if $event.content}
          <div class="content_container"{if $event.audit} data-audit="{$event.audit|escape}"{/if}>
           <div class="content">
             {if $event.subject}<span class="strong">{$event.subject|escape}</span><br />{/if}
             {$event.content}
           </div>
          </div>
          {/if}
        </td>
      </tr>
    {foreachelse}
      <tr class="t_row">
        <td class="error" colspan="2">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    {if $pagination.rpp * $pagination.page lt $pagination.total}
      <tr class="t_row show_more">
        <td>&nbsp;</td>
        <td>
          <div class="loading hidden" title="{#loading#|escape}"></div>
          <a class="" href="#" onclick="return showMore(event, {ldelim}url: '{$submitLink}&amp;source=ajax&amp;history_activity=1&amp;page={math equation='a+1' a=$pagination.page}'{rdelim});">{#show_more#|escape}</a>
        </td>
      </tr>
    {/if}
{if $pagination.page eq 1}
    </table>
{/if}