{if !$hide_side_panel}
<input type="hidden" id="{$side_panel}_total" name="{$side_panel}_total" class="total" value="{$total}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 160px"><div class="t_panel_caption_title">{#from#|escape}/{#to#|escape}/{#date#|escape}</div></td>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title">{#communications_communication_text#|escape}</div></td>
  </tr>
  {foreach name='i' from=$communications item='communication'}
    <tr class="{cycle values='t_odd,t_even'} vtop"  onclick="expandCommunication($(this).select('td.content')[0]);">
      <td class="t_border{if $communication.expand} pointer communicationsdatefield{/if}">
        {if ($communication.attachments)}
          <span class="floatl" style="padding: 3px 3px 3px 0;">
            <img border="0" src="{$theme->imagesUrl}attachments.png" width="16" height="16" alt="" style="vertical-align: middle;"
            onmouseover="showFiles(this, '{if $current_module}{$current_module}{else}{$module}{/if}', '{if $current_controller}{$current_controller}{else}{$controller}{/if}', {$model_id}, '{$communication.attachments}'{if $archive}, 1{/if})"
            onmouseout="mclosetime()" />
          </span>
        {/if}
        <span class="strong">{#from#|escape}</span>: {$communication.from|escape|default:"&nbsp;"}
        {if $communication.to}
        <br />
        <span class="strong">{#to#|escape}</span>:
        {foreach name='c' from=$communication.to item='to_user'}
          {if $to_user.mail_status eq 'sent' && $to_user.receive_status eq 'received'}
            {assign var='name_color' value='000000'}
            {assign var='name_message' value='email_sent_received_comment_reason'}
          {elseif $to_user.mail_status eq 'received'}
            {assign var='name_color' value='006600'}
            {assign var='name_message' value='email_received_comment_reason'}
          {else}
            {assign var='name_color' value='FF0000'}
            {if $to_user.receive_status eq 'not_received'}
              {assign var='name_message' value='email_sent_not_received_comment_reason'}
            {elseif $to_user.mail_status ne 'sent'}
              {capture assign='name_message'}email_not_sent_{$to_user.mail_status}_reason{/capture}
            {/if}
          {/if}
          {strip}
          <span style="color: #{$name_color}; cursor: pointer;" {popup caption=#mail_status#|escape text=$smarty.config.$name_message|escape width=250}>
            {if $communication.type eq 'comment'}
              {if $to_user.name}{$to_user.name|escape|default:"&nbsp;"}{else}&#60;{$to_user.email|escape|default:"&nbsp;"}&#62;{/if}
            {else}
              {if $to_user.name}{$to_user.name|escape|default:"&nbsp;"} {/if}{if $to_user.email}&#60;{$to_user.email|escape}&#62;{/if}
            {/if}
          </span>{if !$smarty.foreach.c.last}, {/if}
          {/strip}
        {foreachelse}
          &nbsp;
        {/foreach}
        {/if}
        <br />
        <span class="strong">{#date#|escape}</span>: {$communication.date|date_format:#date_mid#|escape}
      </td>
          <td class="{$communications_sort.content.isSorted} content" id="communication_{$communication.id}">
        {if ($communication.subject)}<strong>{$communication.subject|escape}</strong><br />{/if}
        <div id="communication_full_{$smarty.foreach.i.iteration}" class="{$communication.type}_parent communication_parent">
          <div class="{$communication.type} communication">{$communication.content}</div>
        </div>
        </td>
    </tr>
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="2">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
</table>
{/if}