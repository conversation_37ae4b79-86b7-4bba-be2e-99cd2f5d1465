{literal}
<style type="text/css">
.white_border_table {
    width: 100%;
    border-width: 1px;
    border-style: solid;
    border-color: #FFFFFF!important;
    border-collapse: collapse;
}
.white_border_table th, .white_border_table td {
    border-width: 1px;
    border-style: solid;
    border-color: #FFFFFF!important;
    background: #DDDDDD;
    padding: 2px;
    margin: 0;
    border-collapse: collapse;
    vertical-align: top;
    text-align: left;
}
</style>
{/literal}
{assign var=add_bb_vars value=$document->get('add_bb_vars')}
{foreach name='jj' from=$document->get('bb_vars') item='bb_var'}
  {assign var=bb_id value=$bb_var.id}
  <table class="white_border_table">
    <tr>
      {foreach name='abbh' from=$add_bb_vars item='add_bb_var' key='add_bb_var_name'}
        {if $add_bb_var_name ne 'bb_group' && $add_bb_var_name ne 'bb_elements' && !$add_bb_var.hidden}
        <td{if $add_bb_var.width} width="{$add_bb_var.width}"{/if}>{$add_bb_var.label|escape}</td>
        {/if}
      {/foreach}
    </tr>
    <tr>
    {foreach name='abb' from=$add_bb_vars item='add_bb_var' key='add_bb_var_name'}
      {if $add_bb_var_name ne 'bb_group' && $add_bb_var_name ne 'bb_elements' && !$add_bb_var.hidden}
      <td style="{if $add_bb_var.text_align}text-align: {$add_bb_var.text_align};{/if}">
        {if $add_bb_var.type eq 'file_upload'}{$add_bb_var.value.$bb_id|default:"&nbsp;"}{elseif $add_bb_var.extended_value.$bb_id || $add_bb_var.extended_value.$bb_id === '0'}{$add_bb_var.extended_value.$bb_id}{else}{$add_bb_var.value.$bb_id|escape|nl2br|default:'&nbsp;'}{/if}{if ($add_bb_var.extended_value.$bb_id || $add_bb_var.extended_value.$bb_id === '0' || $add_bb_var.value.$bb_id || $add_bb_var.value.$bb_id === '0') && $add_bb_var.back_label} {$add_bb_var.back_label}{/if}
      </td>
      {/if}
    {/foreach}
    </tr>
  </table>
  {if $bb_var.type eq 'gt2'}
    {assign var=view_type_name value="_gt2_vars.html"}
    {assign var=table value=$bb_var}
  {elseif $bb_var.type eq 'grouping'}
    {assign var=view_type_name value="grouping_vars.html"}
  {else}
    {assign var=view_type_name value="`$bb_var.type`_vars.html"}
  {/if}
  {include file=$view_type_name
    var=$bb_var
    standalone=false
    var_id=$bb_var.id
    name=$bb_var.name
    custom_id=$varbb_.custom_id
    label=$bb_var.label
    help=$bb_var.help
    back_label=$bb_var.back_label
    value=$bb_var.value
    options=$bb_var.options
    optgroups=$bb_var.optgroups
    option_value=$bb_var.option_value
    first_option_label=$bb_var.first_option_label
    onclick=$bb_var.onclick
    on_change=$bb_var.on_change
    check=$bb_var.check
    scrollable=$bb_var.scrollable
    calculate=$bb_var.calculate
    readonly=$bb_var.readonly
    source=$bb_var.source
    hidden=$bb_var.hidden
    hide_script=1
    hide_label=1
    required=$bb_var.required
    disabled=$bb_var.disabled
    text_align=$bb_var.text_align
    custom_class=$bb_var.custom_class
  }
  <br />
  <br />
{/foreach}
