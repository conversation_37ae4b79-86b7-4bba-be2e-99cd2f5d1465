<table cellpadding="0" cellspacing="0" border="0" class="attachments t_grouping_table t_table hleft" style="border: 1px solid #999999; z-index: 10000; width: 300px;">
  <tr>
    <th class="nowrap">
      <a title="{#expand_all#|escape}" href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$smarty.request.real_module}{if $smarty.request.real_controller ne $smarty.request.real_module}&amp;controller={$smarty.request.real_controller}{/if}&amp;{$smarty.request.real_controller}=communications&amp;communications={$smarty.request.model_id}&amp;communication_type=comments{if $smarty.request.archive}&amp;archive=1{/if}"><span class="t_panel_caption_title">{#communications_comments_last_records_info#}</span></a>
    </th>
  </tr>
{foreach from=$comments item='comment'}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="t_border t_bottom_border" style="color: #000000;">
      {$comment->get('added_by_name')|escape} ({$comment->get('added')|date_format:#date_mid#})<br />
      {if $comment->get('subject')}<span class="strong">{$comment->get('subject')}</span><br />{/if}
      {$comment->get('content')|regex_replace:'#<br( \/)?>#':"\n"|replace:'&nbsp;':' '|strip_tags|regex_replace:'#\n(\s*\n\s*)+#':"\n"|mb_truncate:130|nl2br|url2href}
    </td>
  </tr>
{foreachelse}
  <tr>
    <td class="t_border t_bottom_border" style="color: #FF0000;">
      {if $no_permissions}{#communications_comments_no_permissions#}{else}{#error_no_items_found#}{/if}
    </td>
  </tr>
{/foreach}
</table>
