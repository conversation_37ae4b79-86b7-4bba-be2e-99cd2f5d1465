        <tr{if $var.hidden} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$var.label text_content=$var.help}</td>
          <td class="required">{if $var.required}{#required#}{else}&nbsp;{/if}</td>
          <td{if $var.text_align && $var.text_align neq 'right'} style="text-align: {$var.text_align};"{/if}>
          {strip}
          {if $var.text_align eq 'right'}
            {capture assign='var_width'}{if $var.width}{$var.width}{elseif preg_match('#\b(num|small|short)\b#', $var.custom_class)}80{elseif preg_match('#\bdoubled\b#', $var.custom_class)}400{else}200{/if}{/capture}
            <div style="float: left; text-align: {$var.text_align}; width: {$var_width}{if preg_match('#^[\d\.]+$#', $var_width)}px{/if};">
          {/if}
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', $var.name)}{$var.name|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1'}{/if}{/capture}
          {if $var.grouping}
            {foreach from=$var.value item='val'}
              {if $asterisk_contact}
                {include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact number=$val.value}
              {else}
                {$val.value|url2href}, 
              {/if}
            {/foreach}
          {else}
            {if $asterisk_contact}
              {include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact number=$var.value}
            {else}
              {$var.value|url2href}
            {/if}
          {/if}
          {if $var.text_align eq 'right'}</div>{/if}
          {* Back label *}
          {if ($var.value || $var.value === '0') && $var.back_label}&nbsp;{$var.back_label}{/if}
          {/strip}
          </td>
        </tr>
