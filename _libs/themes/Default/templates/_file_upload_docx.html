    </w:t>
</w:r>
<w:r>
    <w:drawing>
        <wp:inline>
            <wp:extent cx="{math equation=x*914400/92 x=$image.dimensions.0 format=%d}" cy="{math equation=y*914400/92 y=$image.dimensions.1 format=%d}"/>
            <wp:effectExtent r="0" b="0" t="0" l="0"/>
            <wp:docPr id="{$image.index}" name="{$image.filename}" descr="{$image.filename}"/>
            <wp:cNvGraphicFramePr>
                <a:graphicFrameLocks xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" noChangeAspect="1"/>
            </wp:cNvGraphicFramePr>
            <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                    <pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                        <pic:nvPicPr>
                            <pic:cNvPr id="{$image.index}" name="{$image.filename}"/>
                            <pic:cNvPicPr/>
                        </pic:nvPicPr>
                        <pic:blipFill>
                            <a:blip cstate="print" r:embed="rId{$image.rId}"/>
                            <a:stretch>
                                <a:fillRect/>
                            </a:stretch>
                        </pic:blipFill>
                        <pic:spPr>
                            <a:xfrm>
                                <a:off y="0" x="0"/>
                                <a:ext cx="{math equation=x*914400/92 x=$image.dimensions.0 format=%d}" cy="{math equation=y*914400/92 y=$image.dimensions.1 format=%d}"/>
                            </a:xfrm>
                            <a:prstGeom prst="rect">
                                <a:avLst/>
                            </a:prstGeom>
                        </pic:spPr>
                    </pic:pic>
                </a:graphicData>
            </a:graphic>
        </wp:inline>
    </w:drawing>
</w:r>
<w:r>
   <w:t>
