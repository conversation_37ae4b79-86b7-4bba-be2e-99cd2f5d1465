<!-- BB Vars Updater -->
<table cellspacing="0" class="bb_table_edit" cellpadding="0">
  <tr>
    <td class="bb_main_cell">
      <div class="error" style="display: none; background: #FFFFFF; border: 0;" id="bb_invalid_data"></div>
      <table>
      {if $var.type}
        {if $var.type eq 'gt2'}
          {assign var=input_type_name value="_gt2_edit.html"}
          {assign var=table value=$var}
            <tr>
              <td colspan="3" class="t_table">
        {else}
          {assign var=input_type_name value="input_`$var.type`.html"}
        {/if}
        {include file=$input_type_name
          var=$var
          standalone=false
          var_id=$var.id
          name=$var.name
          custom_id=$var.custom_id
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          value=$var.value
          options=$var.options
          optgroups=$var.optgroups
          option_value=$var.option_value
          first_option_label=$var.first_option_label
          onclick=$var.onclick
          on_change=$var.on_change
          check=$var.check
          scrollable=$var.scrollable
          calculate=$var.calculate
          readonly=$var.readonly
          source=$var.source
          hidden=$var.hidden
          hide_label=1
          required=$var.required
          disabled=$var.disabled
          show_placeholder=$var.show_placeholder
          text_align=$var.text_align
          custom_class=$var.custom_class
        }
        {if $bb_var.type eq 'gt2'}
                      </td>
                    </tr>
        {/if}
        <tr style="display: none;">
          <td colspan="3">
          {strip}
            {if $var.id > 0}
              {assign var=bid value=$var.id+1}
                <button type="button" class="button" name="editRow_{$var.bb}" id="editRow_{$var.bb}" onclick="{if $calc_meta_id}$('before_calc').value=$('bb_value_{$bid}').value;calc(this, {$calc_meta_id});$('bb_value_{$bid}').value=$('{$var.name}_calc').value;{/if}{if $bb_with_file_upload}return uploadViaAjax(this.form, {ldelim}source:'bb', module:'{$module}', bb_id:{$var.id}, model_id:{$model_id}, div_id:'list_bb_{$var.bb}', bb_num:{$var.bb}, meta_id:{$var.meta_id}, num_row:{$var.id}{rdelim}){else}saveBB(this.form, '{$module}', {$var.id}, {$model_id}, 'list_bb_{$var.bb}', {$var.bb}, {$var.meta_id}, '');{/if}">{if $calc_meta_id}{#calculate#|escape} &amp; {/if}{#save#|escape}</button>
                <input type="hidden" name="bb_id_{$var.bb}" id="bb_id_{$var.bb}" value="{$var.id}" />
                <input type="hidden" name="before_calc" id="before_calc" />
                {if $bb_with_file_upload}
                    <input type="hidden" name="bb_after_action" id="bb_after_action" />
                    <input type="hidden" name="bb_after_action_target" id="bb_after_action_target" />
                {/if}
            {/if}
            {if $calc_meta_id}
              <input type="hidden" name="{$var.name}_calc" id="{$var.name}_calc" />
            {/if}
          {/strip}
          </td>
        </tr>
      {/if}
      </table>
    </td>
  </tr>
</table>
