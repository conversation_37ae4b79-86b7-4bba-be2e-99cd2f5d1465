{** ALLOWED PARAMETERS:
 * standalone           - defines whether only the HTML element should be inserted or in a row of table
 * name                 - the form name of the variable (in latin characters), for the group tables the name is array [$index] is added
 * var_id               - id of the variable (as in the _fields_meta DB table)
 * custom_id            - each variable contains custom_id which defines the variable uniquely in the DOM
 * index                - index is the number of row in the group tables (starting with 1)
 * label                - label (translated in the language of the interface)
 * help                 - help text shown in the help baloon with overlib (translated in the language of the interface)
 * value                - the actual value of the variable
 * required             - flag that defines whether the variables is required (should be validated) or not
 * readonly             - flag that defines whether the variables should be readonly (not editable) or not
 * hidden               - if the variable is defined as hidden it is not displayed at all hiding it with style="display: none"
 * disabled             - if the variable is defined as disabled
 * width                - the width of the variable defines the width of the HTML element. In the standalone mode the width is defined as 100% of the cell width
 * calculate            - defines whether the HTML element should have calculate formula or not:
 *                        0 - no calculation formula
 *                        1 - calculation formula WITH button for calculation
 *                        2 - calculation formula WITHOUT button for calculation (if the width is 0 the input is not displayed at all)
 * options              - list of options (used only for checkboxes, dropdowns, radio buttons)
 * optgroups            - list of optgroups and their options (overwrites options)(used only for checkboxes, dropdowns, radio buttons)
 * option_value         - the value of the single option (used only for single checkbox)
 * first_option_label   - the label of the first option of a dropdown (used only for dropdowns)
 * origin               - defines the origin of the variable - group, config, table (typically it is not required)
 * format               - defines the format of the element (used only for the date and datetime fields)
 * disallow_date_before - does not allow input of dates before specified date (used only for the date and datetime fields)
 * disallow_date_after  - does not allow input of dates after specified date (used only for the date and datetime fields)
 * hide_calendar_icon   - does not allow showing of calendar icon (used only for the date and datetime fields)
 * onclick              - function defined for the onclick event(used only for buttons, checkboxes and radio buttons)
 * on_change            - function defined for the onchange event(used only for linked dropdowns and radio buttons)
 * back_label           - text for the back label
 * back_label_style     - styles (inline CSS) for the back label tag
 *}
{if $index}{strip}
  {capture assign='index_array'}
  {if $eq_indexes}
    {$index}
  {elseif $empty_indexes}
  {elseif $name_index}
      {$name_index}
  {else}
    {$index-1}
  {/if}
  {/capture}
{/strip}{/if}

{if !$standalone}
<tr{if $hidden} style="display: none"{/if}>
  {* Label Cell *}
  <td class="labelbox">
    {* Anchor for error reference *}
    <a name="error_{$custom_id|default:$name}"></a>
    {* Label of the variable *}
    <label for="{$custom_id|default:$name}{if $readonly}_readonly{/if}"{if $messages->getErrors($name)} class="error"{/if}>{help label_content=$label text_content=$help}</label>
  </td>

  {* Required Cell *}
  <td{if $required} class="required">{#required#}{else} class="unrequired">&nbsp;{/if}</td>

  {* Element Cell *}
  <td nowrap="nowrap">
{/if}

    {* Element *}
    {* This fake hidden radio is to allow radio in group tables to have at least one option for each row *}
    <input 
      type="radio"
      name="{$name}{if $readonly}_readonly{/if}{if $index}[{$index_array}]{/if}"
      id="{$custom_id|default:$name}_0{if $readonly}_readonly{/if}{if $index}_{$index}{/if}"
      value=""
      {if $custom_class}class="{$custom_class}"{/if}
      {if empty($value) && $value !== '0' && $value !== 0 && !$required} checked="checked"{/if}
      {if $readonly} disabled="disabled"{/if}
      style="display: none" />

    {* Options *}
    {foreach name='rb' from=$options item='option'}
      {if (!isset($option.active_option) || $option.active_option == 1 || $option.option_value === $value)}
        {if empty($value) && $value !== '0' && $value !== 0 && $required && (($option.option_value !== '0' && $option.option_value !== 0) || (int)$required eq 2)}
          {assign var='value' value=$option.option_value}
        {/if}
        <input 
          type="radio"
          name="{$name}{if $readonly}_readonly{/if}{if $index}[{$index_array}]{/if}"
          id="{$custom_id|default:$name}_{$smarty.foreach.rb.iteration}{if $readonly}_readonly{/if}{if $index}_{$index}{/if}"
          value="{$option.option_value|escape}"
          title="{$option.label|default:$label|strip_tags:false|escape}"
          class="{if $option.class_name}{$option.class_name}{/if}{if $custom_class} {$custom_class}{/if}"
          {if $on_change}onchange="{$on_change}"{/if}
          {if $option.option_value eq $value && !(empty($value) && $value !== '0' && $value !== 0)} checked="checked"{/if}
          {if $disabled || $readonly || $option.disabled} disabled="disabled"{/if} />
          <label for="{$custom_id|default:$name}_{$smarty.foreach.rb.iteration}{if $readonly}_readonly{/if}{if $index}_{$index}{/if}" class="{if $option.class_name}{$option.class_name}{/if}{if (isset($option.active_option) && $option.active_option == 0)} inactive_option" title="{#inactive_option#}{/if}">{if (isset($option.active_option) && $option.active_option == 0)}* {/if}{$option.label|escape|default:'&nbsp;'}</label>{if $options_align ne 'horizontal'}<br />{/if}
      {/if}
    {/foreach}
    {if $readonly}
      <input type="hidden" name="{$name}{if $index}[{$index_array}]{/if}" id="{$custom_id|default:$name}{if $index}_{$index}{/if}" value="{$value}" readonly="readonly" class="radio_hidden readonly{if $custom_class} {$custom_class}{/if}" />
    {/if}

    {* Back label *}
    {if !$back_label && $var.back_label}
      {assign var='back_label' value=$var.back_label}
    {/if}
    {if !$back_label_style && $var.back_label_style}
      {assign var='back_label_style' value=$var.back_label_style}
    {/if}
    {* do not add label tag for multi-field input controls *}
    {include file="_back_label.html"
      custom_id=''
      name=''
      index=''
      back_label=$back_label
      back_label_style=$back_label_style}

{if !$standalone}
  </td>
</tr>
{/if}
