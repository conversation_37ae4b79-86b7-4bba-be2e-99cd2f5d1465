#documents_sections_menu {
    margin: 0;
    padding: 0;
    z-index: 30
}

#documents_sections_menu li {
    margin: 0;
    padding: 0;
    list-style: none;
    float: left;
    cursor: pointer;
}

/*Top Level Menu Item*/
#documents_sections_menu li a {
    float: left;
    margin: 0;
    margin-right: 1px;
    padding: 0 0 0 4px;
    text-decoration: none;
    background: transparent url('../images/menu/tab_left.gif') no-repeat left bottom;
}
#documents_sections_menu li a span {
    float: left;
    display: block;
    padding: 5px 7px 5px 0;
    font-weight: normal;
    color: #666666;
    background: transparent url('../images/menu/tab_right.gif') no-repeat right bottom;
    cursor: pointer;
}
#documents_sections_menu li.has_sub a span {
    padding-right: 15px;
    background-image: url('../images/menu/tab_right_has_sub.gif');
}

/*Top Level Menu Item Hovered*/
#documents_sections_menu li a:hover {
    background-image: url('../images/menu/tab_left_on.gif');
}
#documents_sections_menu li a:hover span {
    background-image: url('../images/menu/tab_right_on.gif');
}
#documents_sections_menu li.has_sub a:hover span {
    background-image: url('../images/menu/tab_right_has_sub_on.gif');
}

/*Top Level ITEM With Icon*/
#documents_sections_menu li a img {
    float: left;
    padding: 2px 3px 0 0;
}

/*Sub Menu Item Container*/
#documents_sections_menu .sub_items_container {
    position: absolute;
    visibility: hidden;
    margin-top: 1px;
    padding: 0;
    line-height: 12px;
}

/*Sub Menu Item*/
#documents_sections_menu li .sub_items_container a {
    margin: 0!important;
    padding: 0!important;
    padding: 0 0 0 9px;
    margin-bottom: 1px;
    position: relative;
    display: block;
    float: none;
    width: auto;
    white-space: nowrap;
    text-align: left;
    text-decoration: none;
    background: transparent url('../images/menu/sub_menu_item_left.gif') no-repeat left top;
}

#documents_sections_menu li .sub_items_container a span {
    margin: 0!important;
    padding: 0!important;
    padding: 4px 3px 4px 0px;
    position: relative;
    display: block;
    float: none;
    width: auto;
    white-space: nowrap;
    text-align: left;
    text-decoration: none;
    background: transparent no-repeat right top;
    background-image: url('../images/menu/sub_menu_item_right.gif')!important;
}

/*Mozilla Fix*/
#documents_sections_menu li .sub_items_container > a {
    margin: 0!important;
    height: 21px;
    padding-left: 9px!important;
}
#documents_sections_menu li .sub_items_container > a span{
    display: block;
    margin: 0!important;
    height: 21px;
    padding-top: 3px!important;
    padding-right: 3px!important;
}

/*Sub Menu Item Hovered*/
#documents_sections_menu li .sub_items_container a:hover {
    background-image: url('../images/menu/sub_menu_item_left_on.gif')!important;
}
#documents_sections_menu li.has_sub:hover a.over, #documents_sections_menu li.over a:hover, #documents_sections_menu li.over a.over {
    background-image: url('../images/menu/tab_left_on.gif');
}
#documents_sections_menu li.has_sub:hover a.over span, #documents_sections_menu li.over a span {
    background-image: url('../images/menu/tab_right_has_sub_on.gif');
}
#documents_sections_menu li .sub_items_container a:hover span {
    background-image: url('../images/menu/sub_menu_item_right_on.gif')!important;
}
