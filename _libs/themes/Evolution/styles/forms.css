/*Form*/
form {
    margin: 0;
    padding: 0;
}
input[type=text], input[type=password] , textarea, select {
    font-family: var(--font-family);
    font-size: 0.875rem; /* 14px */
}

textarea {
    overflow: auto;
}

#browse input {
    margin-bottom: 2px;
    display: block;
}
.passbox, .filebox {
    width: 200px;
    height: 1.75rem;
    color: var(--text-altcolor);
    font-size: 0.75rem;
    background-color: var(--background-color);
    border: 1px solid var(--border-input-color);
    box-sizing: border-box;
    padding: 0.25rem 0.25em;
    border-radius: 4px;
}

.areabox,
.areabox:hover,
.areabox_hov {
    width: 200px;
    height: 5em;
    padding: 0.5em 0.5em;
    color: var(--text-altcolor);
    background-color: var(--background-color);
    border: 1px solid var(--border-input-color);
    box-sizing: border-box;
    white-space: pre-wrap;
    border-radius: 4px;
}
.areabox:hover,
.areabox_hov {
    color: var(--text-altcolor);
    background-color: var(--background-color);
    border: 1px solid var(--border-input-color);
    white-space: pre-wrap;
}
.areabox.distinctive {
    background-color: #FFFFEE!important;
}
.areabox:hover.distinctive,
.areabox_hov.distinctive {
    background-color: #FFFFCC!important;
}

.nz-form-input-wrapper {
    white-space: nowrap;
    display: inline-block;
    max-width: calc(100% - 1px);
}

.nz-form-input-wrapper a:not(.nz-input-controls):not(.nz-ac-combobox-trigger) {
    white-space: wrap;
}

.nz-input-controls {
    /*border: 1px solid var(--border-input-color);*/
    padding: 0.15rem 0;
    border-radius: 0.25rem;
    display: inline-block;
    height: 1.5rem;
    width: 1.5rem;
    box-sizing: border-box;
    vertical-align: middle;
    text-align: center;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.nz-input-controls img {
    vertical-align: middle;
}

 .nz-input-icon {
    font-size: 1.3rem!important;
    color: var(--primary-color);
    cursor: pointer;
    vertical-align: middle;
}
.nz-input-icon.nz--disabled {
    color: var(--muted-text-color);
}

.nz-input-controls .nz-md-backspace {
    margin-top: -1px;
    font-size: 1.25rem !important;
}

.nz-form-input {

}

.nz-form-input>td {
    padding-bottom: 0.25rem;
    vertical-align: middle;
}
.nz-form-input>td>span,
.nz-form-input>td>span>span{
    vertical-align: middle;
}

.selbox, .txtbox,
.selbox:hover, .txtbox:hover,
.selbox_hov, .txtbox_hov {
    width: 200px;
    max-width: 100%;
    padding: 0.5em 0.5em;
    color: var(--text-color);
    background-color: var(--background-color);
    border: 1px solid var(--border-input-color);
    box-sizing: border-box;
    border-radius: 4px;
}

select.searchablebox:not(.searchboxready) {
    display: none;
}

.nice-select.selbox:hover,
.nice-select.selbox_hov {
    width: initial !important;
}

.nice-select,
.nice-select .nice-select-search {
    font-size: initial !important;
    padding: 0.5rem 2rem 0.5rem 1rem !important;
    line-height: initial !important;
    float: initial !important;
}

.nice-select:active, .nice-select.open, .nice-select:focus {
    border-color: var(--primary-color) !important;
}

.txtbox.autocompletebox.combobox,
.txtbox_hov.autocompletebox.combobox {
    background-position: right 1.5em center;
}
.selbox option, .selbox optgroup, .selbox_hov option, .selbox_hov optgroup {
    /*font-family: Verdana, Arial, Helvetica, sans-serif;*/
}
.selbox.missing_records {
    color: #FF0000!important;
}
.selbox_hov.missing_records {
    color: #FF0000!important;
}
.txtbox.distinctive, .pricebox.distinctive {
    background-color: #FFFFEE!important;
}
.txtbox_hov.distinctive, .pricebox_hov.distinctive {
    background-color: #FFFFCC!important;
}
.txtbox.refreshed, .pricebox.refreshed, .areabox.refreshed, .selbox.refreshed {
    background-color: #C8FFBF!important;
}
.txtbox_hov.refreshed, .pricebox_hov.refreshed, .areabox_hov.refreshed, .selbox_hov.refreshed {
    background-color: #9FFF8F!important;
}
.txtbox.erred, .pricebox.erred, .areabox.erred, .selbox.erred {
    border-color: #FF7F7F!important;
    color: #FF0000;
}

.selbox.nz--failed {
    border-color: var(--important-color);
    color: var(--important-color);
    animation: pulse 1s 2;
}

.txtbox_hov.erred, .pricebox_hov.erred, .areabox_hov.erred, .selbox_hov.erred {
    border-color: #FF5F5F!important;
}
.txtbox.completed, .pricebox.completed, .areabox.completed {
    border-color: #087706!important;
    color: #087706;
}
.txtbox_hov.completed, .pricebox_hov.completed, .areabox_hov.completed {
    border-color: #035F00!important;
}
.txtbox.red, .txtbox_hov.red {
    color: #FF0000!important;
}
.txtbox.green, .txtbox_hov.green {
    color: #298923!important;
}
.txtbox.viewmode, .txtbox_hov.viewmode,
.txtbox.autocompletebox.readonly.viewmode, .txtbox_hov.autocompletebox.readonly.viewmode,
.areabox.viewmode, .areabox_hov.viewmode,
.selbox.viewmode, .selbox_hov.viewmode {
    border: 0px none;
    background-color: transparent;
    background-image: none;
}
.areabox.viewmode, .areabox_hov.viewmode {
    /* Remove WebKit user-resize widget */
    resize: none;
}
.selbox.num, .selbox_hov.num, .txtbox.num, .txtbox_hov.num {
    width: 40px;
}
.pricebox {
    color: #000000;
    /*font-family: Verdana, Arial, Helvetica, sans-serif;*/
    font-size: 0.875em;
    text-align: right;
    background-color: #EFEFEF;
    border: 1px solid #AAAAAA;
}
.pricebox_hov {
    color: #000000;
    /*font-family: Verdana, Arial, Helvetica, sans-serif;*/
    font-size: 0.875em;
    text-align: right;
    background-color: #EFEFEF;
    border: 1px solid #5371AF;
}
.pricebox_view {
    color: #000000;
    /*font-family: Verdana, Arial, Helvetica, sans-serif;*/
    font-size: 0.875em;
    text-align: right;
}
.codebox {
    width: 60px;
}
.namebox {
    margin-left: 6px;
    width: 134px;
}
td > .namebox {
    margin-left: 0px;
}
.labelbox {
    vertical-align: middle;
    color: #666666;
    width: 180px!important;
}
.labelbox_long {
    vertical-align: middle;
    color: #666666;
    width: 210px!important;
}
.back_label {
    display: inline-block;
    overflow: hidden;
}
.viewbox td {
    padding: 3px;
}
.viewbox .labelbox {
    width: 0;
}
.databox {
    color: #000000;
    font-weight: normal;
}
.databox2 {
    color: #000000;
    /*font-family: Verdana, Arial, Helvetica, sans-serif;*/
    font-size: 0.875rem;
    font-weight: bold;
}
.datebox, .timebox {
}
.datetimebox {
}

.nz-form--controls {
    padding: 0.5rem 0;
}

.button:not(.nav),
.nz-button,
.nz-form-button{
    margin: 0 0.75em 0 0;
    padding: 0 1.25em 0.175em;
    line-height: 2em;
    font-size: 1.0rem;
}

.button:not(td),
.nz-button,
.nz-form-button {
    border-radius: 1.5rem;
    background-color: var(--primary-color);
    color: var(--text-onprimary-color);
    border: 1px solid var(--primary-color);
    font-variant-caps: all-small-caps;
    font-weight: 600;
    cursor: pointer;
    display: inline-block;
    box-shadow: 0px 0px 0px 0px rgb(0 0 0 / 20%),
                0px 0px 0px 0px rgb(0 0 0 / 14%),
                0px 0px 0px 0px rgb(0 0 0 / 12%);
    transition: all 200ms ease-out;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

button:hover,
/*button:hover,*/
.button_hov,
.nz-button:hover {
    box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%),
                0px 2px 2px 0px rgb(0 0 0 / 14%),
                0px 1px 5px 0px rgb(0 0 0 / 12%);
    border-color: #00000080;
}
.button_inactive, .button_inactive:hover,
.button:not(.nav).inactive, .button:not(.nav).inactive:hover{
    background-color: var(--altbackground-color)!important;
    color: var(--text-onaltbackground-color)!important;
    cursor: wait;
}

.nz-button .material-icons,
.nz-form-button .material-icons {
    font-variant-caps: normal !important;
    font-size: 1em;
    margin-left: -0.475em;
    margin-right: 0.175rem;
    vertical-align: middle;
    line-height: 1.2em;
}
.nz-button--withIcon {
    /*padding-left: .5em;*/
}

a.nz-button {
    color: var(--text-onprimary-color) !important;
}

.nz-icon-button {
    position: relative;
    font-family: "Material Icons";
    border: none;
    background: transparent;
    color: var(--primary-color);
    border-radius: 50%;
    font-size: 1.5em;
    text-align: center;
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    vertical-align: middle;
    font-style: normal;
    cursor: pointer;
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    user-select: none;
    transition: all 200ms ease-out;
    z-index: 1;
}

.nz-button[disabled],
.nz-button.nz--disabled,
.nz-form-button[disabled],
.nz-form-button.nz--disabled,
.nz-icon-button[disabled],
.nz-icon-button.nz--disabled {
    color: var(--muted-text-color) !important;
    opacity: 0.2!important;
    cursor: default !important;
    pointer-events: none !important;
}

.nz-icon-button.nz--loading::before {
    content: '';
    display: block;
    width: 1.5rem;
    height: 1.5rem;
    margin: 0;
    position: absolute;
    right: -0.75rem;
    top: 50%;
    transform: translate(0%, -50%);
    border-radius: 50%;
    border: 2px solid transparent;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    z-index: 10;
}

.nz-icon-button.nz--loading {
    animation: pulse 500ms linear infinite;
}

.nz-form-button {
    box-sizing: border-box;
    background: var(--background-color);
    border: 1px solid var(--border-input-color);
    color: var(--text-altcolor);
    transition: all 200ms ease-out;
}

.nz-form-button:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.nz-button.nz--primary,
.nz-form-button.nz--primary,
.nz-button-primary,
.nz-button-save1 {
    border-color: var(--accent-color);
    background: var(--accent-color);
    color: var(--text-onaccent-color)
}

.nz-button.nz--primary:hover,
.nz-form-button.nz--primary:hover,
.nz-button-primary:hover,
.nz-button-save1:hover {
    border-color: var(--accent-color);
    background: var(--background-color);
    color: var(--accent-color)
}

.nz-button-inverted {
    background: var(--text-altcolor);
    color: var(--background-color)
}

.nz-button-cancel {

}

@keyframes pulseSize {
    from {
        zoom: 0.2;
    }
    to {
        zoom: 1;
    }
}

.nz-icon-button::after {
    content: '';
    background: var(--primary-color);
    width: 0;
    height: 0;
    border-radius: 100px;
    position: absolute;
    left: 53%;
    top: 50%;

    transform: translateX(calc(-50%)) translateY(calc(-50%));
    z-index: -1;
    opacity: 0;
    transition: opacity 200ms ease-out;
    pointer-events: none;
    /*zoom: 0.2;*/
}

.nz-icon-button:hover {
    color: var(--accent-color);
}
.nz-form-button[disabled]:hover,
.nz-form-button.nz--disabled:hover,
.nz-button[disabled]:hover,
.nz-button.nz--disabled:hover,
.nz-icon-button[disabled]:hover,
.nz-icon-button.nz--disabled:hover {
    color: initial;
}

.nz-icon-button:hover::after {
    width: 48px;
    height: 48px;
    /*animation: pulseSize 1s 1 forwards ease-out;*/
    opacity: 0.2
}

.nz-icon-button[disabled]:hover::after,
.nz-icon-button.nz--disabled:hover::after {
    opacity: 0
}

.nz-icon-button.nz--primary {
    color: var(--accent-color);
}

.nz-file-upload-indicator {
    position: relative;
}
.nz-file-upload-indicator .nz-file-upload__upload,
.nz-file-upload-indicator .nz-file-upload__change {
    position: absolute;
    left: 0;
    top: 0;
    transition: opacity 200ms ease-out;
}

.nz-file-upload-indicator.nz--has-content .nz-file-upload__upload {
    opacity: 0;
}

.nz-file-upload-indicator:not(.nz--has-content) .nz-file-upload__change {
    opacity: 0;
}

.reports_generate_button {
    background-color: #FFF3C8;
}
.reports_export_button {
    background-color: #93D0F9;
}
.copy_button {
    width: 20px!important;
}
.frontendbox {
    color: #FFA500;
    font-style: italic;
}
.doubled,
.doubled_hov,
.doubled:hover {
    width: 400px !important;
}

.short {
    width: 80px!important;
}
/*
.txtbox[readonly], .txtbox_hov[readonly],
.areabox[readonly], .areabox_hov[readonly] {
    background: url('../images/readonly_bg.gif') 0 0;
    border: 1px solid #FFDDBB;
}
*/
.txtbox.autocompletebox,
.txtbox_hov.autocompletebox {
    /*background: url('../images/small/autocompleter.png') no-repeat 100% 2px #F5F5F5;*/
    background: var(--background-color);
    color: var(--text-color);
    position: relative;

}
.txtbox.autocompletebox.working,
.txtbox_hov.autocompletebox.working {
    background-image: url('../images/small/indicator.gif');
    background-position: right 0.5em center;
    background-size: 1em;
    background-repeat: no-repeat;
}
.nz-ac-has-combobox .txtbox.autocompletebox.working,
.nz-ac-has-combobox .txtbox_hov.autocompletebox.working {
    background-position: right 1.5em center !important;
}

.readonly, .txtbox.autocompletebox.readonly {
    background: url('../images/readonly_bg.gif') 0 0;
    border: 1px solid #FFDDBB;
}
.txtbox.distinctive[readonly],
.txtbox_hov.distinctive[readonly],
.areabox.distinctive[readonly],
.areabox_hov.distinctive[readonly],
.readonly.distinctive {
    background-image: none;
}
.readonly.distinctive {
    background-image: none;
}
textarea.higher,
textarea.higher_hov,
textarea.higher:hover {
    height: 200px !important;
}
.txtbox.small, .txtbox_hov.small, .selbox.date, .selbox_hov.date, .selbox.small, .selbox_hov.small, .pricebox.small, .pricebox_hov.small {
    width: 64px!important;
}
.selbox.time, .selbox_hov.time {
    width: 40px!important;
}
.input_inactive {
    background: url('../images/footer_bg4.gif') 0 0;
    border: 1px solid #DDDDDD;
}

.search_inactive {
    background: url('../images/footer_bg4.gif') 0 0;
    border: 1px solid #DDDDDD;
}

/* Autocompleter */
div.autocompletebox {
    position: absolute;
    background-color: var(--background-color);
    /*border: 1px solid var(--border-input-color);*/
    margin: 0;
    padding: 0.25rem;
    z-index: 15000;
    overflow: hidden;
    width: fit-content;
    max-width: 30rem;
    min-width: 13rem;
    border-radius: 0.5rem;
    white-space: normal;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
                0px 4px 5px 0px rgba(0, 0, 0, 0.14),
                0px 1px 10px 0px rgba(0,0,0,.12);
}
div.autocomeplete_positioned {
    margin-left: -66px!important;
}
div.autocompletebox ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
    overflow: auto;
    max-width: 30rem;
    min-width: 13rem;
    max-height: 10rem;/*,calc(100vh - 30rem));*/
}
div.autocompletebox ul li.selected {
    background-color: var(--highlight-color);
    color: var(--accent-color);
    /*background-color: #FFFFE0;*/
}
div.autocompletebox ul li {
    list-style-type: none;
    display: block;
    margin: 0;
    padding: 0.5rem;
    cursor: pointer;
    color: var(--text-color);
}
/*div.autocompletebox ul li a {
    color: #5371af !important;
}
div.autocompletebox ul li a:visited {
    color: #46044F !important;
}
div.autocompletebox ul li a:active {
    color: #EF0000 !important;
}
div.autocompletebox ul li a:hover {
    color: #2D395F !important;
}*/

div.autocompletebox li.user_portal {
    background: url('../images/small/user_portal.png') no-repeat 1px 1px;
    padding-left: 15px ! important;
}

div.autocompletebox li.user_normal {
    background: url('../images/small/user.png') no-repeat 1px 1px;
    padding-left: 15px ! important;
}

div.autocompletebox li.inactive_option {
    color: #A6A6A6;
}

.block_quote_status {
    margin: 0 1rem 0.5rem;
}

.transformations_options_box {
    width: 100%;
}

.inactive_option {
    color: #A6A6A6;
    cursor: help;
}
.inactive_checkbox {
    background-color: #CCCCCC!important;
    color: #000000!important;
}

/* style placeholder to look the same in different browsers */
input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
    color: #888888;
    opacity: 0.8;
}
input:focus::-webkit-input-placeholder, textarea:focus::-webkit-input-placeholder {
    color: transparent; /* autohide */
}
input:-moz-placeholder, textarea:-moz-placeholder { /* Firefox 18- */
    color: #888888;
    opacity: 0.8;
}
input:focus:-moz-placeholder, textarea:focus:-moz-placeholder {
    color: transparent; /* autohide */
}
input::-moz-placeholder, textarea::-moz-placeholder { /* Firefox 19+ */
    color: #888888;
    opacity: 0.8;
}
input:focus::-moz-placeholder, textarea:focus::-moz-placeholder {
    color: transparent; /* autohide */
}
input:-ms-input-placeholder, textarea:-ms-input-placeholder {
    color: #888888;
    opacity: 0.8;
}


.nz-quick-buttons {
    pointer-events: none;
    max-width: var(--fields-max-width);
}
.nz-quick-buttons ~ div{
    max-width: var(--fields-max-width);
}
.nz-quick-buttons > * {
    pointer-events: auto;
}

.nz-quick-buttons.nz--floating {
    position: fixed;
    bottom: 0;
    left: calc(1.5rem + 2px);
    z-index: 2;
    padding: 1rem 1rem 0.5rem;
    /*background: var(--background-color);*/
    border-radius: var(--border-radius);
}
.nz--pinned~#nz-main-content .nz-quick-buttons.nz--floating {
    left: calc(3.75rem + 1.5rem + 2px);
}
.nz-quick-buttons.nz--floating:hover {
    /*opacity: 1;*/
}

.nz-quick-buttons button,
.nz-quick-buttons input,
.nz-quick-buttons .nz-button,
.nz-quick-buttons .nz-form-button {
    margin-right: 0.5rem!important;
    margin-bottom: 0.5rem!important;
    transition: opacity 200ms ease-out, box-shadow 200ms ease-out;
}

.nz-quick-buttons.nz--floating button,
.nz-quick-buttons.nz--floating input,
.nz-quick-buttons.nz--floating .nz-button,
.nz-quick-buttons.nz--floating .nz-form-button {
    box-shadow: 0px 6px 6px -3px rgba(0, 0, 0, 0.2),
                0px 10px 14px 1px rgba(0, 0, 0, 0.14),
                0px 4px 18px 3px rgba(0,0,0,.12);
    opacity: 0.8;
}

.nz-quick-buttons button:hover,
.nz-quick-buttons input:hover,
.nz-quick-buttons .nz-button:hover {
    opacity: 1;
}


.nz-assignments-configurator-body {
    max-height: 120px;
    overflow: auto;
    min-width: 250px;;
}

.nz-assignments-cell {
    padding: 0.5rem 0;
    vertical-align: top;
}

.nz-assignments-input-wrapper {
    width: calc(100% - 48px);
    white-space: nowrap;
    padding: 0.5rem 0;
}

