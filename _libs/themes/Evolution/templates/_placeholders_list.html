<h1><img src="{$theme->imagesUrl}list.png" border="0" alt="{#basic_vars#|escape}" /> {if $title}{$title|escape}{else}{#basic_vars#|escape}{/if}</h1>
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#placeholders_label#|escape}</div></td>
    <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#placeholders_varname#|escape}</div></td>
  </tr>
{counter name='k' start=0 print=false}
{foreach name='i' from=$basic_placeholders key='varname' item='var'}
{if !preg_match("#$prev_model#", $var->get('model')) || $prev_type != $var->get('type')}
{capture assign='divider_name'}placeholders_{$var->get('model')|mb_lower}{/capture}
  <tr>
    <td colspan="3" class="t_caption3 strong legend">{$smarty.config.$divider_name}</td>
  </tr>
{counter name='k' start=0 print=false}
{/if}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="t_border hright">{counter name='k' assign='cnt' print=true}</td>
    <td class="t_border">{$var->get('name')}</td>
    <td>
    {if $var->get('multilang') && $module == 'patterns'}
    {foreach from=$multi_langs item='lang'}
    [{$lang}_{$var->get('varname')}] 
    {/foreach}
    {else}
    [{$var->get('varname')|escape|default:'&nbsp;'}]
    {/if}
    </td>
  </tr>
{assign var='prev_model' value=$var->get('model')}
{assign var='prev_type' value=$var->get('type')}
{foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error" colspan="3">{#no_items_found#|escape}</td>
  </tr>
{/foreach}
  <tr>
    <td class="t_footer" colspan="3"></td>
  </tr>
</table>

