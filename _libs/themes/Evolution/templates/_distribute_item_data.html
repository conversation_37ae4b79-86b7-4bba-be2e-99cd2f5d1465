
{array assign='js_methods' eval='array(\'onkeyup\' => \'distribution_calc(this);\')'}
<script type="text/javascript">
  elements_dd_{$gk}_{$ik} = {json encode=$elements_dd.$gk.$ik|default:''};
  centers_dd_{$gk}_{$ik} = {json encode=$centers_dd.$gk.$ik|default:''};
  {if isset($start_date) && isset($end_date)}
  start_date_{$gk}_{$ik} = '{$start_date}';
  end_date_{$gk}_{$ik} = '{$end_date}';
  {/if}
</script>
<table cellpadding="0" cellspacing="0" border="0" class="t_distribution_data_table" id="elements_table_{$gk}_{$ik}">
  <tr class="data_title2">
    <td rowspan="2" width="15" class="hcenter" style="vertical-align: bottom; border-right: 0px;">
      {if $zero_share_elements.$gk.$ik}
      <div class="pointer" style="width: 16px" onclick="showZeroShareElements(this)" title="{#distribution_show_zero_share_elements#|escape}">
        <img src="{$theme->imagesUrl}show_zero_share.png" alt="" />
      </div>
      {/if}
    </td>
    <td rowspan="2" width="200" class="data_title" style="border-left: 0px;">
      {include file='input_hidden.html'
               name=elements_titles[`$gk`][`$ik`]
               custom_id=elements_titles_`$gk`_`$ik`
               value=$elements_titles.$gk.$ik
               standalone=true}
      {$elements_titles.$gk.$ik|escape}
    </td>
    <td rowspan="2" width="40" class="data_title">
      {#distribution_amount#|escape}
    </td>
    <td rowspan="2" width="40" class="data_title">
      {#distribution_percentage#|escape}
    </td>
    {foreach from=$main_centers item='main_center' name='mci' key='mck'}
    <td colspan="{$main_center.centers|@count}" class="data_title" width="{math equation='92*x' x=$main_center.centers|@count}">
      {$main_center.name|escape}
    </td>
    {/foreach}
  </tr>
  <tr class="data_title2">
    {foreach from=$main_centers item='main_center' name='mci' key='mck'}
    {foreach from=$main_center.centers item='center' name='ci' key='ck'}
    <td class="data_title" width="92">
      {$center.name|escape}
    </td>
    {/foreach}
    {/foreach}
  </tr>
  {foreach from=$element_ids.$gk.$ik item='element' name='ei' key='ek'}
  <tr id="element_{$gk}_{$ik}_{$ek}"{if $element_amounts.$gk.$ik.$ek eq 0} style="display: none"{/if}>
    <td class="hright">
      {$smarty.foreach.ei.iteration}
    </td>
    <td>
      {include file='input_hidden.html'
               name=element_ids[`$gk`][`$ik`][`$ek`]
               custom_id=element_ids_`$gk`_`$ik`_`$ek`
               value=$element
               standalone=true}
      {include file='input_hidden.html'
               name=element_names[`$gk`][`$ik`][`$ek`]
               custom_id=element_names_`$gk`_`$ik`_`$ek`
               value=$element_names.$gk.$ik.$ek
               standalone=true}
      {$element_names.$gk.$ik.$ek|escape}
    </td>
    <td>
      {include file='input_text.html'
               name=element_amounts[`$gk`][`$ik`][`$ek`]
               standalone=true
               required=1
               custom_id=element_amounts_`$gk`_`$ik`_`$ek`
               value=$element_amounts.$gk.$ik.$ek
               restrict='insertOnlyReals'
               text_align='right'
               width='60'
               custom_class='element_amounts'
               js_methods=$js_methods
               label=$smarty.config.distribution_distributed_amount_element}
      </td>
      <td>
        {include file='input_text.html'
            name=element_percentages[`$gk`][`$ik`][`$ek`]
            standalone=true
            required=1
            custom_id=element_percentages_`$gk`_`$ik`_`$ek`
            value=$element_percentages.$gk.$ik.$ek
            restrict='insertOnlyReals'
            text_align='right'
            width='60'
            custom_class='element_percentages'
            js_methods=$js_methods
            label=$smarty.config.distribution_distributed_amount_percentage}
    </td>
    {foreach from=$main_centers item='main_center' name='mci' key='mck'}
    {foreach from=$main_center.centers item='center' name='ci' key='ck'}
    <td>
      {assign var='c_id' value=$center.id}
      {include file='input_hidden.html'
               name=center_ids[`$gk`][`$ik`][`$ek`][`$mck`][`$c_id`]
               custom_id=center_ids_`$gk`_`$ik`_`$ek`_`$mck`_`$c_id`
               value=$c_id
               standalone=true}
      {include file='input_text.html'
               name=center_amounts[`$gk`][`$ik`][`$ek`][`$mck`][`$c_id`]
               standalone=true
               required=1
               custom_id=center_amounts_`$gk`_`$ik`_`$ek`_`$mck`_`$c_id`
               value=$center_amounts.$gk.$ik.$ek.$mck.$c_id
               restrict='insertOnlyReals'
               text_align='right'
               width='80'
               custom_class='center_amounts'
               js_methods=$js_methods
               label=$smarty.config.distribution_distributed_amount_center}
    </td>
    {/foreach}
    {/foreach}
  </tr>
  {/foreach}
</table>