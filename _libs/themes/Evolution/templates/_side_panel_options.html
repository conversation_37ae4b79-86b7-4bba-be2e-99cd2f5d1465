{capture assign=sidepanel<PERSON>ey}{$module}_{$model_type}{/capture}
{assign var=sidepanelCollapsed value=$currentUser->getPersonalSettings('switch', $sidepanelKey, true)}
<div class="nz-side-panel-tools nz-elevation--z1">
  <span id="side_panel_settings_show_div" class="nz-icon-button nz-popout-trigger" title="{#side_panels_personal_settings#|escape}">handyman</span>
  <span class="nz-toggle nz-side-panel-collapse-toggle nz-toggle-autoinit{if empty($sidepanelCollapsed)} nz--active{/if}"
        data-toggle-target="{if $dont_wrap_content|default:false}.nz-page-wrapper{else}.nz-content-wrapper{/if}"
        data-toggle-toggleClass="nz-side-panel-collapse"
        data-toggle-personalsettings-section="switch"
        data-toggle-personalsettings-name="{$sidepanelKey}"
        data-toggle-personalsettings-value="1"><span
          class="nz-toggle__inactive">Скриване <span class="material-icons">keyboard_double_arrow_right</span></span
            ><span class="nz-icon-button nz-toggle__active">keyboard_double_arrow_left</span>
  </span>
</div>
   {* onclick="toggleAvailableSidePanelOptions(1);" *}
<script id="nz-side-panels-template" type="text/x-template">
  <aside id="side_panel_options" class="nz-popout-panel nz-pointer-top-right nz-modal">
    <div class="nz-popout-surface nz-surface nz-elevation--z6">
        <div class="nz-popout-title">
          <h2>{#side_panels_personal_settings#|escape}</h2>
        </div>
        <form name="side_panels" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=users&amp;users=ajax_mynzoom&amp;ajax_save=1" method="post"  class="">
          <div id="nz-side_panel_all_options" class="nz-popout-body side_panel_all_options"></div>
          <div id="side_panel_options_buttons" class="nz-popout-footer side_panel_options_buttons">
            <div id="side_panel_restore_defaults" class="">
              <input type="checkbox" id="update_all_actions" name="update_all_actions" value="1" title="{#side_panels_update_all_actions#|escape}" /> <label for="update_all_actions">{#side_panels_update_all_actions#|escape}</label>
              <br />
              <input type="checkbox" id="restore_defaults" name="restore_defaults" value="1" title="{#side_panels_restore_defaults#|escape}" /> <label for="restore_defaults">{#side_panels_restore_defaults#|escape}</label>
            </div>
            <button type="submit" name="saveButton1" class="nz-button">{#save#|escape}</button>
            <button type="button" name="cancel" class="nz-button nz-popout--close">{#cancel#|escape}</button>
          </div>
        </form>
    </div>
  </aside>
</script>
                 {* <form name="side_panels" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=users&amp;users=ajax_mynzoom&amp;ajax_save=1" method="post">
                    <div id="side_panel_options" class="side_panel_options">
                      <div id="side_panel_settings_hide_div" class="side_panel_settings_hide_div button" onclick="toggleAvailableSidePanelOptions(0);" title="{#close#|escape}">
                        <img src="{$theme->imagesUrl}laquo_big.png" width="12" height="12" alt="&laquo;" />
                      </div>
                      <div id="side_panel_options_title" class="t_caption3 strong">
                        <div class="drag floatr">
                        <img src="{$theme->imagesUrl}small/move.png" alt="" width="10" height="10" title="{#draggable#}" />
                        </div>

                        <div class="drag">
                        <img src="{$theme->imagesUrl}small/info.png" alt="" style="padding-right: 1px; cursor: help;" {help label_content=#side_panels_personal_settings# text_content=#help_side_panels_personal_settings# popup_only=1} /> {#side_panels_personal_settings#|escape}
                        </div>

                      </div>
                      <div id="side_panel_all_options" class="side_panel_all_options"></div>
                      <div id="side_panel_options_buttons" class="side_panel_options_buttons t_caption3_reverted">
                        <div id="side_panel_restore_defaults" class="" style="padding-bottom: 3px;">
                          <input type="checkbox" id="update_all_actions" name="update_all_actions" value="1" title="{#side_panels_update_all_actions#|escape}" /> <label for="update_all_actions">{#side_panels_update_all_actions#|escape}</label>
                          <br />
                          <input type="checkbox" id="restore_defaults" name="restore_defaults" value="1" title="{#side_panels_restore_defaults#|escape}" /> <label for="restore_defaults">{#side_panels_restore_defaults#|escape}</label>
                        </div>
                        <button type="submit" name="saveButton1" class="button">{#save#|escape}</button><button type="button" name="cancel" class="button" onclick="toggleAvailableSidePanelOptions(0);">{#cancel#|escape}</button>
                      </div>
                    </div>
                  </form>
                  <script type="text/javascript">
                    new Draggable('side_panel_options', {ldelim}handle: 'side_panel_options_title'{rdelim});
                  </script> *}
