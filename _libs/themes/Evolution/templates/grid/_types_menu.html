{if !empty($menu)}
<div class="nz-types-menu nz-block nz-block__horizontal">
    <div class="nz-block-label">{#type#}</div>
    <div class="nz-block-content">
      {assign var='baseUrl' value="`$smarty.server.PHP_SELF`?`$module_param`=`$module`&amp;type_section="}
      {strip}
        <a href="{$baseUrl}&amp;type="
           class="nz-chip{if !isset($type_section) && (!isset($type) || $type eq '')} nz--active{/if} nz-types__all">{#all#|escape}</a>
        {foreach name='t' from=$menu item=menuType}
          <a href="{$baseUrl}&amp;type={$menuType->get('id')}"
             class="nz-chip{if !isset($type_section) && $menuType->get('id') eq $type} nz--active{/if} nz-types__{$menuType->get('id')}">{$menuType->get('name')}</a>
        {/foreach}
      {/strip}
    </div>
</div>
{/if}
