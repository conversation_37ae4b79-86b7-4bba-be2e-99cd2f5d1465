{** ALLOWED PARAMETERS:
 * standalone           - defines whether only the HTML element should be inserted or in a row of table
 * name                 - the form name of the variable (in latin characters), for the group tables the name is array [$index] is added
 * var_id               - id of the variable (as in the _fields_meta DB table)
 * custom_id            - each variable contains custom_id which defines the variable uniquely in the DOM
 * index                - index is the number of row in the group tables (starting with 1)
 * label                - label (translated in the language of the interface)
 * help                 - help text shown in the help baloon with overlib (translated in the language of the interface)
 * value                - the actual value of the variable
 * value_id             - the value of the corresponding field that holds the id of the selected record (necessary only for additional variables)
 * required             - flag that defines whether the variables is required (should be validated) or not
 * readonly             - flag that defines whether the variables should be readonly (not editable) or not
 * hidden               - if the variable is defined as hidden it is not displayed at all hiding it with style="display: none"
 * disabled             - if the variable is defined as disabled
 * width                - the width of the variable defines the width of the HTML element. In the standalone mode the width is defined as 100% of the cell width
 * calculate            - defines whether the HTML element should have calculate formula or not:
 *                        0 - no calculation formula
 *                        1 - calculation formula WITH button for calculation
 *                        2 - calculation formula WITHOUT button for calculation (if the width is 0 the input is not displayed at all)
 * options              - list of options (used only for checkboxes, dropdowns, radio buttons)
 * optgroups            - list of optgroups and their options (overwrites options)(used only for checkboxes, dropdowns, radio buttons)
 * option_value         - the value of the single option (used only for single checkbox)
 * first_option_label   - the label of the first option of a dropdown (used only for dropdowns)
 * origin               - defines the origin of the variable - group, config, table (typically it is not required)
 * format               - defines the format of the element (used only for the date and datetime fields)
 * disallow_date_before - does not allow input of dates before specified date (used only for the date and datetime fields)
 * disallow_date_after  - does not allow input of dates after specified date (used only for the date and datetime fields)
 * hide_calendar_icon   - does not allow showing of calendar icon (used only for the date and datetime fields)
 * onclick              - function defined for the onclick event(used only for buttons, checkboxes and radio buttons)
 * on_change            - function defined for the onchange event(used only for linked dropdowns)
 * js_methods           - defines a JavaScript functions for some keyboard and mouse events
 * restrict             - defines a JavaScript restriction of the input characters.
 *                        For example restrict insertOnlyDigits will allow only digits to be inserted in the text field
 * min_chars            - the minimum number of characters on which the autocompleter will be activated
 * show_placeholder     - if set to label or help, respective text is set as placeholder attribute of text field
 * back_label           - text for the back label
 * back_label_style     - styles (inline CSS) for the back label tag
 *}

{if $index|default:null}{strip}
  {capture assign='index_array'}
    {if $eq_indexes}
      {$index}
    {elseif $empty_indexes}
    {elseif $name_index}
      {$name_index}
    {else}
      {$index-1}
    {/if}
  {/capture}
{/strip}{/if}
{capture assign='height'}{strip}
  {if $height && !preg_match('#%$#', $height)}
    {$height}px
  {elseif $height}
    {$height}
  {/if}
{/strip}{/capture}

{if $autocomplete_var_type|default:null ne 'searchable' && !$readonly|default:null}
{if (preg_match('/combobox/', $autocomplete.buttons|default:null) || $autocomplete.combobox|default:null) && !preg_match('/combobox/', $autocomplete.buttons_hide|default:null) && !preg_match('/combobox/', $autocomplete_buttons_hide|default:null)}
    {assign var='include_combobox_button' value=1}
{/if}
{/if}

{if !$standalone|default:null}
<tr{if $hidden|default:null} style="display: none"{/if} class="nz-form-input">
  {* Label Cell *}
  <td class="labelbox">
    {* Anchor for error reference *}
    <a name="error_{$custom_id|default:$name}"></a>
    {* Label of the variable *}
    <label for="{$custom_id|default:$name}"{if $messages->getErrors($name)} class="error"{/if}>{help label_content=$label text_content=$help}</label>
  </td>

  {* Required Cell *}
  <td{if $required|default:null} class="required">{#required#}{else} class="unrequired">&nbsp;{/if}</td>

  {* Element Cell *}
  <td>
{/if}
<span class="nz-form-input-wrapper{if $include_combobox_button|default:null} nz-ac-has-combobox{/if}">
    {* Element *}
    {if !$value_autocomplete|default:null && ($value_code|default:null || $value_name|default:null)}
      {capture assign='value_autocomplete'}{if $value_code|default:null}[{$value_code}] {/if}{if $value_name|default:null}{$value_name}{/if}{/capture}
    {/if}

    {* Get the name of the var *}
    {assign var='var_name' value=$custom_id|default:$name}

    {* Get the additional settings for this autocompleter *}
    {if $autocomplete_var_type|default:null eq 'basic' && $basic_vars_additional_settings.$var_name.autocomplete}
      {foreach from=$basic_vars_additional_settings.$var_name key='setting_key' item='setting_value'}
        {if in_array($setting_key, array('autocomplete', 'readonly', 'hidden', 'width', 'show_placeholder', 'custom_class', 'text_align')) && $setting_value !== '' && $setting_value !== null}
          {assign var=$setting_key value=$setting_value}
        {/if}
      {/foreach}
    {/if}

    {if $autocomplete_var_type|default:null eq 'basic' || ($autocomplete_var_type|default:null eq 'searchable' && $autocomplete.search_by_id|default:null)}
      <input type="hidden"
             name="{$name}{if $index|default:null}[{$index_array}]{/if}"
             id="{$custom_id|default:$name}{if $index|default:null}_{$index}{/if}"
             value="{$value|default:''}" />

      {capture assign='visible_name'}{$name}_autocomplete{if $index|default:null}[{$index_array}]{/if}{/capture}
      {capture assign='visible_id'}{$custom_id|default:$name}_autocomplete{if $index|default:null}_{$index}{/if}{/capture}
      {capture assign='visible_value'}{if $value_autocomplete|default:null}{$value_autocomplete}{elseif $value|default:null}{$value}{/if}{/capture}
      {assign var='value_id' value=$value}

      {if !$autocomplete|default:null && $autocomplete_type|default:null}
        {if preg_match('#^([^_]*)_(.*)#', $autocomplete_type|default:'', $act_matches)}
          {capture assign='autocomplete_url'}{$smarty.server.PHP_SELF}?{$module_param}={$act_matches[1]}&{$controller_param}={$act_matches[2]}&{$act_matches[2]}=ajax_select{/capture}
        {else}
          {capture assign='autocomplete_url'}{$smarty.server.PHP_SELF}?{$module_param}={$autocomplete_type}&{$autocomplete_type}=ajax_select{/capture}
        {/if}
        {if !$view_mode|default:null}{assign var='view_mode' value='link'}{/if}
        {capture assign='view_mode_url'}{if $view_mode == 'link'}{$autocomplete_url|replace:'ajax_select':'view&view='}{/if}{/capture}
        {array assign='autocomplete'
               type=$autocomplete_type|default:null
               url=$autocomplete_url|default:null
               min_chars=$min_chars|default:null
               buttons=$autocomplete_buttons|default:null
               buttons_hide=$autocomplete_buttons_hide|default:null
               execute_after=$execute_after|default:null
               filters=$filters_array|default:null
               addquick_type=$addquick_type|default:null
               stop_customer_details=$stop_customer_details|default:null
               suggestions=$autocomplete_suggestions|default:null
               fill_options=$autocomplete_fill_options|default:null
               view_mode=$view_mode|default:null
               view_mode_url=$view_mode_url|default:null
        }
      {else}
        {if $autocomplete_var_type|default:null eq 'searchable'}
          {array assign='autocomplete'
                 fill_options=''
          }
        {elseif $filters_array|default:null}
          {array assign='autocomplete'
                 filters=$filters_array
          }
        {/if}
      {/if}
      {* If this is a basic var
         then prepare some additional autocomplete settings *}
      {if $autocomplete_var_type|default:null eq 'basic'}
        {* Set the type of the var *}
        {array assign='autocomplete'
          var_type=$autocomplete_var_type
        }
      {/if}
    {else}
      {capture assign='visible_name'}{$name}{if $index|default:null}[{$index_array}]{/if}{/capture}
      {capture assign='visible_id'}{$custom_id|default:$name}{if $index|default:null}_{$index}{/if}{/capture}
      {capture assign='visible_value'}{if $value_autocomplete|default:null}{$value_autocomplete}{elseif $value}{$value}{/if}{/capture}
    {/if}

    {assign var='buttons_count'          value=0}
    {assign var='include_clear_button'   value=0}
    {assign var='include_add_button'     value=0}
    {assign var='include_search_button'  value=0}
    {assign var='include_refresh_button' value=0}
    {assign var='include_report_button'  value=0}
    {assign var='include_edit_button'    value=0}
    {if $autocomplete_var_type|default:null ne 'searchable' && !$readonly}
      {if $available_action.action|default:null == 'filter' || $available_action.action|default:null == 'search' || (preg_match('/clear/', $autocomplete.buttons|default:null) || $autocomplete.clear) && !preg_match('/clear/', $autocomplete.buttons_hide) && !preg_match('/clear/', $autocomplete_buttons_hide)}
        {assign var='include_clear_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if ($available_action.action|default:null != 'filter' && $available_action.action|default:null != 'search') && (preg_match('/search/', $autocomplete.buttons|default:null) || !$autocomplete.buttons) && !preg_match('/search/', $autocomplete.buttons_hide) && !preg_match('/search/', $autocomplete_buttons_hide)}
        {assign var='include_search_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if (preg_match('/refresh/', $autocomplete.buttons|default:null) || $autocomplete.refresh|default:null) && !preg_match('/refresh/', $autocomplete.buttons_hide|default:null) && !preg_match('/refresh/', $autocomplete_buttons_hide|default:null)}
        {assign var='include_refresh_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if $autocomplete.report|default:null && !preg_match('/report/', $autocomplete.buttons_hide|default:null)}
        {assign var='include_report_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if ($available_action.action|default:null != 'filter' && $available_action.action|default:null != 'search') && ($autocomplete.type|default:null eq 'customers' && !(!empty($autocomplete.filters) && array_key_exists('<contactpersons>', $autocomplete.filters)) || $autocomplete.type eq 'projects' || $autocomplete.type eq 'nomenclatures' && !(!empty($autocomplete.filters) && array_key_exists('<customer_trademark>', $autocomplete.filters))) && (preg_match('/add/', $autocomplete.buttons) || $autocomplete.add) && !preg_match('/add/', $autocomplete.buttons_hide) && !preg_match('/add/', $autocomplete_buttons_hide)}
        {if $currentUser->checkRights($autocomplete.type, 'add')}
          {assign var='include_add_button' value=1}
        {/if}
        {if $include_add_button|default:null || $autocomplete_var_type|default:null eq 'basic'}
          {math equation="x + 1" x=$buttons_count assign='buttons_count'}
        {/if}
      {/if}
      {if ($available_action.action|default:null != 'filter' && $available_action.action|default:null != 'search') && ($autocomplete.type|default:null eq 'customers' && !(!empty($autocomplete.filters) && array_key_exists('<contactpersons>', $autocomplete.filters)) || $autocomplete.type eq 'nomenclatures' && !(!empty($autocomplete.filters) && array_key_exists('<customer_trademark>', $autocomplete.filters))) && (preg_match('/edit/', $autocomplete.buttons) || $autocomplete.edit) && !preg_match('/edit/', $autocomplete.buttons_hide) && !preg_match('/edit/', $autocomplete_buttons_hide)}
        {if $currentUser->checkRights($autocomplete.type, 'edit')}
          {assign var='include_edit_button' value=1}
        {/if}
        {if $include_edit_button|default:null || $autocomplete_var_type|default:null eq 'basic'}
          {math equation="x + 1" x=$buttons_count assign='buttons_count'}
        {/if}
      {/if}
    {/if}

    {if $autocomplete.button_menu|default:null && $buttons_count|default:null gt 0 && ($available_action.action|default:null != 'filter' && $available_action.action|default:null != 'search')}
      {assign var='button_menu' value=1}
      {assign var='buttons_count' value=1}
      {if $autocomplete_var_type|default:null eq 'basic' && $standalone|default:null && !($width && preg_match('#^(\d+%|)$#', $width)) && empty($basic_vars_additional_settings.$var_name.width)}
        {assign var='width' value=222}
      {/if}
    {else}
      {assign var='button_menu' value=0}
    {/if}

    {capture assign='width'}{strip}
      {if $standalone|default:null}
        {if preg_match('#^(\d+%|)$#', $width|default:'')}
          100%
        {else}
          {math equation="x - (y*z)" x=$width y=$buttons_count z=22}px
        {/if}
      {/if}
    {/strip}{/capture}

    {*
      Create unique ID for this autocompleter.
      The parameter more_entropy is set to true to increase the likelihood that the result will be unique,
      because there are cases (like using Windows OS for a web server) when two or more calls of uniqid()
      return same values, because they were executed at the same microsecond.
    *}
    {uniqid assign=uniqID more_entropy=true}
    {*
      The more_entropy param set to true makes the uniqid() function
      return an additional value, separated with a dot. Given that the uniqid is later used in JavaScript,
      the dot may result in some errors, so we remove it.
    *}
    {assign var='uniqID' value=$uniqID|replace:'.':''}

    <input
       type="text"
       class="txtbox autocompletebox{if !$readonly|default:null && !$hidden|default:null} autocomplete_{$autocomplete.type|default:null}{/if}{if $readonly|default:null} readonly{if $autocomplete.view_mode|default:null == 'link' && $autocomplete.view_mode_url|default:null} hidden{/if}{/if}{if $include_combobox_button|default:null} combobox{/if}{if $custom_class|default:null} {$custom_class}{/if}"
       name="{$visible_name}"
       id="{$visible_id}"
       value="{$visible_value|escape}"
       title="{$label|strip_tags:false|escape}"
       style="{if $hidden}display: none;{elseif ($width)}width: {$width};{/if}{if $height}height: {$height};{/if}"
       {if $show_placeholder|default:null}
         placeholder="{if $show_placeholder === 'label'}{$label|escape}{elseif $show_placeholder === 'help'}{$help|escape}{else}{$show_placeholder|escape}{/if}"
       {/if}
       {if $onkeydown|default:null || !empty($js_methods.onkeydown)}
         onkeydown="{if $onkeydown}{$onkeydown};{/if}{if !empty($js_methods.onkeydown)}{$js_methods.onkeydown};{/if}"
       {/if}
       {if $restrict|default:null}
         onkeypress="return changeKey(this, event, {$restrict});"
       {elseif $onkeypress || !empty($js_methods.onkeypress)}
         onkeypress="{if $onkeypress}{$onkeypress};{/if}{if !empty($js_methods.onkeypress)}{$js_methods.onkeypress};{/if}"
       {/if}
       {if $onkeyup|default:null || !empty($js_methods.onkeyup)}
         onkeyup="{if $onkeyup}{$onkeyup};{/if}{if !empty($js_methods.onkeyup)}{$js_methods.onkeyup};{/if}"
       {/if}
       onfocus="{if !empty($js_methods.onfocus)}{$js_methods.onfocus};{/if}"
       onblur="{if !empty($js_methods.onblur)}{$js_methods.onblur};{/if}{if !$readonly}cancelAutocompleter(params_{$uniqID});{/if}"
       onclick="{if $include_combobox_button}toggleAutocompleteItems(params_{$uniqID});{/if}{if !empty($js_methods.onclick)}{$js_methods.onclick};{/if}"
       oncontextmenu="return false;"
       ondrop="return false;"
       {foreach from=$js_methods|default:null key='method' item='func'}
         {if $func
         && $method|default:null
         && $method ne 'onkeydown'
         && $method ne 'onkeypress'
         && $method ne 'onkeyup'
         && $method ne 'onfocus'
         && $method ne 'onblur'
         && $method ne 'onclick'}
           {$method}="{$func}"
         {/if}
       {/foreach}
       {if $readonly|default:null} readonly="readonly"{/if}
       {if $disabled|default:null} disabled="disabled"{/if}
       uniqid="{$uniqID}"/>

    {if !$readonly|default:null}
      {if !$exclude_oldvalues|default:null}
        <input type="hidden"
               name="{$name}_oldvalue{if $index}_{$index}{/if}"
               id="{$custom_id|default:$name}_oldvalue{if $index}_{$index}{/if}"
               value="{$visible_value|escape}"
               disabled="disabled" />
      {/if}

      <div id="suggestions_{$uniqID}" class="autocompletebox" style="display: none;"></div>

      {* Encrypt the autocomplete sql parameter *}
      {*if !empty($autocomplete.sql)}
        {capture assign='autocomplete_sql'}{json encode=$autocomplete.sql}{/capture}
        {array assign='autocomplete'
          sql=$autocomplete_sql|encrypt:$smarty.const.AUTOCOMPLETE_SQL_ENCRYPT_SALT:'xtea'}
      {/if*}

      <script type="text/javascript">
        {array assign='autocomplete'
               uniqid=$uniqID
        }
        params_{$uniqID} = {json encode=$autocomplete|default:false};
        initAutocompleter(params_{$uniqID});
      </script>
    {elseif $autocomplete.view_mode == 'link' && $autocomplete.view_mode_url}
      {capture assign='ac_class'}{$uniqID} id_var-{if $autocomplete_var_type eq 'basic' || ($autocomplete_var_type eq 'searchable' && $autocomplete.search_by_id)}{$custom_id|default:$name}{else}{$autocomplete.id_var}{/if}{/capture}
      {include file="_view_autocompleter.html"
               value=$value_name|default:$visible_value
               value_id=$value_id
               view_mode_url=$autocomplete.view_mode_url
               ac_class=$ac_class
      }
    {/if}

    {if $autocomplete_var_type ne 'searchable' && !$readonly}
        {capture assign='autocomplete_type'}{if !empty($autocomplete.filters) && array_key_exists('<customer_trademark>', $autocomplete.filters)}trademarks{elseif !empty($autocomplete.filters) && array_key_exists('<contactpersons>', $autocomplete.filters)}contactpersons{else}{$autocomplete.type}{/if}{/capture}
        {capture assign='clear_param'}autocomplete_clear_{$autocomplete_type}{/capture}
        {capture assign='search_param'}autocomplete_search_{$autocomplete_type}{/capture}
        {capture assign='add_param'}autocomplete_add_{$autocomplete_type}{/capture}
        {capture assign='refresh_param'}autocomplete_refresh_{$autocomplete_type}{/capture}
        {capture assign='edit_param'}autocomplete_edit_{$autocomplete_type}{/capture}

        {if $include_combobox_button}
          <a href="javascript:void(0);" class="nz-ac-combobox-trigger"
             onclick="toggleAutocompleteItems(params_{$uniqID});">
              <i class="material-icons combobox_button">expand_more</i>
          </a>
        {/if}
      {if $button_menu}
      <div  id="button_menu_{$uniqID}" class="nz-actions-wrapper nz-ac-menu">
        <ul class="nz-actions-list">
          <li class="nz-actions-list-item nz-actions-list-item-has_options nz-openable" title="">
            <a href="javascript:void(0);"><i class="material-icons nz-glyph">more_horiz</i></a>
            <ul class="nz-actions-list-dropdown">
              {if $include_clear_button}<li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="clearAutocompleteItems(params_{$uniqID});">
                  <i class="material-icons nz-glyph nz-md-backspace">backspace</i>
                  <span class="nz-actions-list-label">{$smarty.config.$clear_param}</span>
                </a>
              </li>{/if}
              {if $include_search_button}<li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="filterAutocompleteItems(params_{$uniqID});">
                  <i class="material-icons nz-glyph">search</i>
                  <span class="nz-actions-list-label">{$smarty.config.$search_param}</span>
                </a>
              </li>{/if}
              {if $include_report_button}<li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="reportAutocompleteItems(params_{$uniqID});">
                  <i class="material-icons nz-input-icon">find_in_page</i>
                  <span class="nz-actions-list-label">{#autocomplete_search_report#}</span>
                </a>
              </li>{/if}
              {if $include_refresh_button}<li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="refreshAutocompleteItems(params_{$uniqID});">
                  <i class="material-icons nz-glyph">refresh</i>
                  <span class="nz-actions-list-label">{$smarty.config.$refresh_param}</span>
                </a>
              </li>{/if}
              {if $include_add_button}<li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="addAutocompleteItems(params_{$uniqID});">
                  <i class="material-icons nz-glyph">playlist_add</i>
                  <span class="nz-actions-list-label">{$smarty.config.$add_param}</span>
                </a>
              </li>{/if}
              {if $include_edit_button}<li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="editAutocompleteItems(params_{$uniqID});">
                  <i class="material-icons nz-glyph">edit_note</i>
                  <span class="nz-actions-list-label">{$smarty.config.$edit_param}</span>
                </a>
              </li>{/if}
            </ul>
          </li>
        </ul>
      </div>
      {else}
        {if $include_clear_button}
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#clear_button_tooltip_{$uniqID}"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="{#autocomplete_clear_items#|escape}"
             onclick="clearAutocompleteItems(params_{$uniqID});">
              <i class="material-icons nz-input-icon nz-md-backspace">backspace</i>
          </a>
          <div id="clear_button_tooltip_{$uniqID}" class="nz-tooltip-content nz-tooltip-notch__bottom-center">{#autocomplete_clear_items#} {$smarty.config.$clear_param}</div>
        {/if}
        {if $include_search_button}
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#search_button_tooltip_{$uniqID}"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="{#autocomplete_search_items#|escape}"
             onclick="filterAutocompleteItems(params_{$uniqID});">
              <i class="material-icons nz-input-icon">search</i>
          </a>
          <div id="search_button_tooltip_{$uniqID}" class="nz-tooltip-content nz-tooltip-notch__bottom-center">{$smarty.config.$search_param}</div>
        {/if}
        {if $include_report_button}
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#report_button_tooltip_{$uniqID}"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="{#autocomplete_search_items#|escape}"
             onclick="reportAutocompleteItems(params_{$uniqID});">
              <i class="material-icons nz-input-icon">find_in_page</i>
          </a>
          <div id="report_button_tooltip_{$uniqID}" class="nz-tooltip-content nz-tooltip-notch__bottom-center">{#autocomplete_search_report#}</div>
        {/if}
        {if $include_refresh_button}
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#refresh_button_tooltip_{$uniqID}"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="{#autocomplete_refresh_items#|escape}"
             onclick="refreshAutocompleteItems(params_{$uniqID});">
              <i class="material-icons nz-input-icon">refresh</i>
          </a>
          <div id="refresh_button_tooltip_{$uniqID}" class="nz-tooltip-content nz-tooltip-notch__bottom-center">{$smarty.config.$refresh_param}</div>
        {/if}
        {if $include_add_button}
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#add_button_tooltip_{$uniqID}"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="{#add#|escape}"
             onclick="addAutocompleteItems(params_{$uniqID});">
              <i class="material-icons nz-input-icon">playlist_add</i>
          </a>
          <div id="add_button_tooltip_{$uniqID}" class="nz-tooltip-content nz-tooltip-notch__bottom-center">{$smarty.config.$add_param}</div>
        {/if}
        {if $include_edit_button}
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#edit_button_tooltip_{$uniqID}"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="{#edit#|escape}"
             onclick="editAutocompleteItems(params_{$uniqID});">
              <i class="material-icons nz-input-icon">edit_note</i>
          </a>
          <div id="edit_button_tooltip_{$uniqID}" class="nz-tooltip-content nz-tooltip-notch__bottom-center">{$smarty.config.$edit_param}</div>
        {/if}
      {/if}
    {/if}

    {* Back label *}
    {if !$back_label && $var.back_label}
      {assign var='back_label' value=$var.back_label}
    {/if}
    {if !$back_label_style && $var.back_label_style}
      {assign var='back_label_style' value=$var.back_label_style}
    {/if}
    {include file="_back_label.html"
      custom_id=$custom_id
      name=$name
      back_label=$back_label
      back_label_style=$back_label_style}
    </span>
{if !$standalone}
  </td>
</tr>
{/if}
