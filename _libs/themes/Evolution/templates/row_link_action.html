{strip}
{assign var='object_onclick_handler' value=''}
{assign var='object_row_link_action' value=$object->getTypeRowLinkAction()}

{if $object_row_link_action}
  {* replace placeholders, if necessary *}
  {if preg_match('#\(.*\)#', $object_row_link_action)}{* handler *}
    {assign var='object_onclick_handler' value=$object_row_link_action}
  {elseif preg_match('#&#', $object_row_link_action)}{* URL *}
    {capture assign='object_onclick_handler'}openHref('{$object_row_link_action}', '{$link_target}', event);{/capture}
  {/if}
{/if}

{if !$object_onclick_handler}
  {capture assign='controller_string'}{if $controller ne $module}&amp;{$controller_param}={$controller}{/if}{/capture}
  {* string is only action name, default processing *}
  {if !$object_row_link_action}
    {assign var='object_row_link_action' value=$row_link_action|default:''}
  {/if}
  {if $object->isDeleted()}
    {assign var='object_row_link_action' value=''}
  {elseif $object->isTranslated()}
    {if !empty($row_link_action) && !$object->checkPermissions($row_link_action)}
      {if $object->checkPermissions('view')}
        {assign var='object_row_link_action' value='view'}
      {else}
        {assign var='object_row_link_action' value=''}
      {/if}
    {/if}
  {else}
    {if $object->checkPermissions('translate')}
      {assign var='object_row_link_action' value='translate'}
      {capture assign='object_row_link_model_lang_string'}&amp;model_lang={$object->get('model_lang')}{/capture}
    {else}
      {assign var='object_row_link_action' value=''}
    {/if}
  {/if}
  {if $object_row_link_action}{capture assign='object_onclick_handler'}openHref('{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}={$object_row_link_action}&amp;{$object_row_link_action}={$object->get('id')}{if $object->get('archived_by')}&amp;archive=1{/if}{$object_row_link_model_lang_string}', '{$link_target}', event);{/capture}{/if}
{/if}
{/strip}{if $object_onclick_handler}
  onclick="{$object_onclick_handler}"
{/if}
