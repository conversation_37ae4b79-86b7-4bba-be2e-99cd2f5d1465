<div id="{$timesheets_session_param}">
  {include file="_timesheets_list_panel.html"}
</div>

{if $task->checkPermissions('addtimesheet') || $task->checkPermissions('editalltimesheets')}
  <div id="timesheets_errors">
    {if $messages->getErrors()}{include file='message.html' display="error" items=$messages->getErrors()}{/if}
    {if $messages->getMessages()}{include file='message.html' display="message" items=$messages->getMessages()}{/if}
    {if $messages->getWarnings()}{include file='message.html' display="warning" items=$messages->getWarnings()}{/if}
  </div>

  <a name="add_timesheet"></a>
  <div id="add_panel">
    <form name="timesheets_add" action="" method="post" onsubmit="saveTimesheet(this,'panel_{$timesheets_session_param}'); return false;">
      <input type="hidden" name="model_id" id="model_id" value="{$task->get('id')}" />
      <input type="hidden" name="resource" id="resource" value="human" />
      <input type="hidden" name="parent_module" id="parent_module" value="{$parent_module}" />

      <div id="timesheet_container" class="t_border" style="width: 798px;">
        {include file="_timesheets_edit_panel.html"}
      </div>
    </form>
  </div>
{/if}
