'use strict';

function HtmlEncode(string){
    let el = document.createElement("div");
    el.innerText = el.textContent = string;
    string = el.innerHTML;
    return string;
}

function getRecordDirectionIcon(directionIndex) {
    directionIndex = parseInt(directionIndex);
    switch (directionIndex) {
        // Incoming record
        case 1: return '<i class="material-icons nz-direction-icon '
            + getRecordDirectionClass(directionIndex) + '">call_received</i>';
        // Outgoing record
        case 2: return '<i class="material-icons nz-direction-icon '
            + getRecordDirectionClass(directionIndex) + '">call_made</i>';
        // inner record
        case 3: return '<i class="material-icons nz-direction-icon '
            + getRecordDirectionClass(directionIndex) + '">repeat</i>';
        default: return '';
    }
}
function getRecordDirectionClass (directionIndex) {
    directionIndex = parseInt(directionIndex);
    switch (directionIndex) {
        // Incoming record
        case 1: return 'incoming';
        // Outgoing record
        case 2: return 'outgoing';
        // inner record
        case 3: return 'internal';
        default: return '';
    }
}

function printAssignedNames(obj) {
    let result = Array();
    for(const entry of Object.values(obj)) {
        if (typeof entry !== 'object') {
            break;
        }
        if(typeof entry['assigned_to_name'] !== 'undefined') {
            result.push(entry['assigned_to_name']);
        }
    }
    return result.join('<br/>');
}

function isDateLate(dateStr) {
    if (!dateStr || dateStr === '0000-00-00 00:00:00') {
        return false;
    }
    const dateObj = new Date(dateStr);
    const now = new Date();
    return dateObj.getTime() < now.getTime();
}

function lateRecordIcon(info) {
    const rnd = Math.random().toString(36).substring(2, 9);
    let html = '<i class="material-icons nz-tooltip-trigger nz-tooltip-autoinit warning"' +
        `data-tooltip-element="#doc-status-lete-${rnd}"` +
        'data-tooltip-position="panel: bottom center at: top center"' +
        `>warning</i>`;
    html += `<div id="doc-status-lete-${rnd}" ` +
        'class="nz-tooltip-content nz-tooltip-content--small nz-tooltip-notch__bottom-center">' +
        `<strong>${i18n[env.module_name][env.module_name + '_expired']}</strong> <br />${info}` +
        '</div>';
    return html;
}

function indicateIfLate(status, deadline, validityTerm) {
    if (status === 'closed') {
        return '';
    }
    let info = '';
    if (deadline && isDateLate(deadline)) {
        const d = new Date(deadline);
        const deadlineFormatted = d.format('d.m.Y, H:i');
        info += `${i18n[env.module_name][env.module_name + '_expired_legend']}: <strong>${deadlineFormatted}</strong>!`;
    }

    if(validityTerm && isDateLate(validityTerm)) {
        const d = new Date(validityTerm);
        const validityTermFormatted = d.format('d.m.Y, H:i');
        info += info ? '<br />' : '';
        info += `${i18n[env.module_name][env.module_name + '_expired_validity_legend']}: <strong>${validityTermFormatted}</strong>!`;
    }

    if (info !== '') {
        return lateRecordIcon(info);
    }

    return '';
}


function indicateIfLateTask(status, planned_finish_date) {
    if (status === 'finished') {
        return '';
    }

    if (!planned_finish_date || !isDateLate(planned_finish_date)) {
        return '';
    }

    const d = new Date(planned_finish_date);
    const deadlineFormatted = d.format('d.m.Y, H:i');
    const info = getLabel(`${env.module_name}_expired_legend`, '_module_name')
        + `: <strong>${deadlineFormatted}</strong>!`;
    return lateRecordIcon(info);

}
function statusHelpText(status, substatus) {
    let str = getLabel(`help_${env.module_name}_status_${status}`, '_module_name');
    if (typeof str === 'undefined') {
        str = getLabel(`help_${env.module_name}_status`, '_module_name');
        str += ': ' + getLabel(`${env.module_name}_status_${status}`, '_module_name')
    }
    if (typeof substatus === 'string' && substatus !== '') {
        str += '<br />' + getLabel(`help_${env.module_name}_substatus`, '_module_name') + substatus;
    }

    return str;
}

function statusName(status, substatus) {
    let icon;
    switch (status) {
        // document statuses
        case 'opened': icon = 'article'; break;
        case 'locked': icon = 'lock'; break;
        case 'closed': icon = 'do_not_disturb_on'; break;

        // task/events statuses
        case 'planning': icon = 'pending_actions'; break;
        case 'progress': icon = 'content_paste_go'; break;
        case 'finished': icon = 'inventory'; break;
        // event only statuses
        case 'unstarted': icon = 'event_busy'; break;
        case 'moved': icon = 'move_item'; break;
    }
    if (typeof substatus !== 'string' || substatus === '') {
        substatus = getLabel(`${env.module_name}_status_${status}`, '_module_name');
    }

    return `<i class="material-icons nz-status-icon nz-status__${status}">${icon}</i>${substatus}`;
}

function getAssignmentStatusItem(assignmentStatus, name) {
    const tooltip = getLabel(`events_participant_status_${assignmentStatus}`, '_module_name');
    return `<span class="nz-status-participant nz-status-participant__${assignmentStatus}" title="${tooltip}">${getAssignmentStatusIcon(assignmentStatus)} ${name}</span>`;
}

function getAssignmentStatusIcon(assignmentStatus) {
    let icon;
    switch (assignmentStatus) {
        case 'pending': icon = 'pending';  break;
        case 'confirmed': icon = 'check_circle'; break;
        case 'not_sure': icon = 'help'; break;
        case 'denied': icon = 'cancel'; break;
    }

    return `<i class="material-icons nz-glyph">${icon}</i>`;
}

function getSeverityLabel(severity) {
    return getLabel(`${env.module_name}_${severity}`, '_module_name');
}

function getShortDesc(description) {
    description = description.trim();
    if (description.length > 80) {
        const firstSpaceAfter80 = description.substring(80).search(/[ \r\n\t]/);
        description = description.substring(0, 80+(firstSpaceAfter80>40?0:firstSpaceAfter80));
    }
    description =  Nz.addLinksToText(Nz.HtmlSpecialChars(description));
    const nl = new RegExp('(\r?\n)+', 'g');
    return description.replaceAll(nl, '<br />');
}

function text2Html(text){
    const nl = new RegExp('(\r?\n)+', 'g');
    return Nz.addLinksToText(Nz.HtmlSpecialChars(text)).replaceAll(nl, '<br />');
}

function externalLink(url) {
    if (!url) {
        return '';
    }
    if (url.length && url.indexOf('http') !== 0) {
        url = 'http://' + url;
    }

    return url;
}

function getRecordTags(id, tags, modelTags, rights) {
    let tagsOutput = [];
    const canView = typeof rights.tags_view !== 'undefined' && rights.tags_view;
    if (!canView ) {
        return '';
    }
    const canEdit = typeof rights.tags_edit !== 'undefined' && rights.tags_edit;
    tags.forEach((t,i) => {
        let tag;

        if (typeof modelTags[t] == 'undefined') {
            return;
            /*tag = {
                properties: {
                    name: '&lt;unknown&gt;',
                    color: '#000000',
                    description: 'Unknown tag'
                }
            };*/
        } else {
            tag = modelTags[t];
        }

        const description = tag.properties.description;

        let attrs = '';
        if (description !== '') {
            const tooltipEl = document.createElement('div');
            tooltipEl.id = `tagtt_${id}_${t}`;
            tooltipEl.classList.add('nz-tooltip-content', 'nz-tooltip-notch__bottom-center');
            tooltipEl.innerHTML = description;
            document.body.append(tooltipEl);

            attrs = ` data-tooltip-element="#${tooltipEl.id}"`;
            attrs += ` data-tooltip-title="${tag.properties.name}"`;
            attrs += ` data-tooltip-position="panel: bottom center at: top center"`;
        }
        const classes = description !== '' ? ' nz-tooltip-trigger nz-tooltip-autoinit' : '';
        tagsOutput.push(`<span class="nz-tag-wrapper${classes}"${attrs}>`
            +`<i class="material-icons nz-tag-pin" style="color: ${tag.properties.color};">local_offer</i> ${tag.properties.name}</span>`);
    });
    let html = `<div class="`;
    if (canEdit) {
        html += `nz-grid-cell-editable nz--clickable" onClick="changeTags(${id}, env.module_name, '')`;
    }
    html += `">`;
    if (tagsOutput.legth !== 0) {
        html += tagsOutput.join('<br />');
    }
    html += '&nbsp;</div>';
    return html;
}

function gridPopoutLink(icon, value, type, endpoint, classes, onLoad, clickOutsideClose) {
    const valueHtml = `<i class="material-icons">${icon}</i> ${value}`;
    const position = 'panel: middle right at: middle left -8 5';
    const classesStr = `nz-grid-cell-button nz-popout-trigger nz-popout-autoinit`
        + (classes ? ' ' + classes.join(' ') : '');
    const attributes = Nz.HtmlSpecialChars(JSON.stringify({'class': `nz-grid-popout nz-grid-${type}-popout nz-pointer-middle-right nz-modal`}));
    const onLoadDataStr = onLoad ? ` data-popout-on-load="${onLoad}"` : '';
    const clickOutsideCloseStr = typeof clickOutsideClose !== 'undefined' ? ` data-popout-click-outside-close="${clickOutsideClose}"` : '';
    let title = '';
    switch (icon) {
        case 'email': title = i18n._.emails; break;
        case 'send': title = i18n._.add_email; break;
        case 'forum': title = i18n._.comments; break;
        case 'add_comment': title = i18n._.add_comment; break;
        case 'history_edu': title = i18n._.history_activity; break;
    }
    title = Nz.HtmlSpecialChars(title);
    return `<a href="#" title="${title}" class="${classesStr}" data-popout-endpoint="${endpoint}" data-popout-position="${position}" data-container-attr="${attributes}"${onLoadDataStr}${clickOutsideCloseStr}>${valueHtml}</a>`;
}

function generateCommunicationsEndpoint(type, id, isArchive) {
    let endpoint = Nz.modelUrl('ajax_get_communications_info', '', 'communications', 'communications', !!parseInt(isArchive));
    endpoint = endpoint.replace('&ajax_get_communications_info=', '');
    endpoint += `&real_module=${env.module_name}&real_controller=${env.controller_name}&model_id=${id}&communication_type=${type}`;
    return endpoint;
}

function generateAddCommunicationsEndpoint(type, id) {
    let endpoint = Nz.modelUrl('ajax_load_communication_add_panel', '', 'communications', 'communications');
    endpoint = endpoint.replace('&ajax_load_communication_add_panel=', '');
    endpoint += `&module=${env.module_name}&model_id=${id}&communication_type=${type}s&type_record=${type}`
    endpoint += `&action=add&inline_add=1`;
    return endpoint;
}

function getRecordEmails(id, value, isArchive) {
    const type = 'emails';
    const endpoint = generateCommunicationsEndpoint(type, id, isArchive);
    return gridPopoutLink('email', typeof value === 'undefined' ? '' : value , type, endpoint, [], 'showCommunicationPopoutLoad');
}

function getRecordComments(id, value, isArchive) {
    const type = 'comments';
    const endpoint = generateCommunicationsEndpoint(type, id, isArchive);
    return gridPopoutLink('forum', typeof value === 'undefined' ? '' : value, type, endpoint, [], 'showCommunicationPopoutLoad');
}

function getRecordCommentsAdd(id, isArchive) {
    const type = 'comment';
    const endpoint = generateAddCommunicationsEndpoint(type, id);
    return gridPopoutLink('add_comment', '', type+'_add', endpoint, ['nz-grid-cell-button__add'], 'addCommunicationPopoutLoad', false);
}

function getRecordEmailsAdd(id, isArchive) {
    const type = 'email';
    const endpoint = generateAddCommunicationsEndpoint(type, id);
    return gridPopoutLink('send', '', type+'_add', endpoint, ['nz-grid-cell-button__add'], 'addCommunicationPopoutLoad', false);
}

function getRecordHistory(id, history_activity, isArchive) {
    let endpoint = Nz.modelUrl('history', id, env.module_name, env.controller_name, !!parseInt(isArchive));
    endpoint += `&source=ajax&history_activity=1`;
    return gridPopoutLink('history_edu', history_activity, 'history', endpoint, [], 'showCommunicationPopoutLoad');
}

function showCommunicationPopoutLoad(targetEl, popoutEl) {
    this.shouldRecycleView = false;
    this.onClose = () => {
        this.contentEl.innerHTML = '';
    }
}

function addCommunicationPopoutLoad(targetEl, popoutEl) {
    this.shouldRecycleView = false;
    let formEl = popoutEl.querySelector('form');
    if (formEl) {
        if (formEl.id === 'emails_add') {
            // Disable the submit button/s on load
            formEl.querySelectorAll('button[type="submit"]').forEach(el => {
                el.disabled = true;
            });

            const updateButtons =  (e) => {
                // Enable the submit button/s if 'to' is filled
                let hasError = false;
                formEl.querySelectorAll('input[name^="customer_email["]:not(:disabled), input[name^="customer_email_cc["]:not(:disabled), input[name^="customer_email_bcc["]:not(:disabled)').forEach(el => {
                    hasError = hasError || el.value === ''
                });

                formEl.querySelectorAll('button[type="submit"]').forEach(el => {
                    el.disabled = hasError;
                });
            }
            formEl.addEventListener('blur', updateButtons);
            formEl.addEventListener('change', updateButtons);
            formEl.addEventListener('input', updateButtons);
            formEl.addEventListener('click', updateButtons);

            formEl.querySelector('input[name^="customer_email["]:not(:disabled)').dispatchEvent(new Event('blur', {bubbles: true, cancelable: true}));
        }

        if (formEl.id === 'comments_add') {
            const updateButtons =  (e) => {
                if (!e.target.matches('input[name^="customer_email["]:not(:disabled)') && !e.target.closest('.nz-input-controls')) {
                    return;
                }
            }

            formEl.addEventListener('blur', updateButtons);
            formEl.addEventListener('keypress', updateButtons);
        }
        formEl.addEventListener('submit', (e) => {
            const textElement = formEl.querySelector('[name="content"], [name="body"]');
            const oEditor = CKEDITOR.instances[textElement.id];
            textElement.value = oEditor.getData().replace('&nbsp;', '&#160;')

            let indicator = targetEl.closest('td').querySelector('a');
            let parentHtml = indicator.innerHTML;
            let parts = parentHtml.match(/(.*?)\s+(\d+)$/);
            if (parts && parts.length === 3) {
                indicator.innerHTML = parts[1] + ' ' + (parseInt(parts[2]) + 1);
            }
            const that = this;
            setTimeout(function(){
                that.close();
            }, 100);
        });
    }
    if (!this.onClose) {
        this.onClose = () => {
            this.contentEl.innerHTML = '';
            removeResubmitPrevention(formEl);
        }
    }
}

function getTimesheetIcon(id, timeStr, isArchive) {
    if ((!timeStr && timeStr !== '') || timeStr === 'null') {
        return '';
    }
    const endpoint = Nz.modelUrl('ajax_get_last_records_info&real_module='
            +`${env.module_name}&real_controller=${env.controller_name}&model_id=${id}`,
        id, 'tasks', 'timesheets', !!parseInt(isArchive));

    const valueHtml = `<i class="material-icons nz-grid-cell-button">timer</i> ${timeStr}`;
    const position = 'panel: right middle at: left middle 0 0';
    const classesStr = `nz-tooltip-trigger nz-tooltip-autoinit`;

    let url = Nz.modelUrl(
        'timesheets',
        id,
        'tasks',
        'tasks',
        !!parseInt(isArchive)
    );

    return `<a href="${url}" class="${classesStr}" data-endpoint="${endpoint}" data-tooltip-position="${position}">${valueHtml}</a>`;
}

function getTimesheetAddIcon(task_id, isArchive) {
    const endpoint = Nz.modelUrl('ajax_add',
        task_id + '&ajax_add=1&duration=&task_id='+task_id, 'tasks', 'timesheets', !!parseInt(isArchive));

    return gridPopoutLink('more_time', '', 'timesheet_add', endpoint, ['nz-grid-cell-button', 'nz-grid-cell-button__add'], null, 'false');
}

function salutationText(salutationCode) {
    if(!salutationCode) {
        return '';
    }
    return i18n._['salutation_' + salutationCode] + ' ';
}

function getHumanTimeFromMinutes(timeMinutes) {
    const mHour = 60
    const mDay = mHour * 24
    let days = Math.floor(timeMinutes / mDay);
    let hours = Math.floor((timeMinutes % mDay) / mHour);
    let minutes = Math.floor(timeMinutes % mHour);

    let out = [];
    if (days) {
        out.push(days + i18n._.days_short);
    }
    if (hours) {
        out.push(hours + i18n._.hours_short);
    }
    if (minutes) {
        out.push(minutes + i18n._.minutes_short);
    }

    return out.join(' ');
}

function getHumanTime(timeMs) {
    const msSec = 1000
    const msMin = msSec * 60
    const msHour = msMin * 60
    const msDay = msHour * 24
    let days = Math.floor(timeMs / msDay);
    let hours = Math.floor((timeMs % msDay) / msHour);
    let minutes = Math.floor((timeMs % msHour) / msMin);
    //let seconds = Math.floor((timeMs % msMin) / msSec);

    let out = [];
    if (days) {
        out.push(days + i18n._.days_short);
    }
    if (hours) {
        out.push(hours + i18n._.hours_short);
    }
    if (minutes) {
        out.push(minutes + i18n._.minutes_short);
    }

    return out.join(' ');
}

function getLabel(name, resource) {
    if(!resource) {
        resource = '_';
    } else if (resource && resource === '_module_name') {
        resource = env.module_name;
    }
    return i18n[resource][name];
}

function getGroupLabel($groupId, $groupName) {
    if (parseInt($groupId)) {
        return $groupName;
    }
    return getLabel('undefined');
}

function getSubtypeLabel(subtype) {
    if (!subtype) {
        return '';
    }
    return getLabel(env.module_name + '_subtype_' + subtype, env.module_name);
}

function getHasBatchLabel(value) {
    if (!value) {
        return '';
    }
    return getLabel(parseInt(value) ? 'yes' : 'no');
}

function joinArray(value) {
    if (!value) {
        return '';
    }
    return value.join(', ');
}


function gridProjectUrl(projectId) {
    return Nz.modelUrl('view', projectId, 'projects', 'projects');
}

function gridRefLink(module, controller, recordId, label) {
    if(!module || !recordId || !parseInt(recordId)) {
        return '';
    }
    const url = Nz.modelUrl('view', recordId, module, controller);
    return `<a href="${url}" class="nz-grid-cell-link" title="${label}">${label || recordId}</a>`;
}

function gridContractLink(contractId, customLabel) {
    return gridRefLink('contracts', 'contracts', contractId, customLabel);
}

function gridAssignmentCell_observer(id, properties, rightsAssign) {
    return gridAssignmentCell(id, 'observer', properties, rightsAssign);
}

function gridAssignmentCell_owner(id, properties, rightsAssign) {
    return gridAssignmentCell(id, 'owner', properties, rightsAssign);
}

function gridAssignmentCell_decision(id, properties, rightsAssign) {
    return gridAssignmentCell(id, 'decision', properties, rightsAssign);
}

function gridAssignmentCell_responsible(id, properties, rightsAssign) {
    return gridAssignmentCell(id, 'responsible', properties, rightsAssign);
}

function gridAssignmentCell(id, assignmentType, properties, rightsAssign) {
    const assignments = properties[`assignments_${assignmentType}`] || {};

    const wrapper = document.createElement('div');
    wrapper.classList.add('nz-grid-cell-list', 'nz-grid-cell-list--assignments');

    const content = document.createElement('div');
    content.classList.add('nz-grid-cell-list-content');
    content.innerHTML = printAssignedNames(assignments);
    wrapper.append(content);

    if (
        rightsAssign
        && (typeof properties.archived_by === 'undefined' || properties.archived_by=='0')
        && properties.deleted_by=='0'
        && ((properties.status !== 'closed' && properties.status !== 'finished') || assignmentType !== 'owner')
    ) {
        wrapper.classList.add('nz-grid-cell-editable');
        wrapper.dataset.assignmentsId = id;
        wrapper.dataset.assignmentsModel = env.module_name;
        wrapper.dataset.assignmentsType = `assignments_${assignmentType}`;
    } else if (assignments && Object.keys(assignments).length > 2) {
        const tolltipEl = document.createElement('div');
        tolltipEl.innerHTML = content.innerHTML;
        tolltipEl.id = `nz-grid-assignment_${assignmentType}_${id}`;
        tolltipEl.classList.add('nz-tooltip-content', 'nz-tooltip-notch__right-middle');

        wrapper.classList.add('nz-tooltip-trigger', 'nz-tooltip-autoinit');
        wrapper.dataset.tooltipElement = `#${tolltipEl.id}`;
        wrapper.dataset.tooltipPosition = `panel: right middle at: left middle`;
        wrapper.dataset.tooltipTitle = `Test Title`;
        wrapper.append(tolltipEl);
    }

    return wrapper.outerHTML;
}

function gridDateValueDeref(column, data) {
    return gridDataDeref(column.field, data);
}

function gridDataDeref(path, data) {
    const field = path.split('.');
    let valueDeref = data

    for (let i = 0; i < field.length; i++) {
        valueDeref = valueDeref[field[i]];
    }

    return valueDeref;
}

function gridFormatCheckEmptyValue(column, data) {
    const value = gridDateValueDeref(column, data)
    if (typeof value === 'undefined' || null === value || 'null' === value) {
        return '';
    }
    return value;
}

function gridFormatDateValue(date) {
    if(!date || 'null' === date || '0000-00-00' === date) {
        return '';
    }
    return Nz.formatDate(date, 'd.m.Y');
}

function gridFormatDateTimeValue(date) {
    if(!date || 'null' === date || '0000-00-00 00:00:00' === date) {
        return '';
    }
    return Nz.formatDate(date, 'd.m.Y, H:i');
}

function gridDateValueFormatter(column, data) {
    const value = gridDateValueDeref(column, data);

    if (!value || value === 'null') {
        return '';
    }

    switch (column.type) {
        case 'date':
            return gridFormatDateValue(value);
        case 'datetime':
            return gridFormatDateTimeValue(value);
    }
    return '';
}

function gridFormatDropdownValue(column, data) {
    const value = gridDateValueDeref(column, data);
    if (!value || value === 'null') {
        return '';
    }
    const varPath = column.field.split('.');
    varPath.pop();

    let fieldData = data;
    for(let i = 0; i < varPath.length; i++) {
        fieldData = fieldData[varPath[i]];
    }
    if (typeof fieldData.optgroups !== 'undefined') {
        for(const grp of Object.getOwnPropertyNames(fieldData.optgroups)) {
            for(const opt of fieldData.optgroups[grp]) {
                if (opt.option_value === value) {
                    return opt.label;
                }
            }
        }
    }
    if (typeof fieldData.options !== 'undefined') {
        for(const opt of fieldData.options) {
            if (opt.option_value === value) {
                return opt.label;
            }
        }
    }

    return getLabel(env.module_name + '_status_' + value, env.module_name);
}
