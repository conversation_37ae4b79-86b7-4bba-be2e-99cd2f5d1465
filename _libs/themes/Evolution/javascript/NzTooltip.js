'use strict';
// TODO: add support for AJAX tooltips, requested on open.
class NzTooltip {
    options;
    element;
    tooltipEl;
    mouseEnterBounded;
    mouseLeaveBounded;
    onMessageBounded;
    contentLoader;
    hideDelay;
    interactiveDelay = 100;
    uniqueId = false;
    active = false;

    constructor(options) {
        this.uniqueId = Nz.uniqueId();
        this.options = options;

        if (typeof this.options.position === 'undefined') {
            this.options.position = {
                panel: 'bottom right',
                at: 'top right',
                distance: [5, 5]
            }
        }

        if (typeof options.contentElement !== 'undefined') {
            if (typeof options.contentElement === 'string') {
                this.tooltipEl = document.querySelector(options.contentElement);
            } else {
                this.tooltipEl = options.contentElement;
            }
            this.tooltipEl.parentElement.removeChild(this.tooltipEl);
        } else {
            this.tooltipEl = document.createElement('div');
            this.tooltipEl.classList.add('nz-tooltip-popup');
            this.tooltipEl.classList.add('nz-tooltip-content');
            if (options.template) {
                const templateEl = options.template ? Nz.template2Element(options.template) : '';
                if (templateEl) {
                    this.tooltipEl.append(templateEl);
                }
            } else {
                if (options.content) {
                    const title = options.title ? `<div class="nz-tooltip-popup__title">${options.title}</div>` : '';
                    const content = options.content ? `<div class="nz-tooltip-popup__body">${options.content}</div>` : '';
                    this.tooltipEl.innerHTML = title + content;
                } else if (options.endpoint) {
                    this.contentLoader = new NzContentLoader(
                        options.endpoint,
                        this.tooltipEl);
                }

                if (this.options.position.panel === 'bottom right') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__bottom-right');
                } else if (this.options.position.panel === 'top right') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__top-right');
                } else if (this.options.position.panel === 'bottom left') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__bottom-left');
                } else if (this.options.position.panel === 'top left') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__top-left');
                } else if (this.options.position.panel === 'bottom center') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__bottom-center');
                } else if (this.options.position.panel === 'top center') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__top-center');
                } else if (this.options.position.panel === 'left middle') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__left-middle');
                } else if (this.options.position.panel === 'right middle') {
                    this.tooltipEl.classList.add('nz-tooltip-notch__right-middle');
                }
            }
        }
        this.mouseEnterBounded = this.mouseEnter.bind(this);
        this.mouseLeaveBounded = this.mouseLeave.bind(this);
        this.onMessageBounded = this.onMessage.bind(this);
    }

    attachTo(element) {
        if (this.element) {
            console.error("NzTooltip Instance is already attached, Cant' attach again", this.element);
            throw new Error("NzTooltip Instance is already attached, Cant' attach again");
        }
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        this.element = element;
        this.element.addEventListener('mouseenter', this.mouseEnterBounded);
        this.element.addEventListener('mouseleave', this.mouseLeaveBounded);

        if (this.options.interactive) {
            this.tooltipEl.addEventListener('mouseenter', this.mouseEnterBounded);
            this.tooltipEl.addEventListener('mouseleave', this.mouseLeaveBounded);
            window.addEventListener('message', this.onMessageBounded);
        }

        document.body.append(this.tooltipEl);

        this.element.classList.add('nz-tooltip--upgraded');
    }

    detach() {
        if (!this.element) {
            console.error("NzTooltip Instance is not attached, Cant' detach again");
            throw new Error("NzTooltip Instance is not attached, Cant' detach again");
        }

        this.element.removeEventListener('mouseenter', this.mouseEnterBounded);
        this.element.removeEventListener('mouseleave', this.mouseLeaveBounded);

        this.element = null;

        if (this.options.interactive) {
            this.tooltipEl.removeEventListener('mouseenter', this.mouseEnterBounded);
            this.tooltipEl.removeEventListener('mouseleave', this.mouseLeaveBounded);
            window.removeEventListener('message', this.onMessageBounded);
        }
    }

    onMessage(e) {
        if (e.data && e.data.type === 'nz-tooltip-show' && this.uniqueId !== e.data.uniqueId) {
            this.hide();
        }
    }

    fixPopupPosition() {
        for (let i = 0; i < 220; i += 30) {
            setTimeout(() => {
                Nz.positionAsync(this.element, this.tooltipEl, this.options.position, this.options.position.distance);
            }, i);
        }
    }

    mouseEnter(e) {
        clearTimeout(this.hideDelay);
        this.show();
    }

    mouseLeave(e) {
        if (this.contentLoader) {
            this.hideDelay = setTimeout(() => {
                this.startHide();
            }, 100);
            return;
        }

        this.startHide();
    }

    show() {
        if (this.active) {
            return;
        }
        this.element.classList.add('nz--opened');
        this.tooltipEl.classList.add('nz--opened');
        this.active = true;
        if (this.contentLoader) {
            if (!this.contentLoader.isLoaded() && !this.contentLoader.loadingInProgress) {
                this.tooltipEl.classList.add('nz--loading');
                this.tooltipEl.classList.add('nz-tooltip--loading');
                this.contentLoader.load().then(() => {
                    this.fixPopupPosition();
                }).finally(() => {
                    this.tooltipEl.classList.remove('nz--loading');
                    this.tooltipEl.classList.remove('nz-tooltip--loading');
                });
            }
        }

        window.postMessage({type: 'nz-tooltip-show', uniqueId: this.uniqueId });
        this.fixPopupPosition();
    }

    startHide() {
        if (!this.active) {
            return;
        }
        if (!this.options.interactive) {
            this.hide();
        }

        this.hideDelay = setTimeout(() => {
            this.hide();
        }, this.interactiveDelay);
    }

    hide() {
        if (!this.active) {
            return;
        }
        clearTimeout(this.hideDelay);
        this.element.classList.remove('nz--opened');
        this.tooltipEl.classList.remove('nz--opened');
        this.active = false;
    }

    static autoInit() {
        document.querySelectorAll('.nz-tooltip-trigger.nz-tooltip-autoinit:not(.nz-tooltip--upgraded)').forEach((el, i) => {
            this.initElement(el);
        });
    }

    static initElement(element) {
        const options = {
            element: element,
            title: element.dataset.tooltipTitle,
            content: element.dataset.tooltipContetnt ? element.dataset.tooltipContetnt : element.dataset.tooltipContent,
            position: element.dataset.tooltipPositions,
            contentElement: element.dataset.tooltipElement,
        };

        if (typeof element.dataset.endpoint !== 'undefined') {
            options.endpoint = element.dataset.endpoint;
        }

        if (typeof element.dataset.interactive !== 'undefined') {
            options.interactive = element.dataset.interactive;
        }

        if (typeof element.dataset.tooltipPosition !== 'undefined') {
            const positionStr = element.dataset.tooltipPosition;
            const positionParts = positionStr.split(/\s+/);
            let panel, at, distance;
            if (positionParts[0] === 'panel:' && positionParts[3] === 'at:') {
                panel = `${positionParts[1]} ${positionParts[2]}`;
                at = `${positionParts[4]} ${positionParts[5]}`;
            } else if (positionParts[3] === 'panel:' && positionParts[0] === 'at:') {
                panel = `${positionParts[4]} ${positionParts[5]}`;
                at = `${positionParts[1]} ${positionParts[2]}`;
            }
            options.position = {panel: panel, at: at};
            if (typeof positionParts[6] !== 'undefined') {
                distance = [positionParts[6]];
                if (typeof positionParts[7] !== 'undefined') {
                    distance.push(positionParts[7]);
                }
                options.position.distance = distance;
            }
        }

        const instance = new NzTooltip(options);
        instance.attachTo(element);
        if(typeof element.__ez_instance === 'undefined') {
            element.__ez_instance = [];
        }
        element.__ez_instance.push(instance);
    }
}
