<?xml version="1.0" encoding="{#charset#|escape}"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="{$smarty.session.lang}" lang="{$smarty.session.lang}">
<head>
{include file='head.html'}
</head>

<body>
{overlib_init src=`$smarty.const.PH_JAVASCRIPT_URL`overlib/}
{include file='confirm.html'}

<div id="loading" class="loading" style="display: none;">
  <div id="loading_msg">{#loading#|escape}</div>
  <img width="220" height="19" src="{$theme->imagesUrl}loading.gif" alt="" />
</div>

<table width="100%" border="0" cellpadding="0" cellspacing="0" class="m_container">
  <tr>
    <td class="m_main">
      <table width="100%" border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td class="m_body nopadding" colspan="2">
            <!-- Begin Messages Reports -->
{if $messages->getErrors()}{include file='message.html' display="error" items=$messages->getErrors()}{/if}
{if $messages->getMessages()}{include file='message.html' display="message" items=$messages->getMessages()}{/if}
{if $messages->getWarnings()}{include file='message.html' display="warning" items=$messages->getWarnings()}{/if}
            <!-- End Messages Reports -->
            <!-- Begin Main Body: {$template|regex_replace:"#(.*\/|_|\.html)#":""|capitalize} -->
{include file=$template}
            <!-- End Main Body: {$template|regex_replace:"#(.*\/|_|\.html)#":""|capitalize} -->
          </td>
        </tr>
      </table>
    </td>
  </tr>
  {*<tr>
    <td class="m_footer" colspan="2">
    <!-- Begin Footer -->
{include file='footer.html'}
    <!-- End Footer -->
    </td>
  </tr>*}
</table>
</body>
</html>
