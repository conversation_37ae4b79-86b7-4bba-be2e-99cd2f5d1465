{if !empty($var.values) && count($var.values)}
  {* print_columns *}
  {capture assign="default_action_columns"}print_columns{/capture}
  {capture assign="action_columns"}print_columns_pattern{if !empty($pattern_id)}{$pattern_id}{/if}{/capture}
  {if !isset($var.$action_columns) && !empty($var.$default_action_columns)}
    {assign var='action_columns' value=$default_action_columns}
  {/if}

  {* print_exclude_columns *}
  {capture assign="default_exclude_action_columns"}print_exclude_columns{/capture}
  {capture assign="action_exclude_columns"}print_exclude_columns_pattern{if !empty($pattern_id)}{$pattern_id}{/if}{/capture}
  {if !isset($var.$action_exclude_columns) && !empty($var.$default_exclude_action_columns)}
    {assign var='action_exclude_columns' value=$default_exclude_action_columns}
  {/if}
  {if $var.bb eq 0}
    <div class="t_caption2_title">{$var.label}</div>
      {assign var='t_width' value=$var.t_width_print}
  {else}
    {assign var='t_width' value='100%'}
  {/if}
  <table class="t_grouping_table" cellpadding="5" cellspacing="0" border="1"{if $t_width} width="{$t_width}"{/if}>
    <tr>
      {if empty($var.hide_row_numbers)}
      <th width="20" style="text-align: right;">{#num#|escape}</th>
      {/if}
      {foreach name='i' key='key' from=$var.names item='name'}
        {if !$var.hidden[$key] &&
            (!isset($var.$action_columns) || is_array($var.$action_columns) && in_array($name, $var.$action_columns)) &&
            (!isset($var.$action_exclude_columns) || is_array($var.$action_exclude_columns) && !in_array($name, $var.$action_exclude_columns))}
          <th{if $var.width[$key]} width="{math equation="x-y" x=$var.width[$key] y=10}"{/if}>{$var.labels[$key]}</th>
        {/if}
      {/foreach}
    </tr>
    {foreach name='i' from=$var.values item='val'}
      <tr>
        {if empty($var.hide_row_numbers)}
        <td style="text-align: right;">{$smarty.foreach.i.iteration}</td>
        {/if}
        {foreach key='key' from=$var.names item='name'}
          {if !$var.hidden[$key] &&
            (!isset($var.$action_columns) || is_array($var.$action_columns) && in_array($name, $var.$action_columns)) &&
            (!isset($var.$action_exclude_columns) || is_array($var.$action_exclude_columns) && !in_array($name, $var.$action_exclude_columns))}
            <td style="{if $var.text_align[$key]}text-align: {$var.text_align[$key]};{/if}{if $var.height[$key]}height: {$var.height[$key]}px;{/if}">
              {if $var.types[$key] eq 'text' or $var.types[$key] eq 'autocompleter' or $var.types[$key] eq 'textarea'}
                {$val[$key]|escape|default:'&nbsp;'|nl2br}
              {elseif $var.types[$key] eq 'date'}
                {$val[$key]|date_format:#date_short#|default:'&nbsp;'|nl2br}
              {elseif $var.types[$key] eq 'datetime'}
                {$val[$key]|date_format:#date_mid#|default:'&nbsp;'|nl2br}
              {elseif $var.types[$key] eq 'time'}
                {$val[$key]|date_format:#time_short#|default:'&nbsp;'|nl2br}
              {elseif $var.types[$key] eq 'dropdown' || $var.types[$key] eq 'radio'}
                {if !empty($var[$name].overwrite_value)}
                  {$val[$key]|default:'&nbsp;'}
                {elseif $var[$name].options}
                  {foreach from=$var[$name].options item='option'}
                    {if $option.option_value eq $val[$key]}
                      {if (isset($option.extended_value) && $option.extended_value && preg_match("#^<#", $option.extended_value)) || preg_match("#^<#", $option.label)}
                        {$option.extended_value|default:$option.label|default:'&nbsp;'}
                      {else}
                        {$option.label|nl2br|default:'&nbsp;'}
                      {/if}
                    {/if}
                  {/foreach}
                  {if !$val[$key]}
                    &nbsp;
                  {/if}
                {elseif $var[$name].optgroups}
                  {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                    {foreach from=$optgroup item='option'}
                      {if $option.option_value eq $val[$key]}
                        {if (isset($option.extended_value) && $option.extended_value && preg_match("#^<#", $option.extended_value)) || preg_match("#^<#", $option.label)}
                          {$option.extended_value|default:$option.label|default:'&nbsp;'}
                        {else}
                          {$option.label|nl2br|default:'&nbsp;'}
                        {/if}
                      {/if}
                    {/foreach}
                  {/foreach}
                {/if}
              {elseif $var.types[$key] eq 'file_upload'}
                {$val[$key]}
              {/if}
              {if ($val[$key] || $val[$key] === '0') && $var.back_labels[$key]}{$var.back_labels[$key]}{/if}
            </td>
          {/if}
        {/foreach}
      </tr>
    {/foreach}
  </table>
{/if}
