<table border="0" cellpadding="0" cellspacing="0">
  {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reminds&amp;reminds=ajax_list_reminds&amp;model_id={$current_model->get('id')}&amp;module={$current_model->modelName|mb_lower}s&amp;page={/capture}
  {if $pagination.pages > 1}
    <tr>
      <td class="pagemenu">
        {assign var=sort value=$reminds_sort}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          pagination=$pagination
          sort=$reminds_sort
          session_param=$reminds_session_param
          use_ajax=$reminds_use_ajax
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
  {/if}
  <tr>
    <td id="remind_container" class="nopadding">
      <div id="remind_messages_container">
        {if $messages->getErrors()}{include file='message.html' display="error" items=$messages->getErrors()}{/if}
        {if $messages->getMessages()}{include file='message.html' display="message" items=$messages->getMessages()}{/if}
        {if $messages->getWarnings()}{include file='message.html' display="warning" items=$messages->getWarnings()}{/if}
      </div>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border" style="width: 100%;">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 40px;"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$reminds_sort.from.class}" nowrap="nowrap" style="width: 140px;"><div class="t_caption_title" onclick="{$reminds_sort.from.link}">{#from#|escape}</div></td>
          <td class="t_caption t_border {$reminds_sort.to.class}" nowrap="nowrap" style="width: 140px;"><div class="t_caption_title" onclick="{$reminds_sort.to.link}">{#to#|escape}</div></td>
          <td class="t_caption t_border {$reminds_sort.type.class}" nowrap="nowrap" style="width: 140px;"><div class="t_caption_title" onclick="{$reminds_sort.type.link}">{#reminds_type#|escape}</div></td>
          <td class="t_caption t_border {$reminds_sort.remind_date.class}" nowrap="nowrap" style="width: 140px;"><div class="t_caption_title" onclick="{$reminds_sort.remind_date.link}">{#reminds_date#|escape}</div></td>
          <td class="t_caption t_border {$reminds_sort.content.class}"><div class="t_caption_title" onclick="{$reminds_sort.content.link}" style="width: 300px;">{#reminds_text#|escape}</div></td>
          <td class="t_caption t_border {$reminds_sort.added.class}" nowrap="nowrap" style="width: 150px;"><div class="t_caption_title" onclick="{$reminds_sort.added.link}">{#date#|escape}</div></td>
          <td class="t_caption"><div style="width: 30px;">&nbsp;</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter1' print=false}
        {foreach name='r' from=$reminds item='remind'}
          <tr class="{cycle values='t_odd,t_even'} vtop">
            <td class="t_border hright">{counter name='item_counter1' print=true}</td>
            <td class="t_border" nowrap="nowrap">
              {$remind.added_by_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" nowrap="nowrap">
              {foreach name='assi' from=$remind.assignments item='assignee'}
                {$assignee|escape|default:"&nbsp;"}{if !$smarty.foreach.assi.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
            <td class="t_border" nowrap="nowrap">
              {capture assign='remind_type_label'}reminds_reminder_{$remind.type}{/capture}
              {$smarty.config.$remind_type_label|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" nowrap="nowrap">
              {$remind.date|date_format:#date_mid#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$remind.custom_message|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$remind.date_added|date_format:#date_mid#|escape|default:"&nbsp;"}
            </td>
            <td>
              <img src="{$theme->imagesUrl}small/edit.png" width="12" height="12" border="0" class="pointer" alt="{#edit#|escape}" title="{#edit#|escape}" onclick="manageRemindsEditPanel('edit', '{$current_model->get('id')}','{$remind.reminder_event_id}')" />
              {if $remind.added_by eq $currentUser->get('id')}
                <img src="{$theme->imagesUrl}small/delete.png" width="12" height="12" border="0" alt="{#delete#|escape}" title="{#delete#|escape}" class="pointer" onclick="confirmAction('delete', function() {ldelim} deleteReminds('{$current_model->get('id')}','{$remind.reminder_event_id}'); {rdelim}, this);" />
              {else}
                <img src="{$theme->imagesUrl}small/delete.png" width="12" height="12" border="0" alt="{#delete#|escape}" title="{#delete#|escape}" class="dimmed pointer" onclick="alert('{#error_delete_notallowed#|escape}')" />
              {/if}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="8">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="8"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        pagination=$pagination
        sort=$reminds_sort
        session_param=$reminds_session_param
        use_ajax=$reminds_use_ajax
        hide_selection_stats=true
        link=$link
      }
    </td>
  </tr>
</table>