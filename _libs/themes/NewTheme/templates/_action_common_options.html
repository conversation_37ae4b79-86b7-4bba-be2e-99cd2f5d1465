{if $available_action.template}
    {include file="`$available_action.template`"}
{else}
  <table border="0" cellpadding="3" cellspacing="3">
  {foreach name='i' from=$available_action.options item='option'}
    {strip}
    {capture assign='info'}
      {if $option.help}{$option.help}{else}{$option.label}{/if}
    {/capture}
    {/strip}
    {if $option.type}
      {* var=$option SHOULD BE REMOVED LATER *}
      {include file="input_`$option.type`.html"
        var=$option
        standalone=false
        name=$option.name
        custom_id=$option.custom_id
        label=$option.label
        help=$option.help
        value=$option.value
        options=$option.options
        optgroups=$option.optgroups
        optgroup_label_source = $option.optgroup_label_source
        option_label_source = $option.option_label_source
        option_value=$option.option_value
        first_option_label=$option.first_option_label
        skip_please_select=$option.skip_please_select
        onclick=$option.onclick
        on_change=$option.on_change
        onchange=$option.onchange
        sequences=$option.sequences
        check=$option.check
        scrollable=$option.scrollable
        calculate=$option.calculate
        searchable=$option.searchable
        autocomplete=$option.autocomplete
        value_autocomplete=$option.value_autocomplete
        autocomplete_var_type='basic'
        autocomplete_type=$option.autocomplete_type
        autocomplete_buttons = $option.autocomplete_buttons
        min_chars = $option.min_chars
        filters_array=$option.filters
        execute_after=$option.execute_after
        readonly=$option.readonly
        hidden=$option.hidden
        required=$option.required
        really_required=$option.really_required
        disabled=$option.disabled
        options_align=$option.options_align
        js_methods=$option.js_methods
        restrict=$option.js_filter
        show_placeholder=$option.show_placeholder
        text_align=$option.text_align
        custom_class=$option.custom_class
      }
    {/if}
  {/foreach}
    <tr>
      <td colspan="3">
        <button type="{if $available_action.disable_items_before_execute}button{else}submit{/if}" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go" title="{$available_action.label}"{if $available_action.confirm || $available_action.disable_items_before_execute}onclick="{if $available_action.disable_items_before_execute}{$available_action.disable_items_before_execute}{/if}{if $available_action.confirm}return confirmAction('{$available_action.name}', submitForm, this);{/if}"{/if}>{$available_action.label}</button>
      </td>
    </tr>
  </table>
{/if}
