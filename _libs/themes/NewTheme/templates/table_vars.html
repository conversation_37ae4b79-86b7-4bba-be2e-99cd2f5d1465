{if count($var.values)}
{* print_columns *}
{capture assign="default_action_columns"}print_columns{/capture}
{capture assign="action_columns"}print_columns_pattern{if !empty($pattern_id)}{$pattern_id}{/if}{/capture}
{if !isset($var.$action_columns) && !empty($var.$default_action_columns)}
  {assign var='action_columns' value=$default_action_columns}
{/if}

{* print_exclude_columns *}
{capture assign="default_exclude_action_columns"}print_exclude_columns{/capture}
{capture assign="action_exclude_columns"}print_exclude_columns_pattern{if !empty($pattern_id)}{$pattern_id}{/if}{/capture}
{if !isset($var.$action_exclude_columns) && !empty($var.$default_exclude_action_columns)}
  {assign var='action_exclude_columns' value=$default_exclude_action_columns}
{/if}
<div class="t_caption2_title">{$var.label}</div>
<table class="t_grouping_table" cellpadding="5" cellspacing="0" border="1"{if $var.t_width} width="{$var.t_width}"{/if}><tr>
{foreach name='i' key='key' from=$var.names item='name'}
  {if !$var.hidden[$key] &&
      (!isset($var.$action_columns) || is_array($var.$action_columns) && in_array($name, $var.$action_columns)) &&
      (!isset($var.$action_exclude_columns) || is_array($var.$action_exclude_columns) && !in_array($name, $var.$action_exclude_columns))}
    <th{if $var.width[$key]} width="{$var.width[$key]}"{/if}>{$var.labels[$key]}</th>
  {/if}
{/foreach}
</tr>
<tr>
{foreach key='key' from=$var.names item='name'}
  {if !$var.hidden[$key] &&
      (!isset($var.$action_columns) || is_array($var.$action_columns) && in_array($name, $var.$action_columns)) &&
      (!isset($var.$action_exclude_columns) || is_array($var.$action_exclude_columns) && !in_array($name, $var.$action_exclude_columns))}
  <td style="{if !empty($var[$name].text_align)}text-align: {$var[$name].text_align};{/if}">
  {if $var.types[$key] eq 'text' or $var.types[$key] eq 'textarea' or $var.types[$key] eq 'autocompleter'}
    {$var.values[$key]|escape|default:'&nbsp;'|nl2br}
  {elseif $var.types[$key] eq 'date'}
    {$var.values[$key]|date_format:#date_short#|default:'&nbsp;'|nl2br}
  {elseif $var.types[$key] eq 'datetime'}
    {$var.values[$key]|date_format:#date_mid#|default:'&nbsp;'|nl2br}
  {elseif $var.types[$key] eq 'time'}
    {$var.values[$key]|date_format:#time_short#|default:'&nbsp;'|nl2br}
  {elseif $var.types[$key] eq 'dropdown' || $var.types[$key] eq 'radio'}
    {if !empty($var[$name].overwrite_value)}
      {$var.values[$key]|default:'&nbsp;'}
    {elseif $var[$name].options}
      {foreach from=$var[$name].options item='option'}
        {if $option.option_value eq $var.values[$key]}{$option.label|default:'&nbsp;'}{/if}
      {/foreach}
    {elseif $var[$name].optgroups}
      {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
        {foreach from=$optgroup item='option'}
          {if $option.option_value eq $var.values[$key]}{$option.label|default:'&nbsp;'}{/if}
        {/foreach}
      {/foreach}
    {/if}
  {elseif $var.types[$key] eq 'checkbox_group'}
    {if !empty($var[$name].overwrite_value)}
      {$var.values[$key]|default:'&nbsp;'}
    {elseif $var[$name].options}
      {foreach from=$var[$name].options item='option'}
        {if @in_array($option.option_value, $var.values[$key])}{$option.label|default:'&nbsp;'}{if !empty($var[$name].options_align) and $var[$name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
      {/foreach}
    {elseif $var[$name].optgroups}
      {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
        {foreach from=$optgroup item='option'}
          {if @in_array($option.option_value, $var.values[$key])}{$option.label|default:'&nbsp;'}{if !empty($var[$name].options_align) and $var[$name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
        {/foreach}
      {/foreach}
    {/if}
  {elseif $var.types[$key] eq 'file_upload'}
    {$var.values[$key]}
  {/if}
  {if (array_key_exists($key, $var.values) && ($var.values[$key] || $var.values[$key] === '0')) && !empty($var.back_labels[$key])}{$var.back_labels[$key]}{/if}
  </td>
{/if}
{/foreach}
</tr>
</table>
{/if}
