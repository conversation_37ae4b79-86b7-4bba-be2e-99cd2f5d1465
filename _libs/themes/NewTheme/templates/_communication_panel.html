              <table border="0" cellspacing="0" cellpadding="0" width="100%">
                <tr>
                  <td align="center" style="font-weight: bold; color:#888888;">
                    {foreach from=$allowed_actions item='allowed_action' name='aa'}
                      <a onclick="manageCommunicationTabs(this, '{$current_model->get('id')}'{if $current_model->get('archived_by')}, 1{/if})" id="communication_tab_{$allowed_action.name}" title="{$allowed_action.label|escape}" style="font-size:13px; " class="pointer communication_panel_tabs{if $allowed_action.selected} communication_tab_selected{/if}">{$allowed_action.label|escape|default:"&nbsp;"}</a>
                      {if !$smarty.foreach.aa.last}
                        | 
                      {/if}
                    {/foreach}
                  </td>
                </tr>
                <tr>
                  <td>
                    <div id="{$communications_session_param}">
                      {if $communication_type eq 'minitasks'}
                        {include file=_communication_minitasks_list_panel.html}
                      {else}
                        {include file=_communication_list_panel.html}
                      {/if}
                    </div>
                    <div id="communications_errors"></div>
                  </td>
                </tr>
                <tr>
                  <td nowrap="nowrap" class="m_header_menu t_table" style="border-bottom: 1px solid #DDDDDD; padding: 0;">
                    <ul>
                      {foreach from=$allowed_actions_add item='allowed_action' name='aa1'}
                        {if ($allowed_action eq 'comments')}
                          <li{if !$active_add_tab} style="display: none;"{/if}><span{if $allowed_action eq $active_add_tab} class="selected"{/if}><a onclick="manageCommunicationAddPanels('comment', 'add', '{$current_model->get('id')}')" id="comment_tab" class="pointer"><img src="{$theme->imagesUrl}comments.png" width="16" height="16" title="{#communications_comments_add#|escape}" alt="{#communications_comments_add#|escape}" border="0" style="vertical-align: middle;" /> {#communications_comments_add#|escape}</a></span></li>
                        {elseif ($allowed_action eq 'emails')}
                          <li{if !$active_add_tab} style="display: none;"{/if}><span{if $allowed_action eq $active_add_tab} class="selected"{/if}><a onclick="manageCommunicationAddPanels('email', 'add', '{$current_model->get('id')}')" id="email_tab" class="pointer"><img src="{$theme->imagesUrl}email.png" width="16" height="16" title="{#communications_emails_add#|escape}" alt="{#communications_emails_add#|escape}" border="0" style="vertical-align: middle;" /> {#communications_emails_add#|escape}</a></span></li>
                        {/if}
                      {/foreach}
                    </ul>
                  </td>
                </tr>
                <tr>
                  <td class="nopadding">
                    <div id="add_panel">
                      {if $active_add_tab eq 'comments'}
                        {include file=_communication_comments_panel.html}
                      {elseif $active_add_tab eq 'emails'}
                        {include file=_communication_emails_panel.html}
                      {/if}
                    </div>
                  </td>
                </tr>
              </table>