<?php

namespace Tests\Nzoom\Export\Provider;

use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Entity\ExportTable;
use Tests\Nzoom\Export\ExportTestCase;

/**
 * Integration test for GT2 functionality in ModelTableProvider
 */
class GT2IntegrationTest extends ExportTestCase
{
    private ModelTableProvider $provider;
    private RegistryMock $mockRegistry;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        // Include General class for date formatting
        if (!class_exists('General')) {
            require_once __DIR__ . '/../../../../inc/common/general.class.php';
        }

        $this->mockRegistry = new RegistryMock();
        $this->provider = new ModelTableProvider($this->mockRegistry);
    }

    public function testCompleteGT2WorkflowWithMixedTableTypes(): void
    {
        // Create a model with both grouping and GT2 tables
        $model = $this->createModelWithMixedTableTypes();

        $collection = $this->provider->getTablesForRecord($model);

        $this->assertInstanceOf(ExportTableCollection::class, $collection);
        $this->assertTrue($collection->hasTables());
        $this->assertEquals(2, $collection->count());

        $tables = $collection->getTables();
        $this->assertCount(2, $tables);

        // Check grouping table
        $groupingTable = $tables['purchases'];
        $this->assertInstanceOf(ExportTable::class, $groupingTable);
        $this->assertEquals('purchases', $groupingTable->getTableType());
        $this->assertEquals('Purchases', $groupingTable->getTableName());
        $this->assertEquals(2, $groupingTable->count());

        // Check GT2 table
        $gt2Table = $tables['orders'];
        $this->assertInstanceOf(ExportTable::class, $gt2Table);
        $this->assertEquals('orders', $gt2Table->getTableType());
        $this->assertEquals('Orders', $gt2Table->getTableName());
        $this->assertEquals(2, $gt2Table->count());

        // Verify GT2 table metadata
        $metadata = $gt2Table->getMetadata();
        $this->assertEquals('gt2', $metadata['table_type']);

        // Verify GT2 table structure (columns ordered by position)
        $header = $gt2Table->getHeader();
        $this->assertEquals(3, $header->count()); // 3 visible columns

        // Check column order (should be by position: product_name, quantity, price)
        $columns = $header->getColumns();
        $this->assertEquals('product_name', $columns[0]->getVarName());
        $this->assertEquals('quantity', $columns[1]->getVarName());
        $this->assertEquals('price', $columns[2]->getVarName());

        // Verify hidden column is not included
        $this->assertFalse($header->hasColumn('hidden_field'));

        // Check GT2 table data
        $records = $gt2Table->getRecords();
        $this->assertCount(2, $records);

        $firstRecord = $records[0];
        $this->assertEquals('Laptop', $firstRecord->getValueByColumnName('product_name')->getValue());
        $this->assertEquals('2', $firstRecord->getValueByColumnName('quantity')->getValue());
        $this->assertEquals('999.99', $firstRecord->getValueByColumnName('price')->getValue());
        $this->assertFalse($firstRecord->hasValue('hidden_field'));

        $secondRecord = $records[1];
        $this->assertEquals('Mouse', $secondRecord->getValueByColumnName('product_name')->getValue());
        $this->assertEquals('1', $secondRecord->getValueByColumnName('quantity')->getValue());
        $this->assertEquals('29.99', $secondRecord->getValueByColumnName('price')->getValue());
    }

    public function testGT2ColumnOrderingByPosition(): void
    {
        // Create a model with GT2 table where positions are not in order
        $model = $this->createModelWithUnorderedGT2Positions();

        $collection = $this->provider->getTablesForRecord($model);
        $tables = $collection->getTables();
        $gt2Table = $tables['unordered_table'];

        $header = $gt2Table->getHeader();
        $columns = $header->getColumns();

        // Verify columns are ordered by position despite being defined out of order
        $this->assertEquals('field_a', $columns[0]->getVarName()); // position 1
        $this->assertEquals('field_b', $columns[1]->getVarName()); // position 2
        $this->assertEquals('field_c', $columns[2]->getVarName()); // position 3
        $this->assertEquals('field_no_pos', $columns[3]->getVarName()); // no position (999)
    }

    private function createModelWithMixedTableTypes(): \Model
    {
        $varsForTemplate = [
            'purchases' => [
                'type' => 'grouping',
                'names' => ['item', 'price'],
                'labels' => ['Item', 'Price'],
                'hidden' => [],
                'values' => [
                    ['Laptop', 999.99],
                    ['Mouse', 29.99]
                ]
            ],
            'orders' => [
                'type' => 'gt2',
                'vars' => [
                    'product_name' => [
                        'position' => 1,
                        'hidden' => '0',
                        'label' => 'Product Name',
                        'type' => 'text'
                    ],
                    'quantity' => [
                        'position' => 2,
                        'hidden' => '0',
                        'label' => 'Quantity',
                        'type' => 'text'
                    ],
                    'price' => [
                        'position' => 3,
                        'hidden' => '0',
                        'label' => 'Unit Price',
                        'type' => 'text'
                    ],
                    'hidden_field' => [
                        'position' => 4,
                        'hidden' => '1',
                        'label' => 'Hidden Field',
                        'type' => 'text'
                    ]
                ],
                'values' => [
                    'row1' => [
                        'product_name' => 'Laptop',
                        'quantity' => '2',
                        'price' => '999.99',
                        'hidden_field' => 'secret'
                    ],
                    'row2' => [
                        'product_name' => 'Mouse',
                        'quantity' => '1',
                        'price' => '29.99',
                        'hidden_field' => 'another_secret'
                    ]
                ]
            ]
        ];

        return new \Model(['id' => 123], [], [], $varsForTemplate);
    }

    private function createModelWithUnorderedGT2Positions(): \Model
    {
        $varsForTemplate = [
            'unordered_table' => [
                'type' => 'gt2',
                'vars' => [
                    'field_c' => [
                        'position' => 3,
                        'hidden' => '0',
                        'label' => 'Field C',
                        'type' => 'text'
                    ],
                    'field_a' => [
                        'position' => 1,
                        'hidden' => '0',
                        'label' => 'Field A',
                        'type' => 'text'
                    ],
                    'field_no_pos' => [
                        'hidden' => '0',
                        'label' => 'Field No Position',
                        'type' => 'text'
                    ],
                    'field_b' => [
                        'position' => 2,
                        'hidden' => '0',
                        'label' => 'Field B',
                        'type' => 'text'
                    ]
                ],
                'values' => [
                    'row1' => [
                        'field_c' => 'Value C',
                        'field_a' => 'Value A',
                        'field_no_pos' => 'No Position',
                        'field_b' => 'Value B'
                    ]
                ]
            ]
        ];

        return new \Model(['id' => 123], [], [], $varsForTemplate);
    }
}
