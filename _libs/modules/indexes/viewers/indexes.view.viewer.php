<?php

class Indexes_View_Viewer extends Viewer {
    public $template = 'view.html';
    public $modelName = 'varindex';

    public function prepare() {
        $this->model = $this->getModel();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $params = array(0 => $this->registry,
                        'table' => 'DB_TABLE_CONTRACTS_TYPES',
                        'table_i18n' => "DB_TABLE_CONTRACTS_TYPES_I18N",
                        'value' => 't.id',
                        'label' => 'ti18n.name',
                        'where' => 't.active=1 AND t.deleted=0');
        $this->data['contracts_types'] = Dropdown::getCustomDropdown($params);
        $this->data['formulas'] = $this->model->getFormulas();
        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('indexes_view');
        $this->data['title'] = $title;
    }
}

?>
