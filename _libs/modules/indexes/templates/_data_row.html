<tr id="table_data_{$row_num}">
  <td class="t_border t_v_border t_border_left" nowrap="nowrap">
  {if $action eq 'view'}
    {$row_num}
  {else}
    <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row{if $data.used} dimmed{/if}" style="{if $row_num le 1}visibility: hidden;{/if}" {if !$data.used}onclick="confirmAction('delete_row', function() {ldelim} hideField('table_data','{$row_num}'); {rdelim}, this);"{/if} />
    {if $data.used}
      {$row_num}
    {else}
    <a href="javascript: disableField('table_data','{$row_num}')">{$row_num}</a>
    {/if}
  {/if}
  </td>
  <td class="t_border t_v_border" nowrap="nowrap">
  {if $action == 'edit' || $action == 'add'}
    {include file=`$theme->templatesDir`input_formula.html
             name=from_date
             index=$row_num
             standalone=true
             source=date
             value=$data.from
             readonly=$data.used
             value_formula=$data.from_formula
             width=100
    }
  {else}
    {if $data.from_formula && (!$data.from || $data.from eq '0000-00-00')}
      {foreach from=$formulas item=formula}
        {if $formula.option_value eq $data.from_formula}
          {$formula.label}
        {/if}
      {/foreach}
    {else}
      {$data.from|date_format:#date_short#}
    {/if}
  {/if}
  </td>
  {*<td class="t_border t_v_border" nowrap="nowrap">
  {if $action == 'edit' || $action == 'add'}
    {include file=`$theme->templatesDir`input_formula.html
             name=to_date
             index=$row_num
             standalone=true
             source=date
             value=$data.to
             value_formula=$data.to_formula
             width=100
    }
  {else}
    {if $data.to_formula && (!$data.to || $data.to eq '0000-00-00')}
      {foreach from=$formulas item=formula}
        {if $formula.option_value eq $data.to_formula}
          {$formula.label}
        {/if}
      {/foreach}
    {else}
      {$data.to|date_format:#date_short#}
    {/if}
  {/if}
  </td> *}
  <td class="t_border t_v_border" nowrap="nowrap">
  {if $action == 'edit' || $action == 'add'}
    <select name="operation[{$row_num-1}]{if $data.used}_{/if}" id="operation_{$row_num}{if $data.used}_{/if}" class="selbox short"{if $data.used} disabled="disabled"{/if} onfocus="highlight(this);" onblur="unhighlight(this);">
      <option value="*"{if $data.operation eq '*'} selected="selected"{/if}>*</option>
      <option value="+"{if $data.operation eq '+'} selected="selected"{/if}>+</option>
      <option value="="{if $data.operation eq '='} selected="selected"{/if}>=</option>
      <option value="%"{if $data.operation eq '%'} selected="selected"{/if}>%</option>
    </select>
    {if $data.used}<input type="hidden" name="operation[{$row_num-1}]" id="operation_{$row_num}" value="{$data.operation}" />{/if}
  {else}
    {$data.operation}
  {/if}
  </td>
  {array assign=js_methods onkeyup=calcIndexesFormulas(this)}
  <td class="t_border t_v_border">
  {if $action == 'edit' || $action == 'add'}
    {include file=`$theme->templatesDir`input_text.html
             name=index
             index=$row_num
             restrict=insertOnlyFloats
             standalone=true
             readonly=$data.used
             js_methods=$js_methods
             value=$data.index
    }
  {else}
    {$data.index}
  {/if}
  </td>
  <td class="t_border t_v_border">
  {if $action == 'edit' || $action == 'add'}
    {include file=`$theme->templatesDir`input_formula.html
             name=index
             index=$row_num
             standalone=true
             source=text
             formula_only=true
             readonly=$data.used
             onchange_formula=calcIndexesFormulas(this)
             formula_value=$data.index_formula
    }
  {else}
    {foreach from=$formulas item=formula}
      {if $formula.option_value eq $data.index_formula}
        {$formula.label}
      {/if}
    {/foreach}
  {/if}
  </td>
  <td class="t_border t_v_border">
  {if $action == 'edit' || $action == 'add'}
    {include file=`$theme->templatesDir`input_text.html
             name=value
             index=$row_num
             restrict=insertOnlyFloats
             readonly=true
             standalone=true
             value=$data.value
    }
  {else}
    {$data.value}
  {/if}
  </td>
</tr>
