<!-- Event Reminder -->
{include file=`$eventsTemplatesDir`_remind_info.html event=$reminder}
<br />
{$reminder.custom_message|escape|nl2br|url2href}
<br />
<br />
<form >
{strip}
  <input type="button" class="button" name="OK" value="{#remind_after#}" onclick="countReminder({$reminder.id},'{$reminder.recurrence_date}',$(remind_after).value, '{if isset($reminder.reminder_source)}{$reminder.reminder_source}{/if}');" />
  <select class="selbox" name="{#remind_after#}" id="remind_after" style="width: 40px;">
    <option value="5">5</option>
    <option value="10">10</option>
    <option value="15">15</option>
    <option value="20">20</option>
    <option value="25">25</option>
    <option value="30">30</option>
  </select> {#minutes#}
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  {#or#}
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  <input type="button" class="button" name="cancel" value="{#remind_stop#}" onclick="stopReminder({$reminder.id},'{$reminder.recurrence_date}', '{if isset($reminder.reminder_source)}{$reminder.reminder_source}{/if}');" />
  {/strip}
</form>

