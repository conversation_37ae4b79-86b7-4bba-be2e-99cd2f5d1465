<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
      {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=events&amp;controller=sections&amp;page={/capture}
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        link=$link
        hide_stats=1
      }
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="events_section" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;controller=sections" method="post" enctype="multipart/form-data">
        <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
          <tr>
            <td class="t_caption t_border t_checkall">
              {include file="`$theme->templatesDir`_select_items.html"
                pages=$pagination.pages
                total=$pagination.total
                session_param=$session_param|default:$pagination.session_param
              }
            </td>
            <td class="t_caption t_border" width="15"                nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
            <td class="t_caption t_border {$sort.name.class}"        nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#events_sections_name#|escape}</div></td>
            <td class="t_caption t_border {$sort.position.class}"    nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.position.link}">{#events_sections_position#|escape}</div></td>
            <td class="t_caption t_border {$sort.description.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.description.link}">{#events_sections_description#|escape}</div></td>
            <td class="t_caption" width="80">&nbsp;</td>
          </tr>
          {counter start=$pagination.start name='item_counter' print=false}
          {foreach name='i' from=$events_sections item='events_section'}
            {strip}
              {capture assign='info'}
                <strong>{#events_section#|escape}:</strong> {$events_section->get('name')|escape}<br />
                <strong>{#added#|escape}:</strong> {$events_section->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$events_section->get('added_by_name')|escape}<br />
                <strong>{#modified#|escape}:</strong> {$events_section->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$events_section->get('modified_by_name')|escape}<br />
                {if $events_section->isDeleted()}<strong>{#deleted#|escape}:</strong> {$events_section->get('deleted')|date_format:#date_mid#|escape}{if $events_section->get('deleted_by_name')} {#by#|escape} {$events_section->get('deleted_by_name')|escape}{/if}<br />{/if}

                <strong>{#translations#|escape}:</strong>
                  <span class="translations">
                  {foreach from=$events_section->get('translations') item='trans'}
                    <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $events_section->get('model_lang')} class="selected"{/if} />
                  {/foreach}
                  </span>
              {/capture}
            {/strip}
            <tr class="{cycle values='t_odd,t_even'}{if !$events_section->get('active')} t_inactive{/if}{if $events_section->get('deleted_by')} t_deleted{/if}">
              <td class="t_border">
                <input onclick="sendIds(params = {ldelim}
                                                the_element: this,
                                                module: '{$module}',
                                                controller: '{$controller}',
                                                action: '{$action}',
                                                session_param: '{$session_param|default:$pagination.session_param}',
                                                total: {$pagination.total}
                                               {rdelim});" 
                       type="checkbox"
                       name='items[]'
                       value="{$events_section->get('id')}"
                       title="{#check_to_include#|escape}"
                       {if @in_array($events_section->get('id'), $selected_items.ids) || 
                           (@$selected_items.select_all eq 1 && @!in_array($events_section->get('id'), $selected_items.ignore_ids))}
                         checked="checked"
                       {/if} />
                </td>
              <td class="t_border hright">{counter name='item_counter' print=true}</td>
              <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=sections&amp;{$action_param}=view&amp;view={$events_section->get('id')}">{$events_section->get('name')|escape}</a></td>
              <td class="t_border {$sort.position.isSorted}">{$events_section->get('position')|escape|default:"&nbsp;"}</td>
              <td class="t_border {$sort.description.isSorted}">{$events_section->get('description')|escape|default:"&nbsp;"}</td>
              <td class="hcenter" nowrap="nowrap">
                {include file=`$theme->templatesDir`single_actions_list.html object=$events_section}
              </td>
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="6">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="6"></td>
          </tr>
        </table>
        <br />
        <br />
        {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        link=$link
      }
    </td>
  </tr>
</table>
