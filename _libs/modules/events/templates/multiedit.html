<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}

<form name="events_multiedit" action="{$submitLink}" method="post" onsubmit="return replaceBeforeSubmit();">
<input type="hidden" name="model_lang" id="model_lang" value="{$events[0]->get('model_lang')|default:$lang}" />
<input type="hidden" name="skip_session_ids" id="skip_session_ids" value="1" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table" style="width: inherit">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="1" cellpadding="3" border="0">
        <tr>
          <td class="labelbox">{help label='type'}</td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">{$type_name|escape}</td>
        </tr>
        <tr>
          <td colspan="3"><hr />
            {#help_events_multiedit#}
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <table id="var_table_0" border="1" class="t_grouping_table">
                {assign var='event' value=$events.0}
                <tr>
                  {assign var='count_vars' value=$event->get('multivars')|@count}
                  <th colspan="{$count_vars+1}">{#edit_by_columns#|escape}</th>
                </tr>
                <tr>
                  <td class="hright">
                    <input type="checkbox" name="replace_vals" id="replace_vals" value="1" title="" onfocus="highlight(this)" onclick="selectAll(this)" onblur="unhighlight(this)" class="help" {popup text=#help_events_check_all_text# caption=#help_events_check_all_caption#|escape} />
                  </td>
                  {foreach key='key' from=$event->get('multivars') item='var' name=i}
                  <td nowrap="nowrap"{if $var.hidden} style="display: none;"{/if}>
                    <input type="checkbox" name="{$var.name}_all" id="{$var.name}_all" value="1" title="{$var.label}" onfocus="highlight(this)" onblur="unhighlight(this)" onclick="disableActivateAll(this)" /><label for="{$var.name}_all">{#events_replace_col#|escape}</label>
                  </td>
                  {/foreach}
                </tr>
                {assign var='event' value=$events.0}
                <tr style="border-bottom: 2px solid #CCCCCC;">
                  <td class="hright">
                    &nbsp;
                  </td>
                  {foreach key='key' from=$event->get('multivars') item='var' name=i}
                  <td nowrap="nowrap"{if $var.hidden} style="display: none;"{/if}>
                    {capture assign='var_name'}{$var.name}_val{/capture}
                    {if $var.type eq 'text'}
                      {include file='input_text.html'
                        standalone=true
                        name=$var_name
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        disabled=true
                        hidden=$var.hidden
                        origin='multiedit'
                        width=$var.width}
                    {elseif $var.type eq 'date'}
                      {include file='input_date.html'
                        standalone=true
                        name=$var_name
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        disabled=true
                        hidden=$var.hidden
                        hide_calendar_icon=true
                        origin='multiedit'
                        width=$var.width}
                    {elseif $var.type eq 'datetime'}
                      {include file='input_datetime.html'
                        standalone=true
                        name=$var_name
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        disabled=true
                        hidden=$var.hidden
                        hide_calendar_icon=true
                        origin='multiedit'
                        width=$var.width}
                    {elseif $var.type eq 'time'}
                      {include file='input_time.html'
                        standalone=true
                        name=$var_name
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        disabled=true
                        hidden=$var.hidden
                        hide_calendar_icon=true
                        origin='multiedit'
                        width=$var.width}
                    {elseif $var.type eq 'dropdown'}
                      {include file='input_dropdown.html'
                        standalone=true
                        name=$var_name
                        label=$var.label
                        value=$var.val
                        disabled=true
                        options=$var.options
                        optgroups=$var.optgroups
                        on_change=$var.on_change
                        sequences=$var.sequences
                        readonly=$var.readonly
                        required=$var.required
                        hidden=$var.hidden
                        origin='multiedit'
                        width=$var.width}
                    {elseif $var.type eq 'textarea'}
                      {include file='input_textarea.html'
                        standalone=true
                        name=$var_name
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        disabled=true
                        hidden=$var.hidden
                        origin='multiedit'
                        width=$var.width}
                    {elseif $var.type eq 'autocompleter'}
                      {if is_array($var.autocomplete)}
                        {assign var='autocomplete' value=$var.autocomplete}
                        {assign var='autocomplete_type' value=''}
                      {else}
                        {assign var='autocomplete' value=''}
                        {assign var='autocomplete_type' value=$var.autocomplete}
                      {/if}
                      {include file='input_autocompleter.html'
                        standalone=true
                        autocomplete_buttons='clear'
                        autocomplete_buttons_hide='add search refresh'
                        name=$var.name
                        autocomplete=$autocomplete
                        autocomplete_type=$autocomplete_type
                        autocomplete_var_type='basic'
                        required=$var.required
                        label=$var.label
                        value=$var.value
                        value_id=$var.value_id
                        value_code=$var.value_code
                        value_name=$var.value_name
                        value_autocomplete=$var.value_autocomplete
                        help=$var.help
                        readonly=$var.readonly
                        disabled=true
                        hidden=$var.hidden
                        origin='multiedit'
                        width=$var.width}
                    {elseif $var.type eq 'checkbox'}
                      <input type="checkbox" name="{$var.name}_val" id="{$var.name}_val" value="{$var.option_value}" title="{$var.label}" onfocus="highlight(this)" onblur="unhighlight(this)"{if $var.option_value eq $var.val} checked="checked"{/if}{if $var.onclick} onclick="{$var.onclick}"{/if} disabled="disabled" />
                    {/if}
                    {if $var.name == 'active'}<label for="{$var.name}_val">{#activated#|escape}</label>{/if}
                  </td>  
                  {/foreach}
                </tr>
                <tr>
                  <th align="right">{#num#|escape}</th>
                  {foreach key='key' from=$events[0]->get('multivars') item='var'}
                    <th nowrap="nowrap"{if $var.hidden} style="display: none;"{/if}><a name="error_{$var.name|replace:$smarty.const.PH_ADDITIONAL_VAR_PREFIX:''}"></a><label for="{$var.name}"{if $messages->getErrors($var.name|replace:$smarty.const.PH_ADDITIONAL_VAR_PREFIX:'')} class="error"{/if}>{help label_content=$var.label text_content=$var.help}</label>{if $var.required}{#required#}{/if}</th>
                  {/foreach}
                </tr>
              {foreach name='i' from=$events item='event'}
                <tr>
                  <td class="hright">
                    <input type="hidden" name="items[{$smarty.foreach.i.iteration-1}]" id="items_{$smarty.foreach.i.iteration}" value="{$event->get('id')}" />
                    <input type="hidden" name="num[{$smarty.foreach.i.iteration-1}]" id="num_{$smarty.foreach.i.iteration}" value="{$event->get('num')}" />
                    {if $smarty.foreach.i.iteration eq 1}
                      <a href="javascript:void(0);">{$smarty.foreach.i.iteration}</a>
                    {else}
                      <a href="javascript: disableField('var_table_0','{$smarty.foreach.i.iteration+3}')">{$smarty.foreach.i.iteration}</a>
                    {/if}
                  </td>
                  {foreach key='key' from=$event->get('multivars') item='var'}
                    <td nowrap="nowrap"{if $var.hidden} style="display: none;"{/if}>
                      {if $var.type eq 'text'}
                        {include file='input_text.html'
                          standalone=true
                          name=$var.name
                          index=$smarty.foreach.i.iteration
                          label=$var.label
                          value=$var.val
                          readonly=$var.readonly
                          hidden=$var.hidden
                          origin='multiedit'
                          width=$var.width}
                      {elseif $var.type eq 'date'}
                        {include file='input_date.html'
                          standalone=true
                          name=$var.name
                          index=$smarty.foreach.i.iteration
                          label=$var.label
                          value=$var.val
                          readonly=$var.readonly
                          hidden=$var.hidden
                          origin='multiedit'
                          width=$var.width}
                      {elseif $var.type eq 'datetime'}
                        {include file='input_datetime.html'
                          standalone=true
                          name=$var.name
                          index=$smarty.foreach.i.iteration
                          label=$var.label
                          value=$var.val
                          readonly=$var.readonly
                          hidden=$var.hidden
                          origin='multiedit'
                          width=$var.width}
                      {elseif $var.type eq 'time'}
                        {include file='input_time.html'
                          standalone=true
                          name=$var.name
                          index=$smarty.foreach.i.iteration
                          label=$var.label
                          value=$var.val
                          readonly=$var.readonly
                          hidden=$var.hidden
                          origin='multiedit'
                          width=$var.width}
                      {elseif $var.type eq 'dropdown'}
                        {include file='input_dropdown.html'
                          standalone=true
                          name=$var.name
                          index=$smarty.foreach.i.iteration
                          label=$var.label
                          value=$var.val
                          options=$var.options
                          optgroups=$var.optgroups
                          on_change=$var.on_change
                          sequences=$var.sequences
                          readonly=$var.readonly
                          required=$var.required
                          hidden=$var.hidden
                          origin='multiedit'
                          width=$var.width}
                      {elseif $var.type eq 'textarea'}
                        {include file='input_textarea.html'
                          standalone=true
                          name=$var.name
                          index=$smarty.foreach.i.iteration
                          label=$var.label
                          value=$var.val
                          readonly=$var.readonly
                          hidden=$var.hidden
                          origin='multiedit'
                          width=$var.width}
                      {elseif $var.type eq 'autocompleter'}
                        {if is_array($var.autocomplete)}
                          {assign var='autocomplete' value=$var.autocomplete}
                          {assign var='autocomplete_type' value=''}
                        {else}
                          {assign var='autocomplete' value=''}
                          {assign var='autocomplete_type' value=$var.autocomplete}
                        {/if}
                        {include file='input_autocompleter.html'
                          index=$smarty.foreach.i.iteration
                          standalone=true
                          autocomplete_buttons='clear'
                          autocomplete_buttons_hide='add search refresh'
                          name=$var.name
                          autocomplete=$autocomplete
                          autocomplete_type=$autocomplete_type
                          autocomplete_var_type='basic'
                          required=$var.required
                          label=$var.label
                          value=$var.value
                          value_id=$var.value_id
                          value_code=$var.value_code
                          value_name=$var.value_name
                          value_autocomplete=$var.value_autocomplete
                          help=$var.help
                          readonly=$var.readonly
                          hidden=$var.hidden
                          origin='multiedit'
                          width=$var.width}
                      {elseif $var.type eq 'checkbox'}
                        <input type="checkbox" name="{$var.name}[{$smarty.foreach.i.iteration-1}]" id="{$var.name}_{$smarty.foreach.i.iteration}" value="{$var.option_value}" title="{$var.label}" onfocus="highlight(this)" onblur="unhighlight(this)"{if $var.option_value eq $var.val} checked="checked"{/if}{if $var.onclick} onclick="{$var.onclick}"{/if}{if $disabled || $var.disabled} disabled="disabled"{/if} />{if $var.name == 'active'}<label for="{$var.name}_{$smarty.foreach.i.iteration}">{#activated#|escape}</label>{/if}
                      {/if}
                    </td>
                  {/foreach}
                </tr>
              {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
            <input type="hidden" name="multisave" id="multisave" value="multisave" />
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td class="t_footer"></td>
  </tr>
</table>
</form>
</div>
