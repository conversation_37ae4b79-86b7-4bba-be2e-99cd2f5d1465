<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
    <div id="form_container">

      {include file=`$theme->templatesDir`actions_box.html}
      {include file=`$theme->templatesDir`translate_box.html}
      {include file=`$theme->templatesDir`_submenu_actions_box.html}

      <form name="events_relatives" action="{$submitLink}" method="post">
      <input type="hidden" name="id" id="id" value="{$event->get('id')}" />
      <input type="hidden" name="model_lang" id="model_lang" value="{$event->get('model_lang')|default:$lang}" />
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
        {include file=`$templatesDir`_info_header.html}
        <tr>
          <td class="t_caption3" colspan="3"><div class="t_caption3_title">{$title|escape}</div></td>
        </tr>
        <tr>
          <td colspan="3">
            {include file=_tree.html pc_tree=$relatives_tree sfx='relatives'}
          </td>
        </tr>
        <tr>
          <td colspan="3"><h3>{#events_documents_referer#|escape}</h3>
            <div id="documents_referers">
              <div id="toggleCheckboxes" style="width: 300px; display: {if @count($event->get('document_referers')) gt 4}block{else}none{/if};">
                <span onclick="toggleCheckboxes(this, 'documents_referers', true)" class="pointer">{#check_all#|escape}</span> |
                <span onclick="toggleCheckboxes(this, 'documents_referers', false)" class="pointer">{#check_none#|escape}</span>
              </div>
              {if $event->get('document_referers')}
                {foreach name='i' from=$event->get('document_referers') key='ref_id' item='ref'}
                  <input type="checkbox" name="documents_referers[]" id="documents_ref{$ref_id}" value="{$ref_id}" checked="checked" /><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$ref_id}{if $ref.archived_by}&amp;archive=1{/if}" target="_blank">{$ref.full_num|numerate:$ref.direction} {$ref.name|escape}</a><br />
                {/foreach}
              {/if}
            </div>
            <br />
            <button type="button" name="filterButton" class="button" onclick="var popUrl='{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=filter&amp;open_from=events&amp;model_id={$event->get('id')}'; pop(popUrl, 820, 580);">{#select#|escape}...</button>
          </td>
        </tr>
        <tr>
          <td colspan="3" class="relatives_button_cell">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
      {include file=`$theme->templatesDir`help_box.html}
      </form>
      </div>
    </td>
    <td class="side_panel_container">
      <div id="assignments_info" class="info_extra_panel">
        {include file=`$templatesDir`_assignments_info_panel.html}
      </div>
    </td>
  </tr>
</table>
