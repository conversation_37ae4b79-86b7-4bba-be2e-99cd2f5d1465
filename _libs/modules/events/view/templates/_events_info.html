{assign var='allday_event_start_date' value=$event.event_start|date_format:#date_short#}
{assign var='allday_event_end_date' value=$event.event_end|date_format:#date_short#}
{if $event.visibility eq 'private' && $event.access ne 'edit'}
  <strong>{#events_name#}:</strong> <strong>{#events_private_event2#}</strong>{if $event.event_start lt $event.selected_date && !$event.allday_event} <em>({#events_continues_from_prev_day#})</em>{/if}<br />
  <strong>{#added_by#}:</strong> {$event.added_by_name|escape}<br />
  {if $event.allday_event}
    <strong>{if $event.allday_event == -1}{#events_date#}{else}{#events_allday_event#}{/if}:</strong>
    {if $allday_event_start_date eq $allday_event_end_date}
      {$allday_event_start_date}{if $event.allday_event == -1} ({$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}){/if}
    {else}
      <br />{$allday_event_start_date} - {$allday_event_end_date}
    {/if}
    <br />
  {else}
    <strong>{#events_start_date#}:</strong> {$event.event_start|date_format:#date_mid#}<br />
    <strong>{#events_end_date#}:</strong> {$event.event_end|date_format:#date_mid#}<br />
  {/if}
{else}
  <strong>{#events_name#}:</strong> <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event.id}">{$event.name|escape}{if $event.event_start lt $event.selected_date && !$event.allday_event} <em>({#events_continues_from_prev_day#})</em>{/if}</a><br />
  <strong>{#events_type#}:</strong> {$event.type_name|escape}<br />
  {if $event.allday_event}
    <strong>{if $event.allday_event == -1}{#events_date#}{else}{#events_allday_event#}{/if}:</strong>
    {if $allday_event_start_date eq $allday_event_end_date}
      {$allday_event_start_date}{if $event.allday_event == -1} ({$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}){/if}
    {else}
      <br />{$allday_event_start_date} - {$allday_event_end_date}
    {/if}
    <br />
  {else}
    <strong>{#events_start_date#}:</strong> {$event.event_start|date_format:#date_mid#}<br />
    <strong>{#events_end_date#}:</strong> {$event.event_end|date_format:#date_mid#}<br />
  {/if}
  {if $event.customer}
    <strong>{#events_customer#}:</strong> {$event.customer_name|escape}<br />
  {/if}
  {if $event.location}
    <strong>{#events_location#}:</strong> {$event.location|escape}<br />
  {/if}
  <strong>{#events_participants#}:</strong> {foreach name='cp' from=$event.customers_participants item='participant'}
    {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
    {if $participant.user_status eq 'pending'}<img src="{$theme->imagesUrl}pending.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'confirmed'}<img src="{$theme->imagesUrl}message.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'not_sure'}<img src="{$theme->imagesUrl}not_sure.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'denied'}<img src="{$theme->imagesUrl}error.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.cp.last},{/if}
  {/foreach}
  {foreach name='up' from=$event.users_participants item='participant'}
    {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
    {if $participant.user_status eq 'pending'}<img src="{$theme->imagesUrl}pending.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'confirmed'}<img src="{$theme->imagesUrl}message.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'not_sure'}<img src="{$theme->imagesUrl}not_sure.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'denied'}<img src="{$theme->imagesUrl}error.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.up.last},{/if}
  {/foreach}<br />
  {if !$short_info}
    <strong>{#events_description#}:</strong> {$event.description|mb_truncate:150|escape|nl2br}
  {/if}
{/if}
