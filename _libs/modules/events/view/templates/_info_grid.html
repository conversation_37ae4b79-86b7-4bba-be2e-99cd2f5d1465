{literal}<div><strong>${properties.type_name}</strong>: ${properties.name}</div>
<div><strong>${statusName(properties.status, properties.substatus_name)}</strong></div>

<div><strong>{/literal}{#added#|escape}{literal}</strong>: ${Nz.formatDate(properties.added)}<br />
  {/literal}{#by#|escape}{literal} ${properties.added_by_name}</div>
<div>{/literal}<strong>{#modified#|escape}</strong>{literal}: ${Nz.formatDate(properties.modified)}<br />
  {/literal}{#by#|escape}{literal} ${properties.modified_by_name}</div>
<div>{/literal}<strong>{#status_modified#|escape}</strong>{literal}: ${Nz.formatDate(properties.status_modified)}<br />
  {/literal}{#by#|escape}{literal} ${properties.status_modified_by_name}</div>
 ${if(properties.deleted_by_name)}
<div>{/literal}<strong>{#deleted#|escape}</strong>{literal}: ${Nz.formatDate(properties.deleted)}<br>
  {/literal}{#by#|escape}{literal} ${properties.deleted_by_name}</div>{
${/if}

<div>
  <strong>{/literal}{#translations#|escape}{literal}:</strong>
  <span class="translations">
    ${for(trans of properties.translations)}
      <img src="{/literal}{$theme->imagesUrl}flags{literal}/${trans}.png" alt="${trans}" title="${trans}" border="0" align="absmiddle"${if(trans==properties.model_lang)} class="selected"${/if} />
    ${/for}
  </span>
</div>
{/literal}
