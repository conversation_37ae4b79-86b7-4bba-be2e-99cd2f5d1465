{if $available_action.show_form}
  <form method="{$available_action.options.form_method|default:'get'}" action="{$smarty.server.PHP_SELF}">
    <input type="hidden" name="{$available_action.module_param}" value="{$available_action.module}" />
    <input type="hidden" name="{$available_action.module}" value="{$available_action.action}" />
    {if $available_action.model_id}
      <input type="hidden" name="{$available_action.action}" value="{$available_action.model_id}" />
    {/if}
    {if $available_action.model_lang}
      <input type="hidden" name="model_lang" value="{$available_action.model_lang}" />
    {/if}
    {if $available_action.name eq 'search' || $available_action.name eq 'filter'}
      <input type="hidden" name="{$available_action.session_param}" value="1" />
      <input type="hidden" name="{$available_action.name}_module" value="{$available_action.module}" />
      <input type="hidden" name="{$available_action.name}_controller" value="{$available_action.controller}" />
      {if $event}
      <input type="hidden" name="event" value="{$event}" />
      {/if}
      {if $form_name}
      <input type="hidden" name="form_name" value="{$form_name}" />
      {/if}
    {/if}
{/if}

  <table border="0" cellpadding="3" cellspacing="3" width="100%">
    <tr>
      <td style="vertical-align: top;">
        <table cellpadding="0" cellspacing="0" border="0">
          <tr>
            {if !$hide_status_label}
              <td class="labelbox"><a name="error_status"><label for="status"{if $messages->getErrors('status')} class="error"{/if}>{help label='status'}</label></a></td>
              <td>&nbsp;</td>
            {/if}
              <td class="databox" nowrap="nowrap">
                {capture assign='current_status'}{$event->get('status')}{/capture}
                <input type="hidden" name="current_status_base" value="{$current_status}" id="current_status_base" />
                <input type="radio" name="status" id="status_planning" value="planning"{if $event->get('status') eq 'planning' or !$event->get('status')} checked="checked"{/if} />
                <label for="status_planning" class="events_status planning">{#events_status_planning#|escape}</label><br />
                <input type="radio" name="status" id="status_progress" value="progress"{if $event->get('status') eq 'progress'} checked="checked"{/if} />
                <label for="status_progress" class="events_status progress">{#events_status_progress#|escape}</label><br />
                <input type="radio" name="status" id="status_finished" value="finished"{if $event->get('status') eq 'finished'} checked="checked"{/if} />
                <label for="status_finished" class="events_status finished">{#events_status_finished#|escape}</label><br />
                <input type="radio" name="status" id="status_unstarted" value="unstarted"{if $event->get('status') eq 'unstarted'} checked="checked"{/if} />
                <label for="status_unstarted" class="events_status unstarted">{#events_status_unstarted#|escape}</label><br />
                <input type="radio" name="status" id="status_moved" value="moved"{if $event->get('status') eq 'moved'} checked="checked"{/if} />
                <label for="status_moved" class="events_status moved">{#events_status_moved#|escape}</label><br />
              </td>
          </tr>
        </table>
      </td>
      <td align="right" style="vertical-align: top;">
        &nbsp;
      </td>
    </tr>
    <tr>
      <td colspan="2">
        <button type="submit" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go" title="{$available_action.options.label}">{$available_action.options.label}</button>
      </td>
    </tr>
  </table>

{if $available_action.show_form}
  </form>
{/if}
