  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td colspan="3">
        <table cellpadding="6" cellspacing="0" class="adds_options_box">
          <tr>
            <td width="25%" nowrap="nowrap" class="labelbox">
              {#events_operations#|escape}:
            </td>
            <td nowrap="nowrap" id="adds_suboptions_row">
            {foreach from=$available_actions.adds.options item='operation' key='operation_name' name='adds_operations'}
                <input type="radio" name="operation" id="{$operation.action}" value="{$operation.action}" onclick="toggleCombinedActionOptions(this)"{if $smarty.foreach.adds_operations.first} checked="checked"{/if} /><label for="{$operation.action}">{$operation.label}</label>&nbsp;
            {foreachelse}
                &nbsp;
            {/foreach}
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        {foreach from=$available_actions.adds.options item='current_option' key='opt_name' name='add_options'}
          <table border="0" cellpadding="3" cellspacing="3" id="adds_{$current_option.action}_box"{if ! $smarty.foreach.add_options.first} style="display: none;"{/if}>
            {foreach from=$current_option.options item='option'}
              {strip}
              {capture assign='info'}
                {if $option.help}{$option.help}{else}{$option.label}{/if}
              {/capture}
              {/strip}
              {if $option.type}
                {* var=$option SHOULD BE REMOVED LATER *}
                {include file="input_`$option.type`.html"
                  var=$option
                  standalone=false
                  name=$option.name
                  custom_id=$option.custom_id
                  label=$option.label
                  help=$option.help
                  value=$option.value
                  options=$option.options
                  optgroups=$option.optgroups
                  option_value=$option.option_value
                  first_option_label=$option.first_option_label
                  onclick=$option.onclick
                  on_change=$option.on_change
                  sequences=$option.sequences
                  check=$option.check
                  scrollable=$option.scrollable
                  readonly=$option.readonly
                  hidden=$option.hidden
                  required=$option.required
                  options_align=$option.options_align
                  disabled=$option.disabled}
              {/if}
            {/foreach}
            <tr>
              <td colspan="3">
                <button type="submit" class="button" name="{$current_option.name}Go" id="{$current_option.name}Go" title="{$current_option.label}"{if $current_option.confirm} onclick="return confirmAction('{$current_option.name}', submitForm, this);"{/if}>{$current_option.label}</button>
              </td>
            </tr>
          </table>
        {/foreach}
      </td>
    </tr>
  </table>