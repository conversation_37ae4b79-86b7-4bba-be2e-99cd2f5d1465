{if !$hide_side_panel}
<table border="0" cellspacing="0" cellpadding="0" class="t_layout_table">
{foreach name='events_list' from=$last_records item='event'}
  <tr>
    <td class="cal_events_panel_list">
      {strip}
        {capture assign='event_label'}{$event.event_start|date_format:#date_calendar_day_short#}{/capture}
        {capture assign='event_priority'}events_priority_{$event.priority}{/capture}
        {capture assign='event_info'}
          {include file=`$templatesDir`_events_info.html}
        {/capture}

        {*colors*}
        {if $event.ownership eq 'other'}
          {capture assign='event_color'}{$calendar_settings.color_other}{/capture}
          {capture assign='event_background_color'}{$calendar_settings.background_color_other}{/capture}
        {elseif $event.ownership eq 'mine'}
          {capture assign='mine_color'}color_{$event.type}{/capture}
          {capture assign='mine_background_color'}background_color_{$event.type}{/capture}
          {capture assign='event_color'}{$calendar_settings.$mine_color}{/capture}
          {capture assign='event_background_color'}{$calendar_settings.$mine_background_color}{/capture}
        {else}
          {capture assign='event_color'}{$calendar_settings.color_none}{/capture}
          {capture assign='event_background_color'}{$calendar_settings.background_color_none}{/capture}
        {/if}
        {assign var='currentUserId' value=$smarty.session.currentUserId}
        {capture assign='private_event'}{if $event.visibility eq 'private' && $event.access ne 'edit'}1{else}0{/if}{/capture}
      {/strip}
      <div class="cal_event pointer{if $private_event} cal_event_private help{/if}{if $event.users_assignments.$currentUserId && $event.users_assignments.$currentUserId.user_status == 'denied'} cal_event_denied{/if}"{if !$private_event} onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event.id}'"{/if} style="background-color: {$event_background_color};">
        <img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" {help text_content=$event_info label_content=$event_label|escape popup_only=1} />
      {if $private_event}
        <span {help text_content=$event_info label_content=$event_label|escape popup_only=1} style="color: {$event_color};">{$event.event_start|date_format:#date_short#} {if $event.allday_event == 1}--&nbsp;:&nbsp;--{elseif $event.allday_event == -1}({$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}){elseif $event.event_start lt $event.selected_date}&raquo;&raquo;:&raquo;&raquo;{else}{$event.event_start|date_format:#time_short#}{/if} {#events_private_event#}</span>
      {else}
        {if $event.access eq 'edit' && !($event.type_keyword == 'plannedtime' && $event.status != 'planning')}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=edit&amp;edit={$event.id}{if $event.recurrence_type && $event.parent_event_id eq 0}&amp;new_recurrence_date={$event.event_start|date_format:#date_iso_short#}&amp;parent_event_id={$event.id}{/if}"{if $event.recurrence_type && $event.parent_event_id eq 0} onclick="return selectEventEditType(this)"{/if} title="{#edit#|escape}"><img src="{$theme->imagesUrl}small/{if $event.recurrence_type && $event.parent_event_id eq 0}edit3{else}edit2{/if}.png" width="10" height="10" border="0" alt="{#edit#|escape}" /></a>
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=delete&amp;delete={$event.id}" onclick="Event.stop(event); return confirmAction('delete', function(el) {ldelim} window.location.href = el.href; {rdelim}, this);" title="{#delete#|escape}"><img src="{$theme->imagesUrl}small/delete2.png" width="10" height="10" border="0" alt="{#delete#|escape}" /></a>
        {/if}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event.id}" title="{#view#|escape}"><img src="{$theme->imagesUrl}small/view2.png" width="10" height="10" border="0" alt="{#view#|escape}" /></a>
        {if $event.access}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=communications&amp;communications={$event.id}&amp;communication_type=comments{if $event.recurrence_type && $event.parent_event_id eq 0}&amp;new_recurrence_date={$calendar->dateISO|date_format:#date_iso_short#}{/if}" title="{#comments#|escape}"><img src="{$theme->imagesUrl}small/comments.png" width="10" height="10" border="0" alt="{#comments#|escape}" /></a>
        {/if}
        {if $event.reminder_type && $event.event_end gt $smarty.now|date_format:"%Y-%m-%d %H:%M:%S"}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=remind&amp;remind={$event.id}{if $event.recurrence_type && $event.parent_event_id eq 0}&amp;new_recurrence_date={$event.event_start|date_format:#date_iso_short#}{/if}"{if $event.recurrence_type && $event.parent_event_id eq 0} onclick="return selectEventRemindType(this)"{/if} title="{#remind#|escape}"><img src="{$theme->imagesUrl}small/remind.png" width="10" height="10" border="0" alt="{#remind#|escape}" /></a>
        {/if}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event.id}" {help text_content=$event_info label_content=$event_label|escape popup_only=1} style="color: {$event_color};">{$event.event_start|date_format:#date_short#} {if $event.allday_event == 1}--&nbsp;:&nbsp;--{elseif $event.allday_event == -1}({$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}){elseif $event.event_start lt $event.selected_date}&raquo;&raquo;:&raquo;&raquo;{else}{$event.event_start|date_format:#time_short#}{/if} {$event.name|mb_truncate:60:'...':true|escape}</a>
      {/if}
      </div>
    </td>
  </tr>
{foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error">{#events_no_customer_events#}</td>
  </tr>
{/foreach}
</table>
{/if}