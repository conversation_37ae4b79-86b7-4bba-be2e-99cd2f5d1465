/**
 * Set a default end date (and time) for event,
 *   based on the start date and the default duration for the current event type
 * (used when changing the start date (and time) of the event)
 */
function setEventDefaultEndDate() {
    // If the event end date is already set manually by the user
    if (checkEventEndDateIsManuallySet()) {
        // Don't change it
        return true;
    }

    // Get the duration for the current event type (using the type field)
    var type = $('type');
    var duration_milliseconds = type.options[type.selectedIndex].className.replace('duration_', '') * 60000;

    // Get the start and the end date fields
    var start_date_field         = $('event_start_date');
    var start_time_field         = $('event_start_time');
    var end_date_field           = $('event_end_date');
    var end_time_field           = $('event_end_time');
    var end_date_field_formatted = $('event_end_date_formatted');
    var end_time_field_formatted = $('event_end_time_formatted');

    // if any field is cleared, do nothing
    if (!start_date_field.value || !start_time_field.value) {
        return true;
    }

    // Get the date and time as arrays
    var date_arr = start_date_field.value.split('-');
    var time_arr = start_time_field.value.split(':');

    // Get the date parts
    var year    = parseInt(date_arr[0]);
    var month   = parseInt(date_arr[1]) - 1;
    var date    = parseInt(date_arr[2]);
    var hour    = parseInt(time_arr[0]);
    var minutes = parseInt(time_arr[1]);

    // Make a date object from the start date
    var start_date = new Date(year, month, date, hour, minutes);

    // Calculate the new end date
    var new_end_date = new Date(start_date.getTime() + duration_milliseconds);

    // Set the new end date
    end_date_field.value = new_end_date.format('Y-m-d');
    if (end_date_field.getAttribute('onchange')) {
        end_date_field.onchange();
    }
    end_time_field.value = new_end_date.format('H:i');
    if (end_time_field.getAttribute('onchange')) {
        end_time_field.onchange();
    }
    end_date_field_formatted.value = new_end_date.format('d.m.Y');
    end_time_field_formatted.value = new_end_date.format('H:i');

    // Set that the event end date is not manually set
    // (we need to set this, because the onchange of the end date should already set it as manually set,
    //   and we need to set it back as not manually set, because it's set from the JavaScript, not by the user)
    eventEndDateIsManuallySet('');
}

/**
 * Set that the end date of the event is manually set (or not set) by the user
 *
 * @param value - the value of the hidden field used as flag (0 = not manually set, 1 = manually set) - default: 1
 */
function eventEndDateIsManuallySet(value) {
    value = typeof value !== 'undefined' ? value : 1;
    $('event_end_date_is_manually_set').value = value;
}

/**
 * Check if the event end date is manually set by the user
 *
 * @return bool - true (if set) or false (if not set)
 */
function checkEventEndDateIsManuallySet() {
    // Check the hidden field used as flag
    if ($('event_end_date_is_manually_set').value) {
        return true;
    } else {
        return false;
    }
}

/**
 * Hide/Show the time fields if the checkbox is checked/unchecked
 *
 * @param element - the checkbox: "All day" (allday_event)
 */
function toggleAlldayEvent(element) {
    // If the checkbox is checked (i.e. the event is "all day")
    if (element.checked) {
        // Hide the time fields
        $('event_start_time').parentNode.style.display = 'none';
        $('event_end_time').parentNode.style.display   = 'none';
        // planned time event
        if (element.value == -1) {
            $('to_cell').style.display                 = 'none';
            $('duration').parentNode.style.display     = '';
        }
    } else {
        // Show the time fields
        $('event_start_time').parentNode.style.display = '';
        $('event_end_time').parentNode.style.display   = '';
        // planned time event
        if (element.value == -1) {
            $('to_cell').style.display                 = '';
            $('duration').parentNode.style.display     = 'none';
        }
    }
}

/**
 * Set end date after start date is changed
 */
function setEndDate(element) {
    if (element.value) {
        var d = parseISODate(element.value);
        $('event_end_date_formatted').value = d.format('d.m.Y');
        $('event_end_date').value = element.value;
    }
}

/**
 * Show events assignments configurator panel
 *
 * @param assignment_type - type of assignment that configurator is opened from: participants or observers
 * @return {Boolean}
 */
function loadEAConfigurator(assignment_type) {
    var container = $('events_assignments_configurator'),
        a_type = $('events_assignments_configurator_type');
    if (!container || !a_type) {
        return false;
    }
    // if panel is visible and the same button is clicked again, hide the panel
    if (container.style.display != 'none' && a_type.value == assignment_type) {
        container.style.display = 'none';
        return true;
    }
    a_type.value = assignment_type;
    var pos = getMousePosition();
    container.style.top = pos[1] + 'px';
    container.style.left = pos[0] + 10 + 'px';
    container.style.display = '';
    return true;
}

/**
 * Save or delete configuration for events assignments
 *
 * @param config_id - name of configuration on save or id of configuration on delete
 * @param div_id - element where configurator panel will be reloaded after success
 * @param config_action - save or delete
 */
function manageEAConfigurator(config_id, div_id, config_action) {
    var container = $(div_id),
        a_type = $('events_assignments_configurator_type');
    if (!container || !a_type || !a_type.value.match(/^(participants|observers)$/) || isNaN(parseInt(config_id)) && config_id == '') {
        return;
    }
    a_type = a_type.value;

    var parameters = {
        config_action: config_action,
        config_id: config_id,
        assignment_type: a_type
    };

    if (config_action == 'save') {
        var current_assignments = [], old_user_ids = [], i;
        if (a_type == 'participants') {
            current_assignments = $$('input[type="hidden"]:not([disabled]).user_assign');
            for (i = 0; i < current_assignments.length; i++) {
                if (current_assignments[i].name.match(/^user_assign\[\d+\]$/)) {
                    old_user_ids.push(current_assignments[i].name.replace(/^user_assign\[(\d+)\]$/, '$1'));
                }
            }
        } else {
            current_assignments = $$('select:not([disabled]).access');
            for (i = 0; i < current_assignments.length; i++) {
                if (current_assignments[i].name.match(/^access\[\d+\]$/)) {
                    old_user_ids.push(current_assignments[i].name.replace(/^access\[(\d+)\]$/, '$1'));
                }
            }
        }
        parameters.user_ids = old_user_ids.join(',');
    }

    parameters = Object.toQueryString(parameters);

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        parameters: parameters,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            var messages_container = $('events_assignments_messages_container');
            if (result.errors) {
                var messages = '';
                if (result.errors) {
                    messages += '<ul class="error" style="padding: 0 0 0 27px; margin: 0; font-size: 10px;">\n';
                    for (var err in result.errors) {
                        if (typeof result.errors[err] == 'string') {
                            messages += '<li>' + result.errors[err] + '</li>\n';
                        }
                    }
                    messages += '</ul>';
                }
                if (messages_container) {
                    messages_container.innerHTML = messages;
                    removeClass(messages_container.parentNode, 'hidden');
                }
            } else if (result.data) {
                if (messages_container) {
                    messages_container.innerHTML = '';
                    addClass(messages_container.parentNode, 'hidden');
                }
                container.innerHTML = result.data;

                var scripts = container.getElementsByTagName('script');
                for (var j = 0; j < scripts.length; j++) {
                    ajaxLoadJS(scripts[j]);
                }
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=events&events=ajax_assignments_configurator';

    new Ajax.Request(url, opt);
}

/**
 * Activate button for saving new events assignments configuration when Enter
 * key is pressed for the text field where name is specified.
 *
 * @param evt - window event
 */
function saveEAConfiguratorOnEnter(evt) {
    if (typeof evt == 'undefined' && window.event) { evt = window.event; }
    if (evt.keyCode == 13) {
        $('save_ea_config').focus();
        $('save_ea_config').click();
        return false;
    }
    return true;
}

/**
 * Load assignments for participants/observers from saved configuration
 *
 * @param user_ids - comma-separated list of user ids to assign
 * @return {Boolean}
 */
function loadSavedEventAssignments(user_ids) {
    var a_type = $('events_assignments_configurator_type') != null ? $('events_assignments_configurator_type').value : '';
    var tbl = $(a_type == 'participants' ? 'table_assign' : 'user_access');
    if (!a_type || !a_type.match(/^(participants|observers)$/) || !tbl) {
        return true;
    }
    // remove all current assignments that the user can delete
    var rows_length = tbl.rows.length - 1;
    for (var r = rows_length; r >= 0; r--) {
        if (tbl.rows[r].className.match(/t_odd|t_even/) && tbl.rows[r].cells[0].innerHTML.match(/hideField/)) {
            tbl.deleteRow(r);
        }
    }
    // update row numbers in non-deletable rows
    var num_header_rows = 0;
    for (var r = 0; r < tbl.rows.length; r++) {
        if (tbl.rows[r].className.match(/t_odd|t_even/) && tbl.rows[r].cells[0].innerHTML.match(/\d+/)) {
            tbl.rows[r].cells[0].innerHTML = (r + 1 - num_header_rows);
        } else {
            num_header_rows++;
        }
    }
    // nothing to load
    if (!user_ids) {
        return true;
    }
    // prepare autocomplete params and data for method
    var autocomplete = {
        field: a_type + '_autocomplete'
    }, data = {
        $user_ids: user_ids
    };
    // call the execute_after method
    loadEventAssignments(autocomplete, data);
    return true;
}

/**
 * Loads data for event assignments (only users are assigned for now)
 *
 * @param {Object} autocomplete - autocompleter settings
 * @param {Object} data - autocompleter returned data
 */
function loadEventAssignments(autocomplete, data) {
    // ids of selected users
    var user_ids = typeof data.$user_ids == 'string' ? data.$user_ids : '';
    if (!user_ids) {
        return;
    }
    user_ids = user_ids.split(',');

    // participants or observers
    var assignment_type = autocomplete.field.replace(/_autocomplete$/, '');
    var current_assignments = [], old_user_ids = [], new_user_ids = [];
    if (assignment_type == 'participants') {
        current_assignments = $$('input[type="hidden"]:not([disabled]).user_assign');
        for (var i = 0; i < current_assignments.length; i++) {
            if (current_assignments[i].name.match(/^user_assign\[\d+\]$/)) {
                old_user_ids.push(current_assignments[i].name.replace(/^user_assign\[(\d+)\]$/, '$1'));
            }
        }
    } else {
        current_assignments = $$('select:not([disabled]).access');
        for (var i = 0; i < current_assignments.length; i++) {
            if (current_assignments[i].name.match(/^access\[\d+\]$/)) {
                old_user_ids.push(current_assignments[i].name.replace(/^access\[(\d+)\]$/, '$1'));
            }
        }
    }
    // get only the new users to load info for
    for (var i = 0; i < user_ids.length; i++) {
        if (old_user_ids.indexOf(user_ids[i]) == -1) {
            new_user_ids.push(user_ids[i]);
        }
    }

    for (var i in data) {
        // iterate through the properties and clear fields
        if (!/^\$/.test(i)) {
            continue;
        }
        var name = i.replace(/\$/, '');
        if ($(name) != null) {
            $(name).value = '';
        }
    }
    removeClass($(autocomplete.field), 'working');

    // if all users are already present as assignments
    if (!new_user_ids.length) {
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var parameters = {
        assignment_type: assignment_type,
        participant_type: 'user',
        user_ids: new_user_ids.join(',')
    };
    parameters = Object.toQueryString(parameters);

    var opt = {
        method: 'get',
        parameters: parameters,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var participants = eval('(' + t.responseText + ')');

            // get container table
            var tbl = $(assignment_type == 'participants' ? 'table_assign' : 'user_access');
            // get number of header rows
            var num_header_rows = 0;
            for (var r = 0; r < tbl.rows.length; r++) {
                if (tbl.rows[r].className.match(/t_odd|t_even/) && tbl.rows[r].cells[0].innerHTML.match(/\d+/)) {
                    break;
                }
                num_header_rows++;
            }
            // get number of cells to add in new rows
            var cells_count = tbl.rows[tbl.rows.length - 1].cells.length;

            for (var i = 0; i < participants.length; i++) {
                var participant = participants[i];

                var new_row = tbl.rows.length;
                var new_row_num = new_row - num_header_rows + 1;

                // insert a new row in the table
                var row = tbl.insertRow(new_row);
                // change the class name
                addClass(row, (tbl.rows[new_row - 1].className.match(/t_odd/) ? 't_even' : 't_odd'));
                // if this is a new participant
                if (!participant.invitations_count) {
                    addClass(row, 't_inactive');
                }

                // get the first column, which is system
                var cell_0 = row.insertCell(0);

                // the arguments for the special first column are prepared customly
                cell_0.style.whiteSpace = 'nowrap';
                cell_0.innerHTML = '<img src="' + env.themeUrl + 'images/small/delete.png" height="12" width="12" onclick="confirmAction(\'delete_row\', function() { hideField(\'' + tbl.id + '\', \'' + new_row_num + '\', ' + num_header_rows + '); }, this);" class="pointer floatl"  alt="' + i18n['labels']['delete'] + '" title="' + i18n['labels']['delete'] + '" /> ' + new_row_num;
                addClass(cell_0, 't_border hright');

                var model = participant.participant_type;

                // change fields attributes in each cell
                if (assignment_type == 'participants') {
                    for (var c = 1; c < cells_count; c++) {
                        var cell = row.insertCell(c);
                        if (c == 1) {
                            icon_name = model;
                            if (icon_name == 'user' && parseInt(participant['is_portal'])) {
                                icon_name += '_portal';
                            }
                            cell.innerHTML = '<img src="' + env.themeUrl + 'images/small/' + icon_name + '.png" height="12" width="12" border="0" alt="" title="' + i18n['labels'][icon_name] + '" /> ' + '\n' +
                            participant[model + '_name'] + '\n' +
                            '<input type="hidden" id="' + model + '_assign_' + new_row_num + '" class="' + model + '_assign" name="' + model + '_assign[' + participant['participant_id'] + ']" value="' + participant['participant_id'] + '" />';
                            addClass(cell, 't_border');
                        } else if (c == 2) {
                            cell.innerHTML = participant.availability_table;
                            addClass(cell, 't_border table_intervals');
                        } else {
                            cell.innerHTML = participant.participant_status;
                        }
                    }
                } else if (assignment_type == 'observers') {
                    for (var c = 1; c < cells_count; c++) {
                        var cell = row.insertCell(c);
                        if (c == 1) {
                            icon_name = model;
                            if (parseInt(participant['is_portal'])) {
                                icon_name += '_portal';
                            }
                            cell.innerHTML = '<img src="' + env.themeUrl + 'images/small/' + icon_name + '.png" height="12" width="12" border="0" alt="" title="'+i18n['labels'][icon_name] + '" /> ' + '\n' +
                            participant[model + '_name'];
                            addClass(cell, 't_border');
                        } else if (c == 2) {
                            cell.innerHTML = '<select class="selbox access" style="width: 100%;" id="access_' + new_row_num +
                            '" name="access[' + participant['participant_id'] + ']" onfocus="highlight(this);" onblur="unhighlight(this);"><option value="view">' + i18n['labels']['event_access_view'] + '</option><option value="edit"' + (participant.access == 'edit' ? ' selected="selected"' : '') + '>' + i18n['labels']['event_access_edit'] + '</option></select>';
                            addClass(cell, 't_border');
                        }
                    }
                }
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.module_name + '=ajax_load_assignments';
    if (env.module_name == 'events' && env.action_name == 'assign' && $('id')) {
        url += '&ajax_load_assignments=' + $('id').value;
    }

    new Ajax.Request(url, opt);
}

/**
 * Replies to invitation via events module
 * The invitation could be confirmed, not_sure or denied
 * This method is used via ajax
 *
 * @param event_id - id of the event to be confirmed or denied
 * @param status - new user status
 * @param old_status - old user status
 */
function replyInvitation(event_id, status, old_status) {

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var container = $('reply_invitation_' + event_id);
            eval('var data = ' + t.responseText);
            container.innerHTML = '<img src="' + data['image_src'] + '" width="12" height="12" alt="" border="0" title="'+ data['status_name'] + ' ' + data['status_date'] + '" /> ' + data['status_name'] + '&nbsp;&nbsp;&nbsp;<span onclick="replyInvitation(' + event_id + ', \'' + data['opposite_status_1'] + '\', \'' + status + '\');" class="pointer" style="color: #666666;"><img src="' + data['opposite_status_image_src_1'] + '" width="12" height="12" alt="" border="0" />&nbsp;<i>' + data['opposite_action_name_1'] + '</i></span>&nbsp;&nbsp;&nbsp;<span onclick="replyInvitation(' + event_id + ', \'' + data['opposite_status_2'] + '\', \'' + status + '\');" class="pointer" style="color: #666666;"><img src="' + data['opposite_status_image_src_2'] + '" width="12" height="12" alt="" border="0" />&nbsp;<i>' + data['opposite_action_name_2'] + '</i></span>';
            removeClass(container.parentNode, 't_inactive');
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=events&events=ajax_reply_invitation&event_id=' + event_id + '&status=' + status;

    new Ajax.Request(url, opt);

    return false;
}

/**
 * Checks participant availability
 *
 * @param element - the current form elements
 * @param event_id - event id
 * @param participant_type - user or customer
 * @param participant_id - participant id
 */
function checkParticipantAvailability(element, event_id, participant_type, participant_id) {
    // validate values before checking availability
    if ($('event_start_date') && $('event_end_date') && $('event_start_time') && $('event_end_time')) {
        var start_date = $('event_start_date').value;
        var end_date = $('event_end_date').value;
        var start_time = $('event_start_time').value;
        var end_time = $('event_end_time').value;
        var allday_event = $('allday_event') && $('allday_event').checked ? $('allday_event').value : 0;
        var msg = '';
        if (!start_date || !end_date || ((!start_time || !end_time) && !allday_event)) {
            msg = (!start_date || !start_time) ? i18n['messages']['error_start_date_after_current'] : '';
            msg += (!end_date || !end_time) ? (msg ? "\n" : '') + i18n['messages']['error_finish_date_after_current'] : '';
        } else if (start_date > end_date || start_date == end_date && start_time >= end_time && !allday_event) {
            msg = i18n['messages']['error_timesheets_startperiod_endperiod'];
        } else if (allday_event == -1 && $('duration')) {
            var duration = parseInt($('duration').value);
            if (isNaN(duration) || duration <= 0 || duration > 1440) {
                msg = i18n['messages']['error_valid_duration'];
            }
        }
        if (msg) {
            alert(msg);
            return false;
        }
    }
    var submit_form, form;
    // check what should be done
    // this function is executed in two possible cases:
    // 1. When adding or editing event
    // 2. When user wants to just check the availability exclusively
    if (element.tagName == 'FORM') {
        // 1. When adding or editing event
        submit_form = true;
        form = element;
    } else {
        // 2. When user wants to just check the availability exclusively
        submit_form = false;
        form = element.form;
    }

    // if user has opted out of checking for conflicts, submit form directly
    if (submit_form && env.confirm && env.confirm.event_conflict === 0) {
        return true;
    }

    // a flag determing whether to submit the form or not
    var do_submit_form = false;

    var opt = {
        asynchronous: false,
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval('var data = ' + t.responseText);

            // update information of the user availability
            if (submit_form) {
                $('availability_check').value = data.availability;
                if (data.availability == 'busy') {
                    // the user is busy in the period of the event
                    // check if the user confirms or denies conflicting events
                    var btn = Element.select(form, 'button[type="submit"]');
                    if (btn.length) {
                        do_submit_form = confirmAction('event_conflict', function(el) { el.form.submit(); }, btn[0]);
                    } else {
                        do_submit_form = confirmAction('event_conflict', function(el) { el.submit(); }, form);
                    }
                } else {
                    do_submit_form = true;
                }
            } else {
                // get rows in order to display them
                availability_rows = Element.select(element.parentNode.parentNode.parentNode, '.availability_check');
                for (var i = 0; i < availability_rows.length; i++) {
                    availability_rows[i].style.display = '';
                }
                target = element.id + '_container';
                $(target).innerHTML = data.availability_table;

                target_caption = element.id + '_caption';
                $(target_caption).innerHTML = i18n['labels']['availability_' + data.availability];
                $(target_caption).className = 'availability ' + data.availability;

                $('availability_check').value = data.availability;
                Effect.Fade('loading');
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    // show "please wait" only if the user checks the availability exclusively
    if (!submit_form) {
        Effect.Center('loading');
        Effect.Appear('loading');
    }

    var url = env.base_url + '?' + env.module_param + '=events&events=ajax_check_availability&ajax_check_availability=' + event_id + '&participant_type=' + participant_type + '&participant_id=' + participant_id + '&show_availability_header=1';

    new Ajax.Request(url, opt);

    if (submit_form) {
        // the form should be submitted only if
        // the user has confirmed the event conflicts
        return do_submit_form;
    } else {
        return true;
    }
}
