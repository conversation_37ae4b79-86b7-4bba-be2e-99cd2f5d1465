<?php

class Events_Subpanel_Viewer extends Viewer {
    public $template = 'subpanel.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'events.factory.php';

        $use_ajax = ($this->registry['request']->get('source')) ? true : false;
        $session_param = $this->registry['request']->get('session_param');
        $session_param_prefix = preg_replace('#event$#', '', $session_param);

        //initialize the custom filters
        $custom_filters = array();

        $filters = Events::saveSearchParams($this->registry, $custom_filters, $session_param_prefix);

        $this->setCustomTemplate();
        $this->setFrameset('frameset_blank.html');

        // modelFields contains visible columns
        if (!empty($this->modelFields)) {

            $filters['get_fields'] = $this->modelFields;

            if (in_array('participants', $this->modelFields)) {
                //set flag in registry, so that the event assignments are taken from the DB
                $this->registry->set('getAssignments', true, true);
            }
        }

        list($events, $pagination) = Events::pagedSearch($this->registry, $filters);

        $this->data['events'] = $events;
        $this->data['pagination'] = $pagination;

        //get calendar settings
        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
        $this->data['calendar_settings'] = Calendars_Calendar::getSettings($this->registry);

        //prepare sort array for the listing
        $this->prepareAjaxSort($filters, $session_param);

        $this->data['session_param'] = $session_param;
        $this->data['use_ajax'] = $use_ajax;
    }
}

?>
