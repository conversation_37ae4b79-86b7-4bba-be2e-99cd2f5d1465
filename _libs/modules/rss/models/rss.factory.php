<?php

/**
 * Rss model class
 */
Class Rss extends Model_Factory {

    /**
     * Prepares list of items for the RSS feed
     *
     * @param object $registry - the main registry
     * @return array - list of items
     */
    public static function getItems(&$registry) {
        $items = array();

        //get all the emails no matter if it is sent or unsent
        //ToDo: when adding items that are not emails expand the guid construction (announcement<ID>, reminder<ID>)
        $query = 'SELECT CONCAT("email", id) as guid, subject, uncompress(body) as body, sent as date, model, model_id ' . "\n" .
                 'FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                 'WHERE recipient="' . General::slashesEscape($registry['currentUser']->get('email')) . '"' . "\n" .
                 'ORDER BY id DESC LIMIT 0,50';
        $items = $registry['db']->GetAll($query);

        if ($items) {
            foreach($items as $idx => $item) {
                //set link to the model (if any)
                if (!empty($item['model']) && !empty($item['model_id'])) {
                    if ($item['model'] == 'Comment') {
                        //get the parent model of the comment
                        $query = 'SELECT model, model_id FROM ' . DB_TABLE_COMMENTS . ' WHERE id=' . $item['model_id'];
                        $data = $registry['db']->GetRow($query);
                        $items[$idx]['link'] = PH_SERVER_BASE . $_SERVER['PHP_SELF'] . General::model2module($data['model'], true) . 'communications&communications=' . $item['model_id'] . '&communication_type=comments';
                    } else {
                        $items[$idx]['link'] = PH_SERVER_BASE . $_SERVER['PHP_SELF'] . General::model2module($item['model'], true) . 'view&view=' . $item['model_id'];
                    }
                }
            }
        }

        return $items;
    }

}

?>
