<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}

<form name="layouts" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$layout->get('model_lang')|default:$lang}" />
<input type="hidden" name="model" id="model" value="{$layout->get('model')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$layout->get('name')|escape}" title="{#layouts_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_model"><label for="model"{if $messages->getErrors('model')} class="error"{/if}>{help label='model'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {capture assign='model_label'}menu_{$layout->get('model')|mb_lower}s{if $layout->get('model') eq 'User'}_mynzoom{/if}{/capture}
            {$smarty.config.$model_label|escape}
          </td>
        </tr>
        {include file="input_dropdown.html"
                  standalone=false
                  really_required=true
                  required=true
                  name='model_type'
                  label=#layouts_model_type#
                  value=$layout->get('model_type')
                  options=$types_options
                  onchange='changePermissionsByAssignmentTypes(this);'
        }
        <tr>
          <td class="labelbox"><a name="error_place"><label for="place"{if $messages->getErrors('place')} class="error"{/if}>{help label='place'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox small hright" name="place" id="place" value="{$layout->get('place')|escape}" title="{#layouts_place#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />{if $help_layouts_place} <img src="{$theme->imagesUrl}warning.png" alt="" title="" class="t_info_image" {help text_content=$help_layouts_place label_content=#system_info#|escape popup_only=1} />{/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#layouts_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$layout->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_visible"><label for="visible"{if $messages->getErrors('visible')} class="error"{/if}>{help label='visible'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="checkbox" name="visible" id="visible" value="1"{if $layout->get('visible')}checked="checked"{/if} title="{#layouts_visible#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
{include file=`$templatesDir`_groups.html mode='view'}
{include file=`$templatesDir`_groups.html mode='edit'}
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$layout exclude=groups}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
