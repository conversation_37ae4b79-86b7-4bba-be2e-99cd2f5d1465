<h1>{$title}</h1>

<script type='text/javascript'>
var fnd = '';
var res = ''
var no_found = '{#no_files_found#|escape}';

{literal}
function findNodes(k){

    if (fnd == document.getElementById('f_name').value && k == 1) {
        var index = Zapatec.Tree.Utils.getNodeIndex(Zapatec.Tree.all['tree_treefiles'].prevSelected);
        if (index != null) {
            k = Zapatec.Tree.all['tree_treefiles'].prevSelected.id;
        }
    }

    if (index == null || fnd != document.getElementById('f_name').value ) {
        index = Zapatec.Tree.Utils.getNodeIndex(Zapatec.Tree.all['tree_treefiles'].getNode(k));
    }
    
    var nextNode = null;

    if(
        Zapatec.Tree.all['tree_treefiles'].getNode(k).hasSubtree() && 
        Zapatec.Tree.all['tree_treefiles'].getNode(k).data.isExpanded &&
        Zapatec.Tree.all['tree_treefiles'].getNode(k).children.length > 0
    ){
        // if node has expanded subtree - choose first node
        nextNode = Zapatec.Tree.all['tree_treefiles'].getNode(k).children[0]; 
    } else if(index < Zapatec.Tree.all['tree_treefiles'].getNode(k).config.parentNode.children.length - 1){
        //if there is other child nodes on same level - choose next node
        nextNode = Zapatec.Tree.all['tree_treefiles'].getNode(k).config.parentNode.children[index + 1];
    } else if(!Zapatec.Tree.all['tree_treefiles'].getNode(k).config.parentNode.config.isRootNode){
        nextNode = Zapatec.Tree.all['tree_treefiles'].getNode(k).config.parentNode;
        index = Zapatec.Tree.Utils.getNodeIndex(nextNode);

        while(index == nextNode.config.parentNode.children.length - 1){
            nextNode = nextNode.config.parentNode;

            if(nextNode.config.isRootNode){
                if (fnd != document.getElementById('f_name').value) {
                    alert(no_found);
                    return;
                } else {
                    fnd = '';
                    findNodes(1);
                    return;
                }

            }

            index = Zapatec.Tree.Utils.getNodeIndex(nextNode);
        }

        nextNode = nextNode.config.parentNode.children[index+1];
    }

    if(nextNode){
        str = nextNode.data.label.replace(/(<([^>]+)>)/ig,"");
        reg =  new RegExp($('f_name').value,'i');
        if (str.match(reg)) {
            nextNode.sync();
            fnd = document.getElementById('f_name').value;
        } else {
            findNodes(nextNode.id);
        }
    }
}
{/literal}
var func_treefiles = function() {ldelim}initTree('treefiles', 7){rdelim}
Event.observe(window, 'load', func_treefiles);
</script>

{include file=`$theme->templatesDir`actions_box.html}
<br />
<table border="0" cellpadding="0" cellspacing="0">
<tr>
<td>
<div id="tree_treefiles_container" class="tree_container">
<a href="javascript:Zapatec.Tree.all['tree_treefiles'].collapseAll()">{#collapse_all#|escape}</a> |
<a href="javascript:Zapatec.Tree.all['tree_treefiles'].expandAll()">{#expand_next#|escape}</a>
<ul id="tree_treefiles">
    <li class="zpLoadJSON={$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=calculate&amp;get_node=files">{#files#|escape}</li>
</ul>
</div>
</td>
<td valign="top">
<form onsubmit="findNodes(1);return false" action="">
<input name="f_name" id="f_name" class="txtbox" />
<button type="button"  class="button" id="search" name="search" onclick="findNodes(1)">{#search#|escape}</button>
</form>
</td>
</tr>
</table>
