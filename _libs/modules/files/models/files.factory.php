<?php

require_once 'files.model.php';

/**
 * Files model class
 */
class Files extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'File';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * The prefix of imported files
     */
    public static $importedFilePrefix = 'Imported_';

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = 'ORDER BY ' . implode(', ', $filters['sort']);
        } else {
            $sort = 'ORDER BY f.id ASC';
        }

        if (!empty($filters['archive'])) {
            $table = DB_TABLE_ARCHIVE_FILES;
            $table_i18n = DB_TABLE_ARCHIVE_FILES_I18N;
        } else {
            $table = DB_TABLE_FILES;
            $table_i18n = DB_TABLE_FILES_I18N;
        }

        $sql = array();
        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         (!empty($filters['archive']) ? $filters['archive'] : 0) . ' AS archived_by,' . "\n" .
                         '  "' . $model_lang . '" as model_lang, pi18n.name as pattern_name,' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM `' . $table . '` AS f' . "\n" .
                       'LEFT JOIN `' . $table_i18n . '` AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to file to the pattern to fetch pattern name
                       'LEFT JOIN ' . DB_TABLE_PATTERNS_I18N . ' AS pi18n' . "\n" .
                       '  ON (f.pattern_id=pi18n.parent_id AND pi18n.lang="' . $lang . '")' . "\n" .
                        //relate to file to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (f.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to file to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (f.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to file to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (f.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        $sql['where'] = $where;

        //order by clause
        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        //d($query);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(f.id) AS total';
                $sql['from1'] = '';
                $sql['limit'] = '';
                $sql['group_by'] = '';
                $sql['order'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return string $where - the prepared where clause
     */
    public static function constructWhere(&$registry, $filters = array()) {
        $where = array();
        $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {
                //search in all fields
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                $key_where = array();
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                    if (preg_match('#ui18n(\d)?.firstname#', $var)) {
                        $var = preg_replace('#firstname#', 'lastname', $var);
                        $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                    }
                }
                $where[] = '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            $current_user_id = ($registry['currentUser']) ? $registry['currentUser']->get('id') : '';
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#f.deleted#', $where)) {
            $where .= ' AND f.deleted = 0';
        }

        //set permissions
        $where .= self::getAdditionalWhere($registry);

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Prepare the specific destination and the file name for Document
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $document  - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    public static function defineDocumentFilePath ($file, $params, $document) {
        //the destination file name is constructed of:
        //1. formatted full_num (the full number of the document)
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%s_%02d_%s',
            str_replace('/', '.', $document->get('full_num')), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. documents folder
        //4. direction from model type
        //5. year from model added date
        //6. folder types
        //7. the id of the type (formatted with 3 digits: 00X)
        switch ($document->get('direction')) {
        case PH_DOCUMENTS_INCOMING:
            $direction = 'incoming';
            break;
        case PH_DOCUMENTS_OUTGOING:
            $direction = 'outgoing';
            break;
        case PH_DOCUMENTS_INTERNAL:
            $direction = 'internal';
            break;
        }
        $sub_folder = 'documents';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $document->get('added'));
        $destination = sprintf('%s%s/%s/%s/attachments/%03d/', PH_FILES_DIR, $sub_folder, $direction, $year, $document->get('type'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Contracts
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $contract  - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function defineContractFilePath ($file, $params, $contract) {
        //the destination file name is constructed of:
        //1. id of the contract
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $contract->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. contracts folder
        //4. year from model added date
        //5. folder types
        //6. the id of the type (formatted with 3 digits: 00X)
        $sub_folder = 'contracts';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $contract->get('added'));
        $destination = sprintf('%s%s/%s/attachments/%03d/', PH_FILES_DIR, $sub_folder, $year, $contract->get('type'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Finance
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $document  - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function defineFinanceFilePath ($file, $params, $document) {
        //the destination file name is constructed of:
        //1. id of the finance document
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $document->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. finance folder
        //4. sub-module (controller) folder
        //5. year from model added date
        //6. folder types
        //7. the id of the model

        //the dirs do not use the format of the document num,
        //because it can be changed in the configuration file
        $sub_folder = General::singular2plural(strtolower(preg_replace('#finance_#i', '', $document->modelName)));
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $document->get('added'));
        $destination = sprintf('%s%s/%s/%s/%s/%s/', PH_FILES_DIR, 'finance', $sub_folder, 'attachments', $year, $document->get('id'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Nomenclature
     *
     * @param array $file          - as passed into the $_FILES array
     * @param array $params        - parameters like title, description, permission, etc.
     * @param object $nomenclature - model where the file will be attached
     * @return array $file_path    - array holding the destination path and the new filename
     */
    private static function defineNomenclatureFilePath ($file, $params, $nomenclature) {
        //the destination file name is constructed of:
        //1. code
        //2. id
        //3. the revision of the file (formatted with two digits: 0X)
        //4. original file name
        $new_filename = sprintf('%s_%06d_%02d_%s',
            str_replace('/', '.', $nomenclature->get('code')), $nomenclature->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. nomenclatures folder
        //4. folder types
        //5. the id of the type (formatted with 3 digits: 00X)
        $sub_folder = 'nomenclatures';
        $destination = sprintf('%s%s/attachments/%03d/', PH_FILES_DIR, $sub_folder, $nomenclature->get('type'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Task
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $task      - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    public static function defineTaskFilePath ($file, $params, $task) {
        //the destination file name is constructed of:
        //1. formatted full_num (the full number of the task)
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%s_%02d_%s',
            str_replace('/', '.', $task->get('full_num')), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. tasks folder
        //4. year from model added date
        //5. folder types
        //6. the id of the type (formatted with 3 digits: 00X)
        $sub_folder = 'tasks';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $task->get('added'));
        $destination = sprintf('%s%s/%s/attachments/%03d/', PH_FILES_DIR, $sub_folder, $year, $task->get('type'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Finance_Transfer
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $transfer  - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function defineTransferFilePath ($file, $params, $transfer) {
        //the destination file name is constructed of:
        //1. id of the transfer
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $transfer->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. transfers folder
        //4. year from model added date
        //5. folder types
        $sub_folder = 'transfers';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $transfer->get('added'));
        $destination = sprintf('%s%s/%s/attachments/', PH_FILES_DIR, $sub_folder, $year);

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Customer
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $customer  - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function defineCustomerFilePath ($file, $params, $customer) {
        //the destination file name is constructed of:
        //1. id of the customer
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $customer->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. customers folder
        //4. current year
        //5. the id of the customer (formatted with 6 digits: 00000X)
        $sub_folder = 'customers';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $customer->get('added'));
        $destination = sprintf('%s%s/%s/attachments/%06d/', PH_FILES_DIR, $sub_folder, $year, $customer->get('id'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Event
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $event     - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function defineEventFilePath ($file, $params, $event) {
        //the destination file name is constructed of:
        //1. id of the event
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $event->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. events folder
        //4. year from model added date
        //5. the id of the event (formatted with 6 digits: 00000X)
        $sub_folder = 'events';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $event->get('added'));
        $destination = sprintf('%s%s/%s/attachments/%06d/', PH_FILES_DIR, $sub_folder, $year, $event->get('id'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Project
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $project  - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function defineProjectFilePath ($file, $params, $project) {
        //the destination file name is constructed of:
        //1. id of the project
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $project->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. projects folder
        //4. year from model added date
        //5. the id of the project (formatted with 6 digits: 00000X)
        $sub_folder = 'projects';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $project->get('added'));
        $destination = sprintf('%s%s/%s/attachments/%06d/', PH_FILES_DIR, $sub_folder, $year, $project->get('id'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Announcement
     *
     * @param array $file          - as passed into the $_FILES array
     * @param array $params        - parameters like title, description, permission, etc.
     * @param object $announcement - model where the file will be attached
     * @return array $file_path    - array holding the destination path and the new filename
     */
    private static function defineAnnouncementFilePath ($file, $params, $announcement) {
        //the destination file name is constructed of:
        //1. id of the announcement
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $announcement->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. announcements folder
        //4. year from model added date
        //5. folder types
        $sub_folder = 'announcements';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $announcement->get('added'));
        $destination = sprintf('%s%s/%s/attachments/', PH_FILES_DIR, $sub_folder, $year);

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Emails_Campaign
     *
     * @param array $file             - as passed into the $_FILES array
     * @param array $params           - parameters like title, description, permission, etc.
     * @param object $emails_campaign - model where the file will be attached
     * @return array $file_path       - array holding the destination path and the new filename
     */
    private static function defineEmailsCampaignFilePath ($file, $params, $emails_campaign) {
        //the destination file name is constructed of:
        //1. id of the email campaign
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $emails_campaign->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. emails_campaigns folder
        //4. year from model added date
        //5. folder types
        //6. the id of the emails campaign (formatted with 6 digits: 00X)
        $sub_folder = 'emails_campaigns';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $emails_campaign->get('added'));
        $destination = sprintf('%s%s/%s/attachments/%06d/', PH_FILES_DIR, $sub_folder, $year, $emails_campaign->get('id'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for Pattern
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $pattern   - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function definePatternFilePath ($file, $params, $pattern) {
        //the destination file name is constructed of:
        //1. id of the pattern
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $pattern->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. patterns folder
        //4. year from model added date
        //5. the id of the pattern (formatted with 6 digits: 00000X)
        $sub_folder = 'patterns';
        $year = preg_replace('#^([0-9]{4}).*#', '\\1', $pattern->get('added'));
        $destination = sprintf('%s%s/%s/attachments/%06d/', PH_FILES_DIR, $sub_folder, $year, $pattern->get('id'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Prepare the specific destination and the file name for import logs
     *
     * @param array $file       - as passed into the $_FILES array
     * @param array $params     - parameters like title, description, permission, etc.
     * @param object $importlog   - model where the file will be attached
     * @return array $file_path - array holding the destination path and the new filename
     */
    private static function defineImportLogFilePath ($file, $params, $importlog) {
        //the destination file name is constructed of:
        //1. id of the pattern
        //2. the revision of the file (formatted with two digits: 0X)
        //3. original file name
        $new_filename = sprintf('%06d_%02d_%s', $importlog->get('id'), $params['revision'], $file['name']);

        //the destination folder name is constructed of:
        //1. resources folder
        //2. files folder
        //3. import logs folder
        //4. year from model added date
        //5. the id of the pattern (formatted with 6 digits: 00000X)
        $sub_folder = 'importlogs';
        $destination = sprintf('%s%s/%s/', PH_FILES_DIR, $sub_folder, General::strftime('%Y'));

        // Set the result
        $file_path = array();
        $file_path['destination']  = $destination;
        $file_path['new_filename'] = $new_filename;

        return $file_path;
    }

    /**
     * Attaches file to a model
     *
     * @param object $registry    - the main registry
     * @param array $file         - as passed into the $_FILES array
     * @param array $params       - parameters like title, description, permission, etc.
     * @param object $model       - model to attach the file to
     * @param array $restrictions - restrictions to be checked when the file is uploaded
     * @return bool|int           - id of file on success or false on failure
     */
    public static function attachFile(&$registry, $file, $params, $model, $restrictions = array()) {
        // Get the database object
        $db = &$registry['db'];

        // Prepare a flag for the result of the operations
        $result = false;

        // Prepare the flag to check if the file is successfully uploaded
        $file_uploaded = false;

        // Set the model name
        $params['model'] = $model->modelName;

        // if model was just added
        if (!$model->isDefined('added')) {
            $model->set('added', date('Y-m-d H:i:s'), true);
        }

        // If there is a file
        if (!empty($file)) {
            // Get the current count of all errors
            $errors_count_before = count($registry['messages']->getErrors());

            // Remove all special chars from the file name
            $error_empty_name = ($file['name'] == $params['name'] ? false : true);
            $file['name']     = $file['name'];
            $params['name']   = $params['name'];

            // If there are some new errors (coming from the removal of the special chars)
            if (count($registry['messages']->getErrors()) > $errors_count_before) {
                // Return false (i.e. show the errors without uploading this file)
                return false;
            }

            // If there is an id for the file
            if (!empty($params['id'])) {
                $filters = array('where'    => array('f.id = \'' . $params['id'] . '\''),
                                 'sanitize' => true);
                $saved_file = self::searchOne($registry, $filters);

                // Replace original filename to be the same as stored before
                // We need to do this because the revisions of the file should be respected
                if ($saved_file) {
                    $file['name'] = $saved_file->get('filename');
                }
            }

            if (empty($params['revision'])) {
                // Calculate the revision of the file
                $rev_params         = array('model_id' => $model->get('id'),
                                            'filename' => $file['name'],
                                            'origin'   => 'attached');
                $params['revision'] = self::getLatestRevision($registry, $rev_params);

                // If there is an id for the file
                if (!empty($params['id'])) {
                    // This is new revision for the file
                    //   so we need insert, instead of update
                    unset($params['id']);
                }
            }

            // Get the file path method name
            if (preg_match('#transfer#i', $model->modelName)) {
                $file_path_function_name = 'defineTransferFilePath';
            } elseif (preg_match('#finance#i', $model->modelName)) {
                $file_path_function_name = 'defineFinanceFilePath';
            } else {
                $file_path_function_name = 'define' . str_replace('_', '', $model->modelName) . 'FilePath';
            }

            // If such method exists
            $file_path = array();
            if (method_exists(__CLASS__, $file_path_function_name)) {
                // Get the destination and the file name according to the model
                $file_path = self::$file_path_function_name($file, $params, $model);
            }

            // Upload the file
            if (!empty($file_path)) {
                $file_path['new_filename'] = self::composeUniqueFilename($file, $file_path['new_filename']);
                $file_uploaded = FilesLib::uploadFile($file, $file_path['destination'], $file_path['new_filename'], $restrictions);
            }
        }

        // If the file is uploaded then insert it into the db
        if ($file_uploaded) {
            $params['model_id']   = $model->get('id');
            $params['model_lang'] = $model->get('model_lang');
            $params['filename']   = $file['name'];
            $params['path']       = $file_path['destination'] . $file_path['new_filename'];
            $params['origin']     = 'attached';

            $attachment       = new File($registry, $params);
            $attachment_saved = $attachment->save();
            if (!$attachment_saved) {
                //remove the file if the save procedure fails
                //the failure could occur only due to SQL error
                @unlink($file_path['destination'] . $file_path['new_filename']);
            } else {
                $result = true;
            }
        } elseif (!empty($params['id']) && $model->modelName != 'Announcement') {
            if (empty($file)) {
                //remove the revision
                unset($params['revision']);
            }
            $params['model_lang'] = $model->get('model_lang');

            $attachment = new File($registry, $params);
            $attachment_saved = $attachment->save();

            if ($attachment_saved) {
                $result = true;
            }
        }

        if ($result) {
            return $attachment->get('id');
        }
        return false;
    }

    /**
     * Composes unique filename
     *
     * @param array $file - file array as uploaded
     * @param string $filename - filename to give
     * @return string $filename - unique filename with suffix generated as a hash
     */
    public static function composeUniqueFilename($file, $filename) {
        // calculate hash of the original name;
        $filenameHash = substr(sha1($file['name']), 0, 8);
        //get the file extension from the original filename
        // because the original might have no extension
        // and the designated new filename might contain dot (.) in the filename
        $fileExt = pathinfo($file['name'], PATHINFO_EXTENSION);
        //remove special characters to save it in any file system
        $filename = FilesLib::removeSpecialChars($filename);
        // add hash of the original name as a suffix because
        // removing special chars might coincide with already exising filename
        $filename = ($fileExt) ?
            pathinfo($filename, PATHINFO_FILENAME) . '_' . $filenameHash . '.' . $fileExt :
            $filename . '_' . $filenameHash;

        return $filename;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_FILES);

        if (!$deleted) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_FILES);

        if (!$restored) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models. Deletion is real.
     * ATTENTION: deletion has no restore
     * IMPORTANT: files are deleted from server as well
     *
     * @param Registry $registry - the main registry
     * @param array|int $ids - ids of the models to be deleted
     * @param bool $archive - flag if files are archived or not
     * @return bool - result of the operations
     */
    public static function purge(&$registry, $ids, $archive = false) {

        if (!$ids) {
            return true;
        }
        if (!is_array($ids)) {
            $ids = array($ids);
        }

        $db = $registry['db'];
        $table = $archive ? DB_TABLE_ARCHIVE_FILES : DB_TABLE_FILES;
        $table_i18n = $archive ? DB_TABLE_ARCHIVE_FILES_I18N : DB_TABLE_FILES_I18N;

        // get file path data in order to delete files from server
        $file_data = $db->GetAssoc("
            SELECT id, path
            FROM $table
            WHERE id IN ('" . implode("', '", $ids) . "')
        ");

        //start transaction
        $db->StartTrans();

        //multiple purge is part of the transaction
        $purged = self::purgeMultiple($registry, $ids, $table);

        if (!$purged) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records
        $purged = self::purgeMultiple($registry, $ids, $table_i18n, 'parent_id');

        if (!$purged) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        if (!$dbTransError) {
            // delete files
            foreach ($file_data as $filename) {
                if (file_exists($filename)) {
                    @unlink($filename);
                }
            }
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Calculates the latest revision of a file
     *
     * @param object $registry - the main registry
     * @param array $params - array of params to search by
     * @return int - latest revision of a file
     */
    public static function getLatestRevision(&$registry, $params) {
        $where = array();

        if (isset($params['model'])) {
            $where['model'] = sprintf('model="%s"', $params['model']);
        }
        if (isset($params['model_id'])) {
            $where['model_id'] = sprintf('model_id="%s"', $params['model_id']);
        }
        if (isset($params['pattern_id'])) {
            $where['pattern_id'] = sprintf('pattern_id=%d', $params['pattern_id']);
        }
        if (isset($params['origin'])) {
            $where['origin'] = sprintf('origin="%s"', $params['origin']);
        }
        if (isset($params['filename'])) {
            $where['filename'] = sprintf('filename="%s"', $params['filename']);
        }

        //calculate the revision of the file
        $query = 'SELECT max(revision) AS revision FROM ' . DB_TABLE_FILES .
                 ' WHERE ' . implode(' AND ', $where);
        $revision = $registry['db']->GetOne($query);

        if (!$revision) {
            $revision = 1;
        } else{
            $revision++;
        }

        return $revision;
    }

    /**
     * Get ids of all users that are members of current user's groups (without root group)
     *
     * @param object $registry - the main registry
     * @return array - all users found
     */
    public static function getGroupsUsers(&$registry) {
        if (!$registry['currentUser']) {
            return array();
        }

        if ($registry['currentUser']->isDefined('groups_users')) {
            return $registry['currentUser']->get('groups_users');
        }

        //take all groups of current user from their 'groups' property
        $parent_ids = $registry['currentUser']->get('groups') ?: array();

        $users = array();
        if (count($parent_ids)) {
            $sql = array();
            //select clause
            $sql['select'] = 'SELECT DISTINCT(u.id) ';

            //from clause
            $sql['from'] = 'FROM ' . DB_TABLE_USERS_GROUPS . ' AS ug ' . "\n" .
                           'INNER JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                           '  ON (u.id=ug.parent_id)';

            $sql['where'] = 'WHERE ' . General::buildClause('ug.group_id', $parent_ids) . "\n";
            // exclude "All" group but make sure that current user is always in the array with results
            $sql['where'] .= ' AND (ug.group_id != ' . PH_ROOT_GROUP . ' OR u.id = ' . $registry['currentUser']->get('id') . ')' . "\n";

            $query = implode("\n", $sql);
            //d($query);
            $users = $registry['db']->GetCol($query);
        }

        $registry['currentUser']->set('groups_users', $users, true);

        return $users;
    }

    /**
     * Additional condition to search files by their access permissions
     *
     * @param object $registry - the main registry
     * @return string - additional search clause
     */
    public static function getAdditionalWhere(&$registry) {
        $additional_where = '';

        if ($registry['currentUser']) {

            // do not check permissions for automaiton user
            if ($registry['currentUser']->get('id') == PH_AUTOMATION_USER) {
                return $additional_where;
            }

            //set permissions
            $group_users = self::getGroupsUsers($registry);
            $additional_where .= " AND ((f.permission = 'mine' AND f.added_by = " . $registry['currentUser']->get('id') .
                                 ") OR (f.permission = 'all') " . ($group_users ?
                                 "OR (f.permission = 'group' AND f.added_by IN (" . implode(',', $group_users) . ")))" : ')');
        }

        return $additional_where;
    }
}

?>
