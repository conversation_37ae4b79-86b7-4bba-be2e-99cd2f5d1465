<?php

use Nzoom\I18n\I18nService;

/**
 * Custom export of invoices and their products to Business Navigator
 */
Class Custom_Export extends Model_Factory
{
    public static $modelName = 'Export';
    private static string $type = 'termo_visits_photo';
    public static array $settings = [];
    public static Registry $registry;
    private static bool $dbTranslationsLoaded = false;

    /*
     * Translate language param
     */
    public static function i18n($param)
    {
        if (!self::$dbTranslationsLoaded) {
            $i18nService = I18nService::getInstance(self::$registry);
            $i18nList = $i18nService->getListArray(self::$registry['lang'], 'exports', [self::$type]);
            self::$registry['translater']->loadData($i18nList);
        }
        return self::$registry['translater']->translate($param);
    }

    /**
     * Export entry point
     *
     * @todo Organize better, at least in separate methods.
     */
    public static function export(Registry $registry)
    {
        $format = '%s_%s';
        self::$registry = $registry;
        $settings = self::$settings;
        $protocolIds = $registry['request']->get('items');
        $protocols = self::getProtocols($protocolIds);
        // Show error, if none of the requested invoices are found
        if (empty($protocols)) {
            return ['error' => [self::i18n('no_protocols_found_for_export')]];
        }


        $zipFile = [];
        $updatedProtocols = [];
        foreach ($protocols as  $protocol) {
            $groupingFiles = [];
            $files = $protocol->getFiles();
            if (empty($files['attachments'])) {
                continue;
            }
            $updatedProtocols[] = $protocol->get('id');
            $assoc = $protocol->getAssocVars();
            $key = $protocol->get('id') . "-" . $protocol->get('customer');
            if (!array_key_exists($key, $zipFile)) {
                $zipFile[$key] = [
                    'name' => $protocol->get('customer_name'),
                    'folders' => [],
                ];
            }

            $key2 = sprintf($format, $assoc['client_name']['value'], str_replace('/', '-', $assoc['contract_num']['value']));
            $zipFile[$key]['folders'][$key2] = [
                'name' => $key2,
                'folders' => [],
                'files' => []
            ];
            $temp = &$zipFile[$key]['folders'][$key2];
            $fileVars = [
                'photo_num_external',
                'photo_view_external',
                'photo_num_internal',
                'photo_view_internal',
                'photo_guarantee',
            ];
            foreach ($assoc['kind_clima']['value'] as $row => $notImportant) {
                $temp['folders'][$row] = [
                    'name' => sprintf('Помещение %s', $row),
                    'files' => []
                ];
                foreach ($fileVars as $fileVar) {
                    $val = $assoc[$fileVar]['value'][$row];
                    if (empty($val)) {
                        continue;
                    }

                    if (!empty($val->properties['not_exists'])) {
                        continue;
                    }
                    $temp['folders'][$row]['files'][$assoc[$fileVar]['label']] = $val->properties;
                    $groupingFiles[] = $val->properties['id'];
                }
            }
            foreach ($files['attachments'] as $attachment) {
                if (in_array($attachment['id'], $groupingFiles)) {
                    continue;
                }
                $temp['files'][] = $attachment;
            }
        }
        // Fail, if no products found from the invoices
        if (empty($zipFile)) {
            return ['error' => [self::i18n('no_files_found_for_export')]];
        }
        // Prepare zip file name and path.
        $zipFileName = "{$settings['zip_file_name']}.zip";
        $zipFilePath = PH_EXPORTS_CACHE_DIR . $zipFileName;


        // Log which invoices were exported.
        Exports::exportLog(
            $registry,
            [
                'file_name' => $zipFileName,
                'log'       => ['Mounting protocols' => $updatedProtocols],
            ]
        );

        // Prepare export dir.
        if (!is_dir(PH_EXPORTS_CACHE_DIR)) {
            FilesLib::createDir(PH_EXPORTS_CACHE_DIR);
        }

        // Prepare zip file.
        $zip = new ZipArchive;
        $zipOpenResult = $zip->open($zipFilePath, ZipArchive::CREATE);
        if ($zipOpenResult !== true) {
            return ['error' => [self::i18n('error_technical_error_please_contact_nzoom_support')]];
        }
        $createdPaths = [];
        $mainPath = $protocols[0]->get('type_name');
        $zip->addEmptyDir($mainPath);
        foreach ($zipFile as $zips) {
            if (in_array($zips['name'], $createdPaths)) {
                $createdPaths[$zips['name']] += 1;
                $zips['name'] .= " ({$createdPaths[$zips['name']]})";
            } else {
                $createdPaths[$zips['name']] = 1;
            }
            $mainFolder = $mainPath . "/" . $zips['name'];
            $zip->addEmptyDir($mainFolder);
            foreach ($zips['folders'] as $folder) {
                $zip->addEmptyDir($mainFolder . '/' . $folder['name']);
                $subfolderPrefix = $mainFolder . '/' . $folder['name'] . '/';

                foreach ($folder['files'] as $file) {
                    $zip->addFile($file['path'], $subfolderPrefix . $file['name']);
                }
                foreach ($folder['folders'] as $subfolders) {
                    $zip->addEmptyDir($subfolderPrefix . $subfolders['name']);
                    foreach ($subfolders['files'] as $row => $file) {
                        $zip->addFile($file['path'], $subfolderPrefix . $subfolders['name'] . "/" . preg_replace('/(.*)(\.[^.]*)$/', '$1 ('.$row.')$2', $file['name']));
                    }
                }
            }
        }

        // Close the zip file.
        $zip->close();
        // Get the zip file contents
        $zipFileContents = General::fileGetContents($zipFilePath);
        unlink($zipFilePath);

        /*
        * Update Protocols
        */
        require_once(PH_MODULES_DIR . 'documents/models/documents.history.php');
        $registry['db']->StartTrans();
        foreach ($protocols as $protocol) {
            $protocol->getTags();
            $oldProtocol = clone $protocol;
            $protocolTags = $protocol->get('tags');
            $protocolTags[] = $settings['exported_protocol_tag'];
            $protocolTags = array_unique($protocolTags);
            $protocol->set('tags', $protocolTags, true);
            if (!$protocol->addTags([$settings['exported_protocol_tag']])) {
                return ['error' => [self::i18n('error_technical_error_please_contact_nzoom_support')]];
            }
            $protocolNewModel = Documents::searchOne($registry, [
                'where'      => [
                    "d.type = {$protocol->get('type')}",
                    "d.id = {$protocol->get('id')}"
                ],
                'model_lang' => $registry['lang'],
            ]);
            $protocolNewModel->getTags();
            $auditId = Documents_History::saveData(
                self::$registry,
                [
                    'action_type' => 'tag',
                    'old_model'   => $oldProtocol,
                    'model'       => $protocol,
                    'new_model'   => $protocolNewModel,
                ]
            );
            if (!$auditId) {
                return ['error' => [self::i18n('error_technical_error_please_contact_nzoom_support')]];
            }
        }
        $registry['db']->CompleteTrans();
        // Send the export file to the client
        ob_clean();
        header("Content-Disposition: attachment; filename=\"{$zipFileName}\"");
        header('Content-type: application/octet-stream;');
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        print $zipFileContents;
        return [];
    }

    public static function getProtocols($protocolIds)
    {
        $settings = self::$settings;
        $protocolIdList = implode(', ', $protocolIds);
        return Documents::search(
            self::$registry,
            [
                'sanitize' => false,
                'where'    => [
                    'd.type = ' . $settings['modelType'],
                    "d.id IN ({$protocolIdList})",
                    "tags.tag_id != '{$settings['exported_protocol_tag']}'",
                ],
            ]
        );
    }
}
