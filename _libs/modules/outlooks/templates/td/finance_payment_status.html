          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$finance_payment_status|escape|default:'&nbsp;' caption=#finance_payments_status#|escape width=250}
          {/capture}
          
          {if $single->get('status') eq 'added'}
            <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
          {elseif $single->get('status') eq 'finished'}
            <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
          {/if}
          {capture assign='status_param'}finance_payments_status_{$single->get('status')}{/capture}
          <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          </td>
