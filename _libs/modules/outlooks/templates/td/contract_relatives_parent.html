          <td class="t_border nopadding" nowrap="nowrap">
            <table border="0" cellspacing="0" cellpadding="0" width="100%">
              <tr>
                <td style="border: none!important;">
                  <div class="collapsed" id="parents_{$single->get('id')}" style="max-height: 36px; overflow: hidden;">
                    {assign var='parents' value=$single->getFirstLevelRelatedContracts('parent')}
                    {foreach name='pd' from=$parents item='parent'}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$parent.id}" {popup text=$parent.name|escape|default:"&nbsp;" caption=#contracts_relative_contract_name#|escape width=250}>{$parent.name|escape}</a>
                      {if !$smarty.foreach.pd.last}
                        <br />
                      {/if}
                    {foreachelse}
                      &nbsp;
                    {/foreach}
                  </div>
                </td>
                <td class="hright" style="border: none!important;">{if is_array($parents) && $parents|@count gt 3}<img alt="" class="pointer" title="{#expand#|escape}" src="{$theme->imagesUrl}expand1.png" onclick="toggleDocumentsListRelatives(this, 'parents_{$single->get('id')}');" />{else}&nbsp;{/if}
                </td>
              </tr>
            </table>
          </td>
