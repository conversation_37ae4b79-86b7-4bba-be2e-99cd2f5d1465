      {strip}
      {capture assign='info'}
        <strong><u>{$basic_vars_labels.code|default:#nomenclatures_code#|escape}:</u></strong> {$single->get('code')|escape}<br />
        <strong>{$basic_vars_labels.type|default:#nomenclatures_type#|escape}:</strong> {$single->get('type_name')|escape}<br />
        <strong>{$basic_vars_labels.name|default:#nomenclatures_name#|escape}:</strong> {$single->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
        <span class="translations">
        {foreach from=$single->get('translations') item='trans'}
          <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
        {/foreach}
        </span>
      {/capture}
      {/strip}
      {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
