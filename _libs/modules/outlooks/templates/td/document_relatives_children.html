          <td class="t_border nopadding" nowrap="nowrap">
            <table border="0" cellspacing="0" cellpadding="0" width="100%">
              <tr>
                <td style="border: none!important;">
                  <div class="collapsed" id="children_{$single->get('id')}" style="max-height: 36px; overflow: hidden;">
                    {assign var='children' value=$single->getFirstLevelRelatedDocuments('child')}
                    {foreach name='cd' from=$children item='child'}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$child.id}{if $child.archived_by}&amp;archive=1{/if}" {popup text=$child.name|escape|default:"&nbsp;" caption=#documents_relative_document_name#|escape width=250}>{$child.full_num|numerate:$child.direction}</a>
                      {if !$smarty.foreach.cd.last}
                        <br />
                      {/if}
                    {foreachelse}
                      &nbsp;
                    {/foreach}
                  </div>
                </td>
                <td class="hright" style="border: none!important;">{if is_array($children) && $children|@count gt 3}<img alt="" class="pointer" title="{#expand#|escape}" src="{$theme->imagesUrl}expand1.png" onclick="toggleDocumentsListRelatives(this, 'children_{$single->get('id')}');" />{else}&nbsp;{/if}
                </td>
              </tr>
            </table>
          </td>
