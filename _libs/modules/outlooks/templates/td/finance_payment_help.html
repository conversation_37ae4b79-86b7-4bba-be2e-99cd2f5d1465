    {strip}
      {capture assign='info'}
        <strong>{$basic_vars_labels.num|default:#[object_name]_num#|escape}:</strong> {if $single->get('num')}{$single->get('num')|escape}{else}<i>{#finance_payments_unfinished_payment#|escape}</i>{/if}<br />
        <strong>{$basic_vars_labels.type|default:#[object_name]_type#|escape}:</strong> {$single->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {capture assign='finance_payment_status'}
        {if $single->get('status') eq 'added'}
          {#finance_payments_status_added#}
        {elseif $single->get('status') eq 'finished'}
          {#finance_payments_status_finished#}
        {/if}
      {/capture}
    {/strip}
    {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
    {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}