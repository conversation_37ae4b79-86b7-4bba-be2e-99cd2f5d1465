    {strip}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#events_name#|escape}:</strong> {$single->get('name')|escape}<br />
        <strong>{$basic_vars_labels.description|default:#events_description#|escape}:</strong> {$single->get('description')|mb_truncate|escape}<br />
        <strong>{$basic_vars_labels.customer|default:#events_customer#|escape}:</strong> {$single->get('customer_name')|escape}<br />
        <strong>{$basic_vars_labels.location|default:#events_location#|escape}:</strong> {$single->get('location')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$single->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('status_modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {if $single->get('ownership') eq 'other'}
        {capture assign='background_style'}style="background-color: {$calendar_settings.background_color_other}; color: {$calendar_settings.color_other};"{/capture}
      {elseif $single->get('ownership') eq 'mine'}
        {capture assign='mine_background_color'}background_color_{$single->get('type')}{/capture}
        {capture assign='mine_color'}color_{$single->get('type')}{/capture}
        {capture assign='background_style'}style="background-color: {$calendar_settings.$mine_background_color}; color: {$calendar_settings.$mine_color};"{/capture}
      {else}
        {capture assign='background_style'}style="background-color: {$calendar_settings.background_color_none}; color: {$calendar_settings.color_none};"{/capture}
      {/if}
    {/strip}
