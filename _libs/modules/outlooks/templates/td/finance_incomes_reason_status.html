          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$finance_incomes_reason_status|escape|default:'&nbsp;' caption=#help_finance_documents_status#|escape width=250}{if !$single->get('annulled_by') && $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'finance', 'incomes_reasons')" style="cursor:pointer;"{/if}
          {/capture}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              <img src="{$smarty.const.PH_FINANCE_DOCUMENTS_STATUSES_URL}{$single->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              {if $single->get('status') eq 'opened'}
                <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
              {elseif $single->get('status') eq 'locked'}
                <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
              {elseif $single->get('status') eq 'finished'}
                <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
              {/if}
            {/if}
            <span {$popup_and_onclick}>{$single->get('substatus_name')|escape}</span>
          {else}
            {if $single->get('status') eq 'opened'}
              <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $single->get('status') eq 'locked'}
              <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $single->get('status') eq 'finished'}
              <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            {capture assign='status_param'}finance_documents_status_{$single->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
