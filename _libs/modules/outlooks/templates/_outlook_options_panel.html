  <!-- Outlooks Personal Manager -->
  <input type="hidden" name="id" id="id" value="{$outlook->get('id')}" />
  <input type="hidden" name="module_controller" id="module_controller" value="{$outlook->get('module')}|{$outlook->get('controller')}" />
  <input type="hidden" name="model_id" id="model_id" value="{$outlook->get('model_id')}" />
  <input type="hidden" name="model_lang" id="model_lang" value="{$outlook->get('model_lang')|default:$lang}" />
  <input type="hidden" name="section" id="section" value="{$outlook->get('section')}" />
  <input type="hidden" name="assignments_type" id="assignments_type" value="Users" />
  <input type="hidden" name="previous_assignments_type" id="previous_assignments_type" value="{$outlook->get('assignments_type')}" />
  <input type="hidden" name="users[0]" id="users" value="{$currentUser->get('id')}" />
  <ol id="outlooks_all_settings">
    {foreach from=$outlook->get('current_custom_fields') item='fields' name='oo'}
      <li class="sortable">
        <input type="checkbox" id="{$fields.name}|{$fields.model_type}" name="positions[{$fields.name}|{$fields.model_type}]" value="1"{if $fields.position} checked="checked"{/if} /> <label for="{$fields.name}|{$fields.model_type}">{$fields.label}</label>
        <input type="hidden" name="labels[{$fields.name}|{$fields.model_type}]" value="{$fields.label|escape}" />
        <input type="hidden" name="origins[{$fields.name}|{$fields.model_type}]" value="{$fields.origin}" />
        <input type="hidden" name="field_types[{$fields.name}|{$fields.model_type}]" value="{$fields.field_type}" />
        <input type="hidden" name="column_widths[{$fields.name}|{$fields.model_type}]" value="{$fields.column_width}" />
      </li>
    {/foreach}
  </ol>

  <script type="text/javascript">
      Position.includeScrollOffsets = true;
      Sortable.create('outlooks_all_settings', {ldelim}tag: 'LI',
                                                containment: 'outlooks_all_settings',
                                                constraint: 'vertical',
                                                only: 'sortable',
                                                scroll: 'outlook_all_options'
                                               {rdelim});
  </script>
