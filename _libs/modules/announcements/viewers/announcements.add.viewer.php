<?php

class Announcements_Add_Viewer extends Viewer {
    public $template = 'add.html';

    public function prepare() {
        $this->model = $this->registry['announcement'];
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare the wysiwyg editor
        $editor = new Editor($this->registry, 'content_html');

        if ($this->model->get('switch_html') != 'html') {
            //the content didn't contain html tags e.g. is taken from text area
            $editor->setContent(nl2br($this->model->get('content')));
        } else {
            //the content is HTML formatted
            $editor->setContent($this->model->get('content_html'));
            if (!$this->model->get('content')) {
                $content = preg_replace('#\r?\n#', '', $this->model->get('content_html'));
                $content = preg_replace('#\s+#', " ", $content);
                $content = preg_replace('#(<p>)|(<br\s*/?>)|(<li>)|(<h\d>)#', "\n", $content);
                $content = strip_tags($content);
                $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
                $this->model->set('content', $content, true);
            }
        }
        $this->data['editor_content'] = $editor->create();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare types and categories
        require_once(PH_MVC_DIR . 'dropdown.class.php');
        $params = array(0 => $this->registry,
                        'table' => 'DB_TABLE_ANNOUNCEMENTS_TYPES',
                        'table_i18n' => 'DB_TABLE_ANNOUNCEMENTS_TYPES_I18N',
                        'label' => 'ti18n.name',
                        'value' => 't.id',
                        'class_name' => 'IF(t.date_period = \'MONTH\', CURDATE() + INTERVAL t.default_validity MONTH, CURDATE() + INTERVAL IF(t.date_period = \'WEEK\', 7, 1) * t.default_validity DAY)',
                        'where' => 't.active=1 AND t.deleted=0');
        if ($this->registry['currentUser']->get('role') == PH_ROLES_ADMIN) {
            $params['get_system'] = 1;
        }
        $types_options = Dropdown::getCustomDropdown($params);
        $user_permissions = $this->registry['currentUser']->getRights();
        foreach ($types_options as $key => $options) {
            if (isset($user_permissions[$this->module.$options['option_value']]) && $user_permissions[$this->module.$options['option_value']]['add'] == 'none') {
                unset($types_options[$key]);
            }
        }
        $types_options = array_values($types_options);
        $this->data['types'] = $types_options;
        if ($types_options && !$this->model->get('type')) {
            $this->model->set('validity_term', $types_options[0]['class_name'], true);
        }
        $params = array(0 => $this->registry,
                        'table' => 'DB_TABLE_ANNOUNCEMENTS_CATEGORIES',
                        'table_i18n' => 'DB_TABLE_ANNOUNCEMENTS_CATEGORIES_I18N',
                        'label' => 'ti18n.name',
                        'value' => 't.id');
        $this->data['categories'] = Dropdown::getCustomDropdown($params);

        //prepare assignments array
        $assignments_type = $this->model->get('assignments_type');

        if (!$this->registry['request']->isPost()) {
            $departments_assignments = @array_keys($this->model->get('departments_assignments'));
            $users_assignments = @array_keys($this->model->get('users_assignments'));
        } else {
            $departments_assignments = $this->model->get('departments_assignments');
            $users_assignments = $this->model->get('users_assignments');
        }

        //prepare users array
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->model->get('lang'),
                         'where' => array('u.hidden = 0',
                                          'u.active = 1'),
                         'sort' => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'));
        $users_obj = Users::search($this->registry, $filters);

        $users_optgroups = array('normal_users' => array(), 'portal_users' => array());
        $m_value = array();
        foreach ($users_obj as $obj) {
            if (is_array($users_assignments) && in_array($obj->get('id'),$users_assignments)) {
                $m_value[] = $obj->get('id');
            }

            $optgroup_label = ($obj->get('is_portal')) ? 'portal_users' : 'normal_users';
            $users_optgroups[$optgroup_label][] = array(
                    'label'        => $obj->get('firstname') . ' ' . $obj->get('lastname'),
                    'option_value' => $obj->get('id'),
                    'value'        => (is_array($users_assignments) && in_array($obj->get('id'),$users_assignments)) ? $obj->get('id') : '');
        }

        if (!empty($users_optgroups['normal_users'])) {
            usort($users_optgroups['normal_users'], array('parent', 'sortAssignments'));
        }
        if (!empty($users_optgroups['portal_users'])) {
            usort($users_optgroups['portal_users'], array('parent', 'sortAssignments'));
        }
        $users = array (
            'name'     => 'users_assignments',
            'type'     => 'checkbox_group',
            'label'    => $this->i18n('announcements_assignments'),
            'help'     => $this->i18n('help_announcements_assignments'),
            'optgroups'  => $users_optgroups,
            'controll_all' => true,
            'value'    => $m_value);
        $this->data['users'] = $users;

        //prepare departments array
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $filters = array('model_lang'=>$this->model->get('lang'));
        $departments_obj = Departments::getTree($this->registry,$filters);

        $departments_options = array();
        $m_value = array();
        foreach ($departments_obj as $obj) {
            $departments_options[] = array(
                'level'        => $obj->get('level'),
                'label'        => $obj->get('name'),
                'option_value' => $obj->get('id'),
                'active'       => $obj->isActivated(),
                'deleted'      => $obj->isDeleted(),
                'value'        => (isset($departments_assignments[$obj->get('id')])?$obj->get('id'):''));
            if (@$assignments_type == 'Departments' && is_array($departments_assignments) && in_array($obj->get('id'),$departments_assignments)) {
                $m_value[] = $obj->get('id');
            }
        }
        $departments = array (
            'name'     => 'departments_assignments',
            'type'     => 'checkbox_tree',
            'label'    => $this->i18n('announcements_assignments'),
            'help'     => $this->i18n('announcements_assignments'),
            'options'  => $departments_options,
            'controll_all' => true,
            'value'    => $m_value);
        $this->data['departments_assignments'] = $departments;
		$this->model->set('departments_assignments', $departments);
		$this->model->set('users_assignments', $users);
        @$this->data['assignments_type'] = $assignments_type;
        $this->registry['include_tree'] = true;

        //prepare priorities
        require_once (PH_MODULES_DIR . 'announcements/models/announcements.dropdown.php');
        $this->data['priorities'] = Announcements_Dropdown::getPriorities(array($this->registry));

        $this->data['commercial_announcements_off'] = !$this->registry['config']->getParam('announcements', 'display_commercial_announcements');
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('announcements_add'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
