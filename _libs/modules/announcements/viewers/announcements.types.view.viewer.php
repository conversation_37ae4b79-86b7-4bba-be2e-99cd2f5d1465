<?php

class Announcements_Types_View_Viewer extends Viewer {
    public $template = 'types_view.html';

    public function prepare() {
        $this->model = $this->registry['announcements_type'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare counters
        if ($this->model->get('counter')) {
            require_once $this->modelsDir . 'announcements.counters.factory.php';
            $filters = array('model_lang'   => $this->model->get('model_lang'),
                             'where'        => array('ac.id = ' . $this->model->get('counter')),
                             'sanitize'     => true
                            );
            $counter = Announcements_Counters::searchOne($this->registry, $filters);

            if ($counter) {
                $this->data['counter_formula'] = $counter->get('formula');
                $this->data['counter_name'] = $counter->get('name');
            }
        }

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array('g.id = ' . $this->model->get('group')));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('announcements_types_view');
    }
}

?>
