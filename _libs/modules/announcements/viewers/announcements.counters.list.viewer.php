<?php

class Announcements_Counters_List_Viewer extends Viewer {
    public $template = 'counters_list.html';
    public $filters = array();

    /**
     * Sortable columns in the list view
     */
    public $sortables = array('name', 'formula', 'description', 'next_number', 'count_announcements');

    public function prepare() {
        require_once $this->modelsDir . 'announcements.counters.factory.php';

        $filters = Announcements_Counters::saveSearchParams($this->registry);

        list($announcements_counters, $pagination) = 
            Announcements_Counters::pagedSearch($this->registry, $filters);
        $this->data['announcements_counters'] = $announcements_counters;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('announcements');
        $href = sprintf('%s=%s', 
                            $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('announcements_counters');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
