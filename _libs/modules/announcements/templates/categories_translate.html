<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="documents" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$announcements_category->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$announcements_category->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="vtop">
      <input type="hidden" name="icon_name" value="{$announcements_category->get('icon_name')}" />
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td colspan="3">
            {#message_translatable_items#|escape}
          </td>
          <td class="vtop t_border divider_cell" rowspan="10">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="2">&nbsp;</td>
          {capture assign='source_lang'}lang_{$announcements_category->get('model_lang')}{/capture}
          <td><img src="{$theme->imagesUrl}flags/{$announcements_category->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
          <td>&nbsp;</td>
          {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
          <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='sections_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="name" id="name" value="{$announcements_category->get('name')|escape}" title="{#announcements_categories_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{#announcements_categories_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='sections_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox distinctive" name="description" id="description" title="{#announcements_categories_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$announcements_category->get('description')}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_description" id="copy_description" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_description" id="bm_description" title="{#announcements_categories_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('description')}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_position"><label for="position"{if $messages->getErrors('position')} class="error"{/if}>{help label='sections_position'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="position" id="position" value="{$announcements_category->get('position')|escape}" title="{#announcements_categories_position#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        {if $announcements_category->get('icon_name')}
          <tr>
            <td class="labelbox"><label for="sections_icon">{help label='sections_icon'}</label></td>
            <td>&nbsp;</td>
            <td nowrap="nowrap">
              <img src="{$smarty.const.PH_ANNOUNCEMENTS_CATEGORIES_URL}{$announcements_category->get('icon_name')}" alt="" />
            </td>
            <td colspan="3">&nbsp;</td>
          </tr>
        {/if}
        {if $announcements_category->get('icon_name')}
          <tr>
            <td class="labelbox"><a name="error_icon_delete"><label for="icon_delete"{if $messages->getErrors('icon_delete')} class="error"{/if}>{help label='sections_icon_delete'}</label></a></td>
            <td>&nbsp;</td>
            <td>
              <input type="checkbox" name="icon_delete" id="icon_delete" title="{#announcements_categories_icon_delete#|escape}" />
            </td>
            <td colspan="3">&nbsp;</td>
          </tr>
        {/if}
        <tr>
          <td class="labelbox"><a name="error_icon"><label for="icon_file"{if $messages->getErrors('icon')} class="error"{/if}>{help label='sections_icon_file'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="file" name="icon_file" id="icon_file" class="filebox" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#translate#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
         <td>&nbsp;</td>
          <td colspan="2">
            <button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$announcements_category}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
