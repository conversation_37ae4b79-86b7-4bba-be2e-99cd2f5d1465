<?php

class Announcements_Types_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Announcements_Type';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Announcements_Types';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'translate'
    );

    /**
     * generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'search':
            $this->_search();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        // Prepare to work with counters
        require_once PH_MODULES_DIR . 'announcements/models/announcements.counters.factory.php';

        // Prepare some default properties for a counter model
        $counter_params = array(
            'next_number' => '1',
            'name' => $request->get('counter_name')
        );
        // counter data to take from request
        $counter_post = array(
            'formula', 'prefix', 'leading_zeroes', 'delimiter', 'date_format', 'group'
        );

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $announcements_type = Announcements_Types::buildModel($this->registry);

            // If we are trying to add a new counter
            if ($request->get('counter') == 'add_counter') {
                // Build a counter model from the request and default params
                $counter = Announcements_Counters::buildFromRequest($this->registry, 'Announcements_Counter', $counter_post);
                foreach ($counter_params as $k => $v) {
                    $counter->set($k, $v, true);
                }

                if ($counter->save()) {
                    // Show success messages
                    $this->registry['messages']->setMessage($this->i18n('message_announcements_counters_add_success'), '', -4);

                    // Set the new counter as counter for the current type
                    $announcements_type->set('counter', $counter->get('id'), true);

                    // counter is now saved in db, clean up its data
                    unset($counter);
                } else {
                    $errors = $this->registry['messages']->getErrors();
                    $this->registry['messages']->flush();
                    $idx = count($errors) + 2;
                    // Show failed message
                    $this->registry['messages']->setError($this->i18n('error_announcements_counters_add_failed'), '', -1 * $idx);
                    foreach ($errors as $k => $v) {
                        if ($k == 'name') {
                            $k = 'counter_' . $k;
                        }
                        $this->registry['messages']->setError($v, $k, -1 * (--$idx));
                    }

                    // Fail the validation of the current type
                    $announcements_type->valid = false;
                }
            }

            // clean up counter data from type model
            if (!isset($counter)) {
                foreach ($counter_post as $k) {
                    $announcements_type->unsetProperty($k, true);
                }
                $announcements_type->unsetProperty('counter_name', true);
            }

            if ($announcements_type->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_announcements_types_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_announcements_types_add_failed'), '', -1);
            }
        } else {
            //create empty anouncements type model
            $announcements_type = Announcements_Types::buildModel($this->registry);
            // default validity
            $announcements_type->set('default_validity', 1, true);
            $announcements_type->set('date_period', 'MONTH', true);
        }

        // If no counter is added yet
        if (!isset($counter)) {
            // Build an empty counter model, to be used for some display purposes
            $counter = new Announcements_Counter($this->registry, $counter_params);
        }

        // Prepare the model to be displayed
        $counter->prepare();
        // Set the counter model into the type model
        $announcements_type->set('counter_model', $counter->sanitize(), true);

        if (!empty($announcements_type)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('announcements_type', $announcements_type->sanitize());
        }

        return true;
    }


    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $announcements_type = Announcements_Types::buildModel($this->registry);
            if ($announcements_type->save()) {
                //show message 'message_announcements_types_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_announcements_types_edit_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_announcements_types_edit_failed'), '', -1);

                //register the model, with all the posted details
                $this->registry->set('announcements_type', $announcements_type);
            }

        } elseif ($id) {

            // the model from the DB
            $filters = array('where' => array('at.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $announcements_type = Announcements_Types::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($announcements_type);
        }

        if (!empty($announcements_type)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('announcements_type')) {
                $this->registry->set('announcements_type', $announcements_type->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_announcement_type'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('at.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $announcements_type = Announcements_Types::searchOne($this->registry, $filters);

        //check access and ownership of the model
        $this->checkAccessOwnership($announcements_type);

        if (!empty($announcements_type)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('announcements_type')) {
                $this->registry->set('announcements_type', $announcements_type->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_announcement_type'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $announcements_type = Announcements_Types::buildModel($this->registry);

            if ($announcements_type->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_announcements_types_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_announcements_types_translate_failed'), '', -1);
            }

        } elseif ($id) {
            //get the model from the DB
            $filters = array('where' => array('at.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $announcements_type = Announcements_Types::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($announcements_type);
        }

        if (!empty($announcements_type)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('announcements_type', $announcements_type->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_announcement_type'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Announcements_Types::changeStatus($this->registry, $ids, $status);
        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete items
        $result = Announcements_Types::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Announcements_Types::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Announcements_Types::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }
}

?>
