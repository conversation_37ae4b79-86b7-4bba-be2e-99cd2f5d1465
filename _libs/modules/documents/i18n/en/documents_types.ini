documents_types = Types of documents
documents_types_name = Name
documents_types_name_plural = Name (for list and menu)
documents_types_status = Status
documents_types_status_active = Active
documents_types_status_inactive = Inactive
documents_types_added_by = Added by
documents_types_modified_by = Modified by
documents_types_added = Added on
documents_types_modified = Modified on
documents_types_add = Add document type
documents_types_edit = Edit document type
documents_types_translate = Translate document type
documents_types_view = View document type
documents_types_description = Description
documents_types_inheritance = Sign
documents_types_inheritance_primary = Primary
documents_types_inheritance_secondary = Secondary
documents_types_direction = Direction
documents_types_default_customer = Contractor
documents_types_generate_system_task = Generate report
documents_types_opened_requires_comment = On status Open
documents_types_locked_requires_comment = On status Locked
documents_types_closed_requires_comment = On status Closed
documents_types_counter = Counter
documents_types_count_documents = Number of documents
documents_types_default_name = About
documents_types_media = Source
documents_types_pattern = Pattern for print
documents_types_email = E-mail template
documents_types_department = Department
documents_types_group = Group
documents_types_default_user_group = [Default user group]
documents_types_default_user_department = [Default user department]
documents_types_code = Code
documents_transformations = Transformations
documents_multitransformations = Transformations +
documents_types_gt2 = Include GT2
documents_types_gt2_layout = Table layout
documents_types_validate = Required fields
documents_types_validate_unique = Unique fields
documents_types_validate_unique_current_year = For current year only
documents_types_assignment_types = Assignment types
documents_types_VAT = Fiscal behaviour
documents_types_include_VAT = VAT document
documents_types_no_VAT = Document without VAT
documents_types_default_VAT = Default VAT rate
documents_types_calculated_price = Price for calculations
documents_types_gt2_price = Sell price
documents_types_gt2_last_delivery_price = Delivery price
documents_types_requires_completed_minitasks = Completed mini tasks
documents_types_related_customers_types = Customers types for AC
documents_types_basic_settings = Basic settings
documents_types_counter_settings = Counter settings
documents_types_additional_settings_of_fields = Additional settings of fields
documents_types_tasks_and_comments = Tasks and comments
documents_types_default_settings = Default settings
documents_types_add_counter = Add new

documents_layouts_type = Type
documents_layouts_full_num = Document No.
documents_layouts_custom_num = Custom number
documents_layouts_name = About
documents_layouts_customer = Contractor
documents_layouts_contract = Contract num
documents_layouts_project = Project
documents_layouts_office = Office
documents_layouts_employee = Employee
documents_layouts_media = Media
documents_layouts_deadline = Deadline
documents_layouts_validity_term = Validity Term
documents_layouts_referers = Linked documents
documents_layouts_description = Description
documents_layouts_notes = Notes
documents_layouts_forward = Forward
documents_layouts_date = Date

message_documents_types_add_success = Document type successfully added
message_documents_types_edit_success = Document type successfully edited
message_documents_types_translate_success = Document type successfully translated

error_documents_types_edit_failed = Document type edited UNSUCCESSFULLY:
error_documents_types_add_failed = Document type added UNSUCCESSFULLY:
error_documents_types_translate_failed = Document type translated UNSUCCESSFULLY:

error_no_name_specified = Please, specify name!
error_no_typename_plural_specified = Please, specify name (for list and menu)!
error_no_type_specified = Select document type
error_no_direction_specified = Select "Direction"!
error_no_counter_specified = Select "Counter"!
error_no_code = Enter "Code"!
error_code_not_unique = This code is already in use. Please, enter another code!
error_no_gt2_layout_specified = Please, choose a layout for the grouping table!
error_no_default_department_specified = Please, select default "Department"!
error_no_default_group_specified = Please, select default "Group"!
error_no_counter_name = Please, enter a "Name" for the counter!
error_no_counter_formula = Please, fill in a "Formula" for the counter!

#Help SECTION for label info 

help_documents_types_name = 
help_documents_types_name_plural = 
help_documents_types_status = 
help_documents_types_status_active = 
help_documents_types_status_inactive = 
help_documents_types_description = 
help_documents_types_inheritance = 
help_documents_types_inheritance_primary = 
help_documents_types_inheritance_secondary = 
help_documents_types_direction = 
help_documents_types_counter = 
help_documents_types_count_documents = 
help_documents_types_opened_requires_comment = Requires comment when enter in status Open
help_documents_types_locked_requires_comment = Requires comment when enter in status Locked
help_documents_types_closed_requires_comment = Requires comment when enter in status Closed
help_documents_types_generate_system_task = Automatic generation of "report" type task upon creating this document type
help_documents_types_default_name = Field defines default name upon adding document of the respective type. The name can include the following variables, which upon adding a new document will be replaced with the respective values: <br /><strong>[document_num]</strong> - Document Serial No.<br /><strong>[customer_name]</strong> - customer name<br /><strong>[office_name]</strong> - name of the office the document refers to
help_documents_types_media = 
help_documents_types_department = 
help_documents_types_group = 
help_documents_types_code = The document type code is used for the documents counters. It is recommended that the code consists of Roman letters and does not include more than 3-4 symbols.
help_documents_transformations = 
help_documents_types_validate = 
help_documents_types_validate_unique = 
help_documents_types_validate_unique_current_year = Whether set fields are validated for uniqueness against records added in current year or against all records.
help_documents_types_assignment_types = 
help_documents_types_gt2_layout = Please, select or input the layout where the GT2 would be displayed. You can add a layout with permissions for view and edit for group "All"
help_documents_types_requires_completed_minitasks = Requires that all mini tasks for record are completed before it can be finished.
help_documents_types_related_customers_types = 
