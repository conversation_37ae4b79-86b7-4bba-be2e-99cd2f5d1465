<div>
  <table border="0" cellpadding="3" cellspacing="3" >
    <tr>
      <td class="labelbox">
        {#document_transform_operations#|escape}:
      </td>
      <td class="nowrap">
      {foreach from=$available_actions.adds.options item='operation' key='operation_name' name='adds_operations'}
          <input type="radio" name="operation" id="{$operation.action}" value="{$operation.action}" onclick="toggleCombinedActionOptions(this)"{if $smarty.foreach.adds_operations.first} checked="checked"{/if} /><label for="{$operation.action}">{$operation.label|default:""}</label>&nbsp;
      {foreachelse}
          &nbsp;
      {/foreach}
      </td>
    </tr>
  </table>
  <div>
    {foreach from=$available_actions.adds.options item='current_option' key='opt_name' name='add_options'}
      {capture assign='is_hidden_option'}{if ! $smarty.foreach.add_options.first}1{/if}{/capture}
      <div id="adds_{$current_option.action}_box" {if ! $smarty.foreach.add_options.first} style="display: none;"{/if}>
      <table border="0" cellpadding="3" cellspacing="3" >
      {foreach from=$current_option.options item='option'}
        {strip}
        {capture assign='info'}
          {if $option.help}{$option.help}{else}{$option.label}{/if}
        {/capture}
        {/strip}
        {if $option.type}
          {* var=$option SHOULD BE REMOVED LATER *}
          {include file="input_`$option.type`.html"
            var=$option
            standalone=false
            name=$option.name
            custom_id=$option.custom_id|default:null
            label=$option.label
            help=$option.help|default:null
            value=$option.value|default:null
            options=$option.options|default:null
            optgroups=$option.optgroups|default:null
            option_value=$option.option_value|default:null
            first_option_label=$option.first_option_label|default:null
            onclick=$option.onclick|default:null
            on_change=$option.on_change|default:null
            onchange=$option.onchange|default:null
            sequences=$option.sequences|default:null
            check=$option.check|default:null
            scrollable=$option.scrollable|default:null
            readonly=$option.readonly|default:null
            hidden=$option.hidden|default:''
            disallow_date_after=$option.disallow_date_after|default:null
            disallow_date_before=$option.disallow_date_before|default:null
            required=$option.required|default:''
            options_align=$option.options_align|default:null
            disabled=$is_hidden_option|default:null
            show_placeholder=$option.show_placeholder|default:null
            text_align=$option.text_align|default:null
            custom_class=$option.custom_class|default:null
          }
        {/if}
      {/foreach}
      </table>
      <div>
        <button type="submit" class="nz-button" name="{$current_option.name}Go" id="{$current_option.name}Go" title="{$current_option.label}"{if $current_option.confirm|default:false} onclick="return confirmAction('{$current_option.name}', submitForm, this);"{/if}
        ><i class="material-icons">add_circle_outline</i> {$current_option.label}</button>
      </div>
    </div>
    {/foreach}
  </div>
</div>
