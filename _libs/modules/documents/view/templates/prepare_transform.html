    {if $document->get('mvars')}
      {if $group_by}
        <input type="hidden" name="group_by_{$group_num}_{$new_type}" value="{$group_by}" />
      {/if}
      {foreach name='j' from=$document->get('mvars') item='var1' key='group_type'}
      {foreach name='i' from=$var1 item='var' key='index'}

      {capture assign='layout_name'}&nbsp;{/capture}

      {if $var.type eq 'grouping' && count($var.values)}
        {strip}
        {capture assign='info'}
        {if $var.help}{$var.help}{else}{$var.label}{/if}
        {/capture}
        {/strip}
        {if $var.layout_name && $var.layout_name != $layout_name}
        {if !$smarty.foreach.i.first}
        {/if}
        {capture assign='layout_name'}{$var.layout_name}{/capture}
        {/if}
        {include file="`$templatesDir`grouptransform_table.html" first_sel_box=$smarty.foreach.i.iteration edit_vars=1}
      {else}
        <tr>
          <td colspan="3" class="error">
            {#no_additional_vals#|escape}
          </td>
        </tr>
      {/if}
      {/foreach}
      {/foreach}
      {if $group_by}
      <script type="text/javascript">
        $('buttons_row').style.display = '';
      </script>
      {/if}

    {else}
        <tr>
          <td colspan="3" class="error">
            {#no_additional_vars#|escape}
          </td>
        </tr>
    {/if}
