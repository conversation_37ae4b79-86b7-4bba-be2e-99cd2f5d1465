<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="documents" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$documents_counter->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$documents_counter->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='counters_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox doubled" name="name" id="name" value="{$documents_counter->get('name')|escape}" title="{#documents_counters_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_next_number"><label for="next_number"{if $messages->getErrors('next_number')} class="error"{/if}>{help label='counters_next_number'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="text" class="txtbox small hright" name="next_number" id="next_number" value="{if $documents_counter->isDefined('next_number')}{$documents_counter->get('next_number')}{else}1{/if}" title="{#documents_counters_next_number#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_formula"><label for="formula"{if $messages->getErrors('formula')} class="error"{/if}>{help label='counters_formula'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {include file=`$templatesDir`_counters_formula.html}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_medial_number_index"><label for="medial_number_index"{if $messages->getErrors('medial_number_index')} class="error"{/if}>{help label='counters_medial_number_index'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <select class="selbox" name="medial_number_index" id="medial_number_index" title="{#documents_counters_medial_number_index#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              <option value="number"{if $documents_counter->get('medial_number_index') == 'number'} selected="selected"{/if}>{#documents_counters_medial_number_index_number#|escape}</option>
              <option value="latin_capital_letters"{if $documents_counter->get('medial_number_index') == 'latin_capital_letters'} selected="selected"{/if}>{#documents_counters_medial_number_index_latin_capital_letters#|escape}</option>
              <option value="latin_small_letters"{if $documents_counter->get('medial_number_index') == 'latin_small_letters'} selected="selected"{/if}>{#documents_counters_medial_number_index_latin_small_letters#|escape}</option>
              <option value="cyrilic_capital_letters"{if $documents_counter->get('medial_number_index') == 'cyrilic_capital_letters'} selected="selected"{/if}>{#documents_counters_medial_number_index_cyrilic_capital_letters#|escape}</option>
              <option value="cyrilic_small_letters"{if $documents_counter->get('medial_number_index') == 'cyrilic_small_letters'} selected="selected"{/if}>{#documents_counters_medial_number_index_cyrilic_small_letters#|escape}</option>
            </select>
          </td>
        </tr>

        <tr>
          <td class="labelbox"><a name="error_medial_number_delimiter"><label for="medial_number_delimiter"{if $messages->getErrors('medial_number_delimiter')} class="error"{/if}>{help label='counters_medial_number_delimiter'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <select class="selbox small" name="medial_number_delimiter" id="medial_number_delimiter" title="{#documents_counters_medial_number_delimiter#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              <option value="."{if $documents_counter->get('medial_number_delimiter') == '.'} selected="selected"{/if}>.</option>
              <option value=","{if $documents_counter->get('medial_number_delimiter') == ','} selected="selected"{/if}>,</option>
              <option value="/"{if $documents_counter->get('medial_number_delimiter') == '/'} selected="selected"{/if}>/</option>
              <option value="-"{if $documents_counter->get('medial_number_delimiter') == '-'} selected="selected"{/if}>-</option>
              <option value="|"{if $documents_counter->get('medial_number_delimiter') == '|'} selected="selected"{/if}>|</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_medial_number_index_position"><label for="medial_number_index_position"{if $messages->getErrors('medial_number_index_position')} class="error"{/if}>{help label='counters_medial_number_index_position'}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <input type="radio" name="medial_number_index_position" value="suffix" id="medial_number_index_position_suffix"{if !$documents_counter->get('medial_number_index_position') || $documents_counter->get('medial_number_index_position') eq 'suffix'} checked="checked"{/if} /><label for="medial_number_index_position_suffix">{#documents_counters_medial_number_index_position_suffix#|escape}</label><br />
            <input type="radio" name="medial_number_index_position" value="prefix" id="medial_number_index_position_prefix"{if $documents_counter->get('medial_number_index_position') eq 'prefix'} checked="checked"{/if} /><label for="medial_number_index_position_prefix">{#documents_counters_medial_number_index_position_prefix#|escape}</label>
          </tr>
        </tr>

        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='counters_description'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <textarea class="areabox doubled" name="description" id="description" title="{#documents_counters_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$documents_counter->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_count_documents"><label for="count_documents">{help label='counters_count_documents'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$documents_counter->get('count_documents')|escape|default:0}
            <input type="hidden" name="count_documents" id="count_documents" value="{$documents_counter->get('count_documents')|escape}" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_types_used"><label for="types_used">{help label='counters_types_used'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $documents_counter->get('types')}
              {foreach name='i' from=$documents_counter->get('types') item='doctype_name' key='doctype_id'}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=types&amp;types=view&amp;view={$doctype_id}" target="_blank">{$smarty.foreach.i.iteration}. {$doctype_name}</a><br />
              {/foreach}
            {else}
              <span class="error">{#error_no_types_used#|escape}</span>
            {/if}
          </td>
        </tr>
        <tr>
          <td colspan="3" class="t_caption3 strong">{#documents_counters_formula_legend#|escape}</td>
        </tr>
        <tr>
          <td colspan="3">
            {include file=`$templatesDir`_counters_formula_legend.html}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$documents_counter}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
