<h1>{$title|escape}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        {include file=`$theme->templatesDir`actions_box.html}
        {include file=`$theme->templatesDir`translate_box.html}
        {include file=`$theme->templatesDir`_submenu_actions_box.html}

        <input type="hidden" name="id" id="id" value="{$document->get('id')}" />
        <input type="hidden" name="model_lang" id="model_lang" value="{$document->get('model_lang')|default:$lang}" />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              {if $document->checkPermissions('addtimesheet')}
                <div class="abs_div stopwatch_div" style="visibility: hidden;">
                  <button type="button" name="stopwatch" id="stopwatch" class="button" title="{#stop_watch#|escape}" onclick="confirmAction('stop_watch', function(el) {ldelim} stopWatch(el, 'document', {$document->get('id')}); {rdelim}, this)" style="{if !$document->get('startwatch')}display: none;{/if}"><img src="{$theme->imagesUrl}stopwatch.png" width="16" height="16" alt="" border="0" /></button><button type="button" name="startwatch" id="startwatch" class="button" title="{#start_watch#|escape}" onclick="confirmAction('start_watch', function(el) {ldelim} startWatch(el, 'document', {$document->get('id')}); {rdelim}, this)" style="{if $document->get('startwatch')}display: none;{/if}"><img src="{$theme->imagesUrl}startwatch.png" width="16" height="16" alt="" border="0" /></button>
                </div>
              {/if}
              {assign var='layouts_vars' value=$document->get('vars')}
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              {foreach from=$document->get('layouts_details') key='lkey' item='layout'}
                {if $layout.view}

                {if $layout.system || array_key_exists($layout.id, $layouts_vars)}
                <tr{if !$layout.visible} style="display: none;"{/if}>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                    </div>
                    <div class="layout_switch" {if $layout.system}onclick="toggleViewLayouts(this)" id="document_{$layout.keyword}_switch"{else}onclick="toggleLayouts(this)" id="layout_{$layout.id}_switch"{/if}>
                      <a name="document_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                    </div>
                  </td>
                </tr>
                {/if}

                {if $lkey eq 'status'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    <div class="documents_status {$document->get('status')}">
                      {if $document->get('status') eq 'opened'}{#documents_status_opened#|escape}
                      {elseif $document->get('status') eq 'locked'}{#documents_status_locked#|escape}
                      {elseif $document->get('status') eq 'closed'}{#documents_status_closed#|escape}
                      {/if}
                      {if $document->get('substatus_name')}
                        &raquo; {$document->get('substatus_name')|escape}
                      {/if}
                      {if $document->checkPermissions('setstatus')}
                      <a href="#" onclick="toggleActionOptions($('setstatus_action')); return false;">{#documents_setstatus#|escape}</a>
                      {/if}
                    </div>
                  </td>
                </tr>
                {elseif $lkey eq 'type'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {$document->get('type_name')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'full_num'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {$document->get('full_num')|numerate:$document->get('direction')}
                    <input type="hidden" value="{$document->get('num')}" name="num" />
                    <input type="hidden" value="{$document->get('full_num')}" name="full_num" />
                  </td>
                </tr>
                {elseif $lkey eq 'custom_num'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {$document->get('custom_num')|default:"&nbsp;"}
                  </td>
                </tr>
                {elseif $lkey eq 'date'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $document->get('date')}
                      {$document->get('date')|date_format:#date_short#}
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'name'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {include file=`$templatesDir`_info.html assign='info'}
                    <span {popup text=$info|escape caption=#system_info#|escape width=250}>{mb_truncate_overlib text=$document->get('name')|escape|default:"&nbsp;"}</span>
                  </td>
                </tr>
                {elseif $lkey eq 'customer'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    <a href="{$customer_view_link}" title="{#view#|escape}: {$document->get('customer_name')|escape}">{$document->get('customer_name')|escape|default:"&nbsp;"}</a>
                    {if $document->get('branch') && $customer_branch}
                      <span class="labelbox">{help label_content=$document->getBranchLabels('documents_branch')|escape}</span>
                      <span{if !$customer_branch->isActivated()} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$document->get('branch_name')|escape}</span>
                    {/if}
                    {if $document->get('contact_person') && $contact_person}
                      <span class="labelbox">{help label_content=#documents_contact_person#|escape}</span>
                      <span{if !$contact_person->isActivated()} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$document->get('contact_person_name')|escape}</span>
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'trademark'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $document->get('trademark')}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$document->get('trademark')}" title="{#view#|escape}: {$document->get('trademark_name')|escape}">{$document->get('trademark_name')|escape|default:"&nbsp;"}</a>
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'contract'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $document->get('contract')}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$document->get('contract')}" title="{#view#|escape}: {$document->get('contract_custom_label')|escape}">{$document->get('contract_custom_label')|escape|default:"&nbsp;"}</a>
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'project'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $project}
                      <a href="{$project_view_link}" title="{#view#|escape}: {$project|escape}">{$project|escape}</a>
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'office'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $office}
                      {if !$office->isActivated()}
                      <span class="inactive_option" title="{#inactive_option#}"> *{$office->get('name')|escape}</span>
                      {else}
                      {$office->get('name')|escape}
                      {/if}
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'employee'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $employee}
                      {if !$employee->isDeleted() && $employee->isActivated()}
                        {$employee->get('name')|escape} {$employee->get('lastname')|escape}
                      {else}
                        <span class="inactive_option" title="{#inactive_option#}">*{$employee->get('name')|escape} {$employee->get('lastname')|escape}</span>
                      {/if}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'media'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $media}
                      {if !$media->isActivated()}
                      <span class="inactive_option" title="{#inactive_option#}"> *{$media->get('name')|escape}</span>
                      {else}
                      {$media->get('name')|escape}
                      {/if}
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'deadline'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $document->get('deadline')}
                      {if $document->get('status') != 'closed' && $document->get('deadline') && $document->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
                        {capture assign='document_expired'}
                          {#documents_expired_legend#}: <strong>{$document->get('deadline')|date_format:#date_mid#}</strong>
                        {/capture}
                        <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" class="t_info_image" alt="{#expired#|escape}" {popup text=$document_expired|escape caption=#documents_expired#|escape} />
                      {/if}
                      {$document->get('deadline')|date_format:#date_mid#}
                    {else}
                      {#documents_no_deadline#|escape}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'validity_term'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $document->get('validity_term')}
                      {if $document->get('status') != 'closed' && $document->get('validity_term') && $document->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
                        {capture assign='document_expired_validity_term'}
                          {#documents_expired_validity_legend#}: <strong>{$document->get('validity_term')|date_format:#date_mid#}</strong>
                        {/capture}
                        <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" class="t_info_image" alt="{#expired#|escape}" {popup text=$document_expired_validity_term|escape caption=#documents_validity_term_expired#|escape} />
                      {/if}
                      {$document->get('validity_term')|date_format:#date_mid#}
                    {else}
                      {#documents_no_validity_term#|escape}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'referers'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {if $document->get('referers')}
                      {foreach name='i' from=$document->get('referers') key='ref_id' item='ref'}
                        {$smarty.foreach.i.iteration}. <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$ref_id}{if $ref.archived_by}&amp;archive=1{/if}" target="_blank">{$ref.full_num|numerate:$ref.direction}&nbsp;&nbsp;{$ref.name|escape} ({$ref.type_name|escape})</a><br />
                      {/foreach}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'description'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {$document->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                  </td>
                </tr>
                {elseif $lkey eq 'notes'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {$document->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                  </td>
                </tr>
                {elseif $lkey eq 'department'}
                <tr id="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $department}
                      {if !$department->isActivated()}
                        <span class="inactive_option" title="{#inactive_option#}"> *{$document->get('department_name')|escape}</span>
                      {else}
                        {$document->get('department_name')|escape}
                      {/if}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'assign_info' && $currentUser->get('is_portal')}
                {if in_array('owner', $settings_assign)}
                <tr class="document_{$layout.keyword}"{if $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label='assign_owner'}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  {foreach name='i' from=$document->get('assignments_owner') item='assign'}
                    {$smarty.foreach.i.iteration}. {$assign.assigned_to_name|escape}<br />
                  {/foreach}
                  </td>
                </tr>
                {/if}
                {if in_array('responsible', $settings_assign)}
                <tr class="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label='assign_responsible'}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  {foreach name='i' from=$document->get('assignments_responsible') item='assign'}
                    {$smarty.foreach.i.iteration}. {$assign.assigned_to_name|escape}<br />
                  {/foreach}
                  </td>
                </tr>
                {/if}
                {if in_array('observer', $settings_assign)}
                <tr class="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label='assign_observer'}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  {foreach name='i' from=$document->get('assignments_observer') item='assign'}
                    {$smarty.foreach.i.iteration}. {$assign.assigned_to_name|escape}<br />
                  {/foreach}
                  </td>
                </tr>
                {/if}
                {if in_array('decision', $settings_assign)}
                <tr class="document_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label='assign_decision'}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                  {foreach name='i' from=$document->get('assignments_decision') item='assign'}
                    {$smarty.foreach.i.iteration}. {$assign.assigned_to_name|escape}<br />
                  {/foreach}
                  </td>
                </tr>
                {/if}
                {elseif array_key_exists($layout.id, $layouts_vars)}
                <!-- Document Additional Vars -->
                {include file=`$templatesDir`_view_vars.html}
                {elseif $lkey eq 'attachments' && $currentUser->get('is_portal') && $document->get('attachments')}
                <!-- Document attachments -->
                <tr>
                  <td colspan="3" class="nopadding">
                    {include file=`$templatesDir`_attachments.html}
                  </td>
                </tr>
                {/if}

                {/if}
              {/foreach}
                {if $document->get('buttons') || $document->get('transform_optgroups')}
                  <tr>
                    <td colspan="3">&nbsp;</td>
                  </tr>

                  <tr>
                    <td colspan="3">
                      {strip}
                        {if $document->get('buttons')}
                          {foreach from=$document->get('buttons') item='button'}
                            {include file=`$theme->templatesDir`input_button.html
                                    label=$button.label
                                    standalone=true
                                    name=$button.name
                                    source=$button.source
                                    disabled=$button.disabled
                                    hidden=$button.hidden
                                    width=$button.width
                                    height=$button.height}
                          {/foreach}
                        {/if}

                        {if $document->get('transform_optgroups')}
                          {foreach from=$document->get('transform_optgroups') item='optgroups'}
                            {foreach from=$optgroups item='button'}
                              <input type="button" value="{$button.label}" name="btn" class="button" onclick="window.open('{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=transformations&amp;transformations={$document->get('id')}&amp;operation=transform&amp;transform={$button.option_value}', '{if $button.settings.target}{$button.settings.target}{else}_self{/if}');" />
                            {/foreach}
                          {/foreach}
                        {/if}
                      {/strip}
                    </td>
                  </tr>
                {/if}
              </table>
            </td>
          </tr>
        </table>
        {include file=`$theme->templatesDir`help_box.html}
        {include file=`$theme->templatesDir`system_settings_box.html object=$document}
      </div>
    </td>
    {if isset($side_panels)}
    <td class="side_panel_container">
      {include file=`$theme->templatesDir`_side_panel_options.html}
      {include file=`$theme->templatesDir`side_panels_box.html}
    </td>
    {/if}
  </tr>
</table>