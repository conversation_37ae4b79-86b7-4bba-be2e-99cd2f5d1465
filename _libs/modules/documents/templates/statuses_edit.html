<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="documents" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$documents_status->get('id')}" />
<input type="hidden" name="doc_type" id="doc_type" value="{$documents_status->get('doc_type')}" />
<input type="hidden" name="status" id="status" value="{$documents_status->get('status')}" />
<input type="hidden" name="icon_name" value="{$documents_status->get('icon_name')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$documents_status->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='statuses_doc_type'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{$type_name|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='statuses_status'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{$status_name|escape}</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='statuses_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$documents_status->get('name')|escape|default:''}" title="{#documents_statuses_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_sequence"><label for="sequence"{if $messages->getErrors('sequence')} class="error"{/if}>{help label='statuses_sequence'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox num hright" name="sequence" id="sequence" value="{$documents_status->get('sequence')|escape|default:''}" title="{#documents_statuses_sequence#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        {include file="input_dropdown.html"
                var=$requiring_comment
                name=$requiring_comment.name
                custom_id=$requiring_comment.custom_id
                label=$requiring_comment.label
                help=$requiring_comment.help
                value=$requiring_comment.value
                options=$requiring_comment.options
                optgroups=$requiring_comment.optgroups
                required=$requiring_comment.required
        }
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='statuses_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#documents_statuses_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$documents_status->get('description')|escape}</textarea>
          </td>
        </tr>
        {if $documents_status->get('icon_name')}
          <tr>
            <td class="labelbox"><label for="statuses_icon">{help label='statuses_icon'}</label></td>
            <td>&nbsp;</td>
            <td nowrap="nowrap">
              <img src="{$smarty.const.PH_DOCUMENTS_STATUSES_URL}{$documents_status->get('icon_name')}" alt="" />
            </td>
          </tr>
        {/if}
        {if $documents_status->get('icon_name')}
          <tr>
            <td class="labelbox"><a name="error_icon_delete"><label for="icon_delete"{if $messages->getErrors('icon_delete')} class="error"{/if}>{help label='statuses_icon_delete'}</label></a></td>
            <td>&nbsp;</td>
            <td>
              <input type="checkbox" name="icon_delete" id="icon_delete" value="1" title="{#documents_statuses_icon_delete#|escape}"{if $documents_status->get('icon_delete')} checked="checked"{/if} />
            </td>
          </tr>
        {/if}
        <tr>
          <td class="labelbox"><a name="error_icon_file"><label for="icon_file"{if $messages->getErrors('icon_file')} class="error"{/if}>{help label='statuses_icon_file'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="file" name="icon_file" id="icon_file" class="filebox" title="{#documents_statuses_icon_file#|escape}" />
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$documents_status}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
