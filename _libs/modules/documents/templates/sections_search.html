<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=documents&amp;controller=sections&amp;sections=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="documents_section" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;controller=sections" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#documents_sections_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.position.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.position.link}">{#documents_sections_position#|escape}</div></td>
          <td class="t_caption t_border {$sort.description.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.description.link}">{#documents_sections_description#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$documents_sections item='documents_section'}
      {strip}
      {capture assign='info'}
        <strong>{#documents_section#|escape}:</strong> {$documents_section->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$documents_section->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$documents_section->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$documents_section->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$documents_section->get('modified_by_name')|escape}<br />
        {if $documents_section->isDeleted()}<strong>{#deleted#|escape}:</strong> {$documents_section->get('deleted')|date_format:#date_mid#|escape}{if $documents_section->get('deleted_by_name')} {#by#|escape} {$documents_section->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$documents_section->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $documents_section->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if !$documents_section->get('active')} t_inactive{/if}{if $documents_section->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$documents_section->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($documents_section->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($documents_section->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=sections&amp;{$action_param}=view&amp;view={$documents_section->get('id')}">{$documents_section->get('name')|escape}</a></td>
          <td class="t_border hright {$sort.position.isSorted}">{$documents_section->get('position')|escape}</td>
          <td class="t_border {$sort.description.isSorted}">{$documents_section->get('description')|escape|default:"&nbsp;"}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$documents_section}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="6">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
