<?php

class Documents_Counters_Search_Viewer extends Viewer {
    public $template = 'counters_search.html';
    public $filters = array();

    /**
     * Sortable columns in the list view
     */
    public $sortables = array('name', 'formula', 'description', 'next_number', 'count_documents');

    public function prepare() {
        require_once $this->modelsDir . 'documents.counters.factory.php';

        $filters = Documents_Counters::saveSearchParams($this->registry, array(), 'search_');

        list($documents_counters, $pagination) = Documents_Counters::pagedSearch($this->registry, $filters);
        $this->data['documents_counters'] = $documents_counters;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('documents');
        $href = sprintf('%s=%s&amp;type=&amp;type_section=', 
                            $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('documents_counters');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
