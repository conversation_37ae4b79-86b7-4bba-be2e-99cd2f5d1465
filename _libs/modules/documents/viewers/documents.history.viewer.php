<?php

class Documents_History_Viewer extends Viewer {

    public $template = 'history.html';

    public function prepare() {
        $this->data['model'] = $this->getModel();

        // set submit link
        $this->submitLink = sprintf(
            '%s?%s=%s&amp;%s=%s&amp;%s=%s%s',
            $_SERVER['PHP_SELF'],
            $this->registry['module_param'],
            $this->module,
            $this->registry['action_param'],
            $this->action,
            $this->action,
            $this->model->get('id'),
            ($this->model->get('archived_by') ? '&amp;archive=1' : '')
        );
        $this->data['submitLink'] = $this->submitLink;

        $filters = array(
            'model' => $this->model,
            'display' => $this->registry['currentUser']->getPersonalSettings('interface', 'list_history') ?: Documents::$itemsPerPage,
            'paginate' => true,
            'page' => $this->registry['request']->get('page') ?: 1,
            'history_activity' => $this->registry['request']->get('history_activity') ?: 0,
        );
        $h_id = $this->registry['request']->get('audit') ?: 0;
        if ($h_id) {
            $filters['page'] = Documents_History::findPage($this->registry, $h_id, $filters) ?: $filters['page'];
        }

        // prepare model history
        list($history, $pagination) = Documents_History::getData($this->registry, $filters);
        $this->data['history'] = $history;
        $this->data['pagination'] = $pagination;
        // prepare audit
        if ($history && !$this->registry['request']->get('history_activity')) {
            $audit_history = array();
            if ($h_id) {
                $audit_history = array_filter($history, function($a) use ($h_id) { return $a['h_id'] == $h_id; });
            }
            $audit_history = !empty($audit_history) ? reset($audit_history) : $history[0];

            $this->data['audit'] = Documents_Audit::getData(
                $this->registry,
                array(
                    'parent_id' => $audit_history['h_id'],
                    'model_name' => $this->model->modelName,
                    'model_type' => $this->model->get('type'),
                    'action_type' => $audit_history['action_type'],
                    'archive' => $this->model->get('archived_by')
                ));
            $this->data['audit_title'] = $this->i18n('audit_vars', array(
                $this->i18n('document')
            ));
            $this->data['audit_legend'] = $this->i18n('audit_legend', array(
                $audit_history['user_name'],
                date('d.m.Y, H:i', strtotime($audit_history['h_date']))
            ));
        }
        $this->data['audit_subpanel_template'] = PH_MODULES_DIR . 'documents/templates/_audit.html';

        if ($this->registry['request']->get('source') != 'ajax') {
            $this->prepareTranslations();
            $this->prepareTitleBar();
        }

        if ($this->theme->isModern()) {
            //$this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/communications.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }
    public function prepareTitleBar() {
        // TODO: find a consensus for the title!
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('documents_history'), $this->model->getModelTypeName());
        }

        $this->data['title'] = $title;
    }
}

?>
