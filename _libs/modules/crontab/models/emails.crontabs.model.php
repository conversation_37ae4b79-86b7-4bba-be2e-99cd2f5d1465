<?php

use Nzoom\Email\Imap\Box;
use Nzoom\Email\Imap\BoxFactory;
use Nzoom\Email\Imap\Message;


/**
 * Emails_Crontabs model class
 */
Class Emails_Crontab extends Model {
    public $modelName = 'Emails_Crontab';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        $this->logger = new Logger($this->modelName,
        'crontab/' . $params['action']. '_' .date('Y_m').'_cron' . '.log');

        $start_text = "\n======================\n"." (".date('Y-m-d H:i:s').")";
        $this->logger->info($start_text);

        $this->log_pattern = "bounced emails:(%s): SENT BOX ITEMS WITH IDS:\n%s";

        //set lang
        $this->lang = $this->registry['lang'];
        $this->registry->set('currentUser',Users::searchOne($this->registry,array('where'=>array('u.id=' . PH_AUTOMATION_USER, 'u.hidden=1'))), true);
    }

    public function processInboxEmails() {

        $email_params = $this->registry['config']->getSectionParams('emails');

        if (empty($email_params['process_emails'])) {
            //do not process emails
            return;
        }

        if (!function_exists('imap_open')) {
            $this->logger->error("PHP's IMAP module is not installed!");
            return false;
        }

        $db = $this->registry['db'];

        if (empty($email_params['replyto_imap_server']) || empty($email_params['replyto_email'])) {
            $this->logger->error("No imap reply to credentials");
            return false;
        }

        $mailhost           = $email_params['replyto_imap_server']; // your mail server
        $port               = (!empty($email_params['replyto_imap_port'])) ? $email_params['replyto_imap_port'] : 143; // your port
        $mailbox_username   = $email_params['replyto_email']; // your mailbox username
        $mailbox_password   = (!empty($email_params['replyto_imap_password'])) ? $email_params['replyto_imap_password'] : ''; // your mailbox password
        $mailbox_cert       = (!empty($email_params['replyto_imap_cert'])) ? $email_params['replyto_imap_cert'] : 'notls'; // encrypt session

        // open IMAP resources
        $mbox = @imap_open("{" . $mailhost . ':' . $port . "/imap/".$mailbox_cert."}INBOX", $mailbox_username, $mailbox_password, CL_EXPUNGE);

        if (!is_resource($mbox)) {
            $this->logger->error("Could not connect to: " . "{" . $mailhost . ':' . $port . "}INBOX; user: " . $mailbox_username . "; pass: " . $mailbox_password);
            return false;
        }

        $msg_count = imap_num_msg($mbox);

        //set limit to 60 minutes
        set_time_limit(60*60);

        $processed = array();

        $add_model = '';
        if (!empty($email_params['add_model'])) {
            if ($email_params['add_model'] == 'Comment') {
                //check settings for adding comment
               $add_comment_settings = array();
                if (!empty($email_params['add_model_settings'])) {
                    //get settings for new model
                    $add_model_settings = $this->getAddModelSettings($email_params['add_model_settings']);
                    $add_model = $email_params['add_model'];
                    require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                }
            } elseif ($email_params['add_model'] == 'Task') {
                //check settings for adding task
                $add_comment_settings = array();
                if (!empty($email_params['add_model_settings'])) {
                    //get settings for new model
                    $add_model_settings = $this->getAddModelSettings($email_params['add_model_settings']);
                    $add_model = $email_params['add_model'];
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.history.php';
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.audit.php';
                    $old_model = new Task($this->registry);
                    $old_model->sanitize();
                }
            } elseif ($email_params['add_model'] == 'Document') {
                //check settings for adding document
                $add_comment_settings = array();
                if (!empty($email_params['add_model_settings'])) {
                    $this->registry->set('edit_all', true, true);
                    //get settings for new model
                    $add_model_settings = $this->getAddModelSettings($email_params['add_model_settings']);
                    $add_model = $email_params['add_model'];
                    require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                    require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                    require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
                    $old_model = new Document($this->registry);
                    $old_model->sanitize();
                }
            }
            if (!empty($add_model)) {
                require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            }
        }

        //set limit to 30 minutes
        set_time_limit(60*30);
        // get messages' uid as their sequence number could changer in time
        // uid could change ONLY after imap_close()
        $uids = array();
        for($i = 1; $i <= $msg_count; $i++) {
            $uids[] = imap_uid($mbox, $i);
        }
        foreach ($uids as $uid) {
            //get back the message sequence number
            $i = imap_msgno($mbox, $uid);

            //parse the structure
            $header = preg_replace('#\r#','', imap_fetchbody($mbox, $i, "0"));
            $original = preg_replace('#\r#','', imap_fetchbody($mbox, $i, "3"));
            $params = array();

            // take the nzoom code if it is set
            if (preg_match('#(^|\n)x-nzoomCode:([^\n][0-9\.a-z]+)#i', $original, $matches)) {
                $params['code'] = trim($matches[2]);
            }

            // get nzoom code from outlook messages
            if (empty($params['code']) && preg_match('#In-Reply-To: <(.+)-nZoomSystem\@#', $header, $matches)) {
                $params['code'] = preg_replace('#\<|\>#', '', $matches[1]);
            }

            // if no code is set the message is skipped
            if (!empty($params['code'])) {
                $structure = imap_fetchstructure($mbox, $i);
                $body = preg_replace('#\r#','', imap_fetchbody($mbox, $i, "1"));
                $diagnostic = preg_replace('#\r#','', imap_fetchbody($mbox, $i, "2"));
                $oHeader = imap_headerinfo($mbox, $i);

                //get messages and attachments
                $messageNumber = $i;
                $html_body = '';
                $plain_text_body =  imap_base64($body);
                $filenames = array();
                $attachments = array();
                if (!empty($structure->parts)) {
                    $flattenedParts = $this->flattenParts($structure->parts);
                    foreach($flattenedParts as $partNumber => $part) {

                        switch($part->type) {

                            case 0:
                                // the HTML or plain text part of the email
                                $message = $this->getPart($mbox, $messageNumber, $partNumber, $part->encoding);
                                //convert the message into UTF8 charset
                                if (!empty($part->parameters)) {
                                    foreach($part->parameters as $parameter) {
                                        if ($parameter->attribute == 'CHARSET') {
                                            if ($parameter->value != 'utf-8') {
                                                $message = iconv($parameter->value, 'UTF-8', $message);
                                            }
                                            break;
                                        }
                                    }
                                }
                                if ($partNumber == '1.1' || $partNumber == '1') {
                                    //plain text
                                    $plain_text_body = $message;
                                } elseif ($partNumber == '1.2' || $partNumber == '2') {
                                    //HTML message
                                    $html_body = $message;
                                }
                                // now do something with the message, e.g. render it
                            break;

                            case 1:
                                // multi-part headers, can ignore

                            break;
                            case 2:
                                // attached message headers, can ignore
                            break;

                            case 3: // application
                            case 4: // audio
                            case 5: // image
                            case 6: // video
                            case 7: // other

                                $filename = $this->getFilenameFromPart($part);
                                if($filename) {
                                    // it's an attachment
                                    $attachment = $this->getPart($mbox, $messageNumber, $partNumber, $part->encoding);
                                    // now do something with the attachment, e.g. save it somewhere
                                    $filenames[] = $filename;
                                    $attachments[] = $attachment;
                                }
                                else {
                                    // don't know what it is
                                }

                            break;

                        }

                    }
                }
                $params['plain_text_body'] = $plain_text_body;

                // check if the message is undelivered
                if (preg_match('#(^|\n)From:[^\n]*mailer-daemon\@#i', $header) || preg_match('#(^|\n)Subject:\s*(Undelivered Mail|Mail delivery failed|Delivery failure|failure|Undeliverable)#i', $header)) {
                    $params['bounced'] = true;
                }
                if (empty($params['bounced']) && isset($structure->parts) && isset($structure->parts[2]) && isset($structure->parts[2]->description)) {
                    if (preg_match('#Undelivered message#i', $structure->parts[2]->description)) {
                        $params['bounced'] = true;
                    }
                }

                if (preg_match('#Auto-Submitted: (.+)\n#', $header, $matches)) {
                    if (strpos($matches[1], 'auto-replied') !== false) {
                        //autoresponder
                        $params['autoresponder'] = true;
                    }
                }

                // parse the diagnostic to find the final recipient
                if (preg_match_all('#(^|\n)Final-Recipient:\s*rfc\d+\;\s*([^\n]*)#i', $diagnostic, $matches)) {
                    $params['mails'] = $matches[2];
                }

                if (empty($params['mails'])) {
                    $fromaddr = $oHeader->from[0]->mailbox . "@" . $oHeader->from[0]->host;
                    if (Validator::validEmail($fromaddr)) {
                        $params['mails'] = array($fromaddr);
                    }
                }
                if (empty($params['mails'])) {
                    $params['mails'] = array();
                }

                // parse the diagnostic to find the action
                if (preg_match_all('#(^|\n)Action:([^\n]*)#i', $diagnostic, $matches)) {
                    $params['action'] = $matches[2];
                }

                // parse the diagnostic code to its fragments depending on the sent emails (one row per each sent mail copy)
                if (preg_match_all('#(^|\n)Diagnostic-Code:(([^\n{2}]*\n)+)(([a-zA-Z\-]+\:)|$)#i', $diagnostic, $matches)) {
                    if (!empty($matches[2])) {
                        foreach ($matches[2] as $key => $match) {
                            $params['diagnostic'][$key] = preg_replace('#\n#', ' ', $match);
                            $params['diagnostic'][$key] = trim(preg_replace('#\s+#', ' ', $params['diagnostic'][$key]));
                        }
                    }
                }

                // get subject
                $params['subject'] = iconv_mime_decode($oHeader->subject, 2, 'UTF-8');

                // go through all sent mails and updates the status in emails_sentbox table
                $found = false;
                $error = false;
                $sentbox_data = array();
                foreach ($params['mails'] as $key => $email) {
                    $found = false;

                    // get the mail from emails_sentbox taken by code and recipient
                    // IMPORTANT!!! The LIKE search by e-mail is because of the possibility the installation to have end-point e-mail
                    $query = "SELECT *, UNCOMPRESS(`body`) as body FROM " . DB_TABLE_EMAILS_SENTBOX . ' WHERE code = \'' . $params['code'] . '\' AND recipient LIKE \'%' . $email . '%\'';
                    $res = $db->GetRow($query);
                    if ($res) {
                        $found = $res['id'];
                        $sentbox_data = $res;
                    } else {
                        // if no mail is found it shouldn't be deleted
                        $error = true;
                    }

                    // if mail in emails_sentbox is found the status in emails_sentbox is updated
                    if ($found !== false) {
                        if (!empty($params['bounced'])) {
                            $set = array();
                            $params['action'][$key] = !empty($params['action'][$key]) ? $params['action'][$key] : 'failed';
                            $params['diagnostic'][$key] = !empty($params['diagnostic'][$key]) ? $params['diagnostic'][$key] : 'Diagnostic string was not found';
                            $set[] = sprintf('`status` = "%s"', $params['action'][$key]);
                            $set[] = sprintf('`bounced` = now()');
                            $set[] = sprintf('`diagnostic` = "%s"', $params['diagnostic'][$key]);
                            $query = "UPDATE " . DB_TABLE_EMAILS_SENTBOX . " SET \n" . implode(",\n", $set) . "\n" .
                                     "WHERE id = " . $found;
                            $db->Execute($query);

                            if (!$db->ErrorMsg()) {
                                $processed[] = $found;
                                $this->updateParentEmail($found);
                            } else {
                                $error = true;
                                $this->logger->error('Can not process bounced mail: ' . $db->ErrorMsg());
                            }
                        } else {
                            //skip autoresponders
                            if (empty($params['autoresponder'])) {
                                //remove reply content
                                $params['cleaned_plain_text_body'] = $this->removeOriginalEmail($params['plain_text_body'], $sentbox_data['body']);

                                $email_user = Users::searchOne($this->registry,array('where'=>array('u.email=\'' . $email . '\'')));
                                if (!empty($email_user)) {
                                    //set email user for current user
                                    $this->registry->set('currentUser', $email_user, true);
                                }
                                //check for adding new model
                                if (!empty($add_model)) {
                                    $parent_basic_vars = array();
                                    $parent_additional_vars = array();
                                    $parent_additional_vals = array();
                                    if (!empty($sentbox_data['model']) && !empty($sentbox_data['model_id']) && (!empty($add_model_settings['transform_settings']['bb']) || !empty($add_model_settings['transform_settings']['ba']) || !empty($add_model_settings['transform_settings']['ab']) || !empty($add_model_settings['transform_settings']['aa']))) {
                                        if ($add_model == 'Document') {
                                            //get parent model data
                                            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                                            $filters = array('where' => array('d.id = ' . $sentbox_data['model_id']));
                                            $parent_model = Documents::searchOne($this->registry, $filters);
                                            //get basic vars
                                            $parent_basic_vars = $parent_model->getAll();
                                            //get additional vars
                                            $parent_model->getVars();
                                            $tmp_vars = $parent_model->get('vars');
                                            foreach ($tmp_vars as $key => $var) {
                                                $parent_additional_vars[$var['name']] = $var;
                                                $parent_additional_vals[$var['name']] = $var['value'];
                                            }
                                        }

                                    }
                                }
                                if ($add_model == 'Comment') {
                                    //add new comment
                                    $comment = Comments::buildModel($this->registry);
                                    if (!empty($add_model_settings['transform_settings']['sbb'])) {
                                        //copy data from sent box to basic vars
                                        $this->setBasicVars($comment, $add_model_settings['transform_settings']['sbb'], $sentbox_data);
                                    }
                                    if (!empty($add_model_settings['transform_settings']['msgb'])) {
                                        //copy data from email message to basic vars
                                        $this->setBasicVars($comment, $add_model_settings['transform_settings']['msgb'], $params);
                                    }
                                    if ($comment->get('model') == 'Comment' && $comment->get('model_id')) {
                                        //get the parent model of the comment
                                        $query = 'SELECT model, model_id FROM ' . DB_TABLE_COMMENTS . ' WHERE id=' . $comment->get('model_id');
                                        $data = $db->GetRow($query);
                                        if ($data) {
                                            $comment->set('model', $data['model'], true);
                                            $comment->set('model_id', $data['model_id'], true);
                                        } else {
                                            //do not allow adding of comment
                                            $comment->set('model', '', true);
                                            $comment->set('model_id', '', true);
                                        }
                                    }
                                    //save comment
                                    if ($comment->get('model') && $comment->get('model_id') && $comment->save()) {
                                        $comment->saveHistory();
                                        //send mail to user
                                        //$this->sendEmailOnSuccess($email, $email_user, $add_model, $html_body, $plain_text_body, $comment->get('model_id'), $comment->get('model'));
                                        $error = false;
                                    } else {
                                        //send mail to user
                                        $this->sendEmailOnError($email, $email_user, $add_model, $html_body, $plain_text_body);
                                        $error = true;
                                    }
                                } elseif ($add_model == 'Task') {
                                    //add new task
                                    $task = Tasks::buildModel($this->registry);
                                    if (!empty($add_model_settings['transform_equals']['b'])) {
                                        //set constant basic vars
                                        $this->setBasicVals($task, $add_model_settings['transform_equals']['b']);
                                    }
                                    if (!empty($add_model_settings['transform_calc']['b'])) {
                                        //calculate basic vars
                                        $this->calcBasicVals($task, $add_model_settings['transform_calc']['b']);
                                    }
                                    if (!empty($add_model_settings['transform_settings']['sbb'])) {
                                        //copy data from sent box to basic vars
                                        $this->setBasicVars($task, $add_model_settings['transform_settings']['sbb'], $sentbox_data);
                                    }
                                    if (!empty($add_model_settings['transform_settings']['msgb'])) {
                                        //copy data from email message to basic vars
                                        $this->setBasicVars($task, $add_model_settings['transform_settings']['msgb'], $params);
                                    }
                                    if ($task->get('type') && !$task->get('name')) {
                                        // get the default name from the type
                                        $sql = 'SELECT name FROM ' . DB_TABLE_TASKS_TYPES_I18N . ' WHERE parent_id=' . $task->get('type');
                                        $task->set('name', $db->GetOne($sql), true);
                                    }

                                    // load lang files for tasks
                                    $lang_file_task = sprintf('%s%s%s%s', PH_MODULES_DIR, 'tasks/i18n/', $this->registry['lang'], '/tasks.ini');
                                    $this->loadI18NFiles($lang_file_task);

                                    //save task
                                    if ($task->save()) {
                                        $filters = array('where' => array('t.id = ' . $task->get('id')));
                                        $task = Tasks::searchOne($this->registry, $filters);
                                        if (!empty($attachments)) {
                                            //add attachments
                                            foreach ($attachments as $key => $attachment) {
                                                $this->attachFileToModel($attachment, $filenames[$key], $task);
                                            }
                                        }
                                        //add history and audit
                                        $audit_parent = Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'add', 'new_model' => $task, 'old_model' => $old_model));
                                        //send mail to user
                                        $new_model_id = $task->get('id');
                                        $this->sendEmailOnSuccess($email, $email_user, $add_model, $html_body, $plain_text_body, $new_model_id);
                                        $error = false;
                                    } else {
                                        //send mail to user
                                        $errors_add = $task->messages->getErrors();
                                        if (!empty($errors_add)) {
                                            $html_body = '<span style="color: red;">' . implode('</span><br /><span style="color: red;">', $errors_add) . '</span><br /><br />' . $html_body;
                                        }
                                        $this->sendEmailOnError($email, $email_user, $add_model, $html_body, $plain_text_body);
                                        $error = true;
                                    }
                                } elseif ($add_model == 'Document') {
                                    //add new document
                                    $document= Documents::buildModel($this->registry);
                                    if (!empty($add_model_settings['transform_equals']['b'])) {
                                        //set constant basic vars
                                        $this->setBasicVals($document, $add_model_settings['transform_equals']['b']);
                                    }
                                    if (!empty($add_model_settings['transform_calc']['b'])) {
                                        //calculate basic vars
                                        $this->calcBasicVals($document, $add_model_settings['transform_calc']['b']);
                                    }
                                    if (!empty($add_model_settings['transform_settings']['sbb'])) {
                                        //copy data from sent box to basic vars
                                        $this->setBasicVars($document, $add_model_settings['transform_settings']['sbb'], $sentbox_data);
                                    }
                                    if (!empty($add_model_settings['transform_settings']['msgb'])) {
                                        //copy data from email message to basic vars
                                        $this->setBasicVars($document, $add_model_settings['transform_settings']['msgb'], $params);
                                    }
                                    if (!empty($add_model_settings['transform_settings']['bb'])) {
                                        //copy parent basic vars to basic vars
                                        $this->setBasicVars($document, $add_model_settings['transform_settings']['bb'], $parent_basic_vars);
                                    }
                                    if (!empty($add_model_settings['transform_settings']['ab'])) {
                                        //copy parent additional vars to basic vars
                                        $this->setBasicVars($document, $add_model_settings['transform_settings']['ab'], $parent_additional_vals);
                                    }
                                    if ($document->get('type') && !$document->get('name')) {
                                        // get the default name from the type
                                        $sql = 'SELECT name FROM ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' WHERE parent_id=' . $document->get('type');
                                        $document->set('name', $db->GetOne($sql), true);
                                    }

                                    // load i18n files for documents
                                    $lang_file_document = sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $this->registry['lang'], '/documents.ini');
                                    $this->loadI18NFiles($lang_file_document);

                                    //save document
                                    if ($document->save()) {
                                        $document->set('plain_vars', null, true);
                                        $document->getVars();
                                        $tmp_vars = $document->get('vars');

                                        $source_additional_vars = array();
                                        foreach ($tmp_vars as $key => $var) {
                                            $source_additional_vars[$var['name']] = $var;
                                        }
                                        $destination_additional_vars = array();
                                        if (!empty($add_model_settings['transform_equals']['a'])) {
                                            //set constant additional vars
                                            $destination_additional_vars = $this->setAdditionalVals($destination_additional_vars, $add_model_settings['transform_equals']['a'], $source_additional_vars);
                                        }
                                        if (!empty($add_model_settings['transform_calc']['a'])) {
                                            //calculate additional vars
                                            $destination_additional_vars = $this->calcAdditionalVals($destination_additional_vars, $add_model_settings['transform_calc']['a'], $source_additional_vars);
                                        }
                                        if (!empty($add_model_settings['transform_settings']['sba'])) {
                                            //copy data from sent box to additional vars
                                            $destination_additional_vars = $this->setAdditionalVars($destination_additional_vars, $add_model_settings['transform_settings']['sba'], $sentbox_data, $source_additional_vars);
                                        }
                                        if (!empty($add_model_settings['transform_settings']['msga'])) {
                                            //copy data from email message to additional vars
                                            $destination_additional_vars = $this->setAdditionalVars($destination_additional_vars, $add_model_settings['transform_settings']['msga'], $params, $source_additional_vars);
                                        }
                                        if (!empty($add_model_settings['transform_settings']['aa'])) {
                                            //copy parent additional vars to additional vars
                                            $destination_additional_vars = $this->setAdditionalVars($destination_additional_vars, $add_model_settings['transform_settings']['aa'], $parent_additional_vals, $source_additional_vars);
                                        }
                                        if (!empty($add_model_settings['transform_settings']['ba'])) {
                                            //copy parent basic vars to additional vars
                                            $destination_additional_vars = $this->setAdditionalVars($destination_additional_vars, $add_model_settings['transform_settings']['ba'], $parent_basic_vars, $source_additional_vars);
                                        }
                                        //save additional vars
                                        if (!empty($destination_additional_vars)) {
                                            $document->set('vars', $destination_additional_vars, true);
                                            $document->saveVars();
                                        }
                                        $filters = array('where' => array('d.id = ' . $document->get('id')));
                                        $document = Documents::searchOne($this->registry, $filters);
                                        //add attachments
                                        if (!empty($attachments)) {
                                            foreach ($attachments as $key => $attachment) {
                                                $this->attachFileToModel($attachment, $filenames[$key], $document);
                                            }
                                        }
                                        //add history and audit
                                        $audit_parent = Documents_History::saveData($this->registry, array('model' => $document, 'action_type' => 'add', 'new_model' => $document, 'old_model' => $old_model));
                                        //send mail to user
                                        $new_model_id = $document->get('id');
                                        $this->sendEmailOnSuccess($email, $email_user, $add_model, $html_body, $plain_text_body, $new_model_id);
                                        $error = false;
                                    } else {
                                        //send mail to user
                                        $errors_add = $document->messages->getErrors();
                                        if (!empty($errors_add)) {
                                            $html_body = '<span style="color: red;">' . implode('</span><br /><span style="color: red;">', $errors_add) . '</span><br /><br />' . $html_body;
                                        }
                                        $this->sendEmailOnError($email, $email_user, $add_model, $html_body, $plain_text_body);
                                        $error = true;
                                    }
                                }
                                if (!empty($email_user)) {
                                    //restore automation user as current user
                                    $automation_user = Users::searchOne($this->registry,array('where'=>array('u.id=' . PH_AUTOMATION_USER, 'u.hidden=1')));
                                    $this->registry->set('currentUser', $automation_user, true);
                                }
                            }
                            $processed[] = $found;
                        }
                    }
                }
                // if no db error occurred the mail is deleted
                if (!$error) {
                    imap_delete($mbox, $i);
                }
            }
        }
        //do not show errors and notices from imap
        imap_alerts();
        imap_errors();

        // auto expunge setup on connect
        // we can just close
        imap_close($mbox);

        if (count($processed) > 0) {
            $this->logger->info(sprintf($this->log_pattern, count($processed), implode(',',$processed)));
        } else {
            $this->logger->info('No bounced emails!');
        }
        return true;
    }

    public function flattenParts($messageParts, $flattenedParts = array(), $prefix = '', $index = 1, $fullPrefix = true) {

        foreach($messageParts as $part) {
            $flattenedParts[$prefix.$index] = $part;
            if(isset($part->parts)) {
                if($part->type == 2) {
                    $flattenedParts = $this->flattenParts($part->parts, $flattenedParts, $prefix.$index.'.', 0, false);
                }
                elseif($fullPrefix) {
                    $flattenedParts = $this->flattenParts($part->parts, $flattenedParts, $prefix.$index.'.');
                }
                else {
                    $flattenedParts = $this->flattenParts($part->parts, $flattenedParts, $prefix);
                }
                unset($flattenedParts[$prefix.$index]->parts);
            }
            $index++;
        }

        return $flattenedParts;

    }

    public function getPart($connection, $messageNumber, $partNumber, $encoding) {

        $data = imap_fetchbody($connection, $messageNumber, $partNumber);
        switch($encoding) {
            case 0: return $data; // 7BIT
            case 1: return $data; // 8BIT
            case 2: return $data; // BINARY
            case 3: return base64_decode($data); // BASE64
            case 4: return quoted_printable_decode($data); // QUOTED_PRINTABLE
            case 5: return $data; // OTHER
        }


    }

    public function getFilenameFromPart($part, $filename = '') {
        if($part->ifdparameters) {
            foreach($part->dparameters as $object) {
                if(strtolower($object->attribute) == 'filename') {
                    $filename = $object->value;
                }
            }
        }

        if(!$filename && $part->ifparameters) {
            foreach($part->parameters as $object) {
                if(strtolower($object->attribute) == 'name') {
                    $filename = $object->value;
                }
            }
        }

        return $filename;

    }

    public function setBasicVars($model, $settings, $params) {

        foreach ($settings as $key => $val) {
            $model->set($val, $params[$key], true);
        }

        return $model;
    }

    public function setAdditionalVars($a_vars, $settings, $params, $s_vars) {

        foreach ($settings as $key => $val) {
            $a_vars[$val] = @$s_vars[$val];
            $a_vars[$val]['value'] = @$params[$key];
        }

        return $a_vars;
    }

    public function setBasicVals($model, $settings) {

        foreach ($settings as $key => $val) {
            $model->set($key, $val, true);
        }

        return $model;
    }

    public function setAdditionalVals($a_vars, $settings, $s_vars) {

        foreach ($settings as $key => $val) {
            $a_vars[$key] = @$s_vars[$val];
            $a_vars[$key]['value'] = $val;
        }

        return $a_vars;
    }

    public function calcBasicVals($model, $settings) {

        $origin = array(
            'table' => DB_TABLE_SETTINGS,
            'name' => 'add_model_settings',
        );
        foreach ($settings as $key => $val) {
            $model->set($key, EvalString::evaluate($this->registry, $val, array('origin' => $origin)), true);
        }

        return $model;
    }

    public function calcAdditionalVals($a_vars, $settings, $s_vars) {

        $origin = array(
            'table' => DB_TABLE_SETTINGS,
            'name' => 'add_model_settings',
        );
        foreach ($settings as $key => $val) {
            if (array_key_exists($key, $s_vars)) {
                $a_vars[$key] = $s_vars[$key];
                $a_vars[$key]['value'] = EvalString::evaluate($this->registry, $val, array('origin' => $origin));
            }
        }

        return $a_vars;
    }

    private function getAddModelSettings($settings) {
        $rows = $settings;
        //settings additional to additional, additional to basic, basic to additional variables
        $transform_settings = array();
        //set new transform variable to value
        $transform_equals = array();
        //calculate value for transform variable
        $transform_calc = array();

        if (!empty($rows)) {
            //parse rows
            $func_arr = preg_split('/(\n|\r|\r\n)/', $rows);
            foreach ($func_arr as $f_row) {
                //row started with # is comment
                if (empty($f_row) || $f_row[0]=='#') {
                    continue;
                }
                list($key, $value) = preg_split('/\s*\:=\s*/', $f_row);
                if (preg_match('/^transform_(a|b|sb|msg)_(.+)/', $key, $s_matches) &&
                    preg_match('/^([ab])_(type)*([0-9]+)*_*(.+)/', $value, $d_matches)) {
                    //variable settings
                    if (!empty($d_matches[2]) && !empty($d_matches[3])) {
                        //like transform_a_total := a_type2_contracted_total
                        $transform_settings[$s_matches[1].$d_matches[1].$d_matches[3]][$s_matches[2]] = $d_matches[4];
                    } else {
                        //like transform_a_total := a_contracted_total
                        $transform_settings[$s_matches[1].$d_matches[1]][$s_matches[2]] = $d_matches[4];
                    }
                } elseif (preg_match('/^equals_([ab])_(type)*([0-9]+)*_*(.+)/', $key, $d_matches)) {
                    $tmp = trim($value);
                    if (!empty($d_matches[2]) && !empty($d_matches[3])) {
                        //like equals_b_type2_status := closed
                        $transform_equals[$d_matches[1].$d_matches[3]][$d_matches[4]] = $tmp;
                    } else {
                        //like equals_b_status := closed
                        $transform_equals[$d_matches[1]][$d_matches[4]] = $tmp;
                    }
                } elseif (preg_match('/^calc_([ab])_(type)*([0-9]+)*_*(.+)/', $key, $d_matches)) {
                    $tmp = trim($value);
                    if (!empty($d_matches[2]) && !empty($d_matches[3])) {
                        //like calc_b_type2_deadline := date("Y-m-d H:i:s", strtotime("+7 day"))
                        $transform_calc[$d_matches[1].$d_matches[3]][$d_matches[4]] = $tmp;
                    } else {
                        //like calc_b_deadline := date("Y-m-d H:i:s", strtotime("+7 day"))
                        $transform_calc[$d_matches[1]][$d_matches[4]] = $tmp;
                    }
                }
            }
        }

        $result = array(
            'transform_settings' => $transform_settings,
            'transform_equals' => $transform_equals,
            'transform_calc' => $transform_calc
        );

        return $result;
    }

    public function attachFileToModel($attachment, $attfilename, $model) {
        $result = true;
        $tmpfname = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $attfilename;
        $handle = fopen($tmpfname, 'wb');
        fwrite($handle, $attachment);
        fclose($handle);
        $file = array(
            'name'     => $attfilename,
            'type'     => mime_content_type($tmpfname),
            'tmp_name' => $tmpfname,
            'error'    => '',
            'size'     => filesize($tmpfname));
        $file_params = array(
            'id'          => '',
            'name'        => $attfilename,
            'filename'    => $attfilename,
            'description' => $attfilename,
            'revision'    => '',
            'permission'  => 'all');
        $rev_params         = array('model_id' => $model->get('id'),
                                    'filename' => $file['name'],
                                    'origin'   => 'attached');
        $file_params['revision'] = Files::getLatestRevision($this->registry, $rev_params);
        $file_path_function_name = 'define' . str_replace('_', '', $model->modelName) . 'FilePath';
        $file_path = Files::$file_path_function_name($file, $file_params, $model);

        $destination = $file_path['destination'];
        $filename = FilesLib::removeSpecialChars($file_path['new_filename']);
        if (substr($destination, -1) != '/') {
            //put a slash at the end of the destination folder
            $destination .= '/';
        }

        if (!is_dir($destination)) {
            //create destination folder
            FilesLib::createDir($destination);
        }

        //check for restrictions
        if (empty($restrictions['upload_max_filesize'])) {
            $restrictions['upload_max_filesize'] = ini_get('upload_max_filesize');
        }
        $maxfilesize = General::convertBytes($restrictions['upload_max_filesize']);
        if ($file['size'] > $maxfilesize) {
            $result = false;
        }

        if (!empty($restrictions['upload_min_filesize']) && $file['size'] < General::convertBytes($restrictions['upload_min_filesize'])) {
            $result = false;
        }

        //check for extension restrictions
        if (!empty($restrictions['allowed_extensions']) && is_string($restrictions['allowed_extensions'])) {
            $restrictions['allowed_extensions'] = preg_split('#\s*(,|;)\s*#', $restrictions['allowed_extensions']);
        }

        if (empty($restrictions['forbidden_extensions'])) {
            $restrictions['forbidden_extensions'] = array('php', 'js', 'vbs', 'exe', 'com');
        } elseif (is_string($restrictions['forbidden_extensions'])) {
            $restrictions['forbidden_extensions'] = preg_split('#\s*(,|;)\s*#', $restrictions['forbidden_extensions']);
        }

        $fileinfo = pathinfo($file['name']);

        //check for allowed extensions
        if (!empty($restrictions['allowed_extensions']) && !in_array(strtolower($fileinfo['extension']), array_map("strtolower", $restrictions['allowed_extensions']))) {
            $result = false;
        }

        //check for not allowed extensions
        if (!empty($restrictions['forbidden_extensions']) && in_array(strtolower($fileinfo['extension']), array_map("strtolower", $restrictions['forbidden_extensions']))) {
            $result = false;
        }

        if ($result) {
            // If different files have the same name, or the name conflicts after transliteration - add random chars on the end of the filename
            $orig_filename = $filename;
            while(file_exists($destination . $filename)) {
                $fileNameParts = explode('.', $orig_filename);
                $fileNameParts[count($fileNameParts)-2] .= '_'.bin2hex(random_bytes('2'));
                $filename = implode('.', $fileNameParts);
            }
            if (@copy($file['tmp_name'], $destination . $filename)) {
                @chmod($destination . $filename, 0777);
                $file_params['model_id']   = $model->get('id');
                $file_params['model']      = $model->modelName;
                $file_params['model_lang'] = $model->get('model_lang');
                $file_params['filename']   = $file['name'];
                $file_params['path']       = $destination . $filename;
                $file_params['origin']     = 'attached';

                $oAttachment       = new File($this->registry, $file_params);
                $attachment_saved  = $oAttachment->save();
                if (!$attachment_saved) {
                    //remove the file if the save procedure fails
                    //the failure could occur only due to SQL error
                    @unlink($destination . $filename);
                    $result = false;
                } else {
                    $result = $oAttachment->get('id');
                }
            } else {
                $result = false;
            }
        }
        unlink($tmpfname);

        return $result;
    }

    public function sendEmailOnError($email, $user, $model_name, $html_body, $plain_text_body) {
        $template = 'crontab_error_adding_model_from_email';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        $mailer = new Mailer($this->registry);
        $mailer->setTemplate($template);
        $mailer->placeholder->add('to_email', $email);
        $translated_model_name = $this->i18n(strtolower($model_name));
        $mailer->placeholder->add('model_name', $translated_model_name);
        if (!empty($user)) {
            $mailer->placeholder->add('user_name', $user->get('display_name'));
        }
        if (!empty($html_body)) {
            $mailer->placeholder->add('email_message', $html_body);
        } else {
            $mailer->placeholder->add('email_message', $plain_text_body);
        }

        $result = $mailer->send();
    }

    public function sendEmailOnSuccess($email, $user, $model_name, $html_body, $plain_text_body, $new_model_id, $parent_model = '') {
        $template = 'crontab_success_adding_model_from_email';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        $mailer = new Mailer($this->registry);
        $mailer->setTemplate($template);
        $mailer->placeholder->add('to_email', $email);
        $translated_model_name = $this->i18n(strtolower($model_name));
        $mailer->placeholder->add('model_name', $translated_model_name);
        if ($model_name == 'Comment') {
            $view_url = sprintf('%s/index.php?%s=%s&%s=communications&communications=%d&communication_type=comments',
                                            $this->registry['config']->getParam('crontab', 'base_host'),
                                            $this->registry['module_param'],
                                            strtolower($parent_model).'s', strtolower($parent_model).'s',
                                            $new_model_id);
        } else {
            $view_url = sprintf('%s/index.php?%s=%s&%s=view&view=%d',
                                            $this->registry['config']->getParam('crontab', 'base_host'),
                                            $this->registry['module_param'],
                                            strtolower($model_name).'s', strtolower($model_name).'s',
                                            $new_model_id);
        }
        $mailer->placeholder->add('view_url', $view_url);
        if (!empty($user)) {
            $mailer->placeholder->add('user_name', $user->get('display_name'));
        }
        if (!empty($html_body)) {
            $mailer->placeholder->add('email_message', $html_body);
        } else {
            $mailer->placeholder->add('email_message', $plain_text_body);
        }
        $mailer->template['model_name'] = $model_name;
        $mailer->template['model_id'] = $new_model_id;

        $result = $mailer->send();
    }

    public function removeOriginalEmail($message, $sentbox_body, $removeFromSentToSubject = true) {
        //replace &nbsp; with space
        $conv_text = str_replace('&nbsp;', ' ', strip_tags($sentbox_body));
        $conv_text = preg_replace("/\s+/", ' ', trim($conv_text));

        $p_text = strip_tags($message);
        //strip > or | from begining of the lines for some email clients
        $p_text = preg_replace("/\n\s*(>|\|)+/","\n",trim($p_text));
        $p_text = preg_replace("/\s+/",' ',trim($p_text));

        $new_text = $message;
        //check if original text is in the message
        $pos = strpos($p_text,$conv_text);
        if ($pos !== false) {
            //check if original text is in the end of the message
            if (strlen($p_text)-strlen($conv_text) == $pos) {
                $new_text = strip_tags($message);
                $new_text = preg_replace("/\n\s*(>)+/","\n",trim($new_text));
                $new_text = preg_replace("/\n\s*(\|)+/","\n",trim($new_text));
                $a_words = explode(' ', $conv_text);
                $a_words = array_reverse($a_words);
                //strip original text
                foreach ($a_words as $word) {
                    $new_text = substr_replace ($new_text, '', strrpos($new_text,$word), strlen($word));
                }
                $new_text = trim($new_text);
                if ($removeFromSentToSubject) {
                    //remove From: ... Sent: ... To ... Subject: ... added from outlook
                    $new_text = preg_replace('#From:[^\n]+\nSent:[^\n]+\nTo:[^\n]+\nSubject:[^\n]+$#', '', $new_text);
                }
                //ToDo - remove top text from reply from if email client is not outlook
            }
        }

        return $new_text;

    }

    /**
     * @return void
     * @throws Exception
     */
    public function processBouncedEmails(): void
    {
        try {
            try {
                $emailParams = $this->registry['config']->getSectionParams('emails');
                $box = $this->getImapBox($emailParams);
            } catch (\Exception | \Error $e) {
                throw new Exception("bouncedEmails is not configured correctly.", null, $e);
            }
            $isVerboseLog = 'verbose' === ($emailParams['bouncedEmail_log'] ?? '');

            // Time limit 60 minutes
            set_time_limit(60*60);
            $folder = $emailParams['bouncedEmail_folder']??'INBOX';
            $chunkSize = (int) ($emailParams['bouncedEmail_chunk_size']??10);

            try {
                $ageFilterSetting = $this->registry['config']->getParamFromDB('crontab', 'process_bounced_emails_max_age');
                $messagePages = $box->getMessagesFromPath($folder, $chunkSize, $this->getMsgAgeFilter($ageFilterSetting));
                $messagePages->setReverse(true);
            } catch (\Exception | \Error $e) {
                throw new Exception("Can't read IMAP folder!", null, $e);
            }

            try {
                $processed = $this->processBouncedEmailPages($messagePages, $isVerboseLog);
            } catch (\Exception|\Error $e) {
                throw new Exception("Can't process bounced emails pages!", null, $e);
            }

            if ($isVerboseLog) {
                $this->logBouncedEmailsInDb($processed);
                echo $this->renderVerboseLogHtml($processed);
            }
        } catch (\Exception | \Error $e) {
            if ($isVerboseLog??false) {
                echo "<pre>";
                echo $this->prepExceptionText($e);
                echo "</pre>";
            }
            $this->logException('Bounced Emails Error', $e);
        } finally {
            if (isset($box)) {
                // Suppress errors as they are not important at all
                try {
                    $box->expunge();
                    $box->disconnect();
                } catch (\Exception | \Error $e) {}
            }
        }
    }
    private function logException(string $event, \Exception $exception) {
        $msg = $this->prepExceptionText($exception);
        General::log($this->registry, $event, $msg);
    }
    private function prepExceptionText(\Exception $exception) {
        $message = " Exception: " . get_class($exception) . "\n";
        $message .= $exception->getMessage()."\n";
        $message .= " " . $exception->getFile();
        $message .= " at {$exception->getLine()}";
        $message .= "\n\n";
        $message .= $exception->getTraceAsString();
        $previous = $exception->getPrevious();
        if (is_a($previous, \Exception::class)) {
            $message .= "\n\nPrevious:\n";
            $message .= $this->prepExceptionText($previous);
        }
        return $message;
    }

    /**
     * @return Box
     * @throws \Webklex\PHPIMAP\Exceptions\MaskNotFoundException
     * @throws \Exception
     */
    private function getImapBox($settings): Box
    {
        if (empty($settings['replyto_imap_server'])
            || empty($settings['replyto_imap_port'])
            || empty($settings['replyto_imap_cert'])
            || empty($settings['replyto_email'])
        ) {
            $this->logger->error("No imap reply to credentials");
            throw new \Exception('Requred configurations not provided! ');
        }

        $imapConfig = new \Nzoom\Email\Authentication\Config([
            'debug' => false,
            'host' => $settings['replyto_imap_server'],
            'port' => $settings['replyto_imap_port'],
            'cert' => $settings['replyto_imap_cert'],
            'username' => $settings['replyto_email'],
            'password' => $settings['replyto_imap_password']??'',
            'authenticationMethod' => $settings['replyto_imap_authentication_method']??'IMAP',
            'providerConfig' => [
                'provider'      => $settings['replyto_imap_oauth2_provider'],
                'clientId'      => $settings['replyto_imap_oauth2_client_id'],
                'clientSecret'  => $settings['replyto_imap_oauth2_client_secret'],
                'tenantId'      => $settings['replyto_imap_oauth2_tenant_id'],
                'refreshToken'  => $settings['replyto_imap_oauth2_refresh_token'],
            ]
        ]);

        return (new BoxFactory())($imapConfig);
    }

    /**
     * @param string $code
     * @param string $email
     * @return int|null
     */
    private function fetchEmailRecord(string $code, string $email):? int
    {
        $query = "SELECT id FROM " . DB_TABLE_EMAILS_SENTBOX . " WHERE code = '{$code}' AND recipient='{$email}'";
        $id = @$this->registry['db']->GetOne($query);
        return $id ? (int)$id : null;
    }

    /**
     * @param int $emailId
     * @param string $actionString
     * @param string $diagnosticString
     * @return void
     * @throws Exception
     */
    private function updateEmailRecordToBounced(int $emailId, string $actionString, string $diagnosticString): void
    {
        $db = $this->registry['db'];
        $set = [];
        $set[] = sprintf('`status` = "%s"', ($actionString ?? null) ?: 'failed');
        $set[] = sprintf('`bounced` = now()');
        $set[] = sprintf('`diagnostic` = "%s"', \General::slashesEscape(($diagnosticString ?? null) ?: 'Diagnostic string was not found'));
        $setStr = implode(",\n", $set);
        $query = "UPDATE " . DB_TABLE_EMAILS_SENTBOX . " SET\n{$setStr}\n" .
            "WHERE id = '{$emailId}'";
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            throw new Exception("Email record not updated! DB error: {$db->ErrorMsg()}");
        }
    }

    private function getMsgAgeFilter($maxAge): ?array
    {
        $maxAgeFilter = null;
        if ($maxAge) {
            $timeFilter = new DateTime('NOW');
            $timeFilter->modify('-' . $maxAge);
            $maxAgeFilter = ['SINCE' => $timeFilter->format('Y-m-d')];
        }
        return $maxAgeFilter;
    }

    private function processBouncedEmail(\Webklex\PHPIMAP\Message $message, $dryRun = false): array
    {
        $mask = $message->mask();
        $code = $mask->getCode();
        $isBounced = $mask->isBounced();
        $emailAddresess = $mask->getMails();

        $processed = [
            'nzoomCode' => $code,
            'isBounced' => $isBounced,
            'addresses' => $emailAddresess,
            'subject' => $mask->getSubject(),
            'body' => $mask->getBetterBody(),
            'email_sentbox.id' => [],
            'passedValidation' => false,
            'shouldDeleteMessage' => false
        ];

        if (empty($code) || !$isBounced || empty($emailAddresess)) {
            return $processed;
        }
        $processed['passedValidation'] = true;
        $processedIds = [];
        $shouldDeleteMessage = true;
        foreach ($emailAddresess as $key => $email) {
            $recordId = $this->processBouncedEmailForSender($mask, $key, $email);
            if ($recordId) {
                $processedIds[] = $recordId;
            } else {
                $shouldDeleteMessage = false;
            }
        }
        $processed['email_sentbox.id'] = $processedIds;

        if ($dryRun) {
            $processed['shouldDeleteMessage'] = $shouldDeleteMessage;
        } elseif ($shouldDeleteMessage) {
            $message->delete(true);
        }

        return $processed;
    }

    /**
     * @param Message $message
     * @param $key
     * @param string $email
     * @return int|null
     */
    private function processBouncedEmailForSender(Message $message, $key, string $email):? int
    {
        $code = $message->getCode();
        $emailId = $this->fetchEmailRecord($code, $email);
        if (!$emailId) {
            return null;
        }

        try {
            $actions = $message->getAction();
            $diagnostic = $message->getDiagnosticCodes();
            $this->updateEmailRecordToBounced($emailId,
                ($actions[$key] ?? null) ?: 'failed',
                ($diagnostic[$key] ?? null) ?: 'Diagnostic string was not found');
        } catch(\Exception $e) {
            General::log(
                $this->registry,
                'bouncedEmails',
                "Can not update bounced mail reecord! \n Error:\n". var_export($e, true));
            return null;
        }

        $this->updateParentEmail($emailId);
        return $emailId;
    }

    /**
     * @param array $processed
     * @return void
     */
    private function logBouncedEmailsInDb(array $processed): void
    {
        $list=[];
        foreach ($processed as $message) {
            $subject = mb_substr($message['subject']??'',0, 100);
            $from = implode(', ', $message['addresses']??[]);
            $list[] = "Subject: {$subject}, from: {$from}";
        }

        $messageString = implode("\n", $list);
        $logMessage = count($processed) === 0
            ? 'No emails processed!'
            : sprintf("Processed emails:(%s)\nProcessing details:\n%s", count($processed), $messageString);
        General::log($this->registry, 'bouncedEmails', $logMessage);
    }

    /*
     * Function used to update parent mails if the returned mail has been a resent copy of other mail
     */
    public function updateParentEmail($current_mail) {
        $db = $this->registry['db'];

        // finds out if this is a resent copy of the mail or it is the original mail
        $sql_current_record = 'SELECT resent_mail_id FROM ' . DB_TABLE_EMAILS_SENTBOX . ' WHERE id="' . $current_mail . '"';
        $parent_mail = $db->GetOne($sql_current_record);

        if ($parent_mail) {
            $update_mail = $parent_mail;
        } else {
            $update_mail = $current_mail;
        }

        // updates the resent field of the parent mail
        $query = 'UPDATE ' . DB_TABLE_EMAILS_SENTBOX . ' SET `resent`=0' . "\n" .
                 "WHERE id = " . $update_mail;
        $db->Execute($query);

        return true;
    }

    /**
     * @param array $processed
     * @return string
     */
    private function renderVerboseLogHtml(array $processed): string
    {
        $tab = "&nbsp;&nbsp;";
        $nl = "<br>";
        $buffer = "<h1>Verbose mode</h1>";
        $buffer .= "No emails are deleted! Run the automation in normal mode to perform deletions!{$nl}";
        $buffer .= "Processed <strong>" . count($processed) . "</strong> emails{$nl}";
        foreach ($processed as $k => $p) {
            $buffer .= "<hr><br>";
            $buffer .= ($k+1) . " Email message \"<strong>{$p['subject']}</strong>\"{$nl}";
            $buffer .= "{$tab}nzoomCode: <strong>{$p['nzoomCode']}</strong>{$nl}";
            $buffer .= "{$tab}isBounced: <strong>" . ($p['isBounced'] ? 'true' : 'false') . "</strong>" . $nl;
            $buffer .= "{$tab}passedValidation: <strong>" . ($p['passedValidation'] ? 'true' : 'false') . "</strong>" . $nl;
            $buffer .= "{$tab}shouldDeleteMessage: <strong>" . ($p['shouldDeleteMessage'] ? 'true' : 'false') . "</strong> (No actual deletions in verbouse mode!)" . $nl;
            $buffer .= "{$tab}addresses: (" . count($p['addresses']) . "){$nl}";
            foreach ($p['addresses'] as $n => $a) {
                $buffer .= "{$tab}{$tab}{$n}: <code>{$a}</code>{$nl}";
            }
            $buffer .= "{$tab}email_sentbox.id (" . count($p['email_sentbox.id']) . ")<br>";
            foreach ($p['email_sentbox.id'] as $n => $a) {
                $buffer .= "{$tab}{$tab}{$n}: <code>{$a}</code>{$nl}";
            }
            $buffer .= "{$tab}body: ";
            $buffer .= "<div style='margin-left:1rem; border: 2px solid red; width:max-content'>{$p['body']}</div><br><br><hr>";
        }
        return $buffer;
    }

    /**
     * Check for IMAP errors and throw exception if any
     *
     * @param $message
     * @return void
     * @throws Exception
     */
    private function checkForImapErrorsAndThrow($message): void
    {
        if (!extension_loaded('imap')) {
            return;
        }
        $imapErrors = imap_errors();
        if (!empty($imapErrors)) {
            $subject = '-';
            $from = '-';
            if (isset($message)) {
                $mask = $message->mask();
                $subject = isset($mask) ? mb_substr($mask->getSubject(), 0, 100) : '-';
                $from = isset($mask) ? ((string)$mask->getFrom()) : '-';
            }
            throw new \Exception(
                "Can't process message! Subject: '{$subject}' From: '{$from}' - " . implode('; ', $imapErrors));
        }
    }

    /**
     * @param $messagePages
     * @param bool $isVerboseLog
     * @return array
     * @throws Exception
     */
    private function processBouncedEmailPages($messagePages, bool $isVerboseLog): array
    {
            $processed = [];
            $errors = [];
            foreach ($messagePages as $messages) {
                foreach ($messages as $message) {
                    try {
                        $processedInfo = $this->processBouncedEmail($message, $isVerboseLog);
                        // Only collect information if verbose log is enabled
                        if ($isVerboseLog) {
                            $processed[] = $processedInfo;
                        }
                        // If IMAP extension is loaded, there is a possibility of notices and errors,
                        // that ar not well controlled in the IMAP-PHP, so we need to check for them,
                        // and suppress them
                        $this->checkForImapErrorsAndThrow($message);
                        unset($message);
                    } catch (\Exception|\Error $e) {
                        // Just collect errors and continue as normal to the next message/page.
                        $errors[] = $e;
                    }
                }
            }

            if (!empty($errors)) {
                $errorString = "Runtime Errors:\n\n";
                foreach ($errors as $error) {
                    $errorString .= "====================\n";
                    $errorString .= $this->prepExceptionText($error);
                }
                General::log($this->registry, 'Bounced Emails Runtime Error', $errorString);
            }

        return $processed;
    }
}

?>
