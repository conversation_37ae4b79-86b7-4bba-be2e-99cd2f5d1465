<?php

require_once PH_MODULES_DIR . 'finance/models/finance.recurring_payments.factory.php';

/**
 * Crontabs model class
 */
Class Finance_Crontab extends Model {
    public $modelName = 'Finance_Crontab';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        $this->logger = new Logger($this->modelName,
        'crontab/' . $params['action']. '_' .date('Y_m').'_cron' . '.log');

        $start_text = "\n======================\n"." (".date('Y-m-d H:i:s').")";
        $this->logger->info($start_text);

        //$this->log_pattern = ":(%s); :%s; :%s";
        //$this->err_log_pattern = ":(%s); :%s; !!!:%s";

        //set lang
        $this->lang = $this->registry['lang'];
        $this->registry->set('currentUser',Users::searchOne($this->registry,array('where'=>array('u.id=' . PH_AUTOMATION_USER, 'u.hidden=1'))), true);
    }

   /**
    * getRecurringPayments() - get recurring payments
    *
    * @param array $filters - arrays to filter
    * @return array with results
    */
    function getRecurringPayments($filters = array()) {
        //get recurring payments
        $byday_names = array ( 0 => 'SU', 1 => 'MO', 2 => 'TU', 3 => 'WE', 4 => 'TH', 5 => 'FR', 6 => 'SA' );

        $sql['select'] = 'SELECT frp.id as idx, ' . "\n" .
                         '  frp.*, frpi18n.* ' . "\n";

        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_RECURRING_PAYMENTS . ' AS frp' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_RECURRING_PAYMENTS_I18N . ' AS frpi18n' . "\n" .
                       '  ON (frp.id=frpi18n.parent_id AND frpi18n.lang="' . $this->lang . '")'. "\n";

        $sql['where'] = 'WHERE start_date <= now() AND end_date >= now() AND crontab_last < CURDATE() AND frp.deleted=0';

        $query = implode("\n", $sql);
        $recurrence_records = $this->registry['db']->GetAssoc($query);
        foreach ($recurrence_records as $key => $record) {
            if ($this->checkRecurringDate($record)) {

            } else {
                unset($recurrence_records[$key]);
            }
        }

        return $recurrence_records;
    }

    /**
     * checkRecurringDate
     *
     * @return bool - result of the operation
     */
    public function checkRecurringDate($record) {
        $db = $this->registry['db'];

        $result = false;

        $recurrence_type = $record['recurrence_type'];
        if ($recurrence_type == 'daily') {
            $result = true;
        } elseif ($recurrence_type == 'monthlyByDate') {
            if (date('d') == sprintf('%02d',$record['offset'])) {
                $result = true;
            }
        } elseif ($recurrence_type == 'weekly') {
            if (date('N') == $record['offset']) {
                $result = true;
            }
        } elseif ($recurrence_type == 'yearly') {
            if (date('z') == $record['offset']-1) {
                $result = true;
            }
        }

        return $result;
    }

    /**
     * recurringPayments
     *
     * @return bool - result of the operation
     */
    public function recurringPayments() {
        $template = 'crontab_recurring_payments';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        $db = $this->registry['db'];

        $recurrence_records = $this->getRecurringPayments();

        $db->StartTrans();
        foreach ($recurrence_records as $id => $record) {
            $recurring_payment = Finance_Recurring_Payments::searchOne($this->registry, array(
                                    'where' => array('frp.id=' . $id)));
            if ($recurring_payment->addReason()) {
                $query = 'UPDATE ' . DB_TABLE_FINANCE_RECURRING_PAYMENTS . ' SET crontab_last = now() WHERE id=' . $id;
                $db->Execute($query);
            }
        }

        /*$mailer = new Mailer($this->registry);
        foreach ($users as $user_id => $user) {
            $mailer->setTemplate($template);
            $this->logData($events, $user_id, $user, $sent, $mailer);
        }*/

        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();
        $result = !$dbTransError;

        return $result;
    }

    /* Get the dates the correspond to the byday values.
     *
     * @param array $byday   ByDay values to process (MO,TU,-1MO,20MO...)
     * @param string $cdate  First day of target search (Unix timestamp)
     * @param string $type   Month, Year, Week (default = month)
     * @param string $date   First day of event (Unix timestamp)
     *
     * @return array  Dates that match ByDay (YYYYMMDD format).
     */
    public function getByDay ( $byday, $cdate, $type = 'month', $date ) {
      $byday_names = array ( 0 => 'SU', 1 => 'MO', 2 => 'TU', 3 => 'WE', 4 => 'TH', 5 => 'FR', 6 => 'SA' );
      $byday_values = array ( 'SU' => 0, 'MO' => 1, 'TU' => 2, 'WE' => 3, 'TH' => 4, 'FR' => 5, 'SA' => 6 );
      //$byday = array ( 0 => '5WE' );

      if ( empty ( $byday ) )
        return;

      $ret = array ();
      $hour = date ( 'H', $cdate );
      $minute = date ( 'i', $cdate );
      $mth = date ( 'm', $cdate );
      $yr = date ( 'Y', $cdate );
      if ( $type == 'month' ) {
        $ditype = date ( 't', $cdate ); //Days in month.
        $fday = mktime ( 0, 0, 0, $mth, 1, $yr ); //First day of month.
        $lday = mktime ( 0, 0, 0, $mth + 1, 0, $yr ); //Last day of month.
        $month = $mth;
      } elseif ( $type == 'year' ) {
        $ditype = date ( 'L', $cdate ) + 365; //Days in year.
        $fday = mktime ( 0, 0, 0, 1, 1, $yr ); //First day of year.
        $lday = mktime ( 0, 0, 0, 12, 31, $yr ); //Last day of year.
        $month = 1;
      } elseif ( $type == 'daily' ) {
        $fday = $lday = $cdate;
        $month = $mth;
      } else
        // We'll see if this is needed.
        return;

      $fdow = date ( 'w', $fday ); //Day of week first day of $type.
      $ldow = date ( 'w', $lday ); //Day of week last day of $type
      foreach ( $byday as $day ) {
        $byxxxDay = '';
        $dayTxt = substr ( $day, -2, 2 );
        $dayOffset = substr_replace ( $day, '', -2, 2 );
        $dowOffset = ( ( -1 * $byday_values[$dayTxt] ) + 7 ) % 7; //SU=0, MO=6, TU=5...
        if ( is_numeric ( $dayOffset ) && $dayOffset > 0 ) {
          // Offset from beginning of $type.
          $dayOffsetDays = ( ( $dayOffset - 1 ) * 7 ); //1 = 0, 2 = 7, 3 = 14...
          $forwardOffset = $byday_values[$dayTxt] - $fdow;
          if ( $forwardOffset < 0 )
            $forwardOffset += 7;

          $domOffset = ( 1 + $forwardOffset + $dayOffsetDays );
          if ( $domOffset <= $ditype ) {
            $byxxxDay = mktime ( $hour, $minute, 0, $month, $domOffset, $yr );
            if ( $mth == date ( 'm', $byxxxDay ) && $byxxxDay > $date )
              $ret[] = $byxxxDay;
          }
        } else
        if ( is_numeric ( $dayOffset ) ) { // Offset from end of $type.
          $dayOffsetDays = ( ( $dayOffset + 1 ) * 7 ); //-1 = 0, -2 = 7, -3 = 14...
          $byxxxDay = mktime ( $hour, $minute, 0, $month + 1,
            ( 0 - ( ( $ldow + $dowOffset ) % 7 ) + $dayOffsetDays ), $yr );
          if ( $mth == date ( 'm', $byxxxDay ) && $byxxxDay > $date )
            $ret[] = $byxxxDay;
        } else {
          if ( $type == 'daily' ) {
            if ( ( date ( 'w', $cdate ) == $byday_values[$dayTxt] ) && $cdate > $date )
              $ret[] = $cdate;
          } else {
            for ( $i = 1; $i <= $ditype; $i++ ) {
              $loopdate = mktime ( $hour, $minute, 0, $month, $i, $yr );
              if ( ( date ( 'w', $loopdate ) == $byday_values[$dayTxt] ) &&
                $loopdate > $date ) {
                $ret[] = $loopdate;
                $i += 6; //Skip to next week.
              }
            }
          }
        }
      }
      return $ret;
    }


    /**
     * Update currencies rates
     *
     * @return bool - result of the operation
     */
    public function currenciesUpdateRates() {
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

        $result = true;

        //check if the rates have been fetched already
        if (!Finance_Currencies::checkCurrenciesRates($this->registry)) {
            $rates = Finance_Currencies::updateRates($this->registry);

            if (empty($rates)) {
                $result = false;
            }
        }

        return $result;
    }

    /**
     * Sends notification for not distributed bank payments
     */
    public function notifyForNotDistributedPayments() {
        $template = 'not_distributed_payments';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        //get some payments depending of the filters
        $date = General::strftime('%Y-%m-%d', strtotime('-' . $this->registry['config']->getParamFromDB('finance', 'payments_distribution_time'), time()));
        $types = preg_split('#\s*,\s*#', $this->registry['config']->getParamFromDB('finance', 'payments_distribution_types'));
        $tag = $this->registry['config']->getParamFromDB('finance', 'payments_system_tag');

        // search for
        $filters = array('where' =>
            array(
                // finished payments
                'fp.status = "finished"',
                // not annulled
                'fp.annulled_by = 0',
                // not fully distributed OR distributed not only on invoices/debit notes
                '(fp.invoices_only = 0 OR fp.distribution_status != "distributed")',
                // that are issued before the date calculated (1 day(default) and more)
                'fp.issue_date <="' . $date . '"',
                // and are of the types provided as setting
                'fp.type IN ("' . implode('", "', $types) . '")'
            )
        );
        if (!empty($tag)) {
            $filters['where'][] = "tags.tag_id != '" . $tag . "'";
        }
        require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
        $payments = Finance_Payments::search($this->registry, $filters);

        $db = &$this->registry['db'];

        //group payments in function of the modified_by
        $grouped = array();
        foreach ($payments as $payment) {
            if (!isset($grouped[$payment->get('modified_by')])) {
                $user_name = preg_split('#\s#', $payment->get('modified_by_name'), 2);
                $grouped[$payment->get('modified_by')]['user_name'] = $user_name[0];
                $grouped[$payment->get('modified_by')]['payments'] = array();
            }
            $grouped[$payment->get('modified_by')]['payments'][] = $payment;
        }

        $not_users = Users::getUsersNoSend($this->registry, $template);

        //get email template
        $filters = array('where' => array('e.name = "' . $template . '"'),
                         'sanitize' => true);
        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $mail = Emails::searchOne($this->registry, $filters);
        $link_base = $this->registry['config']->getParam('crontab', 'base_host') . '/index.php';
        $link = sprintf('%s?%s=finance&amp;%s=payments&amp;payments=search&amp;search_finance_payments=1' .
                        '&amp;search_module=finance&amp;search_controller=payments',
                        $link_base, $this->registry['module_param'], $this->registry['controller_param']);
        $i = 0;
        foreach($types as $k => $t) {
            $types[$k] = "&amp;search_fields[$i]=fp.type&amp;compare_options[$i]=" . urlencode("= '%s'") . "&amp;values[$i]=$t&amp;logical_operator[$i]=";
            $i++;
        }
        $link .= implode('OR', $types) . 'AND';
        $link .= "&amp;search_fields[$i]=fp.modified_by&amp;compare_options[$i]=" . urlencode("= '%s'") . "&amp;values[$i]=currentUser&amp;logical_operator[$i]=AND";
        $i++;
        $link .= "&amp;search_fields[$i]=fp.status&amp;compare_options[$i]=" . urlencode("= '%s'") . "&amp;values[$i]=finished&amp;logical_operator[$i]=AND";
        $i++;
        $link .= "&amp;search_fields[$i]=fp.issue_date&amp;compare_options[$i]=" . urlencode("<= DATE_SUB(CURDATE(), INTERVAL 1 DAY)") . "&amp;logical_operator[$i]=AND";
        $i++;
        $link .= "&amp;search_fields[$i]=fp.invoices_only&amp;compare_options[$i]=" . urlencode("= '%s'") . "&amp;values[$i]=0&amp;logical_operator[$i]=OR";
        $i++;
        $link .= "&amp;search_fields[$i]=fp.distribution_status&amp;compare_options[$i]=" . urlencode("!= '%s'") . "&amp;values[$i]=distributed&amp;logical_operator[$i]=AND";
        if ($tag) {
            $i++;
            $link .= "&amp;search_fields[$i]=tags.tag_id&amp;compare_options[$i]=" . urlencode("!= '%s'") . "&amp;values[$i]=$tag&amp;logical_operator[$i]=AND";
        }
        $link .= '&amp;sort[0]=fp.added&amp;order[0]=DESC&amp;display=10';

        foreach ($grouped as $key => $group) {
            if (in_array($key, $not_users)) {
                // user does not want to be notified
                continue;
            }
            $viewer = new Viewer($this->registry);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->template = 'not_distributed_payments.html';
            $viewer->data['payments'] = $group['payments'];
            $viewer->data['link_location'] = $link_base;
            $user_mail = $db->GetOne('SELECT email FROM ' . DB_TABLE_USERS . ' WHERE id = ' . $key);

            $mailer = new Mailer($this->registry, $mail->get('name'));
            $mailer->placeholder->add('detailed_payments_info', $viewer->fetch());
            $mailer->placeholder->add('report_link', $link);
            $mailer->placeholder->add('user_name', $group['user_name']);
            $mailer->placeholder->add('to_email', $user_mail);
            $mailer->send();
        }
        return true;
    }

    /**
     * Finish reservations
     *
     * @return bool - result of the operation
     */
    public function finishReservations() {
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';

        $filters = array('where' => array('fwd.type=' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION,
                                          'fwd.date < "' . date('Y-m-d') . '"',
                                          'fwd.status="locked"'),
                         'sanitize' => true);
        $reservations = Finance_Warehouses_Documents::search($this->registry, $filters);

        foreach ($reservations as $reservation) {
            $reservation->finishReservation();
        }

        return true;
    }

    /**
     * Sends notification for changed payment status of revenue documents (type > 100)
     */
    public function notifyPaymentStatus() {
        $template = 'finance_incomes_reason_payment_status';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        $db = &$this->registry['db'];

        $notify_interval = $this->registry['config']->getParam('crontab', 'notify_payment_status_interval');
        $notify_types = $this->registry['config']->getParam('crontab', 'notify_payment_status_types');

        // get incomes reasons
        $query = 'SELECT fir.id AS idx, fir.id, fir.num, fir.payment_status, fir.payment_status_modified,' . "\n" .
                 '  firi18n.customer_name, fdti18n.name AS type_name,' . "\n" .
                 '  (SELECT MAX(sent) FROM crontab WHERE model="finance_incomes_reason" AND model_id=fir.id AND about="payment_status") AS lsent' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir'. "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n'. "\n" .
                 '  ON fir.id=firi18n.parent_id AND firi18n.lang="' . $this->lang . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                 '  ON fir.type=fdti18n.parent_id AND fdti18n.lang="' . $this->lang . '"' . "\n" .
                 'WHERE fir.status="finished" AND fir.annulled_by=0 AND fir.total_with_vat > 0 ' . "\n" .
                 '  AND fir.type > ' . PH_FINANCE_TYPE_MAX .
                 ($notify_types ? ' AND fir.type IN (' . $notify_types . ')' : '') . "\n" .
                 '  AND fir.payment_status_modified != "0000-00-00 00:00:00"' .
                 ($notify_interval ? ' AND fir.payment_status_modified >= DATE_SUB(NOW(), INTERVAL ' . $notify_interval . ')' : '') . "\n" .
                 'GROUP BY fir.id' . "\n" .
                 'HAVING lsent < fir.payment_status_modified OR lsent IS NULL';
        $reasons = $db->getAssoc($query);

        if (!$reasons) {
            return true;
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.model.php';

        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $this->lang . '/finance.ini');

        $this->registry->set('getAssignments', true, true);

        // collect data for all notified users and reuse it
        $notify_users = array();

        // get users whom not to send emails to
        $not_users = Users::getUsersNoSend($this->registry, $template);

        $mailer = new Mailer($this->registry);

        $this->log_pattern = "finance_incomes_reason:(%s); user:%s; email:%s; about:%s";
        $this->err_log_pattern = "finance_incomes_reason:(%s); user:%s; !!!email:%s; about:%s";

        $db->StartTrans();

        foreach ($reasons as $id => $record) {
            $reason = new Finance_Incomes_Reason($this->registry, $record);

            // collect unique users assigned to model
            $assignments = $reason->get('assignments_owner') +
                           $reason->get('assignments_responsible') +
                           $reason->get('assignments_observer') +
                           $reason->get('assignments_decision');

            $users_ids = array();
            foreach ($assignments as $user_info) {
                if (!isset($notify_users[$user_info['assigned_to']])) {
                    $users_ids[] = $user_info['assigned_to'];
                }
            }
            if ($users_ids) {
                $query = 'SELECT id, email FROM ' . DB_TABLE_USERS . "\n" .
                         'WHERE id in (' . implode(',', $users_ids) . ') AND active=1 AND deleted_by=0';
                $emails = $db->GetAssoc($query);
                foreach ($emails as $u_id => $email) {
                    if (!in_array($u_id, $not_users)) {
                        $notify_users[$u_id] = array('id' => $u_id, 'email' => $email, 'name' => $assignments[$u_id]['assigned_to_name']);
                    }
                }
            }

            $recipients = array();
            foreach ($assignments as $user_id => $user_info) {
                if (array_key_exists($user_id, $notify_users)) {
                    $recipients[$user_id] = $notify_users[$user_id];
                }
            }
            if (!$recipients) {
                continue;
            }

            // set values for placeholders for e-mail
            $model_view_url = sprintf('%s/index.php?%s=finance&controller=incomes_reasons&incomes_reasons=view&view=%d',
                                        $this->registry['config']->getParam('crontab', 'base_host'),
                                        $this->registry['module_param'], $record['id']);
            $add_comment_url = sprintf('%s/index.php?%s=finance&controller=incomes_reasons&incomes_reasons=communications&communications=%d&communication_type=comments#comments_add_form',
                                        $this->registry['config']->getParam('crontab', 'base_host'),
                                        $this->registry['module_param'], $record['id']);

            $mailer->placeholder->add('customer_name', $record['customer_name']);
            $mailer->placeholder->add('finance_incomes_reason_num', $record['num']);
            $mailer->placeholder->add('finance_incomes_reason_payment_status', $this->registry['translater']->translate('finance_payment_status_' . $record['payment_status']));
            $mailer->placeholder->add('finance_incomes_reason_type', $record['type_name']);
            $mailer->placeholder->add('finance_incomes_reason_view_url', $model_view_url);
            $mailer->placeholder->add('finance_incomes_reason_add_comment_url', $add_comment_url);

            $mailer->setTemplate($template);
            $mailer->template['model_name'] = $reason->modelName;
            $mailer->template['model_id'] = $record['id'];

            $record['model_name'] = strtolower($reason->modelName);

            foreach ($recipients as $user_id => $user) {
                $this->logData($record, $user, $mailer, 'payment_status');
            }
        }

        $this->registry->Set('getAssignments', false, true);

        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Log data in file and DB
     *
     * @return bool - result of the operation
     */
    public function logData(&$record, &$user, &$mailer, $about) {

        $db = $this->registry['db'];

        $mailer->placeholder->add('to_email', $user['email']);
        $mailer->placeholder->add('user_name', $user['name']);
        //set data for saving in emails_sentbox
        //$mailer->template['extra'] = 'source := crontab';
        $mailer->template['recipient_name'] = $user['name'];

        $result = $mailer->send();
        if (!@in_array($user['email'], $result['erred'])) {
            $set = array();
            $set['recipient_id'] = $user['id'];
            $set['model'] = sprintf("'%s'", $record['model_name']);
            $set['model_id'] = $record['id'];
            $set['sent'] = "now()";
            $set['about'] = "'$about'";

            $query = "INSERT INTO " . DB_TABLE_CRONTAB .
                     " (recipient_id, model, model_id, sent, about) VALUES (" . implode(', ', $set) . ")";
            $db->Execute($query);

            $this->logger->info(sprintf($this->log_pattern,
                                $record['id'], $user['name'], $user['email'], $about));
            return true;
        } else {
            $this->logger->error(sprintf($this->err_log_pattern,
                                 $record['id'], $user['name'], $user['email'], $about));
            return false;
        }
    }
}

?>
