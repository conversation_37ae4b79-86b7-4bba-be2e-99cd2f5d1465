<?php

class Crontab_Controller extends Controller {
    /**
     * Action name one of the add, edit, delete, list, etc.
     */
    public $defaultAction = '';

    /**
     * Model name of this controller
     */
    public $modelName = 'Crontab';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Crontabs';

    /**
     * Collected execution errors
     * @var array
     */
    private $executionErrors = array();

    /**
     * Actions included in Main crontab - methods corresponding to actions are
     * executed when crontab is run with no action specified
     * @var array
     */
    private $_mainCrons = array(
        'notify_before_password_expiration',
        'expired_deadlines',
        'before_deadlines',
        'expired_validity_terms',
        'before_validity_terms',
        'tasks_notifications',
        'expired_phases',
        'expired_events',
        'budgets_expired_deadlines',
        'budgets_before_deadlines',
        'minitasks_expired_deadlines',
        'minitasks_before_deadlines',
        //contracts
        'start_contracts_agreements',
        'end_contracts_agreements',
        'notify_start_contracts',
        'notify_end_contracts',
        //finance
        'recurring_payments',
        'finish_reservations',
        /*'not_distributed_payments',*/
        //forthcoming_events should be last but one action!
        'forthcoming_events',
    );

    /**
     * Function to change the error handler and shutdown handler with
     *
     * @param string $code - error code
     */
    public function onError($code = false) {
        // Fail any transactions that may be opened
        $this->endTransactions();

        // If error reporting is currently turned off or suppressed with @
        if (error_reporting() !== 0) {
            $error = error_get_last();
            if (!is_null($error)) {
                $key = base64_encode(sha1($error['file'] . $error['line'] . $error['message']));
                //don't care for errors of calling non-static methods statically and for errors in the /ext folder.
                //if we need to care about these errors, we will have lots of work to do.
                if (!array_key_exists($key, $this->executionErrors) &&
                    !preg_match('#non-static|chmod#i', $error['message']) &&
                    !preg_match('#(/inc/ext/|\\\inc\\\ext\\\)#', $error['file']) &&
                    !preg_match('#(/cache/tmpc/|\\\cache\\\tmpc\\\)#', $error['file']) &&
                    !preg_match('#^Declaration of .* should be compatible with that of .*$#', $error['message'])) {
                    $this->executionErrors[$key] = '<pre>' . print_r($error, true) . '</pre>';
                }
            }
        }

        if ($code == 'fatal') {
            $this->sendExecutionErrors();
            $this->finishCurrentTasks();
            exit;
        }
        return false;
    }

    /**
     * Send Notification email about crontab execution
     *
     * @return bool - result of operation
     */
    public function sendExecutionErrors() {

        // get recipients
        $mails = $this->registry['config']->getParam('emails', 'crontab_errors');

        if (empty($this->executionErrors) || empty($mails)) {
            return true;
        }

        //prepare mailer
        $template = 'crontab_errors_notification';
        $model = new Model($this->registry);
        if (!$model->shouldSendEmail($template)) {
            return true;
        }

        $mailer = new Mailer($this->registry, $template);
        $mailer->placeholder->add('action', $this->action);
        $mailer->placeholder->add('errors', implode('<br />', $this->executionErrors));
        $mailer->placeholder->add('to_email', $mails);
        $mailer->placeholder->add('installation', $this->registry['config']->getParam('sys', 'code'));

        //send email
        $result = $mailer->send();

        // reset execution errors for next crontab method to be executed
        $this->executionErrors = array();

        return true;
    }

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        // check execution permissions
        if ($this->action == 'bounced_emails') {
            // bounced emails will be disabled only if do_not_send emails option is enabled
            $dns = $this->registry['config']->getParam('emails', 'do_not_send');
            if ($dns) {
                return false;
            }
        } else {
            if (empty($this->registry['db'])) {
                //if no connection to the DB just exit;
                return false;
            }
            $crontab_disable = $this->registry['config']->getParamAsArray('crontab', 'disable');
            //check if some of the crontab actions have been disabled
            if (!empty($crontab_disable)) {
                if (
                    in_array('all', $crontab_disable) ||
                    (!$this->action && in_array('main', $crontab_disable)) ||
                    (
                        $this->action &&
                        (
                            in_array($this->action, $crontab_disable) ||
                            in_array('main', $crontab_disable) && in_array($this->action, $this->_mainCrons)
                        )
                    )
                ) {
                    return false;
                }
            }
        }

        //set error handler and shutdown handler
        $shutdown = new Shutdown();
        $shutdown->register(array($this, 'onError'), 'fatal');
        set_error_handler(array($this, 'onError'));

        $result = true;

        if ($this->action) {
            $result = $this->executeMethod($this->action);
        } else {
            //set limit to 30 minutes
            set_time_limit(60*30);
            //no action specified: execute all of the main crontab methods
            foreach ($this->_mainCrons as $action) {
                if (!$this->executeMethod($action)) {
                    $result = false;
                    break;
                }
            }
        }

        //restore handlers
        restore_error_handler();
        $shutdown->unregister(-1);
        $shutdown = null;

        return $result;
    }

    /**
     * Executes a single method of current class if it exists and its previous
     * execution is not still running.
     *
     * @param string $action - action corresponding to method
     * @return boolean - result of the operation
     */
    private function executeMethod($action) {
        //construct the method (camelCase)
        $method = '_';
        $chunks = explode('_', trim($action));
        foreach ($chunks as $idx => $chunk) {
            $method .= ($idx == 0) ? strtolower($chunk) : ucfirst(strtolower($chunk));
        }

        $result = true;

        //execute the method only if it exists
        if (method_exists($this, $method)) {
            if ($this->checkCurrentTasks($method)) {
                $this->setAction($action);
                $this->$method();
                //unlock crontab method
                $this->finishCurrentTasks($method);
                $this->sendExecutionErrors();
            } else {
                //do not start another process for the same method
                $result = false;
            }
        } else {
            // invalid action was specified
            $result = false;
        }

        return $result;
    }

    /**
     * Check current crontab tasks
     *
     * @param string $method - crontab method of task
     * @return boolean - result of the operation
     */
    private function checkCurrentTasks($method) {
        $db = $this->registry['db'];
        $query = 'SELECT started FROM ' . DB_TABLE_CRONTAB_LOCK . ' WHERE method="' . $method . '"';
        $started = $db->GetOne($query);
        //check date - 4 hours before current datetime
        $check_date = General::strftime("%Y-%m-%d %H:%M:%S", strtotime("-4 hour"));

        //check last started
        if ($started > $check_date) {
            //do not start
            return false;
        } else {
            //save started date for method
            $query = 'INSERT INTO ' . DB_TABLE_CRONTAB_LOCK . "\n" .
                     'SET started = now(), method="' . $method . '"' . "\n" .
                     'ON DUPLICATE KEY UPDATE ' . "\n" .
                     'started = now()';
            $db->Execute($query);
            //so lazy
            $this->_method = $method;
            return true;
        }
    }

    /**
     * Finish current crontab tasks
     *
     * @param string $method - crontab method of task
     * @return boolean - result of the operation
     */
    private function finishCurrentTasks($method = '') {
        if (!$method && !empty($this->_method)) {
            $method = $this->_method;
        }
        // Make sure to unlock the crontab method, even if transactions are not ended properly
        $db = DB::getConnection('crontabError');
        $query = 'DELETE FROM ' . DB_TABLE_CRONTAB_LOCK . ' WHERE method="' . $method . '"';
        $db->Execute($query);
        if ($db->ErrorMsg()) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * Notifies users when their passwords are about to expire (and they should
     * change them)
     * @return boolean
     */
    private function _notifyBeforePasswordExpiration() {
        require_once PH_MODULES_DIR . 'crontab/models/users.crontabs.model.php';
        $crontab = new Users_Crontab($this->registry, array('action' => $this->action));
        $crontab->notifyBeforePasswordExpiration();
        unset($crontab);
        return true;
    }

    /**
     * Expired deadlines of documents
     */
    private function _expiredDeadlines() {
        $crontab = new Crontab($this->registry, array('action'=>'expired_deadlines'));
        $crontab->expiredDeadlines();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Before deadlines of documents
     */
    private function _beforeDeadlines() {
        $crontab = new Crontab($this->registry, array('action'=>'before_deadlines'));
        $crontab->beforeDeadlines();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Expired validity terms of documents
     */
    private function _expiredValidityTerms() {
        $crontab = new Crontab($this->registry, array('action'=>'expired_validity_terms'));
        $crontab->expiredValidityTerms();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Before validity terms of documents
     */
    private function _beforeValidityTerms() {
        $crontab = new Crontab($this->registry, array('action'=>'before_validity_terms'));
        $crontab->beforeValidityTerms();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Tasks notifications
     */
    private function _tasksNotifications() {
        require_once PH_MODULES_DIR . 'crontab/models/tasks.crontabs.model.php';
        $crontab = new Tasks_Crontab($this->registry, array('action'=>'tasks_notifications'));
        $crontab->tasksNotifications();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Expired phases of projects
     */
    private function _expiredPhases() {
        require_once PH_MODULES_DIR . 'crontab/models/projects.crontabs.model.php';
        $crontab = new Projects_Crontab($this->registry, array('action'=>'expired_phases'));
        $crontab->expiredPhases();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Perform crontab automations
     */
    private function _automations() {
        require_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php';
        $automation = new Automations_Controller($this->registry);
        $automation->performCrontabAutomation($this->registry);

        //try to free some more memory
        unset($automation);
        return true;
    }

    /**
     * Send e-mail reminders
     */
    private function _reminders() {
        require_once PH_MODULES_DIR . 'crontab/models/events.crontabs.model.php';
        $crontab = new Events_Crontab($this->registry, array('action'=>'reminders'));
        $crontab->remindEvents();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Recurring payments
     */
    private function _recurringPayments() {
        require_once PH_MODULES_DIR . 'crontab/models/finance.crontabs.model.php';
        $crontab = new Finance_Crontab($this->registry, array('action'=>'recurring_payments'));
        $crontab->recurringPayments();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Finish commodities reservations
     */
    private function _finishReservations() {
        require_once PH_MODULES_DIR . 'crontab/models/finance.crontabs.model.php';
        $crontab = new Finance_Crontab($this->registry, array('action'=>'finish_reservations'));
        $crontab->finishReservations();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Notification for expired non-finished events
     */
    private function _expiredEvents() {
        require_once PH_MODULES_DIR . 'crontab/models/events.crontabs.model.php';
        $crontab = new Events_Crontab($this->registry, array('action'=>'expired_events'));
        $crontab->expiredEvents();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Process inbox emails of sender email (replyto_email setting, usually: <EMAIL>)
     */
    private function _inboxEmails() {
        require_once PH_MODULES_DIR . 'crontab/models/emails.crontabs.model.php';
        $crontab = new Emails_Crontab($this->registry, array('action'=>'inbox_emails'));
        $crontab->processInboxEmails();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Process bounced emails of sender email (replyto_email setting, usually: <EMAIL>)
     */
    private function _bouncedEmails() {
        require_once PH_MODULES_DIR . 'crontab/models/emails.crontabs.model.php';
        $crontab = new Emails_Crontab($this->registry, array('action'=>'bounced_emails'));
        try {
            $crontab->processBouncedEmails();
        } catch(\Exception | \Error $e) {
            General::log(
                $this->registry,
                'processBouncedEmails',
                "Bounced e-mails not procesed!\n\n Error: \n". var_export($e, true));
        }

        return true;
    }

    /**
     * Notify invoice supervisors to confirm invoices
     */
    private function _notifyInvoiceSupervisors() {
        require_once PH_MODULES_DIR . 'crontab/models/invoices.crontabs.model.php';
        $crontab = new Invoices_Crontab($this->registry, array('action'=>'notify_invoice_supervisors'));
        $crontab->notifyObservers();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Issue all the invoices that should be issued
     * in function of the invoices templates settings
     */
    private function _issueInvoices() {
        require_once PH_MODULES_DIR . 'crontab/models/invoices.crontabs.model.php';
        $crontab = new Invoices_Crontab($this->registry, array('action'=>'issue_invoices'));
        $crontab->issueInvoices();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Send issued invoices
     * in function of the invoices templates settings
     */
    private function _sendIssuedInvoices() {
        require_once PH_MODULES_DIR . 'crontab/models/invoices.crontabs.model.php';
        $crontab = new Invoices_Crontab($this->registry, array('action'=>'send_issued_invoices'));
        $crontab->sendIssuedInvoices();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Send e-mail campaigns
     */
    private function _emailsCampaigns() {
        require_once PH_MODULES_DIR . 'crontab/models/emails.campaigns.crontabs.model.php';
        $crontab = new Emails_Campaigns_Crontab($this->registry, array('action' => 'emails_campaigns'));
        $crontab->sendEmailsCampaigns();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Notification of forthcoming events for the day
     */
    private function _forthcomingEvents() {
        require_once PH_MODULES_DIR . 'crontab/models/events.crontabs.model.php';
        $crontab = new Events_Crontab($this->registry, array('action'=>'forthcoming_events'));
        $crontab->forthcomingEvents();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * End temporary agreements for a contracts
     */
    private function _endContractsAgreements() {
        require_once PH_MODULES_DIR . 'crontab/models/contracts.crontabs.model.php';
        $crontab = new Contracts_Crontab($this->registry, array('action'=>'restore_contracts'));
        $crontab->endContractsAgreements();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Start contracts new agreements
     */
    private function _startContractsAgreements() {
        require_once PH_MODULES_DIR . 'crontab/models/contracts.crontabs.model.php';
        $crontab = new Contracts_Crontab($this->registry, array('action'=>'start_annex'));
        $crontab->startContractsAgreements();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Notify for start of contracts
     */
    private function _notifyStartContracts() {
        require_once PH_MODULES_DIR . 'crontab/models/contracts.crontabs.model.php';
        $crontab = new Contracts_Crontab($this->registry, array('action'=>'notify_start_contracts'));
        $crontab->notifyStartContracts();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Notify for end of contracts
     */
    private function _notifyEndContracts() {
        require_once PH_MODULES_DIR . 'crontab/models/contracts.crontabs.model.php';
        $crontab = new Contracts_Crontab($this->registry, array('action'=>'notify_end_contracts'));
        $crontab->notifyEndContracts();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Update currency rates
     */
    private function _currenciesUpdateRates() {
        require_once PH_MODULES_DIR . 'crontab/models/finance.crontabs.model.php';
        $crontab = new Finance_Crontab($this->registry, array('action'=>'currencies_update_rates'));
        $crontab->currenciesUpdateRates();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Expired deadlines of items of budgets
     */
    private function _budgetsExpiredDeadlines() {
        require_once PH_MODULES_DIR . 'crontab/models/budgets.crontabs.model.php';
        $crontab = new Budgets_Crontab($this->registry, array('action' => 'budgets_expired_deadlines'));
        $crontab->budgetsExpiredDeadlines();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Before deadlines of items of budgets
     */
    private function _budgetsBeforeDeadlines() {
        require_once PH_MODULES_DIR . 'crontab/models/budgets.crontabs.model.php';
        $crontab = new Budgets_Crontab($this->registry, array('action' => 'budgets_before_deadlines'));
        $crontab->budgetsBeforeDeadlines();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Expired deadlines of minitasks
     */
    private function _minitasksExpiredDeadlines() {
        require_once PH_MODULES_DIR . 'crontab/models/minitasks.crontabs.model.php';
        $crontab = new Minitasks_Crontab($this->registry, array('action' => 'minitasks_expired_deadlines'));
        $crontab->minitasksExpiredDeadlines();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Before deadlines of minitasks
     */
    private function _minitasksBeforeDeadlines() {
        require_once PH_MODULES_DIR . 'crontab/models/minitasks.crontabs.model.php';
        $crontab = new Minitasks_Crontab($this->registry, array('action' => 'minitasks_before_deadlines'));
        $crontab->minitasksBeforeDeadlines();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Not distributed bank transfers
     */
    private function _notDistributedPayments() {
        require_once PH_MODULES_DIR . 'crontab/models/finance.crontabs.model.php';
        $crontab = new Finance_Crontab($this->registry, array('action' => 'not_distributed_payments'));
        $crontab->notifyForNotDistributedPayments();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Changed payment status of incomes reasons
     */
    private function _notifyPaymentStatus() {
        require_once PH_MODULES_DIR . 'crontab/models/finance.crontabs.model.php';
        $crontab = new Finance_Crontab($this->registry, array('action' => 'notify_payment_status'));
        $crontab->notifyPaymentStatus();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Automatically mass-archive models:
     * Auto-archiving parameters should be set in crontab section of settings table in DB.
     * Parameters by type (if any) should be set in module section of settings table.
     */
    protected function _archive() {
        require_once PH_MODULES_DIR . 'crontab/models/archive.crontabs.model.php';
        //get models to be archived (only 'document' for now)
        $models = $this->registry['config']->getParamAsArray('crontab', 'archive_models');
        //get interval for the models (default settings per module)
        $interval = General::parseSettings($this->registry['config']->getParam('crontab', 'archive_interval'));
        //users to be notified - optional
        $archive_responsible = $this->registry['config']->getParamAsArray('crontab', 'archive_responsible');
        // flag if files of models should be completely removed
        $remove_files = (bool)$this->registry['config']->getParam('crontab', 'archive_remove_files');

        // stop if no models
        if (empty($models)) {
            return false;
        }

        // create multi-level array from interval settings
        $interval_detailed = array_fill_keys($models, array());
        // main interval setting applies to all types without type-specific settings
        // main interval setting might be empty when only type-specific archiving should be applied
        foreach ($interval as $model => $value) {
            if (in_array($model, $models)) {
                // common settings without model type
                $model_type = 0;
            } else {
                continue;
            }
            // if search field is not specified, assume "added" as default field
            $interval_detailed[$model][$model_type] = array(
                'field' => 'added',
                'interval' => $value,
            );
            // if value contains both field and interval, split it
            if (preg_match('#\s*=>\s*#', $value)) {
                list(
                    $interval_detailed[$model][$model_type]['field'],
                    $interval_detailed[$model][$model_type]['interval']
                ) = preg_split('#\s*=>\s*#', $value);
            }
        }

        // type settings (saved with section <module> and name archive_<model_type>
        foreach ($models as $model) {
            $type_settings = $this->registry['config']->getParam(General::singular2plural($model));
            $type_settings = array_intersect_key(
                $type_settings,
                array_flip(array_filter(array_keys($type_settings), function($a) { return preg_match('#^archive_.+$#', $a); }))
            );
            foreach ($type_settings as $model_type => $value) {
                $model_type = preg_replace('#^archive_#', '', $model_type);
                $value = General::parseSettings($value);
                if (!empty($value['interval']) && !empty($value['field'])) {
                    $interval_detailed[$model][$model_type] = $value;
                }
            }
        }
        $interval_detailed = array_filter($interval_detailed);

        $result = array();
        $send = false;
        foreach ($models as $model) {
            $action = 'archive' . ucfirst(General::singular2Plural($model));
            if (method_exists('Archive_Crontab', $action) && array_key_exists($model, $interval_detailed)) {
                $crontab = new Archive_Crontab(
                    $this->registry,
                    array(
                        'action' => $action,
                        'remove_files' => $remove_files,
                    )
                );
                $result[$model] = $crontab->$action($interval_detailed[$model]);
                if ($result[$model]) {
                    $send = true;
                }
            }
        }

        $template = 'archive_notification';
        $model = new Model($this->registry);
        $shouldSendEmail = $model->shouldSendEmail($template);

        $archive_responsible = array_diff(
            $archive_responsible,
            Users::getUsersNoSend($this->registry, $template)
        );

        if ($send && $shouldSendEmail && $archive_responsible) {
            //notify the users responsible
            $mailer = new Mailer($this->registry, $template);
            foreach ($result as $model => $value) {
                if (!$value) {
                    continue;
                }
                $pkey = General::singular2Plural($model);
                @list($module, $controller) = preg_split('#_#', $pkey, 2);
                if (!empty($controller)) {
                    $controller_string = '&controller=' . $controller . '&' . $controller;
                    $factory = ucfirst($module) . '_' . str_replace(' ', '_', ucwords(str_replace('_', ' ', $controller)));
                } else {
                    $controller = '';
                    $controller_string = '&' . $module;
                    $factory = ucfirst($module);
                }
                $itemsPerPage = 10;

                $alias = $factory::getAlias($module, $controller);
                $link = sprintf(
                    '%s/index.php?%s=%s%s=search&search_%s=1&search_module=%s&search_controller=%s' .
                    '&search_fields[0]=%s.archive&compare_options[0]=%%3D+\'%%25s\'&values[0]=archive&logical_operator[0]=AND' .
                    '&search_fields[1]=DATE_FORMAT(%s.archived%%2C+%%22%%25Y-%%25m-%%25d%%22)' .
                    '&compare_options[1]=%%3D+\'%%25s\'&values[1]=%s&display=%d',
                    $this->registry['config']->getParam('crontab', 'base_host'),
                    $this->registry['module_param'],
                    $module,
                    $controller_string,
                    $model,
                    $module,
                    $controller ?: $module,
                    $alias,
                    $alias,
                    General::strftime('%Y-%m-%d'),
                    $itemsPerPage
                );
                $mailer->placeholder->add('archived_' . $pkey . '_url', $link);
            }

            $mailer->placeholder->add('archived_by', $this->registry['currentUser']->get('firstname') . ' ' .
                                                     $this->registry['currentUser']->get('lastname'));

            $filters = array(
                'where' => array(
                    "u.id IN ('" . implode("', '", $archive_responsible) . "')",
                    "u.active = 1",
                    "u.hidden IS NOT NULL",
                ),
                'sanitize' => true
            );
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $users = Users::search($this->registry, $filters);
            foreach ($users as $user) {
                $mailer->placeholder->add('user_name', $user->get('firstname') . ' ' . $user->get('lastname'));
                $mailer->placeholder->add('to_email', $user->get('email'));
                $mailer->send();
            }
        }

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Automatically mass-purge models:
     * Auto-purging parameters should be set in crontab section of settings table in DB.
     * Parameters by type (if any) should be set in module section of settings table.
     */
    protected function _purge() {
        require_once PH_MODULES_DIR . 'crontab/models/purge.crontabs.model.php';
        //get models to be archived (only 'document' for now)
        $models = $this->registry['config']->getParamAsArray('crontab', 'purge_models');
        //get interval for the models (default settings per module)
        $interval = General::parseSettings($this->registry['config']->getParam('crontab', 'purge_interval'));
        //users to be notified - optional
        $purge_responsible = $this->registry['config']->getParamAsArray('crontab', 'purge_responsible');

        // stop if no models
        if (empty($models)) {
            return false;
        }

        // create multi-level array from interval settings
        $interval_detailed = array_fill_keys($models, array());
        // main interval setting applies to all types without type-specific settings
        // main interval setting might be empty when only type-specific purging should be applied
        foreach ($interval as $model => $value) {
            if (in_array($model, $models)) {
                // common settings without model type
                $model_type = 0;
            } else {
                continue;
            }
            // if search field is not specified, assume "added" as default field
            $interval_detailed[$model][$model_type] = array(
                'field' => 'added',
                'interval' => $value,
            );
            // if value contains both field and interval, split it
            if (preg_match('#\s*=>\s*#', $value)) {
                list(
                    $interval_detailed[$model][$model_type]['field'],
                    $interval_detailed[$model][$model_type]['interval']
                    ) = preg_split('#\s*=>\s*#', $value);
            }
        }

        // type settings (saved with section <module> and name purge_<model_type>
        foreach ($models as $model) {
            $type_settings = $this->registry['config']->getParam(General::singular2plural($model));
            $type_settings = array_intersect_key(
                $type_settings,
                array_flip(array_filter(array_keys($type_settings), function($a) { return preg_match('#^purge_.+$#', $a); }))
            );
            foreach ($type_settings as $model_type => $value) {
                $model_type = preg_replace('#^purge_#', '', $model_type);
                $value = General::parseSettings($value);
                if (!empty($value['interval']) && !empty($value['field'])) {
                    $interval_detailed[$model][$model_type] = $value;
                }
            }
        }
        $interval_detailed = array_filter($interval_detailed);

        $result = array();
        $send = false;
        foreach ($models as $model) {
            $action = 'purge' . ucfirst(General::singular2Plural($model));
            if (method_exists('Purge_Crontab', $action) && array_key_exists($model, $interval_detailed)) {
                $crontab = new Purge_Crontab($this->registry, array());
                $result[$model] = $crontab->$action($interval_detailed[$model]);
                if ($result[$model]) {
                    $result[$model] = $crontab->results;
                    $send = true;
                }
            }
        }

        $template = 'purge_notification';
        $model = new Model($this->registry);
        $shouldSendEmail = $model->shouldSendEmail($template);


        $purge_responsible = array_diff(
            $purge_responsible,
            Users::getUsersNoSend($this->registry, $template)
        );

        if ($send && $shouldSendEmail && $purge_responsible) {
            //notify the users responsible
            $mailer = new Mailer($this->registry, $template);
            $purged_records_list = array();
            foreach ($result as $model => $value) {
                if (empty($value)) {
                    continue;
                }
                $purged_records_list[] = $model . ': ' .  implode(', ', $value);
            }

            $mailer->placeholder->add('purged_records_list', implode(', ', $purged_records_list));
            $mailer->placeholder->add('purged_by', $this->registry['currentUser']->get('firstname') . ' ' .
                                                   $this->registry['currentUser']->get('lastname'));

            $filters = array(
                'where' => array(
                    "u.id IN ('" . implode("', '", $purge_responsible) . "')",
                    "u.active = 1",
                    "u.hidden IS NOT NULL",
                ),
                'sanitize' => true
            );
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $users = Users::search($this->registry, $filters);
            foreach ($users as $user) {
                $mailer->placeholder->add('user_name', $user->get('firstname') . ' ' . $user->get('lastname'));
                $mailer->placeholder->add('to_email', $user->get('email'));
                $mailer->send();
            }
        }

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Begin process of scanning file contents.
     * Maximum 100 files will be processed.
     */
    private function _initScanFileContents() {
        require_once PH_MODULES_DIR . 'crontab/models/optiscan.crontabs.model.php';
        $crontab = new Optiscan_Crontab($this->registry);
        $crontab->createOptiscanJobs();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Process previously created file conversions
     * Maximum 100 files will be processed.
     */
    private function _processScanFileContents() {
        require_once PH_MODULES_DIR . 'crontab/models/optiscan.crontabs.model.php';
        $crontab = new Optiscan_Crontab($this->registry);
        $crontab->processOptiscanJobs();

        //try to free some more memory
        unset($crontab);
        return true;
    }

    /**
     * Fails all nested transactions in progress in $this->registry['db'] connection
     * If no transactions are in progress, does nothing
     * Useful when an error occurs, and we want to make sure that all transactions are rolled back
     * @return void
     */
    private function endTransactions(): void
    {
        $db = $this->registry['db'];
        // Make sure to fail any transactions in progress, as this is an error and, it can't be recovered from
        if (is_int($db->transOff) && $this->registry['db']->transOff > 0) {
            // Considering multi level transactions, we need to complete all of them
            while ($db->transOff > 0) {
                $db->CompleteTrans(false);
            }
        }
    }
}

?>
