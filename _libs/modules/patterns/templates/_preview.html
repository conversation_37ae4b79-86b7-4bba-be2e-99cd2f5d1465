<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="{$smarty.const.LANG}" lang="{$smarty.const.LANG}">
<head>
<title></title>
<meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
<meta http-equiv="content-language" content="{$smarty.const.LANG}" />
{literal}
<style>
  @media screen, print {
    body {
        margin: 0;
        padding: 0;
        height: 100%;
    }
    #container {
        width: 21cm;
    }
    #content {
        vertical-align: top;
        height: 100%;
        padding: 2.54cm;
        border-right: solid 1px #CCCCCC;
        border-left: solid 1px #CCCCCC;
    }
    body, td {
      font-family: <PERSON><PERSON>, Verdana, Sans-Ser<PERSON>;
      font-size: 12px;
    }
    table[border="1"] {
        border: 1px solid ;
        border-collapse: collapse;
    }
    table[border="1"] td {
        vertical-align: top;
    }
    a {
      color: #0000FF !important;  /* For Firefox... mark as important, otherwise it becomes black */
    }
    .Bold {
      font-weight: bold;
    }
    .Title {
      font-weight: bold;
      font-size: 18px;
      color: #cc3300;
    }
    .Code {
      border: #8b4513 1px solid;
      padding-right: 5px;
      padding-left: 5px;
      color: #000066;
      font-family: 'Courier New' , Monospace;
      background-color: #ff9933;
    }
  }
</style>
{/literal}
</head>

<body>

<table border="0" align="center" id="container">
  <tr>
    <td id="content">
{$preview_content}
    </td>
  </tr>
</table>
</body>

</html>
