<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<input type="hidden" name="id" id="id" value="{$patterns_part->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$patterns_part->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='partsname'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$patterns_part->get('name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='parts_type'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
{foreach from=$pattern_parts_types key='type' item='type_name'}
              {if $type eq $patterns_part->get('type')}{$type_name}{/if}
{/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='parts_height'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$patterns_part->get('height')|escape}
          </td>
        </tr>
        {if $plugins}
        <tr id="pattern_plugins">
          <td class="labelbox">{help label='plugin'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $patterns_part->get('plugin')}
              {foreach from=$plugins item='plugin'}
                {if $patterns_part->get('plugin') eq $plugin.id}
                  <div class="floatl" title="{$plugin.description|escape}">{$plugin.name|escape}</div>
                  <div style="float: left" id="plugins_preview">
                  {if $plugin.images}
                    {foreach name='image_idx' from=$plugin.images item='image'}
                      <img src="{$theme->imagesUrl}view.png" alt="" class="pointer plugin_{$plugin.id}_{$smarty.foreach.image_idx.iteration}" title="{#preview#} {#patterns_plugin#|mb_lower}: {$plugin.description|escape}" border="0" onclick="previewPlugin(this, 'pattern')" />
                    {/foreach}
                  {/if}
                  </div>
                {/if}
              {/foreach}
            {/if}
          </td>
        </tr>
        {/if}
        <tr>
          <td class="labelbox">{help label='description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$patterns_part->get('description')|escape|mb_wordwrap|url2href|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='parts_content'}</td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="3">
            {$editor_content}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$patterns_part}
</div>

<br /><br />
<div id="placeholders">
{include file=`$theme->templatesDir`_placeholders_list.html title=''}
{if $plugin_vars}
  <br />
  <div id="plugins_vars_container">
  {include file=`$theme->templatesDir`_placeholders_list.html basic_placeholders=$plugin_vars title=$plugin_vars_title}
  </div>
{/if}
</div>
