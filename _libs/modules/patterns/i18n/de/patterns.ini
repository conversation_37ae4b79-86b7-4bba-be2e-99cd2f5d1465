patterns = Vorlagen
patterns_header = Kopfzeile
patterns_footer = Fußzeile
patterns_name = Name
patterns_landscape = Richtung
patterns_partsname = Name der Kopf-/Fußzeile
patterns_model = Für
patterns_model_type = Typ/Abschnitt
patterns_plugin = Plug-in
patterns_list = Zum Speichern/für Verzeichnis
patterns_list1 = zum Speichern
patterns_list2 = für Verzeichnis
patterns_for_printform = Für Druckform 
patterns_company = Unternehmen
patterns_model_document = Dokument
patterns_model_customer = Vertragspartner
patterns_model_event = Ereignis
patterns_model_report = Bericht
patterns_model_contract = Vertrag
patterns_model_nomenclature = Nomenklatur
patterns_model_project = Projekt
patterns_model_task = Aufgabe
patterns_finance_models = Fin<PERSON>zen
patterns_model_finance_incomes_reason = Erlösbeleg
patterns_model_finance_expenses_reason = Kostenbeeg
patterns_model_finance_warehouses_document = Lagerbeleg
patterns_model_finance_annulment = Protokoll zum Annullieren
patterns_model_finance_payment = Zahlung
patterns_model_finance_invoices_template = zu erstellende Rechnung
patterns_document_type_search = Für Dokument Typ/Abschnitt 
patterns_customer_type_search = Für Vertragspartner Typ/Abschnitt 
patterns_event_type_search = Für Ereignis Typ/Abschnitt
patterns_report_type_search = Für Bericht Typ 
patterns_contract_type_search = Für Vertrag Typ
patterns_nomenclature_type_search = Für Nomenklatur Typ/Abschnitt
patterns_project_type_search = Für Projekt Typ/Abschnitt
patterns_task_type_search = Für Aufgabe Typ/Abschnitt
patterns_finance_incomes_reason_type_search = Für Erlösbeleg Typ/Abschnitt
patterns_finance_expenses_reason_type_search = Für Kostenbeleg Typ/Abschnitt
patterns_finance_warehouses_document_type_search = Für Lagerbeleg Typ/Abschnitt 
patterns_finance_annulment_type_search = Für Protokoll zum Annullieren Typ/Abschnitt
patterns_finance_payment_type_search = Für Zahlung Typ 
patterns_finance_invoices_template_type_search = fr zu erstellende Rechnung Typ 
patterns_type_incoming = eingehend
patterns_type_outgoing = ausgehend
patterns_type_landscape = waagerecht
patterns_type_portrait = senkrecht
patterns_type_internal = intern
patterns_type = Typ
patterns_section = Abschnitt
patterns_section_type = Für Typ/Abschnitt
patterns_no_section_type = Ohne Abschnitt oder Typ
patterns_for_section = für Abschnitt 
patterns_for_type = Für Typ
patterns_type_kind_person = natürliche Person
patterns_type_kind_company = Rechtsperson
patterns_type_kind_company_person = Rechtsperson/natürliche Person
patterns_type_kind_person_company = natürliche Person/Rechtsperson
patterns_parts_type = Typ
patterns_prefix = Vorlage für Dateiname
patterns_position = Position
patterns_format = Format
patterns_content = Inhalt
patterns_background_image_show = Hintergrundbild
patterns_background_image = neues Hintergrundbild uploaden
patterns_background_image_delete = gegenwärtiges Hintergrundbild löschen
patterns_background_image_position = Position des Hintergrundbildes
patterns_description = Beschreibung
patterns_force_generate = Datei beim Drucken speichern
patterns_not_regenerate_finished_record = bei gesperrtem/beendetem Speichern nicht erneut erstellen
patterns_handover_direction = Richtung
patterns_handover_direction_incoming = Übernahmeprotokoll
patterns_handover_direction_outgoing = Übergabeprotokoll
patterns_handover_direction_both = Übernahme-/Übergabeprotokoll
patterns_add_new = neue Vorlage hinzufügen
patterns_edit = Vorlage editieren
patterns_view = Vorlage einsehen
patterns_translate = Daten in der Vorlage übersetzen
patterns_attachments = Dateien zur Vorlage
patterns_file_not_exist = Datei gelöscht oder beschädigt
patterns_added_by = Hinzugefügt von
patterns_modified_by = Geändert von
patterns_added = Hinzugefügt am
patterns_modified = Geändert am
basic_vars = Verzeichnis mit Eckdaten
message_patterns_add_success = Vorlage erfolgreich hinzugefügt!
message_patterns_edit_success = Vorlage erfolgreich editiert!
message_patterns_translate_success = Die Daten dieser Vorlage wurden erfolgreich übersetzt!
message_please_select_model = bitte Art des Speicherns wählen
message_patterns_file_deleted_success = Datei erfolgreich gelöscht!
error_patterns_file_deleted_failed = Fehler beim Löschen der Datei!
error_no_name_specified = Sie haben keinen Namen der Vorlage eingegeben!
error_no_prefix_specified = Sie haben kein Präfix für die Vorlage gewählt!
error_invalid_prefix = Ungültiger Präfix. Bitte Lateinbuchstaben für den Präfix verwenden, so z.B. für Angebot - off_!
error_prefix_not_unique = Dieser Präfix wird bereits verwendet. Bitte einen anderen eingeben!
error_no_such_pattern = Dieser Datensatz ist nicht verfügbar für Sie!
error_invalid_email = Ungültige E-Mail. Versichern Sie sich, dass Sie korrekte Daten eingegeben haben : ххх@ххх.ххх
error_no_type = Bitte den Typ angeben, für den diese Vorlage verwendet wird. Bitte wählen!
error_patterns_add_failed = Vorlage nicht hinzugefügt!
error_patterns_edit_failed = Vorlage wurde nicht editiert!
error_patterns_translate_failed = Die Daten in dieser Vorlage wurden nicht übersetzt!
error_no_model_specified = Bitte wähle, für welche Art des Speicherns diese Vorlage verwendet wird!
error_no_model_type_specified = Bitte wähle, für welchen Typ des Speicherns diese Vorlage verwendet wird!
error_description_invalid = Die Beschreibung enthält ungültige Symbole. Bitte eine nur aus Text bestehende Beschreibung eingeben.
patterns_prefix_legend = Bitte Lateinbuchstaben verwenden, so z.B. off_!
patternvars = Vorlagedaten
patternvars_pattern = Vorlage
patternvars_pattern_name = Name der Vorlage
patternvars_type = Typ der Variablen
patternvars_type_text = Text
patternvars_type_dropdown = zählbarer Typ 
patternvars_type_readonly = fest vorgegeben
patternvars_company_list = Liste mit Firmendaten
patternvars_additional_list = Liste mit zusätzlichen Daten
patternvars_system_list = Liste mit Systemdaten
patternvars_user_list = Liste mit Benutzerdaten
patternvars_doctype_list = Liste mit Daten über Typ des Dokuments
patternvars_plugin_list = Liste mit Variablen für Plug-in "%s"
help_patterns_model = Art des Datensatzes
help_patterns_section_type = Für den Datensatz: Typ des Datensatzes, auf den sich die Vorlage bezieht.<br />Für Liste: für Typ/Abschnitt oder für Gesamtliste bezieht sich die Vorlage.
help_patterns_prefix = Folgende Variablen zum Erstellen des Namen der erstellten/gedruckten Datei verwenden
help_patterns_placeholders_document = Die Variablen für das Dokument ersetzen Daten des eigentlichen Dokuments, die in den verschiedenen Modi für Dokumentbearbeitung verwendet werden.
help_patterns_placeholders_customer = Die Variablen des Vertragspartners entstammen den Daten des Vertragspartners, der dem Dokument zugewiesen wird, für das diese Vorlage zu erstellen ist.
help_patterns_placeholders_currentuser = Die Variablen des gegenwärtigen Benutzers verden für den Benutzer verwendet, der das Dokument erstellt.
help_patterns_placeholders_system = Die Systemvariablen werden verwendet, um für das ganze System geltende Daten anzuzeigen.
help_patterns_background_image = Bitte Hintergrund für die Vorlage wählen<br /><span style="color: red;"><strong>ACHTUNG!!!</strong></span> Der Hintergrund ist inkompatibel mit den Kopf- und Fußzeilen der Seite!
help_patterns_list = Ob die Vorlage sich auf einen einzelnen Datensatz oder auf eine Datensatzliste bezieht.
help_patterns_for_printform = Ob die Vorlage sich auf eine Druckform bezieht oder nicht.
help_patterns_handover_direction = Durch die Vorlage können Übergabe- und Übernahmeprotokolle nur für die gewählte Richtung gedruckt werden.
help_patterns_company = Ist ein Unternehmen gewählt, so können durch diese Vorlage Dokumente nur für dieses Unternehmen gedruckt werden.

