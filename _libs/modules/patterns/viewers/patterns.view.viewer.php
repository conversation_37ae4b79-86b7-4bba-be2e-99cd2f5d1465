<?php

class Patterns_View_Viewer extends Viewer {
    public $template = 'view.html';

    public function prepare() {
        $this->model = $this->registry['pattern'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //get langs
        $langs = preg_split('#\s*,\s*#', $this->registry['config']->getParam('i18n', 'model_langs'));
        $this->data['multi_langs'] = $langs;

        //get the placeholders for the output file name
        $this->model->getOutputFileNamePlaceholders();

        //prepare the placeholders
        $placeholders = $this->model->getPlaceholders();
        $this->data['basic_placeholders'] = $placeholders['basic'];
        $this->data['additional_vars'] = $placeholders['additional'];
        if (!empty($placeholders['plugin'])) {
            $this->data['plugin_vars'] = $placeholders['plugin'];
            //get the plugin data
            @list($plugin) = Patterns::getPlugins($this->registry, array('plugin' => $this->model->get('plugin')));
            $this->data['plugin_vars_title'] = sprintf($this->i18n('patternvars_plugin_list'), $plugin['name']);
        }

        //prepare the wysiwyg editor
        $editor = new Editor($this->registry, 'content', array('width' => 1200, 'toolbar' => 'Preview'));
        $editor->setContent($this->model->get('content'));
        $this->data['editor_content'] = $editor->create();

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array ('g.id = ' . $this->model->get('group')));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }

        //get the plugins for the selected model and model_type
        $this->data['plugins'] = Patterns::getPlugins($this->registry,
                                                      array('model' => $this->model->get('model'),
                                                            'model_type' => $this->model->get('model_type')));

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('patterns_view');
        $this->data['title'] = $title;
    }
}

?>
