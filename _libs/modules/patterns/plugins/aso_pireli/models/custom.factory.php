<?php

use Nzoom\I18n\I18n;

/**
 * Custom plugin to prepare data for print/genaration for ASP
 */
Class ASP_Custom_Factory extends Model_Factory {
    public $folderName = 'aso_pireli';
    public static $registry;
    public static $model;
    public static $filePath;
    private static $_settings;
    private static $_registry;
    private static $_pattern;

    /**
     * Translates custom labels defined in the i18n of the plugin
     */
    public static function i18n($param, $lang = '') {
        if (empty($lang)) {
            $lang = self::$model->get('model_lang');
        }
        $custom_file = realpath(dirname(__FILE__) . '/../i18n/' . $lang) . '/print.ini';
        $i18n = new I18n($lang, $custom_file);
        return $i18n->translate($param);
    }

    /**
     * Prepares some custom data for invoice print
     *
     * @param Registry $registry - registry object
     * @param Finance_Incomes_Reason $model - finance_incomes_reason model (the invoice)
     * @param Pattern $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function prepareInvoices(&$registry, &$model, &$pattern, &$params = array()) {
        //save the previous registry lang
        $registry_lang_old = $registry['lang'];

        //this plugin should work only for contracts
        //because only these invoices could have currency different from that in the contract
        //get the relative (income reason or contract)
        $model->getRelatives(array('get_reason' => true));
        $reason = $model->get('reason');

        /* This restricts the plugin to be used only for contract invoices
           The restriction is removed as per Bug 3764, comment 2
        if (($model->get('type') == PH_FINANCE_TYPE_INVOICE || $model->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) &&
            is_object($reason) && (get_class($reason) != 'Contract' || $reason->get('type') != 4)) {
            unset($reason);
            return false;
        }
        unset($reason);
        */

        self::$registry = &$registry;
        self::$model = &$model;

        //get print settings for the 2nd type grouping table
        $print_properties = $model->getGT2PrintSettings($params['pattern_id']);

        $lang = $model->get('model_lang');

        $translations = $model->getTranslations();

        $locations = array();
        foreach ($translations as $t_lang) {
            $model->set('model_lang', $t_lang, true);

            if ($registry['lang'] != $model->get('model_lang')) {
                //change the registry lang
                $registry['translater']->reloadFiles($lang);
            }

            if (!$model->get('table_values_are_set')) {
                $model->getGT2Vars();
            }
            $table = $model->get('grouping_table_2');
            $gt2 = $table;
            $gt2['vars'] = array();
            $styles_for_template = array();

            foreach ($print_properties as $key => $property) {
                // style properties
                if (!empty($property['style'])) {
                    $styles_for_template[$key] = $property['style'];
                }
                // label for table caption
                if ($key == 'var_' . $table['id']) {
                    if (isset($property['labels'][$t_lang])) {
                        $gt2['label'] = $property['labels'][$t_lang];
                    }
                    continue;
                }
                foreach ($table['vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        $gt2['vars'][$idx] = $var;
                        // label for field
                        if (isset($property['labels'][$t_lang])) {
                            $gt2['vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        // aggregates
                        if (isset($property['agregate'])) {
                            if ($property['agregate'] != 'none') {
                                $gt2['vars'][$idx]['agregate'] = $property['agregate'];
                            } elseif (isset($gt2['vars'][$idx]['agregate'])) {
                                unset($gt2['vars'][$idx]['agregate']);
                            }
                        }
                        continue 2;
                    }
                }
                foreach ($table['plain_vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        // label for total field
                        if (isset($property['labels'][$t_lang])) {
                            $gt2['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        continue 2;
                    }
                }
            }

            $count = 0;
            //set the value for the variables
            foreach($gt2['values'] as $row_id => $values) {
                if (empty($values)) continue;
                if ($values['free_field4'] && $values['free_field5']) {
                    //add period to article name
                    //do this only for periodical items (free_field4/free_field5 define the periodicity)
                    $values['article_name'] .= sprintf(' ' . self::i18n('print_article_period', $t_lang), General::strftime('%d.%m.%Y', $values['date_from']),
                                                                                                          General::strftime('%d.%m.%Y', $values['date_to']));
                }

                $gt2['values'][$row_id] = $values;

                $count++;

                //the locations codes are stored in free_field2 (Bug 3764)
                $locations[] = $values['free_field2'];
            }

            $gt2 = $model->calculateGT2Agregates($gt2);

            $groupingViewer = new Viewer($registry);
            $groupingViewer->setFrameset('_gt2_vars.html');

            $groupingViewer->data['styles'] = $styles_for_template;
            $groupingViewer->data['table'] = $gt2;

            $registry->set('lang', $t_lang, true);
            $model->extender->add($t_lang . '_grouping_table_2', $groupingViewer->fetch());
            $registry->set('lang', $registry_lang_old, true);

            unset($gt2);
            unset($table);
        }

        $model->set('model_lang', $lang, true);

        //as per Bug 3764, add variable for locations list
        $model->extender->add('locations', implode(', ', array_unique($locations)));
    }

    /**
     * Prepares some custom data for contract SOT print
     *
     * @param object $registry - registry object
     * @param object $model - contract model
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function prepareSOTContract(&$registry, &$model, &$pattern, &$params = array()) {
        //split pattern to pages according to the page breaks in it
        $pages = preg_split('#<p>[\r\n\s]*\[system_pagebreak\][\r\n\s]*</p>#mu', $pattern->get('content'));
        $pages_count = count($pages);

        //get the gt2 to analyze and resort it
        if (!$model->get('table_values_are_set')) {
            $model->getGT2Vars();
        }
        $table = $model->get('grouping_table_2');

        self::$_settings = $params['settings'];
        self::$_pattern= $pattern;
        self::$_registry= $registry;

        $gt2 = $table;
        $lang = $registry['lang'];

        //define array for the three kind of subcontracts (form, rent, delivery)
        $gt2_values = array(
            'form' => array(),
            'rent' => array(),
            'delivery' => array(),
        );
        foreach($gt2['values'] as $row_id => $values) {
            if ($values['price'] < 0) {
                //system article for discount
                unset($gt2['values'][$row_id]);
                continue;
            }
            if ($values['free_field3'] == 1) {
                //form
                $gt2_values['form'][$row_id] = $values;
            } elseif ($values['free_field3'] == 2) {
                //rent
                $gt2_values['rent'][$row_id] = $values;
            } elseif ($values['free_field3'] == 3) {
                //delivery
                $gt2_values['delivery'][$row_id] = $values;
            }
        }

        $subnum = 0;

        foreach($gt2_values as $contract_subtype => $values) {
            if (!empty($values)) {
                $gt2['values'] = $values;
                //get the content of the gt2 and its totals
                self::_prepareContractGT2($model, $gt2, $contract_subtype);

                $model->extender->add($contract_subtype . '_subnum', $model->get('num') . '.' . ++$subnum);
            } else {
                //remove the page content
                //IMPORTANT: in 5 pages pattern:
                //           page 0 - form
                //           pages 1,2 - delivery
                //           pages 3,4 - rent
                //IMPORTANT: in 3 pages pattern:
                //           page 0 - form
                //           page 2 - delivery
                //           page 1 - rent
                switch($contract_subtype) {
                case 'form':
                    $pages[0] = '';
                    break;
                case 'rent':
                    if ($pages_count == 5) {
                        $pages[3] = '';
                        $pages[4] = '';
                    } elseif ($pages_count == 3) {
                        $pages[1] = '';
                    }
                    break;
                case 'delivery':
                    if ($pages_count == 5) {
                        $pages[1] = '';
                        $pages[2] = '';
                    } elseif ($pages_count == 3) {
                        $pages[2] = '';
                    }
                    break;
                }
            }
        }

        //customer placeholders
        $filters = array('where' => array('c.id = ' . $model->get('customer')),
                         'sanitize' => true);
        $customer = Customers::searchOne($registry, $filters);
        if ($customer->get('is_company')) {
            $model->extender->add('customer_ucn_eik', sprintf(self::i18n('print_customer_eik', $lang), $customer->get('eik')));
            $model->extender->add('customer_address', $customer->get('registration_address'));
            $model->extender->add('customer_personal_id', '');
            $model->extender->add('customer_vat_num', sprintf(self::i18n('print_customer_vat_num', $lang), $customer->get('in_dds')));
            $model->extender->add('customer_mol', sprintf(self::i18n('print_customer_mol', $lang), $customer->get('mol')));
        } else {
            $model->extender->add('customer_ucn_eik', sprintf(self::i18n('print_customer_ucn', $lang), $customer->get('ucn')));
            $model->extender->add('customer_address', $customer->get('address_by_personal_id'));
            $model->extender->add('customer_personal_id', sprintf(self::i18n('print_customer_personal_id', $lang), $customer->get('identity_num'), $customer->get('identity_date'), $customer->get('identity_by')));
            $model->extender->add('customer_vat_num', $customer->get('in_dds') ? sprintf(self::i18n('print_customer_vat_num', $lang), $customer->get('in_dds')) : '');
            $model->extender->add('customer_mol', '');
        }
        unset($customer);

        //placeholders for responsible people
        //get the object from the contract
        $id_object = $model->getVarValue('id_object');
        $filters = array('where' => array('n.id = ' . $id_object),
                         'sanitize' => true);
        include_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        $object = Nomenclatures::searchOne($registry, $filters);
        $responsible_person = $object->getVarValue('responsible_person');
        $responsible_contacts = $object->getVarValue('responsible_contacts');
        $responsible = array();
        foreach($responsible_person as $idx => $val) {
            $responsible[] = sprintf(self::i18n('print_responsible_person', $lang), $val);
            $responsible[] = sprintf(self::i18n('print_responsible_contacts', $lang), $responsible_contacts[$idx]);
        }
        if (!empty($responsible)) {
            $model->extender->add('responsible', implode("<br />\n", $responsible));
        }
        $object_address = array();
        $address_city = $object->getVarValue('address_city');
        if (!empty($address_city)) {
            $object_address[] = sprintf(self::i18n('print_address_city', $lang), $address_city);
        }
        $address_quarter = $object->getVarValue('address_quarter');
        if (!empty($address_quarter)) {
            $object_address[] = sprintf(self::i18n('print_address_quarter', $lang), $address_quarter);
        }
        $address_street = $object->getVarValue('address_street');
        $address_numstreet = $object->getVarValue('address_numstreet');
        if (!empty($address_street)) {
            $object_address[] = sprintf(self::i18n('print_address_street', $lang), $address_street, $address_numstreet);
        }
        $address_block = $object->getVarValue('address_block');
        if (!empty($address_block)) {
            $object_address[] = sprintf(self::i18n('print_address_block', $lang), $address_block);
        }
        $address_entrance = $object->getVarValue('address_entrance');
        if (!empty($address_entrance)) {
            $object_address[] = sprintf(self::i18n('print_address_entrance', $lang), $address_entrance);
        }
        $address_floor = $object->getVarValue('address_floor');
        if (!empty($address_floor)) {
            $object_address[] = sprintf(self::i18n('print_address_floor', $lang), $address_floor);
        }
        $address_apartm = $object->getVarValue('address_apartm');
        if (!empty($address_apartm)) {
            $object_address[] = sprintf(self::i18n('print_address_apartm', $lang), $address_apartm);
        }
        $model->extender->add('object_address', implode(', ', $object_address));
        unset($object);

        //clauses (checkboxes)
        $clauses = array();
        $config_vars = $model->get('config_vars');
        if (!empty($config_vars)) {
            foreach ($config_vars as $idx => $var) {
                if (preg_match('#clause_#', $var['name']) && $var['type'] == 'checkbox_group') {
                    foreach ($var['options'] as $var_option) {
                        if (in_array($var_option['value'], $var['value'])) {
                            $clauses[] = $var_option['extended_value'];
                        } else {
                            $clauses[] = preg_replace('/check_yes.png/', 'check_no.png', $var_option['extended_value']);
                        }
                    }
                }
                //extension for a special occasions
                if (isset($config_vars[$idx+1]) && $config_vars[$idx+1]['name'] == 'podclause_second__value') {
                    $cnt = count($clauses);
                    $clauses[$cnt-1] .= ' ' . sprintf(self::i18n('print_podclause_second__value', $lang), $config_vars[$idx+1]['value'], General::digits2words($config_vars[$idx+1]['value'], 'BGN', $lang));
                } elseif (isset($config_vars[$idx+1]) && $config_vars[$idx+1]['name'] == 'podclause_period__value') {
                    $cnt = count($clauses);
                    $clauses[$cnt-1] .= ' ' . sprintf(self::i18n('podclause_period__value', $lang), $config_vars[$idx+1]['value']);
                } elseif (isset($config_vars[$idx+1]) && $config_vars[$idx+1]['name'] == 'podclause_mont__value') {
                    $cnt = count($clauses);
                    $clauses[$cnt-1] .= ' ' . sprintf(self::i18n('podclause_mont__value', $lang), $config_vars[$idx+1]['value'], General::digits2words($config_vars[$idx+1]['value'], 'BGN', $lang));
                }
            }
        }
        if (!empty($clauses)) {
            $model->extender->add('clauses', implode("<br />\n", $clauses));
        }
        unset($clauses);

        //now remove the empty pages
        $pages = array_filter($pages);
        $pattern->set('content', implode("\n[system_pagebreak]\n", $pages), true);
    }

    /**
     * Prepares GT2 getting the print settings depending on the type of subcontract
     * Gets the content of the gt2 (directly in markup) and its totals
     *
     * @param Contract $model - contract model
     * @param array $gt2 - GT2 variable
     * @param string $contract_subtype - keyword
     * @return boolean - result
     */
    private static function _prepareContractGT2(&$model, $gt2, $contract_subtype) {
        //get print settings for the 2nd type grouping table
        if ($contract_subtype == 'rent' && !empty(self::$_settings['rent_pattern_id'])) {
            //IMPORTANT: the rent type of subcontract should have custom GT2 settings
            //           the pattern is deactivated and is used only to set the columns visibility
            $print_properties = $model->getGT2PrintSettings(self::$_settings['rent_pattern_id']);
        } else {
            $print_properties = $model->getGT2PrintSettings(self::$_pattern->get('id'));
        }

        $subcontract = clone $model;
        $table = $gt2;
        $gt2['vars'] = array();
        $styles_for_template = array();
        $lang = self::$_registry['lang'];

        foreach ($print_properties as $key => $property) {
            // style properties
            if (!empty($property['style'])) {
                $styles_for_template[$key] = $property['style'];
            }
            // label for table caption
            if ($key == 'var_' . $table['id']) {
                if (isset($property['labels'][$lang])) {
                    $gt2['label'] = $property['labels'][$lang];
                }
                continue;
            }
            foreach ($table['vars'] as $idx => $var) {
                if ($key == 'var_' . $var['id']) {
                    $gt2['vars'][$idx] = $var;
                    // label for field
                    if (isset($property['labels'][$lang])) {
                        $gt2['vars'][$idx]['label'] = $property['labels'][$lang];
                    }
                    // aggregates
                    if (isset($property['agregate'])) {
                        if ($property['agregate'] != 'none') {
                            $gt2['vars'][$idx]['agregate'] = $property['agregate'];
                        } elseif (isset($gt2['vars'][$idx]['agregate'])) {
                            unset($gt2['vars'][$idx]['agregate']);
                        }
                    }
                    continue 2;
                }
            }
            foreach ($table['plain_vars'] as $idx => $var) {
                if ($key == 'var_' . $var['id']) {
                    // label for total field
                    if (isset($property['labels'][$lang])) {
                        $gt2['plain_vars'][$idx]['label'] = $property['labels'][$lang];
                    }
                    continue 2;
                }
            }
        }

        //IMPORTANT: remove the discounts for the rent subcontracts (Bug 3693, comments 6-7)!
        if ($contract_subtype == 'rent') {
            foreach($gt2['values'] as &$row) {
                $row['discount_percentage'] = 0;
            }
        }

        $subcontract = clone $model;
        $subcontract->set('grouping_table_2', $gt2, true);
        $subcontract->calculateGT2();

        $gt2 = $subcontract->get('grouping_table_2');
        $gt2 = $subcontract->calculateGT2Agregates($gt2);

        $groupingViewer = new Viewer(self::$_registry);
        $groupingViewer->setFrameset('_gt2_vars.html');

        $groupingViewer->data['styles'] = $styles_for_template;
        $groupingViewer->data['table']  = $gt2;

        $model->extender->add($contract_subtype . '_gt2', $groupingViewer->fetch());
        $model->extender->add($contract_subtype . '_total', $gt2['plain_values']['total']);
        $model->extender->add($contract_subtype . '_total_with_vat', $gt2['plain_values']['total_with_vat']);
        unset($subcontract);
        unset($gt2);

        return true;
    }

    /**
     * Prepares some custom data for
     * invoices issued from FO/SOT contracts (type 7)
     * potocols for FO/SOT contracts (document type 6)
     * non-recurrent invoices issued from SOT contracts (type 4)
     *
     * @param Registry $registry - registry object
     * @param Model $model - finance_incomes_reason model (the invoice)
     * @param Pattern $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
    */
    public static function prepareInvoices2(&$registry, &$model, &$pattern, &$params = array()) {
        //save the previous registry lang
        $registry_lang_old = $registry['lang'];

        //prepare th eorigini of the financial document
        $origin = '';

        //this plugin should work only for invoices from contract type 7
        //and documents type 6
        if (get_class($model) == 'Finance_Incomes_Reason' && ($model->get('type') == PH_FINANCE_TYPE_INVOICE || $model->get('type') == PH_FINANCE_TYPE_PRO_INVOICE)) {
            $model->getRelatives(array('get_reason' => true));
            $reason = $model->get('reason');

            // This restricts the plugin to be used only for contract invoices type 7
            if (is_object($reason) && get_class($reason) == 'Contract') {
                if ($reason->get('type') == 7) {
                    //Contract type 7
                    $origin = 'FO';
                } elseif ($reason->get('type') == 4) {
                    //Contract type 4
                    //IMPORTANT: check if the invoice is NOT RECURRENT
                    $query = 'SELECT fit.recurrent' . "\n".
                             'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' as fit' . "\n".
                             'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' as fiti' . "\n".
                             '  ON fit.id=fiti.parent_id' . "\n".
                             'WHERE invoice_id=' . $model->get('id');
                    $is_recurrent = $registry['db']->GetOne($query);
                    //IMPORTANT: the plugin is only for the non-recurrent invoices of contract 4 (Bug 4286, comment 0)
                    if ($is_recurrent === '0') {
                        $origin = 'SOT';
                    }
                }
            }
            unset($reason);
        } elseif (get_class($model) != 'Document' || $model->get('type') != 6) {
            //Protocol for FO/SOT
            $origin = 'FO';
        }

        if (!$origin) {
            return false;
        }

        self::$registry = &$registry;
        self::$model = &$model;

        $lang = $model->get('model_lang');
        $translations = $model->getTranslations();
        $prec = $registry['config']->getSectionParams('precision');

        if (get_class($model) == 'Finance_Incomes_Reason') {
            //get print settings for the 2nd type grouping table
            $print_properties = $model->getGT2PrintSettings($params['pattern_id']);
            $orig_table = $model->get('grouping_table_2');
        } else {
            $orig_vars = $model->get('vars');
            $print_properties = $model->getGT2PrintSettings($params['pattern_id']);
        }

        foreach ($translations as $t_lang) {
            $model->set('model_lang', $t_lang, true);

            if ($registry['lang'] != $model->get('model_lang')) {
                //change the registry lang
                $registry['translater']->reloadFiles($lang);
            }

            if (get_class($model) == 'Finance_Incomes_Reason') {
                $model->getGT2Vars();
                $table = $model->get('grouping_table_2');
            } else {
                $model->getVarsForTemplate(true);
                $table = $model->get('vars');
                foreach ($table as $v) {
                    if ($v['type'] == 'gt2') {
                        $table = $v;
                        break;
                    }
                }
            }

            $gt2 = $table;
            $gt2['vars'] = array();
            $styles_for_template = array();

            foreach ($print_properties as $key => $property) {
                // style properties
                if (!empty($property['style'])) {
                    $styles_for_template[$key] = $property['style'];
                }
                // label for table caption
                if ($key == 'var_' . $table['id']) {
                    if (isset($property['labels'][$t_lang])) {
                        $gt2['label'] = $property['labels'][$t_lang];
                    }
                    continue;
                }
                foreach ($table['vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        $gt2['vars'][$idx] = $var;
                        // label for field
                        if (isset($property['labels'][$t_lang])) {
                            $gt2['vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        // aggregates
                        if (isset($property['agregate'])) {
                            if ($property['agregate'] != 'none') {
                                $gt2['vars'][$idx]['agregate'] = $property['agregate'];
                            } elseif (isset($gt2['vars'][$idx]['agregate'])) {
                                unset($gt2['vars'][$idx]['agregate']);
                            }
                        }
                        continue 2;
                    }
                }
                foreach ($table['plain_vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        // label for total field
                        if (isset($property['labels'][$t_lang])) {
                            $gt2['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        continue 2;
                    }
                }
            }

            // process data
            $gt2['values'] = array();
            $i = -1;
            //sort and group rows in requested conditions
            foreach ($table['values'] as $row => $values) {
                if (empty($values)) continue;
                switch($origin) {
                    case 'FO':
                        //group by office ID
                        if (empty($gt2['values'][$values['article_barcode']])) {
                            $gt2['values'][$values['article_barcode']] = array();
                        }
                        if (trim($values['free_text1']) != '') {
                            //change the article name as requested
                            $values['article_name'] = $values['free_text1'];
                        }
                        $values['qm_changed'] = 0;
                        if (empty($values['article_delivery_code'])) {
                            //this row is not in a group - just add it
                            if ($values['article_height'] > 0 && $values['article_second_code']) {
                                //exchange the original quantity and measure with the requested
                                $values['quantity'] = $values['article_height'];
                                $values['article_measure_name'] = $values['article_second_code'];
                                $values['qm_changed'] = 1;
                                $values['price'] = round($values['subtotal'] / $values['quantity'], $prec['gt2_rows']);
                            }
                            $gt2['values'][$values['article_barcode']][$i] = $values;
                            //we will use negative indexes as the iterator could match the id fo a group
                            $i--;
                        } else {
                            //group item
                            if (empty($gt2['values'][$values['article_barcode']][$values['article_delivery_code']])) {
                                //this group is for the first time into the office - add the row
                                if ($values['article_height'] > 0 && $values['article_second_code']) {
                                    //exchange the original quantity and measure with the requested
                                    $values['quantity'] = $values['article_height'];
                                    $values['article_measure_name'] = $values['article_second_code'];
                                    $values['qm_changed'] = 1;
                                    $values['price'] = round($values['subtotal'] / $values['quantity'], $prec['gt2_rows']);
                                }
                                $gt2['values'][$values['article_barcode']][$values['article_delivery_code']] = $values;
                            } else {
                                //we have to merge the values of the new row to the existing values

                                //calculate the subtotal SUM of the 2 rows
                                $subtotal = $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['quantity'] * $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['price'] + $values['subtotal'];
                                if ($gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['qm_changed'] == 1) {
                                    // we must use the second quantity/measure
                                    if ($gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['article_measure_name'] == $values['article_second_code']) {
                                        //sum quantities and calculate the new price
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['quantity'] += $values['article_height'];
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['price'] = round($subtotal / $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['quantity'], $prec['gt2_rows']);
                                    } else {
                                        //set quantity 1 and measure "count"
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['quantity'] = 1;
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['price'] = round($subtotal, $prec['gt2_rows']);
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['qm_changed'] = 2;
                                    }
                                } elseif ($gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['qm_changed'] == 0) {
                                    //we must use the first quantity
                                    if ($gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['article_measure_name'] == $values['article_measure_name']) {
                                        //sum quantities and calculate the new price
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['quantity'] += $values['quantity'];
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['price'] = round($subtotal / $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['quantity'], $prec['gt2_rows']);
                                    } else {
                                        //set quantity 1 and measure "count"
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['quantity'] = 1;
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['price'] = round($subtotal, $prec['gt2_rows']);
                                        $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['qm_changed'] = 2;
                                    }
                                } else {
                                    //quantity 1 and measure "count" has been set yet
                                    $gt2['values'][$values['article_barcode']][$values['article_delivery_code']]['price'] += $values['subtotal'];
                                }
                            }
                        }
                        break;
                    case 'SOT':
                        if ($values['free_field3'] == 3) {
                            $gt2['values'][$values['article_barcode']][$i] = $values;
                            $i--;
                        }
                        break;
                }
            }

            //get offices names as we have to sort rows by office NAME not ID
            $query = 'SELECT parent_id FROM ' . DB_TABLE_OFFICES_I18N .
                     ' WHERE parent_id IN("' . implode('", "', array_keys($gt2['values'])) . '") AND lang="' . $t_lang . '" ORDER BY name';
            $offices = $registry['db']->GetCol($query);

            $offices = array_flip($offices);
            //sort by office name
            uksort($gt2['values'], function ($a, $b) use ($offices) {
                    return $offices[$a] > $offices[$b];
                }
            );

            //sort by group
            foreach ($gt2['values'] as $k => $values) {
                ksort($values);
                $gt2['values'][$k] = $values;
            }

            $final = array();
            foreach($gt2['values'] as $values) {
                foreach($values as $v) {
                    $final[] = $v;
                }
            }

            $gt2['values'] = $final;
            $model->set('grouping_table_2', $gt2, true);
            $model->calculateGT2();
            $gt2 = $model->get('grouping_table_2');
            $groupingViewer = new Viewer($registry);
            $groupingViewer->setFrameset('_gt2_vars.html');

            $groupingViewer->data['styles'] = $styles_for_template;
            $groupingViewer->data['table'] = $gt2;

            $registry->set('lang', $t_lang, true);
            if (get_class($model) == 'Finance_Incomes_Reason') {
                $model->extender->add($t_lang . '_grouping_table_2', $groupingViewer->fetch());
            } else {
                $model->extender->add($t_lang . '_a_group_table_2', $groupingViewer->fetch());
            }
            $registry->set('lang', $registry_lang_old, true);
            unset($gt2);
            unset($table);
        }

        $model->set('model_lang', $lang, true);
        if (get_class($model) == 'Finance_Incomes_Reason') {
            $model->set('grouping_table_2', $orig_table, true);
        } else {
            $model->set('vars', $orig_vars, true);
        }
        return true;
    }
}

?>
