<table border="1" width="100%" style="font-family: {$settings.pdf_font_family};">
  <thead style="display: table-header-group;">
    <tr>
      <th width="100">{#pdf_daily_production_rfid#}</th>
      <th width="120">{#pdf_daily_production_order#}</th>
      <th width="80">{#pdf_daily_production_model#}</th>
      <th width="50">{#pdf_daily_production_fabric_type#}</th>
      <th width="50">{#pdf_daily_production_color_code#}</th>
      <th width="50">{#pdf_daily_production_size#}</th>
      <th width="30">{#pdf_daily_production_quantity#}</th>
      <th width="40">{#pdf_daily_production_fabric1#}</th>
      <th width="40">{#pdf_daily_production_fabric2#}</th>
      <th width="40">{#pdf_daily_production_fabric3#}</th>
      <th width="40">{#pdf_daily_production_zipper1#}</th>
      <th width="40">{#pdf_daily_production_zipper2#}</th>
      <th width="50">{#pdf_daily_production_tape_cord1#}</th>
      <th width="50">{#pdf_daily_production_tape_cord2#}</th>
      <th width="80">{#pdf_daily_production_expedition#}</th>
      <th>{#pdf_daily_production_instructions#}</th>
      <th width="50">{#pdf_daily_production_atelier#}</th>
    </tr>
  </thead>
  <tbody>
    {foreach from=$rows item='row' name='daily_production_rows'}
      {if $smarty.foreach.daily_production_rows.first}
        {assign var='reset_daily_production_rows_background_color' value=true}
      {else}
        {assign var='reset_daily_production_rows_background_color' value=false}
      {/if}
      {cycle values=',background-color: #f2f2f2;' assign='background_color' reset=$reset_daily_production_rows_background_color}
      {assign var='td_height' value=''}
      {if $settings.daily_production_pdf_td_height}
        {capture assign='td_height'}height: {$settings.daily_production_pdf_td_height}px; {/capture}
      {/if}
      <tr>
        <td style="{$td_height}vertical-align: middle; {$background_color}">{$row.rfid|escape}</td>
        <td style="{$td_height}vertical-align: middle; {$background_color}">{$row.order|escape}</td>
        <td style="{$td_height}vertical-align: middle; {$background_color}">{$row.model|escape}</td>
        <td style="{$td_height}vertical-align: middle; text-align: center; {$background_color}">{$row.fabric_type|escape}</td>
        <td style="{$td_height}vertical-align: middle; text-align: center; {$background_color}">{$row.color_code|escape}</td>
        <td style="{$td_height}vertical-align: middle; text-align: center; {$background_color}">{$row.size|escape}</td>
        <td style="{$td_height}vertical-align: middle; text-align: center; {$background_color}">{$row.quantity|number_format_depending_type:2:".":" "}</td>
        <td style="{$td_height}vertical-align: middle; text-align: right; {$background_color}">{$row.fabric1|escape}</td>
        <td style="{$td_height}vertical-align: middle; text-align: right; {$background_color}">{$row.fabric2|escape}</td>
        <td style="{$td_height}vertical-align: middle; text-align: right; {$background_color}">{$row.fabric3|escape}</td>
        <td style="{$td_height}vertical-align: middle; white-space: nowrap; {$background_color}">{$row.zipper1.code|escape}{if $row.zipper1.length}/{$row.zipper1.length|escape} {#pdf_daily_production_cm#}{/if}</td>
        <td style="{$td_height}vertical-align: middle; white-space: nowrap; {$background_color}">{$row.zipper2.code|escape}{if $row.zipper2.length}/{$row.zipper2.length|escape} {#pdf_daily_production_cm#}{/if}</td>
        <td style="{$td_height}vertical-align: middle; white-space: nowrap; {$background_color}">{$row.tape_cord1.code|escape}{if $row.tape_cord1.size}/{$row.tape_cord1.size|escape} {#pdf_daily_production_mm#}{/if}</td>
        <td style="{$td_height}vertical-align: middle; white-space: nowrap; {$background_color}">{$row.tape_cord2.code|escape}{if $row.tape_cord2.size}/{$row.tape_cord2.size|escape} {#pdf_daily_production_mm#}{/if}</td>
        <td style="{$td_height}vertical-align: middle; text-align: center; {$background_color}">{$row.expedition|date_format:#date_short#}</td>
        <td style="{$td_height}{$background_color}">{$row.instructions|escape|nl2br}</td>
        <td style="{$td_height}vertical-align: middle; {$background_color}">{$row.atelier|escape}</td>
      </tr>
    {foreachelse}
      <tr>
        <td colspan="17">{#no_items_found#}</td>
      </tr>
    {/foreach}
  </tbody>
</table>