</w:t></w:r></w:p>
<w:tbl>
<w:tblPr>
  <w:tblStyle w:val="TableGrid"/>
  <w:tblLayout w:type="fixed"/>
  <w:tblW w:w="{$smarty.const.TABLES_WIDTH_FULL_PAGE}" w:type="dxa"/>
  {if $var.label}<w:tblCaption w:val="{$var.label}"/>{/if}
  <w:tblBorders>
    <w:top w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:left w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:right w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:insideH w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:insideV w:val="single" w:sz="4" w:space="0" w:color="auto"/>
  </w:tblBorders>
  <w:tblLook w:val="04A0" w:firstRow="1" w:lastRow="0" w:firstColumn="1" w:lastColumn="0" w:noHBand="0" w:noVBand="1"/>
</w:tblPr>
<w:tblGrid>
  {foreach from=$grid item='grid_width'}
    <w:gridCol w:w="{$grid_width}"/>
  {/foreach}
</w:tblGrid>

{* Table CUSTOM Header Row*}
<w:tr>
  <w:trPr>
    <w:tblHeader />
    <w:cantSplit />
  </w:trPr>
  {foreach from=$var.headers key='key' item='header'}
    {if !$var.hidden[$key]}
      {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="center"/>
          {* Do not split across pages *}
          <w:keepNext/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:b/>
          </w:rPr>
      {/strip}{/capture}
      <w:tc>
        <w:tcPr>
          <w:vAlign w:val="center"/>
          <w:gridSpan w:val="{$header.colspan}"/>
          <w:shd w:val="clear" w:color="auto" w:fill="{$smarty.const.COLOR_DARK_GREEN}"/>
        </w:tcPr>
        {$style_tags}
            <w:t>{$header.label}</w:t>
          </w:r>
        </w:p>
      </w:tc>
    {/if}
  {/foreach}
</w:tr>

{* Table Header Row*}
{if !$hide_header_row}
<w:tr>
  <w:trPr>
    <w:tblHeader />
    <w:cantSplit />
  </w:trPr>
  {foreach name='i' key='key' from=$var.names item='name'}
    {if !$var.hidden[$key]}
      {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="center"/>
          {* Do not split across pages *}
          <w:keepNext/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:b/>
          </w:rPr>
      {/strip}{/capture}
        <w:tc>
          <w:tcPr>
            <w:vAlign w:val="center"/>
            <w:shd w:val="clear" w:color="auto" w:fill="{$smarty.const.COLOR_LIGHT_GREEN}"/>
          </w:tcPr>
          {$style_tags}
            <w:t>{$var.labels[$key]}</w:t>
          </w:r>
        </w:p>
      </w:tc>
    {/if}
  {/foreach}
</w:tr>
{/if}

{foreach name='i' from=$var.values item='val'}
  <w:tr>
    <w:trPr>
      <w:cantSplit />
    </w:trPr>
    {foreach key='key' from=$var.names item='name'}
      {if !$var.hidden[$key]}
        {capture assign='style_tags'}{strip}
        <w:p>
          <w:pPr>
            <w:spacing w:after="0"/>
            {if $var.text_align[$key]}<w:jc w:val="{$var.text_align[$key]}"/>{/if}
            {* Do not split across pages *}
            <w:keepNext/>
          </w:pPr>
          <w:r>
        {/strip}{/capture}
        <w:tc>
          {$style_tags}
          <w:t>{strip}
          {if $var.types[$key] eq 'text' or $var.types[$key] eq 'autocompleter' or $var.types[$key] eq 'textarea'}
            {$val[$key]}
          {elseif $var.types[$key] eq 'date'}
            {$val[$key]|date_format:#date_short#}
          {elseif $var.types[$key] eq 'datetime'}
            {$val[$key]|date_format:#date_mid#}
          {elseif $var.types[$key] eq 'time'}
            {$val[$key]|date_format:#time_short#}
          {elseif $var.types[$key] eq 'dropdown' || $var.types[$key] eq 'radio'}
            {if $var[$name].overwrite_value}
              {$val[$key]}
            {elseif $var[$name].options}
              {foreach from=$var[$name].options item='option'}
                {if $option.option_value eq $val[$key]}
                  {if preg_match("#^<#", $option.extended_value) || preg_match("#^<#", $option.label)}
                    {$option.extended_value|default:$option.label}
                  {else}
                    {$option.extended_value|default:$option.label|nl2br}
                  {/if}
                {/if}
              {/foreach}
            {elseif $var[$name].optgroups}
              {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                {foreach from=$optgroup item='option'}
                  {if $option.option_value eq $val[$key]}
                    {if preg_match("#^<#", $option.extended_value) || preg_match("#^<#", $option.label)}
                      {$option.extended_value|default:$option.label}
                    {else}
                      {$option.extended_value|default:$option.label|nl2br}
                    {/if}
                  {/if}
                {/foreach}
              {/foreach}
            {/if}
          {elseif $var.types[$key] eq 'file_upload'}
            {$val[$key]}
          {/if}
          {if ($val[$key] || $val[$key] === '0') && $var.back_labels[$key]}{$var.back_labels[$key]}{/if}
        {/strip}</w:t></w:r></w:p></w:tc>
      {/if}
    {/foreach}
  </w:tr>
{/foreach}
</w:tbl><w:p><w:r><w:t>
