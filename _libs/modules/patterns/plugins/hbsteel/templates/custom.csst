.label {
    width: 465px;
    margin: 0mm;
    border: 0.5px dotted black;
    border-left-width: 0;
    border-top-width: 0;
    float: left;
    border-collapse: collapse;
    font-family: <PERSON><PERSON><PERSON>;
    font-size: 16px;
    table-layout: fixed;
}
.label.first-col {
    border-left-width: 0.5px;
}
.label.first-row {
    border-top-width: 0.5px;
}
.label td {
    padding: 2px 3px;
}
.head-row {
  vertical-align: middle;
}
.head-row img {
    width: 235px;
    /*float: left;*/
}
.head-row table {
    width: 100%;
    border-collapse: collapse;
}
.head-row table, .head-row table td {
    padding: 0;
    margin: 0;
}
.head-row .doc-num {
    text-align: right;
}
.label span {
    font-weight: bold;
    font-size: 20px;
}

.customer-name {
    text-align: center;
    font-size: 20px;
    font-weight: bolder;
    border-top: 0.5px solid black;
}
.trademark-name {
    text-align: center;
    border-bottom: 0.5px solid black;
}
.label .image {
    text-align: center;
    padding:15px;
    height: 171px;
}
.label .image img {
    height: 80px;
}

.label .nom-name,
.label .quantity {
    width: 150px;
    padding-left: 20px;
}
.label .size,
.label .form-detail {
    width: 325px;
}
.break {
    clear: both;
    font-size: 0;
}

.product {
    font-family: "DIN Pro";
    font-weight: normal;
    font-size: 16px;
}
.product .title {
    font-weight: bold;
    font-size: 20px;
    margin-bottom: 10px;
    text-align: center;
}
.detail {
    padding: 40px 25px;
}
.detail .name {
    width: 150px;
}
.detail .name div {
    width: 50px;
    border: 1px solid black;
    padding: 7px;
}
.detail .quantity {
    width: 60px;
    text-align: right;
    padding: 0 10px;
}
.detail .spacer {
    width: 50px;
    text-align: center;
    padding: 0 10px;
}
.detail .length {
    width: 60px;
    padding: 0 10px;
}
.detail .image {
    width: 400px;
}
/*
Fix: remove whitespace symbols
 */
 .head-row,
 .customer-name,
 .trademark-name,
 .nom-name,
 .form-detail,
 .quantity,
 .size,
 .image {
    white-space: nowrap;
}
