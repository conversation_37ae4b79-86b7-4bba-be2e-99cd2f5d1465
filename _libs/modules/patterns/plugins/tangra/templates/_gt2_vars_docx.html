{capture assign='table_id_var'}{if isset($table.meta_id)}meta_{/if}id{/capture}
{capture assign='table_class_name'}var_{$table.$table_id_var}{/capture}
</w:t></w:r></w:p>
<w:tbl>
<w:tblPr>
  {$styles.$table_class_name.width|convert2docx_width:'w:tblW'}
  <w:tblBorders>
    <w:top w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:left w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:right w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:insideH w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:insideV w:val="single" w:sz="4" w:space="0" w:color="auto"/>
  </w:tblBorders>
  <w:tblLook w:val="04A0" w:firstRow="1" w:lastRow="0" w:firstColumn="1" w:lastColumn="0" w:noHBand="0" w:noVBand="1"/>
</w:tblPr>
<w:tblGrid>
  <w:gridCol w:w="400"/>
{foreach name='i' key='key' from=$table.vars item='var'}
  {capture assign='class_name'}var_{$var.id}{/capture}
  {if !$styles.$class_name.hide}
    {if $styles.$class_name.width}
      {$styles.$class_name.width|convert2docx_width:'w:gridCol'}
    {else}
      <w:gridCol w:w="1000"/>
    {/if}
  {/if}
{/foreach}
</w:tblGrid>
<w:tr>
{capture assign='style_tags_num'}{strip}
  <w:p>
    <w:pPr>
      <w:spacing w:after="0"/>
      <w:jc w:val="{$styles.$table_class_name.align}"/>
    </w:pPr>
    <w:r>
      <w:rPr>
        <w:rFonts w:ascii="{$styles.$table_class_name.font}" w:hAnsi="{$styles.$table_class_name.font}" w:cs="{$styles.$table_class_name.font}"/>
        <w:b/>
        {if $styles.$table_class_name.italic}<w:i/>{/if}
        <w:color w:val="{$styles.$table_class_name.color}"/>
        {if $styles.$table_class_name.size}
          {$styles.$table_class_name.size|convert2docx_fontsize:'w:sz'}
          {$styles.$table_class_name.size|convert2docx_fontsize:'w:szCs'}
        {/if}
      </w:rPr>
{/strip}{/capture}
{capture assign='style_tags_num2'}{strip}
  <w:p>
    <w:pPr>
      <w:spacing w:after="0"/>
      <w:jc w:val="{$styles.$table_class_name.align}"/>
    </w:pPr>
    <w:r>
      <w:rPr>
        <w:rFonts w:ascii="{$styles.$table_class_name.font}" w:hAnsi="{$styles.$table_class_name.font}" w:cs="{$styles.$table_class_name.font}"/>
        {if $styles.$table_class_name.bold}<w:b/>{/if}
        {if $styles.$table_class_name.italic}<w:i/>{/if}
        <w:color w:val="{$styles.$table_class_name.color}"/>
        {if $styles.$table_class_name.size}
          {$styles.$table_class_name.size|convert2docx_fontsize:'w:sz'}
          {$styles.$table_class_name.size|convert2docx_fontsize:'w:szCs'}
        {/if}
      </w:rPr>
{/strip}{/capture}
<w:tc>{$style_tags_num}<w:t>{#num#|escape}</w:t></w:r></w:p></w:tc>
{foreach name='i' key='key' from=$table.vars item='var'}
  {capture assign='class_name'}var_{$var.id}{/capture}
  {if !$styles.$class_name.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name.font}" w:hAnsi="{$styles.$class_name.font}" w:cs="{$styles.$class_name.font}"/>
            <w:b/>
            {if $styles.$class_name.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name.color}"/>
            {if $styles.$class_name.size}
              {$styles.$class_name.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tc><w:tcPr>{if $styles.$class_name.width}{$styles.$class_name.width|convert2docx_width:'w:tcW'}{else}<w:tcW w:w="0" w:type="auto"/>{/if}</w:tcPr>{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
  {/if}
{/foreach}
</w:tr>

  {counter assign='cols_count' print=false name='colcount' start=0}
  {assign var='agregates_count' value=0}
  {foreach name='i' from=$table.values item='val'}
    {foreach key='key' from=$table.vars item='var'}
      {capture assign='class_name'}var_{$var.id}{/capture}
      {if $smarty.foreach.i.first && !$styles.$class_name.hide}
        {counter assign='cols_count' print=false name='colcount'}
      {/if}
      {if $smarty.foreach.i.first && !empty($var.agregate) && !$styles.$class_name.hide}
        {counter assign='agregates_count' print=false name='agregatescount'}
      {/if}
    {/foreach}
  {/foreach}
  {foreach name='i' from=$table.values item='val'}
    {if preg_match('#\D+#', $val.free_text1)}
      {assign var='captionRow' value=$val.free_text1}
    {/if}
    {if $captionRow && $captionRow != $prevCaptionRow}
      {if !$smarty.foreach.i.first && !empty($table.agregate_results_grouped)}{include file=`$templatesPath`_gt2_subtotals_docx.html group_by=$prevCaptionRow}{/if}
      {assign var='var' value=$table.vars.article_name}
      {capture assign='class_name'}var_{$var.id}{/capture}
       <w:tr>
         <w:tc>
          <w:p>
           <w:pPr>
             <w:spacing w:after="0"/>
             <w:jc w:val="{$styles.$class_name.align}"/>
           </w:pPr>
           <w:r>
             <w:rPr>
               <w:rFonts w:ascii="{$styles.$class_name.font}" w:hAnsi="{$styles.$class_name.font}" w:cs="{$styles.$class_name.font}"/>
               <w:b/>
               {if $styles.$class_name.italic}<w:i/>{/if}
               <w:color w:val="{$styles.$class_name.color}"/>
               {if $styles.$class_name.size}
               {$styles.$class_name.size|convert2docx_fontsize:'w:sz'}
               {$styles.$class_name.size|convert2docx_fontsize:'w:szCs'}
               {/if}
             </w:rPr>
             <w:tcPr><w:gridSpan w:val="{$cols_count+1}"/></w:tcPr>
             <w:t>{$captionRow}</w:t>
           </w:r>
          </w:p>
         </w:tc>
       </w:tr>
      {assign var='prevCaptionRow' value=$captionRow}
    {/if}
  <w:tr>
    <w:tc>{$style_tags_num2}<w:t>{$smarty.foreach.i.iteration}</w:t></w:r></w:p></w:tc>
    {foreach key='key' from=$table.vars item='var'}
      {capture assign='class_name'}var_{$var.id}{/capture}
      {if !$styles.$class_name.hide}
        {capture assign='style_tags'}{strip}
          <w:p>
            <w:pPr>
              <w:spacing w:after="0"/>
              <w:jc w:val="{$styles.$class_name.align}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:rFonts w:ascii="{$styles.$class_name.font}" w:hAnsi="{$styles.$class_name.font}" w:cs="{$styles.$class_name.font}"/>
                {if $styles.$class_name.bold}<w:b/>{/if}
                {if $styles.$class_name.italic}<w:i/>{/if}
                <w:color w:val="{$styles.$class_name.color}"/>
                {if $styles.$class_name.size}
                  {$styles.$class_name.size|convert2docx_fontsize:'w:sz'}
                  {$styles.$class_name.size|convert2docx_fontsize:'w:szCs'}
                {/if}
              </w:rPr>
        {/strip}{/capture}
      <w:tc><w:tcPr>{if $styles.$class_name.width}{$styles.$class_name.width|convert2docx_width:'w:tcW'}{else}<w:tcW w:w="0" w:type="auto"/>{/if}</w:tcPr>{$style_tags}<w:t>{strip}
        {if $var.type eq 'dropdown'}
          {foreach from=$var.options item=option}
            {if $val.$key eq $option.option_value}{$option.label|escape}{/if}
          {/foreach}
        {elseif $var.type eq 'date'}
          {$val.$key|date_format:#date_short#}
        {elseif $var.type eq 'datetime'}
          {$val.$key|date_format:#date_mid#}
        {elseif $var.type eq 'time'}
          {$val.$key|date_format:#time_short#}
        {else}
          {$val.$key}
        {/if}
        {if ($val.$key || $val.$key === '0') && $var.back_label} {$var.back_label}{/if}
      {/strip}</w:t></w:r></w:p></w:tc>
      {/if}
    {/foreach}
  </w:tr>
{/foreach}

  {if $agregates_count > 0}
    <w:tr>
      <w:tc><w:p><w:r><w:t></w:t></w:r></w:p></w:tc>
      {foreach name='j' key='key' from=$table.vars item='var'}
        {capture assign='class_name'}var_{$var.id}{/capture}
        {if !$styles.$class_name.hide}
        {capture assign='style_tags'}{strip}
          <w:p>
            <w:pPr>
              <w:spacing w:after="0"/><w:jc w:val="{$styles.$class_name.align}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:rFonts w:ascii="{$styles.$class_name.font}" w:hAnsi="{$styles.$class_name.font}" w:cs="{$styles.$class_name.font}"/>
                {if $styles.$class_name.bold}<w:b/>{/if}
                {if $styles.$class_name.italic}<w:i/>{/if}
                <w:color w:val="{$styles.$class_name.color}"/>
                {if $styles.$class_name.size}
                  {$styles.$class_name.size|convert2docx_fontsize:'w:sz'}
                  {$styles.$class_name.size|convert2docx_fontsize:'w:szCs'}
                {/if}
              </w:rPr>
        {/strip}{/capture}
        <w:tc>{$style_tags}<w:t>{strip}
          {if $var.agregate}
            {capture assign='ag_text'}gt2_tagregates_{$var.agregate}{/capture}
            {$smarty.config.$ag_text}: {$table.agregate_results[$var.name]}
          {/if}
        {/strip}</w:t></w:r></w:p></w:tc>
        {/if}
      {/foreach}
    </w:tr>
  {/if}

  {if $table.totals_texts_colspan}
    {assign var='totals_texts_colspan' value=$table.totals_texts_colspan}
  {else}
    {assign var='totals_texts_colspan' value=3}
  {/if}
  {assign var='totals_colspan' value=$cols_count-$totals_texts_colspan}

  {capture assign='totals_colspan_tags'}{if $totals_colspan}<w:tcPr><w:gridSpan w:val="{$totals_colspan}"/></w:tcPr>{/if}{/capture}
  {capture assign='totals_texts_colspan_tags'}{if $totals_texts_colspan}<w:tcPr><w:gridSpan w:val="{$totals_texts_colspan}"/></w:tcPr>{/if}{/capture}

  {assign var='var' value=$table.plain_vars.total_without_discount}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc>{$totals_colspan_tags}{$style_tags}<w:t></w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>{$table.plain_values.total_without_discount|default:0}{if $var.back_label} {$var.back_label}{/if}</w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_discount_percentage}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc>{$totals_colspan_tags}{$style_tags}<w:t></w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>{$table.plain_values.total_discount_percentage|default:0}{if $var.back_label} {$var.back_label}{/if}</w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_discount_value}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc>{$totals_colspan_tags}{$style_tags}<w:t></w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>{$table.plain_values.total_discount_value|default:0}{if $var.back_label} {$var.back_label}{/if}</w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_surplus_percentage}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc>{$totals_colspan_tags}{$style_tags}<w:t></w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>{$table.plain_values.total_surplus_percentage|default:0}{if $var.back_label} {$var.back_label}{/if}</w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_surplus_value}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc>{$totals_colspan_tags}{$style_tags}<w:t></w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>{$table.plain_values.total_surplus_value|default:0}{if $var.back_label} {$var.back_label}{/if}</w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}

  {if $table.plain_vars.total_no_vat_reason_text && !$table.plain_vars.total_no_vat_reason_text.hidden && $table.plain_values.total_no_vat_reason_text && !preg_match('#display\s*:\s*none#', $styles.$class_name_total_nvrt)}
    {assign var='display_no_vat_rate_text' value=1}
  {else}
    {assign var='display_no_vat_rate_text' value=0}
  {/if}

  {assign var='var' value=$table.plain_vars.total}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="right"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    {capture assign='style_tags2'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc><w:tcPr>{if $totals_colspan}<w:gridSpan w:val="{$totals_colspan}"/>{/if}{if !$table.show_totals_inwords && !$display_no_vat_rate_text}<w:vMerge w:val="restart" />{/if}</w:tcPr>{$style_tags}<w:t></w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags2}<w:t>{$table.plain_values.total|default:0}{if $var.back_label} {$var.back_label}{/if}</w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_vat_rate}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {capture assign='class_name_total_nvrt'}var_{$table.plain_vars.total_no_vat_reason_text.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    {capture assign='style_tags2'}{strip}
      <w:p>
        <w:pPr><w:spacing w:after="0"/><w:jc w:val="{$styles.$class_name_total_nvrt.align}"/></w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total_nvrt.font}" w:hAnsi="{$styles.$class_name_total_nvrt.font}" w:cs="{$styles.$class_name_total_nvrt.font}"/>
            {if $styles.$class_name_total_nvrt.bold}<w:b/>{/if}
            {if $styles.$class_name_total_nvrt.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total_nvrt.color}"/>
            {if $styles.$class_name_total_nvrt.size}
              {$styles.$class_name_total_nvrt.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total_nvrt.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc><w:tcPr>{if $totals_colspan}<w:gridSpan w:val="{$totals_colspan}"/>{/if}{if !$table.show_totals_inwords && !$display_no_vat_rate_text}<w:vMerge />{/if}</w:tcPr>{$style_tags2}<w:t>{strip}
        {if $display_no_vat_rate_text}
          {$table.plain_vars.total_no_vat_reason_text.label|escape}:
          {$table.plain_values.total_no_vat_reason_text|escape}{if $table.plain_vars.total_no_vat_reason_text.back_label} {$table.plain_vars.total_no_vat_reason_text.back_label}{/if}
        {/if}
      {/strip}</w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>
        {$table.plain_values.total_vat_rate}%{if $var.back_label} {$var.back_label}{/if}
      </w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_vat}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
    {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc><w:tcPr>{if $totals_colspan}<w:gridSpan w:val="{$totals_colspan}"/>{/if}{if !$table.show_totals_inwords}<w:vMerge{if $display_no_vat_rate_text} w:val="restart"{/if}/>{/if}</w:tcPr>{$style_tags}<w:t>
        {if $table.show_totals_inwords}{#in_words#|mb_ucfirst}: {$table.plain_values.total_vat|inwords:$table.plain_values.currency:$lang}{/if}
      </w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>
        {$table.plain_values.total_vat|default:0}{if $var.back_label} {$var.back_label}{/if}
      </w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}


  {assign var='var' value=$table.plain_vars.total_with_vat}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
     {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc><w:tcPr>{if $totals_colspan}<w:gridSpan w:val="{$totals_colspan}"/>{/if}{if !$table.show_totals_inwords}<w:vMerge/>{/if}</w:tcPr>{$style_tags}<w:t>
        {if $table.show_totals_inwords}{#in_words#|mb_ucfirst}: {$table.plain_values.total_with_vat|inwords:$table.plain_values.currency:$lang}{/if}
      </w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>
        {$table.plain_values.total_with_vat|default:0}{if $var.back_label} {$var.back_label}{/if}
      </w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}



  {assign var='var' value=$table.plain_vars.currency}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$table.hide_totals && !$var.hidden && !$styles.$class_name_total.hide}
     {capture assign='style_tags'}{strip}
      <w:p>
        <w:pPr>
          <w:spacing w:after="0"/>
          <w:jc w:val="{$styles.$class_name_total.align}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:rFonts w:ascii="{$styles.$class_name_total.font}" w:hAnsi="{$styles.$class_name_total.font}" w:cs="{$styles.$class_name_total.font}"/>
            {if $styles.$class_name_total.bold}<w:b/>{/if}
            {if $styles.$class_name_total.italic}<w:i/>{/if}
            <w:color w:val="{$styles.$class_name_total.color}"/>
            {if $styles.$class_name_total.size}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:sz'}
              {$styles.$class_name_total.size|convert2docx_fontsize:'w:szCs'}
            {/if}
          </w:rPr>
    {/strip}{/capture}
    <w:tr>
      <w:tc><w:tcPr>{if $totals_colspan}<w:gridSpan w:val="{$totals_colspan}"/>{/if}{if !$table.show_totals_inwords}<w:vMerge/>{/if}</w:tcPr>{$style_tags}<w:t></w:t></w:r></w:p></w:tc>
      <w:tc>{$totals_texts_colspan_tags}{$style_tags}<w:t>{$var.label|escape}</w:t></w:r></w:p></w:tc>
      <w:tc>{$style_tags}<w:t>
        {$table.plain_values.currency}{if $var.back_label} {$var.back_label}{/if}
      </w:t></w:r></w:p></w:tc>
    </w:tr>
  {/if}
</w:tbl><w:p><w:r><w:t>
