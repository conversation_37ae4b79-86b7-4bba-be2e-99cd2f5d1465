<?php

/**
 * Assignments_Configurator Class
 */
Class Assignments_Configurator extends Model {
    public $modelName = 'Assignments_Configurator';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        $this->registry = $registry;

        //additional custom settings
    }

    /**
     * Validate model
     *
     * (non-PHPdoc)
     * @see Model::validate()
     * @return bool - result of the operation
     */
    public function validate($action = '') {
        $post_filter = $this->get('config_id');
        if (empty($post_filter)) {
            return false;
        }
        return true;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        $action = 'replace';

        if ($this->validate()) {

            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();
                return false;
            }

        } else {
            $this->registry['messages']->setError($this->i18n('error_assignments_configurator_save'));
            return false;
        }
    }

    /**
     * Add or update configuration
     *
     * @return bool - result of the operation
     */
    public function replace() {

        $db = $this->registry['db'];

        $replace = false;
        $post_filter = $this->get('config_id');
        $added_by = $this->registry['currentUser']->get('id');

        $db->StartTrans();

        if (!empty($post_filter)) {
            if (is_numeric($post_filter)) {
                $query = 'SELECT * FROM ' . DB_TABLE_ASSIGNMENTS_CONFIGURATOR . "\n" .
                         'WHERE id='. $post_filter . "\n";
                $record = $db->GetRow($query);
                if (count($record)) {
                    if ($added_by == $record['added_by']) {
                        $replace = true;
                    }
                }
            }
            $set = $this->prepareMainData();

            if ($replace) {
                //update
                $query1 = 'UPDATE ' . DB_TABLE_ASSIGNMENTS_CONFIGURATOR . "\n" .
                          'SET ' . implode(', ', $set) . "\n" .
                          'WHERE id=' . $post_filter;
                $db->Execute($query1);
                $this->set('id', $post_filter, true);
            } else {
                //insert
                $set['added']         = sprintf("added=now()");
                $set['added_by']      = sprintf("added_by=%d", $added_by);
                $set['name']          = sprintf("name='%s'", $post_filter);

                $query1 = 'INSERT INTO ' . DB_TABLE_ASSIGNMENTS_CONFIGURATOR . "\n" .
                          'SET ' . implode(', ', $set) . "\n" .
                          'ON DUPLICATE KEY UPDATE ' . "\n" .
                          implode(', ', $set);

                $db->Execute($query1);
                if ($id = $db->Insert_Id()) {
                    $this->set('id', $id, true);
                }
            }

            if ($db->ErrorMsg()) {
                $this->registry['logger']->dbError('add new assignments configurator base details', $db, $query1);
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    public function prepareMainData() {
        $set = array();
        $set['params']     = sprintf("params='%s'", $this->get('user_ids'));
        if ($this->get('layout')) {
            $model = ucfirst(General::plural2singular($this->get('layout')));
            if ($this->get('layout') == 'finance') {
                $query = 'SELECT model FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' WHERE id = ' . $this->get('model_type');
                $model = $this->registry['db']->GetOne($query);
            }
            $set['model']     = sprintf("model='%s'", $model);
        } else {
            $set['model']     = sprintf("model='%s'", $this->get('model'));
        }
        $set['model_type']     = sprintf("model_type='%s'", $this->get('model_type'));
        $set['assignment_type']     = sprintf("assignment_type='%s'", $this->get('assignment_type'));

        return $set;
    }
}

?>
