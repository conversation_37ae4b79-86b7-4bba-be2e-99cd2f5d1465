<?php
class Assignments_Configurators_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Assignments_Configurator';

    public function execute() {
        switch($this->action) {
            case 'ajax_assignments_configurator':
                $this->_assignments_configurator();
                break;
        }
    }

    /**
     * Add or delete assignments configurator
     */
    private function _assignments_configurator() {

        $request = &$this->registry['request'];

        $assignment_type = $request->get('assignment_type');
        $config_action = $request->get('config_action');
        $config_id = $request->get('config_id');

        $errors = false;
        if ($config_id) {
            require_once PH_MODULES_DIR . 'assignments/models/assignments.configurators.factory.php';

            if ($config_action == 'save') {
                $configurator = Assignments_Configurators::buildModel($this->registry, '');
                if (!$configurator->save()) {
                    $errors = true;
                }
            } elseif ($config_action == 'delete') {
                if (!Assignments_Configurators::purge($this->registry, $config_id)) {
                    $this->registry['messages']->setError($this->i18n('error_assignments_configurator_delete'));
                    $errors = true;
                }
            }
        }

        $result = array();
        if ($errors) {
            $result['errors'] = $this->registry['messages']->getErrors();
        } else {
            // prepare saved assignments configurations
            require_once PH_MODULES_DIR . 'assignments/models/assignments.configurators.factory.php';
            if ($request->get('layout')) {
                $filters = array(
                    'where' => array(
                        $request->get('layout') == 'finance' ? 'ac.model LIKE "finance%"' : 'ac.model = "' . General::plural2singular($request->get('layout')) . '"',
                        'ac.added_by = \'' . $this->registry['currentUser']->get('id') . '\'',
                    )
                );
            } else {
                $filters = array(
                    'where' => array(
                        'ac.model = "' . $request->get('model') . '"',
                        '(ac.model_type = ' . $request->get('model_type') . ' OR ac.model_type = 0)',
                        'ac.added_by = \'' . $this->registry['currentUser']->get('id') . '\'',
                    )
                );
            }
            $config_templates = Assignments_Configurators::search($this->registry, $filters);

            $viewer = new Viewer($this->registry);
            $viewer->data['assignment_type'] = $assignment_type . $request->get('row');
            $viewer->data['config_templates'] = $config_templates;
            $viewer->data['exclude_div'] = 1;
            $viewer->setFrameset(PH_MODULES_DIR . 'assignments/templates/_assignments_configurator_panel.html');
            $result['data'] = $viewer->fetch();
        }

        print 'var result = ' . json_encode($result) . ';';
        exit;
    }


}