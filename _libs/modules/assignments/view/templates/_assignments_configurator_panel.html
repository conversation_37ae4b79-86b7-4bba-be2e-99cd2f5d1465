{if !$exclude_div}
  <aside class="nz-popup" id="assignments_configurator">
    <div class="nz-popup-surface">
{/if}
      <header class="nz-popup-header drag" >
        <div id="assignments_configurator_title"><i class="material-icons" title="{#draggable#}">drag_indicator</i>{#assignments_configurator_load_save#|escape}</div>
        <i class="material-icons pointer nz-popup-header-close" onclick="$('assignments_configurator').classList.remove('nz--opened')" title="{#close#}">close</i>
      </header>
      <section class="nz-popup-body">
        <div id="assignments_messages_container"></div>
        <div class="nz-assignments-configurator-body">
          <table style="width: 100%; border: 0px none;">
            {foreach from=$config_templates item='templ'}
            <tr class="{cycle name='assignments_cycle' values='t_even,t_odd'}">
              <td>
                {if $templ->get('model') ne 'all' && $templ->get('model') ne 'noone'}
                <i class="material-icons pointer" onclick="manageAssignmentsConfigurator('{$templ->get('id')}', 'assignments_configurator', 'delete');" title="{#delete#}">delete</i>
                {/if}
                <div class="pointer" style="display: inline-block; width: 200px!important; color: #666666;" onclick="loadSavedAssignments('{$templ->get('params')}');" title="{#config_load#}: {$templ->get('name')|escape}">
                  {if $templ->get('model') eq 'all' || $templ->get('model') eq 'noone'}<strong>{/if}
                  {$templ->get('name')|mb_truncate:30:'...':true|escape|default:"&nbsp;"}
                  {if $templ->get('model') eq 'all' || $templ->get('model') eq 'noone'}</strong>{/if}
                </div>
              </td>
            </tr>
            {/foreach}
            <tr class="{if ($config_templates|@count % 2)}t_odd{else}t_even{/if}">
              <td>
                {include file='input_text.html'
                name='assignments_configurator_name'
                standalone=true
                width=200
                label=#assignments_configurator_name#
                show_placeholder='label'
                onkeypress='return saveAssignmentsConfiguratorOnEnter(event);'
                }
                {include file='input_hidden.html'
                name='assignments_configurator_type'
                standalone=true
                value=$assignment_type
                }
                <i id="save_assignments_config" class="material-icons pointer" onclick="manageAssignmentsConfigurator($('assignments_configurator_name').value, 'assignments_configurator', 'save');" title="{#save#}">save</i>
              </td>
            </tr>
          </table>
        </div>
      </section>
{if !$exclude_div}
    </div>
  </aside>
{/if}
<script type="text/javascript">
  new Draggable('assignments_configurator', {ldelim}handle: 'assignments_configurator_title'{rdelim});
</script>
