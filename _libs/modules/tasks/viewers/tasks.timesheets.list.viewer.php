<?php

class Tasks_Timesheets_List_Viewer extends Viewer {
    public $template = '';

    public function prepare() {
        $this->model = $this->registry['task'];
        $this->data['task'] = $this->model;

        //suffix for the session param
        if ($this->registry['request']->get('parent_module')) {
            $suffix = $this->registry['request']->get('parent_module') . '_';
            $this->data['parent_module'] = $this->registry['request']->get('parent_module');
        } else {
            $suffix = 'tasks_' . $this->model->get('id');
        }

        $session_param = 'timesheets_ajax_' . $suffix;
        if ($this->registry['request']->get('session_param')) {
            $session_param = $this->registry['request']->get('session_param');
        }

        //search the timesheets for the current task
        require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheets.factory.php';
        $filters = Tasks_Timesheets::saveSearchParams(
                    $this->registry,
                    array(
                        'where' => array('tt.task_id = ' . $this->model->get('id')),
                        'sanitize' => true),
                    $session_param);
        if ($this->registry['request']->get('event_id')) {
            $filters['where'][] = 'tt.event_id=' . $this->registry['request']->get('event_id');
            $this->data['task']->set('event_id', $this->registry['request']->get('event_id'), true);
            $this->data['task']->getTimesheetTime(true);
            $this->data['task']->getTimesheetBillingTime(true);
        }
        list($timesheets, $pagination) = Tasks_Timesheets::pagedSearch($this->registry, $filters);

        $this->data['timesheets'] = $timesheets;
        $this->data['pagination'] = $pagination;
        $this->data['timesheets_use_ajax'] = true;
        $this->data['timesheets_session_param'] = $session_param;

        $sort_base_link = sprintf("%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s",
            $_SERVER['PHP_SELF'],
            $this->registry['module_param'], 'tasks',
            $this->registry['controller_param'], 'timesheets',
            'timesheets', 'list',
            'model_id', $this->model->get('id'));
        if ($this->registry['request']->get('parent_module')) {
            $sort_base_link .= '&amp;parent_module=' . $this->registry['request']->get('parent_module');
        }
        if ($this->registry['request']->get('event_id')) {
            $sort_base_link .= '&amp;event_id=' . $this->registry['request']->get('event_id');
        }

        $this->getSortables(array('module' => 'tasks', 'controller' => 'timesheets'));

        $this->data['timesheets_sort'] = $this->prepareAjaxSort($filters, $session_param, $session_param, $sort_base_link, true);

        $this->setFrameset('_timesheets_list_panel.html');
    }
}

?>
