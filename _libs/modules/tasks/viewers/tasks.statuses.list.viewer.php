<?php

class Tasks_Statuses_List_Viewer extends Viewer {
    public $template = 'statuses_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'tasks.statuses.factory.php';
        $filters = Tasks_Statuses::saveSearchParams($this->registry);
        list($tasks_statuses, $pagination) = Tasks_Statuses::pagedSearch($this->registry, $filters);

        $this->data['tasks_statuses'] = $tasks_statuses;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('tasks_statuses');
        $this->data['title'] = $title;
    }
}

?>
