<?php

class Tasks_Multiadd_Viewer extends Viewer {
    public $template = 'multiadd.html';

    public function prepare() {
        $this->data['tasks'] = $this->registry['tasks'];

        $this->data['tasktype'] = $this->registry['tasktype'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;type=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->registry['request']->get('type'));
        $this->data['submitLink'] = $this->submitLink;

        if ($this->theme->isModern()) {
            $this->data['dont_wrap_content'] = true;
            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('tasks_multiadd');
        $this->data['title'] = $title;
    }
}

?>
