<?php

class Tasks_Filter_Viewer extends Viewer {
    public $template = 'filter.html';

    public function prepare() {
        require_once $this->modelsDir . 'tasks.factory.php';

        $request = &$this->registry['request'];

        $sort_base = '';
        //we open this from autocompleter's select button
        $autocomplete_filter = $request->get('autocomplete_filter');
        if ($autocomplete_filter) {
            if ($autocomplete_filter != 'session') {
                $filters = $this->data['autocomplete_filters'];
                unset($filters['display']);
                $this->registry['session']->remove('filter_task');
            } else {
                $filters = array();
            }

            //set sort link
            $sort_base = sprintf("%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s",
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'], $this->module,
                $this->registry['action_param'], $this->action,
                'autocomplete_filter', 'session',
                'uniqid', $request['uniqid']);
        } else {
            $filters = array();
            if ($request->get('status')) {
                $filters['where'][] = 't.status =\'' . $request->get('status') . '\'';
                $this->data['status'] = $request->get('status');
            }
            if ($request->get('form_name')) {
                $this->data['form_name'] = $request->get('form_name');
            }
        }

        //'open_from' - module that popup is opened from, 'model_id' - id of opening model
        //parameters signify that predefined filter should be loaded (only this time)
        if ($request->get('open_from') && $request->get('model_id') > 0
            && !preg_match('#(save|del)filter#', $request->get('filters_action'))) {
            $where = array('f.module = \'tasks\'', 'f.controller = \'tasks\'', 'f.active = 1', 'f.user_defined = 0');

            // connecting task with task
            if ($request->get('open_from') == 'tasks') {
                $where[] = 'f.module_from = \'tasks\'';
                $where[] = 'f.controller_from = \'tasks\'';
            }

            //search conditions for calling module and controller are set
            $saved_filters = array();
            if (count($where) == 6) {
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');

                $pred_filters = array(
                    'where' => $where,
                    'model_lang' => $this->registry->get('model_lang'),
                    'sanitize' => true);

                $predefined_filter = Filters::searchOne($this->registry, $pred_filters);

                if ($predefined_filter) {
                    //set these properties in order to load predefined filter into session
                    $request->set('filters_action', 'loadfilter', 'get', true);
                    $request->set('filter_name', $predefined_filter->get('id'), 'get', true);

                    //load filters from the DB
                    $saved_filters = Tasks::saveSearchParams($this->registry, array(), 'filter_');

                    $request->remove('filters_action');
                    $request->remove('filter_name');

                    //clear variable
                    $filters = array();
                }

                if (!empty($saved_filters) && preg_match('#currentCustomer#', implode('', $saved_filters['where']))) {
                    $saved_filters = $this->registry['session']->get('filter_task');
                    $model_plural = $request->get('open_from');
                    $factory_name = ucfirst($model_plural);

                    require_once PH_MODULES_DIR . $model_plural . '/models/' . $model_plural . '.factory.php';

                    //try to guess the table alias for the filters
                    $alias = $factory_name::getAlias($model_plural, $model_plural);

                    $model = $factory_name::searchOne(
                        $this->registry,
                        array(
                            'where' => array($alias . '.id = ' . intval($request->get('model_id'))),
                            'sanitize' => true,
                            'get_fields' => array('customer', 'customer_name_code')
                        ));
                    if ($model) {
                        foreach ($saved_filters['compare_options'] as $idx => $filter) {
                            if (preg_match('#currentCustomer#', $filter)) {
                                $saved_filters['compare_options'][$idx] = "= '%s'";
                                $saved_filters['values'][$idx] = $model->get('customer');
                                $saved_filters['values_autocomplete'][$idx] = sprintf('[%s] %s',
                                    $model->get('customer_code'), $model->get('customer_name'));
                            }
                        }
                    }
                    $this->registry['session']->remove('filter_task');
                    $this->registry['session']->set('filter_task', $saved_filters, '', true);
                }

                if (empty($saved_filters)) {
                    // set default filters
                    $filters = array('where' => array('t.active = \'1\''));
                }
            }
        }

        $filters = Tasks::saveSearchParams($this->registry, $filters, 'filter_');

        //when searching for customer-dependent task from autocompleter, prepare customer name for session filters
        if ($autocomplete_filter && $autocomplete_filter != 'session' && $request->isRequested('filters')) {
            $customer = '';
            $customer_values_autocomplete = '';

            $request_filters = $request->get('filters');
            if (is_array($request_filters) && array_key_exists('<customer>', $request_filters)) {
                $customer = $request_filters['<customer>'];
            }

            if ($customer) {
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $customer_model = Customers::searchOne($this->registry, array(
                    'sanitize' => true,
                    'model_lang' => $this->registry->get('model_lang'),
                    'where' => array('c.id = ' . $customer)));
                if (!empty($customer_model)) {
                    $customer_values_autocomplete = sprintf('[%s] %s',
                                                    $customer_model->get('code'),
                                                    $customer_model->get('name') .
                                                        (!$customer_model->get('is_company') ? ' ' . $customer_model->get('lastname') : ''));
                } else {
                    $customer = '';
                }
            }

            if ($customer) {
                $session_filters = $this->registry['session']->get('filter_task');
                foreach ($session_filters['search_fields'] as $idx => $fld) {
                    if ($fld == 't.customer') {
                        $session_filters['values_autocomplete'][$idx] = $customer_values_autocomplete;
                        break;
                    }
                }
                $this->registry['session']->set('filter_task', $session_filters, '', true);
            }
        }

        if ($request->get('model_lang')) {
            $filters['model_lang'] = $request->get('model_lang');
            $this->data['model_lang'] = $request->get('model_lang');
            $sort_base .= '&amp;model_lang=' . $request->get('model_lang');
        }

        // get necessary fields according to visible columns
        $filters['get_fields'] = array('type', 'department', 'status', 'customer');
        // only executor assignees
        $assignments_types = array(
            'owner' => 'getAssignmentsOwner',
            'responsible' => 'getAssignmentsResponsible',
            'decision' => 'getAssignmentsDecision',
            'observer' => 'getAssignmentsObserver'
        );
        foreach ($assignments_types as $at => $at_flag) {
            $this->registry->set($at_flag, true, true);
        }

        if (!empty($filters['where'])) {
            $customize = array();
            $found = 0;
            foreach ($filters['where'] as $where) {
                if (preg_match('/t\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val       = trim(preg_replace('/t\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'type', 'value' => $val);
                    $found++;

                    //get type for multi actions
                    $type = $val;
                }
                if (preg_match('/tt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val                        = trim(preg_replace('/tt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize                  = array('name' => 'section', 'value' => $val);
                    $this->data['type_section'] = $customize['value'];
                    $found++;
                }
            }
            if ($found == 1 && $customize) {
                if (!$this->setCustomTemplate($customize) && $customize['name'] == 'type') {
                    //the list of types is only shown when there is no custom outlook template
                    $this->data['type'] = $customize['value'];
                }
            } else {
                $this->setCustomTemplate();
            }
        } else {
            $this->setCustomTemplate();
        }

        // modelFields contains visible columns
        if (!empty($this->modelFields)) {
            $filters['get_fields'] = $this->modelFields;

            if (in_array('tags', $this->modelFields)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }
        }

        list($tasks, $pagination) = Tasks::pagedSearch($this->registry, $filters);

        $this->data['tasks'] = $tasks;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters, $sort_base);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('tasks');
        $this->data['title'] = $title;
    }
}

?>
