{** Displays a type of assignment and manages toggle and assign from dashlet 
* ALLOWED PARAMETERS:
* a_type           - type of assignment
* INFO:
* dashlet_id       - has a value if included from dashlet; not needed if included from list/search
*}

    {if !$a_type}
      {assign var=a_type value='assignments_owner'}
    {/if}
  
    <div{if !$task->get($a_type) || is_array($task->get($a_type)) && !($task->get($a_type)|@count gt 3)}{if $task->checkPermissions('assign')} style="padding: 3px 0 3px 0; cursor:pointer;" onclick="changeAssignments({$task->get('id')}, 'tasks', '{$a_type}')" title="{#tasks_assign_change#}"{/if}{/if}>
      {assign var='long_text' value=''}
      {assign var='short_text' value=''}
      {if $task->get($a_type)}
        {foreach from=$task->get($a_type) item='assignment' name='assignees'}
          {capture assign='long_text'}
            {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}<br />
          {/capture}
          {if $smarty.foreach.assignees.iteration<=3}
            {capture assign='short_text'}
              {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}<br />
            {/capture}
          {/if}
        {foreachelse}
          &nbsp;
        {/foreach}
        {if is_array($task->get($a_type)) && $task->get($a_type)|@count gt 3}
          <div id="{$a_type}_{$dashlet_id}_part_{$smarty.foreach.i.iteration}">
            <div{if $task->checkPermissions('assign')} onclick="changeAssignments({$task->get('id')}, 'tasks', '{$a_type}')" style="cursor:pointer; padding: 3px 0 3px 0;" title="{#tasks_assign_change#}"{/if}>
            {$short_text}
            </div>
            <img src="{$theme->imagesUrl}small/arrow_down.png" border="0" alt="{#tasks_show_full_assignments_list#|escape}" title="{#tasks_show_full_assignments_list#|escape}" onclick="toggleContent('{$a_type}_{$dashlet_id}', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
          </div>
          <div id="{$a_type}_{$dashlet_id}_full_{$smarty.foreach.i.iteration}" style="display: none;">
            <div{if $task->checkPermissions('assign')} onclick="changeAssignments({$task->get('id')}, 'tasks', '{$a_type}')" style="cursor:pointer; padding: 3px 0 3px 0;" title="{#tasks_assign_change#}"{/if}>
            {$long_text}
            </div>
            <img src="{$theme->imagesUrl}small/arrow_up.png" border="0" alt="{#tasks_hide_full_assignments_list#|escape}" title="{#tasks_hide_full_assignments_list#|escape}" onclick="toggleContent('{$a_type}_{$dashlet_id}', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
          </div>
        {else}
          {$short_text}
        {/if}
      {else}
        &nbsp;
      {/if}
    </div>