<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<br />
{strip}
<a href="{$smarty.server.PHP_SELF}?{$module_param}=tasks&amp;type=&amp;type_section=">
{if !isset($type_section) && (!isset($type) || $type eq '')}
  <strong>{#all#|escape}</strong>
{else}
  {#all#|escape}
{/if}
</a>,&nbsp;
{foreach name='t' from=$menu_types_custom_listing item=task_type}
  <a href="{$smarty.server.PHP_SELF}?{$module_param}=tasks&amp;type={$task_type->get('id')}&amp;type_section=">
  {if !isset($type_section) && $task_type->get('id') eq $type}
    <strong>{$task_type->get('name')|escape}</strong>
  {else}
    {$task_type->get('name')|escape}
  {/if}
  </a>{if !$smarty.foreach.t.last},&nbsp;{/if}
{/foreach}
{/strip}

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=tasks&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="tasks" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=tasks" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.full_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.full_num.link}">{$basic_vars_labels.full_num|default:#tasks_full_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#tasks_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.priority.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.priority.link}">{$basic_vars_labels.severity|default:#tasks_severity#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{$basic_vars_labels.type|default:#tasks_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{$basic_vars_labels.customer|default:#tasks_customer#|escape}</div></td>
          <td class="t_caption t_border {$sort.department.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.department.link}">{$basic_vars_labels.department|default:#tasks_department#|escape}</div></td>
          <td class="t_caption t_border {$sort.owners.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.owners.link}">{#tasks_assign_owner#|escape}</div></td>
          <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{$basic_vars_labels.status|default:#tasks_status#|escape}</div></td>
          <td class="t_caption t_border {$sort.planned_start_date.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.planned_start_date.link}">{$basic_vars_labels.planned_start_date|default:#tasks_planned_start_date#|escape}</div></td>
          <td class="t_caption t_border {$sort.planned_finish_date.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.planned_finish_date.link}">{$basic_vars_labels.planned_finish_date|default:#tasks_planned_finish_date#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#tasks_age#|escape}</div></td>
          <td class="t_caption t_border {$sort.exec_time.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.exec_time.link}">{#tasks_exec_time#|escape}</div></td>
          {*<td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#date#|escape}</div></td>*}
          <td class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.tags.link}">{#tasks_tags#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$tasks item='task'}
      {strip}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#tasks_name#|escape}:</strong> {$task->get('name')|escape}<br />
        <strong>{$basic_vars_labels.type|default:#tasks_type#|escape}:</strong> {$task->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$task->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$task->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$task->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$task->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$task->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$task->get('status_modified_by_name')|escape}<br />
        {if $task->isDeleted()}<strong>{#deleted#|escape}:</strong> {$task->get('deleted')|date_format:#date_mid#|escape}{if $task->get('deleted_by_name')} {#by#|escape} {$task->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$task->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $task->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='task_status'}
        {if $task->get('status') eq 'planning'}
          {#help_tasks_status_planning#}
        {elseif $task->get('status') eq 'progress'}
          {#help_tasks_status_progress#}
        {elseif $task->get('status') eq 'finished'}
          {#help_tasks_status_finished#}
        {/if}
        {if $task->get('substatus_name')}
          <br />
          {#help_tasks_substatus#}{$task->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
      {if !$task->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$task->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="13" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$task disabled='edit,delete,view'}
          </td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$task->get('active')} t_inactive{/if}{if $task->get('deleted_by')} t_deleted{/if} {$task->get('severity')}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$task->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($task->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($task->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright" nowrap="nowrap">
          {if $task->get('files_count')}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=tasks&amp;tasks=attachments&amp;attachments={$task->get('id')}">
              <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$task->get('id')})"
                     onmouseout="mclosetime()" />
            </a>
          {/if}
          {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.full_num.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$task->get('id')}">{$task->get('full_num')}</a></td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$task->get('id')}">{$task->get('name')|escape|default:"&nbsp;"}</a></td>
          {capture assign='severity_name'}tasks_{$task->get('severity')}{/capture}
          <td class="t_border {$sort.priority.isSorted}">{$smarty.config.$severity_name|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.type.isSorted}">{$task->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.customer.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$task->get('customer')}" title="{#view#|escape}: {$task->get('customer_name')|escape}">{$task->get('customer_name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.department.isSorted}">{$task->get('department_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.owner.isSorted}" style="width:150px">
            {include file=`$templatesDir`_assignments_dashlet.html a_type='assignments_owner'}
          </td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$task_status|escape caption=#help_tasks_status#|escape width=250}{if $task->checkPermissions('setstatus')} onclick="changeStatus({$task->get('id')}, 'tasks')" style="cursor:pointer;"{/if}
          {/capture}
          {capture assign='task_expired'}
            {if $task->get('status') != 'finished' && $task->get('planned_finish_date') && $task->get('planned_finish_date')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {#tasks_expired_legend#}: <strong>{$task->get('planned_finish_date')|date_format:#date_mid#}</strong>!
            {/if}
          {/capture}
          {if $task->get('status') != 'finished' && (($task->get('planned_finish_date') && $task->get('planned_finish_date')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
              <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=$task_expired|escape caption=#tasks_expired#|escape width=250} />
          {/if}
          {if $task->get('substatus_name')}
            {if $task->get('icon_name')}
              <img src="{$smarty.const.PH_TASKS_STATUSES_URL}{$task->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              <img src="{$theme->imagesUrl}tasks_{$task->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            <span {$popup_and_onclick}>{$task->get('substatus_name')}</span>
          {else}
            <img src="{$theme->imagesUrl}tasks_{$task->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {capture assign='status_param'}tasks_status_{$task->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
          <td class="t_border {$sort.planned_start_date.isSorted}">{$task->get('planned_start_date')|escape|date_format:#date_mid#|default:"&nbsp;"}</td>
          <td class="t_border {$sort.planned_finish_date.isSorted}">{$task->get('planned_finish_date')|escape|date_format:#date_mid#|default:"&nbsp;"}</td>
          <td class="t_border {$sort.added.isSorted}" nowrap="nowrap">{$task->get('age_formatted')|escape}</td>
          <td class="t_border {$sort.exec_time.isSorted}" nowrap="nowrap">{$task->get('exec_time_formatted')|escape}</td>
          {*<td class="t_border {$sort.added.isSorted}" nowrap="nowrap">{$task->get('added')|date_format:#date_short#|escape}</td>*}
          <td class="t_border {$sort.tags.isSorted}" {if $task->getModelTags() && $task->get('available_tags_count') gt 0 && $task->checkPermissions('tags_view') && $task->checkPermissions('tags_edit')} onclick="changeTags({$task->get('id')}, 'tasks')" style="cursor: pointer;" title="{#tags_change#}"{/if}>
            {if $task->get('model_tags')|@count gt 0 && $task->checkPermissions('tags_view')}
              {foreach from=$task->get('model_tags') item='tag' name='ti'}
                <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$task}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="16">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="16"></td>
        </tr>
      </table>
      {include file="`$theme->templatesDir`_severity_legend.html" prefix='tasks'}
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html 
               tags=$tags
               include="tags,multistatus"
               session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
