{strip}
  {assign var='layout' value=$task->getLayoutsDetails('name')}
  <strong>{$layout.name|escape}:</strong> {$task->get('name')|mb_truncate|escape|default:"&nbsp;"}<br />
  {assign var='layout' value=$task->getLayoutsDetails('type')}
  <strong>{$layout.name|escape}:</strong> {$task->get('type_name')|escape}<br />
  <strong>{#added#|escape}:</strong> {$task->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$task->get('added_by_name')|escape}<br />
  <strong>{#modified#|escape}:</strong> {$task->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$task->get('modified_by_name')|escape}<br />
  <strong>{#status_modified#|escape}:</strong> {$task->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$task->get('status_modified_by_name')|escape}<br />
  {if $task->isDeleted()}<strong>{#deleted#|escape}:</strong> {$task->get('deleted')|date_format:#date_mid#|escape}{if $task->get('deleted_by_name')} {#by#|escape} {$task->get('deleted_by_name')|escape}{/if}<br />{/if}
  {assign var='layout' value=$task->getLayoutsDetails('status')}
  <strong>{$layout.name|escape}:</strong>&nbsp;
  {if $task->get('status') eq 'planning'}
    {#help_tasks_status_planning#}
  {elseif $task->get('status') eq 'progress'}
    {#help_tasks_status_progress#}
  {elseif $task->get('status') eq 'finished'}
    {#help_tasks_status_finished#}
  {/if}
  {if $task->get('substatus_name')}
    <br />
    {#help_tasks_substatus#}{$task->get('substatus_name')}
  {/if}
  <br />

  <strong>{#translations#|escape}:</strong>
    <span class="translations">
    {foreach from=$task->get('translations') item='trans'}
      <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $task->get('model_lang')} class="selected"{/if} />
    {/foreach}
    </span><br />
{/strip}
