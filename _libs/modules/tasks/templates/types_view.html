<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='types_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {mb_truncate_overlib text=$tasks_type->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_name_plural'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$tasks_type->get('name_plural')|escape|default:''}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_counter'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $tasks_type->get('counter')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=counters&amp;counters=view&amp;view={$tasks_type->get('counter')}" target="_blank">{$counter_name|escape}</a> - {$counter_formula|escape}
            {/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='type_section'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$tasks_type->get('section_name')}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_planning_requires_comment'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{capture assign='current_planning_requires_comment_label'}required_statuses_option_{$tasks_type->get('planning_requires_comment')}{/capture}
            {$smarty.config.$current_planning_requires_comment_label}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_progress_requires_comment'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{capture assign='current_progress_requires_comment_label'}required_statuses_option_{$tasks_type->get('progress_requires_comment')}{/capture}
            {$smarty.config.$current_progress_requires_comment_label}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_finished_requires_comment'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{capture assign='current_finished_requires_comment_label'}required_statuses_option_{$tasks_type->get('finished_requires_comment')}{/capture}
            {$smarty.config.$current_finished_requires_comment_label}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_requires_completed_minitasks'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $tasks_type->get('requires_completed_minitasks')}{#yes#}{else}{#no#}{/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$tasks_type->get('description')|mb_wordwrap|url2href}
          </td>
        </tr>
        {if $layouts_search_url}
        <tr>
          <td class="labelbox">{help label_content=#menu_layouts#}</td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            <a href="{$layouts_search_url}">{#help_menu_layouts#|escape}</a>
          </td>
        </tr>
        {/if}
        <tr>
          <td class="labelbox">{help label='types_template_vars'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {include file='view_checkbox_group.html'
                     standalone=true
                     name='template_vars'
                     options=$available_template_vars
                     value=$tasks_type->get('template_vars')}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_validate'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {foreach from=$tasks_type->get('validate') item='field' name='f'}
              {$field|escape|default:'&nbsp;'}{if !$smarty.foreach.f.last}<br />{/if}
            {/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_validate_unique'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $tasks_type->get('validate_unique_current_year')}
              <span style="position: absolute; left: 450px;">{#tasks_types_validate_unique_current_year#|escape}</span>
            {/if}
            {foreach from=$tasks_type->get('validate_unique') item='field' name='f'}
              {$field|escape|default:'&nbsp;'}{if !$smarty.foreach.f.last}<br />{/if}
            {/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_assignment_types'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {foreach from=$tasks_type->get('assignment_types') item='a_type' name='a'}
              {$a_type|escape|default:'&nbsp;'}{if !$smarty.foreach.a.last}<br />{/if}
            {/foreach}
          </td>
        </tr>
        {if !empty($customers_types)}
        <tr>
          <td class="labelbox">{help label='types_related_customers_types'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {include file='view_checkbox_group.html'
                    standalone=true
                    name='related_customers_types'
                    options=$customers_types
                    value=$tasks_type->get('related_customers_types')}
          </td>
        </tr>
        {/if}
        <tr>
          <td class="labelbox">{help label='types_default_department'}</td>
          <td>&nbsp;</td>
          <td>{$default_department|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_default_group'}</td>
          <td>&nbsp;</td>
          <td>{$default_group|escape}</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$tasks_type}
</div>
