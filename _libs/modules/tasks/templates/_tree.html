<div id="tree_{$sfx}_container" class="tree_container">
  <a href="javascript:Zapatec.Tree.all['tree_{$sfx}'].collapseAll()">{#collapse_all#|escape}</a> |
  <a href="javascript:Zapatec.Tree.all['tree_{$sfx}'].expandAll()">{#expand_all#|escape}</a>
  <ul id="tree_{$sfx}">
    <li>{include file="`$templatesDir`_tree_node.html" node=$list.0 objects=$objects}</li>
  </ul>
</div>


{* tree initialization *}
<script type='text/javascript'>
var func_{$sfx} = function() {ldelim}initTree('{$sfx}'){rdelim}
Event.observe(window, 'load', func_{$sfx});
</script>
