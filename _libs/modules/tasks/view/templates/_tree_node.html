<div class="nz-tree-indicator">
  {if !empty($node.children)}
    <i class="material-icons nz-tree-indicator-icon">chevron_right</i>
  {/if}
</div>
<div class="nz-tree-node-content">
  <div class="nz-tree-node-row">
    {assign var=objectId value=`$node.id`}
    {assign var=object value=$objects[$node.id]}
    {if $object && $object->modelName eq 'Task'}
      {include file="`$templatesDir`_tree_node_task.html" node=$node object=$object objects=$objects}
    {elseif $object && $object->modelName eq 'Document'}
      {include file="`$templatesDir`_tree_node_document.html" node=$node object=$object objects=$objects}
    {else}
      [{$node.id}]
    {/if}
  </div>
  {if !empty($node.children)}
    {include file="`$templatesDir`_tree_children.html" list=$node.children objects=$objects}
  {/if}
</div>
