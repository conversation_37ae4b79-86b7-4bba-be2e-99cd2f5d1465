<div class="nz-page-wrapper{if !empty($side_panels) && count($side_panels)} nz--has-side-panel{/if}">
  <div class="nz-page-main-column nz-content-surface{if empty($_isPopup)} nz-elevation--z3{/if}">
    <div class="nz-page-title"><h1>{$title|escape}</h1>
      <div class="nz-page-title-tools">
        {if isset($available_page_actions.general)}
          {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.general}
        {/if}
      </div>
      <div class="nz-page-title-sidetools">
        {if isset($available_page_actions.quick)}
          {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.quick  onlyIcons=true}
        {/if}
      </div>
    </div>
    <div class="nz-page-actions">
      {include file="`$theme->templatesDir`actions_box.html" available_actions=$available_page_actions.context}
    </div>

    {include file="`$templatesDir`_add_popout_xtemplate.html"}
    {include file="`$theme->templatesDir`_translations_menu.html" translations=$translations}

    <div id="form_container" class="nz-page-content main_panel_container">
      <input type="hidden" name="id" id="id" value="{$task->get('id')}" />
      <input type="hidden" name="model_lang" id="model_lang" value="{$task->get('model_lang')|default:$lang}" />
      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td>
            {include file='_timesheet_stopwatch_button.html' model=$task}
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            {foreach from=$task->get('layouts_details') key='lkey' item='layout'}
              {if $layout.view}
              {if $lkey ne 'configurtor'}
              <tr{if !$layout.visible} style="display: none;"{/if}>
                <td colspan="3" class="t_caption3 pointer">
                  <div class="floatr index_arrow_anchor">
                    <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                  </div>
                  <div class="layout_switch" onclick="toggleViewLayouts(this)" id="task_{$layout.keyword}_switch">
                    <a name="task_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                  </div>
                </td>
              </tr>
              {/if}

              {if $lkey eq 'full_num'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td>{#required#}</td>
                <td>
                  {$task->get('full_num')|escape}
                </td>
              </tr>
              {elseif $lkey eq 'name'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{#required#}</td>
                <td>
                  {include file=`$templatesDir`_info.html assign='info'}
                  <span {popup text=$info|escape caption=#system_info#|escape width=250}>{mb_truncate_overlib text=$task->get('name')|escape|default:"&nbsp;"}</span>
                </td>
              </tr>
              {elseif $lkey eq 'type'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{#required#}</td>
                <td>{$task->get('type_name')|escape}</td>
              </tr>
              {elseif $lkey eq 'status'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {capture assign='task_status_icon_path'}tasks_statuses_{$task->get('status')}{/capture}
                  <span class="material-icons nz-status-icon nz-status__{$task->get('status')}">{$theme->getIconForRecord($task_status_icon_path)}</span>
                  {if $task->get('status') eq 'planning'}{#tasks_status_planning#|escape}
                  {elseif $task->get('status') eq 'progress'}{#tasks_status_progress#|escape}
                  {elseif $task->get('status') eq 'finished'}{#tasks_status_finished#|escape}{/if}
                  {if $task->checkPermissions('setstatus') && $layout.edit}
                    <a href="javascript:void(0)" onclick="changeStatus({$task->get('id')}, 'tasks')">{#tasks_setstatus#|escape}</a>
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'substatus'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$task->get('substatus_name')|default:" "}
                </td>
              </tr>
              {elseif $lkey eq 'customer'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{#required#}</td>
                <td>
                  <a href="{$customer_view_link}" title="{#view#|escape}: {$task->get('customer_name')|escape}">{$task->get('customer_name')|escape|default:"&nbsp;"}</a>
                  {if $task->get('branch') && $customer_branch}
                    <span class="labelbox">{help label_content=$task->getBranchLabels('tasks_branch')|escape}</span>
                    <span{if !$customer_branch->isActivated()} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$task->get('branch_name')|escape}</span>
                  {/if}
                  {if $task->get('contact_person') && $contact_person}
                    <span class="labelbox">{help label_content=#tasks_contact_person#|escape}</span>
                    <span{if !$contact_person->isActivated()} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{$task->get('contact_person_name')|escape}</span>
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'trademark'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>
                  {if $task->get('trademark')}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$task->get('trademark')}" title="{#view#|escape}: {$task->get('trademark_name')|escape}">{$task->get('trademark_name')|escape|default:"&nbsp;"}</a>
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'project'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>
                  {if $task->get('project')}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$task->get('project')}" title="{#view#|escape}: {$task->get('project_name')|escape}">{$task->get('project_name')|escape|default:"&nbsp;"}</a>{if $task->get('phase')} <span class="labelbox">{help label_content=#tasks_phase#}</span> {$task->get('phase_name')|escape}{/if}
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'planned_start_date'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{#required#}</td>
                <td>{$task->get('planned_start_date')|escape|date_format:#date_mid#}</td>
              </tr>
              {elseif $lkey eq 'planned_finish_date'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{#required#}</td>
                <td>
                  {capture assign='task_expired'}
                    {if $task->get('status') != 'finished' && $task->get('planned_finish_date') && $task->get('planned_finish_date')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
                      {#tasks_expired_legend#}: <strong>{$task->get('planned_finish_date')|date_format:#date_mid#}</strong>!
                    {/if}
                  {/capture}
                  {if $task->get('status') != 'finished' && (($task->get('planned_finish_date') && $task->get('planned_finish_date')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
                    <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" class="t_info_image" alt="{#expired#|escape}" {popup text=$task_expired|escape caption=#tasks_expired#|escape} />
                  {/if}
                  {$task->get('planned_finish_date')|escape|date_format:#date_mid#}
                </td>
              </tr>
              {elseif $lkey eq 'planned_time'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>{$task->get('planned_time')|default:0|escape} {#minutes#}</td>
              </tr>
              {elseif $lkey eq 'timesheet_time'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$task->get('timesheet_time_formatted')|escape}
                  {if $task->get('timesheet_time') gt 0}
                    ({#total#}: {$task->get('timesheet_time')} {#minutes#})
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'severity'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                {capture assign='severity_name'}tasks_{$task->get('severity')}{/capture}
                <td class="{$task->get('severity')|escape}">{$smarty.config.$severity_name|escape|default:"&nbsp;"}</td>
              </tr>
              {elseif $lkey eq 'progress'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>{$task->get('progress')|escape} %</td>
              </tr>
              {elseif $lkey eq 'equipment'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>{$task->get('equipment')|escape}</td>
              </tr>
              {elseif $lkey eq 'task_field'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>{$task->get('task_field')|escape}</td>
              </tr>
              {elseif $lkey eq 'source'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>
                  {if $task->get('source')}
                    {capture assign='source_lang_var'}tasks_{$task->get('source')}{/capture}
                    {$smarty.config.$source_lang_var}
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'description'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>
                  {$task->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                </td>
              </tr>
              {elseif $lkey eq 'notes'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>
                  {$task->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                </td>
              </tr>
              {elseif $lkey eq 'department'}
              <tr id="task_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="labelbox">{help label_content=$layout.name text_content=$layout.description}</td>
                <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                <td>
                {if $department}
                  {if !$department->isActivated()}
                    <span class="inactive_option" title="{#inactive_option#}"> *{$task->get('department_name')|escape}</span>
                  {else}
                    {$task->get('department_name')|escape}
                  {/if}
                {/if}
                </td>
              </tr>
              {/if}

              {/if}
            {/foreach}
              {if $task->get('buttons')}
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="3">
                    {strip}
                      {foreach from=$task->get('buttons') item='button'}
                        {include file="`$theme->templatesDir`input_button.html"
                                label=$button.label
                                standalone=true
                                name=$button.name
                                source=$button.source
                                disabled=$button.disabled
                                hidden=$button.hidden
                                width=$button.width
                                height=$button.height}
                      {/foreach}
                    {/strip}
                  </td>
                </tr>
              {/if}
            </table>
          </td>
        </tr>
      </table>
      {include file="`$theme->templatesDir`help_box.html"}
      {include file="`$theme->templatesDir`system_settings_box.html" object=$task}
    </div>
  </div>

  {if isset($side_panels)}
    {include file="`$theme->templatesDir`_side_panel.html" side_panels=$side_panels}
  {/if}
</div>
