<?php
require_once 'tasks.timesheetsactivities.model.php';

/**
 * Tasks_TimesheetsActivities model factory class
 */
class Tasks_TimesheetsActivities extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Tasks_TimesheetsActivity';

    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @param array $sql - components of SQL query
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = 'ORDER BY ' . implode(', ', $filters['sort']);
        } else {
            $sort = 'ORDER BY tta.id ASC';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT(tta.id)';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_TASKS_TIMESHEETS_ACTIVITIES . ' AS tta';

        //where clause
        $sql['where'] = $where . "\n";

        //group clause
        $sql['group'] = 'GROUP BY tta.id' . "\n";

        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search ids
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {
        $sql = array('select' => '',
                     'from' => '',
                     'where' => '',
                     'group' => '',
                     'order' => '',
                     'limit' => '');

        $ids = self::getIds($registry, $filters, $sql);

        // build models with just the ids
        $records = array();
        foreach ($ids as $id) {
            $records[] = array('id' => $id);
        }

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(tta.id) AS total';
                $sql['limit'] = '';
                $sql['order'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @param array $model_types - not used
     * @return string $where - the prepared where clause
     */
    public static function constructWhere(&$registry, $filters = array(), &$model_types = array()) {

        $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {
                //search in all fields
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            if (!empty($registry['currentUser'])) {
                $current_user_id = $registry['currentUser']->get('id');
            } else {
                $current_user_id = '';
            }
            foreach ($filters['where'] as $filter) {
                if(preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if(!preg_match('/(AND|OR)\s*$/', $filter)) {
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Builds a model object
     */
    public static function buildModelIndex(&$registry, $index) {
        $model = self::buildFromRequestIndex($registry, self::$modelName, $index);

        return $model;
    }

    /**
     * Save timesheets activities models
     *
     * @param object $registry - the main registry
     * @param array $models - timesheets activitiy models
     */
    public static function multiSave(&$registry, $models) {

        $db = $registry['db'];

        $db->StartTrans();

        $insert = array();
        $max_id = 0;
        foreach ($models as $model) {
            if (!$model->get('names')) {
                continue;
            }
            if ($model->get('id') === false || $model->get('id') === '') {
                if (!$max_id) {
                    $query = 'SELECT id FROM ' . DB_TABLE_TASKS_TIMESHEETS_ACTIVITIES . "\n" .
                             'ORDER BY id DESC LIMIT 1' . "\n";
                    $max_id = @intval($db->GetOne($query)) + 1;
                } else {
                    $max_id++;
                }
                $id = $max_id;
            } else {
                $id = $model->get('id');
            }

            foreach ($model->get('names') as $lang => $name) {
                $insert[] = sprintf('(%d, \'%s\', %d, \'%s\')',
                                    $id, General::slashesEscape($name), $model->get('event_type'), $lang);
            }
        }
        if ($insert) {
            // SQL for insert/update
            $query = 'INSERT INTO ' . DB_TABLE_TASKS_TIMESHEETS_ACTIVITIES . ' (id, name, event_type, lang)  VALUES' . "\n" .
                     implode(", \n", $insert) . "\n" .
                     'ON DUPLICATE KEY UPDATE name=VALUES(name), event_type=VALUES(event_type)';
            $db->Execute($query);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }
}

?>
