<?php

//require_once 'tasks.validator.php';
//require_once 'tasks.dropdown.php';

/**
 * Tasks model class
 */
Class Task extends Model {
    public $modelName = 'Task';

    public $counter;

    //use status for changing permissions
    public $checkPermissionsByStatus = true;

    //use active state for changing permissions
    public $checkPermissionsByActive = true;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
        if ($this->get('id') && ($registry->get('getAssignmentsResponsible') || $registry->get('getAssignments'))) {
            $this->getAssignments('responsible');
        }
        if ($this->get('id') && ($registry->get('getAssignmentsDecision') || $registry->get('getAssignments'))) {
            $this->getAssignments('decision');
        }
        if ($this->get('id') && ($registry->get('getAssignmentsObserver') || $registry->get('getAssignments'))) {
            $this->getAssignments('observer');
        }
        if ($this->get('id') && ($registry->get('getAssignmentsOwner') || $registry->get('getAssignments'))) {
            $this->getAssignments();
        }

        if ($this->get('project')) {
            if ($this->get('phase')) {
                $this->set('project_referer', $this->get('project') . '_' . $this->get('phase'), true);
            } else {
                $this->set('project_referer', $this->get('project'), true);
            }
        }

        //get execute time
        if ($this->get('start_date')) {
            if ($this->get('finish_date')) {
                $exec_time = intval((strtotime($this->get('finish_date')) - strtotime($this->get('start_date'))) / 60);
            } else {
                $exec_time = intval((time() - strtotime($this->get('start_date'))) / 60);
            }
            $this->set('exec_time', $exec_time, true);
        } else {
            $this->set('exec_time', 0, true);
        }
        $this->set('exec_time_formatted', General::minutes2Human($this->registry, $this->get('exec_time'), true), true);

        $this->set('age', intval((time() - strtotime($this->get('added'))) / 60), true);
        $this->set('age_formatted', General::minutes2Human($this->registry, $this->get('age'), true), true);

        if ($this->get('status')) {
            $this->set('status_name', $this->i18n('tasks_status_' . $this->get('status')), true);
        }

        $this->set('planned_time_formatted', General::minutes2Human($this->registry, $this->get('planned_time'), false), true);

        if ($this->get('severity')) {
            $this->set('severity_name', $this->i18n("tasks_{$this->get('severity')}"), true);
        }

        //get the task timesheet time
        $this->getTimesheetTime();

        //get the task timesheet billing time
        $this->getTimesheetBillingTime();
    }

    /**
     * Checks permissions for certain action
     *
     * @param array $action - action name
     * @param array $module_check - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     */
    public function checkPermissions($action, $module_check = 'tasks', $force = false) {

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        $rights = $this->setPermissions(array(), $force, $module_check);
        if ($this->get('type_rights')) {
            $type_rights = $this->get('type_rights');
        } elseif ($this->get('type')) {
            $sanitize_after = false;
            if (empty($this->registry)) {
                $this->unsanitize();
                $sanitize_after = true;
            }
            $user_permissions = $this->registry['currentUser']->getRights();
            $type_rights = $this->setPermissions(@array_keys($user_permissions[$module_check.$this->get('type')]), true, $module_check.$this->get('type'));
            if ($sanitize_after) {
                $this->sanitize();
            }
            $this->set('type_rights', $type_rights, true);
        }

        if (!isset($rights[$action]) && is_array($rights)) {
            //the action is not defined within the rights array
            $action_defs = array_keys($rights);
            $action_defs[] = $action;

            //try to get permission definition for this action
            $rights = $this->setPermissions($action_defs, true, $module_check);
        }

        if ($action == 'delete' && ($this->get('type') == PH_TASK_SYSTEM_TYPE)) {
            return false;
        }

        $permissions = true;
        //set permissions depending model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'planning') {
                //planning status
                switch ($action) {
                    //forbidden actions
                    case 'timesheets':
                    case 'addtimesheet':
                    case 'viewtimesheets':
                        $permissions = false;
                        break;
                    case 'edit_allocate':
                        if (!isset($this->registry)) {
                            $registry = $GLOBALS['registry'];
                        } else {
                            $registry = $this->registry;
                        }
                        if ($this->get('type') == PH_TASK_SYSTEM_TYPE ||
                        !preg_match('#,' . $registry['currentUser']->get('id') . ',#', $this->get('user_permissions'))) {
                            $permissions = false;
                            break;
                        }
                    //allowed actions
                    case 'view':
                    case 'edit':
                    case 'assign':
                    case 'relatives':
                    case 'dependencies':
                    case 'comments':
                    case 'attachments':
                    case 'translate':
                    default:
                        if (isset($rights[$action])) {
                            if (isset($type_rights[$action])) {
                                $permissions = $type_rights[$action];
                            } else {
                                $permissions = $rights[$action];
                            }
                        } else {
                            $permissions = true;
                        }
                }
            } elseif ($this->get('status') == 'finished') {
                //finished status
                switch ($action) {
                    //forbidden actions
                    case 'edit':
                    case 'multiedit':
                    case 'dependencies':
                    case 'addtimesheet':
                    case 'edit_allocate':
                        $permissions = false;
                        break;
                    //allowed actions
                    case 'view':
                    case 'assign':
                    case 'relatives':
                    case 'comments':
                    case 'attachments':
                    case 'timesheets':
                    default:
                        if (isset($rights[$action])) {
                            if (isset($type_rights[$action])) {
                                $permissions = $type_rights[$action];
                            } else {
                                $permissions = $rights[$action];
                            }
                        } else {
                            $permissions = true;
                        }
                }
            } else {
                //progress status
                switch ($action) {
                //specific actions
                case 'addtimesheet':
                    //adding timesheets is ONLY allowed in status progress
                    if (!empty($type_rights[$action])) {
                        if (!isset($this->registry)) {
                            $registry = $GLOBALS['registry'];
                        } else {
                            $registry = $this->registry;
                        }
                        $current_user_id = $registry['currentUser']->get('id');

                        if ($this->get('type') == PH_TASK_SYSTEM_TYPE) {
                            if (!isset($user_permissions)) {
                                $user_permissions = $registry['currentUser']->getRights();
                            }

                            //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                            if (isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets']) &&
                                $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets'] == 'none') {
                                return false;
                            }

                            //check if the permission for adding timesheets for TASK type TIMESHEET is allowed
                            if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                                $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                                return false;
                            }

                            //check for system tasks
                            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
                            $current_user_id = $registry['currentUser']->get('id');
                            $parent_info = Tasks::getRelateToSystemTask($registry, $this->get('id'));

                            if (! empty($parent_info)) {
                                if ($parent_info['origin'] == 'document') {

                                    //the task is SYSTEM and is created for DOCUMENT
                                    require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                                    $document_filters = array ('where' => array('d.id = ' . $parent_info['link_to']),
                                                               'archive' => 'all',
                                                               'sanitize'  => true);
                                    $parent_document = Documents::searchOne($registry, $document_filters);

                                    //check if the document is assigned to the current user
                                    //and the document is NOT in closed status
                                    //conditions:
                                    //1. the document is NOT in closed status and is not archived
                                    //2. the document is assigned to the current user in any assignment type
                                    if (($parent_document->get('status') == 'closed') || $parent_document->get('archived_by')) {
                                        $permissions = false;
                                    } elseif (!array_key_exists($current_user_id, $parent_document->get('assignments_owner')) &&
                                              !array_key_exists($current_user_id, $parent_document->get('assignments_responsible')) &&
                                              !array_key_exists($current_user_id, $parent_document->get('assignments_decision')) &&
                                              !array_key_exists($current_user_id, $parent_document->get('assignments_observer'))) {
                                        $permissions = false;
                                    }
                                } else if ($parent_info['origin'] == 'project') {

                                    //the task is SYSTEM and is created for PROJECT
                                    require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                                    $project_filters = array ('where' => array('p.id = ' . $parent_info['link_to']),
                                                              'sanitize'  => true);
                                    $parent_project = Projects::searchOne($registry, $project_filters);

                                    //check if the project is assigned to the current user or the current user is manager of the project
                                    //and the project is NOT in finished status
                                    //conditions:
                                    //1. the project is NOT in finished status
                                    //2. the project has assignments and is assigned to the current user OR the current user is manager of the project
                                    if ($parent_project->get('status') == 'finished') {
                                        $permissions = false;
                                    } elseif (!($parent_project->get('users_assignments') && array_key_exists($current_user_id, $parent_project->get('users_assignments'))) &&
                                              $parent_project->get('manager') != $current_user_id) {
                                        $permissions = false;
                                    }
                                }
                            }
                        } elseif (!array_key_exists($current_user_id, $this->get('assignments_owner')) &&
                                  !array_key_exists($current_user_id, $this->get('assignments_responsible')) &&
                                  !array_key_exists($current_user_id, $this->get('assignments_decision')) &&
                                  !array_key_exists($current_user_id, $this->get('assignments_observer'))) {
                            //check for non-system tasks
                            //conditions:
                            //1. the task is assigned to the user in any assignment type
                            $permissions = false;
                        }
                    } else {
                        $permissions = false;
                    }
                    break;
                case 'edit_allocate':
                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }
                    if ($this->get('type') == PH_TASK_SYSTEM_TYPE ||
                    !preg_match('#,' . $registry['currentUser']->get('id') . ',#', $this->get('user_permissions'))) {
                        $permissions = false;
                        break;
                    }
                default:
                    if (isset($rights[$action])) {
                        if (isset($type_rights[$action])) {
                            $permissions = $type_rights[$action];
                        } else {
                            $permissions = $rights[$action];
                        }
                    } else {
                        $permissions = true;
                    }
                }
            }
        } else {
            if (isset($rights[$action])) {
                if (isset($type_rights[$action])) {
                    $permissions = $type_rights[$action];
                } else {
                    $permissions = $rights[$action];
                }
            } else {
                $permissions = true;
            }
        }

        //set permissions depending of active state
        if ($this->checkPermissionsByActive && $this->get('id')) {
            if (intval($this->get('active')) == 0) {
            //not active state
                switch ($action) {
                    case 'setstatus':
                    case 'assign':
                    case 'comments':
                    case 'attachments':
                    case 'relatives':
                    case 'dependencies':
                    case 'addtimesheet':
                    case 'edit_allocate':
                        $permissions = false;
                        break;
                    default:
                        //leave the permission set in the previous checks
                        break;
                }
            }
        }

        //no restrictions are specified, allow action
        return $permissions;
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - action with the model
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        parent::validate($action);
        if ((!$this->get('id') || $this->isDefined('name')) && !$this->get('name')) {
            $this->raiseError('error_no_name', 'name', null, array($this->getLayoutName('name', false)));
        }

        if (!$this->get('id') && !$this->get('type')) {
            $this->raiseError('error_no_type', 'type');
        }

        if ($this->isDefined('customer') && !$this->get('customer')) {
            $this->raiseError('error_no_customer', 'customer', null, array($this->getLayoutName('customer')));
        }

        if (!$this->hasValidTrademark()) {
            $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
        }

        if ($this->isDefined('planned_start_date')) {
            if (!Validator::validDate($this->get('planned_start_date'))) {
                $this->raiseError('error_invalid_planned_start_date', 'planned_start_date', null, array($this->getLayoutName('planned_start_date', false)));
            }
            if ($this->isDefined('planned_finish_date') && !$this->get('planned_finish_date')) {
                $this->raiseError('error_invalid_planned_finish_date', 'planned_finish_date', null, array($this->getLayoutName('planned_finish_date', false)));
            } elseif ($this->isDefined('planned_finish_date') && ($this->get('planned_finish_date') < $this->get('planned_start_date'))) {
                $this->raiseError('error_invalid_planned_start_finish_date', 'planned_start_date, planned_finish_date', null, array($this->getLayoutName('planned_start_date', false), $this->getLayoutName('planned_finish_date', false)));
            }
        } elseif ($this->isDefined('planned_finish_date')) {
            $this->raiseError('error_invalid_planned_start_date', 'planned_start_date', null, array($this->getLayoutName('planned_start_date', false)));
            if (!Validator::validDate($this->get('planned_finish_date'))) {
                $this->raiseError('error_invalid_planned_finish_date', 'planned_finish_date', null, array($this->getLayoutName('planned_finish_date', false)));
            }
        }
        // validate against planned time events
        if ($this->get('id') && ($this->get('planned_start_date') || $this->get('planned_finish_date'))) {
            $events = $this->getPlannedTime();
            if ($events) {
                usort($events, function ($a, $b) {
                    return $a['event_start_date'] . $a['event_start_time'] . $a['event_end_time'] >
                           $b['event_start_date'] . $b['event_start_time'] . $b['event_end_time'];
                });

                $min_date = reset($events);
                $min_date_format = $this->i18n($min_date['event_start_time'] ? 'date_mid' : 'date_short');
                $min_date = $min_date['event_start_date'] . ' ' . ($min_date['event_start_time'] ?: '23:59') . ':00';
                if ($this->get('planned_start_date') && $this->get('planned_start_date') > $min_date) {
                    $this->raiseError('error_invalid_date_plannedtime', 'planned_start_date', null,
                                      array($this->getLayoutName('planned_start_date', false), $this->i18n('before'),
                                            General::strftime($min_date_format, strtotime($min_date))));
                }
                $max_date = end($events);
                $max_date_format = $this->i18n($max_date['event_end_time'] ? 'date_mid' : 'date_short');
                $max_date = $max_date['event_start_date'] . ' ' . ($max_date['event_end_time'] ?: '00:00') . ':00';
                if ($this->get('planned_finish_date') && $this->get('planned_finish_date') < $max_date) {
                    $this->raiseError('error_invalid_date_plannedtime', 'planned_finish_date', null,
                                      array($this->getLayoutName('planned_finish_date', false), $this->i18n('after'),
                                            General::strftime($max_date_format, strtotime($max_date))));
                }
            }
            unset($events);
        }

        if ($this->get('id') && $this->get('planned_time') && $this->get('planned_time') < $this->getPlannedDuration()) {
            $this->raiseError('error_invalid_duration_plannedtime', 'planned_time', '',
                              array($this->getLayoutName('planned_time'), $this->get('planned_duration')));
        }

        if (! $this->getCounter()) {
            $this->raiseError('error_no_counter_for_this_task_type', 'type');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['type']     = sprintf("type=%d", $this->get('type'));
        if ($set['active'] == 'active=1') {
            $set['full_num'] = sprintf("full_num='%s'", $this->getTaskFullNum());
        } else {
            $set['full_num'] = sprintf("full_num='%s'", '');
        }

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_TASKS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new task base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N('add');

        //increment the counter
        if ($set['active'] == 'active=1') {
            $this->counter->increment();
        }

        //if create from event update event relatives
        if ($this->get('event_id')) {
            $origin = 'task';
            $link_type = 'child';
            $query = 'INSERT IGNORE INTO ' . DB_TABLE_EVENTS_RELATIVES . "\n" .
                      'SET parent_id=' . $this->get('event_id') . "\n" .
                      ', link_to=' . $this->get('id') . "\n" .
                      ', origin="' . $origin . '"' . "\n" .
                      ', link_type="' . $link_type . '"' . "\n" .
                      ', added=now()' . "\n";
            $db->Execute($query);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        // If the transaction has failed
        if ($dbTransError) {
            // Remove the id
            $this->set('id', '', true);
        }

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        if ((!$this->get('full_num')) && (isset($set['active'])) && ($set['active'] == 'active=1')) {
            $this->set('added', '', true);
            $set['full_num'] = sprintf("full_num='%s'", $this->getTaskFullNum());
            $this->counter->increment();
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_TASKS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Change status/substatus
     *
     * @return bool - result of the operation
     */
    public function setStatus() {
        $flag_error = false;
        $flag_error_substatus = false;

        // Get the database object
        $db = $this->registry['db'];

        // Start a transaction
        $db->StartTrans();

        $permission_unlock = $this->checkPermissions('setstatus_unlock');

        if (!$this->get('status')) {
            $flag_error = true;
        } else {
            if ($this->get('id')) {
                $id_task = $this->get('id');
            }

            // gets the current status from the database
            $current_status = $this->getTaskStatus();
            $status_info = explode ('_', $this->get('status'));
            $status_name = $status_info[0];
            if ($current_status == 'finished' && $status_name == 'progress') {
                if (! $permission_unlock) {
                    $flag_error = true;
                }
            } else if ($current_status == 'finished' && $status_name == 'planning') {
                $flag_error = true;
            } else if ($current_status == 'progress' && $status_name == 'planning') {
                $flag_error = true;
            } elseif ($current_status != 'finished' && $status_name == 'finished' &&
            $this->getPlannedTime(array('status' => array('planning', 'progress')))) {
                // check for non-finished planned time events
                if (!$this->registry['request']->get('multistatusSelect')) {
                    $this->raiseError('error_invalid_status_plannedtime', 'status');
                }

                // Complete the transaction before exit the function
                $db->CompleteTrans();

                return false;
            }

            //takes the status and the substatus for the task
            @ $status_properties = explode('_', $this->get('status'));
            $new_status = $status_properties[0];

            if ($this->get('substatus')) {
                $substatus_properties = explode('_', $this->get('substatus'));
                if ($substatus_properties[0] != $new_status) {
                    $flag_error_substatus = true;
                } else {
                    $new_substatus = $substatus_properties[1];
                }
            } elseif (isset($status_properties[1])) {
                $new_substatus = $status_properties[1];
            }
        }

        //do not display error messages when in multistatus
        $multistatus = $this->registry['request']->get('multistatusSelect');
        if ($flag_error || $flag_error_substatus) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            if (isset($current_substatus)) {
                $this->set('substatus', $current_substatus, true);
            }
            if ($flag_error && !$multistatus) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }
            if ($flag_error_substatus && !$multistatus) {
                $this->raiseError('error_invalid_substatus_change', 'substatus', -3);
            }

            // Complete the transaction before exit the function
            $db->CompleteTrans();

            return false;
        }

        //check dependencies
        if ($new_status == 'progress' || $new_status == 'finished') {
            $this->getDependencies();
            $tasks_dependencies = $this->get('tasks_dependencies');
            if (count($tasks_dependencies)) {
                $ids = array_keys($tasks_dependencies);
                foreach ($ids as $id) {
                    $filters = array('where' => array('t.id = ' . $id),
                                     'model_lang' => $this->get('model_lang'));
                    $task_object = Tasks::searchOne($this->registry, $filters);
                    if ($task_object) {
                        $blocking_task_view = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                                    $_SERVER['PHP_SELF'],
                                    $this->registry['module_param'], $this->module,
                                    $this->registry['action_param'], 'view',
                                    'view', $task_object->get('id'));
                        $placeholders = array('blocking_task' => $task_object->get('name'),
                                        'blocking_task_view' => $blocking_task_view);
                        switch ($tasks_dependencies[$id]['origin']) {
                        case 'F2S':
                            if (!$task_object->get('finish_date')) {
                                if (!$multistatus) {
                                    $this->raiseError('error_invalid_finish_dependencies', 'status', 0, $placeholders);
                                }
                                return false;
                            }
                            break;
                        case 'F2F':
                            if (!$task_object->get('finish_date') && $new_status == 'finished') {
                                if (!$multistatus) {
                                    $this->raiseError('error_invalid_finish_dependencies', 'status', 0, $placeholders);
                                }
                                return false;
                            }
                            break;
                        case 'S2S':
                            if (!$task_object->get('start_date')) {
                                if (!$multistatus) {
                                    $this->raiseError('error_invalid_start_dependencies', 'status', 0, $placeholders);
                                }
                                return false;
                            }
                            break;
                        case 'S2F':
                            if (!$task_object->get('start_date') && $new_status == 'finished') {
                                if (!$multistatus) {
                                    $this->raiseError('error_invalid_start_dependencies', 'status', 0, $placeholders);
                                }
                                return false;
                            }
                            break;
                        }
                    }
                }
            }
        }

        $set['status'] = sprintf("`status`='%s'", $new_status);
        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
        if ($new_status == 'progress' && !$this->get('started_by')) {
            $set['started_by'] = sprintf("started_by=%d", $this->registry['currentUser']->get('id'));
            $set['start_date'] = "start_date=now()";
        } elseif ($new_status == 'finished' && !$this->get('finish_by')) {
            $set['finish_by'] = sprintf("finish_by=%d", $this->registry['currentUser']->get('id'));
            $set['finish_date'] = "finish_date=now()";
        }
        if (isset($new_substatus)) {
            $set['substatus'] = sprintf("`substatus`=%d", $new_substatus);
        } else {
            $set['substatus'] = "`substatus`=0";
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_TASKS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        // The result is true if the transaction hasn't failed
        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();
        $result = !$dbTransError;

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        return $result;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("customer=%d", $this->get('customer'));
        }
        if ($this->isDefined('branch')) {
            $set['branch'] = sprintf("branch=%d", $this->get('branch'));
        }
        if ($this->isDefined('contact_person')) {
            $set['contact_person'] = sprintf("contact_person=%d", $this->get('contact_person'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark']      = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("project=%d", $this->get('project'));
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('planned_start_date')) {
            $set['planned_start_date'] = sprintf("planned_start_date='%s'", $this->get('planned_start_date'));
        }
        if ($this->isDefined('planned_finish_date')) {
            $set['planned_finish_date'] = sprintf("planned_finish_date='%s'", $this->get('planned_finish_date'));
        }
        if ($this->isDefined('severity')) {
            $set['severity'] = sprintf("severity='%s'", $this->get('severity'));
        }
        if ($this->isDefined('equipment')) {
            $set['equipment'] = sprintf("equipment='%s'", $this->get('equipment'));
        }
        if ($this->isDefined('task_field')) {
            $set['task_field'] = sprintf("task_field='%s'", $this->get('task_field'));
        }
        if ($this->isDefined('source')) {
            $set['source'] = sprintf("source='%s'", $this->get('source'));
        }
        if ($this->isDefined('phase')) {
            $set['phase'] = sprintf("phase=%d", $this->get('phase'));
        }
        if ($this->isDefined('progress')) {
            $set['progress'] = sprintf("progress=%d", $this->get('progress'));
        }
        if ($this->isDefined('status')) {
            $set['status'] = sprintf("status='%s'", $this->get('status'));
        }
        if ($this->isDefined('start_date')) {
            $set['start_date'] = sprintf("start_date='%s'", $this->get('start_date'));
        }
        if ($this->isDefined('started_by')) {
            $set['started_by'] = sprintf("started_by=%d", $this->get('started_by'));
        }
        if ($this->isDefined('finish_date')) {
            $set['finish_date'] = sprintf("finish_date='%s'", $this->get('finish_date'));
        }
        if ($this->isDefined('finish_by')) {
            $set['finish_by'] = sprintf("finish_by=%d", $this->get('finish_by'));
        }
        /*if ($this->isDefined('project_referer')) {
            $new_relatives = $this->get('project_referer');
            if ($new_relatives) {
                $tmp_array = explode('_',$new_relatives);
                $new_project = $tmp_array[0];
                if (isset($tmp_array[1])) {
                    $new_phase = $tmp_array[1];
                } else {
                    $new_phase = 0;
                }
            } else {
                $new_project = 0;
                $new_phase = 0;
            }
            $set['project'] = sprintf("project=%d", $new_project);
            $set['phase'] = sprintf("phase=%d", $new_phase);
        }*/

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }

        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        $set['modified']        = sprintf("modified=now()");
        $set['modified_by']     = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('planned_time')) {
            $set['planned_time'] = sprintf("planned_time='%d'", $this->get('planned_time'));
        }

        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N($action = '') {
        $db = $this->registry['db'];
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';

        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $name = $this->get('name');
            $update['name'] = sprintf("name='%s'", $name);
        }
        if ($this->isDefined('description')) {
            $description = $this->get('description');
            $update['description']  = sprintf("description='%s'", $description);
        }
        if ($this->isDefined('notes')) {
            $notes = $this->get('notes');
            $update['notes']  = sprintf("notes='%s'", $notes);
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_TASKS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing task i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Update dependencies table of the model
     *
     * @return bool
     */
    public function updateDependencies() {
        $db = $this->registry['db'];
        $db->StartTrans();

        if (!is_array($this->get('tasks_dependencies'))) {
            $this->set('tasks_dependencies',array(),true);
        }
        $query = 'SELECT link_to, origin FROM ' . DB_TABLE_TASKS_DEPENDENCIES .
                    ' WHERE parent_id=' . $this->get('id');
        $old_dependencies = $db->GetAssoc($query);
        $new_relatives = array_unique($this->get('tasks_dependencies'));
        $new_origin = $this->get('tasks_dependencies_origin');
        $new_dependencies = array();
        $del_dependencies = array();
        foreach ($new_relatives as $ref) {
            if (!isset($old_dependencies[$ref]) ||
                (isset($old_dependencies[$ref]) && $old_dependencies[$ref] != $new_origin[$ref])) {
                $new_dependencies[] = $ref;
                if (isset($old_dependencies[$ref])) {
                    $del_dependencies[] = $ref;
                }
            }
        }
        foreach ($old_dependencies as $ref => $origin) {
            if (!in_array($ref, $new_relatives)) {
                $del_dependencies[] = $ref;
            }
        }
        if (count($del_dependencies)) {
            $query3 = 'DELETE FROM ' . DB_TABLE_TASKS_DEPENDENCIES .
                      ' WHERE parent_id=' . $this->get('id') .
                      ' AND link_to in (' . implode(',', $del_dependencies) . ')';
            $db->Execute($query3);
        }

        $tmp = array();
        if (count($new_dependencies)) {
            foreach ($new_dependencies as $ref) {
                $refIsAChild = $this->hasChildWithId($ref);
                if ($refIsAChild) {
                    $this->registry['messages']->setWarning($this->i18n('warning_tasks_relative_child'), '',-1);
                    $this->registry['messages']->insertInSession($this->registry);
                }

                if (!$refIsAChild && $this->get('id') != $ref) {
                    $tmp[] = '(' . $this->get('id') . ', ' . $ref . ', "' . $new_origin[$ref] . '", now())';
                }
            }
            $query4 = 'INSERT IGNORE INTO ' . DB_TABLE_TASKS_DEPENDENCIES . ' (parent_id, link_to,origin, added)'
                . ' VALUES ' . implode(',', $tmp);
        }

        if (count($tmp)) {
            $db->Execute($query4);
        }

        $dbTransError = $db->HasFailedTrans();

        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update relatives table of the model
     *
     * @return bool - result of the operation
     */
    public function updateAllRelatives() {
        $this->updateRelatives();
        $this->updateRelativesDocuments();

        return true;
    }

    /**
     * Update relatives table of the model
     *
     * @return bool - result of the operation
     */
    public function updateRelatives() {
        $db = $this->registry['db'];

        if (!is_array($this->get('tasks_referers'))) {
            $this->set('tasks_referers', array(), true);
        }
        $query = 'SELECT link_to FROM ' . DB_TABLE_TASKS_RELATIVES .
                    ' WHERE parent_id=' . $this->get('id') .
                    ' AND origin="task" AND link_type="child"';
        $old_relatives = array_unique($db->GetCol($query));
        $new_relatives = array_unique($this->get('tasks_referers'));
        $del_relatives = array_diff($old_relatives, $new_relatives);
        if (count($del_relatives)) {
            $query3 = 'DELETE FROM ' . DB_TABLE_TASKS_RELATIVES .
                      ' WHERE parent_id=' . $this->get('id') .
                      ' AND link_to in (' . implode(',', $del_relatives) . ')' .
                      ' AND origin="task" AND link_type="child"';
            $db->Execute($query3);
        }

        $new_relatives = array_diff($new_relatives, $old_relatives);
        $tmp = array();
        if (count($new_relatives)) {
            foreach ($new_relatives as $ref) {
                $tree = array();
                $this->getChildrenTree($this->get('id'), 0, $tree);
                $insert = true;
                //check if ref is child for this model
                foreach ($tree as $rec) {
                    if ($rec['id'] == $ref) {
                        $insert = false;
                        $this->registry['messages']->setWarning($this->i18n('warning_tasks_relative_child'), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        continue;
                    }
                }
                if ($insert && $this->get('id') != $ref) {
                    $tmp[] = '(' . $this->get('id') . ', ' . $ref . ', "task", now())';
                }
            }
            $query4 = 'INSERT IGNORE INTO ' . DB_TABLE_TASKS_RELATIVES . ' (parent_id, link_to, origin, added) VALUES ' . "\n" .
                      implode(', ', $tmp);
        }

        if (count($tmp)) {
            $db->Execute($query4);
            return !$db->HasFailedTrans();
        } else {
            return true;
        }
    }

    /**
     * Update the relations between the current task and the related documents (the referers)
     *
     * @return bool - true/false respectively on success/failure
     */
    public function updateRelativesDocuments() {
        // If the referers is not an array
        if (!is_array($this->get('referers'))) {
            // Set it as an empty array
            $this->set('referers', array(), true);
        }

        // Prepare the database object
        $db = $this->registry['db'];

        // Get the current list of related documents
        $query = 'SELECT `link_to`' . "\n" .
                 '  FROM `' . DB_TABLE_TASKS_RELATIVES . '`' . "\n" .
                 '  WHERE `parent_id` = \'' . $this->get('id') . '\'' . "\n" .
                 '    AND `origin`    = \'document\'' . "\n" .
                 '    AND `link_type` = \'child\'';
        $old_relatives = array_unique($db->GetCol($query));

        // Get the new list of related documents (the ones that the user have checked into the form)
        $new_relatives = array_unique($this->get('referers'));

        // Get the list of related documents, which relations should be removed (i.e. this are the unchecked documents from the form)
        $del_relatives = array_diff($old_relatives, $new_relatives);

        // Check if there are documents, which relations should be removed
        if (count($del_relatives)) {
            // Remove the relations to this documents ($del_relatives)
            $query3 = 'DELETE FROM `' . DB_TABLE_TASKS_RELATIVES . '`' . "\n" .
                      '  WHERE `parent_id` = \'' . $this->get('id') . '\'' . "\n" .
                      '    AND `link_to`   IN (\'' . implode('\', \'', $del_relatives) . '\')' .
                      '    AND `origin`    = \'document\'' . "\n" .
                      '    AND `link_type` = \'child\'';
            $db->Execute($query3);
        }

        // Get the list of the brand new documents (i.e. the new ones that we want to relate to the current task)
        $new_relatives = array_diff($new_relatives, $old_relatives);

        // If there are any brand new documents, then add relations for them
        if (count($new_relatives)) {
            // Prepare an INSERT query
            $insert = array();
            foreach ($new_relatives as $ref) {
                $insert[] = '(\'' . $this->get('id') . '\', \'' . $ref . '\', \'document\', \'child\', NOW())';
            }
            $query4 = 'INSERT IGNORE INTO `' . DB_TABLE_TASKS_RELATIVES . '` (`parent_id`, `link_to`, `origin`, `link_type`, `added`) VALUES ' . "\n" .
                      implode(', ' . "\n", $insert);

            // Execute the INSERT query
            $db->Execute($query4);

            // Check for database errors
            if ($db->ErrorMsg()) {
                return false;
            } else {
                return true;
            }
        } else {
            // Just exit
            return true;
        }
    }

    /**
     * Update relatives table of the model
     *
     * @return bool - result of the operation
     */
    public function updateRelativesProject() {
        $db = $this->registry['db'];

        $query = 'SELECT link_to, extra FROM ' . DB_TABLE_TASKS_RELATIVES .
                    ' WHERE parent_id=' . $this->get('id') .
                    ' AND origin="project" AND link_type="child"';
        $old_relatives = $db->GetRow($query);
        if ($old_relatives) {
            $old_project = $old_relatives['link_to'];
            $old_phase = $old_relatives['extra'];
        } else {
            $old_project = 0;
            $old_phase = 0;
        }
        $new_relatives = $this->get('project_referer');
        if ($new_relatives) {
            $tmp_array = explode('_',$new_relatives);
            $new_project = $tmp_array[0];
            if (isset($tmp_array[1])) {
                $new_phase = $tmp_array[1];
            } else {
                $new_phase = 0;
            }
        } else {
            $new_project = 0;
            $new_phase = 0;
        }

        if ($old_project && ($old_project != $new_project || $old_phase != $new_phase)) {
            $query3 = 'DELETE FROM ' . DB_TABLE_TASKS_RELATIVES .
                      ' WHERE parent_id=' . $this->get('id') .
                      ' AND origin="project" AND link_type="child"';
            $db->Execute($query3);
        }

        if ($new_project && ($old_project != $new_project || $old_phase != $new_phase)) {
            $tmp[] = $this->get('id');
            $tmp[] = $new_project;
            $tmp[] = $new_phase;
            $tmp[] = '"project"';
            $tmp[] = 'now()';
            $query4 = 'INSERT IGNORE INTO ' . DB_TABLE_TASKS_RELATIVES . ' (parent_id, link_to, extra, origin, added) VALUES (' . implode(',', $tmp) . ')';
            $db->Execute($query4);
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Insert relatives table of the model clone, transform
     *
     * @return bool - result of the operation
     */
    public function insertRelatives($multi = 0, $group_index = '') {
        $db = $this->registry['db'];

        if (!empty($group_index)) {
            $multi = 0;
        }
        $query4 = 'INSERT INTO ' . DB_TABLE_TASKS_RELATIVES .
                    ' (parent_id, link_to, origin, multi_index, group_index) VALUES (' .
                    $this->get('id') . ',' . $this->get('origin_id') . ',' .
                    "'" . $this->get('clone_transform') . "'," . $multi . ", '$group_index')";

        if (!empty($query4)) {
            $db->Execute($query4);
            return !$db->HasFailedTrans();
        } else {
            return true;
        }
    }

    /**
     * Get tasks full number
     *
     * @return string
     */
    public function getTaskFullNum($force = false) {
        if (!$this->get('full_num') || $force) {

            //get the task type
            require_once 'tasks.types.factory.php';
            $filters = array('where' => array('tt.id = ' . $this->get('type'),
                                              'tt.deleted IS NOT NULL'),
                             'sanitize' => true);
            $type = Tasks_Types::searchOne($this->registry, $filters);

            //get the counter assigned to the task type
            $this->getCounter();

            //define some the counter's fomula components
            $formula = $this->counter->get('formula');
            $prefix = $this->counter->get('prefix');
            $delimiter = $this->counter->get('delimiter');
            $zeroes = $this->counter->get('leading_zeroes');
            $date_format = $this->counter->get('date_format');

            //create extender to expand the formula components
            $extender = new Extender;

            //lock the counter for update to guarantee unique next number
            $query = 'SELECT next_number FROM ' . DB_TABLE_TASKS_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
            $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

            //set task number
            $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
            $extender->add('task_num', $num);

            if ($this->counter->get('prefix_used')) {
                //add this component to the extender
                $extender->add('prefix', $prefix);
            }

            if ($this->counter->get('customer_code') && $this->get('customer')) {
                //get customer code
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $filters = array('where' => array('c.id = ' . $this->get('customer'),
                                                  'c.deleted IS NOT NULL'),
                                 'sanitize' => true);
                $customer = Customers::searchOne($this->registry, $filters);

                //add this component to the extender
                $extender->add('customer_code', $customer->get('code'));
            }

            if ($this->counter->get('project_code') && $this->get('project')) {
                //get project code
                require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                $filters = array('where' => array('p.id = ' . $this->get('project'),
                                                  'p.deleted IS NOT NULL'),
                                 'sanitize' => true);
                $project = Projects::searchOne($this->registry, $filters);

                //add this component to the extender
                $extender->add('project_code', $project->get('code'));
            }

            if ($this->counter->get('user_code')) {
                //get user code
                //add this component to the extender
                $extender->add('user_code', $this->registry['currentUser']->get('code'));
            }

            if ($this->counter->get('task_type_code')) {
                //get task type code
                //add this component to the extender
                $extender->add('task_type_code', $type->get('code'));
            }

            if ($this->counter->get('task_date')) {
                //replace the date
                $date = ($this->get('added')) ? General::strftime($date_format, strtotime($this->get('added'))) : General::strftime($date_format);

                //add this component to the extender
                $extender->add('task_date', $date);
            }

            $full_num = $extender->expand($formula);
            if ($delimiter) {
                //remove repeating delimiters
                $full_num = preg_replace('#' . preg_quote($delimiter . $delimiter) . '#', $delimiter, $full_num);
                $full_num = preg_replace('#' . preg_quote($delimiter) . '$#', '', $full_num);
                $full_num = preg_replace('#^' . preg_quote($delimiter) . '#', '', $full_num);
            }

            $this->set('num', $num, true);
            $this->set('full_num', $full_num, true);
        }

        return $this->get('full_num');
    }

    /**
     * sets number to the task
     *
     * @return int - result of the operation
     */
    public function setNumber() {
        $this->set('added', '', true);

        $set['full_num'] = sprintf("full_num='%s'", $this->getTaskFullNum());
        $this->counter->increment();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_TASKS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $result = $this->registry['db']->Execute($query1);

        return $result;
    }

    /**
     * get counter for this task
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->registry = $GLOBALS['registry'];
            }

            require_once 'tasks.types.factory.php';
            $filters = array('where' => array('tt.id = ' . $this->get('type'),
                                              'tt.deleted IS NOT NULL',
                                              'tt.system IS NOT NULL'),
                             'sanitize' => true);
            $type = Tasks_Types::searchOne($this->registry, $filters);

            require_once 'tasks.counters.factory.php';
            $filters = array('where' => array('tc.id = ' . $type->get('counter'),
                                              'tc.deleted IS NOT NULL'));
            $this->counter = Tasks_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                unset($this->registry);
            }
        }

        return $this->counter;
    }

    /**
     * Checks permitted layouts
     *
     * @param string $mode - action name
     * @param string $model - model name, not used in overwrite method
     * @return array - 'view' layouts or 'edit' layouts or array of both
     */
    public function getPermittedLayouts($mode = '', $model = '') {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        //if validLogin or automation user for crontab
        if ($this->registry['validLogin'] || ($this->registry['currentUser'] && $this->registry['currentUser']->get('id') == PH_AUTOMATION_USER)) {
            if (!$this->isDefined('layouts_' . $mode)) {
                $groups = $this->registry['currentUser']->getGroups();
                if (count($groups)) {
                    // check if current user is assigned as observer of records of this type
                    $module = strtolower(General::singular2plural($this->modelName));
                    $set_observer =
                        in_array('observer', $this->registry['config']->getParamAsArray($module, 'assignment_types_' . $this->get('type'))) &&
                        $this->registry['currentUser']->getPersonalSettings($module, 'set_observer');

                    // make sure model has all types of assignments
                    if ($this->get('id')) {
                        if (!$this->isDefined('assignments_responsible')) {
                            $this->getAssignments('responsible');
                        }
                        if (!$this->isDefined('assignments_decision')) {
                            $this->getAssignments('decision');
                        }
                        if (!$this->isDefined('assignments_observer')) {
                            $this->getAssignments('observer');
                        }
                        if (!$this->isDefined('assignments_owner')) {
                            $this->getAssignments();
                        }
                    }

                    //get rights for layouts view
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="view")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval($this->get('type')) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="view"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_view_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_view = array();
                    foreach ($layouts_view_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_view[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_view[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_view[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_view', $layouts_view);

                    //get rights for layouts edit
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="edit")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval($this->get('type')) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="edit"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_edit_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_edit = array();
                    foreach ($layouts_edit_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_edit[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_edit[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_edit[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_edit', $layouts_edit);
                } else {
                    $this->set('layouts_view', array());
                    $this->set('layouts_edit', array());
                }
            }
        } else {
            $this->set('layouts_view', array());
            $this->set('layouts_edit', array());
        }

        if ($sanitized) {
            $this->sanitize();
        }

        if ($mode) {
            return $this->get('layouts_' . $mode);
        } else {
            return array($this->get('layouts_view'), $this->get('layouts_edit'));
        }
    }

    /**
     * Get parents from database
     *
     * @return bool - result of the operation
     */
    public function getDependencies() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT td.link_to as idx, ti18n.name, td.link_to as id, td.origin FROM ' . DB_TABLE_TASKS_DEPENDENCIES . ' AS td ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TASKS . ' AS t ' . "\n" .
                 '  ON (td.link_to=t.id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TASKS_TYPES . ' AS tt ' . "\n" .
                 '  ON (t.type=tt.id AND tt.active=1 AND tt.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS ti18n ' . "\n" .
                 '  ON (td.link_to=ti18n.parent_id AND ti18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE td.parent_id=' . $this->get('id') . "\n";
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('tasks_dependencies', $records, true);
    }

    /**
     * Get parents from database
     *
     * @return bool - result of the operation
     */
    public function getAllParents() {
        $this->getParents();
        $this->getParentsDocuments();
        $this->getParentsProject();
    }

    /**
     * Get task parents from database
     *
     * @return bool - result of the operation
     */
    public function getParents() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT tr.link_to as idx, ti18n.name, tr.link_to as id FROM ' . DB_TABLE_TASKS_RELATIVES . ' AS tr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TASKS . ' AS t ' . "\n" .
                 '  ON (tr.link_to=t.id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TASKS_TYPES . ' AS tt ' . "\n" .
                 '  ON (t.type=tt.id AND tt.active=1 AND tt.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS ti18n ' . "\n" .
                 '  ON (tr.link_to=ti18n.parent_id AND ti18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE tr.parent_id=' . $this->get('id') . ' AND origin="task"' . "\n";
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('tasks_referers', $records, true);
    }

    /**
     * Get document parents from database
     *
     * @return bool - result of the operation
     */
    public function getParentsDocuments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT tr.link_to as idx, d.full_num, dt.direction, di18n.name, tr.link_to as id, d.archived_by' . "\n" .
                 'FROM ' . DB_TABLE_TASKS_RELATIVES . ' AS tr ' . "\n" .
                 'JOIN `' . DB_TABLE_DOCUMENTS . '` AS d ' . "\n" .
                 '  ON (tr.link_to=d.id) ' . "\n" .
                 'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt ' . "\n" .
                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0) ' . "\n" .
                 'LEFT JOIN `' . DB_TABLE_DOCUMENTS_I18N . '` AS di18n ' . "\n" .
                 '  ON (tr.link_to=di18n.parent_id AND di18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE tr.parent_id=' . $this->get('id') . ' AND origin="document"';
        $query = implode("\nUNION\n",
                         array($query,
                               preg_replace('#`([^`\s]+)`#', '`archive_$1`', $query))) . "\n" .
                 'ORDER BY direction ASC';
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('referers', $records, true);
    }

    /**
     * Get project parents from database
     *
     * @return bool - result of the operation
     */
    public function getParentsProject() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT si18n.parent_id as s_id, si18n.name as phase_name, pi18n.name, tr.link_to as id FROM ' . DB_TABLE_TASKS_RELATIVES . ' AS tr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p ' . "\n" .
                 '  ON (tr.link_to=p.id) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES . ' AS pt ' . "\n" .
                 '  ON (p.type=pt.id AND pt.active=1 AND pt.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n ' . "\n" .
                 '  ON (tr.link_to=pi18n.parent_id AND pi18n.lang="' . $lang . '") ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_STAGES_I18N . ' AS si18n ' . "\n" .
                 '  ON (tr.extra=si18n.parent_id AND si18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE tr.parent_id=' . $this->get('id') . ' AND origin="project"' . "\n";
        $record = $this->registry['db']->GetRow($query);

        return $this->set('project_referer', $record, true);
    }

    /**
     * Get tasks names
     *
     * @return bool - result of the operation
     */
    public function getParentNames($filter) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT ti18n.parent_id as idx, ti18n.name, ti18n.parent_id as id FROM ' .
                 DB_TABLE_TASKS_I18N . ' AS ti18n ' .
                 'WHERE ti18n.lang="' . $lang . '" AND ti18n.parent_id in (' .
                 implode(',', $filter) . ')';
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('tasks_referers' ,$records, true);
    }

    public function hasChildWithId(int $taskId): bool
    {
        $recursionFn = function ($list) use(&$recursionFn) {
            $ids = [];
            $childrenIds = [];
            foreach ($list as $node) {
                $ids[] = $node['id'];
                if (!empty($node['children'])) {
                    $childrenIds[] = $recursionFn($node['children']);
                }
            }
            return array_merge($ids, ...$childrenIds);
        };

        return in_array($taskId, $recursionFn($this->getChildrenTreeDependencies($this->get('id'))));
    }

    public function getChildrenTreeDependencies($parentId, int $level=0): array
    {
        $tbl = DB_TABLE_TASKS_DEPENDENCIES;
        $query = <<<SQL
            SELECT dr.parent_id, dr.origin
            FROM $tbl as dr
            WHERE link_to='{$parentId}';
            SQL;
        $records = $this->registry['db']->GetAll($query);

        $tree = [];
        foreach ($records as $record) {
            $node = [
                'id' => $record['parent_id'],
                'level' => $level,
                'origin' => $record['origin'],
                'children' => [],
            ];
            if ($level < 10) {
                $node['children'] = $this->getChildrenTreeDependencies($record['parent_id'], $level+1);
            }
            $tree[] = $node;
        }
        return $tree;
    }

    public function getParentsTreeDependencies($child, $level=0) {
        $tbl = DB_TABLE_TASKS_DEPENDENCIES;
        $query = <<<SQL
            SELECT dr.link_to, dr.origin
            FROM $tbl as dr
            WHERE parent_id='{$child}';
            SQL;
        $records = $this->registry['db']->GetAll($query);

        $tree = [];
        foreach ($records as $record) {
            $node = [
                'id' => $record['link_to'],
                'level' => $level,
                'origin' => $record['origin'],
                'children' => [],
            ];
            if ($level < 10) {
                $node['children'] = $this->getParentsTreeDependencies($record['link_to'], $level+1);
            }
            $tree[] = $node;
        }
        return $tree;
    }

    public function addTreeRoot(array $childTasks): array
    {
        if (empty($childTasks)) {
            return [];
        }

        // Add the task as tree root
        return [
            [
                'id' => $this->get('id'),
                'level' => -1,
                'origin' => '',
                'children' => $childTasks,
            ]
        ];
    }

    public function getChildrenTree($parent, $level, &$tree, $origin='task') {
        // retrieve all children of $parent
        $query = 'SELECT dr.parent_id, dr.origin FROM ' . DB_TABLE_TASKS_RELATIVES .
                              ' as dr WHERE link_to="' . $parent . '" AND origin="' . $origin . '"';

        $records = $this->registry['db']->GetAll($query);
        // display each child
        foreach ($records as $k=>$rec) {
            $tree[] = array('id' => $rec['parent_id'], 'level' => $level, 'origin' => $rec['origin']);
            if ($level < 10) {
                $this->getChildrenTree($rec['parent_id'], $level+1, $tree);
            }
        }
    }

    /**
     * Gets the sum of the reported time in the timesheets
     *
     * @param bool $force - force to always get the reported time from the database
     * @return int $minutes - the total of minutes reported in the timesheets
     */
    public function getTimesheetTime($force = false) {

        // If the model has no registry
        $sanitize_after = false;
        if ($this->isSanitized()) {
            // Set the registry to the model
            $this->unsanitize();
            $sanitize_after = true;
        }

        if ($this->isDefined('timesheet_time') && !$force) {
            $minutes = $this->get('timesheet_time');
        } else {
            $minutes = 0;

            // If this model has id
            if ($this->get('id')) {
                $query = 'SELECT SUM(duration) FROM ' . DB_TABLE_TASKS_TIMESHEETS .
                         ' WHERE task_id="' . $this->get('id') . '"';
                if ($this->get('event_id')) {
                    $query .= ' AND event_id=' . $this->get('event_id');
                }

                $minutes = $this->registry['db']->GetOne($query);
            }

            $this->set('timesheet_time', $minutes, true);
        }

        // prepare formatted value
        $this->set('timesheet_time_formatted', General::minutes2Human($this->registry, $minutes), true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $minutes;
    }

    /**
     * Gets the sum of the reported billing time in the timesheets
     *
     * @param bool $force - force to always get the billing time from the database
     * @return int $total_minutes - the total of billing minutes reported in the timesheets
     */
    public function getTimesheetBillingTime($force = false) {
        // If we already have billing time and we are not forcing to get the billing time from the database
        if ($this->get('timesheet_billing_time') && !$force) {
            // Return the billing time from the model
            return $this->get('timesheet_billing_time');
        }

        // If the model has no registry
        $sanitize_after = false;
        if ($this->isSanitized()) {
            // Set the registry to the model
            $this->unsanitize();
            $sanitize_after = true;
        }

        // If this model has id
        if ($this->get('id')) {
            // Prepare the SQL query
            $query = 'SELECT SUM(`duration_billing`) FROM `' . DB_TABLE_TASKS_TIMESHEETS . '` ' .
                     '  WHERE `task_id` = "' . $this->get('id') . '"';

            // Get the total billing minutes
            $total_minutes = $this->registry['db']->GetOne($query);
        } else {
            // Set 0 minutes
            $total_minutes = 0;
        }

        // Set the billing hours, minutes and total minutes
        $this->set('timesheet_billing_time', $total_minutes, true);
        $this->set('timesheet_billing_time_formatted', General::minutes2Human($this->registry, $total_minutes), true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $total_minutes;
    }

    /**
     * Sends notification e-mail.
     *
     * @param string $template - template name
     * @param string $email - recipient email
     * @param string $user_name - recipient name and last name
     */
    function sendNotification($template, $email, $user_name) {
        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        //send email
        $mailer = new Mailer($this->registry, $template, $this);
        $mailer->placeholder->merge($this->getNotificationEmailsVars($template));

        $mailer->placeholder->add('to_email', $email);
        $mailer->placeholder->add('user_name', $user_name);

        $mailer->template['model_name'] = $this->modelName;
        $mailer->template['model_id'] = $this->get('id');

        $result = $mailer->send();
        if (!@in_array($email, $result['erred'])) {
            $notify_for = $this->i18n('tasks_' . $template . '_notify', array($this->getModelTypeName()));
            if ($this->registry['sent_email'] != 1) {
                $this->registry['messages']->setMessage($this->i18n('tasks_email_sent_success', array($notify_for)));
                $this->registry->set('sent_email', 1, true);
            }
            $this->registry['messages']->insertInSession($this->registry);
        } else {
            if ($this->registry['err_sent_email'] != 1) {
                $this->registry['messages']->setWarning($this->i18n('error_tasks_send_email', array($notify_for)), '', 10);
                $this->registry->set('err_sent_email', 1, true);
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

    /**
     * Translates some i18n fields
     *
     * @return bool - result of the operation
     */
    public function getDefaultTranslations() {
        $db = $this->registry['db'];
        $model_lang = $this->registry['lang'];

        //select clause
        $sql = array();
        $sql['select'] = 'SELECT d.*, di18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  dti18n.name as type_name, ' . "\n" .
                         '  gi18n.name as group_name, ' . "\n" .
                         '  pi18n.name as project_name ' . "\n";
       //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_TASKS . ' AS d' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS di18n' . "\n" .
                       '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to task group
                       'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                       '  ON (d.group=gi18n.parent_id AND gi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to customers
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to task types i18n
                       'LEFT JOIN ' . DB_TABLE_TASKS_TYPES_I18N . ' AS dti18n' . "\n" .
                       '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to task types i18n
                       'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                       '  ON (d.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n";
        $sql['where'] = 'WHERE d.id=' . $this->get('id') . "\n";

        $query = implode("\n", $sql);
        $records = $db->GetRow($query);

        $this->set('default_translations', $records, true);

        return $records;
    }

    /**
     * gets status of a task
     */
    public function getTaskStatus() {
        $db = $this->registry['db'];
        $query = 'SELECT status FROM ' . DB_TABLE_TASKS .
                 ' WHERE id="' . $this->get('id') . '"';
        $stuses = $db->GetAll($query);
        $current_status = $stuses[0]['status'];

        return $current_status;
    }

    /**
     * Get task attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        if (!isset($this->registry)) {
            $this->unsanitize();
        }
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Task\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted = 0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get tasks files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        //select clause
        $sql = array();
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        //require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        //$ids_where .= Files::getAdditionalWhere($this->registry);

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Gets related to the model records
     *
     * @return array - array with related records
     */
    public function getRelatedRecords() {

        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $related = array();
        $registry = &$this->registry;
        $db = &$registry['db'];

        $rights = $registry['currentUser']->get('rights');

        if ($this->checkPermissions('relatives')) {

            // all links of related records lead to Relatives tab
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=tasks&amp;';
            $link .= 'tasks=relatives&amp;relatives=' . $this->get('id');

            //get related tasks
            $query = 'SELECT DISTINCT(IF(tr.parent_id = ' . $this->get('id') . ', tr.link_to,' . "\n" .
                     '       IF(tr.link_to = ' . $this->get('id') . ', tr.parent_id, NULL))) as id' . "\n" .
                     'FROM ' . DB_TABLE_TASKS_RELATIVES . " AS tr\n" .
                     'LEFT JOIN ' . DB_TABLE_TASKS . " AS t" . "\n" .
                     '  ON t.id = tr.parent_id OR t.id = tr.link_to' . "\n" .
                     'WHERE t.deleted = 0 AND tr.origin = \'task\'' . "\n" .
                     '  AND (tr.parent_id = ' . $this->get('id') . ' OR tr.link_to = ' . $this->get('id') . ')';
            $result = $db->GetCol($query);

            $related['tasks'] = array('name' => 'tasks',
                                      'label' => $this->i18n('menu_tasks'),
                                      'link' => $link,
                                      'ids' => is_array($result) ? $result : array());

            if (!empty($rights['documents']['_access_']) && $rights['documents']['_access_'] != 'none') {
                //get related documents
                $query = 'SELECT tr.link_to' . "\n" .
                         'FROM ' . DB_TABLE_TASKS_RELATIVES . " AS tr\n" .
                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS . " AS d" . "\n" .
                         '  ON d.id = tr.link_to' . "\n" .
                         'WHERE d.deleted_by = 0 AND origin = \'document\' AND parent_id = ' . $this->get('id');
                $result = $db->GetCol($query);

                $related['documents'] = array('name' => 'documents',
                                          'label' => $this->i18n('menu_documents'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
            }

            if (!empty($rights['events']['_access_']) && $rights['events']['_access_'] != 'none') {
                //get related events
                $query = 'SELECT er.parent_id' . "\n" .
                         'FROM ' . DB_TABLE_EVENTS_RELATIVES . " AS er\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS . " AS e" .
                         '  ON er.parent_id=e.id' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES . " AS et" .
                         '  ON e.type=et.id AND et.active=1 AND et.deleted=0' . "\n" .
                         'WHERE e.deleted_by = 0 AND er.origin = \'task\'' . "\n" .
                         '  AND et.keyword != "reminder" AND er.link_to = ' . $this->get('id');
                $result = $db->GetCol($query);

                $related['events'] = array('name' => 'events',
                                           'label' => $this->i18n('menu_events'),
                                           'link' => $link,
                                           'ids' => is_array($result) ? $result : array());
            }
        }

        if (isset($rights[$this->module]['communications']) && $rights[$this->module]['communications'] != 'none' && $this->checkPermissions('communications')) {
            if (isset($rights[$this->module]['comments']) && $rights[$this->module]['comments'] != 'none' && $this->checkPermissions('comments')) {
                //get related comments
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_COMMENTS . "\n" .
                         'WHERE deleted_by = 0 AND model = \'Task\' AND model_id = \'' . $this->get('id') . '\'' . ($registry['currentUser']->get('is_portal') ? ' AND is_portal = \'1\'' : '');

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=tasks&amp;';
                $link .= 'tasks=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=comments';
                $related['comments'] = array('name' => 'comments',
                                              'label' => $this->i18n('tasks_comments'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }

            if (isset($rights[$this->module]['emails']) && $rights[$this->module]['emails'] != 'none' && $this->checkPermissions('emails')) {
                //get related e-mails
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model = \'Task\' AND model_id = ' . $this->get('id') . ' AND `system`=\'0\'' . "\n" .
                         'GROUP BY code';

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=tasks&amp;';
                $link .= 'tasks=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=emails';
                $related['emails'] = array('name' => 'email',
                                          'label' => $this->i18n('tasks_emails'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
            }

            if ($this->checkPermissions('minitasks')) {
                //get related mini tasks
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_MINITASKS . ' AS m' . "\n" .
                         'WHERE model = \'Task\' AND model_id=' . $this->get('id');

                $current_user_id = $registry['currentUser']->get('id');
                $rights = $registry['currentUser']->get('rights');
                $current_right = isset($rights['minitasks']['list']) ? $rights['minitasks']['list'] : '';
                unset($rights);
                if ($current_right == 'mine') {
                    $query .= " AND m.assigned_to=$current_user_id ";
                } elseif ($current_right == 'group') {
                    $query .= " AND (m.added_by=$current_user_id OR m.assigned_to=$current_user_id) ";
                } elseif ($current_right != 'all') {
                    $query .= ' AND 0';
                }

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=tasks&amp;';
                $link .= 'tasks=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=minitasks';
                $related['minitasks'] = array('name' => 'minitasks',
                                              'label' => $this->i18n('menu_minitasks'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }
        }

        if ($this->checkPermissions('attachments')) {
            //get related files
            $query = 'SELECT f.id' . "\n" .
                     'FROM ' . DB_TABLE_FILES . ' AS f ' . "\n" .
                     'WHERE f.deleted_by = 0 AND f.model = \'Task\' AND f.model_id = ' . $this->get('id');

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=tasks&amp;';
            $link .= 'tasks=attachments&amp;attachments=' . $this->get('id');
            $related['files'] = array('name' => 'attachments',
                                      'label' => $this->i18n('attachments'),
                                      'link' => $link,
                                      'ids' => is_array($result) ? $result : array());
        }

        if ($this->checkPermissions('viewtimesheets')) {
            //get related timesheets
            $query = 'SELECT tt.id' . "\n" .
                     'FROM ' . DB_TABLE_TASKS_TIMESHEETS . " AS tt\n" .
                     'WHERE tt.deleted_by = 0 AND tt.task_id = ' . $this->get('id');

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=tasks&amp;';
            $link .= 'tasks=timesheets&amp;timesheets=' . $this->get('id');
            $related['timesheets'] = array('name' => 'timesheets',
                                           'label' => $this->i18n('timesheets'),
                                           'link' => $link,
                                           'ids' => is_array($result) ? $result : array());
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $related;
    }

    /**
     * Get planned time events created from current task
     *
     * @param array $params - parameters modifying behaviour of method:
     *                      - event_id - string or array - id(s) of events to search for
     *                      - status - string or array - status(es) of events to search for
     *                      - user_id - string - id of assignee (only one) of events to search for
     *                      - past - boolean - if specified and true, search for events until today
     *                      - before - ISO-formatted GMT datetime - for search within period
     *                      - after - ISO-formatted GMT datetime - for search within period
     * @return array - planned time events
     */
    public function getPlannedTime($params = array()) {
        $events = array();
        if (!$this->get('id')) {
            return $events;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // if 'event_id' parameter is specified, search by event id(s)
        if (!empty($params['event_id'])) {
            if (!is_array($params['event_id'])) {
                $params['event_id'] = array($params['event_id']);
            }
        }

        // get events created from task
        $query = 'SELECT parent_id' . "\n" .
                 'FROM ' . DB_TABLE_EVENTS_RELATIVES . "\n" .
                 'WHERE link_to=\'' . $this->get('id') . '\' AND origin=\'task\' AND link_type=\'parent\'' . "\n" .
                 (!empty($params['event_id']) ? '  AND parent_id IN (\'' . implode('\', \'', $params['event_id']) . '\')' : '');
        $ids = $this->registry['db']->GetCol($query);

        if ($ids) {
            require_once PH_MODULES_DIR . 'events/models/events.factory.php';
            $filters = array('where' => array('e.id IN (' . implode(',', $ids) . ')',
                                              'et.keyword = \'plannedtime\''),
                             'sort' => array('e.event_start ASC',
                                             'e.priority DESC',
                                             'e.allday_event ASC',
                                             'DATE_ADD(e.event_start, INTERVAL e.duration MINUTE)',
                                             'e.active DESC'),
                             'return_array' => true,
                             'sanitize' => true,
                             'model_lang' => $this->get('model_lang'));
            // if 'user_id' parameter is specified, filter by assignee (only one)
            if (!empty($params['user_id'])) {
                $filters['where'][] = "ea1.participant_id = 'user-{$params['user_id']}'";
            }
            // if 'status' parameter is specified, filter by status(es)
            if (!empty($params['status'])) {
                if (!is_array($params['status'])) {
                    $params['status'] = array($params['status']);
                }
                $filters['where'][] = 'e.status IN (\'' . implode('\', \'', $params['status']) . '\')';
            }
            // if specified, search for events until today
            if (!empty($params['past'])) {
                $filters['where'][] = 'DATE_FORMAT(e.event_start, \'%Y-%m-%d\') <= CURDATE()';
            }
            // if specified, search for events within period (excluding those without fixed start/end)
            if (!empty($params['after']) && !empty($params['before'])) {
                $filters['where'][] = 'e.event_start < \'' . $params['before'] . '\'';
                $filters['where'][] = 'DATE_ADD(e.event_start, INTERVAL e.duration MINUTE) > \'' . $params['after'] . '\'';
                $filters['where'][] = 'e.allday_event = 0';
            }
            $events = Events::search($this->registry, $filters);

            foreach ($events as $idx => $event) {
                // keep just the necessary data for event
                $events[$idx] = array();
                $events[$idx]['event_id'] = $event['id'];
                $events[$idx]['event_start_date'] = substr($event['event_start'], 0, 10);
                $events[$idx]['event_start_time'] = ($event['allday_event'] ? '' : substr($event['event_start'], 11, 5));
                $events[$idx]['event_end_time'] = ($event['allday_event'] ? '' : substr($event['event_end'], 11, 5));
                foreach (array('description', 'priority', 'duration', 'status', 'active') as $key) {
                    $events[$idx][$key] = $event[$key];
                }
                if (isset($event['users_participants'][0])) {
                    $events[$idx]['owner'] = $event['users_participants'][0]['participant_id'];
                    $events[$idx]['assigned_to_name'] = $event['users_participants'][0]['assigned_to_name'];
                } else {
                    $events[$idx]['owner'] = $events[$idx]['assigned_to_name'] = '';
                }
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $events;
    }

    /**
     * Save planned time events for current task
     *
     * @param array $params - parameters modifying behaviour of method:
     *                      - skip_delete - if specified and true, perform insert/update without delete
     * @return bool - result of the operation
     */
    public function savePlannedTime($params = array()) {
        // get event type
        if ($this->get('event_type')) {
            $type = $this->get('event_type');
        } else {
            require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
            $filters = array('where' => array('et.active = 1',
                                              'et.keyword = \'plannedtime\''),
                             'sanitize' => true);
            $type = Events_Types::searchOne($this->registry, $filters);
            if (!$type) {
                $this->raiseError('error_invalid_type');
                return false;
            }
        }

        // validate all submitted data before save
        if (!$this->validatePlannedTime()) {
            return false;
        }

        $db = &$this->registry['db'];
        $request = &$this->registry['request'];
        $request->set('event_end_date', $request->get('event_start_date'), 'post', true);

        // collect all assigned users to task to reuse them in event assignments
        $task_assignments = array();
        foreach (array('owner', 'responsible', 'observer', 'decision') as $atype) {
            $task_assignments = $task_assignments + $this->get('assignments_' . $atype);
        }

        $copy_from_task = array(
            'customer', 'customer_code', 'customer_name',
            'branch', 'contact_person', 'trademark', 'trademark_name',
            'project', 'project_code', 'project_name', 'phase'
        );
        $copy_from_old_model = array(
            'name', 'location', 'availability', 'visibility',
            'type', 'type_name', 'type_keyword', 'active', 'added_by'
        );
        // notify assignees only if any of the submitted properties has been modified
        $request_vars = array(
            'event_id', 'status', 'description', 'owner', 'priority',
            'event_start_date', 'event_start_time', 'event_end_time', 'duration'
        );
        $date_format = $this->i18n('date_short');

        $db->StartTrans();

        require_once PH_MODULES_DIR . 'events/models/events.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.history.php';
        require_once PH_MODULES_DIR . 'events/models/events.audit.php';
        $i18n_files = array(PH_MODULES_DIR . 'events/i18n/' . $this->registry['lang'] . '/events.ini');
        $this->registry['translater']->loadFile($i18n_files);

        // check if current user has permissions to edit task allocation
        $edit_allocate = $this->checkPermissions('edit_allocate');

        // collect ids of events that were manually modified
        $modified = array();
        $new_ids = $request->get('event_id') ?: array();
        foreach ($new_ids as $i => $event_id) {
            $event = Events::buildModelIndex($this->registry, $i);
            if ($event_id) {
                $filters = array('where' => array('e.id = \'' . $event_id . '\'',
                                                  'et.keyword = \'plannedtime\''),
                                 'sanitize' => true,
                                 'model_lang' => $this->get('model_lang'));
                // if current user cannot edit task allocation, then check their relation to event
                if (!$edit_allocate) {
                    if ($this->registry['action'] == 'ajax_watch') {
                        // if starting stopwatch, require that current user is a participant
                        $filters['where'][] = 'ea.ownership = \'mine\'';
                    } else {
                        // otherwise require that user has 'edit' access
                        $filters['where'][] = 'ea.access = \'edit\'';
                    }
                }

                $old_model = Events::searchOne($this->registry, $filters);
                if (!$old_model) {
                    // old model not found
                    $db->FailTrans();
                    continue;
                }
                // don't edit events in another status than planning
                if ($old_model->get('status') != 'planning') {
                    continue;
                }

                $event->set('id', $event_id, true);
                foreach ($copy_from_old_model as $prop) {
                    $event->set($prop, $old_model->get($prop), true);
                }
                if ($old_model->get('event_start_date') != $event->get('event_start_date') &&
                strpos($event->get('name'), General::strftime($date_format, $old_model->get('event_start_date'))) !== false) {
                    $event->set('name',
                                str_replace(General::strftime($date_format, $old_model->get('event_start_date')),
                                            General::strftime($date_format, $event->get('event_start_date')),
                                            $event->get('name')),
                                true);
                }

                // if method is called from planned_time_allocation dashlet,
                // set old event to current model for further use
                if ($this->get('save_single_planned_time')) {
                    $old_copy = clone $old_model;
                    $old_copy->unsanitize();
                    $old_copy->getAssignments();
                    $old_copy->sanitize();
                    $this->set('old_event', $old_copy->getAll(), true);
                    unset($old_copy);
                }

            } else {
                $old_model = new Event($this->registry);
                $old_model->sanitize();

                $event->unsetProperty('id', true);
                $event->set('type', $type->get('id'), true);
                $event->set('type_name', $type->get('name'), true);
                $event->set('type_keyword', $type->get('keyword'), true);
                $event->set('group', $type->getDefaultGroup(), true);
                if (!$event->get('status')) {
                    $event->set('status', 'planning', true);
                }
                $event->set('availability', 'busy', true);
                $event->set('visibility', 'public', true);
                $event->set('name',
                            $this->i18n('tasks_planned_time_name',
                                        array(General::strftime($date_format, $event->get('event_start_date')))),
                            true);
            }
            // properties that should be up-to-date with those of task
            foreach ($copy_from_task as $prop) {
                $event->set($prop, $this->get($prop), true);
            }
            if ($event->get('event_start_time') && $event->get('event_end_time') && $event->get('event_end_time') != '00:00') {
                $event->set('allday_event', 0, true);
                $event->set('event_start', $event->get('event_start_date') . ' ' . $event->get('event_start_time') . ':00', true);
                $event->set('event_end', $event->get('event_end_date') . ' ' . $event->get('event_end_time') . ':00', true);
                if (!$event->get('duration')) {
                    $event->set('duration', (int)(strtotime($event->get('event_end')) - strtotime($event->get('event_start')))/60, true);
                }
            } else {
                $event->set('allday_event', -1, true);
                $event->set('event_start', $event->get('event_start_date') . ' 00:00:00', true);
                $event->set('event_end', $event->get('event_end_date') . ' 23:59:00', true);
            }

            if ($event->save()) {

                // strip slashes as event values will be reused in further actions
                $event->slashesStrip();

                // write history
                Events_History::saveData($this->registry,
                                         array('model' => $event,
                                               'action_type' => ($event_id ? 'edit' : 'add'),
                                               'new_model' => $event,
                                               'old_model' => $old_model));

                // reassign event after edit if necessary
                if ($event_id) {
                    $event->getAssignments();
                    $users_assignments = $event->get('users_assignments') ?: array();
                    $participant_id = $event->get('owner');
                    $currentUserId = $this->registry['currentUser']->get('id');
                    $owner_modified = empty($users_assignments[$participant_id]) || $users_assignments[$participant_id]['ownership'] != 'mine';
                    if ($owner_modified || empty($users_assignments[$currentUserId])) {
                        // assign new owner
                        if ($owner_modified) {
                            if (isset($users_assignments[$participant_id])) {
                                $users_assignments[$participant_id]['ownership'] = 'mine';
                                $users_assignments[$participant_id]['access'] = 'edit';
                                $users_assignments[$participant_id]['invitation_status'] = 'confirmed';
                                $users_assignments[$participant_id]['status_date'] = General::strftime($this->i18n('date_iso'));
                            } else {
                                $users_assignments[$participant_id] = array(
                                    'parent_id' => $event->get('id'),
                                    'participant_type' => 'user',
                                    'participant_id' => $participant_id,
                                    'ownership' => 'mine',
                                    'access' => 'edit',
                                    'invitation_status' => 'confirmed',
                                    'status_date' => General::strftime($this->i18n('date_iso')),
                                    'assigned_to_name' => (!empty($task_assignments[$participant_id]) ?
                                                           $task_assignments[$participant_id]['assigned_to_name'] : '')
                                );
                            }
                            foreach ($users_assignments as $uid => $user) {
                                if ($uid != $participant_id && $user['ownership'] == 'mine') {
                                    $users_assignments[$uid]['ownership'] = 'other';
                                }
                            }
                        }
                        // assign current user as observer so they can edit it from calendar/events modules as well
                        if (empty($users_assignments[$currentUserId])) {
                            $users_assignments[$currentUserId] = array(
                                'parent_id' => $event->get('id'),
                                'participant_type' => 'user',
                                'participant_id' => $currentUserId,
                                'ownership' => 'other',
                                'access' => 'edit',
                                'invitation_status' => 'pending',
                                'status_date' => General::strftime($this->i18n('date_iso')),
                                'assigned_to_name' =>
                                    $this->registry['currentUser']->get('firstname') . ' ' .
                                    $this->registry['currentUser']->get('lastname')
                            );
                        }

                        $old_model_assign = clone $event;
                        $old_model_assign->sanitize();
                        $event->set('new_users', $users_assignments, true);

                        if ($event->assign(false, false)) {
                            // write history
                            $event->set('users_assignments', $users_assignments, true);
                            Events_History::saveData($this->registry,
                                                     array('model' => $event,
                                                           'action_type' => 'assign',
                                                           'new_model' => $event,
                                                           'old_model' => $old_model_assign));
                            // modified assignee
                            if ($owner_modified) {
                                $modified[] = $event_id;
                            }
                        } else {
                            $db->FailTrans();
                        }
                        unset($old_model_assign);
                    }
                    if ($event->get('allday_event') != $old_model->get('allday_event')) {
                        // event could have same start (from 00:00) and end but be with fixed start or not
                        $modified[] = $event_id;
                    } elseif (!$owner_modified) {
                        // check all properties that could be modified in interface
                        foreach ($request_vars as $var) {
                            if (in_array($var, array('event_id', 'owner')) ||
                            // don't compare times if both old and new events are without fixed start/end
                            in_array($var, array('event_start_time', 'event_end_time')) &&
                            $event->get('allday_event') == -1 && $old_model->get('allday_event') == -1) {
                                continue;
                            } elseif (preg_replace('#\r(\n)?#', "\n", $event->get($var)) != preg_replace('#\r(\n)?#', "\n", $old_model->get($var))) {
                                // IMPORTANT: most browsers (but IE8 or older) return newlines in textarea as "\n"
                                // when taken programmatically: element.value, so such differences are ignored
                                // because data could be submitted by an AJAX request or a regular page submit.
                                $modified[] = $event_id;
                                break;
                            }
                        }
                    }

                } else {
                    // get id of added event
                    $modified[] = $new_ids[$i] = $event->get('id');

                    // add relation to task
                    $record = array();
                    $record['link_to'] = $this->get('id');
                    $record['origin'] = 'task';
                    $record['link_type'] = 'parent';
                    $event->updateRelatives($record);
                }
            } else {
                $db->FailTrans();
            }
        }

        $delete_ids = array();
        if (empty($params['skip_delete'])) {

            // get planned time events created from task that could be deleted
            $query = 'SELECT e.id' . "\n" .
                     'FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                     'JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' AS er' . "\n" .
                     '  ON e.id=er.parent_id AND er.link_to=\'' . $this->get('id') . '\' AND er.origin=\'task\'' . "\n" .
                     'WHERE e.type=\'' . $type->get('id') . '\' AND e.deleted_by=0' . "\n" .
                     '  AND e.status=\'planning\' AND link_type=\'parent\'';
            $old_ids = $this->registry['db']->GetCol($query);

            $delete_ids = array_diff($old_ids, $new_ids);

            // update events not present as deleted
            if ($delete_ids && Events::delete($this->registry, $delete_ids)) {
                foreach ($delete_ids as $id) {
                    $event = new Event($this->registry);
                    $event->set('id', $id, true);
                    Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'delete'));
                }
                // deleted events
                $modified = array_merge($modified, $delete_ids);
            }
        }

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        // notify assignees of changes only for modified events
        if ($result && $modified) {
            $filters = array('where' => array('e.id IN (' . implode(',', $modified) . ')',
                                              'e.deleted IS NOT NULL'),
                             'sort' => array('e.event_start ASC',
                                             'e.priority ASC',
                                             'e.allday_event ASC',
                                             'DATE_ADD(e.event_start, INTERVAL e.duration MINUTE)',
                                             'e.active DESC'),
                             'return_array' => true,
                             'sanitize' => true,
                             'model_lang' => $this->get('model_lang'));
            require_once PH_MODULES_DIR . 'events/models/events.factory.php';
            $events = Events::search($this->registry, $filters);

            // notify assignees (event participants) of changes
            $this->sendPlannedTimeNotification($events);

            // if method is called from planned_time_allocation dashlet,
            // get saved event and set it to current model for further use
            if ($this->get('save_single_planned_time') && $events) {
                $this->set('new_event', reset($events), true);
            }
        }

        return $result;
    }

    /**
     * Notify participant users for modifications with planned time events
     *
     * @param array[] $events - data for events after modifications
     */
    public function sendPlannedTimeNotification(&$events) {

        $template = 'task_allocate';

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        //get users whom not send e-mails to
        $not_users = Users::getUsersNoSend($this->registry, $template);

        $users_grouped = array();
        foreach ($events as $idx => $event) {
            $events[$idx]['action'] =
                $this->i18n($event['deleted_by'] ? 'delete' :
                            (in_array($event['id'], $this->registry['request']->get('event_id')) ? 'edit' : 'add'));
            $events[$idx]['view_url'] = sprintf('%s/index.php?%s=events&events=view&view=%d',
                                                $this->registry['config']->getParam('crontab', 'base_host'),
                                                $this->registry['module_param'], $event['id']);
            foreach ($event['users_participants'] as $user) {
                if ($user['participant_id'] == $this->registry['currentUser']->get('id') ||
                in_array($user['participant_id'], $not_users) || !$user['active'] || !$user['email']) {

                } else {
                    if (!isset($users_grouped[$user['participant_id']])) {
                        $users_grouped[$user['participant_id']] = $user + array('events' => array());
                    }
                    $users_grouped[$user['participant_id']]['events'][$idx] = &$events[$idx];
                }
            }
        }

        if ($users_grouped) {
            $mailer = new Mailer($this->registry, $template, $this);
            $mailer->placeholder->merge($this->getNotificationEmailsVars($template));

            $mailer->template['model_name'] = $this->modelName;
            $mailer->template['model_id'] = $this->get('id');

            $i18n_files = array();
            $i18n_files[] = PH_MODULES_DIR . 'events/i18n/' . $this->registry['lang'] . '/events.ini';
            $i18n_files[] = PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini';

            $sent_to = array();
            foreach ($users_grouped as $user) {
                $mailer->placeholder->add('to_email', $user['email']);
                $mailer->placeholder->add('user_name', $user['assigned_to_name']);
                $viewer = new Viewer($this->registry);
                $viewer->loadCustomI18NFiles($i18n_files);
                $viewer->setFrameset(PH_MODULES_DIR . 'tasks/templates/_allocate_email.html');
                $viewer->data['events'] = $user['events'];
                $mailer->placeholder->add('events_info', $viewer->fetch());

                $mailer_result = $mailer->send();

                if (!@in_array($user['email'], $mailer_result['erred'])) {
                    $sent_to[] = $user['assigned_to_name'];
                }
            }

            if (count($sent_to)) {
                $notify_for = $this->i18n('tasks_' . $template . '_notify', array($this->getModelTypeName()));
                if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                    $this->registry['messages']->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
                } else {
                    $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
                }
                $this->registry['messages']->insertInSession($this->registry);
            }
        }
    }

    /**
     * Validate all submitted data before saving planned time events
     *
     * @return boolean - true if valid, false if invalid
     */
    public function validatePlannedTime() {
        $valid = true;
        $request = &$this->registry['request'];
        $events = array();
        $request_vars = array(
            'event_id', 'status', 'description', 'owner', 'priority',
            'event_start_date', 'event_start_time', 'event_end_time', 'duration'
        );
        if ($request->isRequested('event_id')) {
            foreach ($request->get('event_id') as $idx => $event_id) {
                foreach ($request_vars as $key) {
                    $events[$idx][$key] = $request[$key] && isset($request[$key][$idx]) ? $request[$key][$idx] : '';
                }
            }
        }
        if (!$events) {
            return $valid;
        }
        $date_min = substr($this->get('planned_start_date'), 0, 10);
        $today = General::strftime($this->i18n('date_iso_short'));
        if ($date_min < $today) {
            $date_min = $today;
        }
        $date_max = substr($this->get('planned_finish_date'), 0, 10);
        $required_invalid = array();
        $allocated_time = 0;
        $user_date_times = array();
        foreach ($events as $idx => $event) {
            if (!$event['status'] || $event['status'] == 'planning') {
                if ((!$event['owner'] || !preg_match('#,' . $event['owner'] . ',#', $this->get('user_permissions'))) && !array_key_exists('owner', $required_invalid)) {
                    $required_invalid['owner'] = $this->i18n('tasks_assign_owner');
                }
                if (!$event['priority'] && !array_key_exists('priority', $required_invalid)) {
                    $required_invalid['priority'] = $this->i18n('tasks_priority');
                }
                if ((!$event['event_start_date'] ||
                $event['event_start_date'] < $date_min ||
                $event['event_start_date'] > $date_max) && !array_key_exists('event_start_date', $required_invalid)) {
                    $required_invalid['event_start_date'] = $this->i18n('tasks_date');
                }
                if (($event['event_start_time'] &&
                (!$event['event_end_time'] || $event['event_start_time'] >= $event['event_end_time'])) &&
                !array_key_exists('event_end_time', $required_invalid)) {
                    $required_invalid['event_end_time'] = ''; // no label needed, just a flag
                }
                if (!$event['event_start_time'] && $event['event_end_time'] &&
                !array_key_exists('event_start_time', $required_invalid)) {
                    $required_invalid['event_start_time'] = ''; // no label needed, just a flag
                }
                if (($event['duration'] <= 0 || $event['duration'] > 1440) &&
                !array_key_exists('duration', $required_invalid)) {
                    $required_invalid['duration'] = ''; // no label needed, just a flag
                }
            }
            if ($event['owner'] && $event['event_start_date'] &&
            $event['event_start_time'] && $event['event_end_time'] &&
            $event['event_start_time'] < $event['event_end_time']) {
                $user_date_key = $event['owner'] . '|' . $event['event_start_date'];
                if (!isset($user_date_times[$user_date_key])) {
                    $user_date_times[$user_date_key] = array();
                }
                $user_date_times[$user_date_key][] = array($event['event_start_time'], $event['event_end_time']);
            }
            $allocated_time += $event['duration'];
        }
        if ($required_invalid) {
            $valid = false;
            foreach ($required_invalid as $field => $label) {
                if ($field == 'event_start_time') {
                    $this->raiseError('error_timesheets_startperiod', $field);
                } elseif ($field == 'event_end_time') {
                    $this->raiseError('error_timesheets_endperiod', $field);
                } elseif ($field == 'duration') {
                    $this->raiseError('error_timesheets_duration', $field);
                } elseif ($field == 'event_start_date') {
                    $this->raiseError('error_validDate', $field, null, array('var_label' => $label));
                } else {
                    $this->raiseError('error_empty_field', $field, null, array('var_label' => $label));
                }
            }
        }

        // compare times for every user+date
        foreach ($user_date_times as $user_date_key => $times) {
            $len = count($times);
            if ($len > 1) {
                for ($c1 = 0; $c1 < $len; $c1++) {
                    for ($c2 = $len - 1; $c2 > $c1; $c2--) {
                        if ($times[$c1][0] >= $times[$c2][1] || $times[$c1][1] <= $times[$c2][0]) {
                            // not overlapping
                        } else {
                            $valid = false;
                            $this->raiseError('error_tasks_planned_time_overlap');
                            break 3;
                        }
                    }
                }
            }
        }

        // if method is called from planned_time_allocation dashlet,
        // just one event is added/edited, so duration of other events should be added
        if ($this->get('save_single_planned_time')) {
            $allocated_time += $this->getPlannedDuration($request->get('event_id'));
        }

        // validate allocated time against planned time of task
        if ($this->get('planned_time') && $this->get('planned_time') < $allocated_time) {
            $valid = false;
            $info = $this->get('save_single_planned_time') ?
                sprintf(' (%d > %d)', $allocated_time, $this->get('planned_time')) : '';
            $this->raiseError('error_tasks_allocated_time', '', '', array($info));
        }

        return $valid;
    }

    /**
     * Get total duration of all planned time events for task
     *
     * @param mixed $exclude - id(s) of events to exclude (optional)
     * @return number - duration in minutes
     */
    public function getPlannedDuration($exclude = array()) {
        if (!is_array($exclude)) {
            $exclude = array($exclude);
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $query = 'SELECT SUM(e.duration) FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                 'JOIN ' . DB_TABLE_EVENTS_TYPES . ' AS et' . "\n" .
                 '  ON e.type=et.id AND et.keyword=\'plannedtime\' AND e.deleted_by=0 AND et.active=1 AND et.deleted=0' . "\n" .
                 'JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' AS er' . "\n" .
                 '  ON e.id=er.parent_id AND er.link_to=\'' . $this->get('id') . '\' AND er.origin=\'task\'' . "\n" .
                 ($exclude ? 'WHERE e.id NOT IN (\'' . implode('\', \'', $exclude) . '\')' . "\n" : '');
        $planned_duration = (int)$this->registry['db']->GetOne($query);

        $this->set('planned_duration', $planned_duration, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $planned_duration;
    }

    /**
     * Gets planned time events for task that are assigned to specified user.
     * Options are used for adding timesheets so future events, as well as
     * finished ones, should not be included.
     *
     * @param array $params - parameters modifying behaviour of method:
     *                      - event_id - string or array - id(s) of events to search for
     *                      - status - string or array - status(es) of events to search for
     *                      - user_id - string - id of assignee (only one) of events to search for
     *                      - past - boolean - if specified and true, search for events until today
     * @return array - dropdown options for planned time events (event ids as keys)
     */
    public function getPlannedTimeOptions($params = array()) {
        $options = array();
        // at least one of the filters is required
        if (empty($params['user_id']) && empty($params['event_id'])) {
            return $options;
        }
        // when searching by user_id and not by event_id, add implicit filters
        // to exclude finished and future events
        if (!empty($params['user_id']) && empty($params['event_id'])) {
            if (!isset($params['status'])) {
                $params['status'] = array('planning', 'progress');
            }
            if (!isset($params['past'])) {
                $params['past'] = true;
            }
        }
        $date_format = $this->i18n('date_short');
        $events = $this->getPlannedTime($params);
        foreach ($events as $event) {
            $options[$event['event_id']] = array(
                'option_value' => $event['event_id'],
                'active_option' => $event['active'],
                'class_name' => implode('|', array($event['event_start_date'], $event['event_start_time'],
                                                   $event['event_end_time'], $event['duration'])),
                'label' => sprintf('%s (%s)',
                    General::strftime($date_format, $event['event_start_date']),
                    ($event['event_start_time'] && $event['event_end_time'] ?
                    $event['event_start_time'] . ' - ' . $event['event_end_time'] :
                    $event['duration'] . ' ' . $this->i18n('minute' . (abs($event['duration']) != 1 ? 's' : ''))))
            );
        }
        unset($events);

        return $options;
    }

    /**
     * Validate user assignments: users who are assigned to non-completed
     * planned time events cannot be removed.
     *
     * @return boolean - result of the operation
     */
    public function validateAssign() {
        $assign_valid = true;

        // if execution of task is finished, all events must have been finished beforehand
        if ($this->get('status') == 'finished') {
            return $assign_valid;
        }

        // search for all non-completed planned time events
        $events = $this->getPlannedTime(array('status' => array('planning', 'progress')));

        if (!$events) {
            return $assign_valid;
        }

        // get users assigned to non-completed planned time events
        $participants = array();
        array_walk($events, function($a) use (&$participants) {
            if ($a['owner'] && !isset($participants[$a['owner']])) {
                $participants[$a['owner']] = $a['assigned_to_name'];
            }
        });

        // get new user assignments to task
        $assignments = array();
        foreach (array('owner', 'responsible', 'observer', 'decision') as $atype) {
            if ($this->get('assignments_' . $atype)) {
                foreach ($this->get('assignments_' . $atype) as $assignment) {
                    if (is_array($assignment)) {
                        $assignment = $assignment['assigned_to'];
                    }
                    // if action is 'observer' and current user is assigned as such,
                    // then they are removing their assignment so skip it
                    if ($atype == 'observer' && $this->registry['action'] == 'observer' &&
                    $this->registry['currentUser'] && $assignment == $this->registry['currentUser']->get('id')) {
                        continue;
                    }
                    if (!in_array($assignment, $assignments)) {
                        $assignments[$assignment] = $assignment;
                    }
                }
            }
        }

        $missing = array_diff_key($participants, $assignments);
        if ($missing) {
            $assign_valid = false;
            $this->raiseError('error_tasks_assign_plannedtime', '', null, array(implode(', ', $missing)));
        }

        return $assign_valid;
    }

    /**
     * Checks whether minitasks should be completed to finish the task
     *
     * @return string
     */
    public function requiresCompletedMinitasks() {

        //get the task type
        $filters = array('where' =>
            array(
                'tt.id = ' . $this->get('type'),
                'tt.deleted IS NOT NULL',
                'tt.system IS NOT NULL'
            ),
            'sanitize' => true);
        $type = Tasks_Types::searchOne($this->registry, $filters);

        return $type->get('requires_completed_minitasks');
    }
}

?>
