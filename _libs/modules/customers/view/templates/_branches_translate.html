    <form name="branches_add" action="" method="post" onsubmit="saveBranch(this,'{$customers_branches_session_param}', 'translate', '{$branch->get('parent_customer_id')}', '{$branch->get('id')}'); return false;">
      <input type="hidden" name="parent_customer_id" id="parent_customer_id" value="{$branch->get('parent_customer_id')}" />
      <input type="hidden" name="model_lang" id="model_lang" value="{$branch->get('model_lang')|default:$lang}" />
      <input type="hidden" name="main_branch" id="main_branch" value="{$branch->get('main_branch')}" />

      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border t_layout_table" style="width: 75%;">
        <tr>
          <td class="t_caption" nowrap="nowrap" colspan="7"><div class="t_caption_title"><img src="{$theme->imagesUrl}flags/{$branch->get('model_lang')}.png" alt="" {capture assign='lang_label'}lang_{$branch->get('model_lang')}{/capture} title="{$smarty.config.$lang_label}" class="t_flag" />{$branch->getBranchLabels('customers_branches_translate_branch')|escape}</div></td>
        </tr>
        <tr>
          <td colspan="3">
            {#message_translatable_items#|escape}
          </td>
          <td class="vtop t_border divider_cell" rowspan="7">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="2">&nbsp;</td>
          {capture assign='source_lang'}lang_{$branch->get('model_lang')}{/capture}
          <td><img src="{$theme->imagesUrl}flags/{$branch->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
          <td>&nbsp;</td>
          {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
          <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
        </tr>
        <tr>
          <td class="labelbox"><label for="name">{help label_content=$branch->getBranchLabels('customers_branches_name')}</label></td>
          <td class="required" style="width: 10px!important">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="name" id="name" value="{$branch->get('name')|escape}" title="{$branch->getBranchLabels('customers_branches_name')|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{$branch->getBranchLabels('customers_branches_name')|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="country">{help label='branches_country'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            {$branch->get('country_name')|escape}
            <input type="hidden" name="country" id="country" value="{$branch->get('country')}" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><label for="city">{help label='branches_city'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="city" id="city" value="{$branch->get('city')|escape}" title="{#customers_branches_city#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_city" id="copy_city" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_city" id="bm_city" value="{$base_model->get('city')|escape}" title="{#customers_branches_city#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="postal_code">{help label='branches_postal_code'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="postal_code" id="postal_code" value="{$branch->get('postal_code')|escape}" title="{#customers_branches_postal_code#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><label for="address">{help label='branches_address'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox distinctive" name="address" id="address" title="{#customers_branches_address#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$branch->get('address')|escape}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_address" id="copy_address" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_address" id="bm_address" title="{#customers_branches_address#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('address')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox" colspan="7">{help label='branches_contacts'}</td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="7" class="nopadding">
          {include file=`$templatesDir`_contact_data.html object=$branch predefined_contact_params=$branch->predefinedBranchContactParameters}
          </td>
        </tr>
        <tr>
          <td colspan="7">
            <button type="submit" class="button" name="translateBranch" id="translateBranch">{#translate#|escape}</button><button type="button" name="cancel" class="button" onclick="confirmAction('cancel', function() {ldelim} $('branch_custom_panel').innerHTML = ''; {rdelim}, this)" title="{#help_cancel#|escape}">{#cancel#|escape}</button><button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
          </td>
        </tr>
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
    </form>