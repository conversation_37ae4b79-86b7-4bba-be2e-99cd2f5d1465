    <form name="branches_add" action="" method="post" onsubmit="saveBranch(this,'{$customers_branches_session_param}', 'add', '{$branch->get('parent_customer_id')}'); return false;">
      <input type="hidden" name="parent_customer_id" id="parent_customer_id" value="{$branch->get('parent_customer_id')}" />
      <input type="hidden" name="model_lang" id="model_lang" value="{$branch->get('model_lang')|default:$lang}" />

      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border t_layout_table" style="width: 75%;">
        <tr>
          <td class="t_caption" nowrap="nowrap" colspan="3"><div class="t_caption_title"><img src="{$theme->imagesUrl}flags/{$branch->get('model_lang')}.png" alt="" {capture assign='lang_label'}lang_{$branch->get('model_lang')}{/capture} title="{$smarty.config.$lang_label}" class="t_flag" />{$branch->getBranchLabels('customers_branches_add_branch')|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><label for="name">{help label_content=$branch->getBranchLabels('customers_branches_name')}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="name" id="name" value="{$branch->get('name')|escape}" title="{$branch->getBranchLabels('customers_branches_name')|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a id="error_country"><label for="country"{if $messages->getErrors('country')} class="error"{/if}>{help label='branches_country'}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox{if !$branch->get('country')} undefined{/if}" name="country" id="country" title="{#customers_branches_country#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
              <option value="" class="undefined"{if !$branch->get('country')} selected="selected"{/if}>[{#please_select#|escape}]</option>
              {foreach from=$countries item='country'}
                <option value="{$country.option_value}"{if $branch->get('country') eq $country.option_value} selected="selected"{/if}>{$country.label|escape}</option>
              {/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="city">{help label='branches_city'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="city" id="city" value="{$branch->get('city')|escape}" title="{#customers_branches_city#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="postal_code">{help label='branches_postal_code'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="postal_code" id="postal_code" value="{$branch->get('postal_code')|escape}" title="{#customers_branches_postal_code#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="address">{help label='branches_address'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="address" id="address" title="{#customers_branches_address#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$branch->get('address')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox" colspan="3">{help label='branches_contacts'}</td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="3" class="nopadding">
          {include file=`$templatesDir`_contact_data.html object=$branch predefined_contact_params=$branch->predefinedBranchContactParameters}
          </td>
        </tr>
        <tr><td colspan="3"></td></tr>
        <tr>
          <td colspan="3" class="nz-form--controls">
            <button type="submit" class="nz-form-button nz-button-primary" name="addBranch" id="addBranch">{#add#|escape}</button><button type="button" name="cancel" class="nz-form-button" onclick="confirmAction('cancel', function() {ldelim} $('branch_custom_panel').innerHTML = ''; {rdelim}, this)" title="{#help_cancel#|escape}">{#cancel#|escape}</button>
          </td>
        </tr>
        <tr>
          <td class="t_footer" colspan="3"></td>
        </tr>
      </table>
    </form>
