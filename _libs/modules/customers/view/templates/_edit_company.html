      {if empty($available_actions)}
        {include file='layouts_index.html' display='abs_div'}
      {/if}
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
      {assign var='super_layout_offset' value='0'}
      {foreach from=$customer->get('layouts_details') key='lkey' item='layout'}

      {if $layout.place gt $super_layout_offset}
        {if $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_MAIN_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_MAIN_TO}
          {assign var='super_layout' value='main_data'}
          {assign var='super_layout_class' value='customers_main_data'}
        {elseif $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_ADDRESS_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_ADDRESS_TO}
          {assign var='super_layout' value='contact_data'}
          {assign var='super_layout_class' value='customers_contact_data'}
        {elseif $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_REG_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_REG_TO}
          {assign var='super_layout' value='company_data'}
          {assign var='super_layout_class' value='customers_company_data'}
        {else}
          {assign var='super_layout' value=''}
          {assign var='super_layout_class' value=''}
        {/if}
        {assign var='super_layout_cookie' value=''}
        {if $super_layout && $customer->get('super_layouts') && in_array($super_layout, $customer->get('super_layouts'))}
        <tr>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
            </div>
            <div class="layout_switch" onclick="toggleViewLayouts(this)" id="customers_{$super_layout}_switch">
              {capture assign='super_layout_cookie'}customers_{$super_layout}_box{/capture}
              {capture assign='super_layout_name'}customers_{$super_layout}{/capture}
              <a name="customer_{$super_layout}_index"></a><div class="switch_{if $smarty.cookies.$super_layout_cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$smarty.config.$super_layout_name|escape}{if $super_layout eq 'contact_data' && $customer->get('main_branch_name')} - {$customer->get('main_branch_name')|escape}{/if}</div>
            </div>
          </td>
        </tr>
        {/if}
      {/if}

        {if $layout.system || $layout.view && array_key_exists($layout.id, $layouts_vars)}
        <tr{if $layout.view && $layout.visible} class="{$super_layout_class}"{/if}{if !$layout.view || !$layout.visible || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
            </div>
            <div class="layout_switch" {if $layout.system}onclick="toggleViewLayouts(this)" id="customer_{$layout.keyword}_switch"{else}onclick="toggleLayouts(this)" id="layout_{$layout.id}_switch"{/if}>
              <a name="customer_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
            </div>
          </td>
        </tr>
        {/if}

        {if $lkey eq 'name'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="name" id="name" value="{$customer->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="name" id="name" value="{$customer->get('name')|escape}" />
              {include file=`$templatesDir`_info.html assign='info'}
              <span {popup text=$info|escape caption=#system_info#|escape width=250}>{mb_truncate_overlib text=$customer->get('name')|escape|default:"&nbsp;"}</span>
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'type'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_type"><label for="type"{if $messages->getErrors('type')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$customer->get('type_name')|escape}
            <input type="hidden" name="type" id="type" value="{$customer->get('type')|escape}" />
            <input type="hidden" name="type_name" id="type_name" value="{$customer->get('type_name')|escape}" />
          </td>
        </tr>
        {elseif $lkey eq 'is_company'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_is_company"><label for="is_company">{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $customer->get('is_company')}
              {#customers_company#|escape}
            {else}
              {#customers_person#|escape}
            {/if}
            <input type="hidden" name="is_company" id="is_company" value="{$customer->get('is_company')}" />
          </td>
        </tr>
        {elseif $lkey eq 'code'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_code"><label for="code"{if $messages->getErrors('code')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="code" id="code" value="{$customer->get('code')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="code" id="code" value="{$customer->get('code')|escape}" />
              {$customer->get('code')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'num'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_num"><label for="num"{if $messages->getErrors('num')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$customer->get('num')|escape}
            <input type="hidden" value="{$customer->get('num')|escape}" name="num" id="num" />
          </td>
        </tr>
        {elseif $lkey eq 'admit_VAT_credit'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_admit_VAT_credit"><label for="admit_VAT_credit"{if $messages->getErrors('admit_VAT_credit')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">&nbsp;</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="checkbox" value="1" class="checkbox" name="admit_VAT_credit" id="admit_VAT_credit"{if $customer->get('admit_VAT_credit') eq 1} checked="checked"{/if} title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="admit_VAT_credit" id="admit_VAT_credit" value="{$customer->get('admit_VAT_credit')|escape}" />
              {if $customer->get('admit_VAT_credit')}{#yes#}{else}{#no#}{/if}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'department'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_department"><label for="department"{if $messages->getErrors('department')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              {capture assign='current_department'}{$customer->get('department')}{/capture}
              <select name="department" id="department" class="selbox{if !$departments} missing_records{elseif !$current_department} undefined{/if}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); changeAssigningOptions(this, 'assigned');" onkeypress="dropdownTypingSearch(this, event);" title="{$layout.name|escape}">
                {if $departments}
                  <option value="" class="undefined"{if !$current_department} selected="selected"{/if}>[{#please_select#|escape}]</option>
                  {foreach from=$departments item='department'}
                    {if !$department->get('deleted_by') && ($department->get('active') || $department->get('id') === $current_department)}
                      <option value="{$department->get('id')}"{if $department->get('id') eq $current_department} selected="selected"{/if}{if !$department->get('active')} class="inactive_option" title="{#inactive_option#}" disabled="disabled"{/if}>{if !$department->get('active')}*&nbsp;{/if}{$department->get('name')|indent:$department->get('level'):'-'|escape}</option>
                    {/if}
                  {/foreach}
                {else}
                  <option value="" class="missing_records"{if !$current_department} selected="selected"{/if}>[{#no_select_records#|escape}]</option>
                {/if}
              </select>
            {else}
              <input type="hidden" name="department" id="department" value="{$customer->get('department')|escape}" />
              {$customer->get('department_name')|escape|default:"&nbsp;"}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'assigned'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_assigned"><label for="assigned"{if $messages->getErrors('assigned')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              {capture assign='current_assigned'}{$customer->get('assigned')}{/capture}
              <select name="assigned" id="assigned" class="selbox{if !$department_users} missing_records{elseif !$current_assigned} undefined{/if}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="{$layout.name|escape}">
                {if $department_users}
                  <option value="" class="undefined"{if !$current_assigned} selected="selected"{/if}>[{#please_select#|escape}]</option>
                {else}
                  <option value="" class="missing_records"{if !$current_assigned} selected="selected"{/if}>[{#no_select_records#|escape}]</option>
                {/if}
                {foreach from=$department_users item='dep_user'}
                  <option value="{$dep_user.id}"{if $dep_user.id eq $current_assigned} selected="selected"{/if}>{$dep_user.name|escape}</option>
                {/foreach}
              </select>
            {else}
              <input type="hidden" name="assigned" id="assigned" value="{$customer->get('assigned')|escape}" />
              {$customer->get('assigned_to_name')|escape|default:"&nbsp;"}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'country'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_country"><label for="country"{if $messages->getErrors('country')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <select class="selbox{if !$customer->get('country')} undefined{/if}" name="country" id="country" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                <option value="" class="undefined"{if !$customer->get('country')} selected="selected"{/if}>[{#please_select#|escape}]</option>
                {foreach from=$countries item='country'}
                  <option value="{$country.option_value}"{if $customer->get('country') eq $country.option_value} selected="selected"{/if}>{$country.label|escape}</option>
                {/foreach}
              </select>
            {else}
              <input type="hidden" name="country" id="country" value="{$customer->get('country')|escape}" />
              {$customer->get('country_name')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'city'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_city"><label for="city"{if $messages->getErrors('city')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="city" id="city" value="{$customer->get('city')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="city" id="city" value="{$customer->get('city')|escape}" />
              {$customer->get('city')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'postal_code'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_postal_code"><label for="postal_code"{if $messages->getErrors('postal_code')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="postal_code" id="postal_code" value="{$customer->get('postal_code')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="postal_code" id="postal_code" value="{$customer->get('postal_code')|escape}" />
              {$customer->get('postal_code')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'address'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_address"><label for="address"{if $messages->getErrors('address')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="address" id="address" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if !$layout.edit} style="display: none;"{/if}>{$customer->get('address')|escape}</textarea>
            {if !$layout.edit}{$customer->get('address')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}{/if}
          </td>
        </tr>
        {elseif $lkey eq 'notes'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_notes"><label for="notes"{if $messages->getErrors('notes')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="notes" id="notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if !$layout.edit} style="display: none;"{/if}>{$customer->get('notes')|escape}</textarea>
            {if !$layout.edit}{$customer->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}{/if}
          </td>
        </tr>
        {elseif $lkey eq 'contacts'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox" colspan="3">
            {include file=`$templatesDir`_contact_data.html object=$customer predefined_contact_params=$customer->getPredefinedContactParameters()}
          </td>
        </tr>
        {elseif $lkey eq 'company_name'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_company_name"><label for="company_name"{if $messages->getErrors('company_name')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td>
            {if $layout.edit}
              <input type="text" class="txtbox" name="company_name" id="company_name" value="{$customer->get('company_name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="company_name" id="company_name" value="{$customer->get('company_name')|escape}" />
              {$customer->get('company_name')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'in_dds'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_in_dds"><label for="in_dds"{if $messages->getErrors('in_dds')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="in_dds" id="in_dds" value="{$customer->get('in_dds')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
              <i class="nz-icon-button"  title="{#customers_refresh_company_info#|escape}" onclick="return confirmAction('refresh_company_info', function() {ldelim} getCompanyInfoByVat(); {rdelim}, this);">refresh</i>
              {*<img src="{$theme->imagesUrl}small/refresh.png" class="icon_button pointer vmiddle" width="14" height="14" alt="{#customers_refresh_company_info#|escape}" title="{#customers_refresh_company_info#|escape}" border="0" onclick="return confirmAction('refresh_company_info', function() {ldelim} getCompanyInfoByVat(); {rdelim}, this);" />*}
            {else}
              <input type="hidden" name="in_dds" id="in_dds" value="{$customer->get('in_dds')|escape}" />
              {$customer->get('in_dds')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'eik'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_eik"><label for="eik"{if $messages->getErrors('eik')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="eik" id="eik" value="{$customer->get('eik')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="eik" id="eik" value="{$customer->get('eik')|escape}" />
              {$customer->get('eik')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'registration_file'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_registration_file"><label for="registration_file"{if $messages->getErrors('registration_file')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="registration_file" id="registration_file" value="{$customer->get('registration_file')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="registration_file" id="registration_file" value="{$customer->get('registration_file')|escape}" />
              {$customer->get('registration_file')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'registration_volume'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_registration_volume"><label for="registration_volume"{if $messages->getErrors('registration_volume')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="registration_volume" id="registration_volume" value="{$customer->get('registration_volume')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="registration_volume" id="registration_volume" value="{$customer->get('registration_volume')|escape}" />
              {$customer->get('registration_volume')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'registration_number'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_registration_number"><label for="registration_number"{if $messages->getErrors('registration_number')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="registration_number" id="registration_number" value="{$customer->get('registration_number')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="registration_number" id="registration_number" value="{$customer->get('registration_number')|escape}" />
              {$customer->get('registration_number')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'registration_address'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_registration_address"><label for="registration_address"{if $messages->getErrors('registration_address')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="registration_address" id="registration_address" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if !$layout.edit} style="display: none;"{/if}>{$customer->get('registration_address')|escape}</textarea>
            {if !$layout.edit}{$customer->get('registration_address')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}{/if}
          </td>
        </tr>
        {elseif $lkey eq 'mol'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_mol"><label for="mol"{if $messages->getErrors('mol')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="mol" id="mol" value="{$customer->get('mol')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="mol" id="mol" value="{$customer->get('mol')|escape}" />
              {$customer->get('mol')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'bank'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_bank"><label for="bank"{if $messages->getErrors('bank')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="bank" id="bank" value="{$customer->get('bank')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="bank" id="bank" value="{$customer->get('bank')|escape}" />
              {$customer->get('bank')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'iban'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_iban"><label for="iban"{if $messages->getErrors('iban')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="iban" id="iban" value="{$customer->get('iban')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="iban" id="iban" value="{$customer->get('iban')|escape}" />
              {$customer->get('iban')|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'bic'}
        <tr{if $layout.view} class="{$super_layout_class}"{/if} id="customer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_bic"><label for="bic"{if $messages->getErrors('bic')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {if $layout.edit}
              <input type="text" class="txtbox" name="bic" id="bic" value="{$customer->get('bic')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              <input type="hidden" name="bic" id="bic" value="{$customer->get('bic')|escape}" />
              {$customer->get('bic')|escape}
            {/if}
          </td>
        </tr>
        {elseif $layout.view && array_key_exists($layout.id, $layouts_vars)}
        <!-- Customer Additional Vars -->
        {include file="`$templatesDir`_manage_vars.html"}
        {/if}
      {/foreach}
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <input type="hidden" name="main_branch_id" id="main_branch_id" value="{$customer->get('main_branch_id')}" />
            {strip}
              {if $customer->get('buttons')}
                {foreach from=$customer->get('buttons') item='button'}
                  {include file=`$theme->templatesDir`input_button.html
                    label=$button.label
                    standalone=true
                    name=$button.name
                    source=$button.source
                    disabled=$button.disabled
                    hidden=$button.hidden
                    width=$button.width
                    height=$button.height}
                {/foreach}
              {/if}

              <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>
              {include file=`$theme->templatesDir`cancel_button.html}
            {/strip}
          </td>
        </tr>
      </table>
