      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
      {assign var='super_layout_offset' value='0'}
      {foreach from=$customer->get('layouts_details') key='lkey' item='layout'}
      {if $layout.view}

      {if $layout.place gt $super_layout_offset}
        {if $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_MAIN_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_MAIN_TO}
          {assign var='super_layout' value='main_data'}
          {assign var='super_layout_class' value='customers_main_data'}
        {elseif $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_ADDRESS_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_ADDRESS_TO}
          {assign var='super_layout' value='contact_data'}
          {assign var='super_layout_class' value='customers_contact_data'}
        {elseif $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_REG_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_REG_TO}
          {assign var='super_layout' value='company_data'}
          {assign var='super_layout_class' value='customers_company_data'}
        {else}
          {assign var='super_layout' value=''}
          {assign var='super_layout_class' value=''}
        {/if}
        {assign var='super_layout_cookie' value=''}
        {if $super_layout}
        <tr>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
            </div>
            <div class="layout_switch" onclick="toggleViewLayouts(this)" id="customers_{$super_layout}_switch">
              {capture assign='super_layout_cookie'}customers_{$super_layout}_box{/capture}
              {capture assign='super_layout_name'}customers_{$super_layout}{/capture}
              <a name="customer_{$super_layout}_index"></a><div class="switch_{if $smarty.cookies.$super_layout_cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$smarty.config.$super_layout_name|escape}{if $super_layout eq 'contact_data' && $customer->get('main_branch_name')} - {$customer->get('main_branch_name')|escape}{/if}</div>
            </div>
          </td>
        </tr>
        {/if}
      {/if}

        {if $layout.system || array_key_exists($layout.id, $layouts_vars)}
        <tr{if $layout.visible} class="{$super_layout_class}"{/if}{if !$layout.visible || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
            </div>
            <div class="layout_switch" {if $layout.system}onclick="toggleViewLayouts(this)" id="customer_{$layout.keyword}_switch"{else}onclick="toggleLayouts(this)" id="layout_{$layout.id}_switch"{/if}>
              <a name="customer_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
            </div>
          </td>
        </tr>
        {/if}

        {if $lkey eq 'name'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {include file=`$templatesDir`_info.html assign='info'}
            <span {popup text=$info|escape caption=#system_info#|escape width=250}>{mb_truncate_overlib text=$customer->get('name')|escape|default:"&nbsp;"}</span>
          </td>
        </tr>
        {elseif $lkey eq 'type'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$customer->get('type_name')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'is_company'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $customer->get('is_company')}
              {#customers_company#|escape}
            {else}
              {#customers_person#|escape}
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'code'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$customer->get('code')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'num'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$customer->get('num')|escape|default:"&nbsp;"}
          </td>
        </tr>
        {elseif $lkey eq 'admit_VAT_credit'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">&nbsp;</td>
          <td nowrap="nowrap">
            {if $customer->get('admit_VAT_credit')}{#yes#}{else}{#no#}{/if}
          </td>
        </tr>
        {elseif $lkey eq 'department'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('department_name')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'assigned'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('assigned_to_name')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'country'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('country_name')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'city'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('city')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'postal_code'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('postal_code')|escape|default:"&nbsp;"}
          </td>
        </tr>
        {elseif $lkey eq 'address'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('address')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
          </td>
        </tr>
        {elseif $lkey eq 'notes'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td>
            {$customer->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
          </td>
        </tr>
        {elseif $lkey eq 'contacts'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td colspan="2" nowrap="nowrap" class="nopadding">
            {counter name='k' start=0 print=false}
            {foreach name='z' from=$customer->contactParameters item='contact_param'}
              {capture assign='contact_param_label'}customers_{$contact_param}{/capture}
              {capture assign='contact_param_note_var'}{$contact_param}_note{/capture}
              {assign var='contact_param_notes' value=$customer->get($contact_param_note_var)}
              {if $customer->get($contact_param)}
                {foreach name='n' from=$customer->get($contact_param) key='contact_num' item='contact'}
                  <div style="width: 10px; padding: 5px; float: left; clear: left;">{if in_array($contact_param, $required_fields) && $smarty.foreach.n.first}{#required#}{/if}</div>
                  {if $use_asterisk && ($contact_param eq 'fax' || $contact_param eq 'phone' || $contact_param eq 'gsm')}
                    {include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$contact_param number=$contact label=$smarty.config.$contact_param_label error=$messages->getErrors($contact_param_error) note=$contact_param_notes.$contact_num}
                  {else}
                    <div style="float: left; padding: 5px;">
                      {if $contact_param eq 'web'}
                        <a href="http://{$contact}" target="_blank">
                      {elseif $contact_param eq 'email'}
                        <a href="mailto:{$contact}" target="_self">
                      {/if}
                      <img src="{$theme->imagesUrl}{$contact_param}.png" alt="{$smarty.config.$contact_param_label}:" title="{$smarty.config.$contact_param_label|escape}" border="0" />&nbsp;{$contact}{if $contact_param_notes.$contact_num} - {$contact_param_notes.$contact_num}{/if}
                      {if $contact_param eq 'web' || $contact_param eq 'email'}
                        </a>
                      {/if}
                    </div>
                  {/if}
                {/foreach}
              {/if}
            {/foreach}
          </td>
        </tr>
        {elseif $lkey eq 'company_name'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td>
            {$customer->get('company_name')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'in_dds'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('in_dds')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'eik'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('eik')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'registration_file'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('registration_file')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'registration_volume'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('registration_volume')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'registration_number'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('registration_number')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'registration_address'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('registration_address')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
          </td>
        </tr>
        {elseif $lkey eq 'mol'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('mol')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'bank'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('bank')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'iban'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('iban')|escape}
          </td>
        </tr>
        {elseif $lkey eq 'bic'}
        <tr class="{$super_layout_class}" id="customer_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off' || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
          <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('bic')|escape}
          </td>
        </tr>
        {elseif array_key_exists($layout.id, $layouts_vars)}
        <!-- Customer Additional Vars -->
        {include file=`$templatesDir`_view_vars.html}
        {/if}

      {/if}
      {/foreach}
        {if $customer->get('buttons')}
          <tr>
            <td colspan="3">&nbsp;</td>
          </tr>
          <tr>
            <td colspan="3">
              {strip}
                {foreach from=$customer->get('buttons') item='button'}
                  {include file=`$theme->templatesDir`input_button.html
                          label=$button.label
                          standalone=true
                          name=$button.name
                          source=$button.source
                          disabled=$button.disabled
                          hidden=$button.hidden
                          width=$button.width
                          height=$button.height}
                {/foreach}
              {/strip}
            </td>
          </tr>
        {/if}
      </table>