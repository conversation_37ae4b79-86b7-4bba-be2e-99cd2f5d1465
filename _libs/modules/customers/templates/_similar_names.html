{if $similar_names}
  <div style="background-color:#FFFFDD; padding: 3px; border-bottom: 1px solid #666666;">
    <div style=" width: 16px; float: right;">
      <img src="{$theme->imagesUrl}delete.png" width="12" height="12" border="0" alt="{#message_customers_similar_names#}" onclick="$('similar_names_panel').parentNode.style.display='none'" />
    </div>
    <div>
      <span style="color:red">
        <img src="{$theme->imagesUrl}warning.png" width="12" height="12" border="0" alt="{#message_customers_similar_names#}" />
        {#message_customers_similar_names#}:
      </span>
      {foreach from=$similar_names item='item' name='cstm'}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$item.id}&amp;lang={$item.lang}" target="_blank">{$item.check|escape}</a>{if !$smarty.foreach.cstm.last}, {/if}
        {*{$item.lang} {$item.lev}*}
      {/foreach}
      <br />
      <span>
        <img src="{$theme->imagesUrl}info.png" width="12" height="12" border="0" alt="{#message_customers_similar_names#}" />
        {#message_customers_confirm_add#}
      </span>
       <button type="button" class="button" name="add_" id="add_" title="" onclick="$(check_similar_names).value=0; this.form.submit();" style="font-size: 9px;">{#add#|escape}</button>
    </div>
  </div>
{/if}