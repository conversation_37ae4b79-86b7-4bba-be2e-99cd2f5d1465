<?php

class Customers_Trademarks_Viewer extends Viewer {
    public $template = 'trademarks.html';

    public function prepare() {
        $this->model = $this->registry['customer'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.types.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'sanitize' => true,
                         'where' => array('nt.active = 1',
                                          'nt.keyword = "' . PH_NOMENCLATURES_TYPE_KEYWORD_TRADEMARK . '"'));
        $nomenclatures_type = Nomenclatures_Types::searchOne($this->registry, $filters);
        $type_id = $nomenclatures_type ? $nomenclatures_type->get('id') : '0';

        // parameters for autocompleter for nomenclature of "Trademark" type
        $trademarks_autocomplete = array(
            'type'              => 'nomenclatures',
            'url'               => sprintf('%s?%s=%s&%s=%s',
                                           $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                          'nomenclatures', 'nomenclatures', 'ajax_select'),
            'filters'           => array(
                '<type>' => $type_id
            ),
            'buttons_hide'      => 'search',
            'execute_after'     => 'updateTrademarks',
            'id_var'            => 'nomenclature',
        );

        if ($this->theme->isModern()) {
            // configure AK to use the standart search butten
            $trademarks_autocomplete['table'] = 'nomenclatures_subpanel';
            $trademarks_autocomplete['select_multiple'] = 1;
            unset($trademarks_autocomplete['buttons_hide']);
        }

        //set "trademark" type parameter for addquick action of autocompleter
        if ($nomenclatures_type && $this->registry['currentUser']->checkRights('nomenclatures' . $type_id, 'add')) {
            if ($this->theme->isModern()) {
                // add the search button
                $trademarks_autocomplete['buttons'] = 'add, search';
            } else {
                $trademarks_autocomplete['buttons'] = 'add';
            }
            $trademarks_autocomplete['addquick_type'] = array($type_id);
        }

        $this->data['trademarks_autocomplete'] = $trademarks_autocomplete;
        $this->data['type_id'] = $type_id;

        // don't display general messages in the trademarks panel
        $messages = clone $this->registry['messages'];
        $this->registry->set('messages', new Messages($this->registry), true);

        $session_param_prefix = strtolower($this->model->modelName) . $this->model->get('id') . '_ajax_trademark_';
        $session_param = $session_param_prefix . 'nomenclature';

        //get requested models for the specified record
        //prepare parameters needed for the viewer constructor
        $this->registry->set('module', 'nomenclatures', true);
        $this->registry->set('controller', 'nomenclatures', true);
        $this->registry->set('action_param', 'nomenclatures', true);
        $this->registry->set('action', 'subpanel', true);

        //prepare filters
        if (!$this->registry['request']->isPost()) {
            $custom_filters = array(
                'where' => array(
                    'n.id IN (' . implode(', ', array_map(function($a) { return $a['id']; }, ($this->model->get('trademarks') ?: array(array('id' => 0))))) . ')',
                    'n.type = \'' . $type_id . '\''
                )
            );
            //save filters in the session param prepared above
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            Nomenclatures::saveSearchParams($this->registry, $custom_filters, $session_param_prefix);
        }

        $this->registry['request']->set('session_param', $session_param, 'all', true);
        $this->registry['request']->set('source', 'ajax', 'all', true);
        //include viewer file
        include_once PH_MODULES_DIR . 'nomenclatures/viewers/nomenclatures.subpanel.viewer.php';

        $viewer = new Nomenclatures_Subpanel_Viewer($this->registry);
        $viewer->prepare();
        $this->data['trademarks_subpanel'] = $viewer->fetch();
        $this->data['tradermarks_session_param'] = $session_param;

        $this->registry['request']->remove('session_param');
        $this->registry['request']->remove('source');
        $this->registry->set('module', $this->module, true);
        $this->registry->set('controller', $this->controller, true);
        $this->registry->set('action', $this->action, true);
        $this->registry->set('action_param', $this->controller, true);

        if ($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
        // restore general messages
        $this->registry->set('messages', $messages, true);

        $this->prepareTranslations();
        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('customers_trademarks_title'), $this->i18n('trademarks'), $this->model->getModelTypeName());
        }
        $this->data['title'] = $title;
    }
}

?>
