<?php

class Customers_Contactpersons_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Customers_Contactperson';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Customers_Contactpersons';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'add', 'edit', 'delete'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'add', 'edit', 'translate', 'delete'
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        // id of (normal) customer
        $this->registry->set('parent_customer_id', $this->registry['request']->get('parent_customer_id'), true);

        switch ($this->action) {
        case 'add':
        case 'ajax_add':
            $this->_add();
            break;
        case 'edit':
        case 'ajax_edit':
            $this->_edit();
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'search':
            $this->_search();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer
        $this->setListingViewer();
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {
        //check if details are submitted via POST
        if ($this->registry['request']->isPost()) {
            //build the model from the POST
            $contact_person = Customers_Contactpersons::buildModel($this->registry);

            if ($contact_person->save()) {
                // write history for the customer
                require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                $old_contact_person = new Customers_Contactperson($this->registry);
                $old_contact_person->sanitize();

                $filters = array('where' => array('c.id = ' . $contact_person->get('id'),
                                                  'c.subtype = \'contact\''),
                                 'model_lang' => $this->registry['request']->get('model_lang'),
                                 'sanitize' => true);
                $new_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

                $filters_customer =  array('where'      => array('c.id = ' . $this->registry['request']->get('parent_customer_id')),
                                           'model_lang' => $this->registry['request']->get('model_lang'),
                                           'sanitize'   => true);
                $parent_customer = Customers::searchOne($this->registry, $filters_customer);

                Customers_History::saveData($this->registry, array('model'               => $parent_customer,
                                                                   'new_model'           => $new_contact_person,
                                                                   'old_model'           => $old_contact_person,
                                                                   'action_type'         => 'add_contact_person',
                                                                   'contact_person_name' => trim($new_contact_person->get('name') . ' ' . $new_contact_person->get('lastname'))));

                $this->registry['messages']->setMessage($this->i18n('message_customers_contact_add_success'), '', -1);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
                if ($this->action == 'ajax_add') {
                    $v = new Viewer($this->registry);
                    $v->setFrameset('message.html');
                    $v->data['display'] = 'message';
                    $v->data['items'] = $this->registry['messages']->getMessages();
                    echo ('var result = ' . json_encode(array('messages' => $v->fetch())) . ';');
                    exit;
                }
                $this->setListingViewer();
            } else {
                $this->registry['messages']->setError($this->i18n('error_customers_contact_add_failed'), '', -2);
                if ($this->action != 'ajax_add') {
                    $this->registry->set('failed_contact_person', $contact_person);
                    $this->setListingViewer();
                }
            }
        } else {
            //create empty contact person model
            $contact_person = Customers_Contactpersons::buildModel($this->registry);
        }

        if (!empty($contact_person)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('contact_person', $contact_person->sanitize());
        }
        if ($this->action == 'ajax_add') {
            if ($this->registry['messages']->getErrors()) {
                $v = new Viewer($this->registry);
                $v->setFrameset('message.html');
                $v->data['display'] = 'error';
                $v->data['items'] = $this->registry['messages']->getErrors();
                echo ('var result = ' . json_encode(array('errors' => $v->fetch())) . ';');
                exit;
            }
            require_once PH_MODULES_DIR . 'customers/viewers/customers.contactpersons.add.viewer.php';
            $this->viewer = new Customers_Contactpersons_Add_Viewer($this->registry);
            $this->viewer->setFrameset('frameset_blank.html');
        }

        return true;
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $contact_person = Customers_Contactpersons::buildModel($this->registry);
            $contact_person->set('id', $id, true);

            $filters = array('where' => array('c.id = \'' . $id . '\'',
                                              'c.subtype = \'contact\''),
                             'model_lang' => $this->registry['request']->get('model_lang'),
                             'sanitize' => true);
            $old_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

            if ($contact_person->save()) {
                // write history for the customer
                require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                $filters = array('where' => array('c.id = \'' . $contact_person->get('id') . '\'',
                                                  'c.subtype = \'contact\''),
                                 'model_lang' => $this->registry['request']->get('model_lang'),
                                 'sanitize' => true,
                                 'skip_permissions' => true);
                $new_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

                $filters_customer =  array('where'      => array('c.id = ' . $this->registry['request']->get('parent_customer_id')),
                                           'model_lang' => $this->registry['request']->get('model_lang'),
                                           'sanitize'   => true);
                $parent_customer = Customers::searchOne($this->registry, $filters_customer);

                Customers_History::saveData($this->registry, array('model'               => $parent_customer,
                                                                   'new_model'           => $new_contact_person,
                                                                   'old_model'           => $old_contact_person,
                                                                   'action_type'         => 'edit_contact_person',
                                                                   'contact_person_name' => trim($new_contact_person->get('name') . ' ' . $new_contact_person->get('lastname'))));

                // if updating emails targetlists is enabled
                if ($this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
                    Customers::updateEmailsTargetlists($this->registry, $new_contact_person, $old_contact_person);
                }

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_customers_contact_edit_success'), '', -1);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
                if ($this->action == 'ajax_edit') {
                    $v = new Viewer($this->registry);
                    $v->setFrameset('message.html');
                    $v->data['display'] = 'message';
                    $v->data['items'] = $this->registry['messages']->getMessages();
                    echo ('var result = ' . json_encode(array('messages' => $v->fetch())) . ';');
                    exit;
                }
                $this->setListingViewer();
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_customers_contact_edit_failed'), '', -2);
                if ($this->action != 'ajax_edit') {
                    //register the model, with all the posted details
                    $this->registry->set('failed_contact_person', $contact_person);
                    $this->setListingViewer();
                }
            }

        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('c.id = ' . $id,
                                      'c.subtype = \'contact\'');
            $filters['model_lang'] = $request->get('model_lang');
            $contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);
            $contact_person->set('parent_customer_id', $request->get('parent_customer_id'), true);
        }

        if (!empty($contact_person)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('contact_person')) {
                $this->registry->set('contact_person', $contact_person->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_customers_contact'));

            //there is no such model, display error message
            if ($this->action != 'ajax_edit') {
                $v = new Viewer($this->registry);
                $v->setFrameset('message.html');
                $v->data['display'] = 'error';
                $v->data['items'] = $this->registry['messages']->getErrors();
                echo $v->fetch();
                exit;
            }
        }
        if ($this->action == 'ajax_edit') {
            if ($this->registry['messages']->getErrors()) {
                $v = new Viewer($this->registry);
                $v->setFrameset('message.html');
                $v->data['display'] = 'error';
                $v->data['items'] = $this->registry['messages']->getErrors();
                echo ('var result = ' . json_encode(array('errors' => $v->fetch())) . ';');
                exit;
            }
            require_once PH_MODULES_DIR . 'customers/viewers/customers.contactpersons.edit.viewer.php';
            $this->viewer = new Customers_Contactpersons_Edit_Viewer($this->registry);
            $this->viewer->setFrameset('frameset_blank.html');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $contact_person = Customers_Contactpersons::buildModel($this->registry);
            $contact_person->set('id', $id, true);

            $filters = array('where' => array('c.id = ' . $id,
                                              'c.subtype = \'contact\''),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $old_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

            if ($contact_person->save()) {
                // write history for the customer
                require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                $filters = array('where' => array('c.id = ' . $contact_person->get('id'),
                                                  'c.subtype = \'contact\''),
                                 'model_lang' => $this->registry['request']->get('model_lang'),
                                 'sanitize' => true);
                $new_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

                $filters_customer =  array('where'      => array('c.id = ' . $this->registry['request']->get('parent_customer_id')),
                                           'model_lang' => $this->registry['request']->get('model_lang'),
                                           'sanitize'   => true);
                $parent_customer = Customers::searchOne($this->registry, $filters_customer);

                Customers_History::saveData($this->registry, array('model'               => $parent_customer,
                                                                   'new_model'           => $new_contact_person,
                                                                   'old_model'           => $old_contact_person,
                                                                   'action_type'         => 'translate_contact_person',
                                                                   'contact_person_name' => trim($new_contact_person->get('name') . ' ' . $new_contact_person->get('lastname'))));

                // if updating emails targetlists is enabled
                if ($this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
                    Customers::updateEmailsTargetlists($this->registry, $new_contact_person, $old_contact_person);
                }

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_customers_contact_translate_success'), '', -1);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                $this->setListingViewer();
            } else {
                //register the model, with all the posted details
                $this->registry['messages']->setError($this->i18n('error_customers_contact_translate_failed'), '', -2);
                $this->registry->set('failed_contact_person', $contact_person);
                $this->setListingViewer();
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters['where'] = array('c.id = ' . $id,
                                      'c.subtype = \'contact\'');
            $filters['model_lang'] = $request->get('model_lang');
            $contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);
            $contact_person->set('parent_customer_id', $request->get('parent_customer_id'), true);
        }

        if (!empty($contact_person)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('contact_person', $contact_person->sanitize());
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_customers_contact'));

            //there is no such model, display error message
            $v = new Viewer($this->registry);
            $v->setFrameset('message.html');
            $v->data['display'] = 'error';
            $v->data['items'] = $this->registry['messages']->getErrors();
            echo $v->fetch();
            exit;
        }

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        } elseif (!is_array($ids)) {
            //convert $ids to array
            if (preg_match('#,#', $ids)) {
                $ids = preg_split('#\s*,\s*#', $ids);
            } else {
                $ids = array($ids);
            }
        }

        //delete customer's contact person
        $result = Customers_Contactpersons::delete($this->registry, $ids);

        if ($result) {
            if ($result != count($ids)) {
                //set warning
                $this->registry['messages']->setWarning($this->i18n('warning_delete_notallowed'), '', -1);
            }

            if (count($ids)) {
                $filters = array('where' => array('c.id IN (' . implode(',', $ids) . ')',
                                                  'c.subtype = \'contact\'',
                                                  'c.deleted_by!=0'),
                                 'model_lang' => $this->registry['request']->get('model_lang'),
                                 'sanitize' => true);
                $deleted_contact_persons = Customers_Contactpersons::search($this->registry, $filters);
                $deleted_contact_persons_names = array();

                foreach ($deleted_contact_persons as $del_cont_pers) {
                    $deleted_contact_persons_names[] = trim($del_cont_pers->get('name') . ' ' . $del_cont_pers->get('lastname'));
                }

                if ($this->registry['request']->get('parent_customer_id')) {
                    // write history for the customer
                    require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                    $filters_customer =  array('where'      => array('c.id = ' . $this->registry['request']->get('parent_customer_id')),
                                               'model_lang' => $this->registry['request']->get('model_lang'),
                                               'sanitize'   => true);
                    $parent_customer = Customers::searchOne($this->registry, $filters_customer);

                    Customers_History::saveData($this->registry, array('model' => $parent_customer, 'action_type' => 'delete_contact_person', 'deleted_contact_persons' => $deleted_contact_persons_names));
                }

                // if updating emails targetlists is enabled
                if (!empty($deleted_contact_persons) &&
                $this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
                    Customers::deleteEmailsFromTargetlists($this->registry, $deleted_contact_persons);
                }
            }

            //delete successful
            $this->registry['messages']->setMessage($this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setWarning( $this->i18n('error_items_not_deleted'), '', -1);
        }

        //set action as completed
        $this->actionCompleted = true;

        $this->setListingViewer();
    }

    /**
     * Activates or deactivates the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access to all selected items
        $access = $this->checkAccessOwnershipMultiple($ids, $this->action, $this->modelFactoryName, false);

        if ($access) {
            //activate/deactivate
            $result = Customers_Contactpersons::changeStatus($this->registry, $ids, $status);

            if ($result) {
                if ($result != count($ids)) {
                    //set warning
                    $text = ($this->action == 'activate') ?
                              $this->i18n('warning_activate_notallowed') :
                              $this->i18n('warning_deactivate_notallowed');
                    $this->registry['messages']->setWarning($text, '', -1);
                }

                if (count($ids)) {
                    $filters = array('where' => array('c.id IN (' . implode(',', $ids) . ')',
                                                      'c.subtype = \'contact\'',
                                                      'c.active = ' . ($this->action == 'activate' ? 1 : 0)),
                                     'model_lang' => $this->registry['request']->get('model_lang'),
                                     'sanitize' => true);
                    $changed_contact_persons = Customers_Contactpersons::search($this->registry, $filters);
                    $changed_contact_persons_names = array();

                    foreach ($changed_contact_persons as $chng_cont_per) {
                        $changed_contact_persons_names[] = trim($chng_cont_per->get('name') . ' ' . $chng_cont_per->get('lastname'));
                    }

                    if ($this->registry['request']->get('parent_customer_id')) {
                        // write history for the customer
                        require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                        require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                        $filters_customer =  array('where'      => array('c.id = ' . $this->registry['request']->get('parent_customer_id')),
                                                   'model_lang' => $this->registry['request']->get('model_lang'),
                                                   'sanitize'   => true);
                        $parent_customer = Customers::searchOne($this->registry, $filters_customer);

                        Customers_History::saveData($this->registry, array('model' => $parent_customer,
                                                                           'action_type' => $this->action . '_contact_person',
                                                                           'changed_contact_persons' => $changed_contact_persons_names));
                    }

                    // if updating emails targetlists is enabled
                    if ($this->action == 'deactivate' && !empty($changed_contact_persons) &&
                    $this->registry['config']->getParam('customers', 'update_emails_targetlists')) {
                        Customers::deleteEmailsFromTargetlists($this->registry, $changed_contact_persons);
                    }

                }

                //change status successful
                $text = ($this->action == 'activate') ?
                          $this->i18n('message_items_activated') :
                          $this->i18n('message_items_deactivated');
                $this->registry['messages']->setMessage($text);
            } else {
                //change status failed
                $text = ($this->action == 'activate') ?
                          $this->i18n('error_items_not_activated') :
                          $this->i18n('error_items_not_deactivated');
                $this->registry['messages']->setWarning($text, '', -1);
            }

            //set action as completed
            $this->actionCompleted = true;
        } else {
            $this->registry['messages']->setWarning($this->i18n('error_no_access_to_models'));
        }

        $this->setListingViewer();
    }

    /**
     * Changes The Viewer for listing
     */
    private function setListingViewer() {
        // change controller and action as if user enters 'contactpersons' action of 'customers' module
        $this->registry->set('controller', 'customers', true);
        $this->registry->set('action', 'contactpersons', true);

        require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
        $filters = array('where' => array('c.id = \'' . $this->registry->get('parent_customer_id') . '\''),
                         'model_lang' => $this->registry['request']->get('model_lang'),
                         'sanitize' => true);
        $customer = Customers::searchOne($this->registry, $filters);
        if (!$customer) {
            $customer = new Customer($this->registry);
            $customer->sanitize();
        }

        $this->registry->set('customer', $customer, true);

        if ($this->actionCompleted == 1) {
            $this->registry['session']->remove($this->registry['request']->get('session_param'), 'selected_items');
        }

        require_once ($this->viewersDir . 'customers.contactpersons.viewer.php');
        $this->viewer = new Customers_Contactpersons_Viewer($this->registry);
        if ($this->viewer->theme->isModern()) {
            $this->viewer->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
        $this->viewer->setFrameset($this->viewer->templatesDir . '_contact_persons_panel.html');

        if ($this->registry['failed_contact_person']) {
            if (!$this->registry['failed_contact_person']->isSanitized()) {
                $this->registry['failed_contact_person']->sanitize();
            }
            $this->viewer->data['failed_contact_person'] = $this->registry['failed_contact_person'];
            $this->viewer->data['failed_action'] = $this->action;
            if ($this->action == 'translate') {
                $model_translations = $this->viewer->data['failed_contact_person']->getTranslations();
                //prepare the basic language model
                //basic model lang is the first language the model has been translated to
                $filters = array (
                    'where' => array(
                        'c.id = ' . $this->viewer->data['failed_contact_person']->get('id'),
                        'c.subtype = \'contact\''),
                    'model_lang' => $model_translations[0],
                    'sanitize' => true
                );
                $this->viewer->data['base_model'] = Customers_Contactpersons::searchOne($this->registry, $filters);
            }

            $this->viewer->data['salutations'] = Dropdown::getSalutations(array($this->registry));

            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $filters = array(
                'where' => array('u.is_portal = 0'),
                'model_lang' => $customer->get('model_lang'),
                'sanitize' => true
            );
            $this->viewer->data['assign_users'] = Users::search($this->registry, $filters);

            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branches = array(
                'where' => array('c.parent_customer = \'' . $customer->get('id') . '\'',
                                 'c.subtype = \'branch\''),
                'sort' => array('c.is_main DESC', 'ci18n.name ASC', 'c.id DESC'),
                'model_lang' => $customer->get('model_lang'),
                'sanitize' => true
            );
            $this->viewer->data['parent_branches'] = Customers_Branches::search($this->registry, $filters_branches);

            $branch_main_contacts = array();
            foreach ($this->viewer->data['parent_branches'] as $parent_branch) {
                $branch_main_contacts[$parent_branch->get('id')] = $parent_branch->get('contact_person_id');
            }
            $this->viewer->data['branch_main_contacts'] = json_encode($branch_main_contacts);
        }

        $this->viewer->prepare();
        $this->viewer->display();
        exit;
    }
}

?>
