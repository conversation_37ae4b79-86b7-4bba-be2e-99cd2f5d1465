<?php

class Tags_Rights_Viewer extends Viewer {
    public $template = 'rights.html';

    public function prepare() {
        //prepare table variables
        $table_users = array(
            'type'   => 'grouping',
            'names'  => array('tag_id', 'allow', 'users', 'groups'),
            'labels' => array($this->i18n('tags_tag'), $this->i18n('tags_permission'), $this->i18n('menu_users'), $this->i18n('menu_groups')),
            'widths' => array('200', '100', '220', '200'),
            'types'  => array('dropdown', 'dropdown', 'checkbox_group', 'checkbox_group'),
            'required' => array(true, true, false, false),
            'dont_copy_values' => true,
            'floating_buttons' => true
        );
        //get users
        $user_options = Dropdown::getUsers(array($this->registry));
        if (!empty($user_options['contain_optgroups'])) {
            $tmp_arr = array();
            $normal_users_label = $this->i18n('normal_users');
            $portal_users_label = $this->i18n('portal_users');
            if (!empty($user_options[$normal_users_label])) {
                $tmp_arr = array_merge($tmp_arr, $user_options[$normal_users_label]);
            }
            if (!empty($user_options[$portal_users_label])) {
                $tmp_arr = array_merge($tmp_arr, $user_options[$portal_users_label]);
            }
            $user_options = $tmp_arr;
            unset($tmp_arr);
        }
        $user_options = array_map(function($a) {
            return $a + array('class_name' => ($a['is_portal'] ? 'user_portal' : 'user_normal'));
        }, $user_options);
        //get groups
        $group_options = Dropdown::getGroups(array($this->registry));

        //set options
        $table_users['allow']['options'] = array(array('label'=> $this->i18n('tags_not_allowed'),
                                                       'option_value' => '0'),
                                                 array('label'=> $this->i18n('tags_allowed'),
                                                       'option_value' => '1'));
        $table_users['allow']['options']['skip_please_select'] = 1;
        $table_users['users']['options'] = $user_options;
        $table_users['groups']['options'] = $group_options;

        $table_users['users']['do_not_show_check_all_button'] = 1;
        $table_users['users']['do_not_show_check_none_button'] = 1;
        $table_users['groups']['do_not_show_check_all_button'] = 1;
        $table_users['groups']['do_not_show_check_none_button'] = 1;

        $params = array(
            0 => $this->registry,
            'table' => 'DB_TABLE_TAGS',
            'table_i18n' => 'DB_TABLE_TAGS_I18N',
            'label' => 'name',
            'value' => 'id, \'^\', model',
            'order_by' => 'model ASC'
        );
        $tags_data = Dropdown::getCustomDropdown($params);
        $tags_options = array();
        foreach ($tags_data as $opt) {
            list($opt['option_value'], $module_controller) = explode('^', $opt['option_value']);
            $label_module_controller = $this->i18n('tags_model_' . $module_controller);
            if (!isset($tags_options[$label_module_controller])) {
                $tags_options[$label_module_controller] = array();
            }
            $tags_options[$label_module_controller][] = $opt;
        }
        ksort($tags_options);
        $table_users['tag_id']['optgroups'] = $tags_options;

        //get settings from DB
        $rows = Tags::getPermissions($this->registry);
        if ($rows) {
            $i = 0;
            //prepare status options and table values from DB data
            foreach ($rows as $row) {
                if (!empty($row['users'])) {
                    $u_ids = preg_split('#\s*,\s*#', $row['users']);
                } else {
                    $u_ids = array();
                }
                if (!empty($row['groups'])) {
                    $g_ids = preg_split('#\s*,\s*#', $row['groups']);
                } else {
                    $g_ids = array();
                }
                $table_users['values'][$i] = array($row['tag_id'], $row['allow'], $u_ids, $g_ids);
                $i++;
            }
        }

        $table_users['last_visible_column'] = count($table_users['names']) - 1;
        $this->data['table_users'] = $table_users;

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('tags_rights');
        $this->data['title'] = $title;
    }
}

?>
