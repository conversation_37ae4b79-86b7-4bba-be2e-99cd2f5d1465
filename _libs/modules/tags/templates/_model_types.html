{if $mode == 'view'}
  <table cellspacing="0" cellpadding="0" border="0" width="100%" class="t_list">
    <tr>
      {capture assign="c0"}{if $tag->get('model_types')|@count gt 6}2{else}1{/if}{/capture}
      {capture assign='mt_label_length'}{if $c0 eq 2}40{else}80{/if}{/capture}
      <td class="nopadding"{if $c0 eq 2} style="width: 50%;"{/if}>
      {counter name='t' start=0 print=false}
      {foreach name='i' from=$available_model_types item='mt' name='key'}
        {if is_array($tag->get('model_types')) && in_array($mt.option_value, $tag->get('model_types'))}
          {counter name='t' assign='c' print=true}. {mb_truncate_overlib length=$mt_label_length break_words=true text=$mt.label|escape}
          {if !($c % $c0)}
            {math equation="x+y" x=$c y=$c0 assign="c"}
            </td></tr>
            <tr><td class="nopadding"{if $c0 eq 2} style="width: 50%;"{/if}>
          {else}
            </td><td class="nopadding"{if $c0 eq 2} style="width: 50%;"{/if}>
          {/if}
        {/if}
      {/foreach}
      </td>
    </tr>
  </table>
{else}
  {if $available_model_types}
    {if $available_model_types|@count gt 6}
      <div style="width: 300px; display: block; color: #666666; padding: 5px 5px 2px;">
        <span onclick="toggleCheckboxes(this, 'model_types', true);" class="pointer">{#check_all#|escape}</span> |
        <span onclick="toggleCheckboxes(this, 'model_types', false);" class="pointer">{#check_none#|escape}</span>
      </div>
    {/if}
    <table cellspacing="0" cellpadding="0" border="0" width="100%" class="t_list">
      <tr>
        {capture assign="c"}{if $available_model_types|@count gt 6}2{else}1{/if}{/capture}
        {capture assign='mt_label_length'}{if $c eq 2}40{else}80{/if}{/capture}
        <td class="nopadding"{if $c eq 2} style="width: 50%;"{/if}>
        {foreach name='i' from=$available_model_types item='mt' name='key'}
          <input type="checkbox" name="model_types[]" id="model_type_{$mt.option_value}" value="{$mt.option_value}" title="{$mt.label|escape}"{if is_array($tag->get('model_types')) && in_array($mt.option_value, $tag->get('model_types'))} checked="checked"{/if} />
          <label for="model_type_{$mt.option_value}"><span>{mb_truncate_overlib length=$mt_label_length break_words=true text=$mt.label|escape}</span></label>
          {if !($smarty.foreach.key.iteration % $c) && ($available_model_types|@count - $smarty.foreach.key.iteration) > 0}
            </td></tr>
            <tr><td class="nopadding"{if $c eq 2} style="width: 50%;"{/if}>
          {elseif $smarty.foreach.key.last ne 1}
            </td><td class="nopadding"{if $c eq 2} style="width: 50%;"{/if}>
          {/if}
        {/foreach}
        </td>
      </tr>
    </table>
  {/if}
{/if}