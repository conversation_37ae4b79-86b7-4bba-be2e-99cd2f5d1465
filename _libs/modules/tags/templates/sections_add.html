<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}

<form name="tags_sections" id="tags_sections" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$tags_section->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table"{if $tags_optgroups|@count gt 7} style="width: {math equation='800+80*(x-7)' x=$tags_optgroups|@count}px;"{/if}>
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='sections_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$tags_section->get('name')|escape}" title="{#tags_sections_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_tag_id"><label{if $messages->getErrors('tag_id')} class="error"{/if}>{help label='sections_tags'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {include file=`$theme->templatesDir`input_checkbox_group.html
                     name='tag_id'
                     value=$tags_section->get('tag_id')
                     standalone=true
                     optgroups=$tags_optgroups
                     selected_tab=$tags_optgroups_selected_tab
            }
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_tag_limit"><label for="tag_limit"{if $messages->getErrors('tag_limit')} class="error"{/if}>{help label='sections_tag_limit'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox num hright" name="tag_limit" id="tag_limit" value="{$tags_section->get('tag_limit')|escape}" title="{#tags_sections_tag_limit#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_place"><label for="place"{if $messages->getErrors('place')} class="error"{/if}>{help label='sections_place'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox num hright" name="place" id="place" value="{$tags_section->get('place')|escape}" title="{#tags_sections_place#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='sections_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#tags_sections_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$tags_section->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$tags_section}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
