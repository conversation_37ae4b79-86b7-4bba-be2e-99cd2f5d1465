function clearGetAllCheck() {
    let getAll_check = document.querySelector('#getAll');
    if (getAll_check && getAll_check.checked) {
        getAll_check.checked = false;
    }
}

/**
 * Clears autocompleter fields and puts the names of the assignees into the below list
 *
 * @param Object autocomplete - autocompleter params
 * @param Object data - data for selected option
 */
function completeSelectedAssignment(autocomplete, data) {
    for (const i in data) {
        // iterate through the properties and clear fields
        if (!/^\$/.test(i)) {
            continue;
        }
        const name = i.replace(/\$/, '');
        if ($(name) != null) {
            $(name).value = '';
        }
    }
    removeClass($(autocomplete.field), 'working');

    const id_var = autocomplete.field.replace(/^(.+)_autocomplete$/, '$$$1');
    const assignee_hidden_value = data[id_var].replace(/^(user|department)s/, '$1');
    if (!$('assignments_' + assignee_hidden_value)) {
        let cell_assignments = document.querySelector('#filter_assignments_list');
        clearGetAllCheck();
        let assignee_name = data['$' + autocomplete.field];
        if (assignee_name.match(/\s*\(\)$/)) {
            assignee_name = assignee_name.replace(/\s*\(\)$/, '');
        }
        if (data[id_var].match(/^departments/)) {
            assignee_name = '<strong>' + assignee_name + '</strong>';
        }
        let item = document.createElement('li');
        item.id = 'assignments_' + assignee_hidden_value;
        item.classList.add('nz-calendar-filter-item')
        item.innerHTML= `<i class="nz-icon-button action-remove" title="${i18n['labels']['delete']}">delete</i>`
                    + `<input type="hidden" name="assignments_filters[]" value="${assignee_hidden_value}" />`
                    + assignee_name;
        cell_assignments.append(item);
    }
}

function selectAllEventTypes() {
    const eventTypeWrapper = document.querySelector('.nz-calendar-filter-event-types');
    eventTypeWrapper.removeEventListener('change', clearGetAllCheck);

    const eventTypeList = eventTypeWrapper.querySelectorAll('input');
    eventTypeList.forEach((el, index) => {
        el.checked = true;
    });

    eventTypeWrapper.addEventListener('change', clearGetAllCheck);
}

/**
 * Function to delete assignee from the list of the assignees
 *
 * @param element - trigger element
 */
function removeFiltersAssignments(element) {
    const itemEl = element.closest('.nz-calendar-filter-item');
    itemEl.parentNode.removeChild(itemEl);
}

/*
 * Clears filter assignments if 'check all' option is selected
 *
 * @param params - trigger element
 */
function clearFilterAssignments(element) {
    document.querySelector('#filter_assignments_list').innerHTML = '';
}

nz_ready().then(()=> {
    const filterList = document.querySelector('#filter_assignments_list');
    if (! filterList) {
        return;
    }

    filterList.addEventListener('click', (e) => {
        if (e.target.closest('.action-remove')) {
            removeFiltersAssignments(e.target);
        }
        if (e.target.closest('#getAll')) {
            clearFilterAssignments(e.target);
        }
    });
    const getAll_check = document.querySelector('#getAll');
    getAll_check.addEventListener('change', (e) => {
        if (e.target.checked === true) {
            clearFilterAssignments()
            selectAllEventTypes();
        }
    });
    const eventTypeWrapper = document.querySelector('.nz-calendar-filter-event-types');
    eventTypeWrapper.addEventListener('change', clearGetAllCheck);

    //select all/deselect all
    document.querySelectorAll('.nz-calendar-filter-event-types span.pointer').forEach( span => {
        span.addEventListener('click', clearGetAllCheck);
    });
});
