{strip}
{math equation="x+2" x=$week_days_number assign='table_cols'}
{capture assign='current_day_of_week'}{if $monday_start && $calendar->day_of_week == 0}7{else}{$calendar->day_of_week}{/if}{/capture}
{assign var='week_num' value=$calendar->day->week}
{assign var='all_days_count' value=$all_day_events|@count}
{assign var='day' value=$calendar->day}

{*Events Positioning Settings*}
{assign var='event_cell_height' value=15}
{assign var='event_allday_cell_height' value=20}
{assign var='event_cell_width' value=850}
{assign var='event_overlapping' value=10}
{assign var='event_area_offset_left' value=36}
{assign var='event_area_border_width' value=1}
{/strip}
<h1>{$title}</h1>
<div id="form_container" style="width: {math equation='x+y' x=$event_cell_width y=$event_area_offset_left}px; float: left;">

{include file=`$theme->templatesDir`actions_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table" id="calendar_container">
  <tr>
    <td id="cal_nav_container">
      <div class="t_footer" id="cal_nav_switch" title="{#cal_nav_show#|escape}"><div class="{if $smarty.cookies.cal_nav_box eq 'off'}switch_up{else}switch_down{/if}"></div></div>
      <div id="cal_nav_details"{if $smarty.cookies.cal_nav_box eq 'off'} style="display: none;"{/if}>
        <table cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td class="t_caption3 t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=day&amp;date={$calendar->offsetDay(-1)}" class="strong" title="{#calendars_previous_day#}">&nbsp;&laquo;&nbsp;</a>
            </td>
            {if !$monday_start && $week_days_number gt 6}
              {math equation="-x" x=$calendar->day_of_week assign='offset_date'}
              <td class="t_caption2{if $calendar->day_of_week eq 0} t_caption4{/if} t_border hcenter strong" width="{math equation='100/x' x=$week_days_number format='%d'}%" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=day&amp;date={$calendar->offsetDay($offset_date)}">{$smarty.config.weekday_0} ({$calendar->offsetDay($offset_date)|date_format:#date_calendar_day_mini#|trim})</a></td>
            {/if}

            {section name=weekdays loop=6 start=1}
              {capture assign='week_label'}weekday_{$smarty.section.weekdays.index}{/capture}
              {math equation="y-x" x=$current_day_of_week y=$smarty.section.weekdays.index assign='offset_date'}
              {assign var='week_day_index' value=$smarty.section.weekdays.index-1}
              <td class="t_caption2{if $calendar->day_of_week eq $smarty.section.weekdays.index} t_caption4{/if} t_border hcenter strong" width="{math equation='100/x' x=$week_days_number format='%d'}%" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=day&amp;date={$calendar->offsetDay($offset_date)}">{$smarty.config.$week_label} ({$calendar->offsetDay($offset_date)|date_format:#date_calendar_day_mini#|trim})</a></td>
            {/section}

            {if $week_days_number gt 5}
              {math equation="6-x" x=$current_day_of_week assign='offset_date'}
              <td class="t_caption2{if $calendar->day_of_week eq 6} t_caption4{/if} t_border hcenter strong" width="{math equation='100/x' x=$week_days_number format='%d'}%" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=day&amp;date={$calendar->offsetDay($offset_date)}">{$smarty.config.weekday_6} ({$calendar->offsetDay($offset_date)|date_format:#date_calendar_day_mini#|trim})</a></td>
            {/if}
            {if $monday_start && $week_days_number gt 6}
              {math equation="7-x" x=$current_day_of_week assign='offset_date'}
              <td class="t_caption2{if $calendar->day_of_week eq 0} t_caption4{/if} t_border hcenter strong" width="{math equation='100/x' x=$week_days_number format='%d'}%" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=day&amp;date={$calendar->offsetDay($offset_date)}">{$smarty.config.weekday_0} ({$calendar->offsetDay($offset_date)|date_format:#date_calendar_day_mini#|trim})</a></td>
            {/if}
            <td class="t_caption3">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=day&amp;date={$calendar->offsetDay(1)}" class="strong" title="{#calendars_next_day#}">&nbsp;&raquo;&nbsp;</a>
            </td>
          </tr>
        </table>
      </div>
    </td>
  </tr>
  <tr>
    <td class="cal_title_bar t_caption2 hcenter"><div class="t_caption2_title">{$calendar->dateISO|date_format:#date_calendar_day#}</div></td>
  </tr>
  <tr>
    <td style="width: {$event_cell_width}px;" class="cal_day_day">
      <div style="position: relative;">
        {*Events for the Current Day*}
        {*foreach from=$events item='event'*}
          {include file=`$templatesDir`_events_day.html}
        {*/foreach*}

        <table width="100%" cellpadding="0" cellspacing="0">
        {*All Day Events*}
        {foreach from=$all_day_events item='event'}
          <tr>
            <td>&nbsp;</td>
            <td colspan="1" style="height: {$event_allday_cell_height}px;">
              {include file=`$templatesDir`_event_allday_day.html}
            </td>
          </tr>
        {/foreach}

        {*Time Grid*}
        {section name='time' loop=$day_hours}
          {math equation="x/60+y" x=$day_start y=$smarty.section.time.index assign='grid_time' format='%02d'}
          {capture assign='grid_time_info'}{#calendars_add_event#} <b>{$calendar->dateISO|date_format:#date_calendar_day#} {$grid_time}{/capture}
          <tr style="height: {$event_cell_height}px;" class="{cycle values='cal_odd,cal_even'} pointer" onclick="window.open('{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=add&amp;date={$calendar->dateISO}%20{$grid_time}:00', '_self')">
            <td class="cal_hour t_border_double" {help label_content=#addevent# text_content="`$grid_time_info`:00</b>" popup_only=1}>{$grid_time}:00</td>
            <td>&nbsp;</td>
          </tr>
          <tr style="height: {$event_cell_height}px;" class="{cycle values='cal_odd,cal_even'} pointer" onclick="window.open('{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=add&amp;date={$calendar->dateISO}%20{$grid_time}:15', '_self')">
            <td class="t_border_double" {help label_content=#addevent# text_content="`$grid_time_info`:15</b>" popup_only=1}>&nbsp;</td>
            <td>&nbsp;</td>
          </tr>
          <tr style="height: {$event_cell_height}px;" class="{cycle values='cal_odd,cal_even'} pointer" onclick="window.open('{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=add&amp;date={$calendar->dateISO}%20{$grid_time}:30', '_self')">
            <td class="t_border_double" {help label_content=#addevent# text_content="`$grid_time_info`:30</b>" popup_only=1}>&nbsp;</td>
            <td>&nbsp;</td>
          </tr>
          <tr style="height: {$event_cell_height}px;" class="{cycle values='cal_odd,cal_even'} pointer" onclick="window.open('{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=add&amp;date={$calendar->dateISO}%20{$grid_time}:45', '_self')">
            <td class="t_v_border t_border_double" style="height: auto;" {help label_content=#addevent# text_content="`$grid_time_info`:45</b>" popup_only=1}>&nbsp;</td>
            <td class="t_v_border" style="height: auto;">&nbsp;</td>
          </tr>
        {/section}
        </table>
      </div>
    </td>
  </tr>
  <tr>
    <td class="t_footer"></td>
  </tr>
</table>
</div>

{include file=`$templatesDir`_side_panel.html panel_width="300"}
