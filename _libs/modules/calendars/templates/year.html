{strip}
{assign var="table_cols" value=3}
{/strip}
<h1>{$title}</h1>
<div id="form_container" style="width: 900px; float:left">

{include file=`$theme->templatesDir`actions_box.html}
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td colspan="{$table_cols}">
      <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
          <td class="t_caption4 t_border2 hcenter" style="width: 13px">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=year&amp;date={math equation='x-1' x=$calendar->year->year}-01-01" class="strong" title="{#calendars_previous_year#}">&laquo;</a>
          </td>
          <td class="cal_title_bar t_caption2 t_border hcenter">
            <div class="t_caption2_title">{$calendar->dateISO|date_format:#date_calendar_year#}</div>
          </td>
          <td class="t_caption4 hcenter" style="width: 13px">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=year&amp;date={math equation='x+1' x=$calendar->year->year}-01-01" class="strong" title="{#calendars_next_year#}">&raquo;</a>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  {foreach from=$calendar->year->months item=month name="months"}
  {if $smarty.foreach.months.iteration % $table_cols eq 1}
  <tr>
  {/if}
    <td class="cal_year_month">
      {include file=`$templatesDir`_month.html}
    </td>
  {if $smarty.foreach.months.iteration % $table_cols eq 0}
  </tr>
  {/if}
{/foreach}
</table>
</div>

{include file=`$templatesDir`_side_panel.html panel_width="300" skip='previous_month, current_month, next_month'}
