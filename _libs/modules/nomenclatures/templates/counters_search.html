<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=nomenclatures&amp;controller=counters&amp;counters=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="nomenclatures_counter" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;controller=counters" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#nomenclatures_counters_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.formula.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.formula.link}">{#nomenclatures_counters_formula#|escape}</div></td>
          <td class="t_caption t_border {$sort.description.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.description.link}">{#nomenclatures_counters_description#|escape}</div></td>
          <td class="t_caption t_border {$sort.next_number.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.next_number.link}">{#nomenclatures_counters_next_number#|escape}</div></td>
          <td class="t_caption t_border {$sort.count_nomenclatures.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.count_nomenclatures.link}">{#nomenclatures_counters_count_nomenclatures#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$nomenclatures_counters item='nomenclatures_counter'}
      {strip}
      {capture assign='info'}
        <strong>{#nomenclatures_counters_name#|escape}:</strong> {$nomenclatures_counter->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$nomenclatures_counter->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclatures_counter->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$nomenclatures_counter->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclatures_counter->get('modified_by_name')|escape}<br />
        {if $nomenclatures_counter->isDeleted()}<strong>{#deleted#|escape}:</strong> {$nomenclatures_counter->get('deleted')|date_format:#date_mid#|escape}{if $nomenclatures_counter->get('deleted_by_name')} {#by#|escape} {$nomenclatures_counter->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$nomenclatures_counter->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $nomenclatures_counter->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$nomenclatures_counter->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
        <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$nomenclatures_counter->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
        <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
        <td colspan="5" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
        <td>
          {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclatures_counter disabled='edit,delete,view'}
        </td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$nomenclatures_counter->get('active')} t_inactive{/if}{if $nomenclatures_counter->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$nomenclatures_counter->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($nomenclatures_counter->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($nomenclatures_counter->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=counters&amp;{$action_param}=view&amp;view={$nomenclatures_counter->get('id')}">{$nomenclatures_counter->get('name')|escape|default:'&nbsp;'}</a></td>
          <td class="t_border {$sort.formula.isSorted}">{$nomenclatures_counter->get('formula')|escape|default:'&nbsp;'}</td>
          <td class="t_border {$sort.description.isSorted}">{$nomenclatures_counter->get('description')|escape|nl2br|url2href|default:'&nbsp;'}</td>
          <td class="t_border hright {$sort.next_number.isSorted}">{$nomenclatures_counter->get('next_number')|default:1}</td>
          <td class="t_border hright {$sort.count_nomenclatures.isSorted}">{$nomenclatures_counter->get('count_nomenclatures')|default:0}</td>
          <td class="hcenter" nowrap="nowrap">
            {capture assign='delete_limited'}{if $nomenclatures_counter->get('types')}1{else}0{/if}{/capture}
            {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclatures_counter delete_limited=$delete_limited}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="8">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="8"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
