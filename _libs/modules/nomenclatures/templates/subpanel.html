{capture assign='trademark_nomenclature'}{if preg_match('#trademark_nomenclature$#', $session_param)}1{else}0{/if}{/capture}
{if $trademark_nomenclature}
  <div id="messages_container_{$session_param}">
    {if $messages->getErrors()}{include file='message.html' display="error" items=$messages->getErrors()}{/if}
    {if $messages->getMessages()}{include file='message.html' display="message" items=$messages->getMessages()}{/if}
    {if $messages->getWarnings()}{include file='message.html' display="warning" items=$messages->getWarnings()}{/if}
  </div>
  {if $currentUser->checkRights($module, 'manage_outlooks')}
  <div style="position: relative;">
    <div class="action_tabs action_tabs_submenu_upper_right">
      <ul id="subpanel_actions_right_{counter start=1 name='menu_counter_upper_right' assign='subpanel_action_upper_right_menu_num' print=true}" class="zpHideOnLoad">
        <li title="{#manage_outlooks#}">
          <input type="image" src="{$theme->imagesUrl}manage_outlooks.png" width="16" height="16" alt="" title="{#manage_outlooks#}" border="0" onclick="showAvailableOutlookOptions('{$module}|{$controller}', '{$custom_template_options.name}', '{$custom_template_options.value}'); return false;" />
        </li>
      </ul>
      <script type="text/javascript">
        new Zapatec.Menu({ldelim}source: 'subpanel_actions_right_{$subpanel_action_upper_right_menu_num}',
                          hideDelay: 100,
                          theme: 'nzoom'{rdelim});
      </script>
    </div>
  </div>
  {/if}
  <script type="text/javascript">Effect.Pulsate($('messages_container_{$session_param}'), {ldelim}pulses: 1{rdelim});</script>
{/if}
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
  <tr>
    {if $trademark_nomenclature}
    <td class="t_caption t_border" width="15">&nbsp;</td>
    <td class="t_caption t_border hcenter vmiddle" width="15"><span class="help" {help label_content=#main_trademark# popup_only=1}>&nbsp;</span></td>
    {/if}
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
    <td class="t_caption t_border {$sort.code.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.code.link}">{$basic_vars_labels.code|default:#nomenclatures_code#|escape}</div></td>
    <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#nomenclatures_name#|escape}</div></td>
    <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{$basic_vars_labels.type|default:#nomenclatures_type#|escape}</div></td>
    <td class="t_caption t_border {$sort.subtype.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.subtype.link}">{$basic_vars_labels.subtype|default:#nomenclatures_subtype#|escape}</div></td>
    <td class="t_caption t_border {$sort.category.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.category.link}">{$basic_vars_labels.categories|default:#nomenclatures_categories#|escape}</div></td>
    <td class="t_caption" width="80">&nbsp;</td>
  </tr>
{counter start=$pagination.start name='item_counter' print=false}
{foreach name='i' from=$nomenclatures item='nomenclature'}
{strip}
{capture assign='info'}
  <strong><u>{$basic_vars_labels.code|default:#nomenclatures_code#|escape}:</u></strong> {$nomenclature->get('code')|escape}<br />
  <strong>{$basic_vars_labels.type|default:#nomenclatures_type#|escape}:</strong> {$nomenclature->get('type_name')|escape}<br />
  <strong>{$basic_vars_labels.name|default:#nomenclatures_name#|escape}:</strong> {$nomenclature->get('name')|escape}<br />
  <strong>{#added#|escape}:</strong> {$nomenclature->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclature->get('added_by_name')|escape}<br />
  <strong>{#modified#|escape}:</strong> {$nomenclature->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclature->get('modified_by_name')|escape}<br />
  {if $nomenclature->isDeleted()}<strong>{#deleted#|escape}:</strong> {$nomenclature->get('deleted')|date_format:#date_mid#|escape}{if $nomenclature->get('deleted_by_name')} {#by#|escape} {$nomenclature->get('deleted_by_name')|escape}{/if}<br />{/if}

  <strong>{#translations#|escape}:</strong>
  <span class="translations">
  {foreach from=$nomenclature->get('translations') item='trans'}
    <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $nomenclature->get('model_lang')} class="selected"{/if} />
  {/foreach}
  </span>
{/capture}
{/strip}
{include file="`$theme->templatesDir`row_link_action.html" object=$nomenclature assign='row_link'}
{capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
  <tr class="{cycle values='t_odd,t_even'}{if !$nomenclature->get('active')} t_inactive{/if}{if $nomenclature->get('deleted_by')} t_deleted{/if}{if $trademark_nomenclature && !$nomenclature->get('parent_id')} attention{/if}">
    {if $trademark_nomenclature}
    <td class="t_border">
      <input type="image" src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" onclick="return confirmAction('delete_row', function() {ldelim} updateTrademarks(null, {ldelim}action: 'delete', nomenclature: '{$nomenclature->get('id')}'{rdelim}); {rdelim}, this);" />
    </td>
    <td class="t_border">
      <input onclick="updateTrademarks(null, {ldelim}action: 'default', nomenclature: '{$nomenclature->get('id')}', the_element: this{rdelim});"
             type="checkbox"
             name='items[]'
             value="{$nomenclature->get('id')}"
             title="{#check_to_set_default#}"
             {if $nomenclature->get('is_default')}checked="checked"{/if} />
    </td>
    {/if}
    <td class="t_border hright" nowrap="nowrap">
      {if $nomenclature->get('files_count')}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=attachments&amp;attachments={$nomenclature->get('id')}"{if $link_target} target="{$link_target}"{/if}>
          <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
               onmouseover="showFiles(this, '{$module}', '{$controller}', {$nomenclature->get('id')})"
               onmouseout="mclosetime()" />
        </a>
      {/if}
      {counter name='item_counter' print=true}
    </td>
    <td class="t_border {$sort.code.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$nomenclature->get('id')}"{if $link_target} target="{$link_target}"{/if}>{$nomenclature->get('code')|escape|default:'&nbsp;'}</a></td>
    <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$nomenclature->get('id')}"{if $link_target} target="{$link_target}"{/if}>{$nomenclature->get('name')|escape|default:'&nbsp;'}</a></td>
    <td class="t_border {$sort.type.isSorted} {$row_link_class}"{$row_link}>{$nomenclature->get('type_name')|escape}</td>
    <td class="t_border {$sort.subtype.isSorted} {$row_link_class}"{$row_link}>{capture assign='subtype_i18n_param'}nomenclatures_subtype_{$nomenclature->get('subtype')|escape}{/capture}{$smarty.config.$subtype_i18n_param}</td>
    <td class="t_border {$sort.category.isSorted} {$row_link_class}"{$row_link}>&nbsp;
      {if $nomenclature->get('categories_names')}
        {foreach name='cn' from=$nomenclature->get('categories_names') item='cat_name'}
          {$cat_name|escape}{if !$smarty.foreach.cn.last},{/if}
        {foreachelse}
          &nbsp;
        {/foreach}
      {/if}
    </td>
    <td class="hcenter" nowrap="nowrap">
      {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclature}
    </td>
  </tr>
{foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error" colspan="9">{#no_items_found#|escape}</td>
  </tr>
{/foreach}
  <tr>
    <td class="t_footer" colspan="9"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
{capture assign='search_link'}{$smarty.server.PHP_SELF}?{$module_param}={$module}&amp;{$action_param}=subpanel&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$search_link
  use_ajax=$use_ajax
  hide_selection_stats=true
  session_param=$session_param
}
    </td>
  </tr>
</table>