<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=nomenclatures&amp;controller=types&amp;types=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="nomenclatures_type" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;controller=types" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#nomenclatures_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.code.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.code.link}">{#nomenclatures_types_code#|escape}</div></td>
          <td class="t_caption t_border {$sort.type_section.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type_section.link}">{#nomenclatures_types_section#|escape}</div></td>
          <td class="t_caption t_border {$sort.count_nomenclatures.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.count_nomenclatures.link}">{#nomenclatures_types_count_nomenclatures#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$nomenclatures_types item='nomenclatures_type'}
      {strip}
      {capture assign='info'}
        <strong>{#nomenclatures_type#|escape}:</strong> {$nomenclatures_type->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$nomenclatures_type->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclatures_type->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$nomenclatures_type->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclatures_type->get('modified_by_name')|escape}<br />
        {if $nomenclatures_type->isDeleted()}<strong>{#deleted#|escape}:</strong> {$nomenclatures_type->get('deleted')|date_format:#date_mid#|escape}{if $nomenclatures_type->get('deleted_by_name')} {#by#|escape} {$nomenclatures_type->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$nomenclatures_type->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $nomenclatures_type->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$nomenclatures_type->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
        <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$nomenclatures_type->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
        <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
        <td colspan="4" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
        <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclatures_type disabled='edit,delete,view'}
        </td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$nomenclatures_type->get('active')} t_inactive{/if}{if $nomenclatures_type->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$nomenclatures_type->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($nomenclatures_type->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($nomenclatures_type->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
            </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=types&amp;{$action_param}=view&amp;view={$nomenclatures_type->get('id')}">{if $nomenclatures_type->get('icon_name')}<img src="{$smarty.const.PH_NOMENCLATURES_TYPES_URL}{$nomenclatures_type->get('icon_name')}" alt="" border="" /> {/if}{$nomenclatures_type->get('name')|escape}</a></td>
          <td class="t_border {$sort.code.isSorted}">{$nomenclatures_type->get('auto_code_suffix')|escape}</td>
          <td class="t_border {$sort.type_section.isSorted}">{$nomenclatures_type->get('section_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.count_nomenclatures.isSorted} hright">{if $nomenclatures_type->get('count_nomenclatures') gt 0}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;type={$nomenclatures_type->get('id')}&amp;type_section=">{$nomenclatures_type->get('count_nomenclatures')}</a>{else}0{/if}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclatures_type}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="7">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
