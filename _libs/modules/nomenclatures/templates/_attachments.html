{if $nomenclature->get('attachments') || ($action ne 'view' && $layout.edit)}
<table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
{/if}
<!-- ATTACHMENTS --> 
{if $nomenclature->get('attachments')}
  <tr>
    <td class="t_caption2" colspan="4"><div class="strong">{#attachments_files_title#|mb_upper}</div></td>
  </tr>
  <tr>
    <td class="t_caption3 t_border" width="30"><div class="t_caption3_title">{#num#|escape}</div></td>
    <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_path#|escape}</div></td>
    <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_description#|escape}</div></td>
    <td class="t_caption3" width="60">&nbsp;</td>
  </tr>
  {foreach name='i' from=$nomenclature->get('attachments') item='file'}
    {capture assign='modified'}{if $modified_files && array_key_exists($file->get('id'), $modified_files)}1{else}0{/if}{/capture}
    {capture assign='midx'}{if $modified}{$file->get('id')}{else}0{/if}{/capture}
    {capture assign='info'}
      {strip}
        <strong>{#attachments_path#|escape}:</strong> {$file->get('filename')|escape}<br />
        <strong>{#attachments_revision#|escape}:</strong> {$file->get('revision')|string_format:'%02d'}<br />
        <strong>{#attachments_added#|escape}:</strong> {$file->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$file->get('added_by_name')|escape}<br />
        <strong>{#attachments_modified#|escape}:</strong> {$file->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$file->get('modified_by_name')|escape}<br />
        {if $file->isDeleted()}<strong>{#deleted#|escape}:</strong> {$file->get('deleted')|date_format:#date_mid#|escape}{if $file->get('deleted_by_name')} {#by#|escape} {$file->get('deleted_by_name')|escape}{/if}<br />{/if}
        <strong>{#translations#|escape}:</strong>
        <span class="translations">
        {foreach from=$file->get('translations') item='trans'}
          <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $file->get('model_lang')} class="selected"{/if} />
        {/foreach}
        </span>
      {/strip}
    {/capture}
    {if !$file->get('not_exist')}
      {capture assign='row_link'}{strip}{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=viewfile&amp;viewfile={$nomenclature->get('id')}&amp;file={$file->get('id')}{/strip}{/capture}
      {capture assign='row_link_clauses'}{strip}onclick="window.open('{$row_link}', '_blank')" style="cursor:pointer;"{/strip}{/capture}
    {else}
      {capture assign='row_link'}{/capture}
      {capture assign='row_link_clauses'}{/capture}
    {/if}
  <tr class="{cycle values='t_odd,t_even'}{if $erred_modified_files && @in_array($file->get('id'), $erred_modified_files)} t_deleted{/if}">
    <td{if $erred_modified_files && @in_array($file->get('id'), $erred_modified_files)} class="error"{/if} align="right">
      {if $file->get('not_exist')}
      <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=#attachments_file_not_exist#|escape} />
      {/if}
      {$smarty.foreach.i.iteration}.
    </td>
    <td {$row_link_clauses}>
      <input type="hidden" name="file_filenames[{$file->get('id')}]" value="{$file->get('filename')}" />
      <input type="hidden" name="file_indices[{$file->get('id')}]" value="{$smarty.foreach.i.iteration}" />
      {if $modified}
      <div id="file_paths_value_{$midx}" style="display: none">
        <img src="{$theme->imagesUrl}{$file->getIconName()}.png" width="16" height="16" border="0" alt="" />
        {$modified_files.$midx.filename|escape}
      </div>
      <input type="file" class="filebox" name="file_paths[{$midx}]" id="file_paths_{$midx}" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#attachments_path#|escape}" />
      {else}
      <div id="file_paths_value_{$file->get('id')}">
        <img src="{$theme->imagesUrl}{$file->getIconName()}.png" width="16" height="16" border="0" alt="" />
        {$file->get('filename')|escape}
      </div>
      <input type="file" class="filebox" name="file_paths[{$file->get('id')}]" id="file_paths_{$file->get('id')}" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#attachments_path#|escape}" style="display: none" disabled="disabled" />
      {/if}
    </td>
    <td {$row_link_clauses}>
      {if $modified}
        {$modified_files.$midx.description|escape}
      {else}
        {$file->get('description')|escape}
      {/if}
    </td>
    <td nowrap="nowrap">
    {if !$file->get('not_exist')}
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=viewfile&amp;viewfile={$nomenclature->get('id')}&amp;file={$file->get('id')}" target="_blank">
        <img src="{$theme->imagesUrl}view.png" width="16" height="16" border="0" alt="" />
      </a>
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=getfile&amp;getfile={$nomenclature->get('id')}&amp;file={$file->get('id')}">
        <img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="" />
      </a>
    {else}
      <img src="{$theme->imagesUrl}view.png" width="16" height="16" border="0" alt="{#view#|escape}" class="pointer dimmed" />
      <img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" class="pointer dimmed" />
    {/if}
    {if $action ne 'view' && $layout.edit}
      {if $currentUser->checkRights('nomenclatures', 'delete_file') && $smarty.session.currentUserId eq $file->get('added_by') && !$file->get('not_exist')}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=delfile&amp;delfile={$nomenclature->get('id')}&amp;file={$file->get('id')}" onclick="return confirmAction('delete_file', function(el) {ldelim} window.location.href = el.href; {rdelim}, this);" title="{#delete#|escape}">
          <img src="{$theme->imagesUrl}delete.png" width="16" height="16" border="0" alt="{#delete#|escape}" />
        </a>
      {else}
        <img src="{$theme->imagesUrl}delete.png" width="16" height="16" border="0" alt="{#delete#|escape}" onclick="alert('{#error_delete_notallowed#|escape:'quotes'|escape}');" class="pointer dimmed" />
      {/if}
    {/if}
      <img src="{$theme->imagesUrl}info.png" width="16" height="16" border="0" alt="" class="help" {popup text=$info|escape caption=#system_info#|escape} />
    </td>
  </tr>
  {foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error" colspan="4">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
{/if}
{if $action ne 'view' && $layout.edit}
  <tr>
    <td class="t_caption2" colspan="4">
    <div class="strong">{#attachments_add_new#|mb_upper}</div></td>
  </tr>
  <tr>
    <td class="t_caption3 t_border"><div class="t_caption3_title">{#num#|escape}</div></td>
    <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_path#|escape}{#required#}</div></td>
    <td class="t_caption3 t_buttons_container" colspan="2">
      <div class="t_caption3_title floatl">{#attachments_description#|escape}</div>
      <div class="t_buttons">
        <div id="plusButton" onclick="addFileBrowse('a_file_paths', 'a_file_descriptions', this)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
        <div id="minusButton" class="disabled" onclick="removeFileBrowse(this)" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
      </div>
    </td>
  </tr>
  <tr>
    <td class="t_border hright">1.</td>
    <td class="t_border">
      <input type="file" class="filebox" name="a_file_paths[]" id="a_file_paths_1" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#attachments_path#|escape}" />
    </td>
    <td>
      <input type="text" class="txtbox" name="a_file_descriptions[]" id="a_file_descriptions_1" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#attachments_description#|escape}" style="width: 300px" />
    </td>
  </tr>
  {/if}
{if $nomenclature->get('attachments') || ($action ne 'view' && $layout.edit)}
</table>
<div style="height:10px; border-top: 1px solid #CCCCCC"></div>
{/if}