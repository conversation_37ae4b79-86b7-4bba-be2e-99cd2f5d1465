{** PARAMETERS:
 * object           - model object - nomenclature of nomenclature type
 * items_type       - type of items - can be either 'income' or 'expense'
 * items_options    - array with income/expense items to fill dropdown with items
 *}

{array assign='js_methods' eval='array(\'onkeyup\' => \'recalculateNomItemsPercentage(this);\')'}
{capture assign='table_id'}table_items_{$items_type}{/capture}
{capture assign='items'}items_{$items_type}{/capture}
{capture assign='percentage'}percentage_{$items_type}{/capture}
{assign var='items' value=$object->get($items)}
{assign var='percentage' value=$object->get($percentage)}

<table border="0" cellpadding="5" cellspacing="0" class="t_table" id="{$table_id}">
  <tr>
    <td class="t_caption3 t_border" width="15"><div class="t_caption3_title">{#num#|escape}</div></td>
    <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{#nomenclatures_items#|escape}</div></td>
    <td class="t_caption3" width="100" nowrap="nowrap">
      <div class="t_caption3_title floatl">{#nomenclatures_percentage#|escape}</div>
      <div class="t_buttons">
        <div id="{$table_id}_plusButton" onclick="addField('{$table_id}', '', '', true); recalculateNomItemsPercentage($('percentage_{$items_type}_1'));" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
        <div id="{$table_id}_minusButton"{if $items|@count le 1} class="disabled"{/if} onclick="removeField('{$table_id}'); recalculateNomItemsPercentage($('percentage_{$items_type}_1'));" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
      </div>
    </td>
  </tr>
  {foreach from=$items item='item' name='i' key='k'}
  <tr id="{$table_id}_{$smarty.foreach.i.iteration}">
    <td class="t_border t_bottom_border hright" nowrap="nowrap">
      <img {if $items|@count le 1}style="visibility: hidden;" {/if}src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" onclick="confirmAction('delete_row', function() {ldelim} hideField('{$table_id}','{$smarty.foreach.i.iteration}'); recalculateNomItemsPercentage($('percentage_{$items_type}_1')); {rdelim}, this);" />&nbsp;<a href="javascript: disableField('{$table_id}','{$smarty.foreach.i.iteration}');recalculateNomItemsPercentage($('percentage_{$items_type}_1'));">{$smarty.foreach.i.iteration}</a>
    </td>
    <td class="t_border t_bottom_border">
      {include file='input_dropdown.html'
               name=items_`$items_type`
               standalone=true
               required=1
               really_required=1
               show_inactive_options=true
               index=$smarty.foreach.i.iteration
               value=$items.$k
               options=$items_options
               onchange='validateNomItems(this)'
               label=$smarty.config.nomenclatures_items}
    </td>
    <td class="t_bottom_border">
      {include file='input_text.html'
               name=percentage_`$items_type`
               standalone=true
               required=1
               index=$smarty.foreach.i.iteration
               value=$percentage.$k
               label=$smarty.config.nomenclatures_percentage
               restrict='insertOnlyFloats'
               js_methods=$js_methods
               text_align='right'}
    </td>
  </tr>
  {foreachelse}
  <tr id="{$table_id}_1">
    <td class="t_border t_bottom_border hright" nowrap="nowrap">
      <img src="{$theme->imagesUrl}small/delete.png" style="visibility: hidden;" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" onclick="confirmAction('delete_row', function() {ldelim} hideField('{$table_id}','1'); recalculateNomItemsPercentage($('percentage_{$items_type}_1')); {rdelim}, this);" />&nbsp;<a href="javascript: disableField('{$table_id}','1');recalculateNomItemsPercentage($('percentage_{$items_type}_1'));">1</a>
    </td>
    <td class="t_border t_bottom_border">
      {include file='input_dropdown.html'
               name=items_`$items_type`
               standalone=true
               required=1
               really_required=1
               index=1
               options=$items_options
               show_inactive_options=true
               onchange='validateNomItems(this)'
               value=''
               label=$smarty.config.nomenclatures_items}
    </td>
    <td class="t_bottom_border">
      {include file='input_text.html'
               name=percentage_`$items_type`
               standalone=true
               required=1
               index=1
               value='0'
               label=$smarty.config.nomenclatures_percentage
               restrict='insertOnlyFloats'
               js_methods=$js_methods
               text_align='right'}
    </td>
  </tr>
  {/foreach}
</table>
