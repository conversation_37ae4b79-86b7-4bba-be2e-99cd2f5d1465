<h1>{$title}</h1>

<div id="form_container">

  {include file=`$theme->templatesDir`actions_box.html}
  {include file=`$theme->templatesDir`translate_box.html}
  {include file=`$theme->templatesDir`_submenu_actions_box.html}

  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
    <tr>
      <td class="nopadding">
        <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
          {include file=`$templatesDir`_info_header.html}
        </table>
      </td>
    </tr>
    {capture assign="current_communication_list"}nomenclatures_{if $communication_type}{$communication_type}{else}communications{/if}{/capture}
    <tr>
      <td class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="nomenclatures_communications_switch"><div class="switch_{if $smarty.cookies.nomenclatures_communications_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title" id="title_section_communications">{$smarty.config.$current_communication_list|escape}</div></td>
    </tr>
    <tr id="nomenclatures_communications"{if $smarty.cookies.nomenclatures_communications_box eq 'off'} style="display: none"{/if}>
      <td class="nopadding">
        {include file=_communication_panel.html}
      </td>
    </tr>
  </table>
  {include file=`$theme->templatesDir`help_box.html}
</div>