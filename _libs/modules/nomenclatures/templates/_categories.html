{if $from_type}
  <tr>
    <td colspan="3" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="nom_categories_switch"><div class="switch_{if $smarty.cookies.nom_categories_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption3_title">{#nomenclatures_categories#|escape}</div></td>
  </tr>
  <tr id="nom_categories"{if $smarty.cookies.nom_categories_box eq 'off'} style="display: none;"{/if}>
{else}
  <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
{/if}
  <td colspan="3">
    <input type="hidden" name="update_categories" value="1" />
    {* tree initialization *}
    <script type='text/javascript'>
    {literal}
    var tree;
    window.onload = function() {
      new Zapatec.Tree({
          tree: "tree",
          theme : "default",
          initLevel: 10, 
          hiliteSelectedNode: false
      });
    }
    {/literal}
    </script>

    <a href="javascript:Zapatec.Tree.all['tree'].collapseAll()">{#collapse_all#|escape}</a> |
    <a href="javascript:Zapatec.Tree.all['tree'].expandAll()">{#expand_all#|escape}</a>
    <div id="tree_container">
    <ul id="tree">
    {strip}
{foreach name='i' from=$categories_tree item='category' key='idx'}
    {assign var='level' value=$category->get('level')}
    {if $smarty.foreach.i.last}
      {assign var='next_level' value=0}
    {else}
      {assign var='next_idx' value=$idx+1}
      {assign var='next_category' value=$categories_tree.$next_idx}
      {assign var='next_level' value=$next_category->get('level')}
    {/if}
    <li><input type="checkbox" name="categories[]" id="category_{$category->get('id')}" value="{$category->get('id')}"{if @in_array($category->get('id'), $cats)} checked="checked"{/if}{if $action eq 'view' || $category->isDeleted() || !$category->isActivated() || (!$from_type && !$layout.edit)} disabled="disabled"{/if}{if !$category->isDeleted() && $category->isActivated()} onclick="toggleCategories(this)"{/if} /><label for="category_{$category->get('id')}"{if $category->isDeleted() || !$category->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{$category->get('name')|escape}</label>
    {if !$from_type && $action ne 'view' && !$layout.edit && @in_array($category->get('id'), $cats)}
      <input type="hidden" name="categories[]" value="{$category->get('id')}" />
    {/if}
    {if $next_level > $level}
       <ul>
    {elseif $next_level eq $level}
       </li>
    {else}
       {repeat string='</li></ul>' num=$level-$next_level}</li>
    {/if}
{/foreach}
    {/strip}
    </ul>
    </div>
  </td>
</tr>
