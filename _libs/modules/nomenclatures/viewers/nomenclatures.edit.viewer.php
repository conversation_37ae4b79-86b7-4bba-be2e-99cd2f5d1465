<?php

use Nzoom\Mvc\ViewTrait\ModelPermissionsTrait;

class Nomenclatures_Edit_Viewer extends Viewer {
    use ModelPermissionsTrait;

    public $template = 'edit.html';

    public function prepare() {

        if (!$this->isMain) {
            $this->model = $this->registry['nomenclature'];

            /** @var Request $request */
            $request = &$this->registry['request'];

            //finds the information for autocompleter field
            $autocomplete_params = $this->preparePopupAutocompleteParams();

            if ($autocomplete_params) {
                $this->model->set('autocomplete_params', json_encode($autocomplete_params), true);
            }

            if (!$autocomplete_params || !$request['uniqid']) {
                $this->data['messages']->setError($this->i18n('error_popup_autocomplete_params_missing'));
            }

            // after successful save - redirected to same action, close the popup
            if (!$request->isPost() && $request['autocomplete_filter'] == 'session') {
                $autocomplete_data = $this->preparePopupAutocompleteData($autocomplete_params);

                $this->data['autocomplete_data'] = json_encode($autocomplete_data);
                $this->templatesDir = $this->theme->get('templatesDir');
                $this->setTemplate('_added_model.html');

                return;
            }
        }

        $this->prepareModelsPermissionsForRest([$this->model]);

        // structure the layout vars into two-dimensional array
        $this->model->getLayoutVars();

        // prepare layout index
        $this->prepareLayoutIndex();

        // id of model
        $this->data['model_id'] = $this->model->get('id');

        // prepare saved configurations
        $this->prepareSavedConfigurations();

        if ($this->registry['added_files']) {
            $this->data['added_files'] = $this->registry['added_files'];
        }

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                                    $_SERVER['PHP_SELF'],
                                    $this->registry['module_param'], $this->module,
                                    $this->registry['action_param'], $this->action,
                                    $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        // GET GROUPS
        require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare nomenclature type
        require_once($this->modelsDir . 'nomenclatures.types.factory.php');
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'sanitize' => true,
                         'where' => array('nt.id = ' . $this->model->get('type')));
        $this->data['nomenclature_type'] = Nomenclatures_Types::searchOne($this->registry, $filters);
        //$this->model->set('type_name', ($this->data['nomenclature_type'] ? $this->data['nomenclature_type']->get('name') : ''), true);

        //prepare categories tree
        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php');
        $this->data['categories_tree'] = array_values(Nomenclatures_Categories::getTree($this->registry, array('sanitize' => true)));

        //get nomenclature categories
        $this->data['cats'] = $this->model->getCategories();

        $this->registry['include_tree'] = true;

        $this->data['currencies'] = Nomenclatures_Dropdown::getCurrencies(array($this->registry));

        if ($this->isMain) {
            $this->prepareSubpanels();

            $this->prepareTranslations();
        }

        $this->prepareTitleBar();

        if ($this->model->get('disable_batch_edit')) {
            $this->registry['messages']->setWarning($this->i18n('warning_nomenclature_batch_edit_disabled'));
        }

        if ($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }

    public function prepareTitleBar() {
        // TODO: find a consensus for the title!
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('nomenclatures_edit'), $this->model->getModelTypeName());
        }
        $this->data['title'] = $title;
    }
}

?>
