<?php

class Nomenclatures_Counters_Search_Viewer extends Viewer {
    public $template = 'counters_search.html';

    public function prepare() {
        require_once $this->modelsDir . 'nomenclatures.counters.factory.php';

        $filters = Nomenclatures_Counters::saveSearchParams($this->registry, array(), 'search_');

        list($this->data['nomenclatures_counters'], $this->data['pagination']) =
            Nomenclatures_Counters::pagedSearch($this->registry, $filters);

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('nomenclatures_counters');
    }
}

?>
