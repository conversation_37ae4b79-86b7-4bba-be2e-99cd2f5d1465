<?php

class Nomenclatures_Pricelists_Add_Viewer extends Viewer {
    public $template = 'pricelists_add.html';

    public function prepare() {
        $this->model = $this->registry['nomenclatures_pricelist'];
        $this->data['nomenclatures_pricelist'] = $this->model;

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s', 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare customers/deliverer list
        require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
        $this->data['deliverer'] = Customers::search($this->registry);

        //prepare categories tree
        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php');
        $categories_tree = Nomenclatures_Categories::getTree($this->registry);
        $this->data['categories_tree'] = Nomenclatures_Categories::sanitizeModels($categories_tree);

        $this->data['cats'] = $this->registry->get('cats');
            
        $this->registry['include_tree'] = true;
    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures');
        $href = sprintf('%s=%s', 
                            $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('nomenclatures_pricelists');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('nomenclatures_pricelists_add');
        $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
