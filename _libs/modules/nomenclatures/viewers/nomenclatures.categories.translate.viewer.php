<?php

class Nomenclatures_Categories_Translate_Viewer extends Viewer {
    public $template = 'categories_translate.html';

    public function prepare() {
        $this->model = $this->registry['category'];
        $this->data['nomenclatures_category'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;controller=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'],
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare category tree
        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php');
        $this->data['categories_tree'] = Nomenclatures_Categories::getTree($this->registry);

        //prepare descendants
        $this->data['categories_descendants'] = Nomenclatures_Categories::getTreeDescendants($this->registry, array('where' => array('c1.id = ' . $this->model->get('id')),
                                                                                                                    'sanitize' => true));

        //prepare parents
        $this->data['categories_parents'] = Nomenclatures_Categories::getTreeParents($this->registry, $this->model->get('id'));

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('c.id = ' . $this->model->get('id')),
                         'model_lang' => $basic_model_lang,
                         'sanitize' => true);
        $this->data['base_model'] = Nomenclatures_Categories::searchOne($this->registry, $filters);
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('nomenclatures_categories_translate');
    }
}

?>
