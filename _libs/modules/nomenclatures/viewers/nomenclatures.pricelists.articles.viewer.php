<?php

class Nomenclatures_Pricelists_Articles_Viewer extends Viewer {
    public $template = 'pricelists_articles.html';
    public $filters = array();

    /**
     * Sortable columns in the list view
     */
    public $sortables = array('code','name', 'type', 'deliverer', 'sell_price', 'delivery_price', 
                              'pricelist_price', 'currency');

    public function prepare() {
        $this->model = $this->registry['nomenclatures_pricelist'];
        $this->data['nomenclatures_pricelist'] = $this->model;

        require_once $this->modelsDir . 'nomenclatures.pricelists.factory.php';

        $additional_filters = array('paginate' => true,
                                    'where' => array('p.id = ' . $this->model->get('id')));

        $filters = Nomenclatures_Pricelists::saveSearchParams($this->registry, $additional_filters, 'articles_');

        list($nomenclatures_articles, $pagination) = Nomenclatures_Pricelists::pagedSearch($this->registry, $filters, 'searchArticles');

        $this->data['nomenclatures_articles'] = $nomenclatures_articles;
        $this->data['pagination'] = $pagination;
        $this->data['pricelist_id'] = $this->registry['request']->get('articles');

        //prepare sort array for the listing
        $sortBase = sprintf("%s?%s=nomenclatures&amp;controller=%s&amp;%s=%s&amp;%s=%s", 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'],
                            $this->registry->get('controller'),
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));

        $this->prepareSort($filters, $sortBase);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures');
        $href = sprintf('%s=%s', 
                            $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('nomenclatures_pricelists_articles');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
