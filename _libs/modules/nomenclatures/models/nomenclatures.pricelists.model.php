<?php

/**
 * Nomenclatures_Pricelist model class
 */
Class Nomenclatures_Pricelist extends Model {
    public $modelName = 'Nomenclatures_Pricelist';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //convert CSV transform_to list into array
        if ($this->get('transform_to') && !is_array($this->get('transform_to'))) {
            $transform_to_array = preg_split('#\s*,\s*#', $this->get('transform_to'));
            $this->set('transform_to', $transform_to_array, true);
        }
        if ($this->get('multitransform_to') && !is_array($this->get('multitransform_to'))) {
            $multitransform_to_array = preg_split('#\s*,\s*#', $this->get('multitransform_to'));
            $this->set('multitransform_to', $multitransform_to_array, true);
        }

        if ($this->get('id')) {
            $this->getPricesCount('article');
            $this->getPricesCount('service');
        }
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_pricelist_no_name_specified', 'name');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();
            if ($this->$action()) {

                //assign categories
                $this->assignCategories();

                return true;
            } else {
                $this->slashesStrip();
                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        $db->StartTrans();

        //query to insert the main table
        $query = 'INSERT INTO ' . DB_TABLE_PRICESLISTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        //start transaction
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new nomenclature pricelist base details', $db, $query);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_PRICESLISTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('updating user base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }


        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;
        return $result;
    }

    /**
     * GETS all pricelists
     *
     * @return array - result of the operation
     */
    public function updatePrices(&$registry, $article_type) {
        $db = &$registry->get('db');
        $request = &$registry->get('request');
        $pricelist_id = $request->get('articles');//$request->get($request->get('pricelists'));

        $nomenclatures_items = Nomenclatures_Pricelists::searchArticles($registry, array('type'=>$article_type,'id'=>$pricelist_id));

        if ($request->get('items') && !$request->get('pricelist_all')) {
            $data['ids'] = $request->get('items');
            $prices = $request->get('pricelist_price');

            foreach ($data['ids'] as $price_id) {
                if (!empty($price_id) && isset($prices[$price_id])) {
                    $data['price'][] = $prices[$price_id];
                }
            }

        } else if ($request->get('pricelist_all')) {
            foreach ($nomenclatures_items as $nomenclatures_item) {
                $data['ids'][] = $nomenclatures_item->get('id');
                $data['price'][] = $nomenclatures_item->get('price');
            }
        }

        if (is_numeric($pricelist_id) && !empty($data['ids'])) {
            foreach ($data['price'] as $k => $price) {
                switch ($request->get('pricelist_price_action')) {
                case 'increase':
                    if ($request->get('pricelist_price_type') == 'int') {
                        $with = sprintf('%.2F', $request->get('pricelist_price_value'));
                        $new_price = $price + $with;
                    } else {
                        $with = 1 + sprintf('%.2F', $request->get('pricelist_price_value'))/100;
                        $new_price = $price * $with;
                    }
                    break;
                case 'decrease':
                    if ($request->get('pricelist_price_type') == 'int') {
                        $with = sprintf('%.2F', $request->get('pricelist_price_value'));
                        $new_price = (($price - $with) > 0) ? $price - $with: 0;
                    } else {
                        $with = 1 - sprintf('%.2F', $request->get('pricelist_price_value'))/100;
                        $new_price = $price * $with;
                    }
                    break;
                case 'hard':
                    if ($request->get('pricelist_price_value')) {
                        $new_price = $request->get('pricelist_price_value');
                    }
                    break;
                default:
                    $new_price = $price;
                    break;
                }

                $query = "UPDATE " . DB_TABLE_PRICES . "
                            SET price=" . (($new_price < 0) ? 0 :$new_price) . "
                            WHERE
                                list_id=" . $pricelist_id . " AND
                                nom_id=" . $data['ids'][$k] . ";";

                $db->Execute($query);
            }
        } else {

            return false;
        }
        return !$db->ErrorMsg() ;
    }

    /**
     * Gets available Categories for all nomenclatures
     *
     * @return array $permissions
     */
    public function getCategories() {
        $db = &$this->registry['db'];
        $request = $this->registry['request'];

        $where = array();
        if ($this->get('id')) {
            $where[] = sprintf("c.parent_id=%d", $this->get('id'));
        }

        $where[] = sprintf("c.model='%s'", $request->get('controller'));

        //get permissions from the db
        $query = 'SELECT c.cat_id' . "\n" .
                 'FROM ' . DB_TABLE_NOM_CATS . ' AS c' . "\n" .
                 'WHERE ' . implode(' AND ', $where) . " \n" .
                 'ORDER BY c.cat_id' . "\n";

        $cats = $db->GetCol($query);

        return $cats;
    }

    /**
     * Assigns categories to nomneclatures
     *
     * @return bool - result of the operation
     */
    public function assignCategories() {
        $db = &$this->registry['db'];

        //check if the model has assigned
        if (!$this->get('categories')) {
            return false;
        }

        //clear all previously assigned roles for the user
        $query = 'DELETE FROM ' . DB_TABLE_NOM_CATS . "\n" .
                 'WHERE parent_id=' . $this->get('id') . ' AND model="' . $this->get('controller') . '"';
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('clearing user groups', $db, $query);
        }


        $inserts = array();
        foreach ($this->get('categories') as $cat_id) {
            //create insert statement for all roles
            $inserts[] = sprintf("(%d, %d, '%s')", $this->get('id'), $cat_id, $this->get('controller'));
        }

        $query2 = 'INSERT INTO ' . DB_TABLE_NOM_CATS . "\n" .
                  'VALUES ' . implode(",\n", $inserts);
        $db->Execute($query2);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('updating user groups', $db, $query2);
            return false;
        }

        return true;
    }

    /**
     * Checks model translations
     *
     * @return bool - array of available languages
     */
    public function getTranslations() {
        if (!$this->get('id')) {
            return array();
        }

        if ($this->isDefined('translations')) {
            return $this->get('translations');
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT a_i18n.lang ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_PRICESLISTS_I18N . ' AS a_i18n' . "\n";

        //where clause
        $sql['where'] = 'WHERE a_i18n.parent_id=' . $this->get('id') . "\n";

        $sql['order'] = 'ORDER BY a_i18n.translated' . "\n";

        $query = implode("\n", $sql);

        $records = $this->registry['db']->GetCol($query);

        if ($records) {
            $this->set('translations', $records, true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        $set['modified']      = sprintf("modified=now()");
        $set['modified_by']   = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        $update['name']         = sprintf("name='%s'", $this->get('name'));
        $update['description']  = sprintf("description='%s'", $this->get('description'));

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        //query to insert/update the i18n table for the selected model language
        $query2 = 'INSERT INTO ' . DB_TABLE_PRICESLISTS_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * Checks if the model is permitted for a certain nomenclatures operation
     *
     * @return bool
     */
    public function isPermitted($action) {
        if ($this->checkPermissions($action, 'nomenclatures_pricelists')) {
            $user_permissions = $this->registry['currentUser']->getRights();

            if ($user_permissions['nomenclatures_pricelists'][$action] == 'group' && !$this->get('group')) {
                //if the group is empty do not allow action
                return false;
            } else {
                return true;
            }

        }

        return false;
    }

    /**
     * Translates some i18n fields
     *
     * @return bool - result of the operation
     */
    public function getDefaultTranslations() {
        $db = $this->registry['db'];
        $model_lang = $this->registry['lang'];

        //select clause
        $sql['select'] = 'SELECT a.*, a_i18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang ' . "\n";
       //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_PRICESLISTS . ' AS dt' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_PRICESLISTS_I18N . ' AS a_i18n' . "\n" .
                       '  ON (a.id=a_i18n.parent_id AND a_i18n.lang="' . $model_lang . '")'. "\n";

        $sql['where'] = 'WHERE a.id=' . $this->get('id') . "\n";

        $query = implode("\n", $sql);
        $records = $db->GetRow($query);

        $this->set('default_translations', $records, true);

        return $records;
    }

    /**
     * Get the count of articles or services items in a pricelist
     *
     * @return string $type - articles or services
     * @return bool - result of the operation
     */
    public function getPricesCount($type) {
        if ($this->get('count_' . strtolower($type) . 's')) {
            return $this->get('count_' . strtolower($type) . 's');
        }

        $db = $this->registry['db'];

        //select clause
        $query = 'SELECT COUNT(pp.nom_id) AS ' . 'count_' . strtolower($type) . 's' . "\n" .
                 'FROM ' . DB_TABLE_PRICESLISTS . ' AS p' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PRICES . ' AS pp' . "\n" .
                 '  ON (p.id=pp.list_id)' . "\n" .
                 'WHERE p.id=' . $this->get('id') . "\n" .
                 'GROUP BY p.id' . "\n";

        $count = $db->GetOne($query);

        $this->set('count_' . strtolower($type) . 's', $count, true);

        return $count;
    }
}

?>
