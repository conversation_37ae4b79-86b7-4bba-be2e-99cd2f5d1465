<?php

class Nomenclatures_Audit extends Audit {
    //module name
    public static $module = 'nomenclatures';

    /**
     * prepare audit data for saving
     *
     * @return bool
     */
    public static function prepareData(&$registry, &$params) {

        //replace ids with labels for basic variables
        $replace_labels = array();
        $basic_vars = parent::getBasicAuditVars($registry, self::$module);

        $audit_vars = array();

        switch ($params['action_type']) {
        case 'add':
        case 'clone':
        case 'translate':
        case 'edit':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                $is_add_action = in_array($params['action_type'], array('add', 'clone'));
                foreach ($basic_vars as $var) {
                    if ($is_add_action && $params['model']->get($var) || !$is_add_action && $params['model']->get($var) != $params['old_model']->get($var)) {
                        $audit_vars[$i]['field_name'] = $var;
                        $audit_vars[$i]['field_value'] = $params['model']->get($var);
                        $audit_vars[$i]['old_value'] = '';
                        if (isset($replace_labels[$var])) {
                            $audit_vars[$i]['label'] = $params['model']->get($replace_labels[$var]);
                            if (!$is_add_action) {
                                $audit_vars[$i]['old_value'] = $params['old_model']->get($replace_labels[$var]);
                            }
                        } else {
                            switch ($var) {
                                case 'subtype':
                                    $audit_vars[$i]['label'] = $registry['translater']->translate("nomenclatures_subtype_{$params['model']->get($var)}");
                                    if (!$is_add_action) {
                                        $audit_vars[$i]['old_value'] = $registry['translater']->translate("nomenclatures_subtype_{$params['old_model']->get($var)}");
                                    }
                                    break;
                                case 'has_batch':
                                    $audit_vars[$i]['label'] = $registry['translater']->translate($params['model']->get($var) ? 'yes' : 'no');
                                    if (!$is_add_action) {
                                        $audit_vars[$i]['old_value'] = $registry['translater']->translate($params['old_model']->get($var) ? 'yes' : 'no');
                                    }
                                    break;
                                default:
                                    $audit_vars[$i]['label'] = $params['model']->get($var);
                                    if (!$is_add_action) {
                                        $audit_vars[$i]['old_value'] = $params['old_model']->get($var);
                                    }
                                    break;
                            }
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                    }
                }
            }

            $i = count($audit_vars);
            $vars = $params['model']->get('vars') ? $params['model']->getAssocVars() : array();
            $old_vars = $params['old_model']->get('vars') ? $params['old_model']->getAssocVars() : array();
            $bb_delimiter_added = null;
            if ($vars) {
                foreach ($vars as $var_name => $var) {
                    if ($var['auditable'] && !in_array($var['type'], self::$no_audit_var_types)) {
                        $add_audit = false;
                        $old_not_exist = true;
                        $old_value = '';

                        if (!empty($old_vars[$var_name])) {
                            $same_object = false;
                            $old_not_exist = false;
                            $old_var = $old_vars[$var_name];
                            //objects - compare ids
                            if (is_object($var['value']) && is_object($old_var['value']) &&
                                $var['value']->get('id') == $old_var['value']->get('id')) {
                                $same_object = true;
                            } elseif (is_array($var['value']) || is_array($old_var['value'])) {
                                if (!is_array($var['value'])) {
                                    $var['value'] = array();
                                }
                                if (!is_array($old_var['value'])) {
                                    $old_var['value'] = array();
                                }
                                //array of objects - get only ids
                                $tmp = array_filter($var['value']);
                                $tmp = reset($tmp);
                                if (!is_object($tmp)) {
                                    $tmp = array_filter($old_var['value']);
                                    $tmp = reset($tmp);
                                }

                                if (is_object($tmp)) {
                                    $tmp = array();
                                    foreach ($var['value'] as $obj) {
                                        $tmp[] = is_object($obj) ? $obj->get('id') : $obj;
                                    }
                                    $var['value'] = $tmp;
                                    $tmp = array();
                                    foreach ($old_var['value'] as $obj) {
                                        $tmp[] = is_object($obj) ? $obj->get('id') : $obj;
                                    }
                                    $old_var['value'] = $tmp;
                                }
                                unset($tmp);

                                $diff = array_diff($var['value'], $old_var['value']);
                                $diff2 = array_diff($old_var['value'], $var['value']);
                                $diff = array_unique(array_merge($diff, $diff2));
                            }
                            if ((!is_array($var['value']) && $var['value'] !== $old_var['value'] && !($var['value'] === '' && is_null($old_var['value'])) && empty($same_object)) ||
                            (is_array($var['value']) && !empty($diff))) {
                                $add_audit = true;
                                $old_value = $old_var['value'];
                            }
                        }

                        if ($add_audit || $old_not_exist) {
                            if ($params['model']->get('bb_id') && $params['action_type'] == self::BB_AUDIT_ACTION && !isset($bb_delimiter_added)) {
                                // add audit of non-existent variable to display start of a variant
                                $bb_delimiter_added = true;
                                $audit_vars[$i]['field_name'] = self::BB_DELIMITER_VAR;
                                $audit_vars[$i]['field_value'] = $params['model']->get('bb_id');
                                $audit_vars[$i]['label'] = '';
                                $audit_vars[$i]['old_value'] = '';
                                $audit_vars[$i]['var_type'] = PH_VAR_ADDITIONAL;
                                $i++;
                            }
                            $audit_vars[$i]['field_name'] = $var['name'];
                            if (is_array($var['value']) || is_array($old_value)) {
                                $audit_vars[$i]['field_value'] = serialize($var['value']);
                                $audit_vars[$i]['is_array'] = 1;
                                if (!empty($var['options'])) {
                                    foreach ($var['options'] as $opt) {
                                        if (is_array($var['value'])) {
                                            foreach ($var['value'] as $k => $val) {
                                                if ($opt['option_value'] == $val) {
                                                    $var['value'][$k] = $opt['label'];
                                                }
                                            }
                                        }
                                        if (is_array($old_value)) {
                                            foreach ($old_value as $k => $val) {
                                                if ($opt['option_value'] == $val) {
                                                    $old_value[$k] = $opt['label'];
                                                }
                                            }
                                        }
                                    }
                                }
                                $audit_vars[$i]['label'] = is_array($var['value']) ? implode("\n", $var['value']) : '';
                                $audit_vars[$i]['old_value'] = is_array($old_value) ? implode("\n", $old_value) : '';
                            } else {
                                $audit_vars[$i]['field_value'] = $var['value'];
                                if (!empty($var['options'])) {
                                    foreach ($var['options'] as $opt) {
                                        if ($opt['option_value'] == $var['value']) {
                                            $audit_vars[$i]['label'] = $opt['label'];
                                        }
                                        if ($opt['option_value'] == $old_value) {
                                            $audit_vars[$i]['old_value'] = $opt['label'];
                                        }
                                    }
                                } else {
                                    $audit_vars[$i]['label'] = $var['value'];
                                    $audit_vars[$i]['old_value'] = $old_value;
                                }
                            }
                            $audit_vars[$i]['var_type'] = PH_VAR_ADDITIONAL;
                            $i++;
                        }
                    }
                }
            }
            break;
        case 'tag':
        case 'multitag':
            $i = count($audit_vars);

            if (in_array('tag', $basic_vars)) {
                $diff = array_diff($params['old_model']->get('tags'), $params['model']->get('tags'));
                $diff2 = array_diff($params['model']->get('tags'), $params['old_model']->get('tags'));
                if (!empty($diff) || !empty($diff2)) {
                    $new_var_value =
                        $params['model']->get('tag_names_for_audit') ?
                        implode("\n", $params['model']->get('tag_names_for_audit')) :
                        '';
                    $old_var_value =
                        $params['old_model']->get('tag_names_for_audit') ?
                        implode("\n", $params['old_model']->get('tag_names_for_audit')) :
                        '';
                    $audit_vars[$i]['field_name'] = 'tags';
                    $audit_vars[$i]['field_value'] = serialize($params['model']->get('tags'));
                    $audit_vars[$i]['label'] = $new_var_value;
                    $audit_vars[$i]['old_value'] = $old_var_value;
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = 1;
                }
            }
            break;
        case 'receive_email':
        case 'email':
            $basic_vars = array('mail_from', 'mail_code', 'mail_to', 'mail_cc', 'mail_bcc', 'mail_subject', 'mail_content', 'attached_files');
            $basic_vars_array = array('mail_to', 'mail_cc', 'mail_bcc', 'attached_files');
            $i = count($audit_vars);

            foreach ($basic_vars as $var) {
                $field_value = '';
                if (in_array($var, $basic_vars_array)) {
                    $mails = $params['model']->get($var) ?: array();
                    $mail_names = $params['model']->get("{$var}_name") ?: array();
                    $field_value = array();
                    foreach ($mails as $key => $value) {
                        $field_value[] = !empty($mail_names[$key]) ? $mail_names[$key] . ' (' . $value . ')' : $value;
                    }
                    $field_value = $field_value ? serialize($field_value) : '';
                } else {
                    switch ($var) {
                        case 'mail_subject':
                            $prop = 'email_subject';
                            break;
                        case 'mail_content':
                            $prop = 'body_formated';
                            break;
                        default:
                            $prop = $var;
                            break;
                    }
                    $field_value = $params['model']->get($prop) ?: '';
                }
                if ($field_value) {
                    $audit_vars[$i]['field_value'] = $field_value;
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = in_array($var, $basic_vars_array);
                    $audit_vars[$i]['label'] = '';
                    $i++;
                }
            }
            break;
        case 'add_comment':
        case 'edit_comment':
            $basic_vars = array('subject', 'content', 'is_portal');
            $comment = $params['model']->get('comment');
            $old_comment = $params['old_model']->get('comment');
            $i = count($audit_vars);
            $is_add_action = $params['action_type'] == 'add_comment';

            foreach ($basic_vars as $var) {
                if ($is_add_action && $comment->get($var) || !$is_add_action && $old_comment->get($var) != $comment->get($var) || $var == 'is_portal' && $comment->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $comment->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    if (isset($replace_labels[$var])) {
                        $audit_vars[$i]['label'] = $comment->get($replace_labels[$var]);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get($replace_labels[$var]);
                        }
                    } else {
                        $audit_vars[$i]['label'] = $comment->get($var);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get($var);
                        }
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'add_minitask':
            $auditable_minitask_vars = array('model_id', 'description', 'deadline', 'assigned_to', 'severity');
            $replace_labels['model_id'] = 'record_name';
            $replace_labels['assigned_to'] = 'assigned_to_name';
            $replace_labels['severity'] = 'severity_name';
            $minitask = $params['model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($minitask->isDefined($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['label'] = isset($replace_labels[$var]) ?
                                               $minitask->get($replace_labels[$var]) :
                                               $minitask->get($var);
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'edit_minitask':
            $auditable_minitask_vars = array('model_id', 'description', 'deadline', 'assigned_to', 'severity');
            $replace_labels['model_id'] = 'record_name';
            $replace_labels['assigned_to'] = 'assigned_to_name';
            $replace_labels['severity'] = 'severity_name';
            $minitask = $params['model']->get('minitask');
            $old_minitask = $params['old_model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($minitask->get($var) != $old_minitask->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    if (isset($replace_labels[$var])) {
                        $audit_vars[$i]['label'] = $minitask->get($replace_labels[$var]);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($replace_labels[$var]);
                    } else {
                        $audit_vars[$i]['label'] = $minitask->get($var);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($var);
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'status_minitask':
        case 'multistatus_minitask':
            $auditable_minitask_vars = array('status', 'comment');
            $minitask = $params['model']->get('minitask');
            $old_minitask = $params['old_model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($old_minitask->get($var) != $minitask->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    if ($var == 'status') {
                    $audit_vars[$i]['label'] = ($minitask->get('status') ? ($registry['translater']->translate('minitasks_status_' . $minitask->get('status'))) : ('-'));
                    $audit_vars[$i]['old_value'] = ($old_minitask->get('status') ? ($registry['translater']->translate('minitasks_status_' . $old_minitask->get('status'))) : ('-'));
                    } else {
                        $audit_vars[$i]['label'] = $minitask->get($var);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($var);
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        }

        $params['data'] = $audit_vars;
        return true;
    }

    /**
     * prepare audit data for view
     *
     * @return bool
     */
    public static function prepareGetData($records, $params) {
        foreach ($records as $k => $rec) {
            if ($rec['is_array']) {
                $arr = unserialize($rec['field_value']);
                if (empty($arr)) {
                    $arr = array();
                }
                if (is_array($arr)) {
                    if (count($arr) == count($arr, COUNT_RECURSIVE)) {
                        //array is NOT multidimensional, it is safe to implode it into string
                        $records[$k]['field_value'] = implode("\n", $arr);
                    } else {
                        //array is multidimensional, usually wrong assignments array is saved
                        //it should not be displayed
                        unset($records[$k]);
                        continue;
                    }
                }
            }
        }

        return $records;
    }

    /**
     * save model audit
     *
     * @return bool
     */
    public static function saveData(&$registry, $params) {
        self::prepareData($registry, $params);
        parent::saveData($registry, $params);

        //prepare data to be audited
        $vars = $params['model']->get('vars');
        if (!$vars) return true;
        $old_vars = $params['old_model']->get('vars');

        if (in_array($params['action_type'], array('clone', 'transform')) && !$params['model']->get('bb_id')) {
            // check for bb cstm variables and whether they have any saved values
            $bb_ids = array_filter($vars, function($var) {
                return $var['bb'] && $var['type'] != 'bb' && !$var['configurator'] && !$var['grouping'] && !$var['gt2'] && !empty($var['value']);
            });
            // get the ids of the bb rows from the keys of the cstm values
            if ($bb_ids) {
                $bb_ids = reset($bb_ids);
                $bb_ids = array_keys($bb_ids['value']);
            }
            // save audit of inner variables in each row
            if ($bb_ids) {
                $records = $params['model']->getBB(array('model_id' => $params['model']->get('id')));
                // cache the inner bb variables per variant
                $all_variants = array();

                /** @var Nomenclature $tmp_model */
                $tmp_model = clone $params['model'];
                $tmp_model->unsetVars();
                // keep old values in old model
                $tmp_model->old_model = clone $tmp_model;
                $tmp_model->old_model->sanitize();

                foreach ($records as $bb_row) {
                    $bb_id = $bb_row['id'];
                    if (!array_key_exists($bb_row['meta_id'], $all_variants)) {
                        $all_variants[$bb_row['meta_id']] = $tmp_model->getBBRowInnerFields(array('meta_id' => $bb_row['meta_id']));
                    }

                    $vars = $old_vars = array();
                    foreach ($all_variants[$bb_row['meta_id']] as $var_name => $var) {
                        if ($var['configurator'] || $var['grouping']) {
                            $default_value = $var['grouping'] || $var['type'] == 'checkbox_group' ? array() : '';
                            $vars[$var_name] = $old_vars[$var_name] = $var;
                            $old_vars[$var_name]['value'] = $default_value;
                            $vars[$var_name]['value'] = isset($bb_row['params'][$var_name]) ? $bb_row['params'][$var_name] : $default_value;
                        } elseif ($var['gt2'] && $var['type'] == 'gt2') {
                            $default_value = array_fill_keys(array('values', 'plain_values', 'rows'), array());
                            $old_vars[$var_name] = $default_value + $var;
                            $vars[$var_name] = ($bb_row['params'] ?: $default_value) + $var;
                        }
                    }
                    $tmp_model->set('bb_id', $bb_id, true);
                    $tmp_model->set('assoc_vars', $vars, true);
                    $tmp_model->set('vars', array_values($tmp_model->get('assoc_vars')), true);
                    $tmp_model->old_model->set('bb_id', $bb_id, true);
                    $tmp_model->old_model->set('assoc_vars', $old_vars, true);
                    $tmp_model->old_model->set('vars', array_values($tmp_model->old_model->get('assoc_vars')), true);

                    // save bb audit for the same history record as the main audit
                    self::saveData($registry, array(
                        'action_type' => self::BB_AUDIT_ACTION,
                        'parent_id' => $params['parent_id'],
                        'model_name' => $tmp_model->modelName,
                        'model' => $tmp_model,
                        'old_model' => $tmp_model->old_model,
                    ));
                }
                unset($tmp_model);
            }
        }

        return true;
    }

    /**
     * get model audit
     *
     * @return array data
     */
    public static function getData(&$registry, $params) {
        if (!empty($params['action_type']) && in_array($params['action_type'], array('add', 'edit'))) {
            $params['get_merged_bb_audit'] = true;
        }
        $records = parent::getData($registry, $params);
        $records = self::prepareGetData($records, $params);

        return array('vars' => $records);
    }
}

?>
