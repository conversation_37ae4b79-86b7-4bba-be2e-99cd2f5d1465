<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}

<form name="categories" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$category->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='categories_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$category->get('name')|escape}" title="{#nomenclatures_categories_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_ancestor"><label for="ancestor"{if $messages->getErrors('ancestor')} class="error"{/if}>{help label='categories_ancestor'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="ancestor" id="ancestor" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="dropdownTypingSearch(this, event);" title="{#nomenclatures_categories_ancestor#|escape}">
{foreach from=$categories_tree item='item'}
              {if (!$item->isDeleted() && $item->isActivated()) || $item->get('id') eq $category->get('ancestor')}
              <option value="{$item->get('id')}"{if $item->get('id') eq $category->get('ancestor')} selected="selected"{/if}{if $item->isDeleted() || !$item->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{$item->get('name')|indent:$item->get('level'):"-"}</option>
              {/if}
{/foreach}
            </select>
          </td>
        </tr>
        {include file='input_text.html'
                 name='position'
                 custom_id='position'
                 standalone=false
                 required=0
                 value=$category->get('position')
                 restrict='insertOnlyDigits'
                 custom_class='short'
                 label=#nomenclatures_categories_position#
                 help=#help_nomenclatures_categories_position#
        }
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='categories_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#nomenclatures_categories_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$category->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$category}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
