<div class="nz-page-wrapper{if !empty($side_panels) && count($side_panels)} nz--has-side-panel{/if}">
  <div class="nz-page-main-column nz-content-surface{if empty($_isPopup)} nz-elevation--z3{/if}">
    <div class="nz-page-title"><h1>{$title|escape}</h1>
      <div class="nz-page-title-tools">
        {if isset($available_page_actions.general)}
          {include file=`$theme->templatesDir`actions_box.html available_actions=$available_page_actions.general}
        {/if}
      </div>
      <div class="nz-page-title-sidetools">
        {if isset($available_page_actions.quick)}
          {include file=`$theme->templatesDir`actions_box.html available_actions=$available_page_actions.quick  onlyIcons=true}
        {/if}
        {*if isset($available_page_actions.infriquent)}
          {include file=`$theme->templatesDir`actions_box.html available_actions=$available_page_actions.infriquent onlyIcons=true}
        {/if*}
      </div>
    </div>
    <div class="nz-page-actions">
      {include file=`$theme->templatesDir`actions_box.html available_actions=$available_page_actions.context}
    </div>
    {include file=`$theme->templatesDir`_translations_menu.html translations=$translations}

    <div id="form_container" class="nz-page-content main_panel_container">
        <input type="hidden" name="id" id="id" value="{$nomenclature->get('id')}" />
        <input type="hidden" name="model_lang" id="model_lang" value="{$nomenclature->get('model_lang')|default:$lang}" />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              {assign var='layouts_vars' value=$nomenclature->get('vars')}
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              {foreach from=$nomenclature->get('layouts_details') key='lkey' item='layout'}
                {if $layout.view}

                {if $layout.system && ($lkey ne 'batch_options' || $nomenclature->get('subtype') eq 'commodity') || array_key_exists($layout.id, $layouts_vars)}
                <tr{if !$layout.visible} style="display: none;"{/if}>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                    </div>
                    <div class="layout_switch" {if $layout.system}onclick="toggleViewLayouts(this)" id="nomenclature_{$layout.keyword}_switch"{else}onclick="toggleLayouts(this)" id="layout_{$layout.id}_switch"{/if}>
                      <a name="nomenclature_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                    </div>
                  </td>
                </tr>
                {/if}

                {if $lkey eq 'type'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {$nomenclature->get('type_name')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'code'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {$nomenclature->get('code')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'num'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$nomenclature->get('num')|escape|default:"&nbsp;"}
                  </td>
                </tr>
                {elseif $lkey eq 'name'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {include file=`$templatesDir`_info.html assign='info'}
                    <span {popup text=$info|escape caption=#system_info#|escape width=250}>{mb_truncate_overlib text=$nomenclature->get('name')|escape|default:"&nbsp;"}</span>
                  </td>
                </tr>
                {elseif $lkey eq 'last_delivery_price'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$nomenclature->get('last_delivery_price')|escape} {$nomenclature->get('last_delivery_price_currency')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'average_weighted_delivery_price'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$nomenclature->get('average_weighted_delivery_price')|escape} {$nomenclature->get('average_weighted_delivery_price_currency')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'sell_price'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$nomenclature->get('sell_price')|escape} {$nomenclature->get('sell_price_currency')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'subtype'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {if $nomenclature->get('subtype') eq 'commodity' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'commodity'}
                      {#nomenclatures_subtype_commodity#|escape}
                    {elseif $nomenclature->get('subtype') eq 'service' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'service'}
                      {#nomenclatures_subtype_service#|escape}
                    {elseif $nomenclature->get('subtype') eq 'other' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'other'}
                      {#nomenclatures_subtype_other#|escape}
                    {elseif $nomenclature_type->get('keyword') eq 'system'}
                      {capture assign=subtype_label}nomenclatures_subtype_{$nomenclature->get('subtype')}{/capture}
                      {$smarty.config.$subtype_label|escape}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'batch_options' && $nomenclature->get('subtype') eq 'commodity'}
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {if $nomenclature->get('has_batch')}
                      {#nomenclatures_has_batch#|escape}
                      {if $nomenclature->get('has_serial')}/ {#nomenclatures_has_serial#|escape}{/if}
                      {if $nomenclature->get('has_expire')}/ {#nomenclatures_has_expire#|escape}{/if}
                      {if $nomenclature->get('has_batch_code')}/ {#nomenclatures_has_batch_code#|escape}{/if}
                    {else}
                      {#no#}
                    {/if}
                  </td>
                </tr>
                {elseif array_key_exists($layout.id, $layouts_vars)}
                <!-- Nomenclature Additional Vars -->
                  {assign var='layout_id' value=$layout.id}
                  {assign var='vars' value=$layouts_vars.$layout_id}
                  {if $layout.id}
                  <tr id="layout_{$layout.id}_box"{if $layout.cookie eq 'off'} style="display: none;"{/if}>
                    <td class="nopadding" colspan="3">
                      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                  {/if}
                  {foreach name='j' from=$vars item='var'}
                    {if $var.type}
                      {strip}
                      {capture assign='info'}{if $var.help}{$var.help}{else}{$var.label}{/if}{/capture}
                      {/strip}
                      {include file="view_`$var.type`.html"}
                    {/if}
                  {/foreach}
                  {if $layout.id}
                      </table>
                    </td>
                  </tr>
                  {/if}
                {elseif $lkey eq 'categories'}
                <!-- Nomenclature categories -->
                  {include file=`$templatesDir`_categories.html}
                {elseif $lkey eq 'attachments'}
                <!-- Nomenclature attachments -->
                <tr id="nomenclature_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td colspan="3" class="nopadding">
                    <a name="attachments"></a>
                    {include file=`$templatesDir`_attachments.html}
                  </td>
                </tr>
                {/if}

                {/if}
              {/foreach}
                {if $nomenclature->get('buttons')}
                  <tr>
                    <td colspan="3">&nbsp;</td>
                  </tr>
                  <tr>
                    <td colspan="3">
                      {strip}
                        {foreach from=$nomenclature->get('buttons') item='button'}
                          {include file=`$theme->templatesDir`input_button.html
                                  label=$button.label
                                  standalone=true
                                  name=$button.name
                                  source=$button.source
                                  disabled=$button.disabled
                                  hidden=$button.hidden
                                  width=$button.width
                                  height=$button.height}
                        {/foreach}
                      {/strip}
                    </td>
                  </tr>
                {/if}
              </table>
            </td>
          </tr>
        </table>
        {include file=`$theme->templatesDir`help_box.html}
        {include file=`$theme->templatesDir`system_settings_box.html object=$nomenclature exclude='is_portal'}
      </div>

  </div>
  {if isset($side_panels)}
    {include file=`$theme->templatesDir`_side_panel.html side_panels=$side_panels}
  {/if}
</div>

{if isset($side_panels) && in_array('related_records', $side_panels) && $related_records_modules}
  {capture var="subpanel_name"}related_subpanel_nomenclature{$nomenclature->get('id')}{/capture}
  {include file="`$theme->templatesDir`_related_records.html"
      related_records_modules=$related_records_modules
      subpanel_name = `$subpanel_name`
      coockiename = 'nomenclatures_selected_related_tab'
      available_actions_related_records = $available_actions_related_records
      session_params = $session_params}
{/if}

