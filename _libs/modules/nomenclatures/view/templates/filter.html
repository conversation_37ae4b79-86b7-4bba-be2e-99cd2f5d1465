<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=nomenclatures&amp;nomenclatures=filter&amp;{if $smarty.get.autocomplete_filter}autocomplete_filter=session&amp;{/if}{if $smarty.request.uniqid}uniqid={$smarty.request.uniqid}&amp;{/if}page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="nomenclatures" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=filter&amp;group_table={$gr_table}&amp;select_one={$select_one}" method="post" enctype="multipart/form-data">
      {if $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
          {if !$autocomplete_params || $autocomplete_params.select_multiple}
            {include file="`$theme->templatesDir`_select_items.html"
              pages=$pagination.pages
              total=$pagination.total
              session_param=$session_param|default:$pagination.session_param
            }
          {else}
            {assign var='hide_selection_stats' value=true}
          {/if}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.code.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.code.link}">{#nomenclatures_code#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#nomenclatures_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#nomenclatures_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.category.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.category.link}">{#nomenclatures_categories#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$nomenclatures item='nomenclature'}
      {strip}
      {capture assign='info'}
        <strong><u>{#nomenclatures_code#|escape}:</u></strong> {$nomenclature->get('code')|escape}<br />
        <strong>{#nomenclatures_type#|escape}:</strong> {$nomenclature->get('type_name')|escape}<br />
        <strong>{#nomenclatures_name#|escape}:</strong> {$nomenclature->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$nomenclature->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclature->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$nomenclature->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclature->get('modified_by_name')|escape}<br />
        {if $nomenclature->isDeleted()}<strong>{#deleted#|escape}:</strong> {$nomenclature->get('deleted')|date_format:#date_mid#|escape}{if $nomenclature->get('deleted_by_name')} {#by#|escape} {$nomenclature->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$nomenclature->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $nomenclature->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        {if !$nomenclature->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$nomenclature->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="5" class="dimmed">{#error_right_notallowed#|escape}</td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$nomenclature->get('active')} t_inactive{/if}{if $nomenclature->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$nomenclature->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
            {else}
                <input type="checkbox"
                   onclick="setCheckAllBox(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                           {rdelim});" 
                   name='items[]' 
                   value="{$nomenclature->get('id')}"
                   title="{#check_to_include#|escape}" />
           {/if}
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.code.isSorted}">{$nomenclature->get('code')|escape|default:'&nbsp;'}</td>
          <td class="t_border {$sort.name.isSorted}">{$nomenclature->get('name')|escape|default:'&nbsp;'}</td>
          <td class="t_border {$sort.type.isSorted}">{$nomenclature->get('type_name')|escape}</td>
          <td class="t_border {$sort.category.isSorted}">&nbsp;
            {if $nomenclature->get('categories_names')}
              {foreach name='cn' from=$nomenclature->get('categories_names') item='cat_name'}
                {$cat_name|escape}{if !$smarty.foreach.cn.last},{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {/if}
          </td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclature exclude='edit,delete,view'}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="7">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
      <br />
      <br />
      {if $smarty.request.autocomplete_filter}
        {strip}
        {if $autocomplete_params.select_multiple}
          <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
        {/if}
        <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
        {/strip}
      {/if}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
