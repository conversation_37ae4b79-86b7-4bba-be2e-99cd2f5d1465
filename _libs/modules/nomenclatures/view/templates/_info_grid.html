<div><strong>{$basic_vars_labels.name|default:#nomenclatures_type#|escape}</strong>{literal}: ${properties.name}</div>
<div>{/literal}<strong>{#added#|escape}</strong>{literal}: ${Nz.formatDate(properties.added)}<br>
  {/literal}{#by#|escape}{literal} ${properties.added_by_name}</div>
<div>{/literal}<strong>{#modified#|escape}</strong>{literal}: ${Nz.formatDate(properties.modified)}<br>
  {/literal}{#by#|escape}{literal} ${properties.modified_by_name}</div>
${if(properties.deleted_by_name)}
<div>{/literal}<strong>{#deleted#|escape}</strong>{literal}: ${Nz.formatDate(properties.deleted)}<br>
  {/literal}{#by#|escape}{literal} ${properties.deleted_by_name}</div>
${/if}
<div>
  <strong>{/literal}{#translations#|escape}{literal}:</strong>
  <span class="translations">
                  ${for(trans of properties.translations)}
                    <img src="{/literal}{$theme->imagesUrl}flags{literal}/${trans}.png" alt="${trans}" title="${trans}" border="0" align="absmiddle"${if(trans==properties.model_lang)} class="selected"${/if} />
                  ${/for}
                </span>
</div>
{/literal}
