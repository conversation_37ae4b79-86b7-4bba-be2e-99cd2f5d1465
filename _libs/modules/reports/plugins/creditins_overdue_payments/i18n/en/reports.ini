reports_filter_payments_count = Overdue payments
reports_filter_not_performed_action = No action commited
reports_employee = Assigned employee/company
reports_payment_date = Payment from/to date
reports_date_deadline = Deadline date
reports_show_contract_wo_payments = Show only overdue contracts w/o payment

reports_filter_payments_count_options_1 = 1 payment
reports_filter_payments_count_options_2 = 2 payments
reports_filter_payments_count_options_3 = 3 payments
reports_filter_payments_count_options_4 = 4 or more payments
reports_filter_not_performed_action_options_1 = more than 1 week
reports_filter_not_performed_action_options_2 = more than 2 weeks
reports_filter_not_performed_action_options_4 = more than a month
reports_filter_not_performed_action_options_5 = no communication

reports_send_email = Send email
reports_make_processding = Make proceeding
reports_add_call = Add call
reports_client_ucn = Client (UCN)
reports_address_phone = Address (phone)
reports_contact_person = Contact person (phone)
reports_office = Office
reports_responsible_person_company = Assigned employee/company
reports_paid_before_assign = Paid before assign
reports_paid_after_assign = Paid after assign
reports_overdue_payments = Overdue payments
reports_paid_payments = Completed payments (Total payments)
reports_contract_num_date = Contract num (date)
reports_total_owed_sum = Total owed
reports_overdue_principal = Overdue principal
reports_overdue_interest = Overdue interest
reports_overdue_warranty = Overdue warranty
reports_overdue_penalty = Overdue penalty
reports_total_overdue_sum = Total overdue
reports_paid_owed_sum = Total paid
reports_payments = Payments (amount/date)
reports_sent_sms = Sent sms
reports_sent_emails = Sent e-mails
reports_made_calls = Client calls
reports_workplace = Workplace

reports_call_date = Call date
reports_call_option_description = Option (call description)
reports_call_promised_pay_date = Declared payment date
reports_call_promised_all_calls = All calls
reports_show_all_calls = show all

reports_create_proceedings = Make proceeding
reports_add_call = Add call

report_owed_sums_to_credit_ins = Sums owed to CreditIns
reports_send_emails = Sent mails for the overdue sums
error_reports_missing_patterns = No e-mails can be sent without print or e-mail patterns!

message_report_added_project_successfully = Successfully created <a href='%s' target='_blank'>proceeding</a> from <a href='%s' target='_blank'>loan contract %s</a> for customer %s!

reports_no_payments = no payments
error_reports_complete_required_filters = Please, complete required filters!
error_reports_project_add_failed = Adding proceeding for contract %s failed
error_reports_document_status_change_failed = Status change of loan contract %s failed! Please, contact the nZoom support!
error_reports_contract_to_proceeding_issue_correct_document = Issuing a correction for the incomes reason for the contract %s failed! Please, contact the nZoom support!
error_reports_proceeding_already_added = For contract %s (client: %s) there is a proceeding already added (%s)! Please, reload the report and try again!
error_reports_mail_already_sent = For contract %s (client: %s) there is an e-mail sent already.  Please, reload the report and try again!
