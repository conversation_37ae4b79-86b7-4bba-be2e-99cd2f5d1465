<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
        {if $reports_additional_options.total_owed_dew}
          <tr class="t_even">
            <td colspan="4"><strong>{#reports_total_owed_dew#|escape}:</strong></td>
            <td class="hright"><strong>{$reports_additional_options.total_owed_dew|default:"0,00"}</strong></td>
          </tr>
        {/if}
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_full_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_tourist_count#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_price_per_tourist#|escape}</div></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}">
            <td class="t_border hright" nowrap="nowrap" width="25">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
            </td>
            <td class="t_border">
              {$result.customer|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$result.tourist_count|default:"0"}
            </td>
            <td class="hright">
              {$result.price_per_tourist|default:"0,00"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="5">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="5"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>