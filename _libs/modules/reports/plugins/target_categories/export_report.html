<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    {literal}
    <style type="text/css">
      br { mso-data-placement: same-cell; }
    </style>
    {/literal}
  </head>
  <body>
    {if isset($report_filters.protocol_customer) && !empty($report_filters.protocol_customer.value)}
    <h3>{help label_content=$report_filters.protocol_customer.label} {$report_filters.protocol_customer.value_autocomplete}</h3>
    {/if}
    <table border="1" cellpadding="5" cellspacing="0">
      <tr style="background-color: #dfdfdf;">
        <td nowrap="nowrap" align="center"><strong>{#num#|escape}</strong></td>
        <td nowrap="nowrap" align="center"><strong>{#reports_chain#|escape}</strong></td>
        <td nowrap="nowrap" align="center" colspan="3"><strong>{#reports_object#|escape}</strong></td>
        <td nowrap="nowrap" align="center" colspan="3"><strong>{#reports_visit#|escape}</strong></td>
        <td nowrap="nowrap" align="center" colspan="3"><strong>{$reports_additional_options.labels[$smarty.const.CATEGORY_NAME_VAR]|default:#reports_category#|escape}</strong></td>
        <td nowrap="nowrap" align="center" width="60"><strong>{$reports_additional_options.labels[$smarty.const.VFACE_VAR]|default:#reports_vface#|escape}</strong></td>
        <td nowrap="nowrap" align="center" width="60"><strong>{$reports_additional_options.labels[$smarty.const.CFACE_VAR]|default:#reports_cface#|escape}</strong></td>
        <td nowrap="nowrap" align="center" width="60"><strong>%</strong></td>
        <td nowrap="nowrap" align="center"><strong>{$reports_additional_options.labels[$smarty.const.PICTURE_VAR]|default:#reports_picture#|escape}</strong></td>
        <td nowrap="nowrap" align="center" colspan="3"><strong>{$reports_additional_options.labels[$smarty.const.NOTES_VAR]|default:#reports_notes#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results key='rk' name='ri' item='chain'}
        {foreach from=$chain.objects name='oi' item='object'}
          {foreach from=$object.visits key='vk' name='vi' item='visit'}
            {foreach from=$visit.categories name='ci' item='category'}
              <tr>
                {if $smarty.foreach.ci.first}
                {if $smarty.foreach.vi.first}
                {if $smarty.foreach.oi.first}
                <td valign="middle" style="text-align: right; vertical-align: middle;" rowspan="{$chain.rowspan}">{$smarty.foreach.ri.iteration}</td>
                <td valign="middle" style="vertical-align: middle;" rowspan="{$chain.rowspan}">
                  {$chain.name|escape|default:"&nbsp;"}
                </td>
                {/if}
                <td valign="middle" style="vertical-align: middle;" rowspan="{$object.rowspan}" colspan="3">
                  {$object.name|escape|default:"&nbsp;"}
                </td>
                {/if}
                <td valign="middle" style="vertical-align: middle;" rowspan="{$visit.rowspan}" colspan="3">
                  {$visit.name|escape|default:"&nbsp;"} ({$visit.date|date_format:#date_short#})<br />{#added_by#|escape}: {$visit.added_by_name|escape}
                </td>
                {/if}
                <td colspan="3">
                  {$category.name|escape|default:"&nbsp;"}
                </td>
                <td style="text-align: right; mso-number-format: '0\.00';">
                  {$category.vface|escape|default:"&nbsp;"}
                </td>
                <td style="text-align: right; mso-number-format: '0\.00';">
                  {$category.cface|escape|default:"&nbsp;"}
                </td>
                <td style="text-align: right; mso-number-format: '0\.00';">
                  {$category.percentage|escape|default:0}
                </td>
                <td align="center">
                  {if $category.picture}
                    {#yes#|escape}
                  {else}
                    &nbsp;
                  {/if}
                </td>
                <td colspan="3" style="mso-number-format: \@;">
                  {$category.notes|escape|nl2br|url2href|default:"&nbsp;"}
                </td>
              </tr>
            {/foreach}
          {/foreach}
        {/foreach}
      {foreachelse}
        <tr>
          <td style="color: red;" colspan="18">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
    <br />
    <table border="1" cellpadding="5" cellspacing="0">
      <tr style="background-color: #dfdfdf;">
        <td align="center" valign="middle" style="vertical-align: middle;" {if $reports_results} rowspan="3"{/if} colspan="2"><strong>{$reports_additional_options.labels[$smarty.const.CATEGORY_NAME_VAR]|default:#reports_category#|escape}</strong></td>
        {assign var='total_colspan' value=0}
        {foreach from=$reports_results key='rk' name='ri' item='chain'}
          {capture assign='chain_objects'}{if is_array($chain.objects)}{$chain.objects|@count}{else}0{/if}{/capture}
          {math assign='total_colspan' equation='t+n*3' t=$total_colspan n=$chain_objects}
          <td align="center" colspan="{math equation='n*3' n=$chain.objects|@count}" style="white-space: pre;"><strong>{$chain.name|escape|default:"&nbsp;"}</strong></td>
        {foreachelse}
          {assign var='total_colspan' value=16}
          <td colspan="{$total_colspan}">&nbsp;</td>
        {/foreach}
      </tr>
      {if $reports_results}
      <tr style="background-color: #dfdfdf;">
        {foreach from=$reports_results key='rk' name='ri' item='chain'}
          {foreach from=$chain.objects name='oi' item='object'}
            <td colspan="3" align="center"><strong>{$object.name|escape|default:"&nbsp;"}</strong></td>
          {/foreach}
        {/foreach}
      </tr>
      <tr style="background-color: #dfdfdf;">
      {foreach from=$reports_results key='rk' name='ri' item='chain'}
        {foreach from=$chain.objects name='oi' item='object'}
          <td width="60" align="center"><strong>{$reports_additional_options.labels[$smarty.const.VFACE_VAR]|default:#reports_vface#|escape}</strong></td>
          <td width="60" align="center"><strong>{$reports_additional_options.labels[$smarty.const.CFACE_VAR]|default:#reports_cface#|escape}</strong></td>
          <td width="60" align="center"><strong>%</strong></td>
        {/foreach}
      {/foreach}
      </tr>
      {foreach from=$reports_additional_options.categories key='ck' name='ci' item='category'}
        <tr>
          <td style="text-align: right; width: 30px;">{$smarty.foreach.ci.iteration}</td>
          <td>{$category|escape|default:"&nbsp;"}</td>
          {foreach from=$reports_results key='rk' name='ri' item='chain'}
            {foreach from=$chain.objects name='oi' item='object'}
              <td style="text-align: right; mso-number-format: '0\.00';">{if isset($object.categories_avg.$ck)}{$object.categories_avg.$ck.vface|default:0}{else}&nbsp;{/if}</td>
              <td style="text-align: right; mso-number-format: '0\.00';">{if isset($object.categories_avg.$ck)}{$object.categories_avg.$ck.cface|default:0}{else}&nbsp;{/if}</td>
              <td style="text-align: right; mso-number-format: '0\.00';">{if isset($object.categories_avg.$ck)}{$object.categories_avg.$ck.percentage|default:0}{else}&nbsp;{/if}</td>
            {/foreach}
          {/foreach}
        </tr>
      {/foreach}
      <tr>
        <td>&nbsp;</td>
        <td style="text-align: right;"><strong>{help label_content=#total#}</strong></td>
        {foreach from=$reports_results key='rk' name='ri' item='chain'}
          {foreach from=$chain.objects name='oi' item='object'}
            <td style="text-align: right; mso-number-format: '0\.00';">{$object.vface_total|default:0}</td>
            <td style="text-align: right; mso-number-format: '0\.00';">{$object.cface_total|default:0}</td>
            <td style="text-align: right; mso-number-format: '0\.00';">{$object.percentage_total|default:0}</td>
          {/foreach}
        {/foreach}
      </tr>
      {else}
        <tr>
          <td style="color: red;" colspan="{math equation='2+t' t=$total_colspan}">{#no_items_found#|escape}</td>
        </tr>
      {/if}
    </table>
  </body>
</html>
