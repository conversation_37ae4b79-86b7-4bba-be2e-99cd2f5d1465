{if !$reports_additional_options.error}
  {if empty($reports_results)}
    <span class="error">{#no_items_found#|escape}</span>
  {else}
    <table border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td>
          {if empty($report_filters.uninvoiced_contracts.value)}
            <table class="reports_table">
              <tr class="reports_title_row">
                <th width="200">{#reports_th_client#}</th>
                <th width="80">{#reports_th_contract#}</th>
                <th width="140">{#reports_th_type_contract#}</th>
                <th width="200">{#reports_th_contact_person#}</th>
                <th width="80">{#reports_th_due_contracts#}</th>
                <th width="80">{#reports_th_due_sells#}</th>
                <th width="60">{#reports_th_payment_with_vat#}</th>
                <th width="80">{#reports_th_payment_status#}</th>
                <th width="60">{#reports_th_rest_to_pay#}</th>
                <th width="60">{#reports_th_invoice_sent#}</th>
              </tr>
              {foreach from=$reports_additional_options.results item='customer' key='customer_id' name='customers'}
                {assign var='first_sell' value=true}
                {foreach from=$customer.reasons item='reason' name='reasons'}
                  {foreach from=$reason.invoices item='invoice' key='invoice_id' name='invoices'}
                    <tr>
                      {if $smarty.foreach.reasons.first && $smarty.foreach.invoices.first}
                        <td rowspan="{$customer.rowspan}">
                          {if !empty($customer.name)}
                            <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$customer_id}">
                              {$customer.name|escape}
                            </a>
                          {/if}
                        </td>
                      {/if}
                      {if $reason.model_name eq 'Contract'}
                        {if $smarty.foreach.invoices.first}
                          <td rowspan="{$reason.rowspan}" class="hcenter">
                            {if !empty($reports_additional_options.contracts[$reason.id].num)}
                              <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$reason.id}">
                                {$reports_additional_options.contracts[$reason.id].num|escape}
                              </a>
                            {/if}
                          </td>
                          <td rowspan="{$reason.rowspan}">{$reports_additional_options.contracts[$reason.id].type_name|escape}</td>
                          <td rowspan="{$reason.rowspan}">
                            {if !empty($reports_additional_options.contracts[$reason.id].contact_person_name)}
                              <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$reports_additional_options.contracts[$reason.id].contact_person_id}">
                                {$reports_additional_options.contracts[$reason.id].contact_person_name|escape}
                              </a>
                            {/if}
                          </td>
                        {/if}
                      {else}
                        {if $first_sell}
                          <td rowspan="{$customer.sells_rowspan}" class="hcenter">-</td>
                          <td rowspan="{$customer.sells_rowspan}" class="hcenter">-</td>
                          <td rowspan="{$customer.sells_rowspan}" class="hcenter">-</td>
                        {/if}
                        {assign var='first_sell' value=false}
                      {/if}
                      {if $reason.model_name ne 'Contract'}
                        <td class="hcenter">-</td>
                      {/if}
                      <td class="hcenter">
                        {if !empty($invoice.num)}
                          <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view={$invoice_id}">
                            {$invoice.num|escape}
                          </a>
                        {/if}
                      </td>
                      {if $reason.model_name eq 'Contract'}
                        <td class="hcenter">-</td>
                      {/if}
                      <td class="hright">
                        {$invoice.total_with_vat|number_format:2:".":" "}
                      </td>
                      <td>
                        {capture assign='payment_status'}finance_payment_status_{$invoice.payment_status}{/capture}
                        {$smarty.config.$payment_status|escape}
                      </td>
                      <td class="hright">
                        {math equation=x-y x=$invoice.total_with_vat|string_format:"%.2F" y=$invoice.full_paid_amount assign='rest_to_pay'}
                        {$rest_to_pay|number_format:2:".":" "}
                      </td>
                      <td class="hcenter">
                        {if isset($reports_additional_options.sent_invoices[$invoice_id])}
                          {#yes#}
                        {else}
                          {#no#}
                        {/if}
                      </td>
                    </tr>
                  {/foreach}
                {/foreach}
              {/foreach}
            </table>
          {else}
            <table class="reports_table">
              <tr class="reports_title_row">
                <th width="200">{#reports_th_client#}</th>
                <th width="80">{#reports_th_contract#}</th>
                <th width="140">{#reports_th_type_contract#}</th>
                <th width="200">{#reports_th_contact_person#}</th>
                <th width="300">{#reports_th_comments#}</th>
              </tr>
              {foreach from=$reports_additional_options.results item='customer' key='customer_id' name='customers'}
                {foreach from=$customer.contracts item='contract' key='contract_id' name='contracts'}
                  <tr>
                    {if $smarty.foreach.contracts.first}
                      <td rowspan="{$customer.rowspan}">
                        {if !empty($customer.name)}
                          <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$customer_id}">
                            {$customer.name|escape}
                          </a>
                        {/if}
                      </td>
                    {/if}
                    <td class="hcenter">
                      {if !empty($contract_id)}
                        <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$contract_id}">
                          {$contract.num|escape|default:#contracts_unfinished_contract#}
                        </a>
                      {/if}
                    </td>
                    <td>{$contract.type_name|escape}</td>
                    <td>
                      {if !empty($contract.contact_person_name)}
                        <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$contract.contact_person_id}">
                          {$contract.contact_person_name|escape}
                        </a>
                      {/if}
                    </td>
                    {if isset($reports_additional_options.comments_contracts[$contract_id])}
                      {if $reports_additional_options.comments_contracts[$contract_id]|strip_tags|mb_strlen gt 80}
                        <td style="cursor: pointer;" onclick="$(this).select('div.comment_content')[0].setAttribute('style', 'max-height: 100%; overflow: visible;'); $(this).select('span.collapsed_dots')[0].style.display = 'none'; $(this).removeAttribute('onclick'); $(this).style.cursor = '';">
                          <div class="comment_content" style="max-height: 24px; overflow: hidden;">
                              {$reports_additional_options.comments_contracts[$contract_id]}
                          </div>
                          <span class="collapsed_dots" style="float: right;">...</span>
                        </td>
                      {else}
                        <td>
                          {$reports_additional_options.comments_contracts[$contract_id]}
                        </td>
                      {/if}
                    {else}
                      <td>&nbsp;</td>
                    {/if}
                  </tr>
                {/foreach}
              {/foreach}
            </table>
          {/if}
        </td>
      </tr>
      <tr>
        <td class="pagemenu">
          {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
          {include file="`$theme->templatesDir`pagination.html"
            found=$pagination.found
            total=$pagination.total
            rpp=$pagination.rpp
            page=$pagination.page
            pages=$pagination.pages
            link=$link
            stat_items_sequence=1
            hide_selection_stats=1
          }
        </td>
      </tr>
    </table>
  {/if}
{/if}