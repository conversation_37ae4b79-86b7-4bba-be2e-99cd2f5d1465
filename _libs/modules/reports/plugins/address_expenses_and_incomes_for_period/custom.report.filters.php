<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // Hardcoded settings
            $hardcoded_settings = array(
                'cus_type_own_company' => '2',
                'nom_type_region' => '6',
            );

            // Encapsulate the registry for local use
            self::$registry = $registry;

            // Prepare array for all filters
            $filters = array();

            // Filter: Period
            $this->loadDefaultFilter($registry, $filters, 'period_from_to');
            $filters['period_from']['required'] = true;

            // Filter: Own company
            $filters['customer'] = array(
                'name'         => 'customer',
                'type'         => 'autocompleter',
                'label'        => $this->i18n('reports_filter_customer'),
                'autocomplete' => array(
                    'type' => 'customers',
                    'fill_options' => array(
                        '$customer => <id>',
                        '$customer_autocomplete => <name>',
                        '$customer_oldvalue => <name>'
                    ),
                    'suggestions' => '<name>',
                    'combobox' => '1',
                    'combobox_mode' => 'empty',
                    'filters' => array(
                        '<type>' => $hardcoded_settings['cus_type_own_company'],
                    ),
                    'url' => $_SERVER['PHP_SELF'] . '?' . $registry['module_param'] . '=customers&customers=ajax_select',
                    'buttons' => 'clear'
                )
            );
            $company_label_setting = strtoupper('filter_company_lbl_' . $registry['lang']);
            if (defined($company_label_setting) && constant($company_label_setting)) {
                $filters['customer']['label'] = $filters['customer']['help'] = constant($company_label_setting);
            }

            // Filter: Region
            $filters['parent_entity'] = array(
                'name'            => 'parent_entity',
                'type'            => 'custom_filter',
                'actual_type'     => 'autocompleter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'label'           => $this->i18n('reports_filter_parent_entity'),
                'width'           => 222,
                'autocomplete'    => array(
                    'type' => 'nomenclatures',
                    'fill_options' => array(
                        '$parent_entity => <id>',
                        '$parent_entity_autocomplete => <name>',
                        '$parent_entity_oldvalue => <name>'
                    ),
                    'suggestions' => '<name>',
                    'combobox' => '1',
                    'combobox_mode' => 'empty',
                    'filters' => array(
                        '<type>' => $hardcoded_settings['nom_type_region'],
                    ),
                    'url' => $_SERVER['PHP_SELF'] . '?' . $registry['module_param'] . '=nomenclatures&nomenclatures=ajax_select',
                    'buttons' => 'clear'
                )
            );
            $region_label_setting = strtoupper('filter_region_lbl_' . $registry['lang']);
            if (defined($region_label_setting) && constant($region_label_setting)) {
                $filters['parent_entity']['label'] = $filters['parent_entity']['help'] = constant($region_label_setting);
            }

            // Filter: entity
            $filters['entity'] = array(
                'name'            => 'entity',
                'type'            => 'custom_filter',
                'actual_type'     => 'autocompleter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'label'           => $this->i18n('reports_filter_entity'),
                'width'           => 222,
                'autocomplete'    => array(
                    'clear'         => 1,
                    'combobox'      => 1,
                    'combobox_mode' => 'empty',
                    'type'          => 'autocompleters',
                    'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'autocompleters', 'autocompleters', 'ajax_select'),
                    'plugin_search' => 'customQuery',
                    'plugin_params' => array(
                        'comp'    => '$customer',
                        'sql'     => "SELECT n.id, ni.name, nti18n.name as type_name " .
                            "FROM nom AS n JOIN nom_cstm AS nc1 ON (n.type = 7 AND !n.deleted AND n.active AND nc1.model_id = n.id AND nc1.var_id = 6005 AND nc1.num = 1 AND nc1.lang = '') " .
                            "JOIN nom_cstm as nc7 ON n.id=nc7.model_id AND nc7.var_id=5601 " .
                            "JOIN customers_cstm AS cc1 ON (cc1.model_id = {$registry['currentUser']->get('employee')} AND cc1.var_id = 103 AND cc1.num = 1 AND cc1.lang = '') " .
                            "JOIN nom_cstm AS nc2  ON (nc2.var_id = 6005 AND nc2.model_id = cc1.value AND nc2.num = 1 AND nc2.lang = '') " .
                            "JOIN customers_cstm AS cc2 ON (cc2.model_id = cc1.model_id AND cc2.var_id = 109 AND cc2.num = 1 AND cc2.lang = '' AND IF(cc2.value IN (336, 334), nc1.value = nc2.value, true)) " .
                            "JOIN nom_i18n AS ni ON (ni.parent_id = n.id AND ni.lang = '{$registry['lang']}') " .
                            "LEFT JOIN nom_types_i18n nti18n ON n.type=nti18n.parent_id AND nti18n.lang='{$registry['lang']}' " .
                            "LEFT JOIN tags_models t ON n.id=t.model_id AND t.model='Nomenclature' AND t.tag_id=2 " .
                            "WHERE nc7.value=<comp> AND t.model_id is NULL AND ni.name LIKE \"%<search_string_parts>%\" ORDER BY ni.name ASC",
                    ),
                    'suggestions'   => '<name> [<type_name>]',
                    'fill_options'  => array(
                        '$entity => <id>',
                        '$entity_oldvalue => <name>',
                        '$entity_autocomplete => <name>'
                    ),
                    'buttons'  => 'clear'
                ),
            );
            $office_label_setting = strtoupper('filter_office_lbl_' . $registry['lang']);
            if (defined($office_label_setting) && constant($office_label_setting)) {
                $filters['entity']['label'] = $filters['entity']['help'] = constant($office_label_setting);
            }

            // Filter: Kind
            $filters['kind'] = array(
                'name' => 'kind',
                'type' => 'radio',
                'options' => array(
                    array(
                        'label' => $this->i18n('reports_filter_kind_all'),
                        'option_value' => 'all'
                    ),
                    array(
                        'label' => $this->i18n('reports_filter_kind_expenses'),
                        'option_value' => 'expenses'
                    ),
                    array(
                        'label' => $this->i18n('reports_filter_kind_incomes'),
                        'option_value' => 'incomes'
                    )
                ),
                'label' => $this->i18n('reports_filter_kind')
            );

            // Filter: Range
            $filters['range_from'] = array(
                'name' => 'range_from',
                'type' => 'custom_filter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_from_to.html',
                'additional_filter' => 'range_to',
                'label' => $this->i18n('reports_filter_range'),
                'help' => $this->i18n('reports_filter_range_help')
            );
            $filters['range_to'] = array(
                'name' => 'range_to'
            );

            // Custom export button
            $filters['custom_export'] = array(
                'name' => 'custom_export',
                'type' => 'custom_filter',
                'custom_template'   => PH_MODULES_DIR . "reports/plugins/{$registry['report_type']['name']}/_filter_export_button.html"
            );

            return $filters;
        }

        public function processDependentFilters(&$filters) {
            // Get the registry
            $registry = &self::$registry;

            // Hide the standard "Generate" button
            // $registry->set('hide_generate_button', true, true);

            // Process additional filters
            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            // Set default value for filter Kind
            if ($filters['kind']['value'] == '') {
                $filters['kind']['value'] = 'all';
            }
            $filters['custom_export']['hidden'] = true;

            return $filters;
        }
    }
?>
