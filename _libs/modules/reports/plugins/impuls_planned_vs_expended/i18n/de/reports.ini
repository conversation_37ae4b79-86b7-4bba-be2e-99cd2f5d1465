reports_general_report_option = Gesamt
reports_financial_resources_option = finanzielle Mittel
reports_transport_option = Mobile Verkehrstechnik
reports_materials_option = Material
reports_warehouse_option = Lager/ZBUT/IST

reports_general_report = Gesamtbericht:
reports_warehouse = Lager/ZBUT/IST
reports_transport = Mobile Verkehrstechnik
reports_financial = finanzielle Mittel
reports_materials = Material
reports_request_status = Status der Anfrage
reports_request_currency = Währung
reports_from_date = von Datum
reports_to_date = Bis Datum
reports_project = Objekt / Projekt
reports_pps_machine = PPS / Maschine
reports_requested_by = Beantragt von Einheit
reports_company = Unternehmen
reports_nomenclature = Nomenklatur
reports_expense_description = Beschreibung des Aufwands
reports_deliverer = Wird erhalten (Lieferant)
reports_employee = Wird erhalten (Mitarbeiter)
reports_warehouse_type_expense = Typ des Aufwands
reports_transport_type_expense = Typ des Aufwands
reports_type_pps = Fahrzeugart
reports_financial_type_expense = Typ des Aufwands
reports_materials_type_expense = Typ des Aufwands
reports_spec_materials = Spezialmaterial

reports_undefined_type_expense = [nicht definierte Kosten]

reports_all_statuses = alle Anfragen
reports_approved = genehmigt
reports_approved_and_executed = genehmigt und erfüllt
reports_approved_and_partially_executed = genehmigt und teilweise erfüllt
reports_disapproved = nicht genehmigt
reports_company_impulse_co = ICTVP
reports_company_impulse_project = Ganimed

reports_exp_invoice_prefix = F
reports_exp_proforma_prefix = PI

reports_warehouse_optlabel_warehouse = Lager
reports_warehouse_optlabel_zbut = ZBUT
reports_warehouse_optlabel_ist = IST

reports_request_num = Anfrage №
reports_request_date = Datum
reports_request_type = Typ der Anfrage
reports_receive_branch = Objekt zum Erhalten/ TS
reports_expense_type = Typ des Aufwands
reports_expense_description_lbl = Beschreibung
reports_quantity = Menge
reports_price = Preis
reports_value = Betrag
reports_total_value = Gesamtbetrag
reports_total_value_with_vat = Betrag mit MwSt.
reports_total_value_invoice = Betrag F/PF
reports_difference = Differenz
reports_invoice_num_date = F/PF № (von Datum)
reports_note = Anmerkung
reports_received_by = Erhalten
reports_requested_by_unit = Beangtragt von Einheit

reports_no_number = [keine Nummer]

error_reports_complete_required_fields = Bitte Filter für Status der Anfrage ausfüllen!
error_reports_complete_type_table_filter = Bitte Filter für Typ der Tabelle ausfüllen (Allgemeines / finanzielle Mittel / Kraftstoffe / Material / Sonstige)
