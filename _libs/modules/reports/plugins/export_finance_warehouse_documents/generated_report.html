<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 30px;">{#reports_num#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 180px;">{#reports_customer#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 80px;">{#reports_customer_eik#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 80px;">{#reports_num_doc#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 60px;">{#reports_doc_date#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_total_sum#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_discount#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_total_with_discount#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_total_vat_percent#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_total_vat_value#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_total_with_vat#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 90px;">{#reports_nom_type#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 180px;">{#reports_nom_category#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 80px;">{#reports_nom_code#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 200px;">{#reports_nom_name#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 250px;">{#reports_nom_description#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 40px;">{#reports_nom_measure#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 40px;">{#reports_nom_quantity#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_nom_sell_price#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_nom_delivery_price#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;">
            <div style="width: 70px;">
              <img src="{$theme->imagesUrl}small/info.png" border="0" alt="" {popup text=#reports_nom_avg_price_help#|escape caption=#reports_nom_avg_price#|escape} />
              {#reports_nom_avg_price#|escape}
            </div>
          </td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_nom_value#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 140px;">{#reports_handover_status#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 200px;">{#reports_warehouse_in#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;"><div style="width: 200px;">{#reports_warehouse_out#|escape}</div></td>
        </tr>
        {foreach from=$reports_results item=doc name='dc'}
          {capture assign='current_row_class'}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          {counter name='doc_count' assign='doc_count_num'}
          {foreach from=$doc.gt2 item='gt2' name='gt2_row'}
            <tr class="{$current_row_class}">
              {if $smarty.foreach.gt2_row.first}
                <td class="t_border hright" rowspan="{$doc.rowspan}">{$doc_count_num}</td>
                <td class="t_border" rowspan="{$doc.rowspan}">{$doc.customer_name|escape|default:"&nbsp;"}</td>
                <td class="t_border" rowspan="{$doc.rowspan}">{$doc.eik|escape|default:"&nbsp;"}</td>
                <td class="t_border" rowspan="{$doc.rowspan}">
                  <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller={$doc.controller}&amp;{$doc.controller}=view&amp;view={$doc.id}">
                    {if $doc.num}{$doc.num|escape|default:"&nbsp;"}{else}<i>{#no_number#}</i>{/if}
                  </a>
                </td>
                <td class="t_border" rowspan="{$doc.rowspan}">{$doc.issue_date|date_format:#date_short#|escape|default:"&nbsp;"}</td>
                <td class="t_border hright" rowspan="{$doc.rowspan}">{$doc.total_without_discount|string_format:"%.2f"|default:"0.00"}</td>
                <td class="t_border hright" rowspan="{$doc.rowspan}">{$doc.total_discount_value|string_format:"%.2f"|default:"0.00"}</td>
                <td class="t_border hright" rowspan="{$doc.rowspan}">{$doc.total|string_format:"%.2f"|default:"0.00"}</td>
                <td class="t_border hright" rowspan="{$doc.rowspan}">{$doc.total_vat_rate|string_format:"%.2f"|default:"0.00"}</td>
                <td class="t_border hright" rowspan="{$doc.rowspan}">{$doc.total_vat|string_format:"%.2f"|default:"0.00"}</td>
                <td class="t_border hright" rowspan="{$doc.rowspan}">{$doc.total_with_vat|string_format:"%.2f"|default:"0.00"}</td>
              {/if}
              <td class="t_border">{$gt2.article_type|escape|default:"&nbsp;"}</td>
              <td class="t_border">{$gt2.category|escape|default:"&nbsp;"}</td>
              <td class="t_border">{$gt2.article_code|escape|default:"&nbsp;"}</td>
              <td class="t_border">{$gt2.article_name|escape|default:"&nbsp;"}</td>
              <td class="t_border">{$gt2.article_description|escape|nl2br|default:"&nbsp;"}</td>
              <td class="t_border">{$gt2.article_measure_name|escape|default:"&nbsp;"}</td>
              <td class="t_border hright">{$gt2.quantity|string_format:"%.2f"|default:"0.00"}</td>
              <td class="t_border hright">{if $gt2.system_nom}&nbsp;{else}{$gt2.sell_price|string_format:"%.2f"|default:"0.00"}{/if}</td>
              <td class="t_border hright">{if $gt2.price > 0 && !$gt2.system_nom}{$gt2.price|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
              <td class="t_border hright">{if $gt2.system_nom}&nbsp;{else}{$gt2.average_weighted_delivery_price|string_format:"%.2f"|default:"0.00"}{/if}</td>
              <td class="t_border hright">{$gt2.subtotal_with_discount|string_format:"%.2f"|default:"0.00"}</td>
              {if $smarty.foreach.gt2_row.first}
                <td class="t_border" rowspan="{$doc.rowspan}">{$doc.handovered_status_name|escape|default:"&nbsp;"}</td>
                <td class="t_border" rowspan="{$doc.rowspan}">
                  {foreach from=$doc.handovers.incoming item=incoming name='inc'}
                    <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=warehouses_documents&amp;warehouses_documents=view&amp;view={$incoming.id}">{$incoming.num|escape}</a> - {$incoming.warehouse_name|escape}
                    {if !$smarty.foreach.inc.last}
                      <br />
                    {/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
                <td rowspan="{$doc.rowspan}">
                  {foreach from=$doc.handovers.outgoing item=outgoing name='outg'}
                    <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=warehouses_documents&amp;warehouses_documents=view&amp;view={$outgoing.id}">{$outgoing.num|escape}</a> - {$outgoing.warehouse_name|escape}
                    {if !$smarty.foreach.outg.last}
                      <br />
                    {/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
              {/if}
            </tr>
          {/foreach}
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="25">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr class="row_blue">
          <td colspan="5" class="t_border" style="text-align: right;">&nbsp;</td>
          <td class="t_border" style="mso-number-format: '0\.00'; text-align: right;"><strong>{$reports_additional_options.totals.total_without_discount|string_format:"%.2f"|default:"0.00"}</strong></td>
          <td class="t_border" style="mso-number-format: '0\.00'; text-align: right;"><strong>{$reports_additional_options.totals.total_discount_value|string_format:"%.2f"|default:"0.00"}</strong></td>
          <td class="t_border" style="mso-number-format: '0\.00'; text-align: right;"><strong>{$reports_additional_options.totals.total|string_format:"%.2f"|default:"0.00"}</strong></td>
          <td class="t_border" style="mso-number-format: '0\.00'; text-align: right;"><strong>&nbsp;</strong></td>
          <td class="t_border" style="mso-number-format: '0\.00'; text-align: right;"><strong>{$reports_additional_options.totals.total_vat|string_format:"%.2f"|default:"0.00"}</strong></td>
          <td class="t_border" style="mso-number-format: '0\.00'; text-align: right;"><strong>{$reports_additional_options.totals.total_with_vat|string_format:"%.2f"|default:"0.00"}</strong></td>
          <td colspan="14" class="t_border" style="text-align: right;">&nbsp;</td>
        </tr>
        <tr>
          <td class="t_footer" colspan="25"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
