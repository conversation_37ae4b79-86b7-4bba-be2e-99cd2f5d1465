reports_document_section = Section
reports_document_type = Document type
reports_customer = Customer
reports_category = Category
reports_incoming_warehouse = Incoming warehouse
reports_outgoing_warehouse = Outgoing warehouse
reports_nomenclature = Article

reports_document_section_incomes = Incomes
reports_document_section_expenses = Expenses
reports_document_section_warehouse = Warehouse documents

reports_num = Num
reports_customer = Customer
reports_customer_eik = EIK
reports_num_doc = Document num
reports_doc_date = Date
reports_total_sum = Total document sum
reports_discount = Discount (value)
reports_total_with_discount = Total (after discount)
reports_total_vat_percent = VAT (%)
reports_total_vat_value = VAT (value)
reports_total_with_vat = Total with VAT
reports_nom_type = Nomenclature type
reports_nom_category = Category
reports_nom_code = Code
reports_nom_name = Article
reports_nom_description = Description
reports_nom_measure = Measure
reports_nom_quantity = Quantity
reports_nom_sell_price = Sell price
reports_nom_delivery_price = Delivery price
reports_nom_avg_price = AWDP
reports_nom_value = Value
reports_handover_status = Handover status
reports_warehouse_in = Source warehouse
reports_warehouse_out = Target warehouse

reports_nom_avg_price_help = Average weighted delivery price

reports_handover_status_none = Not processed
reports_handover_status_not = Not processed
reports_handover_status_partial = Partial
reports_handover_status_full = Full

reports_warehouse_status_finished = Finished
reports_warehouse_transfer_status_locked = Article moved out of the warehouse
reports_warehouse_transfer_status_finished = Article is moved in the warehouse

error_reports_complete_required_filters = Please, complete required filters!
