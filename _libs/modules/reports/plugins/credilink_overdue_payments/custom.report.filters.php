<?php
    class Custom_Report_Filters extends Report_Filters {
        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;
            $filters = array();

            $registry->set('include_gt2', true, true);
            $report_name = '';
            if ($this->reportName) {
                $report_name = $this->reportName;
            } else {
                $report_name = $registry['report_type']['name'];
            }

            // OVERDUE PAYMENTS FILTER
            $filters['overdue_payments'] = array(
                'id'      => 'overdue_payments',
                'name'    => 'overdue_payments',
                'type'    => 'dropdown',
                'required'=> true,
                'label'   => $this->i18n('reports_filter_payments_count'),
                'options' => array(
                    array(
                        'option_value' => 'all',
                        'label' => $this->i18n('all')
                    ),
                    array(
                        'option_value' => '1',
                        'label' => $this->i18n('reports_filter_payments_count_options_1')
                    ),
                    array(
                        'option_value' => '2',
                        'label' => $this->i18n('reports_filter_payments_count_options_2')
                    ),
                    array(
                        'option_value' => '3',
                        'label' => $this->i18n('reports_filter_payments_count_options_3')
                    ),
                    array(
                        'option_value' => '4',
                        'label' => $this->i18n('reports_filter_payments_count_options_4')
                    )
                )
            );

            // OVERDUE PAYMENTS FILTER
            $filters['not_performed_action'] = array(
                'id'      => 'not_performed_action',
                'name'    => 'not_performed_action',
                'type'    => 'dropdown',
                'label'   => $this->i18n('reports_filter_not_performed_action'),
                'options' => array(
                    array(
                        'option_value' => 'never',
                        'label' => $this->i18n('reports_filter_not_performed_action_options_5')
                    ),
                    array(
                        'option_value' => 'week_1',
                        'label' => $this->i18n('reports_filter_not_performed_action_options_1')
                    ),
                    array(
                        'option_value' => 'week_2',
                        'label' => $this->i18n('reports_filter_not_performed_action_options_2')
                    ),
                    array(
                        'option_value' => 'month',
                        'label' => $this->i18n('reports_filter_not_performed_action_options_4')
                    )
                )
            );

            //DEFINE EMPLOYEE FILTER
            $filters['employee'] = array (
                'custom_id'       => 'employee',
                'name'            => 'employee',
                'type'            => 'autocompleter',
                'width'           => 222,
                'label'           => $this->i18n('reports_employee'),
                'help'            => $this->i18n('reports_employee'),
                'autocomplete'    => array (
                    'search'       => array('<name>', '<ucn>', '<eik>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'client',
                    'fill_options' => array(
                        '$employee => <id>',
                        '$employee_autocomplete => <name> <lastname>'),
                    'filters'      => array(
                        '<type>' => (string)PH_CUSTOMER_EMPLOYEE,
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );

            //DEFINE THE DATES FILTER
            $filters['date_from'] = array(
                'name'               => 'date_from',
                'type'               => 'custom_filter',
                'custom_template'    => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
                'first_filter_label' => $this->i18n('from'),
                'additional_filter'  => 'date_to',
                'width'              => '64',
                'label'              => $this->i18n('reports_payment_date'),
                'help'               => $this->i18n('reports_payment_date')
            );
            $filters['date_to'] = array(
                'name'               => 'date_to',
                'type'               => 'date',
                'width'              => '64',
                'label'              => $this->i18n('to')
            );

            //DEFINE THE DEADLINE DATES FILTER
            $filters['date_deadline_from'] = array(
                'name'               => 'date_deadline_from',
                'type'               => 'custom_filter',
                'custom_template'    => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
                'first_filter_label' => $this->i18n('from'),
                'additional_filter'  => 'date_deadline_to',
                'width'              => '64',
                'label'              => $this->i18n('reports_date_deadline'),
                'help'               => $this->i18n('reports_date_deadline')
            );
            $filters['date_deadline_to'] = array(
                'name'               => 'date_deadline_to',
                'type'               => 'date',
                'width'              => '64',
                'label'              => $this->i18n('to')
            );

            //DEFINE SHOW NOT PLAYMENTS ONLY
            $filters['show_contract_wo_payments'] = array (
                'custom_id' => 'show_contract_wo_payments',
                'name'      => 'show_contract_wo_payments',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_show_contract_wo_payments'),
                'help'      => $this->i18n('reports_show_contract_wo_payments'),
                'options'   => array(
                    array(
                        'label'        => '',
                        'option_value' => '1',
                    )
                )
            );

            /** !!! IMPORTANT !!!!
             *  We need this variable for each report we will use to send emails
             */
            $filter = array (
                'custom_id' => 'custom_generate',
                'name'      => 'custom_generate',
                'type'      => 'hidden',
                'hidden'    => true,
                'value'     => 1
            );
            $filters['custom_generate'] = $filter;

            return $filters;
        }

        /**
         * Process some filters that depends on the request
         *
         * @param array $filters - the report filters
         */
        function processDependentFilters(&$filters) {
            $unset_filters = array();

            foreach ($filters as $name => $filter) {
                // Process the filter from/to date
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }
    }
?>