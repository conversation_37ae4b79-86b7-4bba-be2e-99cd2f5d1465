<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="margin-bottom: 5px;">
  <tr class="reports_title_row hcenter">
    <td class="t_border" style="vertical-align: middle;">
      <div style="width: 90px;">{#reports_call_date#|escape}</div>
    </td>
    <td class="t_border" style="vertical-align: middle;">
      <div style="width: 300px;">{#reports_call_option_description#|escape}</div>
    </td>
    <td style="vertical-align: middle;">
      <div style="width: 90px;">{#reports_call_promised_pay_date#|escape}</div>
    </td>
  </tr>
  {foreach from=$clients_communication item=cl_com}
    <tr class="{cycle name=comm_rower values='t_odd1 t_odd2,t_even1 t_even2'}">
      <td class="t_border">
        {$cl_com.call_date|date_format:#date_short#|escape|default:"&nbsp;"}
      </td>
      <td class="t_border" style="width: 100px;">
        {$cl_com.call_status_description|escape|nl2br|default:"&nbsp;"}
      </td>
      <td style="width: 90px;">
        {$cl_com.call_next_date|date_format:#date_short#|escape|default:"&nbsp;"}
      </td>
    </tr>
  {foreachelse}
    <tr class="{cycle name=comm_rower values='t_odd1 t_odd2,t_even1 t_even2'}">
      <td class="error" colspan="3">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td class="t_footer" colspan="3"></td>
  </tr>
</table>
<button class="button" onclick="lb.deactivate(); return false;" style="float: right;">{#close#}</button>
