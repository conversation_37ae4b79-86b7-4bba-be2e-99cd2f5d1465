// TODO: Extract duplicated code
// TODO: Get out all selectors and pass them through the constructor
// TODO: Atomize functionalities as separate functions
class StadaVisitAnalysis {
    resultsBy;
    exportFileName;
    segmentationColumns;
    gridDiv;
    i18n;
    gridCustomers;
    gridSegmentation;

    constructor(options) {
        this.resultsBy = options.resultsBy;
        this.exportFileName = options.exportFileName;
        this.gridDiv = options.gridDiv;
        this.i18n = options.i18n;
    }

    getSegmentationColumns(gridColumns){
        if (typeof this.segmentationColumns === 'undefined') {
            this.segmentationColumns = [];
            gridColumns.forEach((column) => {
                if (/^segm_/.test(column.field)) {
                    this.segmentationColumns.push(column.field);
                }
            });
        }
        return this.segmentationColumns;
    }

    prepareGrid() {
        if (!this.gridDiv) {
            return;
        }

        if (this.resultsBy === 'customer') {
            this.prepareGridCustomer();
        } else if (this.resultsBy === 'segmentation') {
            this.prepareGridSegmentation();
        }
    }

    prepareGridCustomer() {
        /*
        Grid: customers
         */
        let gridCustomersParams = {
            gridLines: 'Both',
            allowTextWrap: true,
            allowSorting: true,
            allowMultiSorting: true,
            allowResizing: true,
            allowReordering: true,
            enableStickyHeader: true,
            dataSource: JSON.parse(this.gridDiv.getAttribute('data-customers'))??[],
            columns: JSON.parse(this.gridDiv.getAttribute('data-customers-grid-columns')),
            sortSettings: JSON.parse(this.gridDiv.getAttribute('data-customers-grid-sort-settings')),
        };

        /*
        Grid: visits
         */
        let visitsDataSource = [];
        const visitsData = JSON.parse(this.gridDiv.getAttribute('data-visits'));
        if (visitsData) {
            const dataTypes = {
                date: ['visit_date'],
                // int: [
                //     'visits_number',
                //     'customer_visits_count',
                // ],
            };
            visitsDataSource = ej2Helper.grid.prepareDataTypes(visitsData, dataTypes);
        }
        gridCustomersParams.childGrid = {
            gridLines: 'Both',
            allowTextWrap: true,
            allowSorting: true,
            allowMultiSorting: true,
            allowResizing: true,
            allowReordering: true,
            dataSource: visitsDataSource,
            columns: JSON.parse(this.gridDiv.getAttribute('data-visits-grid-columns')),
            sortSettings: JSON.parse(this.gridDiv.getAttribute('data-visits-grid-sort-settings')),
            queryString: 'customer_id',
            queryCellInfo: (args) => {
                if (args.column.field === 'visit_notes') {
                    args.cell.innerHTML = args.cell.innerHTML.replace(/(\r\n|\r|\n)/, '<br />');
                }
            },
        };

        /*
        Grid: photos
         */
        gridCustomersParams.childGrid.childGrid = {
            gridLines: 'Both',
            allowTextWrap: true,
            allowSorting: true,
            allowMultiSorting: true,
            allowResizing: true,
            allowReordering: true,
            allowExcelExport: true,
            dataSource: JSON.parse(this.gridDiv.getAttribute('data-photos')),
            columns: JSON.parse(this.gridDiv.getAttribute('data-photos-grid-columns')),
            queryString: 'visit_id',
            queryCellInfo: (args) => {
                if (args.column.field === 'note_sr' || args.column.field === 'manager_comment') {
                    args.cell.innerHTML = args.cell.innerHTML.replace(/(\r\n|\r|\n)/, '<br />');
                }
            },
            // excelQueryCellInfo: (args) => {
            //     if (args.column.template === '#templatePhoto'/* && args.data.photo_exists === true*/) {
            //         const b64 = '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';
            //         if (args.name === "excelQueryCellInfo") {
            //             // args.image = { height: 75, base64: args.data.photo_base64, width: 75 };
            //             args.image = { height: 75, base64: b64, width: 75 };
            //         } else {
            //             // args.image = { base64: args.data.photo_base64 };
            //             args.image = { base64: b64 };
            //         }
            //         // args.image = {base64: args.data.photo_base64, width: 75, height: 75};
            //         let debugHere;
            //     }
            // },
        };

        /*
        Excel export
         */
        if (gridCustomersParams.dataSource && this.exportFileName) {
            gridCustomersParams.allowExcelExport = true;
            gridCustomersParams.toolbar = ['ExcelExport'];
            gridCustomersParams.toolbarClick = (args) => {
                if (/_excelexport$/.test(args.item.id)) {
                    this.gridCustomers.showSpinner();

                    // // Hide columns
                    // this.gridCustomers.columns.forEach((e, i) => {
                    //     if (typeof e.field === 'undefined') {
                    //         return;
                    //     }
                    //     if (e.field === 'customer_visits_count') {
                    //         this.gridCustomers.columns[i].visible = false;
                    //     }
                    // });

                    // // Change data in columns
                    // let dataSource = this.gridCustomers.dataSource;
                    // dataSource.forEach((row) => {
                    //     if (row.hasOwnProperty('phones')) {
                    //         let phones = [];
                    //         row.phones_original = row.phones;
                    //         row.phones.forEach((v) => {
                    //             phones.push(v);
                    //         });
                    //         row.phones = phones.join("\r\n");
                    //     }
                    // });

                    // Make the Excel export
                    this.gridCustomers.excelExport({
                        fileName: this.exportFileName,
                        // columns: this.gridCustomers.columns,
                        // dataSource: dataSource,
                        hierarchyExportMode: 'Expanded',
                    });
                }
            };
            gridCustomersParams.excelExportComplete = () => {
                // // Restore columns
                // this.gridCustomers.columns.forEach(function (e, i) {
                //     if (typeof e.field === 'undefined') {
                //         return;
                //     }
                //     if (e.field === 'customer_visits_count') {
                //         e.visible = true;
                //     }
                // });
                // this.gridCustomers.dataSource.forEach(function (row) {
                //     if (row.hasOwnProperty('phones')) {
                //         row.phones = row.phones_original;
                //     }
                // });
                this.gridCustomers.hideSpinner();
            }
        }

        // Build the grid
        this.gridCustomers = new ej.grids.Grid(gridCustomersParams);
        // Append the grid
        this.gridCustomers.appendTo(this.gridDiv);
        // this.gridVisits = this.gridCustomers.childGrid;
        // this.gridPhotos = this.gridCustomers.childGrid.childGrid;
    }

    prepareGridSegmentation() {
        let gridParams = {
            gridLines: 'Both',
            allowTextWrap: true,
            allowSorting: true,
            allowMultiSorting: true,
            allowResizing: true,
            allowReordering: true,
            enableStickyHeader: true,
            dataSource: JSON.parse(this.gridDiv.getAttribute('data-regions')),
            columns: JSON.parse(this.gridDiv.getAttribute('data-segmentation-grid-columns')),
            dataBound: () => {
                ej2Helper.grid.applyRowspan(this.gridSegmentation, [
                    {
                        'criteria': 'region_id',
                        'spanColumns': [
                            'region_name',
                        ],
                    },
                ]);
            },
        };

        let aggregates = [
            {
                field: 'employee_name',
                type: 'Custom',
                customAggregate: () => {
                    return this.i18n['th_total'];
                },
                textAlign: 'Right',
            },
            {
                type: 'Sum',
                field: 'segmentations_total',
            }
        ];
        this.getSegmentationColumns(gridParams.columns).forEach((field) => {
            aggregates.push({
                type: 'Sum',
                field: field,
            });
        });
        gridParams.aggregates = [{columns: aggregates}];

        /*
        Excel export
         */
        if (gridParams.dataSource && this.exportFileName) {
            gridParams.allowExcelExport = true;
            gridParams.toolbar = ['ExcelExport'];
            gridParams.toolbarClick = (args) => {
                if (/_excelexport$/.test(args.item.id)) {
                    this.gridSegmentation.showSpinner();

                    // Make the Excel export
                    this.gridSegmentation.excelExport({
                        fileName: this.exportFileName,
                    });
                }
            };
            gridParams.excelExportComplete = () => {
                this.gridSegmentation.hideSpinner();
            }
        }

        // Build the grid
        this.gridSegmentation = new ej.grids.Grid(gridParams);
        // Append the grid
        this.gridSegmentation.appendTo(this.gridDiv);
    }
}
