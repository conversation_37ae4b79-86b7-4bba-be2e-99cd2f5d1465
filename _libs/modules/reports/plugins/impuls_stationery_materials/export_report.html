<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
    <table border="1">
      <tr>
        <td rowspan="2" align="center" style="vertical-align: middle;" nowrap="nowrap"><strong>{#reports_num_date#|escape}</strong></td>
        <td colspan="7" align="center" style="vertical-align: middle;" nowrap="nowrap"><strong>{#reports_request_description#|escape}</strong></td>
        <td rowspan="2" align="center" style="vertical-align: middle;" nowrap="nowrap"><strong>{#reports_requested_by_unit#|escape}</strong></td>
        <td rowspan="2" align="center" style="vertical-align: middle;" nowrap="nowrap"><strong>{#reports_received_by#|escape}</strong></td>
        <td rowspan="2" align="center" style="vertical-align: middle;"><strong>{#reports_received_by_deliverer#|escape}</strong></td>
      </tr>
      <tr>
        <td nowrap="nowrap"><strong>{#reports_cat_num#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_material_description#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_material_count#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_material_measure#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_material_single_price#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_material_price#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_total_value#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results item=result name=results key=index_res}
        <tr>
          <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle;">
            {$result.full_num|escape} / {$result.date_added|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          {if $result.materials.0}
            <td align="left">
              {$result.materials.0.cat_num|escape|default:"&nbsp;"}
            </td>
            <td style="width: 400px;">
              {$result.materials.0.material_name|escape|default:"&nbsp;"}
            </td>
            <td align="right">
              {$result.materials.0.material_count|string_format:"%.2f"|escape|default:"0"}
            </td>
            <td nowrap="nowrap">
              {$result.materials.0.material_measure|escape|default:"&nbsp;"}
            </td>
            <td align="right" nowrap="nowrap" style="mso-number-format:'0\.00';">
              {$result.materials.0.material_price|string_format:"%.2f"|escape|default:"0.00"}
            </td>
            <td align="right" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'0\.00';">
              {$result.materials.0.material_total|string_format:"%.2f"|escape|default:"0.00"}
            </td>
          {else}
            <td colspan="7">
              &nbsp;
            </td>
          {/if}
          <td rowspan="{$result.rowspan}" align="right" style="vertical-align: middle;">
            {$result.total|escape|default:"0.00"}
          </td>
          <td rowspan="{$result.rowspan}" style="vertical-align: middle; width: 200px;">
            {$result.requested_by_unit|escape|default:"&nbsp;"}
          </td>
          <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
            {$result.received_by|escape|default:"&nbsp;"}
          </td>
          <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
            {$result.received_by_deliverer|escape|default:"&nbsp;"}
          </td>
        </tr>
        {foreach from=$result.materials item=material name=mat}
          {if !$smarty.foreach.mat.first}
            <tr>
              <td align="left">
                {$material.cat_num|escape|default:"&nbsp;"}
              </td>
              <td style="width: 400px;">
                {$material.material_name|escape|default:"&nbsp;"}
              </td>
              <td align="right" nowrap="nowrap">
                {$material.material_count|string_format:"%.2f"|escape|default:"0"}
              </td>
              <td nowrap="nowrap">
                {$material.material_measure|escape|default:"&nbsp;"}
              </td>
              <td align="right" nowrap="nowrap" style="mso-number-format:'0\.00';">
                {$material.material_price|string_format:"%.2f"|escape|default:"0.00"}
              </td>
              <td align="right" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'0\.00';">
                {$material.material_total|string_format:"%.2f"|escape|default:"0.00"}
              </td>
            </tr>
          {/if}
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="11"><span color="red">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
      <tr>
        <td colspan="7">
          &nbsp;
        </td>
        <td style="background-color:#98BCFF; padding: 5px;" align="right">
          <strong>{$reports_additional_options.total_value|string_format:"%.2f"|escape|default:"0.00"}</strong>
        </td>
        <td colspan="3">
          &nbsp;
        </td>
      </tr>
    </table>
  </body>
</html>