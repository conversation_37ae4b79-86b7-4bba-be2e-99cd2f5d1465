<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td style="vertical-align: top;">
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td class="t_border" rowspan="2" style="text-align: center; vertical-align: middle;" nowrap="nowrap">{#reports_num_date#|escape}</td>
          <td class="t_border" colspan="7" style="text-align: center; vertical-align: middle;" nowrap="nowrap">{#reports_request_description#|escape}</td>
          <td class="t_border" rowspan="2" style="text-align: center; vertical-align: middle;" nowrap="nowrap">{#reports_requested_by_unit#|escape}</td>
          <td class="t_border" rowspan="2" style="text-align: center; vertical-align: middle;" nowrap="nowrap">{#reports_received_by#|escape}</td>
          <td rowspan="2" style="text-align: center; vertical-align: middle;">{#reports_received_by_deliverer#|escape}</td>
        </tr>
        <tr class="reports_title_row">
          <td class="t_border" nowrap="nowrap">{#reports_cat_num#|escape}</td>
          <td class="t_border" nowrap="nowrap">{#reports_material_description#|escape}</td>
          <td class="t_border" nowrap="nowrap">{#reports_material_count#|escape}</td>
          <td class="t_border" nowrap="nowrap">{#reports_material_measure#|escape}</td>
          <td class="t_border" nowrap="nowrap">{#reports_material_single_price#|escape}</td>
          <td class="t_border" nowrap="nowrap">{#reports_material_price#|escape}</td>
          <td class="t_border" nowrap="nowrap">{#reports_total_value#|escape}</td>
        </tr>
        {foreach from=$reports_results item=result name=results key=index_res}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td rowspan="{$result.rowspan}" class="t_border" nowrap="nowrap" style="vertical-align: middle;">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|escape}</a> / {$result.date_added|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            {if $result.materials.0}
              <td class="t_border">
                {$result.materials.0.cat_num|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" style="width: 400px;">
                {$result.materials.0.material_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border hright" nowrap="nowrap">
                {$result.materials.0.material_count|string_format:"%.2f"|escape|default:"0"}
              </td>
              <td class="t_border" nowrap="nowrap">
                {$result.materials.0.material_measure|escape|default:"&nbsp;"}
              </td>
              <td class="t_border hright" nowrap="nowrap">
                {$result.materials.0.material_price|string_format:"%.2f"|escape|default:"0.00"}
              </td>
              <td class="t_border hright" nowrap="nowrap" style="vertical-align: middle;">
                {$result.materials.0.material_total|string_format:"%.2f"|escape|default:"0.00"}
              </td>
            {else}
              <td colspan="7" class="t_border">
                &nbsp;
              </td>
            {/if}
            <td rowspan="{$result.rowspan}" class="t_border hright" style="vertical-align: middle;">
              {$result.total|string_format:"%.2f"|escape|default:"0.00"}
            </td>
            <td rowspan="{$result.rowspan}" class="t_border" style="vertical-align: middle; width: 200px;">
              {$result.requested_by_unit|escape|default:"&nbsp;"}
            </td>
            <td rowspan="{$result.rowspan}" class="t_border" style="vertical-align: middle;">
              {$result.received_by|escape|default:"&nbsp;"}
            </td>
            <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
              {$result.received_by_deliverer|escape|default:"&nbsp;"}
            </td>
          </tr>
          {foreach from=$result.materials item=material name=mat}
            {if !$smarty.foreach.mat.first}
              <tr class="{$current_row_class}">
                <td class="t_border">
                  {$material.cat_num|escape|default:"&nbsp;"}
                </td>
                <td class="t_border" style="width: 400px;">
                  {$material.material_name|escape|default:"&nbsp;"}
                </td>
                <td class="t_border hright" nowrap="nowrap">
                  {$material.material_count|string_format:"%.2f"|escape|default:"0"}
                </td>
                <td class="t_border" nowrap="nowrap">
                  {$material.material_measure|escape|default:"&nbsp;"}
                </td>
                <td class="t_border hright" nowrap="nowrap">
                  {$material.material_price|string_format:"%.2f"|escape|default:"0.00"}
                </td>
                <td class="t_border hright" nowrap="nowrap">
                  {$material.material_total|string_format:"%.2f"|escape|default:"0.00"}
                </td>
              </tr>
            {/if}
          {/foreach}
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="11">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td colspan="7" class="t_border">
            &nbsp;
          </td>
          <td style="background-color:#98BCFF; padding: 5px;" class="t_border hright">
            <strong>{$reports_additional_options.total_value|string_format:"%.2f"|escape|default:"0.00"}</strong>
          </td>
          <td colspan="3">
            &nbsp;
          </td>
        </tr>
        <tr>
          <td class="t_footer" colspan="11"></td>
        </tr>
      </table>
    </td>
  </tr>
  {foreach from=$reports_additional_options.charts item='single_chart'}
    <tr>
      <td style="padding-left: 20px; vertical-align: top; width: 100%;">
        {include file="chart.html" chart=$single_chart}
      </td>
    </tr>
  {/foreach}
</table>
