<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
  <tr class="reports_title_row">
    <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 62px;">{#reports_contract_date_start#|escape}</div></td>
    <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 62px;">{#reports_contract_date_validity#|escape}</div></td>
    <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 50px;">{#reports_contract_num#|escape}</div></td>
    {if $reports_additional_options.columns.include_trademark}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 250px;">{#reports_trademark#|escape}</div></td>
    {/if}
    <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 250px;">{#reports_customer#|escape}</div></td>
    <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 120px;">{#reports_unit#|escape}</div></td>
    <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_area#|escape}</div></td>
    {if $reports_additional_options.columns.show_base_rent_price}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_base_rental_price#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_tax_service}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_tax_service#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_tax_medial_period}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_fip#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_parking_zone}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_parcking_place#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_advance_rrp}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_advance_rrp#|escape}</div></td>
    {/if}
    <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_income_rental_price#|escape}</div></td>
    {if $reports_additional_options.columns.show_total_base_rent_price}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_total_base_rental_price#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_total_tax_service}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_total_tax_service#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_total_tax_medial_period}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_total_fip#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_total_parking_zone}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_total_parcking_place#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_total_advance_rrp}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_total_advance_rrp#|escape}</div></td>
    {/if}
    {if $reports_additional_options.columns.show_total}
      <td class="t_border hcenter" rowspan="2" style="vertical-align: middle;"><div style="width: 55px;">{#reports_total_t#|escape}</div></td>
    {/if}
    <td class="t_border hcenter" colspan="2" style="vertical-align: middle;">{#reports_step_rent#|escape}</td>
    <td class="hcenter" rowspan="2" style="vertical-align: middle;">{#reports_comments#|escape}</td>
  </tr>
  <tr class="reports_title_row hcenter">
    <td class="t_border hcenter" style="vertical-align: middle;">{#reports_from#|escape}</td>
    <td class="t_border hcenter" style="vertical-align: middle;">{#reports_brp#|escape}</td>
  </tr>
  {foreach from=$reports_results item=customer name=cust}
    {foreach from=$customer.trademarks item=trademark name=tm}
      {foreach from=$trademark.areas item=area name=ar}
        {if $area.colored}
          {capture assign='row_extra_info'}{#reports_month_rrp_chaged#} <strong>{$area.actual_rent_price} %</strong>{/capture}
        {else}
          {assign var='row_extra_info' value=''}
        {/if}
        {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
        <tr class="{$current_row_class}{if $area.colored} row_magenta{/if}"{if $row_extra_info} {popup text=$row_extra_info caption=#system_info#|escape}{/if}>
          <td class="t_border vmiddle" rowspan="{$area.rowspan}">
            {$area.contract_date_start|date_format:#date_short#|escape}
          </td>
          <td class="t_border vmiddle" rowspan="{$area.rowspan}">
            {$area.contract_date_validity|date_format:#date_short#|escape}
          </td>
          <td class="t_border vmiddle" rowspan="{$area.rowspan}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$area.contract_id}">{$area.contract_num|escape}</a>
          </td>
          {if $reports_additional_options.columns.include_trademark}
            <td class="t_border vmiddle" rowspan="{$area.rowspan}">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$trademark.id}">{$trademark.name|escape|default:"&nbsp;"}</a>
            </td>
          {/if}
          <td class="t_border vmiddle" rowspan="{$area.rowspan}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$customer.id}">{$customer.name|escape}</a>
          </td>
          <td class="t_border vmiddle" rowspan="{$area.rowspan}">{$area.name|escape|default:"&nbsp;"}</td>
          <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.area|string_format:"%.2f"|default:"0.00"}</td>
          {if $reports_additional_options.columns.show_base_rent_price}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.base_rent_price|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_tax_service}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.tax_service|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_tax_medial_period}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.tax_medial_period|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_parking_zone}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.parking_zone|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_advance_rrp}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.advance_rrp|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.avg_incomes_rent_price|string_format:"%.2f"|default:"0.00"} %</td>
          {if $reports_additional_options.columns.show_total_base_rent_price}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.total_base_rent_price|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_total_tax_service}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.total_tax_service|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_total_tax_medial_period}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.total_tax_medial_period|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_total_parking_zone}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.total_parking_zone|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_total_advance_rrp}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.total_advance_rrp|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $reports_additional_options.columns.show_total}
            <td class="t_border vmiddle hright" rowspan="{$area.rowspan}">{$area.total|string_format:"%.2f"|default:"0.00"}</td>
          {/if}
          {if $area.brp_rows.0}
            <td class="t_border vmiddle hright">{$area.brp_rows.0.since|date_format:#date_short#|escape}</td>
            <td class="t_border vmiddle hright">{$area.brp_rows.0.brp|string_format:"%.2f"|default:"0.00"}</td>
          {else}
            <td colspan="2" class="t_border vmiddle hright">&nbsp;</td>
          {/if}
          <td class="vmiddle hcenter" style="text-align: center;" rowspan="{$area.rowspan}">{if $area.contract_comments}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=communications&amp;communications={$area.contract_id}&amp;communication_type=comments" target="_blank"><img src="{$theme->imagesUrl}comments.png" border="0" alt="{#comments#}" /> {$area.contract_comments}</a>{else}&nbsp;{/if}</td>
        </tr>
        {foreach from=$area.brp_rows item=brp_row name=abr}
          {if !$smarty.foreach.abr.first}
            <tr class="{$current_row_class}{if $area.colored} row_magenta{/if}">
              <td class="t_border hright">{$brp_row.since|date_format:#date_short#|escape}</td>
              <td class="t_border hright">{$brp_row.brp|string_format:"%.2f"|default:"0.00"}</td>
            </tr>
          {/if}
        {/foreach}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="{$reports_additional_options.total_columns}">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="{$reports_additional_options.total_columns}">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="{$reports_additional_options.total_columns}">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr class="row_blue">
    <td class="t_border" colspan="{if $reports_additional_options.include_trademark}5{else}4{/if}"><strong>{#reports_total#}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.total_areas_included|escape|default:"0"}</strong></td>
    <td class="t_border hright"><strong>{$reports_additional_options.total_area|string_format:"%.2f"|default:"0.00"}</strong></td>
    {if $reports_additional_options.columns.show_base_rent_price}
      <td class="t_border hright"><strong>{$reports_additional_options.avg_base_rent_price|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_tax_service}
      <td class="t_border hright"><strong>{$reports_additional_options.avg_tax_service|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_tax_medial_period}
      <td class="t_border hright"><strong>{$reports_additional_options.avg_tax_medial_period|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_parking_zone}
      <td class="t_border hright"><strong>{$reports_additional_options.avg_parking_zone|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_advance_rrp}
      <td class="t_border hright"><strong>{$reports_additional_options.avg_advance_rrp|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    <td class="t_border hright"><strong>{$reports_additional_options.avg_incomes_rent_price|string_format:"%.2f"|default:"0.00"} %</strong></td>
    {if $reports_additional_options.columns.show_total_base_rent_price}
      <td class="t_border hright"><strong>{$reports_additional_options.total_base_rent_price|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_total_tax_service}
      <td class="t_border hright"><strong>{$reports_additional_options.total_tax_service|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_total_tax_medial_period}
      <td class="t_border hright"><strong>{$reports_additional_options.total_tax_medial_period|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_total_parking_zone}
      <td class="t_border hright"><strong>{$reports_additional_options.total_parking_zone|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_total_advance_rrp}
      <td class="t_border hright"><strong>{$reports_additional_options.total_advance_rrp|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    {if $reports_additional_options.columns.show_total}
      <td class="t_border hright"><strong>{$reports_additional_options.final_total|string_format:"%.2f"|default:"0.00"}</strong></td>
    {/if}
    <td colspan="3"></td>
  </tr>
  <tr>
    <td class="t_footer" colspan="{$reports_additional_options.total_columns}"></td>
  </tr>
</table>
