{if $reports_results.main}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
    <tr class="reports_title_row hcenter">
      <td class="t_border vmiddle"><div style="width: 290px;">{#reports_customer#|escape}</div></td>
      {if $report_filters.separate_invoices.value eq $report_filters.separate_invoices.option_value}
        <td class="t_border vmiddle"><div style="width: 90px;">{#reports_invoice_num#|escape}</div></td>
        <td class="t_border vmiddle"><div style="width: 90px;">{#reports_invoice_date#|escape}</div></td>
        <td class="t_border vmiddle"><div style="width: 90px;">{#reports_invoice_amount#|escape}</div></td>
      {/if}
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_total_percent#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_not_paid#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_current#|escape}</div></td>
      <td class="vmiddle"><div style="width: 90px;">{#reports_expired#|escape}</div></td>
    </tr>
    {foreach from=$reports_results.main key=k name=i item=result}
      <tr class="hright {if $smarty.foreach.i.last}row_blue{else}{cycle values='t_odd,t_even'}{/if}">
        {if $smarty.foreach.i.last}
          <td class="t_border hleft" style="text-align:left!important">
            <strong>{#reports_grand_total#}</strong>
          </td>
          {if $report_filters.separate_invoices.value eq $report_filters.separate_invoices.option_value}
            <td class="t_border" style="text-align:center!important">
              <strong>-</strong>
            </td>
            <td class="t_border" style="text-align:center!important">
              <strong>-</strong>
            </td>
            <td class="t_border" style="text-align:center!important">
              <strong>-</strong>
            </td>
          {/if}
          <td class="t_border">
            <strong>100</strong>
          </td>
          <td class="t_border">
            <strong>{$result.unpaid|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.current|default:0|string_format:"%.2F"}</strong>
          </td>
          <td>
            <strong>{$result.expired|default:0|string_format:"%.2F"}</strong>
          </td>
        {else}
          <td class="t_border hleft" style="text-align:left!important" width="292">
            {$result.customer_name}
          </td>
          {if $report_filters.separate_invoices.value eq $report_filters.separate_invoices.option_value}
            <td class="t_border" width="94">
              {$result.num|default:'-'}
            </td>
            <td class="t_border" width="94">
              {$result.date|date_format:#date_short#|default:'-'}
            </td>
            <td class="t_border" width="94">
              {$result.total|default:0|string_format:"%.2F"}
            </td>
          {/if}
          <td class="t_border" width="94">
            {$result.total_percent|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="94">
            {$result.unpaid|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="94">
            {$result.current|default:0|string_format:"%.2F"}
          </td>
          <td width="94">
            {$result.expired|default:0|string_format:"%.2F"}
          </td>
        {/if}
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="8">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="8"></td>
    </tr>
  </table>
  <br />
  <br />
{/if}
{if $reports_results.prescriptive}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
    <tr class="reports_title_row hcenter">
      <td class="t_border vmiddle"><div style="width: 390px;">{#reports_article#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_30#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_60#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_90#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_more#|escape}</div></td>
      <td class="vmiddle" style="width: 90px;"><div>{#reports_total#|escape}</div></td>
    </tr>
    {foreach from=$reports_results.prescriptive key=k name=i item=result}
      <tr class="hright {if $smarty.foreach.i.last}row_blue{else}{cycle values='t_odd,t_even'}{/if}">
        {if $smarty.foreach.i.last}
          <td class="t_border hleft" style="text-align:left!important">
            <strong>{#reports_grand_total#}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.30|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.60|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.90|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.more|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.total|default:0|string_format:"%.2F"}</strong>
          </td>
        {else}
          <td class="t_border hleft" style="text-align:left!important" width="392">
            {$result.article}
          </td>
          <td class="t_border" width="94">
            {$result.30|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="94">
            {$result.60|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="94">
            {$result.90|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="94">
            {$result.more|default:0|string_format:"%.2F"}
          </td>
          <td width="94">
            {$result.total|default:0|string_format:"%.2F"}
          </td>
        {/if}
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="6">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="6"></td>
    </tr>
  </table>
  <br />
  <br />
{/if}
{if $reports_results.prescriptive_customer}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
    <tr class="reports_title_row hcenter">
      <td class="t_border vmiddle"><div style="width: 390px;">{#reports_customer#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_30#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_60#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_90#|escape}</div></td>
      <td class="t_border vmiddle"><div style="width: 90px;">{#reports_more#|escape}</div></td>
      <td class="vmiddle"><div style="width: 90px;">{#reports_total#|escape}</div></td>
    </tr>
    {foreach from=$reports_results.prescriptive_customer key=k name=i item=result}
      <tr class="hright {if $smarty.foreach.i.last}row_blue{else}{cycle values='t_odd,t_even'}{/if}">
        {if $smarty.foreach.i.last}
          <td class="t_border hleft" style="text-align:left!important">
            <strong>{#reports_grand_total#}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.30|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.60|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.90|default:0|string_format:"%.2F"}</strong>
          </td>
          <td class="t_border">
            <strong>{$result.more|default:0|string_format:"%.2F"}</strong>
          </td>
          <td>
            <strong>{$result.total|default:0|string_format:"%.2F"}</strong>
          </td>
        {else}
          <td class="t_border hleft" style="text-align:left!important" width="392">
            {$result.name}
          </td>
          <td class="t_border" width="92">
            {$result.30|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="92">
            {$result.60|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="92">
            {$result.90|default:0|string_format:"%.2F"}
          </td>
          <td class="t_border" width="92">
            {$result.more|default:0|string_format:"%.2F"}
          </td>
          <td width="92">
            {$result.total|default:0|string_format:"%.2F"}
          </td>
        {/if}
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="6">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="6"></td>
    </tr>
  </table>
  <br />
  <br />
  {#report_note#}
  <br /><br />
{/if}
