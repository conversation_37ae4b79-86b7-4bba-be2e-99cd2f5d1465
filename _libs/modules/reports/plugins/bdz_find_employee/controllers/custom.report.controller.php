<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_email_content':
                $this->_email_content();
                break;
            case 'ajax_get_child_departments':
                $this->_getChildDepartments();
                break;
            case 'ajax_choose_record':
                $this->_chooseRecord();
                break;
            case 'generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    /*
     * AJAX function to take the child departments of a selected department
     */
    public function _getChildDepartments() {
        require_once PH_MODULES_DIR . "departments/models/departments.factory.php";

        $department = $this->registry['request']->get('department');
        $child_departments = Departments::getTreeDescendants($this->registry, array('where' => array('d1.id=' . $department)));

        $departments_options = array();
        if ($child_departments) {
            foreach ($child_departments as $dep_leave) {
                if ($dep_leave->get('id') == $department) {
                    continue;
                }
                $departments_options[] = array(
                    'option_value' => $dep_leave->get('id'),
                    'label'        => sprintf('%s %s', str_repeat('--', ($dep_leave->get('level')-1)), $dep_leave->get('name'))
                );
            }
        }

        print json_encode($departments_options);
        exit;
    }

    /*
     * Index action for the current report
     */
    public function _index() {
        parent::_index();

        $this->viewer = $this->getViewer();
        $this->viewer->data['hide_report_selection'] = true;
        if ($this->registry['request']->get('reports') != 'generate_report') {
            $this->viewer->data['autocomplete_options'] = base64_encode(json_encode($this->registry['request']->getAll()));
        } else {
            $this->viewer->data['autocomplete_options'] = $this->registry['request']->get('autocomplete_options');
        }

        $this->viewer->setFrameset('frameset_blank.html');

        $this->viewer->prepare();
        print $this->viewer->fetch();
        exit;
    }

    /*
     * Function to process the choosing of a record from the report
     */
    public function _chooseRecord() {
        $request = &$this->registry['request'];
        $session = &$this->registry['session'];

        $report_type = $this->getReportType();

        $session_reports_param = 'reports_' . $report_type['name'] . '_report';
        $this->registry->set('session_reports_param', $session_reports_param);

        require_once PH_MODULES_DIR . "reports/models/report.filters.php";
        require_once PH_MODULES_DIR . "reports/plugins/" . $report_type['name'] . "/custom.report.filters.php";
        $defineCustomFilters = new Custom_Report_Filters($this->registry, $report_type['name']);
        $filters_values = $defineCustomFilters->getFilterValues($this->registry, $report_type);

        // get the autocomplter params
        $autocompleter_params_encoded = $filters_values['autocomplete_options'];
        $autocompleter_params = json_decode(base64_decode($autocompleter_params_encoded), true);

        $item = $request->get('current_result_data');
        $item = json_decode(base64_decode($item), true);

        $data = array();
        foreach ($autocompleter_params['fill_options'] as $idx => $option) {
            @list($var_name, $var_value) = preg_split('#\s*=>\s*#', $option);
            $var_name = trim($var_name);
            $var_value = trim($var_value);
            preg_match_all('#<[^<>]*>#', $var_value, $matches);
            $matches = $matches[0];
            $data[$var_name] = $var_value;

            foreach ($matches as $match) {
                $property = preg_replace('#<|>|\s#', '', $match);
                if (!empty($item[$property])) {
                    $replacement = $item[$property];
                } else {
                    $replacement = '';
                }
                $data[$var_name] = preg_replace('#\s*$#', '', str_replace($match, $replacement, $data[$var_name]));
            }
        }

        if (!empty($autocompleter_params['row'])) {
            $data['row'] = $autocompleter_params['row'];
        }

        echo json_encode($data);
/*         printf('  <li id="%s">' . "\n",
                    'customer_report_data');
        printf('    <input type="hidden" name="%s_data" id="%s_data" value=\'%s\' disabled="disabled" />' . "\n",
                        'customer_report_data',
                        'customer_report_data', json_encode($data));
        printf('  </li>' . "\n");

        printf( '<script type="text/javascript">' . "\n" .
                '    selectAutocompleteItems($(\'customer_report_data\'), \'\');' . "\n" .
                '</script>'); */
        exit;
    }
}

?>
