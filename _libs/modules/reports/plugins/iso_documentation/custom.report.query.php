<?php
    Class Iso_Documentation Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            define('ACTUAL_VERSION_DATE', 'actual_version_date');
            define('ACTUAL_CHANGE_NUM', 'actual_change_num');
            define('ACTUAL_CHANGE_DATE', 'actual_change_date');
            define('FORM_NUM', 'form_num');
            define('ACTUAL_VERSION_LINK', 'actual_version_link');

            define('AFORM_DATE', 'aform_date');
            define('AFORM_CREATOR', 'aform_creator');
            define('AFORM_PROTECTOR', 'aform_protector');
            define('AFORM_TERM_PROT', 'aform_term_prot');
            define('AFORM_LINK', 'aform_link');

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $options_main_procedures = array();
            $options_helping_procedures = array();
            $options_all_procedures = array();
            $options_guidings = array();
            $options_regulations = array();
            $options_schedules = array();

            if (defined('DOC_TYPE_MANAGE_DOCUMENTS')) {
                $options_main_procedures[] = DOC_TYPE_MANAGE_DOCUMENTS;
                $options_all_procedures[] = DOC_TYPE_MANAGE_DOCUMENTS;
            }
            if (defined('DOC_TYPE_MANAGE_RECORDS')) {
                $options_main_procedures[] = DOC_TYPE_MANAGE_RECORDS;
                $options_all_procedures[] = DOC_TYPE_MANAGE_RECORDS;
            }
            if (defined('DOC_TYPE_INSIDE_AUDIT')) {
                $options_main_procedures[] = DOC_TYPE_INSIDE_AUDIT;
                $options_all_procedures[] = DOC_TYPE_INSIDE_AUDIT;
            }
            if (defined('DOC_TYPE_MANAGE_INCONFORMITIES')) {
                $options_main_procedures[] = DOC_TYPE_MANAGE_INCONFORMITIES;
                $options_all_procedures[] = DOC_TYPE_MANAGE_INCONFORMITIES;
            }
            if (defined('DOC_TYPE_MANAGE_PREVENTING_ACTIONS')) {
                $options_main_procedures[] = DOC_TYPE_MANAGE_PREVENTING_ACTIONS;
                $options_all_procedures[] = DOC_TYPE_MANAGE_PREVENTING_ACTIONS;
            }

            if (defined('DOC_TYPE_HELPING_PROCEDURE')) {
                $options_helping_procedures[] = DOC_TYPE_HELPING_PROCEDURE;
                $options_all_procedures[] = DOC_TYPE_HELPING_PROCEDURE;
            }

            if (defined('DOC_TYPE_GUIDE')) {
                $options_guidings[] = DOC_TYPE_GUIDE;
            }

            if (defined('DOC_TYPE_GUIDE_LOOK')) {
                $options_regulations[] = DOC_TYPE_GUIDE_LOOK;
            }
            if (defined('DOC_TYPE_MANAGE_STUFF')) {
                $options_regulations[] = DOC_TYPE_MANAGE_STUFF;
            }
            if (defined('DOC_TYPE_MANAGE_MSSI')) {
                $options_regulations[] = DOC_TYPE_MANAGE_MSSI;
            }
            if (defined('DOC_TYPE_CUSTOMERS_COMMUNICATION')) {
                $options_regulations[] = DOC_TYPE_CUSTOMERS_COMMUNICATION;
            }
            if (defined('DOC_TYPE_RIGHTS')) {
                $options_regulations[] = DOC_TYPE_RIGHTS;
            }

            if (defined('DOC_TYPE_DOCUMENT_REGISTER')) {
                $options_schedules[] = DOC_TYPE_DOCUMENT_REGISTER;
            }
            if (defined('DOC_TYPE_OFFER_CHANGING_DOCUMENT')) {
                $options_schedules[] = DOC_TYPE_OFFER_CHANGING_DOCUMENT;
            }
            if (defined('DOC_TYPE_ACCEPTANCE_LIST')) {
                $options_schedules[] = DOC_TYPE_ACCEPTANCE_LIST;
            }
            if (defined('DOC_TYPE_RECORDS_MATRIX')) {
                $options_schedules[] = DOC_TYPE_RECORDS_MATRIX;
            }
            if (defined('DOC_TYPE_AUDIT_PROGRAM')) {
                $options_schedules[] = DOC_TYPE_AUDIT_PROGRAM;
            }
            if (defined('DOC_TYPE_INSIDE_AUDIT_PLAN')) {
                $options_schedules[] = DOC_TYPE_INSIDE_AUDIT_PLAN;
            }
            if (defined('DOC_TYPE_INSIDE_AUDIT_REPORT')) {
                $options_schedules[] = DOC_TYPE_INSIDE_AUDIT_REPORT;
            }
            if (defined('DOC_TYPE_OFFER_KPD')) {
                $options_schedules[] = DOC_TYPE_OFFER_KPD;
            }
            if (defined('DOC_TYPE_KPD_REGISTER')) {
                $options_schedules[] = DOC_TYPE_KPD_REGISTER;
            }

            $final_records = array();

            if (!empty($filters['show'])) {
                $options_documents_name = 'options_' . $filters['show'];
                $options_documents = $$options_documents_name;

                if (!empty($options_documents)) {
                    $final_records['additional_options']['filter_value'] = $filters['show'];

                    if ($filters['show'] == 'schedules') {
                        //sql to take the ids of the needed additional vars 
                        $sql_for_add_vars = 'SELECT fm.id, fm.name, fm.model_type, fm.layout_id ' . "\n" . 
                                            'FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" . 
                                            'WHERE fm.model="Document" AND fm.model_type IN (' . implode(',', $options_documents) . ') AND (fm.name="' . AFORM_DATE . '" OR fm.name="' . AFORM_CREATOR . '" OR fm.name="' . AFORM_PROTECTOR . '" OR fm.name="' . AFORM_TERM_PROT . '" OR fm.name="' . AFORM_LINK . '") ORDER BY fm.position';
                        $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                        $doc_types_vars = array();
                        foreach ($var_ids as $var) {
                            $doc_types_vars[$var['model_type']][$var['name']] = $var['id'];
                            $doc_types_vars[$var['model_type']][$var['name'] . '_layout'] = $var['layout_id'];
                        }

                        $documents_list = array();

                        foreach ($doc_types_vars as $doc_id => $doc_type) {
                            $sql = array();

                            //sql to take all rows of documents_cstm table data
                            $sql['select']    =  'SELECT d.id AS id, d.full_num AS full_num, d.type AS type,' . "\n" . 
                                                 '  d.active AS active,  di18n.name AS name, dt.direction AS direction, ' . "\n" . 
                                                 '  DATE_FORMAT(d_cstm_aform_date.value, "%Y-%m-%d") AS aform_date, ' . $doc_type[AFORM_DATE . '_layout'] . ' AS aform_date_layout, ' . "\n" . 
                                                 '  d_cstm_aform_creator.value AS aform_creator, ' . $doc_type[AFORM_CREATOR . '_layout'] . ' AS aform_creator_layout, ' . "\n" . 
                                                 '  d_cstm_aform_protector.value AS aform_protector, ' . $doc_type[AFORM_PROTECTOR . '_layout'] . ' AS aform_protector_layout, ' . "\n" . 
                                                 '  d_cstm_aform_term_prot.value AS aform_term_prot, ' . $doc_type[AFORM_TERM_PROT . '_layout'] . ' AS aform_term_prot_layout, ' . "\n" . 
                                                 '  d_cstm_aform_link.value AS aform_link ' . "\n";

                            $sql['from']      =   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                  'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                                  '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                                  '  ON (d.id=di18n.parent_id)' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_aform_date' . "\n" .
                                                  '  ON (d_cstm_aform_date.model_id=d.id AND d_cstm_aform_date.var_id="' . $doc_type[AFORM_DATE] . '")' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_aform_creator' . "\n" .
                                                  '  ON (d_cstm_aform_creator.model_id=d.id AND d_cstm_aform_creator.var_id="' . $doc_type[AFORM_CREATOR] . '")' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_aform_protector' . "\n" .
                                                  '  ON (d_cstm_aform_protector.model_id=d.id AND d_cstm_aform_protector.var_id="' . $doc_type[AFORM_PROTECTOR] . '")'  . "\n" .  
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_aform_term_prot' . "\n" .
                                                  '  ON (d_cstm_aform_term_prot.model_id=d.id AND d_cstm_aform_term_prot.var_id="' . $doc_type[AFORM_TERM_PROT] . '")' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_aform_link' . "\n" .
                                                  '  ON (d_cstm_aform_link.model_id=d.id AND d_cstm_aform_link.var_id="' . $doc_type[AFORM_LINK] . '")' . "\n";

                            $where = array();

                            $where[] = 'd.active=1';
                            $where[] = 'd.deleted_by=0';
                            $where[] = 'd.type ="' . $doc_id . '"';
                            
                            $sql['where'] = 'WHERE ' . implode(' AND ', $where) . "\n";

                            $sql['order'] = ' ORDER BY d.added DESC' . "\n";
                            $sql['limit'] = ' LIMIT 0,1' . "\n";

                            //search basic details with current lang parameters
                            $query = implode("\n", $sql);
                            $records = $registry['db']->GetAll($query);

                            if (isset($records[0])) {
                                $final_records[] = $records[0];
                            }
                        }
                    } else {
                        //sql to take the ids of the needed additional vars 
                        $sql_for_add_vars = 'SELECT fm.id, fm.name, fm.model_type, fm.layout_id ' . "\n" . 
                                            'FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" . 
                                            'WHERE fm.model="Document" AND fm.model_type IN (' . implode(',', $options_documents) . ') AND (fm.name="' . ACTUAL_VERSION_DATE . '" OR fm.name="' . ACTUAL_CHANGE_NUM . '" OR fm.name="' . ACTUAL_CHANGE_DATE . '" OR fm.name="' . FORM_NUM . '" OR fm.name="' . ACTUAL_VERSION_LINK . '") ORDER BY fm.position';
                        $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                        $doc_types_vars = array();
                        foreach ($var_ids as $var) {
                            $doc_types_vars[$var['model_type']][$var['name']] = $var['id'];
                            $doc_types_vars[$var['model_type']][$var['name'] . '_layout'] = $var['layout_id'];
                        }

                        $documents_list = array();

                        foreach ($doc_types_vars as $doc_id => $doc_type) {
                            $sql = array();

                            //sql to take all rows of documents_cstm table data
                            $sql['select']    =  'SELECT d.id AS id, d.full_num AS full_num, d.type AS type,' . "\n" . 
                                                 '  d.active AS active,  di18n.name AS name, dt.direction AS direction, ' . "\n" . 
                                                 '  DATE_FORMAT(d_cstm_actual_version_date.value, "%Y-%m-%d") AS actual_version_date, ' . $doc_type[ACTUAL_VERSION_DATE . '_layout'] . ' AS actual_version_date_layout, ' . "\n" . 
                                                 '  d_cstm_actual_change_num.value AS actual_change_num, ' . $doc_type[ACTUAL_CHANGE_NUM . '_layout'] . ' AS actual_change_num_layout, ' . "\n" . 
                                                 'DATE_FORMAT(d_cstm_actual_change_date.value, "%Y-%m-%d") AS actual_change_date, ' . $doc_type[ACTUAL_CHANGE_DATE . '_layout'] . ' AS actual_change_date_layout, ' . "\n" . 
                                                 '  d_cstm_form_num.value AS form_num, ' . $doc_type[FORM_NUM . '_layout'] . ' AS form_num_layout, ' . "\n" . 
                                                 '  d_cstm_actual_version_link.value AS actual_version_link ' . "\n";

                            $sql['from']      =   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                  'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                                  '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                                  '  ON (d.id=di18n.parent_id)' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_actual_version_date' . "\n" .
                                                  '  ON (d_cstm_actual_version_date.model_id=d.id AND d_cstm_actual_version_date.var_id="' . $doc_type[ACTUAL_VERSION_DATE] . '")' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_actual_change_num' . "\n" .
                                                  '  ON (d_cstm_actual_change_num.model_id=d.id AND d_cstm_actual_change_num.var_id="' . $doc_type[ACTUAL_CHANGE_NUM] . '")' . "\n" . 
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_actual_change_date' . "\n" .
                                                  '  ON (d_cstm_actual_change_date.model_id=d.id AND d_cstm_actual_change_date.var_id="' . $doc_type[ACTUAL_CHANGE_DATE] . '")'  . "\n" .  
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_form_num' . "\n" .
                                                  '  ON (d_cstm_form_num.model_id=d.id AND d_cstm_form_num.var_id="' . $doc_type[FORM_NUM] . '")' . "\n" .  
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_actual_version_link' . "\n" .
                                                  '  ON (d_cstm_actual_version_link.model_id=d.id AND d_cstm_actual_version_link.var_id="' . $doc_type[ACTUAL_VERSION_LINK] . '")' . "\n";

                            $where = array();

                            $where[] = 'd.active=1';
                            $where[] = 'd.deleted_by=0';
                            $where[] = 'd.type ="' . $doc_id . '"';
                            
                            $sql['where'] = 'WHERE ' . implode(' AND ', $where) . "\n";

                            $sql['order'] = ' ORDER BY d.added DESC' . "\n";
                            $sql['limit'] = ' LIMIT 0,1' . "\n";

                            //search basic details with current lang parameters
                            $query = implode("\n", $sql);
                            $records = $registry['db']->GetAll($query);

                            if (isset($records[0])) {
                                $final_records[] = $records[0];
                            }
                        }
                    }
                }
            }

            // query if paginate is needed
            if (!empty($filters['paginate'])) {
                $results = array($final_records, 0);
            } else {
                //no pagination required return only the models
                $results = $final_records;
            }

            return $results;
        }
    }
?>