<?php
    Class Deliverers_Obligations Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

           // array with included types for the main search
           $other_expenses_reason = preg_split('/\s*,\s*/', FINANCE_EXPENSE);
            foreach ($other_expenses_reason as $key => $reason_type) {
                if (empty($reason_type)) {
                    unset($other_expenses_reason[$key]);
                }
            }

           $available_main_documents = array_merge($other_expenses_reason, array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE));
           $available_related_documents = array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE);

            // get the expenses reason which matches the criteria
            $sql_expenses_reason = 'SELECT fer.id as idx, fer.id, fer.type' . "\n" .
                                   'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                   'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                   ' ON (fer.id=frr.link_to AND frr.parent_model_name="Finance_Expenses_Reason" AND frr.link_to_model_name="Finance_Expenses_Reason")' . "\n" .
                                   'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer2' . "\n" .
                                   ' ON (frr.parent_id=fer2.id AND fer2.type IN (' . implode(',', $available_related_documents) . ') AND fer2.annulled_by=0 AND fer2.active = 1)' . "\n";
            $where = array();
            $where[] = '(frr.parent_id IS NULL OR fer2.id IS NOT NULL)';
            $where[] = 'fer.annulled_by=0';
            $where[] = 'fer.status = "finished"';
            $where[] = 'fer.active = 1';
            $where[] = 'fer.payment_status NOT IN ("nopay", "paid")';
            $where[] = 'fer.type IN (' . implode(',', $available_main_documents) . ')';
            if (!empty($filters['customer'])) {
                $where[] = 'fer.customer="' . $filters['customer'] . '"';
            }
            $sql_expenses_reason .= 'WHERE ' . implode(' AND ', $where) . "\n";
            $sql_expenses_reason .= 'ORDER BY fer.id ASC' . "\n";
            $main_expenses_reasons = $registry['db']->getAssoc($sql_expenses_reason);

            // find out the proformas
            $proforma_invoices = array();
            $proformas_relations = array();
            $searched_expenses_reasons = array_keys($main_expenses_reasons);

            foreach ($main_expenses_reasons as $mer) {
                if ($mer['type'] == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                    $proforma_invoices[] = $mer['id'];
                }
            }

            if (!empty($proforma_invoices) && !empty($other_expenses_reason)) {
                // search for expenses reasons from custom type
                $sql_custom_expenses_reason = 'SELECT frr.parent_id as idx, frr.link_to' . "\n" .
                                              'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                              'INNER JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                              ' ON (fer.id=frr.link_to AND fer.annulled_by = 0 AND fer.status = "finished" AND fer.active = 1 AND fer.payment_status NOT IN ("nopay", "paid") AND fer.type IN ("' . implode('","', $other_expenses_reason) . '"))' . "\n" .
                                              'WHERE frr.parent_id IN (' . implode(',', $proforma_invoices) . ')  AND frr.parent_model_name="Finance_Expenses_Reason" AND frr.link_to_model_name="Finance_Expenses_Reason"';
                $proformas_relations = $registry['db']->getAssoc($sql_custom_expenses_reason);
                $searched_expenses_reasons = array_merge($searched_expenses_reasons, array_values($proformas_relations));
            }
            unset($proforma_invoices);

            // find out all credit and debit notices
            $sql_expenses_notices = 'SELECT fer.id as idx, fer.id, fer.type' . "\n" .
                                    'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                    'WHERE fer.annulled_by=0 AND fer.status = "finished" AND fer.active = 1 AND fer.payment_status NOT IN ("nopay", "paid") AND fer.type IN (' . implode(',', $available_related_documents) . ')' . (!empty($filters['customer']) ? ' AND fer.customer="' . $filters['customer'] . '"' : '') . ' ORDER BY fer.id ASC';
            $notices_expenses_reasons = $registry['db']->getAssoc($sql_expenses_notices);
            $searched_expenses_reasons = array_merge($searched_expenses_reasons, array_keys($notices_expenses_reasons));
            $searched_expenses_reasons = array_unique($searched_expenses_reasons);

            $final_results = array();

            // take the main currency
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $main_currency = Finance_Currencies::getMain($registry);
            $currency_multiplier = array();
            $currency_multipliers[$main_currency . '->' . $main_currency] = 1;

            $total_obligations = 0;
            $total_not_distributed = 0;
            $total_balance = 0;
            if (!empty($searched_expenses_reasons)) {
                // retrieve the needed models
                require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
                $filters_expenses_reasons=array('model_lang'   => $model_lang,
                                                'sanitize'     => true,
                                                'sort'         => array('fer.id DESC'),
                                                'where'        => array(
                                                    'fer.id IN (' . implode(',', $searched_expenses_reasons) . ')'
                                                )
                );

                $expenses_reasons_list = Finance_Expenses_Reasons::search($registry, $filters_expenses_reasons);

                $expenses_reasons_list_reindexed = array();
                // reindex the array
                foreach ($expenses_reasons_list as $exp_reason) {
                    $expenses_reasons_list_reindexed[$exp_reason->get('id')] = $exp_reason;
                }
                unset($expenses_reasons_list);

                // goes through every expense reason
                foreach ($expenses_reasons_list_reindexed as $exp_reason) {
                    if (in_array($exp_reason->get('id'), $proformas_relations)) {
                        // if document is related to a proforma then it has to be skipped
                        continue;
                    }

                    if ($exp_reason->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE && isset($proformas_relations[$exp_reason->get('id')]) && isset($expenses_reasons_list_reindexed[$proformas_relations[$exp_reason->get('id')]])) {
                        // if the document is proforma then it should be check if it is created from expense (type 101)
                        $use_model_for_calculation = $expenses_reasons_list_reindexed[$proformas_relations[$exp_reason->get('id')]];
                    } else {
                        $use_model_for_calculation = $exp_reason;
                    }

                    $use_model_for_calculation->unsanitize();
                    $current_sum_to_be_paid = 0;
                    $current_multiplier = 1;
                    if ($use_model_for_calculation->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                        $current_sum_to_be_paid = $use_model_for_calculation->get('total_with_vat');
                    } else {
                        $use_model_for_calculation->getPaymentsDistribution('');
                        $current_sum_to_be_paid = $use_model_for_calculation->get('remaining_amount');
                    }

                    if ($use_model_for_calculation->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                        $current_sum_to_be_paid = $current_sum_to_be_paid * (-1);
                    }

                    // if the sum is 0 then we don't have nothing to pay for this document
                    if (!$current_sum_to_be_paid) {
                        continue;
                    }

                    if (!isset($final_results[$exp_reason->get('customer')])) {
                        $final_results[$exp_reason->get('customer')] = array(
                            'id'                       => $exp_reason->get('customer'),
                            'name'                     => $exp_reason->get('customer_name'),
                            'obligations'              => 0,
                            'not_distributed_payments' => 0,
                            'balance'                  => 0,
                            'documents'                => array(),
                            'customer_encoded_data'    => ''
                        );
                    }

                    $currency_key = $exp_reason->get('currency') . '->' . $main_currency;
                    if (!isset($currency_multipliers[$currency_key])) {
                        $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $exp_reason->get('currency'), $main_currency);
                    }
                    $current_multiplier = $currency_multipliers[$currency_key];

                    $calculated_value = round(($current_sum_to_be_paid*$current_multiplier), 2);
                    $final_results[$exp_reason->get('customer')]['obligations'] += $calculated_value;
                    $total_obligations += $calculated_value;
                    $final_results[$exp_reason->get('customer')]['documents'][$exp_reason->get('id')] = array(
                        'id'         => $exp_reason->get('id'),
                        'sum'        => $calculated_value
                    );
                }

                unset($expenses_reasons_list_reindexed);
            }

            if (!empty($final_results)) {
                // get all the payments RKO and PN for the selected deliverers
                $sql_payments = 'SELECT p.customer, p.id as payment, p.amount AS payments_amount, p.currency AS currency_amount, b.paid_amount AS distributed_amount, b.paid_currency as distributed_currency' . "\n" .
                                'FROM ' . DB_TABLE_FINANCE_PAYMENTS . ' AS p' . "\n" .
                                'LEFT JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS b' . "\n" .
                                ' ON (b.paid_to=p.id AND b.paid_to_model_name="Finance_Payment")' . "\n" .
                                'WHERE p.customer IN (' . implode(',', array_keys($final_results)) . ') AND p.annulled_by=0 AND (p.type="PN" OR p.type="RKO")' . "\n";

                // execute query
                $payments_distribution = $registry['db']->getAll($sql_payments);
                $distributed_payments = array();

                // calculates the distributed sum of the payments
                foreach ($payments_distribution as $pay) {
                    $payment_multiplier = 1;
                    $payment_multiplier_key = $pay['currency_amount'] . '->' . $main_currency;
                    if (!isset($currency_multipliers[$payment_multiplier_key])) {
                        $currency_multipliers[$payment_multiplier_key] = Finance_Currencies::getRate($registry, $pay['currency_amount'], $main_currency);
                    }
                    $payment_multiplier = $currency_multipliers[$payment_multiplier_key];

                    if (!isset($distributed_payments[$pay['payment']])) {
                        $distributed_payments[$pay['payment']] = array(
                            'id'              => $pay['payment'],
                            'customer'        => $pay['customer'],
                            'not_distributed' => $payment_multiplier*$pay['payments_amount']
                        );
                    }

                    $distribution_multiplier = 1;
                    $distribution_multiplier_key = $pay['distributed_currency'] . '->' . $main_currency;
                    if (!isset($currency_multipliers[$distribution_multiplier_key])) {
                        $currency_multipliers[$distribution_multiplier_key] = Finance_Currencies::getRate($registry, $pay['distributed_currency'], $main_currency);
                    }
                    $distribution_multiplier = $currency_multipliers[$distribution_multiplier_key];

                    $distributed_payments[$pay['payment']]['not_distributed'] = $distributed_payments[$pay['payment']]['not_distributed'] - ($distribution_multiplier*$pay['distributed_amount']);
                }

                // sets the data to the final results
                foreach ($distributed_payments as $payments_data) {
                    if (isset($final_results[$payments_data['customer']])) {
                        $final_results[$payments_data['customer']]['not_distributed_payments'] = round($payments_data['not_distributed'] + $final_results[$payments_data['customer']]['not_distributed_payments'], 2);
                    }
                }
            }

            foreach ($final_results as $key => $deliverer) {
                $final_results[$key]['balance'] = round(($deliverer['obligations'] - $deliverer['not_distributed_payments']), 2);
                $total_not_distributed = round(($deliverer['not_distributed_payments'] + $total_not_distributed), 2);
                $total_balance = round(($final_results[$key]['balance'] + $total_balance), 2);
                $final_results[$key]['customer_encoded_data'] = base64_encode(json_encode($final_results[$key]));
            }

            // process the sort
            if (defined('SORT_BY_CUSTOMER_NAME') && constant('SORT_BY_CUSTOMER_NAME')) {
                usort($final_results, function($as, $bs) { return strcasecmp($as['name'], $bs['name']); });
            }

            $final_results['additional_options']['main_currency'] = $main_currency;
            $final_results['additional_options']['total_obligations'] = $total_obligations;
            $final_results['additional_options']['total_not_distributed'] = $total_not_distributed;
            $final_results['additional_options']['total_balance'] = $total_balance;

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>
