<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="3" cellspacing="0">
      <tr>
        <td style="vertical-align: middle; text-align: center;" width="70"><strong>{#reports_service_request_num#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="80"><strong>{#reports_service_request_date#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="230"><strong>{#reports_service_request_customer#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="200"><strong>{#reports_service_request_status#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="210"><strong>{#reports_service_request_type#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="210"><strong>{#reports_service_request_kind#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="80"><strong>{#reports_service_request_warranty_until#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="110"><strong>{#reports_service_request_status_warranty#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="160"><strong>{#reports_service_request_diagnostic_result#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="270"><strong>{#reports_service_request_type_problem#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center;" width="270"><strong>{#reports_service_request_part#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results item=result name=results}
        {foreach from=$result.rows item=row name=rr}
          <tr>
            {if $smarty.foreach.rr.first}
              <td style="vertical-align: middle; mso-number-format:\@;" rowspan="{$result.rowspan}">
                {$result.full_num|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$result.rowspan}">
                {$result.request_date|date_format:#date_short#|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
                {$result.customer|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
                {$result.substatus|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
                {$result.type_machine|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
                {$result.kind_machine|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$result.rowspan}">
                {$result.warranty_until|date_format:#date_short#|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
                {$result.warranty_status|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
                {$result.diagnosis_result|escape|default:"&nbsp;"}
              </td>
            {/if}
            <td style="vertical-align: middle;">
              {$row.problem|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">
              {$row.part|default:"&nbsp;"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{$current_class}">
            <td style="vertical-align: middle; mso-number-format:\@;" rowspan="{$result.rowspan}">
              {$result.full_num|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$result.rowspan}">
              {$result.request_date|date_format:#date_short#|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              {$result.customer|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              {$result.substatus|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              {$result.type_machine|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              {$result.kind_machine|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$result.rowspan}">
              {$result.warranty_until|date_format:#date_short#|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              {$result.warranty_status|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              {$result.diagnosis_result|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              &nbsp;
            </td>
            <td style="vertical-align: middle;" rowspan="{$result.rowspan}">
              &nbsp;
            </td>
          </tr>
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="11">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>
