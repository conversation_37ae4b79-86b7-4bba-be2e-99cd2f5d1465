<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td>
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
    <tr class="reports_title_row hcenter">
      <td class="t_border" nowrap="nowrap">{#num#|escape}</td>
      <td class="t_border"><div style="width: 160px;">{#documents_full_num#|escape}</div></td>
      <td class="t_border"><div style="width: 110px;">{#documents_custom_num#|escape}</div></td>
      <td class="t_border"><div style="width: 250px;">{#documents_customer#|escape}</div></td>
      <td class="t_border"><div style="width: 170px;">{#documents_type#|escape}</div></td>
      <td class="t_border"><div style="width: 290px;">{#documents_description#|escape}</div></td>
      <td><div style="width: 290px;">{#documents_notes#|escape}</div></td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach from=$reports_results item=result}
      <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}">
        <td class="t_border hright" nowrap="nowrap" width="25">
          {counter name='item_counter' print=true}
        </td>
        <td class="t_border">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}{if $result.archive}&amp;archive=1{/if}">{$result.full_num|numerate:$result.direction}</a>
        </td>
        <td class="t_border">
          {$result.custom_num|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.id}">{$result.customer_name|escape|default:"&nbsp;"}</a>
        </td>
        <td class="t_border">
          {$result.type_name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.description|escape|default:"&nbsp;"}
        </td>
        <td>
          {$result.notes|escape|default:"&nbsp;"}
        </td>
      </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="7">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    <tr>
      <td class="t_footer" colspan="7"></td>
    </tr>
  </table>
</td>
</tr>
</table>