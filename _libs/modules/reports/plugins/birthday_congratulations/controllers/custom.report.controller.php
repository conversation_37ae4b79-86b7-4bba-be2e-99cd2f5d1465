<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Name of the report
     */
    public $current_report_name = 'birthday_congratulations';

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        $found = false;
        switch ($this->action) {
            case 'generate_preview':
                $this->_generatePreview();
                $found = true;
                break;
            case 'delivery_receipt':
                $this->_deliveryReceipt();
                $found = true;
                break;
        }

        if (!$found) {
            parent::execute();
        }
    }

    /**
     * Validates that there are selected templates for e-mail and/or sms
     * when there are selected recipients.
     *
     * @param array $selected - array containing selected items
     */
    public function validateSelection($selected) {
        $request = &$this->registry['request'];

        $send_email = false;
        $send_sms = false;
        foreach ($selected['ids'] as $sel) {
            if (preg_match('#^(email|sms)_\d+#', $sel, $matches)) {
                $flag = 'send_' . $matches[1];
                $$flag = true;
                continue;
            }
            if ($send_email && $send_sms) {
                break;
            }
        }

        if ($send_email && !$request->get('email_template') || $send_sms && !$request->get('sms_template')) {
            if ($send_email && !$request->get('email_template')) {
                $this->registry['messages']->setError($this->i18n('error_reports_no_template_type',
                                                                  array($this->i18n('reports_email'))));
            }
            if ($send_sms && !$request->get('sms_template')) {
                $this->registry['messages']->setError($this->i18n('error_reports_no_template_type',
                                                                  array($this->i18n('reports_sms'))));
            }
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, '', array('report_type' => $this->current_report_name), '');
            exit;
        }

    }

    /**
     * Send e-mails and smses.
     *
     * @param array $settings - associative array with report settings.
     */
    public function customEmailSend(&$settings) {

        $registry = &$this->registry;
        $request = &$this->registry['request'];

        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report . '/custom.report.query.php';
        $report_factory_name = preg_replace('#\s+#', '_', ucwords(preg_replace('#_#', ' ', $report)));

        //get current report
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];

        $records = $report_factory_name::getCustomData($this->registry, $settings);

        $this->result = array('report_name' => $report->get('name'),
                              'additional_info' => array(),
                              'total_emails' => count($records['email']),
                              'erred_emails' => 0);
        if (!empty($settings['send_sms'])) {
            $this->result['total_smses'] = count($records['sms']);
            $this->result['erred_smses'] = 0;
        }

        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.model.php';

        // custom sender name and e-mail
        $sender_email = !empty($settings['sender_email']) && Validator::validEmail($settings['sender_email']) ?
            $settings['sender_email'] : $this->registry['currentUser']->get('email');
        $sender_name = '';
        if (!empty($settings['sender_name_' . $this->registry['lang']])) {
            $sender_name = $settings['sender_name_' . $this->registry['lang']];
        } elseif (!empty($settings['sender_name'])) {
            $sender_name = $settings['sender_name'];
        } else {
            $sender_name = $this->registry['currentUser']->get('display_name') ?
                $this->registry['currentUser']->get('display_name') :
                $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname');
        }

        foreach ($records['email'] as $idx => $record) {
            $registry['db']->StartTrans();

            if ($record['email']) {
                //set some vars in the report model to get patterns vars
                foreach ($record as $k => $v) {
                    $report->set($k, $v, true);
                }

                // customer id
                $record['id'] = $idx;
                $model = new Customer($this->registry, $record);

                $report->set('customer', $idx, true);
                $placeholders = $report->getPatternsVars();
                $model->extender = new Extender();
                $model->extender->model_lang = $registry['lang'];
                $model->extender->module = $this->module;
                foreach ($placeholders as $key => $value) {
                    $model->extender->add($key, $value);
                }

                $valid_email = '';
                foreach ($model->get('email') as $email) {
                    if (Validator::validEmail($email)) {
                        $valid_email = $email;
                        break;
                    }
                }

                if ($request->get('email_template') && $valid_email) {

                    $recipients = array($record['name'] . ' ' . $record['lastname'] . ' <' . $valid_email . '>');
                    $model->set('customer_email', $recipients, true);

                    $filters = array('where' => array('e.id = \'' . $request->get('email_template') . '\''),
                                     'sanitize' => true);
                    $mail = Emails::searchOne($registry, $filters);
                    if (!empty($mail) && $mail->get('body')) {
                        $body = $model->extender->expand($mail->get('body'));
                        $model->set('body', $body, true);
                        $model->set('email_subject', $mail->get('subject'), true);
                        $model->set('email_template', $mail->get('id'), true);
                        $extra = 'report := ' . $report->get('type');
                        $model->set('mail_additional_extra', $extra, true);
                        $model->set('custom_sender', $sender_email, true);
                        $model->set('custom_from_name', $sender_name, true);

                        if ($result = $model->sendAsMail()) {
                            Customers_History::saveData(
                                $registry,
                                array(
                                    'model' => $model,
                                    'action_type' => 'email',
                                    'old_model' => $model,
                                    'new_model' => $model,
                                ));
                        }
                    } else {
                        $result['erred'] = 1;
                    }
                } else {
                    $result['erred'] = 1;
                }

                if (!empty($result['erred'])) {
                    $this->result['erred_emails'] ++;
                    $s = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                                 $_SERVER['PHP_SELF'], $registry->get('module_param'), 'customers',
                                 'customers', 'view', 'view', $idx, $record['name'] . ' ' . $record['lastname']);
                    $this->result['additional_info'][] = sprintf($this->i18n('error_reports_email_not_sent'), $s);
                }

                unset($model);
            } else {
                $this->result['erred_emails'] ++;
                $s = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                              $_SERVER['PHP_SELF'], $registry->get('module_param'), 'customers',
                              'customers', 'view', 'view', $idx, $record['name'] . ' ' . $record['lastname']);
                $this->result['additional_info'][] = sprintf($this->i18n('error_reports_invalid_email'), $s);
            }

            $registry['db']->CompleteTrans();
        }

        if (!empty($records['sms'])) {
            // check if report has path for request to sms server, username and password specified.
            // sms method must either exist in this class or no setting specified (then take default sms method)
            if (empty($settings['sms_server_path']) || empty($settings['sms_username']) || empty($settings['sms_password']) ||
            (!empty($settings['sms_method']) && !method_exists($this, $settings['sms_method']))) {
                $this->result['error_sms_settings'] = $this->i18n('reports_error_sms_settings');
                unset($this->result['total_smses']);
                unset($this->result['erred_smses']);
                return;
            }

            // default regular expression for valid numbers for Bulgarian mobile operators
            $default_mobile_number_regexp = '(\n|^)08[789][0-9]{7}(\n|\||$)';
            $mobile_number_regexp = sprintf('#%s#', (!empty($settings['mobile_number_regexp']) ? $settings['mobile_number_regexp'] : $default_mobile_number_regexp));

            require_once PH_MODULES_DIR . 'comments/models/comments.model.php';

            foreach ($records['sms'] as $idx => $record) {
                $registry['db']->StartTrans();

                if ($record['gsm']) {
                    //set some vars in the report model to get patterns vars
                    foreach ($record as $key => $value) {
                        $report->set($key, $value, true);
                    }

                    // customer id
                    $record['id'] = $idx;
                    $model = new Customer($this->registry, $record);

                    $report->set('customer', $idx, true);
                    $placeholders = $report->getPatternsVars();
                    $model->extender = new Extender();
                    $model->extender->model_lang = $registry['lang'];
                    $model->extender->module = $this->module;
                    foreach ($placeholders as $key => $value) {
                        $model->extender->add($key, $value);
                    }

                    // gsm and phone numbers are merged into "gsm" field
                    $phones = $model->get('gsm');
                    $phone = '';

                    if (is_array($phones)) {
                        foreach ($phones as $p) {
                            // get first valid mobile phone number
                            if (preg_match($mobile_number_regexp, $p)) {
                                $phone = $p;
                                break;
                            }
                        }
                    }

                    if ($request->get('sms_template') && $phone) {
                        $filters = array('where' => array('e.id = \'' . $request->get('sms_template') . '\''),
                                         'sanitize' => true);
                        $sms = Emails::searchOne($registry, $filters);
                        if (!empty($sms) && $sms->get('body')) {
                            $message = Transliterate::convert($model->extender->expand(General::html2text($sms->get('body'))));
                            $status_text = sprintf('%s %s',
                                                   $this->i18n('reports_status'),
                                                   $this->i18n('reports_status_sent'));
                            $params = array (
                                'model' => 'Customer',
                                'model_id' => $model->get('id'),
                                'content' => $message . "\r\n". $status_text,
                                'subject' => sprintf('%s #%s#', $sms->get('subject'), $report->get('type'))
                            );
                            $comment = new Comment($this->registry, $params);

                            if ($comment->save()) {
                                $comment->saveHistory($model);

                                $params = array('phone' => $phone,
                                                'message' => $message,
                                                'from' => Transliterate::convert($sender_name),
                                                'idd' => $comment->get('id'));

                                $sms_method = !empty($settings['sms_method']) ? $settings['sms_method'] : 'sendProSMS';

                                $result = $this->$sms_method($params, $settings);

                                if (!empty($result['erred'])) {
                                    $status_text = sprintf('%s %s',
                                                           $this->i18n('reports_status'),
                                                           $this->i18n('reports_status_not_sent'));
                                    $upd = $report_factory_name::updateSMSStatus($this->registry, $comment->get('id'), $status_text);
                                }
                            } else {
                                $result['erred'] = 1;
                            }

                            unset($comment);
                        } else {
                            $result['erred'] = 1;
                        }
                    } else {
                        $result['erred'] = 1;
                    }

                    if (!empty($result['erred'])) {
                        $this->result['erred_smses'] ++;
                        $s = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                                     $_SERVER['PHP_SELF'], $registry->get('module_param'), 'customers',
                                     'customers', 'view', 'view', $idx, $record['name'] . ' ' . $record['lastname']);
                        $this->result['additional_info'][] = sprintf($this->i18n('error_reports_sms_not_sent'), $s);
                    }

                    unset($model);
                } else {
                    $this->result['erred_smses'] ++;
                    $s = sprintf('<a href="%s?%s=%s&%s=%s&%s=%s" target="_blank">%s</a>',
                                  $_SERVER['PHP_SELF'], $registry->get('module_param'), 'customers',
                                  'customers', 'view', 'view', $idx, $record['name'] . ' ' . $record['lastname']);
                    $this->result['additional_info'][] = sprintf($this->i18n('error_reports_invalid_gsm'), $s);
                }

                $registry['db']->CompleteTrans();
            }
        }
    }

    /**
     * Method for sending short messages using Pro-SMS service provider.
     * @link http://www.pro-sms.eu
     *
     * @param array $params - params (phone, message, from, idd)
     * @param array $settings - report settings (sms_server_path, sms_username, sms_password, sms_dlr)
     * @return mixed - result of operation
     */
    private function sendProSMS ($params, $settings) {
        $sms_server_path = $settings['sms_server_path'];
        $user = urlencode($settings['sms_username']);
        $pass = urlencode($settings['sms_password']);

        // valid number or alphabetical (no more than 11 characters)
        $from = urlencode(substr($params['from'], 0, 11));

        $idd = $params['idd'];
        $dlr = isset($settings['sms_dlr']) ? !empty($settings['sms_dlr']) : 1;
        $phone = $params['phone'];

        // up to 480 characters (will be split into 3 messages then)
        $message = urlencode(substr($params['message'], 0, 480));

        $sms_location = sprintf('http://%s?uname=%s&pass=%s&from=%s&idd=%s&dlr=%d&phone=%s&message=%s',
                                $sms_server_path, $user, $pass,
                                $from, $idd, $dlr, $phone, $message);

        $x = @file($sms_location);

        if (empty($x) || $x[0][0] !== '0') {
            return array('erred' => 1);
        }

        return true;
    }

    /**
     * Display preview of e-mail or sms.
     */
    private function _generatePreview() {

        $registry = &$this->registry;
        $request = &$this->registry['request'];

        $report = $this->getReportType();
        $report = $report['name'];
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }

        //get current report
        $report = $this->getReportType();
        $report = $report['name'];
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $this->registry['translater']->loadFile($i18n_file);

        //prepare report settings
        $settings = $report->get('settings');
        $settings = preg_split('#\r\n|\n|\r#', $settings);
        foreach ($settings as $key => $value) {
            unset($settings[$key]);
            if (preg_match('#(^\#)|(^\s*$)#', $value)) continue;
            $value = preg_split('#\s*:=\s*#', $value);
            $settings[trim($value[0])] = trim($value[1]);
        }

        require_once PH_MODULES_DIR . 'reports/plugins/' . $report->get('type') . '/custom.report.query.php';
        $report_factory_name = preg_replace('#\s+#', '_', ucwords(preg_replace('#_#', ' ', $report->get('type'))));

        $customer_id = $request->get($this->action);
        $filters = array('v.customer_id = \'' . $customer_id . '\'',
                         'v.type = \'' . $request->get('type') . '\'');
        $record = $report_factory_name::getCustomData($this->registry, $settings, $filters);

        $record = isset($record[$customer_id]) ? $record[$customer_id] : '';

        $error = false;
        $preview_content = '';

        if (empty($record)) {
            $error = true;
            $preview_content .= $this->registry['translater']->translate('error_reports_invalid_customer');
        } else {
            foreach ($record as $key => $value) {
                if ($request->get('type') == 'sms') {
                    $value = Transliterate::convert($value);
                }
                $report->set($key, $value, true);
            }
        }

        $report->set('customer', $customer_id, true);
        $placeholders = !empty($record) ? $report->getPatternsVars() : array();
        $extender = new Extender();
        $extender->model_lang = $registry['lang'];
        $extender->module = $this->module;
        foreach ($placeholders as $key => $value) {
            $extender->add($key, $value);
        }

        if (!$request->get('template')) {
            $error = true;
            $preview_content .= $this->registry['translater']->translate('error_reports_no_template');
        } else {
            require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
            $filters = array('where' => array('e.id = \'' . $request->get('template') . '\''),
                             'model_lang' => $registry['lang'],
                             'sanitize' => true);
            $mail_template = Emails::searchOne($this->registry, $filters);

            $mail_body = '';
            if (!empty($mail_template)) {
                $mail_body = $mail_template->get('body');
                if ($mail_template->get('name') == 'sms') {
                    $mail_body = Transliterate::convert(General::html2text($mail_body, '<br>'));
                }
            }
            if (empty($mail_template) || empty($mail_body)) {
                $error = true;
                $preview_content .= $this->registry['translater']->translate('error_reports_invalid_template');
            }

            if (!$error) {
                $preview_content = $extender->expand($mail_body);
            }
        }

        $viewer = new Viewer($registry);
        $viewer->data['preview_content'] = $preview_content;
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $report->get('type') . '/';
        $viewer->template = '_preview.html';
        $viewer->display();

        die;
    }

    /**
     * Text of announcement to user for sent e-mails and smses.
     */
    public function prepareAnnouncementContent() {

        $content = sprintf('%s: <b>%s</b><br /><br />', $this->i18n('report'), $this->result['report_name']);
        if (!empty($this->result['total_emails'])) {
            $content .= sprintf('%s: %s<br />', $this->i18n('reports_right_emails'),
                               $this->result['total_emails'] - $this->result['erred_emails']);
            $content .= sprintf('%s: %s<br />', $this->i18n('reports_erred_emails'), $this->result['erred_emails']);
            $content .= sprintf('%s: %s<br /><br />', $this->i18n('reports_total_emails'), $this->result['total_emails']);
        }

        if (!empty($this->result['total_smses'])) {
            $content .= sprintf('%s: %s<br />', $this->i18n('reports_right_smses'),
                               $this->result['total_smses'] - $this->result['erred_smses']);
            $content .= sprintf('%s: %s<br />', $this->i18n('reports_erred_smses'), $this->result['erred_smses']);
            $content .= sprintf('%s: %s<br /><br />', $this->i18n('reports_total_smses'), $this->result['total_smses']);
        }

        if (!empty($this->result['error_sms_settings'])) {
            $content .= sprintf('<div style="color: #F50504; font-weight: bold;">%s</div><br />', $this->i18n('reports_error_sms_settings'));
        }

        $content .= implode("<br />", $this->result['additional_info']);

        return $content;
    }

    /**
     * Overwrite method.
     *
     * IMPORTANT!!!
     * This method checks if the background work is running
     * for this report (and current user!!!) and if YES, denies access to the report
     *
     * @param string $report - report type
     * @return bool - the result
     */
    public function checkReportAccess($report) {

        $request = &$this->registry['request'];

        //exclude the process in order not to kill itself
        if ($request->get('background_mode')) {
            return true;
        }

        if (preg_match('#WIN#i', PHP_OS)) {
            //get active processes in WINDOWS with their command line parameters
            exec('wmic PROCESS get Commandline', $output);
        } else {
            exec('ps -e -F|grep -i wget', $output);
        }

        $regex1 = '#reports=(' . implode('|', $this->backgroundActions) . ')#';
        //do it for this installation only (in case there are more than one installations on the server)
        //IMPORTANT: sometimes the installations are accessed from an address different from that in settings > crontab > base_host
        $location1 = sprintf('%s://%s%s?%s=reports',
                        (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                        $_SERVER["HTTP_HOST"], $_SERVER['PHP_SELF'],
                        Router::MODULE_PARAM);
        $regex2 = '#' . preg_quote($location1) . '#';
        $location2 = sprintf('%s/index.php?%s=reports',
                        $this->registry['config']->getParam('crontab', 'base_host'),
                        Router::MODULE_PARAM);
        $regex3 = '#' . preg_quote($location2) . '#';
        foreach ($output as $row) {
            if (preg_match($regex1, $row) && (preg_match($regex2, $row) || preg_match($regex3, $row)) &&
                preg_match('#original_user=' . $this->registry['currentUser']->get('id') . '#', $row) &&
                preg_match('#background_mode=\d+#', $row) && preg_match('#report_type=' . $report . '#', $row)) {

                $this->registry['messages']->setError($this->i18n('reports_background_work_running'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, '', array('report_type' => $this->current_report_name), $this->module);
            }
        }

        return true;
    }

    /**
     * Updates status of a SMS sent from report (content of a comment)
     * according to delivery receipt received from SMS service provider.
     */
    private function _deliveryReceipt() {
        $registry = &$this->registry;
        $request = &$this->registry['request'];

        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }

        $report = $report['name'];
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report . '/custom.report.query.php';
        $report_factory_name = preg_replace('#\s+#', '_', ucwords(preg_replace('#_#', ' ', $report)));

        $comment_id = sprintf('%d', $request->get('idd'));
        $status = $request->get('status');

        if ($comment_id && preg_match('/^(Y|N|P)$/i', $status)) {
            //load plugin i18n files
            $i18n_file = sprintf('%s%s%s%s%s%s',
                                PH_MODULES_DIR,
                                'reports/plugins/',
                                $report,
                                '/i18n/',
                                $registry['lang'],
                                '/reports.ini');
            $this->registry['translater']->loadFile($i18n_file);

            $status_text = '';
            $status = strtoupper($status);
            switch ($status) {
                case 'Y':
                    $status_text = $this->i18n('reports_status_received');
                    break;
                case 'N':
                    $status_text = $this->i18n('reports_status_not_received');
                    break;
                case 'P':
                    $status_text = $this->i18n('reports_status_pending');
                    break;
            }

            if ($status_text) {
                $status_text = sprintf('%s %s', $this->i18n('reports_status'), $status_text);

                // update status
                $upd = $report_factory_name::updateSMSStatus($this->registry, $comment_id, $status_text);
            }
        }

        return true;
    }
}

?>
