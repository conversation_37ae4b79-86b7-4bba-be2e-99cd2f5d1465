<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $included_customer_types = array();
            $included_customer_types = preg_split('#\s*,\s*#', INCLUDED_CUSTOMERS_TYPES);
            $included_customer_types = array_filter($included_customer_types);

            $filter = array (
                'custom_id'         => 'customer',
                'name'              => 'customer',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'autocomplete'          => array(
                    'type'          => 'customers',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions'   => '[<code>] <name> <lastname>',
                    'fill_options'  => array(
                        '$customer_autocomplete  => [<code>] <name> <lastname>',
                        '$customer_oldvalue      => [<code>] <name> <lastname>',
                        '$customer               => <id>'
                    ),
                    'buttons_hide' => 'search',
                    'clear' => 1
                ),
                'label'             => $this->i18n('reports_customer'),
                'help'              => $this->i18n('reports_customer'),
                'value'             => ''
            );
            if (!empty($included_customer_types)) {
                $filter['autocomplete']['filters'] = array('<type>' => implode(',', $included_customer_types));
            }
            $filters['customer'] = $filter;

            //DEFINE PROJECTS' FILTER
            $included_project_types = array();
            $included_project_types = preg_split('#\s*,\s*#', INCLUDED_PROJECTS_TYPES);
            $included_project_types = array_filter($included_project_types);
            $filter = array (
                'custom_id'             => 'project',
                'name'                  => 'project',
                'type'                  => 'autocompleter',
                'autocomplete_type'     => 'projects',
                'autocomplete_buttons'  => 'clear',
                'autocomplete'          => array(
                    'type'          => 'projects',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'projects', 'projects'),
                    'suggestions'   => '[<code>] <name>',
                    'fill_options'  => array(
                        '$project_autocomplete  => [<code>] <name>',
                        '$project_oldvalue      => [<code>] <name>',
                        '$project               => <id>'
                    ),
                    'execute_after' => 'loadDependantAC',
                    'buttons_hide'  => 'search',
                    'clear' => 1
                ),
                'width'                 => 222,
                'label'                 => $this->i18n('reports_project'),
                'help'                  => $this->i18n('reports_project_help'),
                'value'                 => ''
            );
            if (!empty($included_project_types)) {
                $filter['autocomplete']['filters'] = array('<type>' => implode(',', $included_project_types));
            }
            $filters['project'] = $filter;

            //DEFINE TASKS' FILTER
            $included_tasks_types = array();
            $included_tasks_types = preg_split('#\s*,\s*#', INCLUDED_TASKS_TYPES);
            $included_tasks_types = array_filter($included_tasks_types);

            $filter = array (
                'custom_id'             => 'task',
                'name'                  => 'task',
                'type'                  => 'autocompleter',
                'autocomplete_type'     => 'tasks',
                'autocomplete_buttons'  => 'clear',
                'autocomplete'          => array(
                    'type'          => 'tasks',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'tasks', 'tasks'),
                    'suggestions'   => '[<full_num>] <name>',
                    'fill_options'  => array(
                        '$task_autocomplete  => [<full_num>] <name>',
                        '$task_oldvalue      => [<full_num>] <name>',
                        '$task               => <id>'
                    ),
                    'execute_after' => 'loadDependantAC',
                    'buttons_hide'  => 'search',
                    'clear' => 1
                ),
                'width'                 => 222,
                'label'                 => $this->i18n('reports_task'),
                'help'                  => $this->i18n('reports_task_help'),
                'value'                 => ''
            );
            if (!empty($included_tasks_types)) {
                $filter['autocomplete']['filters'] = array('<type>' => implode(',', $included_tasks_types));
            }
            $filters['task'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            if (!empty($filters['project']['value'])) {
                $filters['task']['readonly'] = true;
                $filters['task']['disabled'] = true;
                $filters['task']['width'] = 200;
            }
            if (!empty($filters['task']['value'])) {
                $filters['project']['readonly'] = true;
                $filters['project']['disabled'] = true;
                $filters['project']['width'] = 200;
            }

            return $filters;
        }
    }
?>