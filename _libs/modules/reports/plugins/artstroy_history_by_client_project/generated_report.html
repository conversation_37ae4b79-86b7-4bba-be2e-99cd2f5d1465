{if $reports_results}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
    <tr class="reports_title_row hcenter">
      <td class="t_border">&nbsp;</td>
      <td class="t_border" style="vertical-align: middle;">{#reports_record_type#|escape}</td>
      <td class="t_border" style="vertical-align: middle;">{#reports_added_date#|escape}</td>
      <td class="t_border" style="vertical-align: middle;">{#reports_added_by#|escape}</td>
      <td class="t_border" style="vertical-align: middle;">{#reports_record_about#|escape}</td>
      <td class="t_border" style="vertical-align: middle;">{#reports_record_description#|escape}</td>
      <td class="t_border" style="vertical-align: middle;">{#reports_record_customer#|escape}</td>
      <td class="t_border" style="vertical-align: middle;">{#reports_record_status#|escape}</td>
      <td style="vertical-align: middle;">{#reports_record_related_with#|escape}</td>
    </tr>
    {foreach from=$reports_results item=result name=results key=rec_key}
      <tr class="{cycle values='t_odd,t_even'}{if $result.deleted_by} t_deleted{/if}">
        <td class="t_border hright" nowrap="nowrap" rowspan="{$result.doc_rows}">
          <input type="checkbox" name="included_data[]" id="row_{$rec_key}" value="{$result.encoded_data|escape}" checked="checked" />
        </td>
        <td class="t_border" nowrap="nowrap">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$result.module}&amp;{$result.module}=view&amp;view={$result.id}">
            {$result.record_name}{if $result.full_num} {$result.full_num|escape|default:"&nbsp;"}{/if}
          </a>
        </td>
        <td class="t_border" nowrap="nowrap">
          {$result.added|date_format:#date_mid#|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.added_by|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.description|escape|nl2br|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.customer_name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.status_name|escape|default:"&nbsp;"}{if $result.substatus_name} ({$result.substatus_name|escape|default:"&nbsp;"}){/if}
        </td>
        <td nowrap="nowrap">
          {foreach from=$result.relations item=rels name=rel_for}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$rels.module}&amp;{$rels.module}=view&amp;view={$rels.id}">
              {$rels.type_name}{if $rels.full_num} {$rels.full_num|escape|default:"&nbsp;"}{/if}
            </a>
            {if !$smarty.foreach.rel_for.last}
              <br />
            {/if}
          {/foreach}
        </td>
      </tr>
    {foreachelse}
      <tr>
        <td class="error" colspan="9">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="9"></td>
    </tr>
  </table>
  <br />
  {if $export_permission}
    <button type="submit" name="export_selected_records" class="button reports_export_button" onclick="exportSelectedRecords(this);">{#export_report#|escape}</button>
  {/if}
{else}
  <h1 style="color: red;">{#error_reports_no_results#|escape}</h1>
{/if}
