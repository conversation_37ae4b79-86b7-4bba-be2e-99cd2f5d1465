<?php
    Class Doors_Employees_Paid_Percents Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            define('DOCUMENT_TYPE_PROTOCOL_ID', 8);
            define('DOCUMENT_TYPE_PROTOCOL_DIRECTION', 2);
            define('DOCUMENT_TYPE_CONTRACT_ID', 6);
            define('BLINDATE_PRICE', 'help_label_bl__price');
            define('INTERIOR_PRICE', 'all_help_label__price');
            define('COUNT_BLINDATE_DOORS', 'count_bl_door');
            define('COUNT_INTERIOR_DOORS', 'count_int_door');
            define('SOLD_DOORS', 'count_doors_all');

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            if (!empty($filters['date_period']) && !empty($filters['employee']) && !empty($filters['offices'])) {
                //sql to take the ids of the needed additional vars for PROTOCOLS
                $sql_for_add_vars_protocols = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_PROTOCOL_ID . ' AND (fm.name="' . BLINDATE_PRICE . '" OR fm.name="' . INTERIOR_PRICE . '" OR fm.name="' . COUNT_BLINDATE_DOORS . '" OR fm.name="' . COUNT_INTERIOR_DOORS . '") ORDER BY fm.position';
                $var_ids_protocols = $registry['db']->GetAll($sql_for_add_vars_protocols);

                $blindate_price_id = '';
                $interior_price_id = '';
                $count_interior_doors_id = '';
                $count_blindate_doors_id = '';

                //assign the ids to vars
                foreach ($var_ids_protocols as $vars) {
                    if ($vars['name'] == BLINDATE_PRICE) {
                        $blindate_price_id = $vars['id'];
                    } else if ($vars['name'] == INTERIOR_PRICE) {
                        $interior_price_id = $vars['id'];
                    } else if ($vars['name'] == COUNT_BLINDATE_DOORS) {
                        $count_blindate_doors_id = $vars['id'];
                    } else if ($vars['name'] == COUNT_INTERIOR_DOORS) {
                        $count_interior_doors_id = $vars['id'];
                    }
                }

                //sql to take the ids of the needed additional vars for CONTRACTS
                $sql_for_add_vars_contracts = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_CONTRACT_ID . ' AND (fm.name="' . SOLD_DOORS . '") ORDER BY fm.position';
                $var_ids_contracts = $registry['db']->GetAll($sql_for_add_vars_contracts);

                $sold_doors_id = '';

                //assign the ids to vars
                foreach ($var_ids_contracts as $vars) {
                    if ($vars['name'] == SOLD_DOORS) {
                        $sold_doors_id = $vars['id'];
                    }
                }

                //sql to take the main information from Protocols
                $sql_for_protocols_1['select'] = 'SELECT DISTINCT d.id as id, d.full_num as full_num, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ' . "\n" .
                                                 'd_c.id as contract_id, DATE_FORMAT(d_c.added, "%Y-%m") as month_added_contract, ' . "\n" .
                                                 'DATE_FORMAT(d.added, "%Y-%m-%d") as date_added, ' . "\n" .
                                                 DOCUMENT_TYPE_PROTOCOL_DIRECTION . ' as direction, oi18n.name as office_name, ' . "\n" .
                                                 'd.customer as customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ' . "\n" .
                                                 'd.employee as employee, CONCAT(cei18n.name, " ", cei18n.lastname) as employee_name, ' . "\n" .
                                                 'd_cstm_count_blindate_doors.value as count_blindate_doors, d_cstm_blindate_price.value as blindate_price ' . "\n";

                $sql_for_protocols_1['from']   =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                                                  '  ON (d.id=dr.parent_id AND dr.origin="transformed" AND dr.link_to_model_name="Document")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d_c' . "\n" .
                                                  '  ON (d_c.id=dr.link_to AND d_c.type="' . DOCUMENT_TYPE_CONTRACT_ID . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                                  '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                                  '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS cei18n' . "\n" .
                                                  '  ON (d.employee=cei18n.parent_id AND cei18n.lang="' . $model_lang . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                                                  '  ON (d.office=oi18n.parent_id AND oi18n.lang="' . $model_lang . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_count_blindate_doors' . "\n" .
                                                  '  ON (d_cstm_count_blindate_doors.model_id=d.id AND d_cstm_count_blindate_doors.var_id="' . $count_blindate_doors_id . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_blindate_price' . "\n" .
                                                  '  ON (d_cstm_blindate_price.model_id=d.id AND d_cstm_blindate_price.var_id="' . $blindate_price_id . '" AND d_cstm_blindate_price.num=d_cstm_count_blindate_doors.num)' . "\n";

                // construct where
                $where = array();
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.type="' . DOCUMENT_TYPE_PROTOCOL_ID . '"';
                $where[] = 'd.active!=0';

                if (! empty($filters['employee'])) {
                    $where[] = 'd.employee="' . $filters['employee'] . '"';
                }
                if (! empty($filters['offices'])) {
                    $where[] = 'd.office="' . $filters['offices'] . '"';
                }
                if (! empty($filters['date_period'])) {
                    switch($filters['date_period']) {
                        case 'this_month':
                            $where[] = '(DATE_FORMAT(d.added, "%Y-%m")=DATE_FORMAT((CURDATE()), "%Y-%m"))';
                            break;
                        case 'last_month':
                            $where[] = '(DATE_FORMAT(d.added, "%Y-%m")=DATE_FORMAT((CURDATE() - INTERVAL 1 MONTH), "%Y-%m"))';
                            break;
                        case 'next_month':
                            $where[] = '(DATE_FORMAT(d.added, "%Y-%m")=DATE_FORMAT((CURDATE() + INTERVAL 1 MONTH), "%Y-%m"))';
                            break;
                        default:
                            break;
                    }
                }

                $sql_for_protocols_1['where'] = 'WHERE ' . implode(' AND ', $where);

                $query_for_protocols_1 = implode("\n", $sql_for_protocols_1);
                $records_for_protocols_1 = $registry['db']->GetAll($query_for_protocols_1);

                // form the array with protocols
                $protocols = array();
                $protocols_ids = array();
                foreach ($records_for_protocols_1 as $recs) {
                    if (!isset($protocols[$recs['id']])) {
                        $protocols[$recs['id']]['id'] = $recs['id'];
                        $protocols[$recs['id']]['full_num'] = $recs['full_num'];
                        $protocols[$recs['id']]['direction'] = $recs['direction'];
                        $protocols[$recs['id']]['date_added'] = $recs['date_added'];
                        $protocols[$recs['id']]['office'] = $recs['office_name'];
                        $protocols[$recs['id']]['customer'] = $recs['customer_name'];
                        $protocols[$recs['id']]['employee'] = $recs['employee'];
                        $protocols[$recs['id']]['employee_name'] = $recs['employee_name'];
                        $protocols[$recs['id']]['contract_id'] = $recs['contract_id'];
                        $protocols[$recs['id']]['month_added_contract'] = $recs['month_added_contract'];
                        $protocols[$recs['id']]['total_doors'] = 0;
                        $protocols[$recs['id']]['total_sum'] = 0;
                        $protocols_ids[] = $recs['id'];
                    }
                    if ($recs['count_blindate_doors'] > 0) {
                        $protocols[$recs['id']]['total_doors'] = $protocols[$recs['id']]['total_doors'] + $recs['count_blindate_doors'];
                        $protocols[$recs['id']]['total_sum'] = sprintf("%01.2f", ($protocols[$recs['id']]['total_sum'] + $recs['blindate_price']));
                    }
                }

                //sql to take the information from Protocols for interior doors
                $sql_for_protocols_2['select'] = 'SELECT d.id as id, ' . "\n" .
                                                 'd_cstm_count_interior_doors.value as count_interior_doors, d_cstm_interior_price.value as interior_price' . "\n";

                $sql_for_protocols_2['from']   =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_count_interior_doors' . "\n" .
                                                  '  ON (d_cstm_count_interior_doors.model_id=d.id AND d_cstm_count_interior_doors.var_id="' . $count_interior_doors_id . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_interior_price' . "\n" .
                                                  '  ON (d_cstm_interior_price.model_id=d.id AND d_cstm_interior_price.var_id="' . $interior_price_id . '" AND d_cstm_interior_price.num=d_cstm_count_interior_doors.num)' . "\n";

                // construct where
                $where = array();
                if (! empty($protocols_ids)) {
                    $where[] = 'd.id IN (' . implode(', ', $protocols_ids) . ')';
                } else {
                    $where[] = '1=0';
                }

                $sql_for_protocols_2['where'] = 'WHERE ' . implode(' AND ', $where);
                $query_for_protocols_2 = implode("\n", $sql_for_protocols_2);
                $records_for_protocols_2 = $registry['db']->GetAll($query_for_protocols_2);

                // adds the data to the protocols array
                foreach ($records_for_protocols_2 as $recs) {
                    if (isset($protocols[$recs['id']]) && $recs['count_interior_doors'] > 0) {
                        $protocols[$recs['id']]['total_doors'] = $protocols[$recs['id']]['total_doors'] + $recs['count_interior_doors'];
                        $protocols[$recs['id']]['total_sum'] = sprintf("%01.2f", ($protocols[$recs['id']]['total_sum'] + $recs['interior_price']));
                    }
                }


                // CONTRACTS info
                $contract_ids_extended_info = array();
                foreach ($protocols as $key => $protoc) {
                    if (! array_key_exists($protoc['month_added_contract'], $contract_ids_extended_info)) {
                        //sql to take the main information from Contracts
                        $sql_for_contracts['select'] = 'SELECT d.id as id, d_cstm_sold_doors.value as sold_doors' . "\n";

                        $sql_for_contracts['from']   =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sold_doors' . "\n" .
                                                        '  ON (d_cstm_sold_doors.model_id=d.id AND d_cstm_sold_doors.var_id="' . $sold_doors_id . '")' . "\n";

                        // construct where
                        $where = array();
                        $where[] = 'd.deleted_by=0';
                        $where[] = 'd.type="' . DOCUMENT_TYPE_CONTRACT_ID . '"';
                        $where[] = 'd.employee="' . $filters['employee'] . '"';
                        $where[] = 'd.active!=0';
                        $where[] = '(DATE_FORMAT(d.added, "%Y-%m")="' . $protoc['month_added_contract'] . '")';

                        $sql_for_contracts['where'] = 'WHERE ' . implode(' AND ', $where);
                        $query_for_contracts = implode("\n", $sql_for_contracts);
                        $records_for_contracts = $registry['db']->GetAll($query_for_contracts);

                        $sold_doors = 0;
                        foreach ($records_for_contracts as $recs_contract) {
                            $sold_doors += $recs_contract['sold_doors'];
                        }

                        if ($sold_doors <= 6) {
                            $percents = 0;
                        } else if ($sold_doors <= 20) {
                            $percents = 2;
                        } else {
                            $percents = 3;
                        }

                        // saves the info for percents
                        $contract_ids_extended_info[$protoc['month_added_contract']] = $percents;
                        $protocols[$key]['paid_percents'] = $percents;
                    } else {
                        $protocols[$key]['paid_percents'] = $contract_ids_extended_info[$protoc['month_added_contract']];
                    }
                }

                $total_for_employee = 0;

                // calculate final results
                foreach ($protocols as $k => $protocol) {
                    $total_without_tax = sprintf("%01.2f", (($protocol['total_sum']*5)/6));
                    $paid_to_employee = sprintf("%01.2f", (($total_without_tax*$protocol['paid_percents'])/100));
                    $protocols[$k]['total_without_tax'] = $total_without_tax;
                    $protocols[$k]['paid_to_employee'] = $paid_to_employee;
                    $total_for_employee = sprintf("%01.2f", ($total_for_employee + $paid_to_employee));
                }

                // total to pay to employee
                $protocols['additional_options']['total_for_employee'] = $total_for_employee;
            } else {
                $protocols = array();
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($protocols, 0);
            } else {
                $results = $protocols;
            }

            return $results;
        }
    }
?>