<?php
    class Custom_Report_Filters extends Report_Filters {
        private static $registry = array();

        public $custom_filters_template = 'custom_filters.html';

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            // DEFINE LANGUAGE FILTER
            $filter = array (
                'custom_id'       => 'language',
                'name'            => 'language',
                'type'            => 'autocompleter',
                'required'        => 1,
                'width'           => 222,
                'label'           => $this->i18n('reports_language'),
                'help'            => $this->i18n('reports_language'),
                'autocomplete'    => array (
                    'search'       => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'nomenclatures',
                    'clear'        => 1,
                    'suggestions'  => '<name>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'language',
                    'fill_options' => array('$language => <id>',
                                            '$language_autocomplete => <name>'),
                    'filters'      => array('<type>' => (string)LANGUAGE_NOMENCLATURE_ID),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
                )
            );
            $filters['language'] = $filter;

            // PREPARE LANGUAGE CLASS FILTERS
            //prepare filters
            $filter = array (
                'custom_id' => 'language_class',
                'name'      => 'language_class',
                'type'      => 'dropdown',
                'width'     => 50,
                'label'     => $this->i18n('reports_language_class'),
                'help'      => $this->i18n('reports_language_class'),
                'options'   => array(
                    array(
                        'label'        => $this->i18n('reports_language_class_a'),
                        'option_value' => 'A'
                    ),
                    array(
                        'label'        => $this->i18n('reports_language_class_b'),
                        'option_value' => 'B'
                    ),
                    array(
                        'label'        => $this->i18n('reports_language_class_c'),
                        'option_value' => 'C'
                    ),
                )
            );
            $filters['language_class'] = $filter;

            // PREPARE LANGUAGE COMPETЕNCE FILTERS
            //prepare options
            $sql_language_competence = 'SELECT label, option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE parent_name="' . LANGUAGE_COMPETENCE_VAR . '" AND lang="' . $registry['lang'] . '" ORDER BY position ASC';
            $options_language_competence = $registry['db']->getAll($sql_language_competence);

            //prepare filters
            $filter = array (
                'custom_id' => 'language_competence',
                'name'      => 'language_competence',
                'type'      => 'dropdown',
                'width'     => 200,
                'label'     => $this->i18n('reports_language_competence'),
                'help'      => $this->i18n('reports_language_competence'),
                'options'   => $options_language_competence
            );
            $filters['language_competence'] = $filter;

            // DEFINE TRANSLATOR FILTER
            $filter = array(
                'custom_id'       => 'translator',
                'name'            => 'translator',
                'type'            => 'autocompleter',
                'width'           => 222,
                'label'           => $this->i18n('reports_translator'),
                'help'            => $this->i18n('reports_translator'),
                'autocomplete'    => array (
                    'search'       => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'translator',
                    'fill_options' => array('$translator => <id>',
                                            '$translator_autocomplete => <name> <lastname>'),
                    'filters'      => array('<type>' => (string)PH_CUSTOMER_EMPLOYEE),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['translator'] = $filter;

            return $filters;
        }
    }
?>