<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array ();

            //set interface lang filter
            $lang = $registry['lang'];

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'disallow_date_after' => true,
                'required'  => 1,
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'disallow_date_after' => true,
                'required'  => 1,
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE EQUIPMENT_GROUP FILTER
            if (!defined('FIELD_EQUIPMENT_GROUP')) {
                define('FIELD_EQUIPMENT_GROUP', 'equipment_group');
            }
            $query = 'SELECT label, option_value' . "\n" .
                     '  FROM ' . DB_TABLE_FIELDS_OPTIONS . "\n" .
                     '  WHERE parent_name = "' . FIELD_EQUIPMENT_GROUP . '" AND active_option = 1 AND lang = "' . $lang . '"' . "\n" .
                     '  ORDER BY position ASC';
            $equipment_group_options = $registry['db']->getAll($query);

            $filter = array (
                'custom_id' => 'equipment_group',
                'name'      => 'equipment_group',
                'type'      => 'checkbox_group',
                'required'  => 0,
                'label'     => $this->i18n('reports_equipment_group'),
                'help'      => $this->i18n('reports_equipment_group'),
                'options'   => $equipment_group_options
            );
            $filters['equipment_group'] = $filter;

            return $filters;
        }
    }

?>
