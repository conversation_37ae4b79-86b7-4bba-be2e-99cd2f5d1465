<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
    {if !empty($reports_results)}
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="margin-bottom: 20px;">
        <tr class="reports_title_row hcenter">
          <td class="t_border" style="vertical-align: middle;"><div style="width: 474px;">{#reports_deliverer#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_obligations#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_not_distributed#|escape}</div></td>
          <td style="vertical-align: middle;"><div style="width: 100px;">{#reports_ending_balance#|escape}</div></td>
        </tr>
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border" style="cursor: pointer;" onclick="ajaxLoadExtendedObligationsInfo('{$result.customer_encoded_data|escape:'javascript'}', 'obligations_title');" width="484">
              {$result.name|escape|default:"&nbsp;"}
            </td>
            <td class="hright t_border" width="102">
              {if $result.obligations}
                {$result.obligations|string_format:"%.2f"|default:"0.00"}
              {else}
                -
              {/if}
            </td>
            <td class="hright t_border" width="102">
              {if $result.not_distributed_payments}
                {$result.not_distributed_payments|string_format:"%.2f"|default:"0.00"}
              {else}
                -
              {/if}
            </td>
            <td class="hright" width="102">
              {if $result.balance}
                {$result.balance|string_format:"%.2f"|default:"0.00"}
              {else}
                -
              {/if}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="4">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr class="row_blue">
          <td class="t_border"><strong>{#reports_total_obligations#}</strong></td>
          <td class="hright t_border">
            <strong>{if $reports_additional_options.total_obligations}{$reports_additional_options.total_obligations|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong>
          </td>
          <td class="hright t_border">
            <strong>{if $reports_additional_options.total_not_distributed}{$reports_additional_options.total_not_distributed|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong>
          </td>
          <td class="hright">
            <strong>{if $reports_additional_options.total_balance}{$reports_additional_options.total_balance|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong>
          </td>
        </tr>
        <tr>
          <td class="t_footer" colspan="4"></td>
        </tr>
      </table>
      <div id="deliverer_extended_information"></div>
    {else}
      <h1>{#error_reports_no_results_to_show#}</h1>
    {/if}
    </td>
  </tr>
</table>
