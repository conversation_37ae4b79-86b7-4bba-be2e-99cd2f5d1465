<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="1">
      <tr>
        <td><strong>{#reports_last_tenant_trademark#|escape}</strong></td>
        <td><strong>{#reports_unit#|escape}</strong></td>
        <td><strong>{#reports_type_unit#|escape}</strong></td>
        <td><strong>{#reports_part_gla#|escape}</strong></td>
        <td><strong>{#reports_quadrature_real#|escape}</strong></td>
        <td><strong>{#reports_floor_unit#|escape}</strong></td>
        <td><strong>{#reports_situated_unit#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results key=uk name=ui item=unit}
      <tr>
        <td nowrap="nowrap">{$unit.customer_name|escape}{if $unit.trademark} / {$unit.trademark_name|escape}{/if}</td>
        <td nowrap="nowrap" style='mso-number-format:\@'>{$unit.unit_code|escape}</td>
        <td>{$unit.type_unit|escape}</td>
        <td>{$unit.part_gla|escape}</td>
        <td align="right" style='mso-number-format:"0\.00"'>{$unit.quadrature_real|escape}</td>
        <td align="right" style='mso-number-format:"0"'>{$unit.floor_unit|escape}</td>
        <td nowrap="nowrap">{$unit.situated_unit|escape}</td>
      </tr>
      {foreachelse}
      <tr>
        <td colspan="7">{#no_items_found#|escape}</td>
      </tr>
      {/foreach}
      <tr>
        <td colspan="7"></td>
      </tr>
      <tr>
        <td colspan="7"><strong>{#reports_total_available_units#|escape} {if is_array($reports_results)}{$reports_results|@count}{else}0{/if}</strong></td>
      </tr>
    </table>
  </body>
</html>