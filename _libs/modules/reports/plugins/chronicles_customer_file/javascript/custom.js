var custom_report_i18n = new Object;
custom_report_i18n['bg'] = new Object;
custom_report_i18n['en'] = new Object;
custom_report_i18n['bg']['report_no_translation'] = 'Няма превод за посочения запис!';
custom_report_i18n['en']['report_no_translation'] = 'No translation for the selected record!';

/*
 * Function to check if the selected record is translated
 */
function checkTranslation(source_panel, language_to_translate_to, model_id, active_record) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // the report requires only one customer to be selected
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_check_translation&report_type=' + $('report_type').value;
    url += '&record_lang=' + language_to_translate_to;
    url += '&model_id=' + model_id;
    url += '&source_panel=' + source_panel;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            if (result.translated) {
                completeRecordData(result.translated_data, source_panel, language_to_translate_to, model_id, active_record);
            } else {
                if (active_record) {
                    // activate function to trigger a lightbox with needed data
                    activateCorrectionLightbox(source_panel, language_to_translate_to, model_id, 'translate');
                } else {
                    alert(custom_report_i18n[env.current_lang]['report_no_translation']);
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to complete the record data
 */
function completeRecordData(data, source_panel, record_language, model_id, valid) {
    if (source_panel == 'main_panel') {
        $('main_table_title_cell').innerHTML = data.name_label;
        var needed_images = $$('img[id^="main_panel_"]');

        for (var i = 0; i < needed_images.length; i++) {
            // find img lang
            img_lang = needed_images[i].id.replace(/main_panel_/, '');
            if (img_lang == record_language) {
                removeClass(needed_images[i], 'dimmed');
                addClass(needed_images[i], 'selected');
                needed_images[i].style.cursor = 'default';
                needed_images[i].setAttribute('onClick', 'return false;');
            } else {
                removeClass(needed_images[i], 'selected');
                addClass(needed_images[i], 'dimmed');
                needed_images[i].style.cursor = 'pointer';
                needed_images[i].setAttribute('onClick', 'checkTranslation(\'' + source_panel + '\', \'' + img_lang + '\', \'' + model_id + '\')');
            }
        }
    } else {
        // form the id of the new row
        var base_element_id = source_panel + '_';
        var new_row_index = 0;
        var visible_history = false;

        if ($(source_panel + '_hide_history') && $(source_panel + '_hide_history').style.display!='none'){
            visible_history = true;
        }

        if (!valid) {
            base_element_id += 'history_';
        }
        var existing_active_element_id = source_panel + '_' + model_id;
        var existing_history_element_id = source_panel + '_history_' + model_id;

        // find if similar container exists
        var record_container = $(existing_active_element_id);
        var history_record_container = $(existing_history_element_id);

        if (record_container != null) {
            if (valid) {
                new_row_index = record_container.parentNode.parentNode.rowIndex;
            }

            // remove existing row. It will be replaced later
            record_container.parentNode.parentNode.parentNode.removeChild(record_container.parentNode.parentNode);
        } else if (history_record_container != null) {
            new_row_index = history_record_container.parentNode.parentNode.rowIndex;

            // remove existing row. It will be replaced later
            history_record_container.parentNode.parentNode.parentNode.removeChild(history_record_container.parentNode.parentNode);
        }

        if (!new_row_index) {
            var records_containers = $$('[id^="' + base_element_id + '"]');
            var regex_id = new RegExp(base_element_id + '([0-9]+)', 'gi');
            for (var t = 0; t < records_containers.length; t++) {
                if ((records_containers[t].tagName == 'TABLE' || records_containers[t].tagName == 'TR') && (records_containers[t].id == (base_element_id + 'no_active') || records_containers[t].id.match(regex_id))) {
                    if (records_containers[t].tagName == 'TABLE') {
                        current_row_idx = records_containers[t].parentNode.parentNode.rowIndex;
                    } else {
                        current_row_idx = records_containers[t].rowIndex;
                    }
                    if (current_row_idx > new_row_index) {
                        new_row_index = current_row_idx;
                    }
                }
            }
            new_row_index++;
        }

        // find if the history is activated
        if (valid || (!valid && visible_history)) {
            main_panel_container = $(source_panel + '_container');
            var new_row = main_panel_container.insertRow(new_row_index);
            var new_cell = new_row.insertCell(0);
            new_cell.innerHTML = data;

            // resize the lang box of the new added element
            existing_lang_boxes = $$('#' + source_panel + '_container div[id^="lang_box_"]');
            for (var k = 0; k < existing_lang_boxes.length; k++) {
                resizeSingleLangBox(existing_lang_boxes[k]);
            }
        }

        var active_rows = 0;
        var history_rows = 0;

        var records_containers = $$('[id^="' + source_panel + '_"]');
        var active_record_panes = new RegExp(source_panel + '_([0-9]+)', 'gi');
        var history_record_panes = new RegExp(source_panel + '_history_([0-9]+)', 'gi');
        for (var t = 0; t < records_containers.length; t++) {
            if (records_containers[t].tagName == 'TABLE' || records_containers[t].tagName == 'TR') {
                if (records_containers[t].id == (source_panel + '_history_no_active') || records_containers[t].id.match(history_record_panes)) {
                    history_rows++;
                } else if (records_containers[t].id == (source_panel + '_no_active') || records_containers[t].id.match(active_record_panes)) {
                    active_rows++;
                }
            }
        }

        if (visible_history) {
            if (history_rows > 1) {
                $(source_panel + '_history_no_active').style.display = 'none';
            } else {
                $(source_panel + '_history_no_active').style.display = 'table-row';
            }
        }

        if (active_rows > 1) {
            $(source_panel + '_no_active').style.display = 'none';
        } else {
            $(source_panel + '_no_active').style.display = 'table-row';
        }
    }
}

/*
 * Function to activate the lightbox for changing the model's data
 */
function activateCorrectionLightbox(source_panel, record_lang, model_id, action) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // the report requires only one customer to be selected
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_activate_correction_panel&report_type=' + $('report_type').value;
    url += '&record_lang=' + record_lang;
    url += '&model_id=' + model_id;
    url += '&source_panel=' + source_panel;
    url += '&model_action=' + action;
    url += '&related_client=' + $('customer').value;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');

            // Build the lightbox
            lb = new lightbox({content: result['template'], title: result['label'], width: result['width'] + 'px', height: ''});
            lb.activate();

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to edit selected model
 */
function submitEdit(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var form = element.form;

    var required_fields = new Array();
    var model_id = $('model_id').value;
    var model_lang = $('lang_model').value;
    var source_panel = $('source_panel').value;

    if (source_panel == 'main_panel') {
        required_fields[required_fields.length] = 'client_name';
    } else if (source_panel == 'owner_panel') {
        required_fields[required_fields.length] = 'owner_name';
        required_fields[required_fields.length] = 'ownership_type';
        required_fields[required_fields.length] = 'owner_validity_from';
    } else if (source_panel == 'law_representative_panel' || source_panel == 'attorney_representative_panel') {
        required_fields[required_fields.length] = 'representative_name';
        required_fields[required_fields.length] = 'representative_by';
        required_fields[required_fields.length] = 'representative_validity_from';
    } else if (source_panel == 'operation_contact_person_panel' || source_panel == 'personal_data_contact_person_panel') {
        required_fields[required_fields.length] = 'contact_person_name';
        required_fields[required_fields.length] = 'contact_person_type';
        required_fields[required_fields.length] = 'contact_person_validity_from';
    } else if (source_panel == 'client_deliverer_panel') {
        required_fields[required_fields.length] = 'client_deliverer_name';
        required_fields[required_fields.length] = 'client_deliverer_type';
        required_fields[required_fields.length] = 'client_deliverer_validity_from';
    } else if (source_panel == 'document_panel') {
        required_fields[required_fields.length] = 'document_law_information';
    } else if (source_panel == 'revision_panel') {
        required_fields[required_fields.length] = 'revision_type';
    } else if (source_panel == 'team_panel') {
        required_fields[required_fields.length] = 'team_name';
    } else if (source_panel == 'proxy_panel') {
        required_fields[required_fields.length] = 'proxy_date';
    }

    var uncompleted_fields = 0;
    for (var i = 0; i < required_fields.length; i++) {
        if ($(required_fields[i]) && !$(required_fields[i]).value) {
            uncompleted_fields++;
            break;
        }
    }

    if (!uncompleted_fields) {
        var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_edit_selected_model&report_type=' + $('report_type').value;

        // serialize form
        var get_params = Form.serialize(form);

        var opt = {
            asynchronous: false,
            method: 'post',
            parameters: get_params,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var operation_result = eval('(' + t.responseText + ')');
                if (operation_result.result) {
                    // model has no id before it is added
                    if (operation_result.model_id) {
                        model_id = operation_result.model_id;
                        if ($('model_id') != null) {
                            $('model_id').value = model_id;
                        }
                    }
                    if (operation_result.file_upload) {
                        submitFileData(element, operation_result.data, source_panel, model_lang, model_id, operation_result.valid);
                    } else {
                        completeRecordData(operation_result.data, source_panel, model_lang, model_id, operation_result.valid);
                        lb.deactivate();
                    }
                } else {
                    var alert_messages = '';
                    for (var j = 0; j < operation_result['errors'].length; j++) {
                        alert_messages += operation_result['errors'][j] + '\n';
                    }
                    alert(alert_messages);
                }

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        new Ajax.Request(url, opt);
    } else {
        Effect.Fade('loading');
        alert(i18n['messages']['alert_empty_field']);
    }
}

/*
 * Function to show/hide history of certain panel
 */
function toggleHistory(element, active_history, source_panel) {
    var active_icon = '';
    var new_active_mode = '';

    if (active_history) {
        new_active_mode = 'hide';
    } else {
        new_active_mode = 'show';
    }
    active_icon = $(source_panel + '_' + new_active_mode + '_history');
    element.style.display = 'none';
    active_icon.style.display = 'inline';

    if (!active_history) {
        var needed_elements = $$('[id^="' + source_panel + '_history_"]');
        for (var i = 0; i < needed_elements.length; i++) {
            if (needed_elements[i].tagName == 'TABLE') {
                parent_tr = needed_elements[i].parentNode.parentNode;
                parent_tr.parentNode.removeChild(parent_tr);
            } else if (needed_elements[i].tagName == 'TR') {
                needed_elements[i].style.display="none";
            }
        }
    } else {
        Effect.Center('loading');
        Effect.Appear('loading');

        var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_get_panel_history&report_type=' + $('report_type').value;
        url += '&source_panel=' + source_panel;
        url += '&related_client=' + $('customer').value;

        var opt = {
            asynchronous: false,
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var result = eval('(' + t.responseText + ')');

                if (result.length) {
                    var empty_history_row = $(source_panel + '_history_no_active');
                    empty_history_row.style.display = 'none';
                    var first_new_index_row = empty_history_row.rowIndex + 1;
                    var main_panel_container = $(source_panel + '_container');
                    for (var j=0; j<result.length; j++) {
                        var new_row = main_panel_container.insertRow(first_new_index_row);
                        var new_cell = new_row.insertCell(0);
                        new_cell.innerHTML = result[j];
                        first_new_index_row++;
                    }

                    // resize the lang boxes in the history panel
                    var existing_lang_boxes = $$('#' + main_panel_container.id + ' div[id^="lang_box_"]');
                    for (var k = 0; k < existing_lang_boxes.length; k++) {
                        resizeSingleLangBox(existing_lang_boxes[k]);
                    }
                } else {
                    $(source_panel + '_history_no_active').style.display = 'table-row';
                }

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        new Ajax.Request(url, opt);
    }
}

/*
 * Function to resize lang boxes' containers
 */
function  resizeMultipleLangBoxes() {
    var available_lang_boxes = $$('div[id^="lang_box_"]');
    for (var t = 0; t < available_lang_boxes.length; t++) {
        resizeSingleLangBox(available_lang_boxes[t]);
    }
}

/*
 * Function to resize lang box container
 */
function resizeSingleLangBox(lang_box) {
    var container = lang_box.parentNode;
    if (!container.style.height) {
        var container_box = container.parentNode;
        container.style.height = container_box.clientHeight + 'px';
    }
}

/*
 * Function to change the options for related dropdown
 */
function changeRelatedOptions(element, related_dropdown, lang) {
    related_dropdown = $(related_dropdown);
    Effect.Center('loading');
    Effect.Appear('loading');

    if (element.value) {
        var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_change_related_dropdown_options&report_type=' + $('report_type').value + '&selected_option=' + element.value + '&lang_model=' + lang;

        var opt = {
            asynchronous: false,
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var result = eval('(' + t.responseText + ')');

                var index_option = 0;
                related_dropdown.options.length = 0;
                if (!result.length) {
                    related_dropdown.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                    addClass(related_dropdown, 'missing_records');
                } else {
                    removeClass(related_dropdown, 'missing_records');
                    related_dropdown.options[0] = new Option(i18n['labels']['please_select'], '', false, true);
                    addClass(related_dropdown.options[0], 'undefined');
                    index_option++;

                    for (var j = 0; j < result.length; j++) {
                        related_dropdown.options[index_option] = new Option(result[j]['label'], result[j]['option_value'], false, false);
                        index_option++;
                    }
                }
                toggleUndefined(related_dropdown);

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        new Ajax.Request(url, opt);
    } else {
        related_dropdown.options.length = 0;
        related_dropdown.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
        addClass(related_dropdown, 'missing_records');

        Effect.Fade('loading');
    }
}

function submitFileData(element, data, source_panel, model_lang, model_id, valid) {
    var iframe_target = $('upload_target');
    var form = element.form;
    form.id = form.id + '_files_form';

    iframe_target.onload = function () {processFileUploadResult(this, model_id, model_lang, valid);};

    var current_form_target = form.target;
    var current_form_action = form.action;
    form.action = env.base_url + '?' + env.module_param + '=reports&reports=ajax_submit_chronicle_files&report_type=' + $('report_type').value + '&model_id=' + model_id + '&lang_model=' + model_lang + '&source_panel=' + source_panel;
    form.target = iframe_target.id;
    form.submit();
    form.target = current_form_target;
    form.action = current_form_action;
}

function processFileUploadResult(element, model_id, model_lang, valid) {
    var scripts = element.contentDocument.getElementsByTagName('SCRIPT');
    eval(scripts[0].text);

    completeRecordData(operation_result.data, 'document_panel', model_lang, model_id, valid);
    element.onload = function () {return false;};
    element.contentDocument.innerHTML = '';

    lb.deactivate();
    Effect.Fade('loading');
}

/*
 * Function to delete a selected team
 */
function deleteTeam(team_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // the report requires only one customer to be selected
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_delete_team&report_type=' + $('report_type').value;
    url += '&team_id=' + team_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var operation_result = eval('(' + t.responseText + ')');
            if (operation_result.result) {
                var team_panel_element = $('team_panel_' + team_id).parentNode.parentNode;
                team_panel_element.parentNode.removeChild(team_panel_element);

                // check for active team panels
                var available_team_panels = $$('table[id^="team_panel_"]');
                var count_panels = 0;
                for (var t = 0; t < available_team_panels.length; t++) {
                    if (available_team_panels[t].id.match(/^team_panel_([0-9]*)$/)) {
                        count_panels++;
                    }
                }

                if (!count_panels) {
                    $('team_panel_no_active').style.display = 'table-row';
                }
            } else {
                var alert_messages = '';
                for (var j = 0; j < operation_result['errors'].length; j++) {
                    alert_messages += operation_result['errors'][j] + '\n';
                }
                alert(alert_messages);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}
