<script type="text/javascript" defer="defer" src="{$reports_additional_options.script_url}?{$system_options.build}"></script>
{assign var='dates_data' value=$reports_results.dates_data}
{assign var='report_settings' value=$reports_results.settings}
{assign var='meetings_rows_colors' value=$reports_results.meetings_rows_colors}
{if !empty($dates_data)}
  {counter name='week_day_num' assign='week_day_num' start=1 print=false}
  <table class="reports_btmfv_days_container">
    <tr>
      <td>
        <ul class="reports_btmfv_week">
        {foreach from=$dates_data item=dat_data name=dada key=current_day_idx}
          <li {if $week_day_num ne 1}style=" padding-left: 10px;"{/if}>
            <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
              <tr class="reports_title_row hcenter">
                <td style="vertical-align: middle; width: 200px; height: 30px;" colspan="2"><div>{$dat_data.date_formatted|escape} ({$dat_data.day_name|escape}){if $dat_data.ray_name}<br /><strong>{$dat_data.ray_name|escape}</strong>{/if}{if in_array($dat_data.date_iso, $reports_results.nonworking_days)}<br /><b>{#reports_nonworking_day#}</b>{/if}</div></td>
              </tr>
              {capture assign='counter_name'}counter_{$current_day_idx}{/capture}
              {foreach from=$dat_data.meetings item=meeting name=meet}
                {if $meeting.record_type eq 'document'}
                  {capture assign='meeting_row_color'}{strip}
                    {if $meeting.visit_status eq $report_settings.daily_plan_meeting_marked}
                      {$meetings_rows_colors.daily_plan_meeting_marked}
                    {elseif $meeting.visit_status eq $report_settings.daily_plan_meeting_failed}
                      {$meetings_rows_colors.daily_plan_meeting_failed}
                    {/if}
                  {/strip}{/capture}
                  {capture assign='row_text'}{strip}
                    {$meeting.customer_name}
                    {if $meeting.address}
                      ({$meeting.address|escape})
                    {/if}
                  {/strip}{/capture}
                {else}
                  {capture assign='color_key'}event_type_{$meeting.event_type_keyword}{/capture}
                  {assign var='meeting_row_color' value=$meetings_rows_colors[$color_key]}
                  {capture assign='row_text'}{strip}
                    {$meeting.event_type_name|escape}{if $meeting.customer_name} ({$meeting.customer_name}){/if}
                  {/strip}{/capture}
                {/if}
                {capture assign='meeting_row_uniqid'}{uniqid}{/capture}
                <tr class="{strip}
                  {if $dat_data.current_date}
                    {cycle name=$counter_name values='t_current_dat_odd,t_current_dat_even'}
                  {else}
                    {cycle name=$counter_name values='t_odd1 t_odd2,t_even1 t_even2'}
                  {/if}{/strip}">
                  <td style="height: 19px; width: 20px!important; text-align: center;{if $meeting_row_color} background-color: {$meeting_row_color};{/if}" class="t_border">
                    {if $meeting.visit_status eq $report_settings.daily_plan_meeting_marked}
                      {if $meeting.visit_id}
                        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view={$meeting.visit_id}">
                          <img title="{#reports_open_daily_plan#}" src="{$templatesUrlPlugin}{$report_type}/templates/icons/visit_made.png" />
                        </a>
                      {/if}
                    {elseif $meeting.visit_status eq $report_settings.daily_plan_meeting_failed}
                      <img title="{#reports_show_failed_daily_plan_comment#}" style="cursor: pointer;" onclick="toggleMeetingRow('{$meeting_row_uniqid}')" src="{$templatesUrlPlugin}{$report_type}/templates/icons/visit_failed.png" />
                    {/if}
                  </td>
                  {assign var='row_text' value=$row_text|regex_replace:"/[\r\t\n]/":" "}
                  <td style="{if $meeting_row_color}background-color: {$meeting_row_color};{/if} width: 160px;" title="{$row_text|escape}">
                    <div style="width: 160px; height: 17px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; margin-top: 2px;">
                      {$row_text|escape}
                    </div>
                  </td>
                </tr>
                {if $meeting.visit_status ne $report_settings.daily_plan_meeting_marked}
                  <tr style="display: none;" id="{$meeting_row_uniqid}">
                    <td colspan="2">{#reports_meeting_failed_description#} {$meeting.description}</td>
                  </tr>
                {/if}
              {foreachelse}
                <tr class="{if $dat_data.current_date}{cycle name=$counter_name values='t_current_dat_odd,t_current_dat_even'}{else}{cycle name=$counter_name values='t_odd1 t_odd2,t_even1 t_even2'}{/if}">
                  <td colspan="2" style="height: 19px;">
                    <span class="error">{#no_items_found#|escape}</span>
                  </td>
                </tr>
              {/foreach}
            </table>
          </li>
          {if !$smarty.foreach.dada.last}
            {if $week_day_num eq 7}
              </ul>
              </td>
              </tr>
              <tr>
              <td>
              <ul class="reports_btmfv_week">
              {counter name='week_day_num' assign='week_day_num' start=1 print=false}
            {else}
              {counter name='week_day_num' assign='week_day_num' print=false}
            {/if}
          {/if}
        {/foreach}
        </ul>
      </td>
    </tr>
  </table>
  <fieldset class="bt_mfv_legend_container">
    <legend>{#reports_legend#}</legend>
    <table>
      <tr>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.daily_plan_meeting_marked}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_daily_plan_meeting_marked#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.daily_plan_meeting_failed}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_daily_plan_meeting_failed#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_leave}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_leave#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_medical}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_medical#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_lunch}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_lunch#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_conference}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_conference#}</td>
      </tr>
    </table>
  </fieldset>
{else}
  <span class="error">{#no_items_found#|escape}</span>
{/if}











{*
<script type="text/javascript" defer="defer" src="{$reports_additional_options.script_url}?{$system_options.build}"></script>
{assign var='dates_data' value=$reports_results.dates_data}
{assign var='report_settings' value=$reports_results.settings}
{assign var='meetings_rows_colors' value=$reports_results.meetings_rows_colors}
{if !empty($dates_data)}
  <table border="0" cellpadding="0" cellspacing="0">
    <tr>
      {foreach from=$dates_data item=dat_data name=dada key=current_day_idx}
        <td style="vertical-align: top; width: 280px;{if !$smarty.foreach.dada.first} padding-left: 10px;{/if}">
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="width: 280px;">
            <tr class="reports_title_row hcenter">
              <td style="vertical-align: middle; width: 280px; height: 30px;" colspan="2"><div>{$dat_data.date_formatted|escape} ({$dat_data.day_name|escape}){if $dat_data.ray_name}<br /><strong>{$dat_data.ray_name|escape}</strong>{/if}</div></td>
            </tr>
            {capture assign='counter_name'}counter_{$current_day_idx}{/capture}
            {foreach from=$dat_data.meetings item=meeting name=meet}
              {if $meeting.record_type eq 'document'}
                {capture assign='meeting_row_color'}{strip}
                  {if $meeting.visit_status eq $report_settings.daily_plan_meeting_marked}
                    {$meetings_rows_colors.daily_plan_meeting_marked}
                  {elseif $meeting.visit_status eq $report_settings.daily_plan_meeting_failed}
                    {$meetings_rows_colors.daily_plan_meeting_failed}
                  {/if}
                {/strip}{/capture}
                {capture assign='row_text'}{strip}
                  {$meeting.customer_name}
                  {if $meeting.address}
                    ({$meeting.address|escape})
                  {/if}
                {/strip}{/capture}
              {else}
                {capture assign='color_key'}event_type_{$meeting.event_type_keyword}{/capture}
                {assign var='meeting_row_color' value=$meetings_rows_colors[$color_key]}
                {capture assign='row_text'}{strip}
                  {$meeting.event_type_name|escape}{if $meeting.customer_name} ({$meeting.customer_name}){/if}
                {/strip}{/capture}
              {/if}
              {capture assign='meeting_row_uniqid'}{uniqid}{/capture}
              <tr class="{strip}
                {if $dat_data.current_date}
                  {cycle name=$counter_name values='t_current_dat_odd,t_current_dat_even'}
                {else}
                  {cycle name=$counter_name values='t_odd1 t_odd2,t_even1 t_even2'}
                {/if}{/strip}">
                <td style="height: 19px; width: 20px!important; text-align: center;{if $meeting_row_color} background-color: {$meeting_row_color};{/if}" class="t_border">
                  {if $meeting.visit_status eq $report_settings.daily_plan_meeting_marked}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$meeting.id}">
                      <img title="{#reports_open_daily_plan#}" src="{$templatesUrlPlugin}{$report_type}/templates/icons/visit_made.png" />
                    </a>
                  {elseif $meeting.visit_status eq $report_settings.daily_plan_meeting_failed}
                    <img title="{#reports_show_failed_daily_plan_comment#}" style="cursor: pointer;" onclick="toggleMeetingRow('{$meeting_row_uniqid}')" src="{$templatesUrlPlugin}{$report_type}/templates/icons/visit_failed.png" />
                  {/if}
                </td>
                {assign var='row_text' value=$row_text|regex_replace:"/[\r\t\n]/":" "}
                <td style="{if $meeting_row_color}background-color: {$meeting_row_color};{/if} width: {if !$dat_data.future_date}193{else}255{/if}px; height: 19px;"{if $dat_data.future_date} colspan="2"{/if} title="{$row_text|escape}">
                  <div style="width: 242px; height: 17px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; margin-top: 2px;">
                    {$row_text|escape}
                  </div>
                </td>
              </tr>
              {if $meeting.visit_status ne $report_settings.daily_plan_meeting_marked}
                <tr style="display: none;" id="{$meeting_row_uniqid}">
                  <td colspan="2">{#reports_meeting_failed_description#} {$meeting.description}</td>
                </tr>
              {/if}
            {foreachelse}
              <tr class="{if $dat_data.current_date}{cycle name=$counter_name values='t_current_dat_odd,t_current_dat_even'}{else}{cycle name=$counter_name values='t_odd1 t_odd2,t_even1 t_even2'}{/if}">
                <td colspan="2" style="height: 19px;">
                  <span class="error">{#no_items_found#|escape}</span>
                </td>
              </tr>
            {/foreach}
          </table>
        </td>
      {/foreach}
    </tr>
  </table>
  <fieldset class="bt_mfv_legend_container">
    <legend>{#reports_legend#}</legend>
    <table>
      <tr>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.daily_plan_meeting_marked}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_daily_plan_meeting_marked#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.daily_plan_meeting_failed}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_daily_plan_meeting_failed#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_leave}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_leave#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_medical}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_medical#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_lunch}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_lunch#}</td>
        <td><span class="bt_mfv_legend_sample" style="background-color: {$reports_results.meetings_rows_colors.event_type_conference}"></span></td>
        <td class="bt_mfv_legend_text">{#reports_legend_event_type_conference#}</td>
      </tr>
    </table>
  </fieldset>
{else}
  <span class="error">{#no_items_found#|escape}</span>
{/if}
*}