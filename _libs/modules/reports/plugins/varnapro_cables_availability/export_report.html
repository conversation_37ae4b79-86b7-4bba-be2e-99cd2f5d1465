<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
      <table border="1">
        <tr>
          <td width="250" align="center"><strong>{#reports_nomenclature#|escape}</strong></td>
          <td width="125" align="center"><strong>{#reports_documents_quantity#|escape}</strong></td>
          <td width="125" align="center"><strong>{#reports_availability#|escape}</strong></td>
          <td width="125" align="center"><strong>{#reports_needed_quantity#|escape}</strong></td>
        </tr>
        {foreach from=$reports_results key=k name=list_res item=result}
          <tr>
            <td nowrap="nowrap">
              [{$result.code|escape|default:"&nbsp;"}] {$result.name|escape|default:"&nbsp;"}
            </td>
            <td align="right" nowrap="nowrap">
              {$result.documents_quantity|escape|default:"&nbsp;"}
            </td>
            <td align="right" nowrap="nowrap">
              <span{if $result.colored} style="color: #FF0000; font-weight: bold;"{/if}>{$result.werehouse_quantity}</span>
            </td>
            <td align="right" nowrap="nowrap">
              {if $result.needed_quantity>0}
                {$result.needed_quantity}
              {else}
                -
              {/if}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="4"><span style="color: #FF0000;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
  </body>
</html>