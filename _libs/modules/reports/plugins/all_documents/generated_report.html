<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
      <td class="t_caption t_border {$sort.full_num.class}" nowrap="nowrap"><div class="t_caption_title">{#documents_full_num#|escape}</div></td>
      <td class="t_caption t_border {$sort.custom_num.class}" nowrap="nowrap"><div class="t_caption_title">{#documents_custom_num#|escape}</div></td>
      <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title">{#documents_name#|escape}</div></td>
      <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title">{#documents_type#|escape}</div></td>
      <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title">{#documents_customer#|escape}</div></td>
      <td class="t_caption t_border {$sort.group.class}" nowrap="nowrap"><div class="t_caption_title">{#documents_group#|escape}</div></td>
      <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title">{#documents_status#|escape}</div></td>
      <td class="t_caption {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title">{#date#|escape}</div></td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach from=$reports_results item=result}
      <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}">
        <td class="t_border hright" nowrap="nowrap" width="25">
          {counter name='item_counter' print=true}
        </td>
        <td class="t_border">
          {$result.full_num|numerate:$result.direction}
        </td>
        <td class="t_border">
          {$result.custom_num|numerate:$result.direction:'':'%s'|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.type_name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.customer_name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border">
          {$result.group_name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border hright">
          {if $result.status != 'closed' && $result.deadline && $result.deadline|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
            <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=#documents_expired#|escape caption=#documents_expired#|escape} />
          {/if}
          {if $result.status eq 'opened'}
            <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {popup text=#help_documents_status_opened#|escape caption=#help_documents_status#|escape} />
          {elseif $result.status eq 'locked'}
            <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {popup text=#help_documents_status_locked#|escape caption=#help_documents_status#|escape} />
          {elseif $redult.status eq 'closed'}
            <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {popup text=#help_documents_status_closed#|escape caption=#help_documents_status#|escape} />
          {/if}
        </td>
        <td nowrap="nowrap" width="75">
          {$result.added|date_format:#date_short#|escape}
        </td>
      </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="9">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    <tr>
      <td class="t_footer" colspan="9"></td>
    </tr>
  </table>
</td>
</tr>
    <tr>
      <td class="pagemenu" bgcolor="#FFFFFF" colspan="9">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
</table>