<?php
Class Asp_Income_By_Activities Extends Reports {
    public static function buildQuery(&$registry, $filters = array()) {
        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        if (empty($filters['from_date']) || empty($filters['to_date'])) {
            return array(0, 0);
        }
        $registry->set('prepareModels', false, true);

        $types_array = array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE,
                             PH_FINANCE_TYPE_ANNULMENT);
        $types_array = array_merge($types_array, preg_split('#\s*,\s*#', REPORTS_ADDITIONAL_TYPES));

        $articles_list = array();
        $show_article_list = false;
        $articles_list_filtered = false;
        if ($filters['report_by'] == 'by_article') {
            $show_article_list = true;

            $available_types = array(TECHNICS_TYPE_ID, MTO_TYPE_ID, SERVICES_TYPE_ID);
            $needed_vars = array(TECHNICS_GROUP_VAR, TECHNICS_TYPES_VAR, MTO_GROUP_VAR, MTO_TYPES_VAR, SERVICES_GROUP_VAR, SERVICES_TYPES_VAR);
            $sql = 'SELECT CONCAT(name, "_", model_type), id FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`= "Nomenclature" AND model_type IN (' . implode(',', $available_types) . ') AND name IN ("' . implode('","', $needed_vars) . '")';
            $nom_vars = $registry['db']->getAssoc($sql);

            $union_queries = array();
            if (!empty($filters['report_by_article_technics'])) {
                $sql = 'SELECT n.id' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n";
                if (!empty($filters['group_technics'])) {
                    $sql .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                            ' ON (n_cstm.model_id=n.id AND n_cstm.var_id="' . (isset($nom_vars[TECHNICS_GROUP_VAR . '_' . TECHNICS_TYPE_ID]) ? $nom_vars[TECHNICS_GROUP_VAR . '_' . TECHNICS_TYPE_ID] : "") . '" AND n_cstm.value="' . $filters['group_technics'] . '")';
                }
                if (!empty($filters['types_technics'])) {
                    $sql .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm1' . "\n" .
                            ' ON (n_cstm1.model_id=n.id AND n_cstm1.var_id="' . (isset($nom_vars[TECHNICS_TYPES_VAR . '_' . TECHNICS_TYPE_ID]) ? $nom_vars[TECHNICS_TYPES_VAR . '_' . TECHNICS_TYPE_ID] : "") . '" AND n_cstm1.value="' . $filters['types_technics'] . '")';
                }
                $sql .= 'WHERE n.active=1 AND n.deleted_by=0 AND n.type="' . TECHNICS_TYPE_ID . '"';
                $union_queries[] = sprintf('(%s)', $sql);
            }

            if (!empty($filters['report_by_article_mto'])) {
                $sql = 'SELECT n.id' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n";
                if (!empty($filters['groups_mto'])) {
                    $sql .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                            ' ON (n_cstm.model_id=n.id AND n_cstm.var_id="' . (isset($nom_vars[MTO_GROUP_VAR . '_' . MTO_TYPE_ID]) ? $nom_vars[MTO_GROUP_VAR . '_' . MTO_TYPE_ID] : "") . '" AND n_cstm.value="' . $filters['groups_mto'] . '")';
                }
                if (!empty($filters['types_mto'])) {
                    $sql .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm1' . "\n" .
                            ' ON (n_cstm1.model_id=n.id AND n_cstm1.var_id="' . (isset($nom_vars[MTO_TYPES_VAR . '_' . MTO_TYPE_ID]) ? $nom_vars[MTO_TYPES_VAR . '_' . MTO_TYPE_ID] : "") . '" AND n_cstm1.value="' . $filters['types_mto'] . '")';
                }
                $sql .= 'WHERE n.active=1 AND n.deleted_by=0 AND n.type="' . MTO_TYPE_ID . '"';
                $union_queries[] = sprintf('(%s)', $sql);
            }

            if (!empty($filters['report_by_article_service'])) {
                $sql = 'SELECT n.id' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n";
                if (!empty($filters['groups_services'])) {
                    $sql .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                            ' ON (n_cstm.model_id=n.id AND n_cstm.var_id="' . (isset($nom_vars[SERVICES_GROUP_VAR . '_' . SERVICES_TYPE_ID]) ? $nom_vars[SERVICES_GROUP_VAR . '_' . SERVICES_TYPE_ID] : "") . '" AND n_cstm.value="' . $filters['groups_services'] . '")';
                }
                if (!empty($filters['types_services'])) {
                    $sql .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm1' . "\n" .
                            ' ON (n_cstm1.model_id=n.id AND n_cstm1.var_id="' . (isset($nom_vars[SERVICES_TYPES_VAR . '_' . SERVICES_TYPE_ID]) ? $nom_vars[SERVICES_TYPES_VAR . '_' . SERVICES_TYPE_ID] : "") . '" AND n_cstm1.value="' . $filters['types_services'] . '")';
                }
                $sql .= 'WHERE n.active=1 AND n.deleted_by=0 AND n.type="' . SERVICES_TYPE_ID . '"';
                $union_queries[] = sprintf('(%s)', $sql);
            }

            if (!empty($union_queries)) {
                $articles_list_filtered = true;
                $sql = implode(' UNION ' . "\n", $union_queries);

                $articles_list = $registry['db']->GetCol($sql);
            }
        }

        $query = 'SELECT fir.id as idx, fir.id, fir.type, fir.currency, fir.date_of_payment, fir.issue_date, fir.total_with_vat, fir.total' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n";
        if ($show_article_list && $articles_list_filtered) {
            $query .= 'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                      '  ON (gt2.model="Finance_Incomes_Reason" AND gt2.model_id=fir.id AND gt2.article_id IN ("' . implode('","', $articles_list) . '"))' . "\n";
        }
        $query .= 'WHERE fir.total!= 0 AND fir.type IN (' . implode(',', $types_array) . ') AND fir.issue_date<=\'' . $filters['to_date'] . '\' AND fir.active = 1 AND fir.annulled_by= 0 AND fir.status=\'finished\'';
        if (!empty($filters['office'])) {
            $query .= ' AND fir.office="' . $filters['office'] . '"';
        }
        $incomes = $registry['db']->GetAssoc($query);

        $credit_notices_before_date = array();
        $credit_notices_after_date = array();
        $credit_notices_all_after_date = array();
        $incomes_before_date = array();
        $incomes_after_date = array();
        $incomes_all_after_date = array();
        $before_inv = array();
        $after_inv = array();
        $all_after_inv = array();
        foreach ($incomes as $key => $inc) {
            if ($inc['type'] == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                if ($inc['issue_date'] < $filters['from_date']) {
                    $credit_notices_before_date[] = $inc['id'];
                    $credit_notices_after_date[] = $inc['id'];
                } else {
                    $credit_notices_all_after_date[] = $inc['id'];
                }
            } else {
                if ($inc['issue_date'] < $filters['from_date']) {
                    $incomes_before_date[] = $inc['id'];
                    $incomes_after_date[] = $inc['id'];
                } else {
                    $incomes_all_after_date[] = $inc['id'];
                }
            }
        }

        $result = array('before' => array('invoices' => '0', 'paid' => 0),
                        'during' => array('invoices' => 0, 'paid' => 0),
                        'total' => array('invoices' => 0, 'paid' => 0));

        if (!empty($credit_notices_before_date)) {
            $query = 'SELECT fir.id, -1*SUM(fr.paid_amount) as paid FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     '  ON (fr.parent_id=fir.id AND fr.parent_model_name="Finance_Incomes_Reason" AND DATE_FORMAT(fr.added, "%Y-%m-%d") < "' . $filters['from_date'] . '")' . "\n" .
                     'WHERE fir.id IN (' . implode(',', $credit_notices_before_date) . ')' . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"' . "\n" .
                     'GROUP BY fir.id' . "\n";
            $before_inv = $before_inv + $registry['db']->GetAssoc($query);
        }
        if (!empty($credit_notices_after_date)) {
            $query = 'SELECT fir.id, -1*SUM(fr.paid_amount) as paid FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     '  ON (fr.parent_id=fir.id AND DATE_FORMAT(fr.added, "%Y-%m-%d") >= "' . $filters['from_date'] . '" AND DATE_FORMAT(fr.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '" AND fr.parent_model_name="Finance_Incomes_Reason")' . "\n" .
                     'WHERE fir.id IN (' . implode(',', $credit_notices_after_date) . ')' . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"' . "\n" .
                     'GROUP BY fir.id' . "\n";
            $after_inv = $after_inv + $registry['db']->GetAssoc($query);
        }
        if (!empty($credit_notices_all_after_date)) {
            $query = 'SELECT fir.id, -1*SUM(fr.paid_amount) as paid FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     '  ON (fr.parent_id=fir.id AND DATE_FORMAT(fr.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '" AND fr.parent_model_name="Finance_Incomes_Reason")' . "\n" .
                     'WHERE fir.id IN (' . implode(',', $credit_notices_all_after_date) . ')' . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"' . "\n" .
                     'GROUP BY fir.id' . "\n";
            $all_after_inv = $all_after_inv + $registry['db']->GetAssoc($query);
        }
        if (!empty($incomes_before_date)) {
            $query = 'SELECT fir.id, SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     '  ON (fr.paid_to=fir.id AND fr.paid_to_model_name="Finance_Incomes_Reason" AND DATE_FORMAT(fr.added, "%Y-%m-%d") < "' . $filters['from_date'] . '")' . "\n" .
                     'WHERE fir.id IN (' . implode(',', $incomes_before_date) . ')' . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"' . "\n" .
                     'GROUP BY fir.id' . "\n";
            $before_inv = $before_inv + $registry['db']->GetAssoc($query);
        }
        if (!empty($incomes_after_date)) {
            $query = 'SELECT fir.id, SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     '  ON (fr.paid_to=fir.id AND fr.paid_to_model_name="Finance_Incomes_Reason" AND DATE_FORMAT(fr.added, "%Y-%m-%d") >= "' . $filters['from_date'] . '" AND DATE_FORMAT(fr.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '")' . "\n" .
                     'WHERE fir.id IN (' . implode(',', $incomes_after_date) . ')' . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"' . "\n" .
                     'GROUP BY fir.id' . "\n";
            $after_inv = $after_inv + $registry['db']->GetAssoc($query);
        }
        if (!empty($incomes_all_after_date)) {
            $query = 'SELECT fir.id, SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     '  ON (fr.paid_to=fir.id AND DATE_FORMAT(fr.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '" AND fr.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
                     'WHERE fir.id IN (' . implode(',', $incomes_all_after_date) . ')' . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"' . "\n" .
                     'GROUP BY fir.id' . "\n";
            $all_after_inv = $all_after_inv + $registry['db']->GetAssoc($query);
        }
        unset($credit_notices_before_date);
        unset($credit_notices_after_date);
        unset($credit_notices_all_after_date);
        unset($incomes_before_date);
        unset($incomes_after_date);
        unset($incomes_all_after_date);

        $articles = array();
        $total_before = $total_during = $total_during_without_vat = 0;
        if ($incomes) {
            $query = 'SELECT t1.model_id, t1.article_id, ni18n.name as article_name, t1.subtotal_with_vat_with_discount, t1.subtotal_with_discount' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS . " AS t1\n" .
                     'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . " AS ni18n\n" .
                     '  ON t1.article_id = ni18n.parent_id AND ni18n.lang=\'' . $registry['lang'] . '\'' .
                     'WHERE model=\'Finance_Incomes_Reason\' AND model_id IN(' . implode(', ', array_keys($incomes)) . ')';
            if ($articles_list_filtered) {
                $query .= ' AND t1.article_id IN ("' . implode('","', $articles_list) . '")' . "\n";
            }
            $values = $registry['db']->GetAll($query);
        } else {
            $values = array();
        }

        $gt2_values = array();
        foreach ($values as $val) {
            $gt2_values[$val['model_id']][] = $val;
        }
        $prec = $registry['config']->getSectionParams('precision');

        $currencies = array();

        foreach ($gt2_values as $inc_id => $arts) {
            if (!isset($incomes[$inc_id])) {
                // skip this income
                continue;
            }

            // calculate the currency rate
            $rate = 1;
            if ($incomes[$inc_id]['currency'] != $filters['currency']) {
                if (empty($currencies[$incomes[$inc_id]['currency'] . $filters['currency']])) {
                    $rate = Finance_Currencies::getRate($registry,$incomes[$inc_id]['currency'], $filters['currency']);
                    $currencies[$incomes[$inc_id]['currency'] . $filters['currency']] = $rate;
                } else {
                    $rate = $currencies[$incomes[$inc_id]['currency'] . $filters['currency']];
                }
            }

            // calculate the paid before and paid after for the current invoice
            $paid_before = $paid_after = 0;
            if ($incomes[$inc_id]['issue_date'] < $filters['from_date']) {
                $paid_before = (isset($before_inv[$inc_id]) ? $before_inv[$inc_id] : 0);
                $paid_after = (isset($after_inv[$inc_id]) ? $after_inv[$inc_id] : 0);
            } else {
                $paid_after = (isset($all_after_inv[$inc_id]) ? $all_after_inv[$inc_id] : 0);
            }

            foreach ($arts as $values) {
                if (!isset($articles[$values['article_id']])) {
                    $articles[$values['article_id']] = array('name'                     => $values['article_name'],
                                                             'total_before'             => 0,
                                                             'total_during'             => 0,
                                                             'total_during_without_vat' => 0,
                                                             'paid_old'                 => 0,
                                                             'paid_new'                 => 0,
                                                             'rest'                     => 0);
                }

                if ($incomes[$inc_id]['issue_date'] < $filters['from_date']) {
                    $articles[$values['article_id']]['total_before'] += $values['subtotal_with_vat_with_discount'] * $rate;
                    $articles[$values['article_id']]['paid_old'] += ($paid_before * ($values['subtotal_with_vat_with_discount'] / $incomes[$inc_id]['total_with_vat']) * $rate);
                    $articles[$values['article_id']]['paid_new'] += ($paid_after * ($values['subtotal_with_vat_with_discount'] / $incomes[$inc_id]['total_with_vat']) * $rate);

                    $total_before += $values['subtotal_with_vat_with_discount'] * $rate;
                    $result['before']['paid'] += ($paid_before * ($values['subtotal_with_vat_with_discount'] / $incomes[$inc_id]['total_with_vat']) * $rate);

                    $result['before']['invoices'] += $values['subtotal_with_vat_with_discount'] * $rate;
                } else {
                    $articles[$values['article_id']]['total_during'] += $values['subtotal_with_vat_with_discount'] * $rate;
                    $articles[$values['article_id']]['total_during_without_vat'] += $values['subtotal_with_discount'] * $rate;
                    $articles[$values['article_id']]['paid_new'] += ($paid_after * ($values['subtotal_with_vat_with_discount'] / $incomes[$inc_id]['total_with_vat']) * $rate);

                    $total_during += $values['subtotal_with_vat_with_discount'] * $rate;
                    $total_during_without_vat += $values['subtotal_with_discount'] * $rate;
//                     $result['during']['paid'] += $paid_after *  ($values['subtotal_with_vat_with_discount'] / $incomes[$inc_id]['total_with_vat']) * $rate;
                    $result['during']['invoices'] += $values['subtotal_with_vat_with_discount'] * $rate;
                }

                $result['during']['paid'] += ($paid_after * ($values['subtotal_with_vat_with_discount'] / $incomes[$inc_id]['total_with_vat']) * $rate);
            }
        }

        $result['before']['rest'] = $result['before']['invoices'] - $result['before']['paid'];
        if ($result['before']['invoices'] == 0) {
            $result['before']['collected'] = 0;
            $result['before']['not_collected'] = 0;
        } else {
            $result['before']['collected'] = @round($result['before']['paid'] / $result['before']['invoices'], $prec['gt2_total_with_vat'] + 2) * 100;
            $result['before']['not_collected'] = 100 - $result['before']['collected'];
        }
        $result['during']['rest'] =  $result['during']['invoices'] - $result['during']['paid'];
        if ($result['during']['invoices'] == 0) {
            $result['during']['collected'] = 0;
            $result['during']['not_collected'] = 0;
        } else {
            $result['during']['collected'] = @round($result['during']['paid'] / $result['during']['invoices'], $prec['gt2_total_with_vat'] + 2) * 100;
            $result['during']['not_collected'] = 100 - $result['during']['collected'];
        }

        $result['total']['invoices'] = $result['before']['invoices'] + $result['during']['invoices'];
        $result['total']['paid'] = $result['before']['paid'] + $result['during']['paid'];
        $result['total']['rest'] = $result['before']['rest'] + $result['during']['rest'];

        $articles_totals = array(
            'total_before'             => 0,
            'total_during'             => 0,
            'total_during_without_vat' => 0,
            'paid_old'                 => 0,
            'paid_new'                 => 0,
            'rest'                     => 0
        );
        foreach ($articles as $id => $article) {
            $articles[$id]['rest'] = round($article['total_before'] + $article['total_during'] - $articles[$id]['paid_old'] - $articles[$id]['paid_new'], 2);
            $articles[$id]['total_before'] = round($articles[$id]['total_before'], 2);
            $articles[$id]['total_during'] = round($articles[$id]['total_during'], 2);
            $articles[$id]['total_during_without_vat'] = round($articles[$id]['total_during_without_vat'], 2);
            $articles[$id]['paid_old'] = round($articles[$id]['paid_old'], 2);
            $articles[$id]['paid_new'] = round($articles[$id]['paid_new'], 2);

            if (empty($filters['include_zero_value']) &&
                !round($articles[$id]['total_before'], 2) &&
                !round($articles[$id]['total_during'], 2) &&
                !round($articles[$id]['total_during_without_vat'], 2) &&
                !round($articles[$id]['paid_old'], 2) &&
                !round($articles[$id]['paid_new'], 2) &&
                !round($articles[$id]['rest'], 2)) {
                unset($articles[$id]);
                continue;
            }

            $articles_totals['total_before'] += $articles[$id]['total_before'];
            $articles_totals['total_during'] += $articles[$id]['total_during'];
            $articles_totals['total_during_without_vat'] += $articles[$id]['total_during_without_vat'];
            $articles_totals['paid_old'] += $articles[$id]['paid_old'];
            $articles_totals['paid_new'] += $articles[$id]['paid_new'];
            $articles_totals['rest'] += $articles[$id]['rest'];
        }

        $results = array('result' => $result, 'articles' => $articles, 'articles_totals' => $articles_totals);

         if (!empty($filters['paginate'])) {
            $results = array($results, 0);
        }

        return $results;
    }
}
?>