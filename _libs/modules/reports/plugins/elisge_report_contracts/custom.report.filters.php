<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DOCUMENTS' FILTER
            $filter = array(
                'custom_id' => 'document_id',
                'name'      => 'document_id',
                'type'      => 'hidden',
                'hidden'    => true,
                'required'  => 1,
                'label'     => $this->i18n('reports_documents'),
                'help'      => $this->i18n('reports_documents_help')
            );
            $filters['document_id'] = $filter;

            return $filters;
        }
    }
?>