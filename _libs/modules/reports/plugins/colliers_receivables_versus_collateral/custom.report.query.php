<?php
    Class Colliers_Receivables_Versus_Collateral Extends Reports {

        public static function buildQuery(&$registry, $filters = array()) {

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            if (!defined('REPORT_TYPES')) {
                define('REPORT_TYPES', '1,3,4,17,18');
            }
            if (!defined('REPORT_DEPOSIT')) {
                define('REPORT_DEPOSIT', '25');
            }
            if (!defined('REPORT_DEPOSIT_EXCLUDE')) {
                define('REPORT_DEPOSIT_EXCLUDE', '26');
            }
            if (!defined('REPORT_BANK_GUARANTEE')) {
                define('REPORT_BANK_GUARANTEE', '22');
            }
            if (!defined('REPORT_BANK_GUARANTEE_EXCLUDE')) {
                define('REPORT_BANK_GUARANTEE_EXCLUDE', '16,18,27');
            }
            if (!defined('REPORT_COLLATERAL_PROTOCOL')) {
                define('REPORT_COLLATERAL_PROTOCOL', '33');
            }

            $table_name = Reports::getTemporaryTableName($registry);

            if ($registry->get('report_type')) {
                $report = $registry->get('report_type');
                $report = Reports::getReports($registry, $report);
                $report = $report[0];
            } else {
                return false;
            }

            if ($registry['request']->isRequested('custom_generate') || $registry['action'] == 'dashlet') {

                //get incomes reasons data
                $query = 'SELECT fir.id, fir.total_with_vat, fir.currency, fir.date_of_payment,' . "\n" .
                         '  co.id AS contract_id, co.num AS contract_num,' . "\n" .
                         '  fir.customer AS customer_id, CONCAT(ci18n.name, " ", ci18n.lastname) AS customer_name,' . "\n" .
                         '  co.trademark AS trademark_id, ni18n.name AS trademark_name' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fini' . "\n" .
                         '  ON fir.id = fini.invoice_id AND fir.annulled_by=0 AND fir.active=1' . "\n" .
                         'JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                         '  ON co.id = fini.contract_id AND co.deleted_by=0 AND co.active=1' . "\n" .
                         'JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                         '  ON ci18n.parent_id = fir.customer AND ci18n.lang = "' . $registry['lang'] . '"' . "\n" .
                         'JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                         '  ON ni18n.parent_id = co.trademark AND ni18n.lang = "' . $registry['lang'] . '"' . "\n" .
                         'WHERE fir.type IN(' . REPORT_TYPES . ')' . "\n" .
                         '  AND fir.status = "finished"' . "\n" .
                         '  AND fir.payment_status != "paid"';

                $records = $registry['db']->GetAll($query);

                $data = array();
                $prec = $registry['config']->getSectionParams('precision');
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
                require_once PH_MODULES_DIR . 'finance/models/finance.repayment_plans.factory.php';
                foreach($records as $record) {
                    $key = $record['customer_id'] . '|' . $record['trademark_id'];
                    if (empty($data[$key])) {
                        $data[$key]['trademark_name'] = $record['trademark_name'];
                        $data[$key]['customer_name'] = $record['customer_name'];
                        $data[$key]['trademark_id'] = $record['trademark_id'];
                        $data[$key]['customer_id'] = $record['customer_id'];
                        $data[$key]['contract_num'] = $record['contract_num'];
                        $data[$key]['contract_id'] = $record['contract_id'];
                        $data[$key]['currency'] = $filters['currency'];
                        $data[$key]['amount'] = 0;
                        $data[$key]['amount_less'] = 0;
                        $data[$key]['amount_30'] = 0;
                        $data[$key]['amount_60'] = 0;
                        $data[$key]['amount_90'] = 0;
                        $data[$key]['amount_more'] = 0;
                        if (isset($filters['aging'])) {
                            $data[$key]['aging'] = 1;
                        } else {
                            $data[$key]['aging'] = 0;
                        }

                        //get collaterals
                        $query = 'SELECT d.type, dcstm1.value AS value, dcstm2.value AS currency' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm1' . "\n" .
                                 '  ON fm1.model = "Document" AND d.deleted_by=0 AND d.active=1 AND fm1.model_type = d.type AND fm1.name = "deposit_value"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm1' . "\n" .
                                 '  ON dcstm1.var_id = fm1.id AND dcstm1.model_id = d.id' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
                                 '  ON fm2.model = "Document" AND fm2.model_type = d.type AND fm2.name = "deposit_currency"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm2' . "\n" .
                                 '  ON dcstm2.var_id = fm2.id AND dcstm2.model_id = d.id' . "\n" .
                                 'WHERE d.type = ' . REPORT_DEPOSIT . "\n" .
                                 '  AND d.substatus NOT IN (' . REPORT_DEPOSIT_EXCLUDE . ')' . "\n" .
                                 '  AND d.customer = ' . $record['customer_id'] . "\n" .
                                 '  AND d.trademark = ' . $record['trademark_id'] . "\n" .
                                 'UNION' . "\n" .
                                 'SELECT d.type, dcstm1.value AS value, dcstm2.value AS currency' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm1' . "\n" .
                                 '  ON fm1.model = "Document" AND d.deleted_by=0 AND d.active=1 AND fm1.model_type = d.type AND fm1.name = "value_bankg"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm1' . "\n" .
                                 '  ON dcstm1.var_id = fm1.id AND dcstm1.model_id = d.id' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
                                 '  ON fm2.model = "Document" AND fm2.model_type = d.type AND fm2.name = "bankbg_currency"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm2' . "\n" .
                                 '  ON dcstm2.var_id = fm2.id AND dcstm2.model_id = d.id' . "\n" .
                                 'WHERE d.type = ' . REPORT_BANK_GUARANTEE . "\n" .
                                 '  AND d.substatus NOT IN (' . REPORT_BANK_GUARANTEE_EXCLUDE . ')' . "\n" .
                                 '  AND d.customer = ' . $record['customer_id'] . "\n" .
                                 '  AND d.trademark = ' . $record['trademark_id'] . "\n" .
                                 'UNION' . "\n" .
                                 'SELECT d.type, dcstm1.value AS value, dcstm2.value AS currency' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm1' . "\n" .
                                 '  ON fm1.model = "Document" AND d.deleted_by=0 AND d.active=1 AND fm1.model_type = d.type AND fm1.name = "value_digest"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm1' . "\n" .
                                 '  ON dcstm1.var_id = fm1.id AND dcstm1.model_id = d.id' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
                                 '  ON fm2.model = "Document" AND fm2.model_type = d.type AND fm2.name = "value_digest_currency"' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm2' . "\n" .
                                 '  ON dcstm2.var_id = fm2.id AND dcstm2.model_id = d.id' . "\n" .
                                 'WHERE d.type = ' . REPORT_COLLATERAL_PROTOCOL . "\n" .
                                 '  AND d.customer = ' . $record['customer_id'] . "\n" .
                                 '  AND d.trademark = ' . $record['trademark_id'] . "\n";
                        $collateral = $registry['db']->GetAll($query);
                        if (empty($data[$key]['collateral'])) {
                            $data[$key]['collateral'] = 0;
                        }
                        foreach ($collateral as $c) {
                            $rate = Finance_Currencies::getRate($registry, $c['currency'], $filters['currency']);
                            if ($c['type'] == REPORT_DEPOSIT || $c['type'] == REPORT_BANK_GUARANTEE) {
                                $data[$key]['collateral'] += $c['value'] * $rate;
                            } else {
                                $data[$key]['collateral'] -= $c['value'] * $rate;
                            }
                        }

                    }
                    //get payments and repayment plans
                    $fir = new Finance_Incomes_Reason($registry, array('id' => $record['id']));
                    $fir->getPaidAmount();
                    if (empty($data[$key]['plan'])) {
                        $data[$key]['plan'] = $fir->checkRepaymentPlans();
                    }
                    $rate = Finance_Currencies::getRate($registry, $record['currency'], $filters['currency']);
                    $data[$key]['amount'] += ($record['total_with_vat'] - $fir->get('paid_amount'))* $rate;

                    //prepare aging
                    if (isset($filters['aging'])) {
                        $now = General::strftime('%Y-%m-%d');
                        if (!preg_match('#^\d{4}-\d{2}-\d{2}#', $record['date_of_payment']) || $record['date_of_payment'] >= $now) {
                            $data[$key]['amount_less'] += ($record['total_with_vat'] - $fir->get('paid_amount'))* $rate;
                        } elseif (General::strftime('%Y-%m-%d', strtotime('+30 day', strtotime($record['date_of_payment']))) >= $now) {
                            $data[$key]['amount_30'] += ($record['total_with_vat'] - $fir->get('paid_amount'))* $rate;
                        } elseif (General::strftime('%Y-%m-%d', strtotime('+60 day', strtotime($record['date_of_payment']))) >= $now) {
                            $data[$key]['amount_60'] += ($record['total_with_vat'] - $fir->get('paid_amount'))* $rate;
                        } elseif (General::strftime('%Y-%m-%d', strtotime('+90 day', strtotime($record['date_of_payment']))) >= $now) {
                            $data[$key]['amount_90'] += ($record['total_with_vat'] - $fir->get('paid_amount'))* $rate;
                        } else {
                            $data[$key]['amount_more'] += ($record['total_with_vat'] - $fir->get('paid_amount'))* $rate;
                        }
                    }
                }

                //generate the difference
                foreach ($data as $key => $values) {
                    $data[$key]['difference'] = $values['amount'] - $values['collateral'];
                    //get last sent date
                    $query = 'SELECT sent FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model =\'Report\' AND model_id="' . $report->get('id') . '"' . "\n" .
                         '  AND extra RLIKE \'customer( )*:=( )*' . $values['customer_id'] . '\'' . "\n" .
                         'ORDER BY sent DESC' . "\n" .
                         'LIMIT 1';
                    $data[$key]['last_sent'] = $registry['db']->GetOne($query);
                }

                //the following must be here for this kind of report
                $query = 'DROP TABLE IF EXISTS ' . $table_name;
                $records = $registry['db']->Execute($query);
                //create the report table
                $sql['create'] = 'CREATE TABLE IF NOT EXISTS ' . $table_name . "\n" .
                                 '  (idx int(11) AUTO_INCREMENT,' . "\n" .
                                 '  selected tinyint(1),' . "\n" .
                                 '  trademark_name varchar(255),' . "\n" .
                                 '  customer_name varchar(255),' . "\n" .
                                 '  trademark_id int(11),' . "\n" .
                                 '  customer_id int(11),' . "\n" .
                                 '  contract_num varchar(255),' . "\n" .
                                 '  contract_id int(11),' . "\n" .
                                 '  currency varchar(3),' . "\n" .
                                 '  collateral float(15,6),' . "\n" .
                                 '  plan int(11),' . "\n" .
                                 '  aging tinyint(1),' . "\n" .
                                 '  amount float(15,6),' . "\n" .
                                 '  amount_less float(15, 6),' . "\n" .
                                 '  amount_30 float(15, 6),' . "\n" .
                                 '  amount_60 float(15, 6),' . "\n" .
                                 '  amount_90 float(15, 6),' . "\n" .
                                 '  amount_more float(15, 6),' . "\n" .
                                 '  difference float(15,6),' . "\n" .
                                 '  last_sent date,' . "\n" .
                                 '   PRIMARY KEY(idx)' . "\n" .
                                 '  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci' . "\n";
                $query = implode("\n", $sql);
                //prepare insert query
                $records = $registry['db']->Execute($query);
                $set = array();
                foreach ($data as $k => $values) {
                    $set[$k] = '("' . implode('", "', General::slashesEscape($values)) . '")';
                }
                $set = implode(",\n", $set);
                if (!empty($data)) {
                    $data = array_keys(array_shift($data));
                }
                //insert data into the table
                $query = 'INSERT INTO ' . $table_name . ' (' . implode(', ', $data) . ') VALUES' . "\n" . $set;
                $records = $registry['db']->Execute($query);
            }

            $query = 'SELECT * FROM ' . $table_name . "\n";
            if ($filters['order'] == 'amount') {
                $query .= 'ORDER BY total';
            } elseif ($filters['order'] == 'difference') {
                $query .= 'ORDER BY difference';
            } elseif ($filters['order'] == 'tenant') {
                $query .= 'ORDER BY customer_name';
            } elseif ($filters['order'] == 'trademark') {
                $query .= 'ORDER BY trademark_name';
            }
            $records = $registry['db']->GetAll($query);

            if (!empty($filters['paginate'])) {
                return array($records, 0);
            } else {
                return $records;
            }
        }

        public static function getCustomData(&$registry, $filters = array(), $report_filters = array()) {

            $table_name = Reports::getTemporaryTableName($registry);
            Reports::getReportSettings($registry);

            $query = 'SELECT v.*, ' . "\n" .
                     '  co.branch, co.contact_person, co.company, co.office,' . "\n" .
                     '  co.date_validity AS contract_validity, coi18n.name AS contract_name,' . "\n" .
                     '  fci18n.name AS company_name, oi18n.name AS office_name' . "\n" .
                     'FROM ' . $table_name . ' AS v' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                     '  ON v.contract_id = co.id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n' . "\n" .
                     '  ON v.contract_id = coi18n.parent_id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                     '  ON co.company = fci18n.parent_id AND fci18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                     '  ON co.office = oi18n.parent_id AND oi18n.lang = \'' . $registry['lang'] . '\'' . "\n";
            if (empty($filters)) {
                $query .= 'WHERE v.selected = 1';
            } else {
                $query .= 'WHERE ' . implode(' AND ', $filters);
            }
            $query .= ' AND co.deleted_by=0 AND co.active=1';

            //search basic details with current lang parameters
            $records = $registry['db']->GetAll($query);

            return $records;
        }
    }
?>
