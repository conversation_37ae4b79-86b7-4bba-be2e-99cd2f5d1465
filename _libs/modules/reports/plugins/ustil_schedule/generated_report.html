{literal}
  <style type="text/css">
      td.ustil_schedule_filters_table .reports_half_size_filter_panel {
          width: 100%;
      }
  </style>
{/literal}

<div style="display: none;" id="add_edit_result"></div>
<table style="width: 100%;" cellspacing="0" cellpadding="0">
  <tr>
    <td style="width: 350px; vertical-align: top; height: 250px; white-space: nowrap;" class="ustil_schedule_filters_table">
      <form name="reports_generated" id="reports_generated" action="{$smarty.server.PHP_SELF}" method="post">
      <input type="hidden" name="{$module_param}" value="reports" />
      <input type="hidden" name="reports" value="generate_report" />
      <input type="hidden" name="report_type" value="{$report_type}" />
        {include file="`$templatesDir`default_filters.html"}
      </form>
    </td>
    {capture assign=templates_path}{$smarty.const.PH_MODULES_DIR}reports/plugins/{$report_type}/{/capture}
    <td style="vertical-align: top;" rowspan="2" id="report_container">
      {capture assign='res_offset'}{if is_array($reports_results.offsets)}{$reports_results.offsets|@count}{else}0{/if}{/capture}
      <table cellpadding="0" cellspacing="0" class="t_table" id="ustil_schedule" style="width: 100%; overflow: hidden!important; min-width: {math equation='50+n*125' n=$res_offset}px;">
        <tr>
          <td colspan="2" class="t_caption">
            <div class="t_caption2_title hcenter">
              {$reports_results.today} ({$reports_results.date|date_format:#date_short#})
              <div class="floatr pointer" onclick="showAddForm()" title="{#add#}"><img src="{$theme->imagesUrl}plus.png" alt="{#add#}" /></div>
            </div>
            <div id="add_doc_container" style="display: none; position: absolute; border: 1px solid #cccccc; background: #dddddd; width: 200px;">
              <div class="t_caption2 t_caption3_title">
                {#reports_create#}
                <img src="{$theme->imagesUrl}delete.png" class="floatr pointer" onclick="this.parentNode.parentNode.style.display = 'none';" alt="{#close#}" title="{#close#}" />
              </div>
              <div>&nbsp;</div>
              <div class="hcenter vmiddle">
                  <button class="button" onclick="showEditForm('', {$smarty.const.SCHEDULE_DOC});">{#reports_doc1#}</button><br /><br />
                  <button class="button" onclick="showEditForm('', {$smarty.const.DELIVERY_DOC});">{#reports_doc2#}</button><br /><br />
                  <button class="button" onclick="showEditForm('', {$smarty.const.REPAIR_DOC});">{#reports_repair_service#}</button><br />
              </div>
              <div>&nbsp;</div>
            </div>
          </td>
        </tr>
        {foreach from=$reports_results.schedules.current item=s}
          {if empty($s.start) || empty($s.end)}
            <tr>
              <td class="cal_hour t_border_double t_v_border" style="width: 50px;"></td>
              <td class="t_border_double t_v_border pointer" style="background: {$s.color};" onclick="showEditForm({$s.id})">
                {$s.customer_name} / {$s.object_name} - {$s.notes|default:"&nbsp;"}
              </td>
            </tr>
          {/if}
        {/foreach}
        {array assign=shown}
        {foreach from=$reports_results.hours item=hour name=h}
          {if !$smarty.foreach.h.first}
            <tr>
              <td class="cal_hour t_border_double {if !$smarty.foreach.h.last}t_v_border{/if}" style="height: 31px; width: 50px;">{$prev_hour}</td>
              <td class="vtop {if !$smarty.foreach.h.last}t_v_border{/if}" style="height: 31px;">
              {foreach from=$reports_results.schedules.current item=s key=idx}
                {if !empty($s.start) && !empty($s.end)}
                  {if $s.start lt $hour && $s.start ge $prev_hour}
                    <div style="position: relative; left: {$reports_results.offsets[$s.person]*125}px; top: 0px; overflow: visible; width: 120px;">
                      {assign var=tsheight value=$s.duration-2}
                      {capture assign=full_info}
                      {capture assign='address'}
                      {$s.town}, {$s.quarter},
                      {if $s.street}{$s.street},{/if}
                      {if $s.block}{#reports_block#} {$s.block},{/if}
                      {if $s.entrance}{#reports_entrance#} {$s.entrance},{/if}
                      {if $s.floor}{#reports_floor#} {$s.floor},{/if}
                      {if $s.flat}{#reports_flat#} {$s.flat},{/if}
                      {/capture}
                      {$address|regex_replace:'/[\s,]+$/':''|escape}<br />
                      {if $s.phone}{$s.phone|replace:',':'<br />'}<br />{/if}{if $s.mobile}{$s.mobile|replace:',':'<br />'}<br />{/if}<br />
                      {if $s.type_service}{$s.type_service|nl2br}{else}{#reports_repair_service#}{/if}<br />
                      {$s.start_before|default:$s.start} - {$s.end}
                      {/capture}
                      <div class="cal_event" {help popup_only=true text_content=$full_info label_content=$s.customer_name} style="z-index: 1000; cursor: pointer; overflow: hidden; width: 120px; top: {$s.offset}px; height: {if $smarty.foreach.h.last && $tsheight+$s.offset > 27}{math equation=x-y x=27 y=$s.offset}{else}{$tsheight}{/if}px; position: absolute;{if $s.color} background: {$s.color};{/if}" onclick="showEditForm({$s.id})">
                        {*{$s.town}, {$s.township}, *}{$s.quarter}
                        {if $tsheight ge 20}<br />{$s.customer_name}{/if}
                        {if $tsheight ge 40}<br />{if $s.phone}{$s.phone}{else}{$s.mobile}{/if}{/if}
                        {if $tsheight ge 60 && $s.phone && $s.mobile}<br />{$s.mobile}{/if}
                      </div>
                    </div>
                  {elseif $smarty.foreach.h.last && $s.start ge $hour}
                    {assign var=has_outer value=true}
                  {/if}
                {/if}
              {/foreach}
              </td>
            </tr>
          {/if}
          {assign var=prev_hour value=$hour}
        {/foreach}
        </table>
        {if $has_outer}
        <table cellpadding="0" cellspacing="0" class="t_table" id="out_schedules" style="width: 100%; overflow: hidden!important; border: none!important;">
          <tr>
            <td class="cal_hour t_border" style="width: 50px;">&nbsp;</td>
            <td class="cal_week_day vtop">
            {foreach from=$reports_results.schedules.current item=s key=idx}
              {if $s.start ge $hour}
                {capture assign=full_info}
                {capture assign='address'}
                {$s.town}, {$s.quarter},
                {if $s.street}{$s.street},{/if}
                {if $s.block}{#reports_block#} {$s.block},{/if}
                {if $s.entrance}{#reports_entrance#} {$s.entrance},{/if}
                {if $s.floor}{#reports_floor#} {$s.floor},{/if}
                {if $s.flat}{#reports_flat#} {$s.flat},{/if}
                {/capture}
                {$address|regex_replace:'/[\s,]+$/':''|escape}<br />
                {if $s.phone}{$s.phone|replace:',':'<br />'}<br />{/if}{if $s.mobile}{$s.mobile|replace:',':'<br />'}<br />{/if}<br />
                {if $s.type_service}{$s.type_service|nl2br}{else}{#reports_repair_service#}{/if}<br />
                {$s.start} - {$s.end}
                {/capture}
                <div class="cal_event" {help popup_only=true text_content=$full_info label_content=$s.customer_name} style="cursor: pointer; margin-right: 5px; overflow: hidden; height: 30px; width: 120px; position: relative; float: left; background: {$s.color};" onclick="showEditForm({$s.id})">
                  {*{$s.town}, {$s.township}, *}{$s.quarter}<br />{$s.customer_name}{*<br />{$s.phone}, {$s.mobile}*}
                </div>
              {/if}
            {/foreach}
            </td>
          </tr>
        </table>
        {/if}
      <div class="clear"></div>
    </td>
    <td rowspan="2" style="background: #f1f1f1; width: 150px;" class="vtop t_border t_border_left t_v_border">
      <table class="t_table" cellpadding="3" cellspacing="0">
        <tr>
          <td class="t_caption2" colspan="2" style="height: 16px; padding-left: 10px; padding-right: 10px;">
            <a href="{$reports_results.uri|escape}&amp;datef={$reports_results.prev_week}" title="{#reports_previous_week#}">
              <span class="floatl pointer" style="width: 30%;">
                <img src="{$theme->imagesUrl}arrow_left.png" alt="&laquo;" />
              </span>
            </a>
            <a href="{$reports_results.uri|escape}&amp;datef={$reports_results.next_week}" title="{#reports_next_week#}">
              <span class="floatr pointer" style="width: 30%;">
                <img src="{$theme->imagesUrl}arrow_right.png" class="floatr" alt="&raquo;" />
              </span>
            </a>
          </td>
        </tr>
      {foreach from=$reports_results.schedules.week item=s key=d}
        {if $reports_results.date ne $s.date}
          <tr>
            <td colspan="2" class="t_caption4 t_caption3_title hcenter strong">
              <a href="{$reports_results.uri|escape}&amp;datef={$s.date}">{$d}</a>
            </td>
          </tr>
          {foreach from=$s.hours item=count key=hour name=h}
            {if ($smarty.foreach.h.iteration % 2) ne 0}
              <tr>
            {/if}
            <td style="white-space: nowrap;" class="t_border{if $count} strong{/if}">{$hour} - {$count}</td>
            {if ($smarty.foreach.h.iteration % 2) eq 0}
              </tr>
            {elseif ($smarty.foreach.h.iteration % 2) ne 0 && $smarty.foreach.h.last}
              <td class="t_border">&nbsp;</td></tr>
            {/if}
          {/foreach}
        {/if}
      {/foreach}
      </table>
    </td>
  </tr>
  <tr>
    <td style="background: #f1f1f1; padding-top: 30px; padding-left: 20px;" class="t_border t_border_left t_v_border vtop">
      <table class="t_table" style="width: 100%; border: none!important;" cellspacing="10">
      {foreach from=$reports_results.legend item=color key=name}
        <tr>
          <th style="background: {$color}; width: 30px;">&nbsp;</th>
          <th class="hleft">{$name}</th>
        </tr>
      {/foreach}
      </table>
    </td>
  </tr>
  {if is_array($reports_results.schedules.period) && $reports_results.schedules.period|@count}
  <tr>
    <td colspan="3" style="padding-top: 20px;">
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="width: 100%;">
        <tr class="reports_title_row">
          <th class="t_border">{#num#}/{#reports_date#}</th>
          <th class="t_border">{#type#}</th>
          <th class="t_border">{#reports_office#}</th>
          <th class="t_border">{#reports_customer#}</th>
          <th class="t_border">{#reports_towns#}</th>
          <th class="t_border">{#reports_township#}</th>
          <th class="t_border">{#reports_quarter#}</th>
          <th class="t_border">{#reports_address#}</th>
          <th class="t_border">{#reports_telephone#}</th>
          <th>{#reports_note#}</th>
        </tr>
        {foreach from=$reports_results.schedules.period item=s}
        <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
          <td class="t_border"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$s.id}" target="_blank">{$s.full_num}/{$s.date|date_format:#date_short#}</a></td>
          <td class="t_border">{$s.type_name}</td>
          <td class="t_border">{$s.office_name}</td>
          <td class="t_border"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$s.customer}" target="_blank">{$s.customer_name}</a></td>
          <td class="t_border">{$s.town}</td>
          <td class="t_border">{$s.township}</td>
          <td class="t_border">{$s.quarter}</td>
          <td class="t_border">
            {capture assign='address'}
            {if $s.street}{$s.street},{/if}
            {if $s.block}{#reports_block#} {$s.block},{/if}
            {if $s.entrance}{#reports_entrance#} {$s.entrance},{/if}
            {if $s.floor}{#reports_floor#} {$s.floor},{/if}
            {if $s.flat}{#reports_flat#} {$s.flat},{/if}
            {/capture}
            {$address|regex_replace:'/[\s,]+$/':''|escape}
          </td>
          <td class="t_border">{if $s.phone}{$s.phone|replace:',':'<br />'}<br />{/if}{if $s.mobile}{$s.mobile|replace:',':'<br />'}{/if}</td>
          <td>{$s.notes|default:"&nbsp;"}</td>
        </tr>
        {/foreach}
      </table>
    </td>
  </tr>
  {/if}
</table>
