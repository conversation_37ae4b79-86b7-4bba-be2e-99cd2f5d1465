<?php
    Class Insureres_Circulation_Report Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $document_type_id = 0;
            if (!empty($filters['document_type'])) {
                if ($filters['document_type'] == 'income') {
                    $document_type_id = DOCUMENT_TYPE_INCOME_ID;
                } else {
                    $document_type_id = DOCUMENT_TYPE_EXPENSE_ID;
                }
            }

            if (!empty($filters['year']) || !empty($filters['case_num'])) {
                $document_type_id = DOCUMENT_TYPE_EXPENSE_ID;
            }

            $damage_insys_options = explode(',', CASE_DAMAGE_INSYS_VARS);
            // get ids of vars that are damage in insys
            $sql_for_add_vars_proj = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Project" AND fm.name IN ("' . implode('","', $damage_insys_options) . '")' . "\n";
            $in_damage_insys_ids = $registry['db']->GetCol($sql_for_add_vars_proj);

            // get ids of vars for companies
            if (!empty($filters['company'])) {
                $project_company = explode(',', PROJECT_COMPANY);
                $sql_for_add_vars_proj = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Project" AND fm.name IN ("' . implode('","', $project_company) . '")' . "\n";
                $company_vars = $registry['db']->GetCol($sql_for_add_vars_proj);
            }

            $incomings = array();
            $expenses = array();

            // incomings
            if (!$document_type_id || $document_type_id==DOCUMENT_TYPE_INCOME_ID) {
                $sql_for_add_vars_1 = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_INCOME_ID . ' AND (fm.name="' . TYPE_INCOME . '" OR fm.name="' . PAYING_REASON . '" OR fm.name="' . PAYING_TYPE . '" OR fm.name="' . PAYING_VALUE . '" OR fm.name="' . PAYING_CURRENCY . '" OR fm.name="' . TAKEN_MONEY . '" OR fm.name="' . RECEIVING_DATE . '" OR fm.name="' . PAYING_NOTES . '") ORDER BY fm.position';
                $var_ids_1 = $registry['db']->GetAll($sql_for_add_vars_1);

                $in_type_income_id = '';
                $in_paying_reason_id = '';
                $in_type_paying_id = '';
                $in_paying_value_id = '';
                $in_paying_currency_id = '';
                $in_take_money_id = '';
                $in_money_date_id = '';
                $in_paying_notes_id = '';

                foreach ($var_ids_1 as $vars) {
                    if ($vars['name'] == TYPE_INCOME) {
                        $in_type_income_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_REASON) {
                        $in_paying_reason_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_TYPE) {
                        $in_type_paying_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_VALUE) {
                        $in_paying_value_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_CURRENCY) {
                        $in_paying_currency_id = $vars['id'];
                    } else if ($vars['name'] == TAKEN_MONEY) {
                        $in_take_money_id = $vars['id'];
                    } else if ($vars['name'] == RECEIVING_DATE) {
                        $in_money_date_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_NOTES) {
                        $in_paying_notes_id = $vars['id'];
                    }
                }

                $sql_1['select']  = 'SELECT d.id AS document_id, d.full_num AS full_num, dt.direction AS direction, ' . "\n" .
                                    '  d.active AS active, CONCAT(ci18n.name, " ", ci18n.lastname) AS customer, d.type, ' . "\n" .
                                    '  dti18n.name AS type_name, CONCAT(ei18n.name, " ", ei18n.lastname) as employee_name, ' . "\n" .
                                    '  DATE_FORMAT(d.added, "%Y-%m-%d") AS added, d_cstm_type_income.value AS type_income, ' . "\n" .
                                    '  d_cstm_paying_reason.value AS paying_reason, d_cstm_paying_notes.value AS paying_notes, ' . "\n" .
                                    '  d_cstm_type_paying.value AS type_paying, d_cstm_type_paying_name.label AS type_paying_name, ' . "\n" .
                                    '  d_cstm_paying_value.value AS paying_value, d_cstm_paying_currency.value AS paying_currency, ' . "\n" .
                                    '  d_cstm_take_money.value AS take_money, d_cstm_take_money_name.label AS take_money_name, ' . "\n" .
                                    '  DATE_FORMAT(d_cstm_money_date.value, "%Y-%m-%d") AS money_date' . "\n";

                //from clause
                $sql_1['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ei18n' . "\n" .
                                 '  ON (d.employee=ei18n.parent_id AND ei18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                 '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_income' . "\n" .
                                 '  ON (d.id=d_cstm_type_income.model_id AND d_cstm_type_income.var_id="' . $in_type_income_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_reason' . "\n" .
                                 '  ON (d.id=d_cstm_paying_reason.model_id AND d_cstm_paying_reason.var_id="' . $in_paying_reason_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_paying ' . "\n" .
                                 '  ON (d.id=d_cstm_type_paying.model_id AND d_cstm_type_paying.var_id="' . $in_type_paying_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_type_paying_name' . "\n" .
                                 '  ON (d_cstm_type_paying_name.parent_name="' . PAYING_TYPE . '" AND d_cstm_type_paying_name.option_value=d_cstm_type_paying.value AND d_cstm_type_paying_name.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_value ' . "\n" .
                                 '  ON (d.id=d_cstm_paying_value.model_id AND d_cstm_paying_value.var_id="' . $in_paying_value_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_currency ' . "\n" .
                                 '  ON (d.id=d_cstm_paying_currency.model_id AND d_cstm_paying_currency.var_id="' . $in_paying_currency_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_notes ' . "\n" .
                                 '  ON (d.id=d_cstm_paying_notes.model_id AND d_cstm_paying_notes.var_id="' . $in_paying_notes_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_take_money ' . "\n" .
                                 '  ON (d.id=d_cstm_take_money.model_id AND d_cstm_take_money.var_id="' . $in_take_money_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_take_money_name' . "\n" .
                                 '  ON (d_cstm_take_money_name.parent_name="' . TAKEN_MONEY . '" AND d_cstm_take_money_name.option_value=d_cstm_take_money.value AND d_cstm_take_money_name.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_money_date' . "\n" .
                                 '  ON (d.id=d_cstm_money_date.model_id AND d_cstm_money_date.var_id="' . $in_money_date_id . '")' . "\n";

                // join the table for project cstm only if the filter "damage in insys" is selected
                if (!empty($filters['damage_insys'])) {
                    $sql_1['from'] .= 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_damage' . "\n" .
                                      '  ON (d.project=p_cstm_damage.model_id AND p_cstm_damage.var_id IN (' . implode(',', $in_damage_insys_ids) . ') AND p_cstm_damage.value = "' . $filters['damage_insys'] . '")' . "\n";
                }
                if (!empty($filters['company'])) {
                    $sql_1['from'] .= 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_company' . "\n" .
                                      '  ON (d.project=p_cstm_company.model_id AND p_cstm_company.var_id IN (' . implode(',', $company_vars) . ') AND p_cstm_company.value = "' . $filters['company'] . '")' . "\n";
                }

                // construct where
                $where = array();
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.type="' . DOCUMENT_TYPE_INCOME_ID . '"';
                $where[] = 'd.active!=0';

                if (!empty($filters['from_date']) || !empty($filters['to_date'])) {
                    $sql_dates = array('added' => array(), 'd_cstm_money_date' => array());
                    if (!empty($filters['from_date'])) {
                        $sql_dates['added'][] = 'DATE_FORMAT(d.added, "%Y-%m-%d") >= "' . $filters['from_date'] . '"' .  "\n";
                        $sql_dates['d_cstm_money_date'][] = 'DATE_FORMAT(d_cstm_money_date.value, "%Y-%m-%d") >= "' . $filters['from_date'] . '"' .  "\n";
                    }
                    if (!empty($filters['to_date'])) {
                        $sql_dates['added'][] = 'DATE_FORMAT(d.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '"' .  "\n";
                        $sql_dates['d_cstm_money_date'][] = 'DATE_FORMAT(d_cstm_money_date.value, "%Y-%m-%d") <= "' . $filters['to_date'] . '"' .  "\n";
                    }

                    $sql_date_str = '((' . implode(' AND ', $sql_dates['added']) . ') OR (' . implode(' AND ', $sql_dates['d_cstm_money_date']) . '))';
                    $where[] = $sql_date_str;
                }
                if (!empty($filters['payment_type'])) {
                    $where[] = 'd_cstm_type_paying.value = "' . $filters['payment_type'] . '"';
                }
                if (! empty($filters['employees'])) {
                    $where[] = 'd.employee = "' . $filters['employees'] . '"';
                }
                if (! empty($filters['customers'])) {
                    $where[] = 'd.customer = "' . $filters['customers'] . '"';
                }
                if (! empty($filters['project'])) {
                    $where[] = 'd.project = "' . $filters['project'] . '"';
                }
                if (! empty($filters['currency'])) {
                    $where[] = 'd_cstm_paying_currency.value = "' . $filters['currency'] . '"';
                }
                if (! empty($filters['type_income'])) {
                    $where[] = 'd_cstm_type_income.value = "' . $filters['type_income'] . '"';
                }
                if (! empty($filters['money_given'])) {
                    $where[] = 'd_cstm_take_money.value = "' . $filters['money_given'] . '"';
                }
                if (! empty($filters['reason'])) {
                    $where[] = '(LOWER(d_cstm_paying_reason.value) REGEXP "[[:<:]]' . mb_strtolower($filters['reason'], mb_detect_encoding($filters['reason'])) . '[[:>:]]")';
                }
                if (! empty($filters['notes'])) {
                    $where[] = '(LOWER(d_cstm_paying_notes.value) REGEXP "[[:<:]]' . mb_strtolower($filters['notes'], mb_detect_encoding($filters['notes'])) . '[[:>:]]")';
                }

                $sql_1['where'] = 'WHERE ' . implode(' AND ', $where);

                $sql_1_archived = $sql_1;
                $sql_1['select'] .= ', 0 as archived';
                $sql_1_archived['select'] .= ', 1 as archived';

                $sql_1_archived['from'] = 'FROM ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                           'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                           '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                           '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ei18n' . "\n" .
                                           '  ON (d.employee=ei18n.parent_id AND ei18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                           '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_type_income' . "\n" .
                                           '  ON (d.id=d_cstm_type_income.model_id AND d_cstm_type_income.var_id="' . $in_type_income_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_reason' . "\n" .
                                           '  ON (d.id=d_cstm_paying_reason.model_id AND d_cstm_paying_reason.var_id="' . $in_paying_reason_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_type_paying ' . "\n" .
                                           '  ON (d.id=d_cstm_type_paying.model_id AND d_cstm_type_paying.var_id="' . $in_type_paying_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_type_paying_name' . "\n" .
                                           '  ON (d_cstm_type_paying_name.parent_name="' . PAYING_TYPE . '" AND d_cstm_type_paying_name.option_value=d_cstm_type_paying.value AND d_cstm_type_paying_name.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_value ' . "\n" .
                                           '  ON (d.id=d_cstm_paying_value.model_id AND d_cstm_paying_value.var_id="' . $in_paying_value_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_currency ' . "\n" .
                                           '  ON (d.id=d_cstm_paying_currency.model_id AND d_cstm_paying_currency.var_id="' . $in_paying_currency_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_notes ' . "\n" .
                                           '  ON (d.id=d_cstm_paying_notes.model_id AND d_cstm_paying_notes.var_id="' . $in_paying_notes_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_take_money ' . "\n" .
                                           '  ON (d.id=d_cstm_take_money.model_id AND d_cstm_take_money.var_id="' . $in_take_money_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_take_money_name' . "\n" .
                                           '  ON (d_cstm_take_money_name.parent_name="' . TAKEN_MONEY . '" AND d_cstm_take_money_name.option_value=d_cstm_take_money.value AND d_cstm_take_money_name.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_money_date' . "\n" .
                                           '  ON (d.id=d_cstm_money_date.model_id AND d_cstm_money_date.var_id="' . $in_money_date_id . '")' . "\n";

                // join the table for project cstm only if the filter "damage in insys" is selected
                if (!empty($filters['damage_insys'])) {
                    $sql_1_archived['from'] .= 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_damage' . "\n" .
                                                '  ON (d.project=p_cstm_damage.model_id AND p_cstm_damage.var_id IN (' . implode(',', $in_damage_insys_ids) . ') AND p_cstm_damage.value = "' . $filters['damage_insys'] . '")' . "\n";
                }

                $union_queries = array();

                //search basic details with current lang parameters
                $union_queries[] = implode("\n", $sql_1);
                $union_queries[] = implode("\n", $sql_1_archived);

                $query = '(' . implode(') ' . "\n" . ' UNION ' . "\n" . ' (', $union_queries) . ') ORDER BY full_num ASC';

                $records = $registry['db']->GetAll($query);

                $incomings = $records;
            }

            // expenses
            if (! $document_type_id || $document_type_id==DOCUMENT_TYPE_EXPENSE_ID) {
                $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_EXPENSE_ID . ' AND (fm.name="' . TYPE_EXPENSE . '" OR fm.name="' . PAYING_REASON . '" OR fm.name="' . PAYING_TYPE . '" OR fm.name="' . PAYING_VALUE . '" OR fm.name="' . PAYING_CURRENCY . '" OR fm.name="' . TAKEN_MONEY . '" OR fm.name="' . RECEIVING_DATE . '" OR fm.name="' . PAYING_NOTES . '" OR fm.name="' . CASE_YEAR . '" OR fm.name="' . CASE_NUM . '") ORDER BY fm.position';
                $var_ids_2 = $registry['db']->GetAll($sql_for_add_vars);

                $in_type_expense_id = '';
                $in_paying_reason_id = '';
                $in_type_paying_id = '';
                $in_paying_value_id = '';
                $in_paying_currency_id = '';
                $in_take_money_id = '';
                $in_money_date_id = '';
                $in_paying_notes_id = '';
                $in_case_year_id = '';
                $in_case_num_id = '';

                foreach ($var_ids_2 as $vars) {
                    if ($vars['name'] == TYPE_EXPENSE) {
                        $in_type_expense_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_REASON) {
                        $in_paying_reason_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_TYPE) {
                        $in_type_paying_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_VALUE) {
                        $in_paying_value_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_CURRENCY) {
                        $in_paying_currency_id = $vars['id'];
                    } else if ($vars['name'] == TAKEN_MONEY) {
                        $in_take_money_id = $vars['id'];
                    } else if ($vars['name'] == RECEIVING_DATE) {
                        $in_money_date_id = $vars['id'];
                    } else if ($vars['name'] == PAYING_NOTES) {
                        $in_paying_notes_id = $vars['id'];
                    } else if ($vars['name'] == CASE_YEAR) {
                        $in_case_year_id = $vars['id'];
                    } else if ($vars['name'] == CASE_NUM) {
                        $in_case_num_id = $vars['id'];
                    }
                }

                $sql_2['select']  = 'SELECT d.id AS document_id, d.full_num AS full_num, dt.direction AS direction, ' . "\n" .
                                    '  d.active AS active, CONCAT(ci18n.name, " ", ci18n.lastname) AS customer, d.type, ' . "\n" .
                                    '  dti18n.name AS type_name, CONCAT(ei18n.name, " ", ei18n.lastname) as employee_name, ' . "\n" .
                                    '  DATE_FORMAT(d.added, "%Y-%m-%d") AS added, d_cstm_type_income.value AS type_income, ' . "\n" .
                                    '  d_cstm_paying_reason.value AS paying_reason, d_cstm_paying_notes.value AS paying_notes, ' . "\n" .
                                    '  d_cstm_case_year.value AS case_year, d_cstm_case_num.value AS case_num, ' . "\n" .
                                    '  d_cstm_type_paying.value AS type_paying, d_cstm_type_paying_name.label AS type_paying_name, ' . "\n" .
                                    '  d_cstm_paying_value.value AS paying_value, d_cstm_paying_currency.value AS paying_currency, ' . "\n" .
                                    '  d_cstm_take_money.value AS take_money, d_cstm_take_money_name.label AS take_money_name, ' . "\n" .
                                    '  DATE_FORMAT(d_cstm_money_date.value, "%Y-%m-%d") AS money_date' . "\n";

                //from clause
                $sql_2['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ei18n' . "\n" .
                                 '  ON (d.employee=ei18n.parent_id AND ei18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                 '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_income' . "\n" .
                                 '  ON (d.id=d_cstm_type_income.model_id AND d_cstm_type_income.var_id="' . $in_type_expense_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_reason' . "\n" .
                                 '  ON (d.id=d_cstm_paying_reason.model_id AND d_cstm_paying_reason.var_id="' . $in_paying_reason_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_paying ' . "\n" .
                                 '  ON (d.id=d_cstm_type_paying.model_id AND d_cstm_type_paying.var_id="' . $in_type_paying_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_type_paying_name' . "\n" .
                                 '  ON (d_cstm_type_paying_name.parent_name="' . PAYING_TYPE . '" AND d_cstm_type_paying_name.option_value=d_cstm_type_paying.value AND d_cstm_type_paying_name.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_value ' . "\n" .
                                 '  ON (d.id=d_cstm_paying_value.model_id AND d_cstm_paying_value.var_id="' . $in_paying_value_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_currency ' . "\n" .
                                 '  ON (d.id=d_cstm_paying_currency.model_id AND d_cstm_paying_currency.var_id="' . $in_paying_currency_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_notes ' . "\n" .
                                 '  ON (d.id=d_cstm_paying_notes.model_id AND d_cstm_paying_notes.var_id="' . $in_paying_notes_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_case_year ' . "\n" .
                                 '  ON (d.id=d_cstm_case_year.model_id AND d_cstm_case_year.var_id="' . $in_case_year_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_case_num ' . "\n" .
                                 '  ON (d.id=d_cstm_case_num.model_id AND d_cstm_case_num.var_id="' . $in_case_num_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_take_money ' . "\n" .
                                 '  ON (d.id=d_cstm_take_money.model_id AND d_cstm_take_money.var_id="' . $in_take_money_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_take_money_name' . "\n" .
                                 '  ON (d_cstm_take_money_name.parent_name="' . TAKEN_MONEY . '" AND d_cstm_take_money_name.option_value=d_cstm_take_money.value AND d_cstm_take_money_name.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_money_date' . "\n" .
                                 '  ON (d.id=d_cstm_money_date.model_id AND d_cstm_money_date.var_id="' . $in_money_date_id . '")' . "\n";

                // join the table for project cstm only if the filter "damage in insys" is selected
                if (!empty($filters['damage_insys'])) {
                    $sql_2['from'] .= 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_damage' . "\n" .
                                      '  ON (d.project=p_cstm_damage.model_id AND p_cstm_damage.var_id IN (' . implode(',', $in_damage_insys_ids) . ') AND p_cstm_damage.value = "' . $filters['damage_insys'] . '")' . "\n";
                }
                if (!empty($filters['damage_insys'])) {
                    $sql_2['from'] .= 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_damage' . "\n" .
                                      '  ON (d.project=p_cstm_damage.model_id AND p_cstm_damage.var_id IN (' . implode(',', $in_damage_insys_ids) . ') AND p_cstm_damage.value = "' . $filters['damage_insys'] . '")' . "\n";
                }

                // construct where
                $where = array();
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.type="' . DOCUMENT_TYPE_EXPENSE_ID . '"';
                $where[] = 'd.active!=0';

                if (!empty($filters['from_date']) || !empty($filters['to_date'])) {
                    $sql_dates = array('added' => array(), 'd_cstm_money_date' => array());
                    if (!empty($filters['from_date'])) {
                        $sql_dates['added'][] = 'DATE_FORMAT(d.added, "%Y-%m-%d") >= "' . $filters['from_date'] . '"' .  "\n";
                        $sql_dates['d_cstm_money_date'][] = 'DATE_FORMAT(d_cstm_money_date.value, "%Y-%m-%d") >= "' . $filters['from_date'] . '"' .  "\n";
                    }
                    if (!empty($filters['to_date'])) {
                        $sql_dates['added'][] = 'DATE_FORMAT(d.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '"' .  "\n";
                        $sql_dates['d_cstm_money_date'][] = 'DATE_FORMAT(d_cstm_money_date.value, "%Y-%m-%d") <= "' . $filters['to_date'] . '"' .  "\n";
                    }

                    $sql_date_str = '((' . implode(' AND ', $sql_dates['added']) . ') OR (' . implode(' AND ', $sql_dates['d_cstm_money_date']) . '))';
                    $where[] = $sql_date_str;
                }

                if (!empty($filters['payment_type'])) {
                    $where[] = 'd_cstm_type_paying.value = "' . $filters['payment_type'] . '"';
                }

                if (! empty($filters['employees'])) {
                    $where[] = 'd.employee = "' . $filters['employees'] . '"';
                }

                if (! empty($filters['customers'])) {
                    $where[] = 'd.customer = "' . $filters['customers'] . '"';
                }

                if (! empty($filters['project'])) {
                    $where[] = 'd.project = "' . $filters['project'] . '"';
                }

                if (! empty($filters['currency'])) {
                    $where[] = 'd_cstm_paying_currency.value = "' . $filters['currency'] . '"';
                }

                if (! empty($filters['type_expense'])) {
                    $where[] = 'd_cstm_type_income.value = "' . $filters['type_expense'] . '"';
                }

                if (! empty($filters['money_given'])) {
                    $where[] = 'd_cstm_take_money.value = "' . $filters['money_given'] . '"';
                }
                if (! empty($filters['reason'])) {
                    $where[] = '(LOWER(d_cstm_paying_reason.value) REGEXP "[[:<:]]' . mb_strtolower($filters['reason'], mb_detect_encoding($filters['reason'])) . '[[:>:]]")';
                }
                if (! empty($filters['notes'])) {
                    $where[] = '(LOWER(d_cstm_paying_notes.value) REGEXP "[[:<:]]' . mb_strtolower($filters['notes'], mb_detect_encoding($filters['notes'])) . '[[:>:]]")';
                }
                if (! empty($filters['year'])) {
                    $where[] = 'd_cstm_case_year.value = "' . $filters['year'] . '"';
                }
                if (! empty($filters['case_num'])) {
                    $where[] = 'd_cstm_case_num.value = "' . $filters['case_num'] . '"';
                }

                $sql_2['where'] = 'WHERE ' . implode(' AND ', $where);

                // prepare the archive query
                $sql_2_archived = $sql_2;
                $sql_2['select'] .= ', 0 as archived';
                $sql_2_archived['select'] .= ', 1 as archived';

                $sql_2_archived['from'] = 'FROM ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                           'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                           '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                           '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ei18n' . "\n" .
                                           '  ON (d.employee=ei18n.parent_id AND ei18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                           '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_type_income' . "\n" .
                                           '  ON (d.id=d_cstm_type_income.model_id AND d_cstm_type_income.var_id="' . $in_type_expense_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_reason' . "\n" .
                                           '  ON (d.id=d_cstm_paying_reason.model_id AND d_cstm_paying_reason.var_id="' . $in_paying_reason_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_type_paying ' . "\n" .
                                           '  ON (d.id=d_cstm_type_paying.model_id AND d_cstm_type_paying.var_id="' . $in_type_paying_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_type_paying_name' . "\n" .
                                           '  ON (d_cstm_type_paying_name.parent_name="' . PAYING_TYPE . '" AND d_cstm_type_paying_name.option_value=d_cstm_type_paying.value AND d_cstm_type_paying_name.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_value ' . "\n" .
                                           '  ON (d.id=d_cstm_paying_value.model_id AND d_cstm_paying_value.var_id="' . $in_paying_value_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_currency ' . "\n" .
                                           '  ON (d.id=d_cstm_paying_currency.model_id AND d_cstm_paying_currency.var_id="' . $in_paying_currency_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_paying_notes ' . "\n" .
                                           '  ON (d.id=d_cstm_paying_notes.model_id AND d_cstm_paying_notes.var_id="' . $in_paying_notes_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_case_year ' . "\n" .
                                           '  ON (d.id=d_cstm_case_year.model_id AND d_cstm_case_year.var_id="' . $in_case_year_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_case_num ' . "\n" .
                                           '  ON (d.id=d_cstm_case_num.model_id AND d_cstm_case_num.var_id="' . $in_case_num_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_take_money ' . "\n" .
                                           '  ON (d.id=d_cstm_take_money.model_id AND d_cstm_take_money.var_id="' . $in_take_money_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_take_money_name' . "\n" .
                                           '  ON (d_cstm_take_money_name.parent_name="' . TAKEN_MONEY . '" AND d_cstm_take_money_name.option_value=d_cstm_take_money.value AND d_cstm_take_money_name.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_money_date' . "\n" .
                                           '  ON (d.id=d_cstm_money_date.model_id AND d_cstm_money_date.var_id="' . $in_money_date_id . '")' . "\n";

                // join the table for project cstm only if the filter "damage in insys" is selected
                if (!empty($filters['damage_insys'])) {
                    $sql_2_archived['from'] .= 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_damage' . "\n" .
                                                '  ON (d.project=p_cstm_damage.model_id AND p_cstm_damage.var_id IN (' . implode(',', $in_damage_insys_ids) . ') AND p_cstm_damage.value = "' . $filters['damage_insys'] . '")' . "\n";
                }

                //search basic details with current lang parameters
                $union_queries_2 = array();

                $union_queries_2[] = implode("\n", $sql_2);
                $union_queries_2[] = implode("\n", $sql_2_archived);

                $query = '(' . implode(') ' . "\n" . ' UNION ' . "\n" . ' (', $union_queries_2) . ') ORDER BY full_num ASC';

                $records = $registry['db']->GetAll($query);

                $expenses = $records;
            }

            $records_all = array_merge($incomings, $expenses);

            $money_count_BGN = 0;
            $money_count_EUR = 0;
            $money_bank_count_BGN = 0;
            $money_bank_count_EUR = 0;
            $money_expence_BGN = 0;
            $money_expence_EUR = 0;
            $money_owned_BGN = 0;
            $money_owned_EUR = 0;
            $money_bank_expence_BGN = 0;
            $money_bank_expence_EUR = 0;
            $total_BGN = 0;
            $total_EUR = 0;

            foreach ($records_all as $key => $recs) {
                $var_currency = '';
                if ($recs['type'] == DOCUMENT_TYPE_INCOME_ID) {
                    //incoming
                    if ($recs['type_paying'] == 'bank') {
                        if ($recs['money_date']) {
                            if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                                if (($recs['money_date'] >= $filters['from_date']) && ($recs['money_date'] <= $filters['to_date'])) {
                                    $var_currency = 'money_bank_count_' . $recs['paying_currency'];
                                    $$var_currency += $recs['paying_value'];
                                } else {
                                    $records_all[$key]['color'] = 'yellow';
                                }
                            } else if (!empty($filters['from_date'])) {
                                if ($recs['money_date'] >= $filters['from_date']) {
                                    $var_currency = 'money_bank_count_' . $recs['paying_currency'];
                                    $$var_currency += $recs['paying_value'];
                                } else {
                                    $records_all[$key]['color'] = 'yellow';
                                }
                            } else if (!empty($filters['to_date'])) {
                                if ($recs['money_date'] <= $filters['to_date']) {
                                    $var_currency = 'money_bank_count_' . $recs['paying_currency'];
                                    $$var_currency += $recs['paying_value'];
                                } else {
                                    $records_all[$key]['color'] = 'yellow';
                                }
                            } else {
                                $var_currency = 'money_bank_count_' . $recs['paying_currency'];
                                $$var_currency += floatval($recs['paying_value']);
                            }
                        } else {
                            $records_all[$key]['color'] = 'yellow';
                        }
                    } else {
                        if ($recs['money_date']) {
                            if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                                if (($recs['money_date'] >= $filters['from_date']) && ($recs['money_date'] <= $filters['to_date'])) {
                                    $var_currency = 'money_count_' . $recs['paying_currency'];
                                    if (isset($$var_currency)) {
                                        $$var_currency += $recs['paying_value'];
                                    }
                                } else {
                                    $records_all[$key]['color'] = 'yellow';
                                }
                            } else if (!empty($filters['from_date'])) {
                                if ($recs['money_date'] >= $filters['from_date']) {
                                    $var_currency = 'money_count_' . $recs['paying_currency'];
                                    if (isset($$var_currency)) {
                                        $$var_currency += $recs['paying_value'];
                                    }
                                } else {
                                    $records_all[$key]['color'] = 'yellow';
                                }
                            } else if (!empty($filters['to_date'])) {
                                if ($recs['money_date'] <= $filters['to_date']) {
                                    $var_currency = 'money_count_' . $recs['paying_currency'];
                                    if (isset($$var_currency)) {
                                        $$var_currency += $recs['paying_value'];
                                    }
                                } else {
                                    $records_all[$key]['color'] = 'yellow';
                                }
                            } else {
                                $var_currency = 'money_count_' . $recs['paying_currency'];
                                if (isset($$var_currency)) {
                                    $$var_currency += floatval($recs['paying_value']);
                                }
                            }
                        } else {
                            $records_all[$key]['color'] = 'yellow';
                        }
                    }
                } else {
                    // expenses
                    $records_all[$key]['color'] = 'red';
                    if (!empty($recs['paying_currency'])) {
                        if ($recs['type_paying'] == 'bank') {
                            $var_currency = 'money_bank_expence_' . $recs['paying_currency'];
                        } else {
                            $var_currency = 'money_expence_' . $recs['paying_currency'];
                        }
                        $$var_currency += floatval($recs['paying_value']);
                    }
                }
                if (isset($recs['take_money']) && ($recs['take_money'] == 'no')) {
                   if (!empty($recs['paying_currency'])) {
                        $var_owned_currency = 'money_owned_' . $recs['paying_currency'];
                        $$var_owned_currency += $recs['paying_value'];
                    }
                }
            }

            $total_BGN = ($money_count_BGN + $money_bank_count_BGN);
            $total_EUR = ($money_count_EUR + $money_bank_count_EUR);

            $records_all['sums']['money_count_BGN'] = $money_count_BGN;
            $records_all['sums']['money_count_EUR'] = $money_count_EUR;
            $records_all['sums']['money_bank_count_BGN'] = $money_bank_count_BGN;
            $records_all['sums']['money_bank_count_EUR'] = $money_bank_count_EUR;
            $records_all['sums']['money_expence_BGN'] = $money_expence_BGN;
            $records_all['sums']['money_expence_EUR'] = $money_expence_EUR;
            $records_all['sums']['money_owned_BGN'] = $money_owned_BGN;
            $records_all['sums']['money_owned_EUR'] = $money_owned_EUR;
            $records_all['sums']['money_bank_expence_BGN'] = $money_bank_expence_BGN;
            $records_all['sums']['money_bank_expence_EUR'] = $money_bank_expence_EUR;
            $records_all['sums']['total_BGN'] = $total_BGN;
            $records_all['sums']['total_EUR'] = $total_EUR;

            if (!empty($filters['paginate'])) {
                $results = array($records_all, 0);
            } else {
                //no pagination required return only the models
                $results = $records_all;
            }

            return $results;
        }
    }
?>
