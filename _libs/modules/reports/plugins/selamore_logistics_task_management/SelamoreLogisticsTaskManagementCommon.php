<?php

use Nzoom\I18n\I18n;

class SelamoreLogisticsTaskManagementCommon {
    public Registry $registry;
    public string $reportType;
    public array $settings;
    public array $filters;
    public string $lang;
    private I18n $translator;
    public array $rates = [];
    private array $vars = [];
    public string $ratesKeySeparator = '_';
    public int $precision;
    protected int $detailedPrecision = 6;
    public int $defaultPrecisionValue = 2;
    public string $defaultPrecisionSettingName = 'gt2_total_with_vat';
    private string $hardFilterTradingCompany;
    private string $hardFilterLogisticsSpecialist;
    private array $tradingCompaniesOffices;

    public function __construct(Registry $registry, string $reportType, array $filters) {
        $this->registry = $registry;
        $this->reportType = $reportType;
        $this->filters = $this->processFilters($filters);
        $this->lang = $registry['lang'];
    }

    public function getSettings(): array {
        if (isset($this->settings)) {
            return $this->settings;
        }
        $settings = Reports::getReportSettings($this->registry, $this->reportType);

        $listSettings = [
            'cc_tags_allow_os',
            'sales_condition_allow_os',
            'hard_filter_trading_company_roles',
            'hard_filter_logistics_specialist_roles',
            'hard_filter_point_of_sell',
            'readonly_roles',
            'show_delayed_payment_terms_for_terms_of_purchase',
            'add_tlr_delivery_warehouses',
            'skip_ls_os_substatuses',
        ];
        foreach ($listSettings as $listSetting) {
            if (array_key_exists($listSetting, $settings)) {
                $settings[$listSetting] = preg_split('/\s*,\s*/', $settings[$listSetting]);
            }
        }
        $this->settings = $settings;

        return $this->settings;
    }

    public static function isRoleReadonly(Registry $registry, array $settings): bool
    {
        return (!empty($settings['readonly_roles']) && in_array($registry['currentUser']->get('role'), $settings['readonly_roles']));
    }

    public function getCurrency(): string {
        return Finance_Currencies::getMain($this->registry);
    }

// TODO: Get separate precisions, because it's not proper to use same precision for money. quantity, weight and etc.
    protected function getPrecision(): int {
        if (!isset($this->precision)) {
            $settings = $this->getSettings();
            if (array_key_exists('precision_setting_name', $settings) && $settings['precision_setting_name'] !== '') {
                $precisionSettingName = $settings['precision_setting_name'];
            } else {
                $precisionSettingName = $this->defaultPrecisionSettingName;
            }
            $query = "
                SELECT `value`
                  FROM " . DB_TABLE_SETTINGS . "
                  WHERE `section` = 'precision'
                    AND `name` = '{$precisionSettingName}'";
            $precision = $this->registry['db']->GetOne($query);
            if ($precision === '') {
                $this->precision = $this->defaultPrecisionValue;
            } else {
                $this->precision = (int)$precision;
            }
        }

        return $this->precision;
    }

    protected function warnForInvalidRates() {
        $warnings = [];
        foreach ($this->rates as $rateKey => $rate) {
            if ($rate === 0) {
                $currencies = explode($this->ratesKeySeparator, $rateKey);
                if (!array_key_exists(0, $currencies) || !array_key_exists(1, $currencies)) {
                    $warnings['technical_error'] = $this->translate('error_technical_error_please_contact_nzoom_support');
                    continue;
                }
                $warnings[] = sprintf(
                    $this->translate('no_valid_rate_found'),
                    $currencies[0],
                    $currencies[1],
                    $rate,
                );
            }
        }

        foreach ($warnings as $warning) {
            $this->registry['messages']->setWarning($warning);
        }
    }

    public function getSuppliersLogistics(array $suppliersIds): array {
        $registry = $this->registry;
        $settings = $this->getSettings();
        $filters = $this->filters;
        $suppliersIds = array_filter($suppliersIds);
        if (!$suppliersIds) {
            return [];
        }
        $suppliersIdsList = implode(',', $suppliersIds);
        if (!empty($filters['logistics_specialist'])) {
            $filtersLogisticsSpecialistList = implode(',', $filters['logistics_specialist']);
        }
        $query  = "
            SELECT c.id   AS supplier_id,
                cc.value  AS logistic_id,
                cc1.value AS logistic_name
              FROM " . DB_TABLE_CUSTOMERS . " AS c
              JOIN " . DB_TABLE_FIELDS_META . " AS fm
                ON (c.type = {$settings['cus_type_supplier']}
                  AND fm.model = 'Customer'
                  AND fm.model_type = c.type
                  AND fm.name = 'resp_logistics_id')
              JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc
                ON (cc.model_id = c.id
                  AND cc.var_id = fm.id
                  AND cc.lang = '')
              JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                ON (fm1.model = 'Customer'
                  AND fm1.model_type = c.type
                  AND fm1.name = 'resp_logistics_name')
              JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc1
                ON (cc1.model_id = c.id
                  AND cc1.var_id = fm1.id
                  AND cc1.num = cc.num
                  AND cc1.lang = '{$registry['lang']}')" .
              (!empty($filtersLogisticsSpecialistList) ? "
              JOIN (
                SELECT c1.id AS supplier_id
                  FROM " . DB_TABLE_CUSTOMERS . " AS c1
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm2
                    ON (c1.type = {$settings['cus_type_supplier']}
                      AND fm2.model = 'Customer'
                      AND fm2.model_type = c1.type
                      AND fm2.name = 'resp_logistics_id')
                  JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc2
                    ON (cc2.model_id = c1.id
                      AND cc2.var_id = fm2.id
                      AND cc2.lang = ''
                      AND cc2.value IN ({$filtersLogisticsSpecialistList}))
              ) AS filter
                ON (filter.supplier_id = c.id)" : '') . "
              WHERE c.id IN ({$suppliersIdsList})";
        $suppliersLogisticsRaw = $registry['db']->GetAll($query);
        if (!$suppliersLogisticsRaw) {
            return [];
        }
        $suppliersLogistics = [];
        foreach ($suppliersLogisticsRaw as $sl) {
            $suppliersLogistics[$sl['supplier_id']][$sl['logistic_id']] = [
                'id'   => $sl['logistic_id'],
                'name' => $sl['logistic_name'],
            ];
        }
        return $suppliersLogistics;
    }

    public function getVars(string $model, $modelType): array {
        if (array_key_exists($model, $this->vars) && array_key_exists($modelType, $this->vars[$model])) {
            return $this->vars[$model][$modelType];
        }

        $tblFM = DB_TABLE_FIELDS_META;
        $query = <<<SQL
            SELECT `name`, `id`
              FROM $tblFM
              WHERE `model` = '{$model}'
                AND model_type = {$modelType}
            SQL;
        $vars = $this->registry['db']->GetAssoc($query);
        $this->vars[$model][$modelType] = $vars;
        return $vars;
    }

    protected function getLsOtherDesigners(array $lsIds): array {
        if (!$lsIds) {
            return [];
        }
        $lsIdsList = implode(',', $lsIds);
        $query = "
            SELECT d.id   AS ls_id,
                dc.value  AS designer_id,
                dc1.value AS designer_name
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_FIELDS_META . " AS fm
                ON (fm.model = 'Document'
                  AND fm.model_type = d.type
                  AND fm.name = 'other_designer_id')
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                ON (dc.model_id = d.id
                  AND dc.var_id = fm.id
                  AND dc.lang = ''
                  AND dc.value != '')
              JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                ON (fm1.model = 'Document'
                  AND fm1.model_type = d.type
                  AND fm1.name = 'other_designer_name')
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                ON (dc1.model_id = d.id
                  AND dc1.var_id = fm1.id
                  AND dc1.num = dc.num
                  AND dc1.lang = '{$this->lang}')
              WHERE d.id IN ({$lsIdsList})";
        $lsOtherDesignersRaw = $this->registry['db']->GetAll($query);
        $lsOtherDesigners = [];
        foreach ($lsOtherDesignersRaw as $lsOtherDesigner) {
            $lsOtherDesigners[$lsOtherDesigner['ls_id']][] = [
                'id'   => $lsOtherDesigner['designer_id'],
                'name' => $lsOtherDesigner['designer_name'],
            ];
        }
        return $lsOtherDesigners;
    }

    protected function translate($key) {
        if (!isset($this->translator)) {
            $this->translator = $this->registry['translater'];
            $this->translator->loadFile(PH_MODULES_DIR . "documents/i18n/{$this->lang}/documents.ini");
        }

        return $this->translator->translate($key);
    }

    protected function formatNumber($number): string {
        return number_format(
            (float)$number,
            $this->getPrecision(),
            '.',
            ' '
        );
    }

    protected function getDefaultToolbarSettings() {
        $currentWeek = $this->getWeekAndYear()['week'];
        return [
            [
                'text' => "{$this->translate('report_current_week')}: {$currentWeek}",
                'id' => 'report_current_week',
            ],
            [
                'text'  => 'Search',
                'align' => 'Left',
                // TODO: i18n name
            ],
        ];
    }

    public function getWeekAndYear(string $date = ''): array {
        $dateTime = new DateTime($date);
        return [
            'week' => ltrim($dateTime->format('W'), '0'),
            'year' => $dateTime->format('Y'),
        ];
    }

    public function getAdditionalOptions(): array {
        return [
            'settings' => $this->getSettings(),
            'precision' => $this->getPrecision(),
            'edit_allowed' => (self::isRoleReadonly($this->registry, $this->getSettings()) ? 'disallowed' : 'allowed'),
        ];
    }

    public function getRate($sourceCurrency, $destinationCurrency = null) {
        if (is_null($destinationCurrency)) {
            $destinationCurrency = $this->getCurrency();
        }

        $ratesKeySeparator = $this->ratesKeySeparator;
        $rateKey = "{$sourceCurrency}{$ratesKeySeparator}{$destinationCurrency}";
        if (!array_key_exists($rateKey, $this->rates)) {
            $this->rates[$rateKey] = Finance_Currencies::getRate($this->registry, $sourceCurrency, $destinationCurrency);
        }
        return $this->rates[$rateKey];
    }

    public function formatDate(string $date, string $format = null): string {
        $dateFormatted = '';
        if ($date !== '' && $validDate = Validator::validDate($date)) {
            if (is_null($format)) {
                $format = $this->translate('date_short');
            }
            $dateFormatted = General::strftime(
                $format,
                $validDate
            );
        }

        return $dateFormatted;
    }

    public function hardFilterByTradingCompany(): bool
    {
        $settings = $this->getSettings();
        $role = $this->registry['currentUser']->get('role');
        return (!empty($settings['hard_filter_trading_company_roles']) && in_array($role, $settings['hard_filter_trading_company_roles']));
    }

    public function getHardFilterTradingCompany(): string
    {
        if (!isset($this->hardFilterTradingCompany)) {
            $this->hardFilterTradingCompany = '';
            $employeeId = $this->registry['currentUser']->get('employee');
            if ($employeeId) {
                $employee = Customers::searchOne($this->registry, [
                    'where' => ["c.id = {$employeeId}"]
                ]);
                if ($employee) {
                    $getOldVars = $this->registry->get('get_old_vars');
                    $this->registry->set('get_old_vars', true, true);
                    $employeeVars = $employee->getAssocVars();
                    $this->registry->set('get_old_vars', $getOldVars, true);
                    if (!empty($employeeVars['own_company_id']['value'])) {
                        $this->hardFilterTradingCompany = (string)$employeeVars['own_company_id']['value'];
                    }
                }
            }
        }
        return $this->hardFilterTradingCompany;
    }

    public function hardFilterByPointOfSale(): bool
    {
        $settings = $this->getSettings();
        $role = $this->registry['currentUser']->get('role');
        return (!empty($settings['hard_filter_point_of_sell']) && in_array($role, $settings['hard_filter_point_of_sell']));
    }
    public function getHardFilterPointOfSale(): string
    {
        return $this->registry['currentUser']->get('office');
    }

    public function hardFilterByLogisticsSpecialist(): bool
    {
        $settings = $this->getSettings();
        $role = $this->registry['currentUser']->get('role');
        return (!empty($settings['hard_filter_logistics_specialist_roles']) && in_array($role, $settings['hard_filter_logistics_specialist_roles']));
    }
    private function getHardFilterLogisticsSpecialist(): string
    {
        if (!isset($this->hardFilterLogisticsSpecialist)) {
            $this->hardFilterLogisticsSpecialist = (string)$this->registry['currentUser']->get('employee');
        }
        return $this->hardFilterLogisticsSpecialist;
    }

    private function processFilters(array $filters): array
    {
        if ($this->hardFilterByTradingCompany()) {
            $filters['trading_company'] = [$this->getHardFilterTradingCompany()];
        }

        if ($this->hardFilterByPointOfSale()) {
            $filters['point_of_sale'] = [$this->getHardFilterPointOfSale()];
        }

        if ($this->hardFilterByLogisticsSpecialist()) {
            $filters['logistics_specialist'] = [$this->getHardFilterLogisticsSpecialist()];
        }

        return $filters;
    }

    public function getTradingCompaniesOffices(): array
    {
        if (isset($this->tradingCompaniesOffices)) {
            return $this->tradingCompaniesOffices;
        }

        $settings = $this->getSettings();
        $tbl = [
            'c'  => DB_TABLE_CUSTOMERS,
            'fm' => DB_TABLE_FIELDS_META,
            'cc' => DB_TABLE_CUSTOMERS_CSTM,
        ];
        $sqlHardFilterTradingCompany = '';
        if ($this->hardFilterByTradingCompany()) {
            $sqlHardFilterTradingCompany = "AND c.id = '{$this->getHardFilterTradingCompany()}'";
        }
        $query = <<<SQL
            SELECT c.id  AS trading_company_id,
                cc.value AS office_id
              FROM {$tbl['c']} AS c
              JOIN {$tbl['fm']} AS fm
                ON (c.deleted = '0000-00-00 00:00:00'
                  AND c.deleted_by = 0
                  AND c.type = {$settings['cus_type_trading_company']}
                  {$sqlHardFilterTradingCompany}
                  AND fm.model = 'Customer'
                  AND fm.model_type = c.type
                  AND fm.name = 'trading_company_office')
              JOIN {$tbl['cc']} AS cc
                ON (cc.model_id = c.id
                  AND cc.var_id = fm.id
                  AND cc.lang = '')
            SQL;
        $tcoRaw = $this->registry['db']->GetAll($query);
        $tco = [];
        foreach ($tcoRaw as $to) {
            $tco[$to['trading_company_id']][$to['office_id']] = $to['office_id'];
        }

        return $this->tradingCompaniesOffices = $tco;
    }

    public function getTradingCompaniesOfficesIds(): array
    {
        $tradingCompaniesOfficesIds = [];
        foreach ($this->getTradingCompaniesOffices() as $tradingCompanyOffices) {
            foreach ($tradingCompanyOffices as $tradingCompanyOfficeId) {
                $tradingCompaniesOfficesIds[$tradingCompanyOfficeId] = $tradingCompanyOfficeId;
            }
        }

        return $tradingCompaniesOfficesIds;
    }

    public function getSuppliersAdditionalData(array $suppliersIds): array {
        if (!$suppliersIds) {
            return [];
        }
        $suppliersIdsList = implode(',', $suppliersIds);

        $settings = $this->getSettings();
        $tbl = [
            'c'  => DB_TABLE_CUSTOMERS,
            'ci' => DB_TABLE_CUSTOMERS_I18N,
            'cc' => DB_TABLE_CUSTOMERS_CSTM,
        ];
        $supplierVars = $this->getVars('Customer', $settings['cus_type_supplier']);
        $query = <<<SQL
            SELECT c.id    AS `id`,
                cc.value   AS `loading_point_sequence`,
                cc1.value  AS `loading_point_address_id`,
                ci.address AS `loading_point_address_name`
              FROM {$tbl['c']} AS c
              JOIN {$tbl['cc']} AS cc
                ON (c.id IN ({$suppliersIdsList})
                  AND cc.model_id = c.id
                  AND cc.var_id = {$supplierVars['transport_point_order']}
                  AND cc.num = 1
                  AND cc.lang = '')
              JOIN {$tbl['cc']} AS cc1
                ON (cc1.model_id = c.id
                  AND cc1.var_id = {$supplierVars['transport_point_adress']}
                  AND cc1.lang = '')
              LEFT JOIN {$tbl['ci']} AS ci
                ON (ci.parent_id = cc1.value
                  AND ci.lang = '{$this->lang}')
            SQL;
        $suppliersTransportData = $this->registry['db']->GetAll($query);

        $suppliersAdditionalData = [];
        foreach ($suppliersTransportData as $supplierTransportData) {
            $supplierId = $supplierTransportData['id'];
            if (!array_key_exists($supplierId, $suppliersAdditionalData)) {
                $suppliersAdditionalData[$supplierId] = [
                    'loading_point_sequence'  => $supplierTransportData['loading_point_sequence'],
                    'loading_point_addresses' => []
                ];
            }
            if ($supplierTransportData['loading_point_address_id'] === '') {
                continue;
            }
            $suppliersAdditionalData[$supplierId]['loading_point_addresses'][] = [
                'id'   => $supplierTransportData['loading_point_address_id'],
                'name' => $supplierTransportData['loading_point_address_name']
            ];
        }

        return $suppliersAdditionalData;
    }
}
