<?php

use Nzoom\I18n\I18nService;

class Custom_Report_Filters extends Report_Filters
{
    public Registry $registry;
    function defineFilters(Registry $registry): array {
        $this->registry = $registry;

        $i18nService = I18nService::getInstance($this->registry);

        $reportType = $registry['report_type']['name'];

        // Get report settings
        require_once PH_MODULES_DIR . "reports/plugins/{$reportType}/custom.report.query.php";
        $report = Selamore_Logistics_Task_Management::buildReport($this->registry, $reportType);
        $settings = $report->getSettings();

        // Prepare an array for the filters
        $filters = [];

        // Load report i18n params to JavaScript
        $this->prepareI18nAsJsFilter($registry, $filters);
        $reportI18n = $i18nService->getListArray(
            $registry['lang'],
            'reports',
            [$this->reportName]
        );
        $reportI18nJs = [];
        foreach ($reportI18n as $i18nParam => $i18nValue) {
            $reportI18nJs[] = "i18n['{$i18nParam}'] = '{$i18nValue}';";
        }
        $filters['scripts']['custom_scripts'][0]['src'] .= ("\n" . implode("\n", $reportI18nJs));

        // Load EJ2
        $this->prepareEj2($registry);

        /*
         * FILTERS
         */
        // Report for
        $filters['report_for'] = [
            'name'     => 'report_for',
            'type'     => 'radio',
            'options'  => [
                [
                    'label'        => $this->i18n('filter_report_for_create_orders_to_supplier'),
                    'option_value' => 'create_orders_to_supplier',
                ],
                [
                    'label'        => $this->i18n('filter_report_for_planning_and_informing_supplier'),
                    'option_value' => 'planning_and_informing_supplier',
                ],
                [
                    'label'        => $this->i18n('filter_report_for_tlr'),
                    'option_value' => 'tlr',
                ],
            ],
            'on_change' => 'showHideFilters()',
            'label'    => $this->i18n('filter_report_for'),
        ];

        // Trading company
        if (!$report->hardFilterByTradingCompany()) {
            $this->addVarFilter(
                $filters,
                'trading_company',
                'Document',
                $settings['doc_type_customer_specification'],
                'own_company_name',
                [
                    'readonly' => false,
                    'required' => false,
                    'label'    => $this->i18n('filter_trading_company'),
                    'width'    => '244',
                    'hidden'   => $report->hardFilterByPointOfSale(),
                ],
                true
            );
            $filters['trading_company']['autocomplete']['execute_after'] = 'updateFilterPointOfSale';
        }

        // Point of sale
        if (!$report->hardFilterByPointOfSale()) {
            $filters['point_of_sale'] = [
                'name'    => 'point_of_sale',
                'type'    => 'dropdown',
                'options' => Dropdown::getOffices([$registry]),
                'label'   => $this->i18n('filter_point_of_sale'),
                'hidden'  => false,
            ];
            if ($report->hardFilterByTradingCompany()) {
                $tradingCompaniesOfficesIds = $report->getTradingCompaniesOfficesIds();
                $filters['point_of_sale']['options'] = array_filter($filters['point_of_sale']['options'], function ($option) use ($tradingCompaniesOfficesIds) {
                    return array_key_exists($option['option_value'], $tradingCompaniesOfficesIds);
                });
            }
            if ($report->hardFilterByPointOfSale()) {
                $hardFilterPointOfSale = $report->getHardFilterPointOfSale();
                $filters['point_of_sale']['options'] = array_filter($filters['point_of_sale']['options'], function ($option) use ($hardFilterPointOfSale) {
                    return $option['option_value'] === $hardFilterPointOfSale;
                });
            }
            $filters['point_of_sale'] = $this->makeFilterMultiple($filters['point_of_sale']);
        }

        // Logistics specialist
        if (!$report->hardFilterByLogisticsSpecialist()) {
            $this->addVarFilter(
                $filters,
                'logistics_specialist',
                'Customer',
                $settings['cus_type_supplier'],
                'resp_logistics_name',
                [
                    'readonly' => false,
                    'required' => false,
                    'label'    => $this->i18n('filter_logistics_specialist'),
                    'width'    => '244',
                    'hidden'   => false,
                ],
                true
            );
            if ($report->hardFilterByTradingCompany()) {
                $filters['logistics_specialist']['autocomplete']['filters']['<a__own_company_id>'] = $report->getHardFilterTradingCompany();
            } else {
                $filters['logistics_specialist']['autocomplete']['optional_filters']['<a__own_company_id>'] = '$trading_company';
            }
        }

        // Logistics Specification
        $filters['ls_full_num'] = [
            'name'   => 'ls_full_num',
            'type'   => 'text',
            'label'  => $this->i18n('filter_ls_full_num'),
            'hidden' => false,
        ];
        $filters['ls_full_num'] = $this->makeFilterMultiple($filters['ls_full_num']);

        // Logistics Specification Status
        $filters['ls_status'] = [
            'name'    => 'ls_status',
            'type'    => 'dropdown',
            'options' => Documents_Dropdown::getStatuses([$registry, 'model_types' => [$settings['doc_type_logistics_specification']]]),
            'label'   => $this->i18n('filter_ls_status'),
            'hidden'  => false,
        ];
        $filters['ls_status'] = $this->makeFilterMultiple($filters['ls_status']);

        // Include closed specifications
        $filters['include_closed_specifications'] = [
            'name'    => 'include_closed_specifications',
            'type'    => 'checkbox_group',
            'options' => [
                [
                    'option_value' => '1',
                ],
            ],
            'label'   => $this->i18n('filter_include_closed_specifications'),
            'hidden'  => false,
        ];

        // Period specification (customer/internal)
        $filters['period_specification_from'] = [
            'name'              => 'period_specification_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'period_specification_to',
            'label'             => $this->i18n('filter_period_specification'),
            'hidden'            => true,
        ];
        $filters['period_specification_to'] = [
            'name' => 'period_specification_to'
        ];

        // Specification status
        // $filters['specification_status'] = [
        //     'name'    => 'specification_status',
        //     'type'    => 'dropdown',
        //     'options' => [
        //         [
        //             'label'        => $this->i18n('filter_specification_status_not_processed'),
        //             'option_value' => 'specification_status_not_processed',
        //         ],
        //         [
        //             'label'        => $this->i18n('filter_specification_status_no_documents'),
        //             'option_value' => 'specification_status_no_documents',
        //         ],
        //         [
        //             'label'        => $this->i18n('filter_specification_status_no_payment_from_customer'),
        //             'option_value' => 'specification_status_no_payment_from_customer',
        //         ],
        //         [
        //             'label'        => $this->i18n('filter_specification_status_ready_to_order'),
        //             'option_value' => 'specification_status_ready_to_order',
        //         ],
        //         [
        //             'label'        => $this->i18n('filter_specification_status_released'),
        //             'option_value' => 'specification_status_released',
        //         ],
        //         [
        //             'label'        => $this->i18n('filter_specification_confirmed_from_supplier_pending_payment'),
        //             'option_value' => 'specification_confirmed_from_supplier_pending_payment',
        //         ],
        //     ],
        //     'label'   => $this->i18n('filter_specification_status'),
        // ];
        // $filters['specification_status'] = $this->makeFilterMultiple($filters['specification_status']);

        // Customer
        $query = "
            SELECT `model_type`
              FROM " . DB_TABLE_TYPES_RELATIONS . "
              WHERE `model` = 'Customer'
                AND `relation` = 'autocompleter'
                AND `relate_to_model` = 'Document'
                AND `relate_to_model_type` = '{$settings['doc_type_logistics_specification']}'";
        $logisticsSpecificationCustomerTypes = implode(',', $registry['db']->GetCol($query));
        $filters['customer'] = [
            'name'            => 'customer',
            'type'            => 'custom_filter',
            'actual_type'     => 'autocompleter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'width'           => '244',
            'autocomplete'    => [
                'type'         => 'customers',
                'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                'clear'        => 1,
                'suggestions' => '<name> <lastname>',
                'fill_options' => [
                    '$customer => <id>',
                    '$customer_autocomplete => <name> <lastname>',
                    '$customer_oldvalue => <name> <lastname>',
                ],
                'filters'      => [
                    '<type>' => $logisticsSpecificationCustomerTypes,
                ],
            ],
            'label'           => $this->i18n('filter_customer'),
            'hidden'          => false,
        ];

        // Product category
        $filters['product_category'] = [
            'name'    => 'product_category',
            'type'    => 'dropdown',
            'options' => Dropdown::getArticleCategories([$registry]),
            'label'   => $this->i18n('filter_product_category'),
            'hidden'  => false,
        ];

        // Product in specification
        $this->addVarFilter(
            $filters,
            'product_in_specification',
            'Document',
            $settings['doc_type_logistics_specification'],
            'article_name',
            [
                'readonly' => false,
                'required' => false,
                'label'    => $this->i18n('filter_product_in_specification'),
                'width'    => '244',
                'hidden'   => false,
            ],
            true
        );
        $filters['product_in_specification']['autocomplete']['filters'] = [
            '<type>' => (string)$settings['nom_type_product'],
        ];
        $filters['product_in_specification']['autocomplete']['optional_filters'] = [
            '<category>' => '$product_category',
        ];

        // Supplier type
        $filters['supplier_type'] = [
            'name'    => 'supplier_type',
            'type'    => 'dropdown',
            'options' => [
                [
                    'label' => $this->i18n('filter_supplier_type_products'),
                    'option_value' => '1',
                ],
                [
                    'label' => $this->i18n('filter_supplier_type_electrical_appliances'),
                    'option_value' => '2',
                ],
            ],
            'label'   => $this->i18n('filter_supplier_type'),
            'hidden'  => false,
        ];

        // Supplier
        $this->addVarFilter(
            $filters,
            'supplier',
            'Document',
            $settings['doc_type_logistics_specification'],
            'article_deliverer_name',
            [
                'readonly' => false,
                'required' => false,
                'label'    => $this->i18n('filter_supplier'),
                'width'    => '244',
                'hidden'   => false,
            ],
            true
        );
        $filters['supplier']['autocomplete']['optional_filters']['<a__type_production>'] = '$supplier_type';

        // Period order to supplier
        $filters['period_os_from'] = [
            'name'              => 'period_os_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'period_os_to',
            'label'             => $this->i18n('filter_period_os'),
            'hidden'            => false,
        ];
        $filters['period_os_to'] = [
            'name' => 'period_os_to'
        ];

        // Order to supplier
        $filters['os_full_num'] = [
            'name'   => 'os_full_num',
            'type'   => 'text',
            'label'  => $this->i18n('filter_os_full_num'),
            'hidden' => false,
        ];
        $filters['os_full_num'] = $this->makeFilterMultiple($filters['os_full_num']);

        // Order to Supplier Status
        $filters['os_status'] = [
            'name'    => 'os_status',
            'type'    => 'dropdown',
            'options' => Documents_Dropdown::getStatuses([$registry, 'model_types' => [$settings['doc_type_order_to_supplier']]]),
            'label'   => $this->i18n('filter_os_status'),
            'hidden'  => false,
        ];
        $filters['os_status'] = $this->makeFilterMultiple($filters['os_status']);

        // Include closed orders
        $filters['include_closed_orders'] = [
            'name'    => 'include_closed_orders',
            'type'    => 'checkbox_group',
            'options' => [
                [
                    'option_value' => '1',
                ],
            ],
            'label'   => $this->i18n('filter_include_closed_orders'),
            'hidden'  => false,
        ];

        // Country
        $filters['country'] = [
            'name'    => 'country',
            'type'    => 'dropdown',
            'options' => Dropdown::getCountries([$registry]),
            'label'   => $this->i18n('filter_country'),
            'hidden'  => false,
        ];
        $filters['country'] = $this->makeFilterMultiple($filters['country']);

        // Country
        $filters['allocation_status'] = [
            'name'    => 'allocation_status',
            'type'    => 'dropdown',
            'options' => [
                [
                    'label' => $this->i18n('filter_allocation_status_unallocated_without_confirmed_date'),
                    'option_value' => 'unallocated_without_confirmed_date',
                ],
                [
                    'label' => $this->i18n('filter_allocation_status_unallocated_with_confirmed_date'),
                    'option_value' => 'unallocated_with_confirmed_date',
                ],
                [
                    'label' => $this->i18n('filter_allocation_status_allocated'),
                    'option_value' => 'allocated',
                ],
            ],
            'label'   => $this->i18n('filter_allocation_status'),
            'hidden'  => false,
        ];
        $filters['allocation_status'] = $this->makeFilterMultiple($filters['allocation_status']);

        // Requested week
        $filters['requested_week'] = [
            'name'              => 'requested_week',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/_filter_requested_week.html',
            'additional_filter' => 'requested_year',
            'label'             => $this->i18n('filter_requested_week'),
            'hidden'            => false,
        ];
        $filters['requested_year'] = [
            'name' => 'requested_year'
        ];

        // Confirmed week
        $filters['confirmed_week'] = [
            'name'              => 'confirmed_week',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/_filter_confirmed_week.html',
            'additional_filter' => 'confirmed_year',
            'label'             => $this->i18n('filter_confirmed_week'),
            'hidden'            => false,
        ];
        $filters['confirmed_year'] = [
            'name' => 'confirmed_year'
        ];

        return $filters;
    }

    function processDependentFilters(array $filters): array {
        $unsetFilters = [];
        foreach ($filters as $filterName => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$filterName]['additional_filter'] = $filters[$filter['additional_filter']];
                $unsetFilters[] = $filter['additional_filter'];
            }
        }

        foreach ($unsetFilters as $unsetFilter) {
            unset($filters[$unsetFilter]);
        }

        if (!$this->registry->get('generated_report') && $filters['report_for']['value'] === '') {
            $filters['report_for']['value'] = 'create_orders_to_supplier';
        }

        if ($this->registry->get('generated_report')) {
            $this->registry->push('custom_js', PH_MODULES_URL . "reports/plugins/{$this->registry['report_type']['name']}/javascript/selamoreLogisticsTaskManagement.js");
            switch ($filters['report_for']['value']) {
                case 'create_orders_to_supplier':
                    $this->registry->push('custom_css', PH_MODULES_URL . "reports/plugins/{$this->registry['report_type']['name']}/styles/os.css");
                    $this->registry->push('custom_js', PH_MODULES_URL . "reports/plugins/{$this->registry['report_type']['name']}/javascript/selamoreLogisticsTaskManagementForCreateOS.js");
                    break;
                case 'planning_and_informing_supplier':
                    $this->registry->push('custom_css', PH_MODULES_URL . "reports/plugins/{$this->registry['report_type']['name']}/styles/arp.css");
                    $this->registry->push('custom_js', PH_MODULES_URL . "reports/plugins/{$this->registry['report_type']['name']}/javascript/selamoreLogisticsTaskManagementForCreateARP.js");
                    break;
                case 'tlr':
                    $this->registry->push('custom_css', PH_MODULES_URL . "reports/plugins/{$this->registry['report_type']['name']}/styles/tlr.css");
                    $this->registry->push('custom_js', PH_MODULES_URL . "reports/plugins/{$this->registry['report_type']['name']}/javascript/selamoreLogisticsTaskManagementForCreateTlr.js");
                    break;
            }
        }

        return $filters;
    }

    private function addVarFilter(array &$filters, string $filterName, string $model, string $modelType, string $varName, array $filterReplaceParams = [], bool $multiple = false): void {
        $filter = self::getAdditionalField($this->registry, $model, $modelType, $varName);
        $filter['name'] = $filterName;
        if (array_key_exists('type', $filter) && $filter['type'] === 'autocompleter') {
            $filterAutocompleteFillOptionAutocomplete = '';
            if (array_key_exists('autocomplete', $filter)
                    && is_array($filter['autocomplete'])
                    && array_key_exists('fill_options', $filter['autocomplete'])
                    && is_array($filter['autocomplete']['fill_options'])) {
                foreach ($filter['autocomplete']['fill_options'] as $fillOption) {
                    if (preg_match('/^\$' . $varName . '\s*=>\s*(.+)$/', $fillOption, $matches)) {
                        $filterAutocompleteFillOptionAutocomplete = $matches[1];
                        break;
                    }
                }
            }
            $filter['autocomplete']['fill_options'] = [
                "\${$filterName} => <id>",
                "\${$filterName}_autocomplete => {$filterAutocompleteFillOptionAutocomplete}",
                "\${$filterName}_oldvalue => {$filterAutocompleteFillOptionAutocomplete}",
            ];
        }
        $filter = array_replace($filter, $filterReplaceParams);
        if ($multiple) {
            $filter = $this->makeFilterMultiple($filter);
        }
        $filters[$filterName] = $filter;
    }
}
