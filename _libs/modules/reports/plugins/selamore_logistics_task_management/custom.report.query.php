<?php
class Selamore_Logistics_Task_Management Extends Reports {
    private static Registry $registry;

    public static function buildQuery(Registry $registry, array $filters = []): array {
        // Prepare to collect results
        $results = [];

        self::$registry = $registry;

        $filters = self::processFilters($filters);

        $report = self::buildReport($registry, $filters['report_type'], $filters['report_for'], $filters);
// TODO: rethink the result of buildReport
        if (is_null($report)) {
            $registry['messages']->setError($registry['translater']->translate('error_technical_error_please_contact_nzoom_support'));
            return [$results, 0];
        }

        if ($_SERVER['HTTP_ACCEPT'] !== 'application/json') {
            // Prepare grid
            $results['additional_options'] = $report->getAdditionalOptions();
            return [$results, 0];
        } else {
            // Get results
            $results = $report->getResults();
            header('Content-Type: application/json');
            echo json_encode($results);
            exit;
// TODO: push messages
//             $report->warnForInvalidRates();
        }
    }

    private static function processFilters(array $filters): array {
        foreach ($filters as $filterName => $filterValue) {
            if (is_array($filterValue)) {
                $filters[$filterName] = array_filter($filterValue, function ($v) {
                    return $v !== '';
                });
            }
        }
        $filters['ls_full_num'] = General::slashesEscape($filters['ls_full_num']);
        $filters['os_full_num'] = General::slashesEscape($filters['os_full_num']);
        return $filters;
    }

    private static function getReportClassName(string $reportFor): string {
        switch ($reportFor) {
            case 'create_orders_to_supplier':
                return 'SelamoreLogisticsTaskManagementForCreateOs';
            case 'planning_and_informing_supplier':
                return 'SelamoreLogisticsTaskManagementForCreateArp';
            case 'tlr':
                return 'SelamoreLogisticsTaskManagementForCreateTlr';
            default:
                return 'SelamoreLogisticsTaskManagementCommon';
        }
    }

    public static function buildReport(Registry $registry, string $reportType, string $reportFor = '', array $filters = []): SelamoreLogisticsTaskManagementCommon {
        $reportClassName = self::getReportClassName($reportFor);
        include_once PH_MODULES_DIR . "reports/plugins/{$reportType}/SelamoreLogisticsTaskManagementCommon.php";
        include_once PH_MODULES_DIR . "reports/plugins/{$reportType}/{$reportClassName}.php";
        return new $reportClassName($registry, $reportType, $filters);
    }
}
