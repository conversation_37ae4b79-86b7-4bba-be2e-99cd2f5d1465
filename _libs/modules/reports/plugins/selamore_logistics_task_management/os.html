<style>
  {if !empty($reports_additional_options.settings.color_os_done)}
    .os_done {ldelim}
        background-color: {$reports_additional_options.settings.color_os_done};
    {rdelim}
  {/if}
{*  {if !empty($reports_additional_options.settings.color_os_partially)}*}
{*    .os_partially {ldelim}*}
{*        background-color: {$reports_additional_options.settings.color_os_partially};*}
{*    {rdelim}*}
{*  {/if}*}
  {if !empty($reports_additional_options.settings.color_os_unable)}
    .os_unable {ldelim}
        background-color: {$reports_additional_options.settings.color_os_unable};
    {rdelim}
  {/if}
  {if !empty($reports_additional_options.settings.scroll_vertical_os_screen)}
    .ej2_grid_selamore_logistics_task_management_container {ldelim}
        height: calc(100vh - 80px);
    {rdelim}

    #nz-main-wrapper .ej2_grid_selamore_logistics_task_management_container {ldelim}
        height: calc(100vh - 165px);
    {rdelim}
  {/if}
</style>

<!-- Headers templates -->
<script id="template_th_order_to_supplier" type="text/x-template">
  <span class="e-headertext">{#th_order_to_supplier#}</span>
  <div><input{if ($reports_additional_options.edit_allowed ne 'allowed')} disabled="disabled"{/if} type="checkbox" id="lsr_all" /></div>
</script>
<script id="template_th_requested_week" type="text/x-template">
  <span class="e-headertext" title="{#th_loading_arriving_at_warehouse#}">{#th_requested_week#}</span>
</script>
<script id="template_th_request_date" type="text/x-template">
  <span class="e-headertext" title="{#th_loading_arriving_at_warehouse#}">{#th_request_date_os#}</span>
</script>
<script id="template_th_final_delivery_week" type="text/x-template">
  <span class="e-headertext" title="{#th_as_per_customer_contract#}">{#th_final_delivery_week#}</span>
</script>
<script id="template_th_remaining_weeks" type="text/x-template">
  <span class="e-headertext" title="{#th_from_now_until_delivery#}">{#th_remaining_weeks#}</span>
</script>

<!-- Column fields templates -->
<script id="template_supplier_information" type="text/x-template">
  {literal}${if(supplier_id)}{/literal}
    <div>
      <img src="{$theme->imagesUrl}small/customer.png" width="16" height="16" border="0" alt="" title="" />&nbsp;
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${supplier_id}{/literal}" target="_blank">
        {literal}${supplier_name}{/literal}
      </a>
      {literal}
        ${if(terms_of_delivery_name)}
          &nbsp;(${terms_of_delivery_name})
        ${/if}
      {/literal}
    </div>
  {literal}${/if}{/literal}
  {literal}${if(logistics)}{/literal}
    {literal}${for(logistic of logistics)}{/literal}
      <div>
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${logistic.id}{/literal}" target="_blank">
          {literal}${logistic.name}{/literal}
        </a>
      </div>
    {literal}${/for}{/literal}
  {literal}${/if}{/literal}
  <div>{#supplier_loading_point_sequence#}:&nbsp;
    {literal}${if(supplier_additional_data.loading_point_sequence)}{/literal}
      {literal}${supplier_additional_data.loading_point_sequence}{/literal}
    {literal}${/if}{/literal}
  </div>
</script>
<script id="template_trading_company" type="text/x-template">
  {literal}${if(trading_company_id)}{/literal}
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${trading_company_id}{/literal}" target="_blank">
      {literal}${trading_company_name}{/literal}
    </a>
  {literal}${/if}{/literal}
</script>
<script id="template_logistics_specification" type="text/x-template">
  <img src="{$theme->imagesUrl}documents_{literal}${ls_status}{/literal}.png" width="16" height="16" border="0" alt="" title="{literal}${ls_status_name}${if(ls_substatus_name)}: ${ls_substatus_name}${/if}{/literal}" />&nbsp;
  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${ls_id}{/literal}" target="_blank">
    {literal}${ls_full_num} / ${ls_date_formatted}{/literal}
  </a>
</script>
<script id="template_connected_documents_files" type="text/x-template">
  {literal}${if(cis_id)}{/literal}
    <div>
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${cis_id}{/literal}" target="_blank">
        {literal}${cis_full_num} / ${cis_date_formatted}{/literal}
      </a>
    </div>
  {literal}${/if}{/literal}
  {literal}${if(cc_id)}{/literal}
    <div>
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${cc_id}{/literal}" target="_blank">
        {literal}${cc_full_num} / ${cc_date_formatted}{/literal}
      </a>
    </div>
    {literal}${if(cc_sales_conditions)}{/literal}
      <div>
        <span title="{#th_allow_os_condition#}">({literal}${cc_sales_conditions}{/literal})</span>
      </div>
    {literal}${/if}{/literal}
  {literal}${/if}{/literal}
  {literal}${if(designer_id || other_designers)}{/literal}
    <div>
  {literal}${/if}{/literal}
  {literal}${if(designer_id)}{/literal}
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${designer_id}{/literal}" target="_blank" title="{#designer#}">
      {literal}${designer_name}{/literal}
    </a>
  {literal}${/if}{/literal}
  {literal}${if(other_designers)}{/literal}
    {literal}${for(other_designer of other_designers)}{/literal}
      {literal}${if(other_designerIndex == 0 && designer_id)},&nbsp;${/if}{/literal}
      {literal}${if(other_designerIndex)},&nbsp;${/if}{/literal}
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${other_designer.id}{/literal}" target="_blank" title="{#other_designer#}">
        {literal}${other_designer.name}{/literal}
      </a>
    {literal}${/for}{/literal}
  {literal}${/if}{/literal}
  {literal}${if(designer_id || other_designers)}{/literal}
    <div>
  {literal}${/if}{/literal}
  <div>
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${customer_id}{/literal}" target="_blank" title="{#ls_trading_company#}">
      {literal}${customer_name}{/literal}
    </a>
  </div>
  {literal}${if(cc_customer_id && cc_customer_id !== customer_id)}{/literal}
    <div>
      (<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${cc_customer_id}{/literal}" target="_blank" title="{#cc_trading_company#}">
        {literal}${cc_customer_name}{/literal}
      </a>)
    </div>
  {literal}${/if}{/literal}
  {*literal}${if(is_client_id)}{/literal}
    <div>
      (<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${is_client_id}{/literal}" target="_blank" title="{#is_trading_company#}">
        {literal}${is_client_name}{/literal}
      </a>)
    </div>
  {literal}${/if}{/literal*}
  <div>
    {literal}${if(cs_has_files)}{/literal}
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${cis_id}{/literal}" target="_blank">
        <img src="{$theme->imagesUrl}attachments.png" alt="{#th_customer_specification_files_alt#}" title="{#th_customer_specification_files_title#}" />
      </a>
    {literal}${/if}{/literal}
    {literal}${if(cc_has_files)}{/literal}
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${cc_id}{/literal}" target="_blank">
        <img src="{$theme->imagesUrl}pdf.png" alt="{#th_customer_contract_files_alt#}" title="{#th_customer_contract_files_title#}" />
      </a>
    {literal}${/if}{/literal}
    {literal}${if(payments_search_url)}{/literal}
      <a href="{literal}${payments_search_url}{/literal}" target="_blank">
        <img src="{$theme->imagesUrl}payments.png" alt="{#th_payments#}" title="{#th_payments#}" />
      </a>
    {literal}${/if}{/literal}
  </div>
  <div>
    {literal}${if(allow_os_condition_name)}{/literal}
      <span title="{#th_allow_os_condition#}">({literal}${allow_os_condition_name}{/literal})</span>
    {literal}${/if}{/literal}
    {literal}${if(allow_os_tags_names)}{/literal}
      <span title="{#th_allow_os_tags#}">({literal}${allow_os_tags_names}{/literal})</span>
    {literal}${/if}{/literal}
  </div>
</script>
<script id="template_product_name" type="text/x-template">
  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={literal}${product_id}{/literal}" target="_blank">
    {literal}${product_name}{/literal}
  </a>
</script>
<script id="template_order_to_supplier" type="text/x-template">
  {literal}${if(os_id)}{/literal}
    <img src="{$theme->imagesUrl}documents_{literal}${os_status}{/literal}.png" width="16" height="16" border="0" alt="" title="{literal}${os_status_name}${if(os_substatus_name)}: ${os_substatus_name}${/if}{/literal}" />&nbsp;
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${os_id}{/literal}" target="_blank">
      {literal}${os_order_number}{/literal}
    </a>
  {literal}${else if (show_checkbox)}{/literal}
    <div class="lsr-input-container">
      <input{if ($reports_additional_options.edit_allowed ne 'allowed')} disabled="disabled"{/if} type="checkbox" name="lsr[]" {literal}id="lsr_${ls_gt2_id}" value="${ls_gt2_id}"{/literal} />
    <div>
  {literal}${/if}{/literal}
</script>
<script id="template_requested_week" type="text/x-template">
  {literal}
    ${if(cc_date_week_number && cc_date_year)}
      ${cc_date_week_number} (${cc_date_year})
    ${/if}
  {/literal}
</script>
<script id="template_request_date" type="text/x-template">
  {literal}
    ${if(os_loading_date_formatted && os_loading_date_week_number)}
      ${os_loading_date_formatted} (${os_loading_date_week_number})
    ${/if}
  {/literal}
</script>
<script id="template_final_delivery_week" type="text/x-template">
  {literal}
    ${if(delivery_deadline_week_number && delivery_deadline_year)}
      ${delivery_deadline_week_number} (${delivery_deadline_year})
    ${/if}
  {/literal}
</script>

<div id="loading_date_container"{if ($reports_additional_options.edit_allowed ne 'allowed')} class="hidden"{/if}>
  <label for="loading_date">{#toolbar_request_date_label#}:</label>
  <span class="required">*</span>
  {include file="input_date.html"
    standalone=true
    name=loading_date
    label=#toolbar_request_date_title#
    value=''
    show_calendar_icon=1
    width=73
    height=20
  }
</div>

<table class="legend">
  <caption>{#legend_caption#}</caption>
  <tr>
    <td class="os_done"></td>
    <td>{#legend_os_done#}</td>
  </tr>
{*  <tr>*}
{*    <td class="os_partially"></td>*}
{*    <td>{#legend_os_partially#}</td>*}
{*  </tr>*}
  <tr>
    <td class="os_unable"></td>
    <td>{#legend_os_unable#}</td>
  </tr>
</table>
