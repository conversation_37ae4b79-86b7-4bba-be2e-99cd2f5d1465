:root {
    --background-color-button-save: #00800033;
}

/* Grid */
/*.e-grid {*/
/*    width: fit-content!important;*/
/*}*/

.e-headercell,
.e-rowcell,
.e-summarycell {
    padding: 5px!important;
}
.e-headertext {
    color: black;
    font-weight: bold;
}
#ej2_grid_selamore_logistics_task_management td a img {
    margin-right: 5px;
}
th.confirmed_date,
td.confirmed_date {
    background-color: #EEEEEE!important;
}
#ej2_grid_selamore_logistics_task_management * {
    font-size: 13px!important;
}
#loading_date_formatted {
    float: none;
}
.lsr-input-container {
    text-align: center;
}
.e-toolbar {
    overflow: visible!important;
}
.e-toolbar-items {
    position: sticky;
    left: 0px;
}
#create_orders_to_supplier,
#add_arps_save_confirmed_dates {
    background-color: var(--background-color-button-save);
}
/*td.product_description {*/
/*    overflow: hidden!important;*/
/*    display: -webkit-box!important;*/
/*    -webkit-box-orient: vertical!important;*/
/*    -webkit-line-clamp: 5!important;*/
/*}*/

.legend {
    margin-bottom: 5px;
}
.legend caption {
    text-align: left;
    padding-left: 2px;
}
.legend td:first-child {
    width: 20px;
    padding-left: 0px;
    margin-left: 0px;
}

/*.e-grid td.e-active {*/
/*}*/

span.invoices_total_amount,
span.invoices_total_paid_amount,
span.invoices_for_payment_amount {
    font-weight: bold;
}

.payment_deadline_container {
    width: 120px;
}

.negative_value {
    color: red!important;
    font-weight: bold;
}
input.has_unsaved_changes {
    color: #FF8C00!important;
}
span.has_unsaved_changes {
    border-bottom-color: #FF8C00!important;
}

#report_current_week {
    background-color: transparent;
    cursor: auto;
    font-weight: 500;
}


.missing_records input::placeholder {
  color: #FF0000!important;
  opacity: 1;
}

.missing_records input:-ms-input-placeholder {
  color: #FF0000!important;
  opacity: 1;
}
.missing_records input::-ms-input-placeholder {
  color: #FF0000!important;
  opacity: 1;
}

.row_checkbox {
    margin-right: 3px;
}

.e-summaryrow .e-summarycell {
    font-weight: bold!important;
}

.label_required::before {
  content: "*";
  color: red;
  margin-right: 5px;
}

.label_non_required::before {
  content: "*";
  opacity: 0;
  margin-right: 5px;
}

.e-search-wrapper {
    margin-right: 5px;
}
.e-headertext {
    word-wrap: normal;
}

tr.hidden_filter {
    display: none;
}

tr[id^="table_product_in_specification_"] div[id^="suggestions_"] {
    width: 500px!important;
}

.floatl .floatl {
    width: 244px;
}
