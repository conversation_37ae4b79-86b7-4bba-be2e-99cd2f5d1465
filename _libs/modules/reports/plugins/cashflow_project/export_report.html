<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    {assign var='currencyWithoutVat' value=#reports_th_currency_without_vat#|sprintf:$reports_results.settings.currency}
    <table id="reports_balance" border="1">
      <thead>
        <tr>
          <th colspan="4">{#reports_th_balance_balance#}</th>
        </tr>
        <tr>
          <th>
            {#reports_th_balance_total_expected_income#}<br />
            {$currencyWithoutVat}
          </th>
          <th>
            {#reports_th_balance_total_expected_expense#}<br />
            {$currencyWithoutVat}
          </th>
          <th>
            {#reports_th_balance_total_cash_bank_amount#}<br />
            {$currencyWithoutVat}
          </th>
          <th>
            {#reports_th_balance_balance#}<br />
            {$currencyWithoutVat}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td widt="100" style="mso-number-format: '0\.00';">{$reports_results.balance.total_expected_income|string_format:'%.2f'|default:0}</td>
          <td widt="100" style="mso-number-format: '0\.00';">{$reports_results.balance.total_expected_expense|string_format:'%.2f'|default:0}</td>
          <td widt="100" style="mso-number-format: '0\.00';">{$reports_results.balance.total_cash_bank|string_format:'%.2f'|default:0}</td>
          <td style="mso-number-format: '0\.00';">{$reports_results.balance.balance|string_format:'%.2f'|default:0}</td>
        </tr>
      </tbody>
    </table>

    <br />
    <table id="reports_finished_orders" border="1">
      <thead>
        <tr>
          <th colspan="3">{#reports_th_finished_orders#}</th>
        </tr>
        <tr>
          <th style="vertical-align: middle;">{#reports_th_finished_orders_client#}</th>
          <th>
            {#reports_th_finished_orders_expected_income#}<br />
            {$currencyWithoutVat}
          </th>
          <th>
            {#reports_th_finished_orders_expected_expense#}<br />
            {$currencyWithoutVat}
          </th>
        </tr>
      </thead>
      <tbody>
        {foreach from=$reports_results.finished_orders.customers item='customer_finished_orders'}
          <tr>
            <td widt="250">{$customer_finished_orders.customer_name|escape}</td>
            <td widt="100" style="mso-number-format: '0\.00';">{$customer_finished_orders.expected_income|string_format:'%.2f'|default:0}</td>
            <td widt="100" style="mso-number-format: '0\.00';">{$customer_finished_orders.expected_expense|string_format:'%.2f'|default:0}</td>
          </tr>
        {foreachelse}
          <tr>
            <td style="color: red;" colspan="3">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <th align="right">{#total#}:</th>
          <th align="left" widt="100" style="mso-number-format: '0\.00';">{$reports_results.finished_orders.totals.expected_income|string_format:'%.2f'|default:0}</th>
          <th align="left" widt="100" style="mso-number-format: '0\.00';">{$reports_results.finished_orders.totals.expected_expense|string_format:'%.2f'|default:0}</th>
        </tr>
      </tbody>
    </table>

    <br />
    <table id="reports_opened_orders" border="1">
      <thead>
        <tr>
          <th colspan="3">{#reports_th_opened_orders#}</th>
        </tr>
        <tr>
          <th style="vertical-align: middle;">{#reports_th_opened_orders_client#}</th>
          <th>
            {#reports_th_opened_orders_expected_income#}<br />
            {$currencyWithoutVat}
          </th>
          <th>
            {#reports_th_opened_orders_expected_expense#}<br />
            {$currencyWithoutVat}
          </th>
        </tr>
      </thead>
      <tbody>
        {foreach from=$reports_results.opened_orders.customers item='customer_opened_orders'}
          <tr>
            <td widt="250">{$customer_opened_orders.customer_name|escape}</td>
            <td widt="100" style="mso-number-format: '0\.00';">{$customer_opened_orders.expected_income|string_format:'%.2f'|default:0}</td>
            <td widt="100" style="mso-number-format: '0\.00';">{$customer_opened_orders.expected_expense|string_format:'%.2f'|default:0}</td>
          </tr>
        {foreachelse}
          <tr>
            <td style="color: red;" colspan="3">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <th align="right">{#total#}:</th>
          <th align="left" widt="100" style="mso-number-format: '0\.00';">{$reports_results.opened_orders.totals.expected_income|string_format:'%.2f'|default:0}</th>
          <th align="left" widt="100" style="mso-number-format: '0\.00';">{$reports_results.opened_orders.totals.expected_expense|string_format:'%.2f'|default:0}</th>
        </tr>
      </tbody>
    </table>

    <br />
    <table id="approved_expense_requests" border="1">
      <thead>
        <tr>
          <th colspan="2">{#reports_th_approved_expense_requests#}</th>
        </tr>
        <tr>
          <th style="vertical-align: middle;">{#reports_th_approved_expense_requests_num_data#}</th>
          <th>
            {#reports_th_approved_expense_requests_approved_expense#}<br />
            {$currencyWithoutVat}
          </th>
        </tr>
      </thead>
      <tbody>
        {foreach from=$reports_results.approved_expense_requests.expense_requests key='expense_request_id' item='expense_request'}
          <tr>
            <td>{$expense_request.full_num|escape}/{$expense_request.added|escape|date_format:#date_short#}</td>
            <td widt="100" style="mso-number-format: '0\.00';">{$expense_request.approved_expense|string_format:'%.2f'|default:0}</td>
          </tr>
        {foreachelse}
          <tr>
            <td style="color: red;" colspan="2">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <th align="right">{#total#}:</th>
          <th align="left" widt="100" style="mso-number-format: '0\.00';">{$reports_results.approved_expense.totals.approved_expense|string_format:'%.2f'|default:0}</th>
        </tr>
      </tbody>
    </table>
  </body>
</html>