reports_from_date = From
reports_to_date = To
reports_tenants = Tenants
reports_compare = Compare to
reports_compare_prev_month = previous month
reports_compare_open_date = opening date
reports_unit_type = Unit type
reports_services = Services
reports_services_rent = Rent
reports_services_service_charge = Service charge
reports_show = Show
reports_show_summary = Summary for selected services
reports_show_detailed = Detailed report for selected services
reports_avg_income_month = Average income per month
reports_avg_change_prev_month = Average change compared to
reports_avg_change_open_date = Average change compared to opening date
reports_summary = Summary
reports_detailed = Detailed report
reports_tm = TM
reports_tenant = Tenant
reports_unit = Unit
reports_type = Type
reports_invoice_period = Invoice period
reports_opening_date = Opening date
reports_avg_period = Average for the period
reports_amounts_currency = All amounts are in %s.
reports_tenant_no_data = No data available for invoices for contracts of customer.

reports_recurrence_period_day = day
reports_recurrence_period_week = week
reports_recurrence_period_month = month
reports_recurrence_period_trimester = trimester
reports_recurrence_period_year = year

error_reports_fill_dates = Please enter start and end dates!
error_reports_invalid_period = Start date should be prior to end date!
error_reports_select_tenant = Select at least one tenant!
error_reports_select_compare = Select at least one method of comparison!
error_reports_select_unit_type = Select at least one unit type!
error_reports_select_service = Select at least one service!
error_reports_select_show = Select at least one kind of results to show!
