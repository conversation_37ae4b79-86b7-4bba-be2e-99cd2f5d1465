<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE ARTICLE'S FILTER
            $filter = array (
                'custom_id'         => 'values',
                'name'              => 'values',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'articles',
                'autocomplete_buttons' => 'clear',
                'searchable'        => true,
                'label'             => $this->i18n('reports_article'),
                'help'              => $this->i18n('reports_article'),
                'value'             => ''
            );
            $filters['values'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            return $filters;
        }
    }
?>