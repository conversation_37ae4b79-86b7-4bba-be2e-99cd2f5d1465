/**
 * Get data for the contracts for the selected customer
 *
 * @param object autocomplete settings
 * @param object data autocompleter returned data
 */
function loadSelectedUserDepartments(autocomplete, data) {
    // prepare ajax options
    Effect.Center('loading');
    Effect.Appear('loading');

    var autocomplete_visible_field = $(autocomplete.field);
    var autocomplete_field = $(autocomplete.field.replace(/_autocomplete$/, ''));
    var autocomplete_old_field = $(autocomplete_field.id + '_oldvalue');

    autocomplete_visible_field.value = '';
    autocomplete_field.value = '';
    autocomplete_old_field.value = '';

    if (data['$user_ids']) {
        var user_ids = data['$user_ids'].split(',');
        for (i in user_ids) {
            if (user_ids[i] && $('employees_' + user_ids[i])) {
                $('employees_' + user_ids[i]).checked = true;
            }
        }
    }

    Effect.Fade('loading');
}
