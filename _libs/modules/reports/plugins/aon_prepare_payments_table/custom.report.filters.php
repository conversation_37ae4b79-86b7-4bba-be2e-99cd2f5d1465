<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // Set registry for local usage
            self::$registry = &$registry;

            $report_name = '';
            if ($this->reportName) {
                $report_name = $this->reportName;
            } else {
                $report_name = $registry['report_type']['name'];
            }

            // Prepare array containing description of all filters
            $filters = array();

            // Special filter to contain the scripts for this module
            $filters['scripts'] = array (
                'custom_id'       => 'scripts',
                'name'            => 'scripts',
                'type'            => 'custom_filter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_scripts.html',
                'custom_scripts'  => array(
                    array(
                        'type' => 'external',
                        'src' => PH_MODULES_URL . 'reports/plugins/' . $report_name . '/javascript/custom.js'
                    )
                )
            );

            return $filters;
        }

        function processDependentFilters(&$filters) {
            // Get the registry
            $registry = &self::$registry;

            $registry->set('hide_report_selection', 1, true);

            // Hide the filters panel
            $registry->set('hide_filters_panel', 1, true);

            // Result should be displayed when displaying the report
            $registry->set('generated_report', 1, true);

            return $filters;
        }
    }
?>