  {strip}{capture assign='reas_data'}
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=expenses_reasons&amp;expenses_reasons=view&amp;view={$curr_reason.id}">{if $curr_reason.status eq 'finished'}{if $curr_reason.num}{$curr_reason.num|escape|default:"&nbsp;"}{else}{#reports_no_number#}{/if}{else}{#reports_unfinished_reason#}{/if}</a>/{$curr_reason.issue_date|date_format:#date_short#|escape} ({$curr_reason.type_name|escape|default:"&nbsp;"})
  {/capture}{/strip}
  <div style="white-space: nowrap;">{$reas_data|indent:$curr_reason.level:"--"}</div>
  {foreach from=$curr_reason.relations item=curr_reason}
    {include file=`$templatesDirPlugin``$report_type`/_tree_node.html curr_reason=$curr_reason}
  {/foreach}