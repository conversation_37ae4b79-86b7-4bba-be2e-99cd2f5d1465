<?php
class Custom_Report_Filters extends Report_Filters {

    private static $registry = array();

    /**
     * Defining filters for the certain type report
     */
    function defineFilters(&$registry) {

        // $filters - array containing description of all filters
        $filters = array();
        $date = new DateTime();

        $filters['period_from'] = array(
            'name'              => 'period_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'first_filter_label'=> $this->i18n('reports_filter_from'),
            'additional_filter' => 'period_to',
            'width'             => '65',
            'value'             => $date->format('Y-01-01'),
            'required'          => true
        );
        $filters['period_to'] = array(
            'name'     => 'period_to',
            'type'     => 'date',
            'width'    => '64',
            'value'    => $date->format('Y-12-31'),
            'required' => true
        );

        //get questions
        $query = "
          SELECT fm.name        AS 'option_value',
                 fmi.content  AS 'label' 
            FROM " . DB_TABLE_FIELDS_I18N . " AS fmi
            JOIN " . DB_TABLE_FIELDS_META. " AS fm
                ON (fm.model_type = " . DOCUMENT_TYPE_ID . "
                    AND fm.model = 'Document'
                    AND fm.name LIKE 'poll_question%'
                    AND fm.id = fmi.parent_id
                    AND fmi.lang = '{$registry['lang']}'
                    AND fmi.content_type = 'label')
            ORDER BY cast(fmi.content as unsigned)";
        $questions = $registry['db']->GetAll($query);

        $filters['questions'] = array (
            'name' => 'questions',
            'type' => 'dropdown',
            'label' => $this->i18n('report_questions_dropdown'),
            'options' => $questions,
        );

        //filter show only chart
        $filters['only_chart'] = array(
            'name' => 'only_chart',
            'type' => 'checkbox_group',
            'label' => $this->i18n('report_show_only_chart'),
            'options' => array(
                array(
                    'label' => '',
                    'option_value' => '1'
                )
            )
        );
        return $filters;
    }
    public function processDependentFilters($filters) {
        $unset_filters = array();
        $date = new DateTime();
        if(empty($filters['period_from']['value'])) {
            $filters['period_from']['value'] = $date->format('Y-01-01');
        }
        if(empty($filters['period_to']['value'])) {
            $filters['period_to']['value'] = $date->format('Y-12-31');
        }
        foreach ($filters as $name => $filter) {
            if (isset($filter['additional_filter'])) {
                if (!empty($filter['additional_filter'])) {
                    if (isset($filters[$filter['additional_filter']])) {
                        $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                        unset($filters[$filter['additional_filter']]);
                    }
                }
            }
        }
        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }
        return $filters;
    }

}

?>
