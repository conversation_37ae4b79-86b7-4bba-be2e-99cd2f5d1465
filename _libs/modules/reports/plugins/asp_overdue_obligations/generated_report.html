{if $reports_results}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="margin-bottom: 10px;">
    <tr class="reports_title_row hcenter">
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_client#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_responsibles#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_contract#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_invoice#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_deadline#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_services#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_type_service_lbl#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_service#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_service_value#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;" rowspan="2"><div style="">{#reports_invoice_value#|escape}</div></td>
      <td style="vertical-align: middle;" colspan="4"><div style="">{#reports_overdue_in_days#|escape}</div></td>
    </tr>
    <tr class="reports_title_row hcenter">
      <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_overdue_in_days_0#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_overdue_in_days_30#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_overdue_in_days_60#|escape}</div></td>
      <td style="vertical-align: middle;"><div style="">{#reports_overdue_in_days_90#|escape}</div></td>
    </tr>
    {foreach from=$reports_results item=result}
      {capture assign='current_row_class'}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
      {foreach from=$result.contracts item=contract name=con}
        {foreach from=$contract.invoices item=invoice name=inv}
          {foreach from=$invoice.gt2_rows item=gt2_row name=gt2r}
            <tr class="{$current_row_class}">
              {if $smarty.foreach.con.first && $smarty.foreach.inv.first && $smarty.foreach.gt2r.first}
                <td class="t_border vmiddle" rowspan="{$result.rowspan}">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.id}">{$result.name|escape|default:"&nbsp;"}</a>
                </td>
                <td class="t_border vmiddle" rowspan="{$result.rowspan}">
                  {$result.assigned|escape|default:"&nbsp;"}
                </td>
              {/if}
              {if $smarty.foreach.gt2r.first && $smarty.foreach.inv.first}
                <td class="t_border vmiddle" rowspan="{$contract.rowspan}">
                  {if $contract.id}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$contract.id}">{$contract.type|escape|default:"&nbsp;"} / {$contract.num|escape|default:"&nbsp;"}</a>
                  {else}
                    &nbsp;
                  {/if}
                </td>
              {/if}
              {if $smarty.foreach.gt2r.first}
                <td class="t_border vmiddle" rowspan="{$invoice.rowspan}">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=incomes_reasons&amp;incomes_reasons=view&amp;view={$invoice.id}">{$invoice.num|escape|default:"&nbsp;"}</a>
                </td>
                <td class="t_border vmiddle" rowspan="{$invoice.rowspan}">
                  {$invoice.deadline|date_format:#date_short#|escape}
                </td>
              {/if}
              <td class="t_border vmiddle">
                {$gt2_row.article_gruop|escape|default:"&nbsp;"}
              </td>
              <td class="t_border vmiddle">
                {$gt2_row.article_kind|escape|default:"&nbsp;"}
              </td>
              <td class="t_border vmiddle">
                {$gt2_row.article_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border vmiddle hright">
                {$gt2_row.subtotal|string_format:"%.2f"|escape|default:"0.00"}
              </td>
              {if $smarty.foreach.gt2r.first}
                <td class="t_border vmiddle hright" rowspan="{$invoice.rowspan}">
                  {$invoice.total_with_vat|string_format:"%.2f"|escape|default:"0.00"}
                </td>
                {foreach from=$invoice.overdue item=overdue name=ovd}
                  <td class="{if !$smarty.foreach.ovd.last}t_border {/if}vmiddle hcenter" rowspan="{$invoice.rowspan}">
                    {if $overdue}
                      <img src="{$theme->imagesUrl}small/check_yes.png" width="12" height="12" border="0" alt="" title="" />
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                {/foreach}
              {/if}
            </tr>
          {/foreach}
        {/foreach}
      {/foreach}
    {/foreach}

    <tr class="row_blue">
      <td class="t_border hright" colspan="9"><strong>{#reports_total#}</strong></td>
      <td class="hright t_border"><strong>{$reports_additional_options.total|string_format:"%.2f"|escape|default:"0.00"}</strong></td>
      <td colspan="4">&nbsp;</td>
    </tr>
    <tr>
      <td class="t_footer" colspan="14"></td>
    </tr>
  </table>
{else}
  <h1>{#error_reports_no_results_to_show#}</h1>
{/if}
