<table><tr><td>
<table class="reports_table">
  <tr class="reports_title_row">
    <th>{#reports_customer#|escape}</th>
    <th>{#reports_document_num#|escape}</th>
    <th>{#reports_document_type#|escape}</th>
    <th>{#reports_date#|escape}</th>
    <th>{#reports_due_date#|escape}</th>
    <th>{#reports_days_overdue#|escape}</th>
    <th>{#reports_amount#|escape}</th>
    <th>{#reports_currency#|escape} </th>
    <th>{#reports_due_amount#|escape} ({$report_filters.currency.value|escape})</th>
    <th>{#reports_preview#|escape}</th>
    <th>{#reports_recipient#|escape}</th>
    <th>{#reports_last_sent_emails#|escape}</th>
    <th style="white-space: nowrap; width: 60px;">
      {counter name='total_customers' assign='total_customers' start=0}
      {foreach from=$reports_results item='result'}
        {if $result.rowspan && $result.recipients}{counter name='total_customers' assign='total_customers'}{/if}
      {/foreach}
      {assign var='selected_items' value=$smarty.session.selected_items[$pagination.session_param]}
      {assign var='first_x' value=0}
      {if count($reports_results) > $reports_additional_options.max_recipients}
        {assign var='first_x' value=$reports_additional_options.max_recipients}
      {/if}
      {capture assign='selected_recipients_session_param'}{$session_param|default:$pagination.session_param}_recipients{/capture}
      {assign var='selected_recipients' value=$smarty.session.selected_items.$selected_recipients_session_param.ids}
      {capture assign='toggle_customer_onclick'}toggleCustomerSelected(this, '{$selected_recipients_session_param}');{/capture}
      {include file="`$theme->templatesDir`_select_items.html"
               pages=1
               first_x=$first_x
               total=$total_customers
               session_param=$session_param|default:$pagination.session_param
               onclick=$toggle_customer_onclick
      }
      <span id="selectedItemsCount_1" class="selected_items_span">
        {assign var='selected_items_count' value='0'}
        {if $selected_items.ids}
          {capture assign='selected_items_count'}{if is_array($selected_items.ids)}{$selected_items.ids|@count}{else}0{/if}{/capture}
        {elseif $selected_items.select_all}
          {capture assign='temp_var'}{if is_array($selected_items.ignore_ids)}{$selected_items.ignore_ids|@count}{else}0{/if}{/capture}
          {math equation="all - ignored" all=$total_customers ignored=$temp_var assign='selected_items_count'}
        {/if}
        {$selected_items_count}
      </span>
    </th>
  </tr>
  {foreach from=$reports_results item='result' key='rk'}
  {if $result.rowspan}{cycle values='t_odd1 t_odd2,t_even1 t_even2' name='customers' assign='current_class'}{/if}
  <tr class="{$current_class}">
    {if $result.rowspan}
    <td rowspan="{$result.rowspan}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.customer}" target="_blank">{$result.customer_name|escape|default:"&nbsp;"}</a></td>
    {/if}
    <td><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=incomes_reasons&amp;incomes_reasons=view&amp;view={$rk}" target="_blank">{$result.num|escape|default:"&nbsp;"}</a></td>
    <td>{$report_filters.types.options[$result.type].label}</td>
    <td>{$result.issue_date|date_format:#date_short#}</td>
    <td>{$result.date_of_payment|date_format:#date_short#}</td>
    <td class="hright strong{if $result.days_overdue gt 15 && $result.days_overdue lte 45} heavy{elseif $result.days_overdue gt 45} veryheavy{/if}">{$result.days_overdue|default:"&nbsp;"}</td>
    <td class="hright">{$result.total_with_vat}</td>
    <td>{$result.currency|escape}</td>
    <td class="hright">{$result.total_remaining_amount|string_format:"%.2F"}</td>
    <td class="hcenter">{if $result.file_id}{capture assign='file_exists'}{if file_exists($result.path)}1{else}0{/if}{/capture}{if $file_exists}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=incomes_reasons&amp;incomes_reasons=viewfile&amp;viewfile={$rk}&amp;file={$result.file_id}" target="_blank">{/if}<img src="{$theme->imagesUrl}pdf.png" alt="{$result.filename|escape}" title="{$result.filename|escape}" class="{if $file_exists}pointer{else}dimmed{/if}" />{if $file_exists}</a>{/if}{/if}</td>
    {if $result.rowspan}
    {capture assign='customer_selected'}{if @in_array($result.customer, $selected_items.ids) ||
                 (@$selected_items.select_all eq 1 && @!in_array($result.customer, $selected_items.ignore_ids))}1{else}0{/if}{/capture}
    <td rowspan="{$result.rowspan}">
      {foreach from=$result.recipients item='recipient' key='rec_id' name='ri'}
        <input type="checkbox" name="recipient[{$result.customer}][]" id="recipient_{$result.customer}_{$smarty.foreach.ri.iteration}" value="{$rec_id|escape}" {if !empty($selected_recipients) && in_array($rec_id, $selected_recipients)}checked="checked"{/if} class="items_{$result.customer} recipient" onclick="{$toggle_customer_onclick}" title="{#check_to_include#|escape}" /> <label for="recipient_{$result.customer}_{$smarty.foreach.ri.iteration}" title="{$recipient.email|escape}">{$recipient.name|escape}</label><br />
      {foreachelse}
        &nbsp;
      {/foreach}
    </td>
    <td rowspan="{$result.rowspan}">
      {foreach from=$result.last_emails item='email_date' key='doc_id'}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$doc_id}" target="_blank">{$email_date|escape}</a><br />
      {foreachelse}
        &nbsp;
      {/foreach}
    </td>
    <td class="hcenter" rowspan="{$result.rowspan}">
      {if $result.recipients}
      <input onclick="sendIds(params = {ldelim}
                                        the_element: this,
                                        module: '{$module}',
                                        controller: 'reports',
                                        action: '{$action}',
                                        session_param: '{$session_param|default:$pagination.session_param}',
                                        total: {$total_customers}
                                       {rdelim}); {$toggle_customer_onclick}"
             type="checkbox"
             name='items[]'
             id='items_{$result.customer}'
             value="{$result.customer}"
             title="{#check_to_include#|escape}"
             {if $customer_selected}
               checked="checked"
             {/if} />
      {else}
        &nbsp;
      {/if}
    </td>
    {/if}
  </tr>
  {foreachelse}
  <tr class="t_odd1 t_odd2">
    <td class="error" colspan="13">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
  </table>
  </td>
  </tr>
  {if $reports_additional_options.paginate}
  <tr>
    <td class="pagemenu">
      {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
      {include file="`$theme->templatesDir`pagination.html"
      found=$pagination.found
      total=$pagination.total
      rpp=$pagination.rpp
      page=$pagination.page
      pages=$pagination.pages
      hide_selection_stats=1
      link=$link
      }
    </td>
  </tr>
  {/if}
</table>
<br />
<table class="t_layout_table t_table1{if !$selected_items_count} hidden{/if}" id="send_table" style="width: 426px; border-spacing: 0;">
  <tr class="reports_title_row">
    <th colspan="3">{#reports_send_emails#|escape}</th>
  </tr>
  {if !$reports_additional_options.patterns || !$reports_additional_options.email_templates}
  <tr>
    <td colspan="3" class="error">{#error_reports_missing_patterns#|escape}</td>
  </tr>
  {/if}
  {include file=`$theme->templatesDir`input_dropdown.html
           name=attached_files
           required=1
           options=$reports_additional_options.patterns
           label=#reports_attachment_generated#
  }
  {include file=`$theme->templatesDir`input_dropdown.html
           name=email_template
           options=$reports_additional_options.email_templates
           required=1
           label=#reports_select_email_pattern#
           onchange="$('email_subject').value = this.options[this.options.selectedIndex].innerHTML;"
  }
  {include file=`$theme->templatesDir`input_text.html
           name=email_subject
           value=$reports_additional_options.email_templates.0.label|default:''
           label=#reports_select_email_subject#
  }
  {if $reports_additional_options.patterns && $reports_additional_options.email_templates}
  <tr>
    <td colspan="3">
      <span class="floatr">
        {array assign='btn_src' onclick="return checkMaxRecipients('`$reports_additional_options.max_recipients`', '`$smarty.config.error_max_recipients`') && confirmation(this, 'reports', 'send_as_mail', '', '', '', '', '&report_type=`$report_type`')"}
        {include file=`$theme->templatesDir`input_button.html
                 name=sendButton
                 source=$btn_src
                 standalone=true
                 label=#reports_send#|default:#send#
        }
      </span>
    </td>
  </tr>
  {/if}
</table>
<br />
