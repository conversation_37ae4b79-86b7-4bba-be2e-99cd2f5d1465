<?php

Class Bulgariaplast_Transport_Schedule Extends Reports {
    public static $registry;

    public static function buildQuery(&$registry, $filters = array()) {
        self::$registry = $registry;
        $final_results = array(
            'disable_edit' => false
        );

        if (empty($filters['delivery_date'])) {
            $registry['messages']->setError($registry['translater']->translate('reports_error_complete_required'));
            return self::returnResults($filters, $final_results);
        }

        if (!empty($filters['show_inserted_in_transport_lists'])) {
            $filters += array('transport_list_search' => true);
            $final_results['disable_edit'] = true;
        }
        $results = self::getTransportRequests($filters);
        $vehicles = self::getVehicles();

        // complete the vehicles
        usort($results, function ($a, $b) {
            if ($a['delivery_date'] == $b['delivery_date']) {
                return 0;
            }

            return (!empty($a['delivery_date']) ? 1 : -1);
        });

        // get the quantity and capacity, already inserted in transport list
        $transport_list_inserted = $results;
        if (empty($filters['show_inserted_in_transport_lists'])) {
            $transport_list_inserted =  self::getTransportRequests($filters + array('transport_list_search' => true));
        }

        foreach ($transport_list_inserted as $trans_incl) {
            if (isset($vehicles[$trans_incl['vehicle']])) {
                $vehicles[$trans_incl['vehicle']]['visible'] = 1;
                $vehicles[$trans_incl['vehicle']]['taken_capacity'] += floatval($trans_incl['req_num']);
                $vehicles[$trans_incl['vehicle']]['taken_volume'] += floatval($trans_incl['meters']);
                $vehicles[$trans_incl['vehicle']]['pretaken_capacity'] += floatval($trans_incl['req_num']);
                $vehicles[$trans_incl['vehicle']]['pretaken_volume'] += floatval($trans_incl['meters']);
            }
        }

        foreach ($results as $res_k => $fin_res) {
            $vehicle_name_code = '';
            if (isset($vehicles[$fin_res['vehicle']])) {
                $vehicles[$fin_res['vehicle']]['visible'] = 1;
                $vehicles[$fin_res['vehicle']]['taken_capacity'] += floatval($fin_res['req_num']);
                $vehicles[$fin_res['vehicle']]['taken_volume'] += floatval($fin_res['meters']);
                $vehicle_name_code = sprintf('[%s] %s', $vehicles[$fin_res['vehicle']]['code'], $vehicles[$fin_res['vehicle']]['name']);
            }
            $results[$res_k]['vehicle_name_code'] = $vehicle_name_code;
        }

        foreach ($vehicles as $veh_key => $veh) {
            if (floatval($vehicles[$veh_key]['taken_capacity']) > floatval($vehicles[$veh_key]['capacity']) ||
                floatval($vehicles[$veh_key]['taken_volume']) > floatval($vehicles[$veh_key]['volume'])) {
                $vehicles[$veh_key]['class'] = 'overloaded';
            }
        }

        // get the quantity and capacity, already inserted in transport list
        $results = self::reorderResultsByParentOrder($results);

        $final_results['results'] = $results;
        $final_results['vehicles'] = $vehicles;

        $final_results['vehicles_autocompleter'] = array(
            'type'          => 'nomeclatures',
            'clear'         => 1,
            'suggestions'   => '[<code>] <name>',
            'buttons_hide'  => 'search',
            'fill_options'  => array('$request_vehicle_autocomplete => [<code>] <name>',
                                     '$request_vehicle => <id>',
                                     '$request_driver_autocomplete => <a__' . DOC_VAR_DRIVER_NAME . '>',
                                     '$request_driver => <a__' . DOC_VAR_DRIVER . '>'),
            'execute_after' => 'recalculateVehicleCapacity',
            'search'        => array('<name>', '<code>'),
            'sort'          => array('<name>'),
            'filters'       => array('<type>' => strval(NOM_TYPE_VEHICLE)),
            'url'           => sprintf('%s?%s=%s&%s=%s',
                $_SERVER['PHP_SELF'],
                self::$registry['module_param'], 'nomenclatures',
                'nomenclatures', 'ajax_select')
        );

        $final_results['drivers_autocompleter'] = array(
            'type'         => 'customers',
            'clear'        => 1,
            'suggestions'  => '<name> <lastname>',
            'buttons_hide' => 'search',
            'fill_options' => array('$request_driver_autocomplete => <name> <lastname>',
                                    '$request_driver => <id>'),
            'search'  => array('<name>'),
            'sort'    => array('<name>'),
            'filters' => array('<type>' => strval(CUSTOMER_TYPE_DRIVER)),
            'url'     => sprintf('%s?%s=%s&%s=%s',
                $_SERVER['PHP_SELF'],
                self::$registry['module_param'], 'customers',
                'customers', 'ajax_select')
        );

        self::$registry->set('hide_export_button', true, true);

        return self::returnResults($filters, $final_results);
    }

    /*
     * Function that will get the main results
     *
     * @param array $filters
     * @return array
     */
    public static function getTransportRequests($filters) {
        $stops_list = array();

        // GET CITIES
        // get needed vars
        $doc_vars = self::getAssocVarsList('Document', DOC_TYPE_TRANSPORT_REQUEST, array(DOC_VAR_DELIVERY_DATE, DOC_VAR_DESTINATION, DOC_VAR_REQ_PAYMENT_AMOUNT,
                                                                                         DOC_VAR_CITY, DOC_VAR_ORDER_NUM, DOC_VAR_VEHICLE,
                                                                                         DOC_VAR_DRIVER, DOC_VAR_COMMENTS, DOC_VAR_NOTES,
                                                                                         DOC_VAR_PANEL_FILE, DOC_VAR_MODULE, DOC_VAR_PRODUCTION_ARTICLE,
                                                                                         DOC_VAR_REQ_NUM, DOC_VAR_REQ_METERS, DOC_VAR_REQ_TRANSPORT_LIST,
                                                                                         DOC_VAR_ARTICLE_COLOR, DOC_VAR_MODULE_GLAZING, DOC_VAR_ACCESSORIES,
                                                                                         DOC_VAR_HANDLE, DOC_VAR_CARTRIDGE, DOC_VAR_HANDLE_DESCRIPTION, DOC_VAR_CARTRIDGE_DESCRIPTION));

        // presort
        $sql = array(
            'select' => '',
            'from'   => '',
            'where'  => ''
        );
        $where = array();
        $sql['select'] = 'SELECT d.id as id';
        $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n";
        $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_transport_list' . "\n" .
                        ' ON (doc_transport_list.model_id=d.id AND doc_transport_list.var_id="' . (isset($doc_vars[DOC_VAR_REQ_TRANSPORT_LIST]) ? $doc_vars[DOC_VAR_REQ_TRANSPORT_LIST]: '') . '" AND ' . (empty($filters['transport_list_search']) ? '(doc_transport_list.value="" OR doc_transport_list.value="0" OR doc_transport_list.value IS NULL)' : '(doc_transport_list.value!="" AND doc_transport_list.value IS NOT NULL AND doc_transport_list.value!="0")') . ')' . "\n";
        if (empty($filters['transport_list_search'])) {
            $where[] = '(doc_transport_list.value="" OR doc_transport_list.value="0" OR doc_transport_list.value IS NULL)';
        } else {
            $where[] = '(doc_transport_list.value!="" AND doc_transport_list.value IS NOT NULL AND doc_transport_list.value!="0")';
        }

        $where[] = 'd.active=1';
        $where[] = 'd.deleted_by=0';
        $where[] = 'd.type="' . DOC_TYPE_TRANSPORT_REQUEST . '"';

        if (!empty($filters['customer'])) {
            $where[] = 'd.customer="' . $filters['customer'] . '"';
        }
        if (!empty($filters['from_date'])) {
            $where[] = 'd.date>="' . $filters['from_date'] . '"';
        }
        if (!empty($filters['to_date'])) {
            $where[] = 'd.date<="' . $filters['to_date'] . '"';
        }
        if (!empty($filters['offices'])) {
            $where[] = 'd.office IN ("' . implode('","', $filters['offices']) . '")';
        }
        if (!empty($filters['delivery_date'])) {
            $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date' . "\n" .
                            ' ON (d_cstm_date.model_id=d.id AND d_cstm_date.var_id="' . (isset($doc_vars[DOC_VAR_DELIVERY_DATE]) ? $doc_vars[DOC_VAR_DELIVERY_DATE]: '') . '" AND (DATE_FORMAT(d_cstm_date.value, "%Y-%m-%d")="' . $filters['delivery_date'] . '" OR d_cstm_date.value=""))' . "\n";
        }
        if (!empty($filters['delivery_id'])) {
            $where[] = 'd.id IN ("' . implode('","', $filters['delivery_id']) . '")';
        }
        if (!empty($filters['status'])) {
            if (preg_match('#^substatus_([0-9]*)$#', $filters['status'])) {
                $where[] = 'd.substatus="' . preg_replace('#^substatus_([0-9]*)$#', '$1', $filters['status']) . '"';
            } else {
                $where[] = 'd.status="' . $filters['status'] . '"';
            }
        }

        // ignore substatuses
        $ignore_substatuses = array_filter(preg_split('/\s*,\s*/', IGNORE_SUBSTATUSES));
        if (!empty($ignore_substatuses)) {
            $where[] = 'd.substatus NOT IN ("' . implode('","', $ignore_substatuses) . '")';
        }

        // apply filters
        if (!empty($filters['order_num'])) {
            $order_nums = array_filter($filters['order_num']);
            $order_nums_clauses = array();
            foreach ($order_nums as $ord_num) {
                $order_nums_clauses[] = 'd_cstm_order_num.value LIKE "%' . $ord_num . '%"';
            }
            if (!empty($order_nums_clauses)) {
                $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_order_num' . "\n" .
                                ' ON (d_cstm_order_num.model_id=d.id AND d_cstm_order_num.var_id="' . (isset($doc_vars[DOC_VAR_ORDER_NUM]) ? $doc_vars[DOC_VAR_ORDER_NUM]: '') . '" AND (' . implode(' OR ', $order_nums_clauses) . '))' . "\n";
            }
        }
        if (!empty($filters['custom_num'])) {
            $custom_nums = array_filter($filters['custom_num']);
            $custom_nums_clauses = array();
            foreach ($custom_nums as $cstm_num) {
                $custom_nums_clauses[] = 'd.custom_num LIKE "%' . $cstm_num . '%"';
            }
            if (!empty($custom_nums_clauses)) {
                $where[] = '(' . implode(' OR ', $custom_nums_clauses) . ')';
            }
        }
        if (!empty($filters['vehicle']) || !empty($filters['show_no_vehicle_requests_only'])) {
            $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_vehicle' . "\n" .
                            ' ON (doc_vehicle.model_id=d.id AND doc_vehicle.var_id="' . (isset($doc_vars[DOC_VAR_VEHICLE]) ? $doc_vars[DOC_VAR_VEHICLE]: '') . '"' . (!empty($filters['vehicle']) ? ' AND doc_vehicle.value="' . $filters['vehicle'] . '"' : '') . (!empty($filters['show_no_vehicle_requests_only']) ? ' AND (doc_vehicle.value="" OR doc_vehicle.value IS NULL OR doc_vehicle.value="0")' : '') . ')' . "\n";
        }
        if (!empty($filters['driver'])) {
            $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_driver' . "\n" .
                            ' ON (doc_driver.model_id=d.id AND doc_driver.var_id="' . (isset($doc_vars[DOC_VAR_DRIVER]) ? $doc_vars[DOC_VAR_DRIVER]: '') . '" AND doc_driver.value="' . $filters['driver'] . '")' . "\n";
        }
        if (!empty($filters['region'])) {
            $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_destination' . "\n" .
                            ' ON (doc_destination.model_id=d.id AND doc_destination.var_id="' . (isset($doc_vars[DOC_VAR_DESTINATION]) ? $doc_vars[DOC_VAR_DESTINATION]: '') . '" AND doc_destination.value="' . $filters['region'] . '")' . "\n";
        }
        if (!empty($filters['settlement'])) {
            $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_city' . "\n" .
                            ' ON (doc_city.model_id=d.id AND doc_city.var_id="' . (isset($doc_vars[DOC_VAR_CITY]) ? $doc_vars[DOC_VAR_CITY]: '') . '" AND doc_city.value="' . $filters['settlement'] . '")' . "\n";
        }
        $sql['where'] = 'WHERE ' . implode(' AND ', $where);

        $query = implode("\n", $sql);
        $requests_ids = self::$registry['db']->GetCol($query);

        if (empty($requests_ids)) {
            return array();
        }

        // get the cities
        $sql = 'SELECT d.id as id, d.`date`, d.custom_num, d.full_num, d.office, oi18n.name as office_name, ' . "\n" .
               '       d.status, "" as status_name, d.substatus, ds.name as substatus_name, d.group as `group`, ' . "\n" .
               '       d.customer, CONCAT(ci18n.`name`, " ", ci18n.lastname) as `customer_name`, ' . "\n" .
               '       d_cstm_date.value as delivery_date, d_cstm_order_num.value as order_num, ' . "\n" .
               '       d_cstm_comments.value as comments, d_cstm_notes.value as `notes`, nom_color.name as color_name, ' . "\n" .
               '       doc_module_glazing.value as module_glazing, doc_accessories.value as accessories, ' . "\n" .
               '       d_cstm_file.value as `file`, d_cstm_module.value as `module`, doc_transport_list.value as transport_list,' . "\n" .
               '       d_cstm_req_num.value as `req_num`, doc_var_meters.value as `meters`, ' . "\n" .
               '       doc_destination.value as `destination`, nom_destination.name as `destination_name`, ' . "\n" .
               '       doc_city.value as `city`, nom_city.name as `city_name`, doc_article_color.value as color, nom_color.name as color_name, ' . "\n" .
               '       doc_vehicle.value as `vehicle`, nom_vehicle.name as `vehicle_name`, doc_pay_amount.value as payment_amount, ' . "\n" .
               '       doc_driver.value as `driver`, CONCAT(ci18n_driver.`name`, " ", ci18n_driver.lastname) as `driver_name`, ' . "\n" .
               '       doc_prod_article.value as `production_article`, nom_prod_article.name as `production_article_name`, ' . "\n" .
               '       doc_handle.value as handle, doc_cartridge.value as cartridge, doc_handle_desc.value as handle_desc, doc_cartridge_desc.value as cartridge_desc' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
               ' ON (ci18n.parent_id=d.customer AND ci18n.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
               ' ON (oi18n.parent_id=d.office AND oi18n.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
               ' ON (ds.id=d.substatus AND ds.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date' . "\n" .
               ' ON (d_cstm_date.model_id=d.id AND d_cstm_date.var_id="' . (isset($doc_vars[DOC_VAR_DELIVERY_DATE]) ? $doc_vars[DOC_VAR_DELIVERY_DATE]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_order_num' . "\n" .
               ' ON (d_cstm_order_num.model_id=d.id AND d_cstm_order_num.var_id="' . (isset($doc_vars[DOC_VAR_ORDER_NUM]) ? $doc_vars[DOC_VAR_ORDER_NUM]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_comments' . "\n" .
               ' ON (d_cstm_comments.model_id=d.id AND d_cstm_comments.var_id="' . (isset($doc_vars[DOC_VAR_COMMENTS]) ? $doc_vars[DOC_VAR_COMMENTS]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_notes' . "\n" .
               ' ON (d_cstm_notes.model_id=d.id AND d_cstm_notes.var_id="' . (isset($doc_vars[DOC_VAR_NOTES]) ? $doc_vars[DOC_VAR_NOTES]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_file' . "\n" .
               ' ON (d_cstm_file.model_id=d.id AND d_cstm_file.var_id="' . (isset($doc_vars[DOC_VAR_PANEL_FILE]) ? $doc_vars[DOC_VAR_PANEL_FILE]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_module' . "\n" .
               ' ON (d_cstm_module.model_id=d.id AND d_cstm_module.var_id="' . (isset($doc_vars[DOC_VAR_MODULE]) ? $doc_vars[DOC_VAR_MODULE]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_req_num' . "\n" .
               ' ON (d_cstm_req_num.model_id=d.id AND d_cstm_req_num.var_id="' . (isset($doc_vars[DOC_VAR_REQ_NUM]) ? $doc_vars[DOC_VAR_REQ_NUM]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_var_meters' . "\n" .
               ' ON (doc_var_meters.model_id=d.id AND doc_var_meters.var_id="' . (isset($doc_vars[DOC_VAR_REQ_METERS]) ? $doc_vars[DOC_VAR_REQ_METERS]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_destination' . "\n" .
               ' ON (doc_destination.model_id=d.id AND doc_destination.var_id="' . (isset($doc_vars[DOC_VAR_DESTINATION]) ? $doc_vars[DOC_VAR_DESTINATION]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_transport_list' . "\n" .
               ' ON (doc_transport_list.model_id=d.id AND doc_transport_list.var_id="' . (isset($doc_vars[DOC_VAR_REQ_TRANSPORT_LIST]) ? $doc_vars[DOC_VAR_REQ_TRANSPORT_LIST]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_pay_amount' . "\n" .
               ' ON (doc_pay_amount.model_id=d.id AND doc_pay_amount.var_id="' . (isset($doc_vars[DOC_VAR_REQ_PAYMENT_AMOUNT]) ? $doc_vars[DOC_VAR_REQ_PAYMENT_AMOUNT]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_article_color' . "\n" .
               ' ON (doc_article_color.model_id=d.id AND doc_article_color.var_id="' . (isset($doc_vars[DOC_VAR_ARTICLE_COLOR]) ? $doc_vars[DOC_VAR_ARTICLE_COLOR]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_color' . "\n" .
               ' ON (nom_color.parent_id=doc_article_color.value AND nom_color.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_module_glazing' . "\n" .
               ' ON (doc_module_glazing.model_id=d.id AND doc_module_glazing.var_id="' . (isset($doc_vars[DOC_VAR_MODULE_GLAZING]) ? $doc_vars[DOC_VAR_MODULE_GLAZING]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_accessories' . "\n" .
               ' ON (doc_accessories.model_id=d.id AND doc_accessories.var_id="' . (isset($doc_vars[DOC_VAR_ACCESSORIES]) ? $doc_vars[DOC_VAR_ACCESSORIES]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_destination' . "\n" .
               ' ON (nom_destination.parent_id=doc_destination.value AND nom_destination.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_city' . "\n" .
               ' ON (doc_city.model_id=d.id AND doc_city.var_id="' . (isset($doc_vars[DOC_VAR_CITY]) ? $doc_vars[DOC_VAR_CITY]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_city' . "\n" .
               ' ON (nom_city.parent_id=doc_city.value AND nom_city.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_vehicle' . "\n" .
               ' ON (doc_vehicle.model_id=d.id AND doc_vehicle.var_id="' . (isset($doc_vars[DOC_VAR_VEHICLE]) ? $doc_vars[DOC_VAR_VEHICLE]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_vehicle' . "\n" .
               ' ON (nom_vehicle.parent_id=doc_vehicle.value AND nom_vehicle.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_driver' . "\n" .
               ' ON (doc_driver.model_id=d.id AND doc_driver.var_id="' . (isset($doc_vars[DOC_VAR_DRIVER]) ? $doc_vars[DOC_VAR_DRIVER]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_driver' . "\n" .
               ' ON (ci18n_driver.parent_id=doc_driver.value AND ci18n_driver.lang="' . self::$registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_prod_article' . "\n" .
               ' ON (doc_prod_article.model_id=d.id AND doc_prod_article.var_id="' . (isset($doc_vars[DOC_VAR_PRODUCTION_ARTICLE]) ? $doc_vars[DOC_VAR_PRODUCTION_ARTICLE]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_handle' . "\n" .
               ' ON (doc_handle.model_id=d.id AND doc_handle.var_id="' . (isset($doc_vars[DOC_VAR_HANDLE]) ? $doc_vars[DOC_VAR_HANDLE]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_cartridge' . "\n" .
               ' ON (doc_cartridge.model_id=d.id AND doc_cartridge.var_id="' . (isset($doc_vars[DOC_VAR_CARTRIDGE]) ? $doc_vars[DOC_VAR_CARTRIDGE]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_handle_desc' . "\n" .
               ' ON (doc_handle_desc.model_id=d.id AND doc_handle_desc.var_id="' . (isset($doc_vars[DOC_VAR_HANDLE_DESCRIPTION]) ? $doc_vars[DOC_VAR_HANDLE_DESCRIPTION]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_cartridge_desc' . "\n" .
               ' ON (doc_cartridge_desc.model_id=d.id AND doc_cartridge_desc.var_id="' . (isset($doc_vars[DOC_VAR_CARTRIDGE_DESCRIPTION]) ? $doc_vars[DOC_VAR_CARTRIDGE_DESCRIPTION]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_prod_article' . "\n" .
               ' ON (nom_prod_article.parent_id=doc_prod_article.value AND nom_prod_article.lang="' . self::$registry['lang'] . '")' . "\n";

        $where = array();
        $where[] = 'd.id IN (' . implode(',', $requests_ids) . ')';

        $sql .= 'WHERE ' . implode(' AND ', $where);
        $results = self::$registry['db']->GetAll($sql);

        // get the contact data for the customers
        $customers_list = array_unique(array_column($results, 'customer'));
        $customers_list_contacts = array_fill_keys($customers_list, array());
        if (!empty($customers_list)) {
            $sql = 'SELECT `id`, `phone`, `gsm` FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id` IN ("' . implode('","', $customers_list) . '")';
            $customers_contact_data = self::$registry['db']->GetAssoc($sql);

            foreach ($customers_contact_data as $k => $cust_contact_data) {
                foreach (array('gsm', 'phone') as $c) {
                    if ($cust_contact_data[$c]) {
                        $detail_dat = explode("\n", $cust_contact_data[$c]);
                        foreach($detail_dat as $cont_inf) {
                            $data_wo_notes = explode('|', $cont_inf);
                            $customers_list_contacts[$k][] = $data_wo_notes[0];
                        }
                    }
                }
            }
        }

        // complete the additional data
        $files = array();
        foreach ($results as $res_key => $res) {
            $results[$res_key]['status_name'] = self::$registry['translater']->translate('reports_status_' . $res['status']);
            if (!empty($res['file'])) {
                $files[$res['file']] = $res_key;
            }
            $results[$res_key]['notes_combined'] = array(
                $res['comments'],
                $res['notes']
            );
            $results[$res_key]['notes_combined'] = array_filter($results[$res_key]['notes_combined']);
            $results[$res_key]['notes_combined'] = implode('<br />', $results[$res_key]['notes_combined']);

            if (!empty($customers_list_contacts[$res['customer']])) {
                $results[$res_key]['customer_name'] .= ' (' . implode(', ', $customers_list_contacts[$res['customer']]) . ')';
            }
        }

        // get the files
        $files_list = array();
        if (!empty($files)) {
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            $filters_files = array(
                'model_lang'    => self::$registry['lang'],
                'where'         => array('f.id IN (' . implode(',', array_keys($files)) . ')')
            );

            $files_list = Files::search(self::$registry, $filters_files);
        }

        foreach ($files_list as $filt_lst) {
            if (isset($files[$filt_lst->get('id')])) {
                $results[$files[$filt_lst->get('id')]]['file'] = $filt_lst;
            }
        }

        return $results;
    }

    /*
     * Function that will get all the available vehicles
     *
     * @return array
     */
    public static function getVehicles() {
        // GET VEHICLES
        // get needed vars
        $nom_vars = self::getAssocVarsList('Nomenclature', NOM_TYPE_VEHICLE, array(NOM_VAR_CAPACITY_COUNT, NOM_VAR_CAPACITY_VOLUME));

        // get the cities
        $sql = 'SELECT n.`id` as idx, n.id as id, ni18n.`name` as name, n.code, nom_capacity.value as `capacity`, ' . "\n" .
               '       nom_volume.value as volume, 0 as visible, 0 as taken_capacity, 0 as taken_volume, ' . "\n" .
               '       0 as pretaken_capacity, 0 as pretaken_volume, "" as class' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . self::$registry['lang'] . '" AND n.type="' . NOM_TYPE_VEHICLE . '" AND n.active=1 AND n.deleted_by=0)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_capacity' . "\n" .
               ' ON (nom_capacity.model_id=n.id AND nom_capacity.var_id="' . (isset($nom_vars[NOM_VAR_CAPACITY_COUNT]) ? $nom_vars[NOM_VAR_CAPACITY_COUNT]: '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_volume' . "\n" .
               ' ON (nom_volume.model_id=n.id AND nom_volume.var_id="' . (isset($nom_vars[NOM_VAR_CAPACITY_VOLUME]) ? $nom_vars[NOM_VAR_CAPACITY_VOLUME]: '') . '")' . "\n" .
               'ORDER BY ni18n.`name` ASC' . "\n";
        return self::$registry['db']->GetAssoc($sql);
    }

    /*
     * Function that will get all the available drivers
     *
     * @return array
     */
    public static function getDrivers() {
        // GET Drivers

        // get the cities
        $sql = 'SELECT n.`id` as idx, n.id as id, CONCAT(ni18n.`name`, " ", ni18n.`lastname`) as name, n.code ' . "\n" .
               'FROM ' . DB_TABLE_CUSTOMERS . ' AS n' . "\n" .
               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ni18n' . "\n" .
               ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . self::$registry['lang'] . '" AND n.type="' . CUSTOMER_TYPE_DRIVER . '" AND n.active=1 AND n.deleted_by=0)' . "\n";
        return self::$registry['db']->GetAssoc($sql);
    }

    /**
     * Function to get the needed vars of one model and certain model type
     *
     * @param string $model - the name of the model
     * @param int $model_type - the type of the model
     * @param array of string $vars_names - the names of the needed vars
     * @return mixed
     */
    private static function getAssocVarsList($model, $model_type, $vars_names) {
        $query = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="' . $model . '" AND model_type=' . $model_type . ' AND `name` IN ("' . implode('","', $vars_names) . '")';
        return self::$registry['db']->GetAssoc($query);
    }

    /*
     * Function to manage returning of the results
     */
    public static function returnResults($filters, $results) {
        // pagination
        if (!empty($filters['paginate'])) {
            $results = array($results, 0);
        }

        return $results;
    }

    /**
     * Function to order and group the results by the parent order
     *
     * @param array $results - unordered results
     * @return mixed - ordered results
     */
    public static function reorderResultsByParentOrder($results) {
        // get the relations with orders
        $relatives = array();
        if (!empty($results)) {
            $requests_ids = array_column($results, 'id');

            $orders_types = preg_split('/\s*,\s*/', DOC_TYPE_ORDER);
            $doc_order_vars = array();
            foreach ($orders_types as $ord_type) {
                $order_vars = self::getAssocVarsList('Document', $ord_type, array(DOC_TYPE_ORDER_SUM_TO_PAY, DOC_TYPE_ORDER_SUPPLIER));
                foreach ($order_vars as $vr_nm => $vr_id) {
                    if (!isset($doc_order_vars[$vr_nm])) {
                        $doc_order_vars[$vr_nm] = array();
                    }
                    $doc_order_vars[$vr_nm][] = $vr_id;
                }
            }

            // get the relations
            $sql = 'SELECT dr.parent_id, dr.link_to as order_id, doc_balance.value as sum_to_pay, doc_splr.value as supplier_id, CONCAT(ci18n.`name`, " ", ci18n.`lastname`) as supplier_name' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   ' ON (d.id=dr.link_to AND dr.parent_model_name="Document" AND dr.link_to_model_name="Document" AND d.type IN ("' . implode('","', $orders_types) . '") AND dr.parent_id IN ("' . implode('","', $requests_ids) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_balance' . "\n" .
                   ' ON (doc_balance.model_id=d.id AND doc_balance.var_id IN ("' . (isset($doc_order_vars[DOC_TYPE_ORDER_SUM_TO_PAY]) ? implode('","', $doc_order_vars[DOC_TYPE_ORDER_SUM_TO_PAY]) : '') . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_splr' . "\n" .
                   ' ON (doc_splr.model_id=d.id AND doc_splr.var_id IN ("' . (isset($doc_order_vars[DOC_TYPE_ORDER_SUPPLIER]) ? implode('","', $doc_order_vars[DOC_TYPE_ORDER_SUPPLIER]) : '') . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                   ' ON (ci18n.parent_id=doc_splr.value AND ci18n.lang="' . self::$registry['lang'] . '")' . "\n";;
            $relatives = self::$registry['db']->GetAssoc($sql);
        }

        // reorder results by parent order
        $reordered_results = array();
        foreach ($results as $res) {
            if (empty($relatives[$res['id']]['order_id'])) {
                continue;
            }
            $new_key = $relatives[$res['id']]['order_id'];
            if (!isset($reordered_results[$new_key])) {
                $reordered_results[$new_key] = array(
                    'order_id'    => $new_key,
                    'balance'     => $relatives[$res['id']]['sum_to_pay'],
                    'office'      => $res['office'],
                    'office_name' => $res['office_name'],
                    'city'        => $res['city'],
                    'city_name'   => $res['city_name'],
                    'rowspan'     => 0,
                    'lists'       => array()
                );
            }

            $reordered_results[$new_key]['lists'][$res['id']] = $res;
            $reordered_results[$new_key]['lists'][$res['id']]['order_id'] = $new_key;
            $reordered_results[$new_key]['lists'][$res['id']]['supplier_id'] = $relatives[$res['id']]['supplier_id'];
            $reordered_results[$new_key]['lists'][$res['id']]['supplier_name'] = $relatives[$res['id']]['supplier_name'];
            $reordered_results[$new_key]['rowspan']++;
        }

        return $reordered_results;
    }
}

?>
