<?php

class Custom_Report_Filters extends Report_Filters {

    /**
     * @var Registry
     */
    private static $registry = array();

    /**
     * Defining filters for the certain type of report
     *
     * @param Registry $registry - the main registry
     * @return array - filter definitions of report
     */
    function defineFilters(Registry &$registry) {

        self::$registry = &$registry;

        $settings = Reports::getReportSettings($registry, $this->reportName);

        // $filters - array containing description of all filters
        $filters = array();

        $filters['kind'] = array(
            'name' => 'kind',
            'type' => 'radio',
            'required' => true,
            'options' => array(
                array(
                    'option_value' => 'course',
                    'label' => $this->i18n('reports_analysis_kind_course'),
                ),
                array(
                    'option_value' => 'roundtrip',
                    'label' => $this->i18n('reports_analysis_kind_roundtrip'),
                ),
            ),
            'options_align' => 'horizontal',
            'label' => $this->i18n('reports_analysis_kind_display'),
            'on_change' => "['customer', 'driver'].each(function(a) {
                [$(a).up('tr')].concat($(a).adjacent('input.autocompletebox'))
                .map(getRadioValue($$('input[type=radio][name=kind]')) == 'course' ? Element.show : Element.hide); })",
        );

        $filters['date_from'] = array(
            'name' => 'date_from',
            'type' => 'custom_filter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'first_filter_label' => mb_convert_case($this->i18n('from'), MB_CASE_LOWER),
            'additional_filter' => array(
                'name' => 'date_to',
            ),
        );

        $filters['date_to'] = array();

        $d = new Document($registry, array('type' => $settings['doc_type_request']));
        $assoc_vars = $d->getAssocVars();

        $filters['truck'] = array_replace_recursive(
            $assoc_vars[$settings['request_truck_var']],
            array(
                'name' => 'truck',
                'label' => $this->i18n('reports_truck'),
                'autocomplete' => array(
                    'fill_options' => array(
                        '$truck => <id>',
                        '$truck_autocomplete => <name> <code>',
                    ),
                    'add' => '0',
                ),
            ));

        $filters['customer'] = $d->getMetaInfo($d->getMetaId('customer', true));
        $filters['customer'] = array_replace_recursive(
            $filters['customer'],
            array(
                'label' => $this->i18n('reports_customer'),
                'autocomplete' => array(
                    'fill_options' => array(
                        '$customer => <id>',
                        '$customer_autocomplete => <name> <lastname>',
                    ),
                    'add' => '0',
                ) + $d->processAutocompleteSource($filters['customer']['source']),
            ));

        $filters['driver'] = array_replace_recursive(
            $assoc_vars[$settings['request_driver_var']],
            array(
                'name' => 'driver',
                'label' => $this->i18n('reports_driver'),
                'autocomplete' => array(
                    'fill_options' => array(
                        '$driver => <id>',
                        '$driver_autocomplete => <name> <lastname>',
                    ),
                    'add' => '0',
                ),
            ));

        $filters['currency'] = array(
            'name' => 'currency',
            'type' => 'dropdown',
            'required' => true,
            'options' => Finance_Dropdown::getCurrencies(array($registry)),
            'label' => $this->i18n('reports_currency'),
        );

        return $filters;
    }

    /**
     * Process some filters that depend on the request or on each other
     *
     * @param array $filters - the report filters
     * @return array - report filters after processing
     */
    function processDependentFilters(array &$filters) {
        $registry = &self::$registry;

        if (!$registry['generated_report']) {
            $settings = Reports::getReportSettings($registry, $this->reportName);

            if (!empty($filters['kind']) && empty($filters['kind']['value'])) {
                $filters['kind']['value'] = 'course';
            }

            if (!empty($filters['currency']) && empty($filters['currency']['value']) && !empty($settings['default_currency'])) {
                $filters['currency']['value'] = $settings['default_currency'];
            }
        }

        if (!empty($filters['date_to'])) {
            if (!empty($filters['date_from']['additional_filter'])) {
                $filters['date_from']['additional_filter'] = $filters['date_to'] + $filters['date_from']['additional_filter'];
            }
            unset ($filters['date_to']);
        }

        foreach (array('customer', 'driver') as $a) {
            if (!empty($filters[$a])) {
                $filters[$a]['hidden'] = !empty($filters['kind']['value']) && $filters['kind']['value'] == 'roundtrip';
            }
        }

        return $filters;
    }
}
