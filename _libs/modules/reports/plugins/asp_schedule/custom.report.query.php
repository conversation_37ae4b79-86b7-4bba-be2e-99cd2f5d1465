<?php

class Asp_Schedule extends Reports {
    private static $types = array();
    private static $offsets = array();

    public static function buildQuery(&$registry, $filters = array()) {

        $all_docs = array();
        $error = false;
        self::$types = array(DOC_SERVICE_TYPE_SOT, DOC_SERVICE_TYPE_FO, DOC_SERVICE_TYPE_REPAIR, DOC_SERVICE_TYPE_OTHER, DOC_SERVICE_TYPE_DISMANTLING, DOC_SERVICE_TYPE_ASSEMBLY);

        if (empty($filters['datef'])) {
            $filters['datef'] =
                !empty($filters['period_from']) ? $filters['period_from'] :
                (!empty($filters['period_to']) ? $filters['period_to'] : date("Y-m-d"));
        } else {
            // date is outside period
            if (!empty($filters['period_from']) && $filters['datef'] < $filters['period_from'] ||
                !empty($filters['period_to']) && $filters['datef'] > $filters['period_to']) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_date_outside_period'));
                $error = true;
            }
        }
        if (!empty($filters['period_from']) && !empty($filters['period_to']) && $filters['period_from'] > $filters['period_to']) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_invalid_period'));
            $error = true;
        }

        // make sure whole week of selected date is searched even when outside period
        $period = array();
        if (!empty($filters['period_from']) || empty($filters['period_from']) && empty($filters['period_to'])) {
            $period['period_from'] = date_create($filters['datef']);
            if ($period['period_from']->format('N') > 1) {
                $period['period_from'] = date_sub($period['period_from'], new DateInterval('P' . ($period['period_from']->format('N') - 1) . 'D'));
            }
            $period['period_from'] = $period['period_from']->format('Y-m-d');
            if (!empty($filters['period_from']) && $period['period_from'] > $filters['period_from']) {
                $period['period_from'] = $filters['period_from'];
            }
        }
        if (!empty($filters['period_to']) || empty($filters['period_from']) && empty($filters['period_to'])) {
            $period['period_to'] = date_create($filters['datef']);
            if ($period['period_to']->format('N') < 7) {
                $period['period_to'] = date_add(date_create($filters['datef']), new DateInterval('P' . (7 - $period['period_to']->format('N')) . 'D'));
            }
            $period['period_to'] = $period['period_to']->format('Y-m-d');
            if (!empty($filters['period_to']) && $period['period_to'] < $filters['period_to']) {
                $period['period_to'] = $filters['period_to'];
            }
        }

        $all_docs = $error ? $all_docs : self::searchSchedule($registry, $period + $filters);

        // prepare types
        $sql = 'SELECT `parent_id`, `name`' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS_TYPES_I18N . "\n" .
               'WHERE `lang`="' . $registry['lang'] . '" AND `parent_id` IN (' . implode(',', self::$types) . ')' . "\n" .
               'ORDER BY `name` ASC' . "\n";
        $included_documents = $registry['db']->GetAssoc($sql);

        $docs = array();

        // documents for selected date
        $docs['current'] = array_filter($all_docs, function($d) use ($filters) {
            return !empty($d['date']) && $d['date'] == $filters['datef'];
        });

        // documents for week (excluding selected date)
        $docs['week'] = array_filter($all_docs, function($d) use ($filters) {
            $datef = new DateTime($filters['datef']);
            $dated = new DateTime($d['date']);
            return !empty($d['date']) && $d['date'] != $filters['datef'] &&
                $datef->format('W') == $dated->format('W') && $datef->diff($dated, true)->days <= 7;
        });

        // documents for list (within specified period)
        $docs['period'] = array_filter($all_docs, function($d) use ($filters) {
            return !empty($d['date']) &&
            (empty($filters['period_from']) || $filters['period_from'] <= $d['date']) &&
            (empty($filters['period_to']) || $filters['period_to'] >= $d['date']);
        });

        if (!defined('COLOR_NOT_DISTRIBUTED')) {
            define('COLOR_NOT_DISTRIBUTED', 'blue');
        }

        // prepare hours
        $hours = array();
        $hours_start = date_create($filters['datef'] . ' ' . WORK_START . ':00');
        $hours_end = date_create($filters['datef'] . ' ' . WORK_END . ':00');
        while ($hours_start <= $hours_end) {
            $hours[] = $hours_start->format('H:i');
            $hours_start = date_add($hours_start, new DateInterval('PT30M'));
        }

        $legend = array();
        $offsets = array();

        self::calculateOffsets($docs['current']);

        foreach ($docs['current'] as $i => $d) {
            if (isset(self::$offsets[$d['id']])) {
                $offsets[$d['id']] = self::$offsets[$d['id']];
            }
            if (!empty($d['user_name'])) {
                $legend[$d['user_name']] = $d['color'];
                if (empty($d['color'])) {
                    $docs['current'][$i]['color'] = "white";
                }
            } else {
                $docs['current'][$i]['color'] = COLOR_NOT_DISTRIBUTED;
                $docs['current'][$i]['person'] = 0;
            }
            //calculate offset
            if (preg_replace('#\d{2}:(\d{2})#', '$1', $d['start']) >= 30) {
                $point = preg_replace('#(\d{2}):\d{2}#', '$1:30', $d['start']);
            } else {
                $point = preg_replace('#(\d{2}):\d{2}#', '$1:00', $d['start']);
            }

            // if start hour is before working day start, document will be partially displayed
            if ($point < WORK_START && $d['end'] > WORK_START) {
                $duration_diff = date_diff(date_create(WORK_START), date_create($d['start']));
                $docs['current'][$i]['duration'] = $d['duration'] - ($duration_diff->h * 60 + $duration_diff->i);
                $docs['current'][$i]['start_before'] = $d['start'];
                $docs['current'][$i]['start'] = $d['start'] = $point = WORK_START;
            }

            $docs['current'][$i]['offset'] = date_diff(date_create($point), date_create($d['start']))->i;
        }

        $legend[$registry['translater']->translate('reports_unasigned')] = COLOR_NOT_DISTRIBUTED;
        $week = array();
        $first_day = date_create($filters['datef'])->format('N') - 1;
        $first_day = date_sub(date_create($filters['datef']), new DateInterval('P' . $first_day . 'D'))->format('Y-m-d');
        $last_day = date_add(date_create($first_day), new DateInterval('P5D'))->format('Y-m-d');
        $uri = $_SERVER['REQUEST_URI'];
        $uri = preg_replace('#&+datef=\d{4}-\d{2}-\d{2}#', '', $uri);
        if (!preg_match("#reports=generate_report#", $uri)) {
            $uri .= '&reports=generate_report';
        }

        while ($first_day <= $last_day) {
            $day = $registry['translater']->translate('weekday_' . date_create($first_day)->format('w')) . ' / ' . date_create($first_day)->format('d.m.Y');
            $week[$day] = array('date' => date_create($first_day)->format('Y-m-d'), 'hours' => array());
            foreach ($hours as $idx => $h) {
                if ($idx % 2 != 0) {
                    continue;
                }
                if (!isset($hours[$idx + 2])) {
                    break;
                }
                $h = $h . '/' . $hours[$idx + 2];
                $week[$day]['hours'][$h] = 0;
                foreach ($docs['week'] as $d) {
                    if ($d['date'] == $first_day && $h . ":00" >= $d['start'] && $h . ":00" < $d['end']) {
                        $week[$day]['hours'][$h]++;
                    }
                }
            }
            $first_day = date_add(date_create($first_day), new DateInterval('P1D'))->format('Y-m-d');
        }
        $docs['week'] = $week;

        // define the previous and the next day
        $prev_day = '';
        $current_day = date_create($filters['datef']);
        while (empty($prev_day)) {
            $current_day = date_sub($current_day, new DateInterval('P1D'));
            if ($current_day->format('N') != 0 && $current_day->format('N') != 7) {
                $prev_day = clone($current_day);
            }
        }

        $next_day = '';
        $current_day = date_create($filters['datef']);
        while (empty($next_day)) {
            $current_day = date_add($current_day, new DateInterval('P1D'));
            if ($current_day->format('N') != 0 && $current_day->format('N') != 7) {
                $next_day = clone($current_day);
            }
        }

        $results = array(
            'additional_options' => array('exclude_outter_form' => true, 'documents' => $included_documents),
            'schedules' => $docs,
            'hours' => $hours,
            'legend' => $legend,
            'day_number' => date_create($filters['datef'])->format('N'),
            'today' => $registry['translater']->translate('weekday_' . date_create($filters['datef'])->format('w')),
            'date' => $filters['datef'],
            'next_day' => $next_day->format('Y-m-d'),
            'prev_day' => $prev_day->format('Y-m-d'),
            'next_week' => date_add(date_create($filters['datef']), new DateInterval('P7D'))->format('Y-m-d'),
            'prev_week' => date_sub(date_create($filters['datef']), new DateInterval('P7D'))->format('Y-m-d'),
            'uri' => $uri,
            'offsets' => $offsets,
        );

        $registry->set('hide_export_button', true, true);

        // pagination
        if (!empty($filters['paginate'])) {
            $results = array($results, 0);
        }
        return $results;
    }

    /**
     * Function to build the schedule
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @return array - found results
     */
    public static function searchSchedule($registry, $filters) {
        if (empty(self::$types)) {
            self::$types = array(DOC_SERVICE_TYPE_SOT, DOC_SERVICE_TYPE_FO, DOC_SERVICE_TYPE_REPAIR, DOC_SERVICE_TYPE_OTHER, DOC_SERVICE_TYPE_DISMANTLING, DOC_SERVICE_TYPE_ASSEMBLY);
        }
        $types = self::$types;
        if (!empty($filters['service'])) {
            $types = array($filters['service']);
        }

        //get vars ids
        $query = 'SELECT name, GROUP_CONCAT(id) FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE model = "Document" AND model_type IN (\'' . implode('\', \'', $types) . '\') AND gt2 = 0' . "\n" .
                 'GROUP BY name';
        $dVars = $registry['db']->GetAssoc($query);

        //get customers ids
        $query = 'SELECT name, GROUP_CONCAT(id) FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE model = "Customer" AND model_type="' . PH_CUSTOMER_EMPLOYEE . '" AND name ="' . CUSTOMER_VAR_COLOR . '"' . "\n" .
                 'GROUP BY name';
        $cVars = $registry['db']->GetAssoc($query);

        $technicians = array();
        if (!empty($filters['technician'])) {
            $technicians = array($filters['technician']);
        }
        if (empty($filters['technician'])) {
            $technicians = self::getTechniciansList($registry);
            $technicians = array_keys($technicians);
        }
        $technicians = array_filter($technicians);
        if (empty($technicians)) {
            // exit without further processing - no results to show
            return array();
        }

        $date_filter = 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc1' . "\n" .
                       '  ON d.id = dc1.model_id AND dc1.var_id IN (' . (!empty($dVars[DOC_VAR_DATE]) ? $dVars[DOC_VAR_DATE] : '""') . ') AND dc1.value >= "' . $filters['period_from'] . '"  AND dc1.value <= "' . $filters['period_to'] . '"' . "\n";

        $town_filter = 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc2' . "\n" .
                       '  ON d.id = dc2.model_id AND dc2.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_CITY]) ? $dVars[DOC_VAR_ADDRESS_CITY] : '""') . ')' . "\n";
        if (!empty($filters['town'])) {
            $town_filter .= '    AND dc2.value = "' . $filters['town_autocomplete'] . '"' . "\n";
        } else {
            $town_filter = 'LEFT ' . $town_filter;
        }
        $object_filter = '';
        if (!empty($filters['object'])) {
            $object_filter = 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc14' . "\n" .
                           '  ON d.id = dc14.model_id AND dc14.var_id IN (' . (!empty($dVars[DOC_VAR_OBJECT]) ? $dVars[DOC_VAR_OBJECT] : '""') . ')' . "\n";
        }

        $description_fields = array();
        if (!empty($dVars[DOC_VAR_DESCRIPTION1])) {
            $description_fields = array_merge($description_fields, array_filter(preg_split('/\s*,\s*/', $dVars[DOC_VAR_DESCRIPTION1])));
        }
        if (!empty($dVars[DOC_VAR_DESCRIPTION2])) {
            $description_fields = array_merge($description_fields, array_filter(preg_split('/\s*,\s*/', $dVars[DOC_VAR_DESCRIPTION2])));
        }
        $description_fields = '"' . implode('","', $description_fields) . '"';

        // prepare rights filter
        $tmp_filters = array(
            'where'                    => array(),
            'check_module_permissions' => 'documents'
        );
        foreach ($types as $k => $tp) {
            $tmp_filters['where'][] = sprintf("d.type = '%d' %s", $tp, ($k+1 == count($types) ? 'AND' : 'OR'));
        }

        $current_action = $registry['action'];
        $registry->set('action', 'search', true);
        $where = Documents::constructWhere($registry, $tmp_filters);
        $registry->set('action', $current_action, true);

        $query = 'SELECT d.id as idx, d.id, d.full_num,d.customer, TRIM(CONCAT(ci.name, " ", ci.lastname)) as customer_name, "" as person, ' . "\n" .
                 '       "" as user_name, dc6.value as phone, "" as color, dti.name AS type_name, d.status, d.substatus,  ds.name as substatus_name, ' . "\n" .
                 '      ' . intval(DEFAULT_DURATION_MINUTES) . ' as duration, d.office, d.employee as added, CONCAT(ci18n.name, " ", ci18n.lastname) as added_name, ' . "\n" .
                 '       oi.name AS office_name, dc2.value as city, dc7.value as quarter, dc8.value as street, dc9.value as streetnum, ' . "\n" .
                 '       dc10.value as block, dc11.value as entrance, dc12.value as floor, dc13.value as flat, dc1.value as date, ' . "\n" .
                 '       dc5.value as start, dc3.value as end, dc4.value as description, GROUP_CONCAT(u.employee) AS empl' . "\n" .
                 'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n" .
                 $date_filter . $town_filter . $object_filter .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_ASSIGNMENTS . ' da' . "\n" .
                 '  ON d.id = da.parent_id AND da.assignments_type="' . PH_ASSIGNMENTS_OWNER . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS . ' u' . "\n" .
                 '  ON (u.id=da.assigned_to)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' dti' . "\n" .
                 '  ON d.type = dti.parent_id AND dti.lang = \'' . $registry['lang'] . '\'' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' ci' . "\n" .
                 '  ON ci.parent_id = d.customer AND ci.lang = "' . $registry['lang'] . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' ci18n' . "\n" .
                 '  ON d.employee = ci18n.parent_id AND ci18n.lang = "' . $registry['lang'] . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' oi' . "\n" .
                 '  ON d.office = oi.parent_id AND oi.lang = \'' . $registry['lang'] . '\'' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                 '  ON (d.substatus=ds.id AND ds.lang="' . $registry['lang'] . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc7' . "\n" .
                 '  ON dc7.model_id = d.id AND dc7.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_QUARTER]) ? $dVars[DOC_VAR_ADDRESS_QUARTER] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc8' . "\n" .
                 '  ON dc8.model_id = d.id AND dc8.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_STREET]) ? $dVars[DOC_VAR_ADDRESS_STREET] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc9' . "\n" .
                 '  ON dc9.model_id = d.id AND dc9.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_STREETNUM]) ? $dVars[DOC_VAR_ADDRESS_STREETNUM] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc10' . "\n" .
                 '  ON dc10.model_id = d.id AND dc10.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_BLOCK]) ? $dVars[DOC_VAR_ADDRESS_BLOCK] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc11' . "\n" .
                 '  ON dc11.model_id = d.id AND dc11.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_ENTRANCE]) ? $dVars[DOC_VAR_ADDRESS_ENTRANCE] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc12' . "\n" .
                 '  ON dc12.model_id = d.id AND dc12.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_FLOOR]) ? $dVars[DOC_VAR_ADDRESS_FLOOR] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc13' . "\n" .
                 '  ON dc13.model_id = d.id AND dc13.var_id IN (' . (!empty($dVars[DOC_VAR_ADDRESS_APARTMENT]) ? $dVars[DOC_VAR_ADDRESS_APARTMENT] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc5' . "\n" .
                 '  ON dc5.model_id = d.id AND dc5.var_id IN (' . (!empty($dVars[DOC_VAR_TIME]) ? $dVars[DOC_VAR_TIME] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc6' . "\n" .
                 '  ON dc6.model_id = d.id AND dc6.var_id IN (' . (!empty($dVars[DOC_VAR_CLIENT_PHONE]) ? $dVars[DOC_VAR_CLIENT_PHONE] : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc4' . "\n" .
                 '  ON dc4.model_id = d.id AND dc4.var_id IN (' . (!empty($description_fields) ? $description_fields : '""') . ')' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc3' . "\n" .
                 '  ON dc3.model_id = d.id AND dc3.var_id IN (' . (!empty($dVars[DOC_VAR_TIME_END]) ? $dVars[DOC_VAR_TIME_END] : '""') . ')' . "\n" .
                 $where . "\n" .
                 (!empty($filters['client']) ? ' AND d.customer = ' . $filters['client'] : '') . "\n" .
                 (!empty($filters['office']) ? ' AND d.office = ' . $filters['office'] : '') . "\n" .
                 (!empty($filters['object']) ? ' AND dc14.value="' . $filters['object'] . '"' : '') . "\n" .
                 'GROUP BY d.id' . "\n" .
                 'ORDER BY date, start';
        $data_list = $registry['db']->GetAssoc($query);

        // clear the events which does not match the needed employees
        foreach ($data_list as $dat_k => $dat_lst) {
            $current_assignemnts = array_filter(preg_split('/\s*,\s*/', $dat_lst['empl']));
            if (!empty($current_assignemnts) && !count(array_intersect($technicians, $current_assignemnts))) {
                unset($data_list[$dat_k]);
            }
        }

        if (!empty($data_list)) {
            // get the executors
            $sql = 'SELECT da.parent_id, u.id as user_id, ci18n.parent_id as employee, CONCAT(ci18n.name, " ", ci18n.lastname) as name, cc1.value as color' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS_ASSIGNMENTS . ' AS da' . "\n" .
                   'INNER JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                   '  ON (u.id=da.assigned_to AND da.assignments_type="' . PH_ASSIGNMENTS_OWNER . '" AND da.parent_id IN (' . implode(',', array_keys($data_list)) . '))' . "\n" .
                   'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                   '  ON (ci18n.parent_id=u.employee AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' cc1' . "\n" .
                   '  ON cc1.model_id = ci18n.parent_id AND cc1.var_id = ' . $cVars[CUSTOMER_VAR_COLOR] . "\n" .
                   'ORDER BY da.assigned ASC' . "\n";
            $assignments_list = $registry['db']->GetAll($sql);

            foreach ($assignments_list as $assigned) {
                if (!isset($data_list[$assigned['parent_id']]['assigned'])) {
                    $data_list[$assigned['parent_id']]['assigned'] = array();
                    $data_list[$assigned['parent_id']]['color'] = $assigned['color'];
                    $data_list[$assigned['parent_id']]['person'] = $assigned['user_id'];
                    $data_list[$assigned['parent_id']]['user_name'] = $assigned['name'];
                }
                $data_list[$assigned['parent_id']]['assigned'][$assigned['employee']] = $assigned['name'];
            }

            foreach ($data_list as $dl_k => $dat_lst) {
                if (!isset($dat_lst['assigned'])) {
                    $data_list[$dl_k]['assigned'] = array();
                }

                $data_list[$dl_k]['visible_status'] = $registry['translater']->translate('reports_documents_status_' . $dat_lst['status']);
                if ($dat_lst['substatus']) {
                    $data_list[$dl_k]['visible_status'] = $dat_lst['substatus_name'];
                }

                // prepare the full address
                $full_address = sprintf(
                    '%s %s %s %s %s %s %s',
                    ($dat_lst['city'] ? $dat_lst['city'] . ',' : ''),
                    ($dat_lst['quarter'] ? $dat_lst['quarter'] . ',' : ''),
                    ($dat_lst['street'] ? $dat_lst['street'] . ($dat_lst['streetnum'] ? ' ' . $dat_lst['streetnum'] : '') . ',' : ''),
                    ($dat_lst['block'] ? $registry['translater']->translate('reports_block') . ' ' . $dat_lst['block'] . ',' : ''),
                    ($dat_lst['entrance'] ? $registry['translater']->translate('reports_entrance') . ' ' . $dat_lst['entrance'] . ',' : ''),
                    ($dat_lst['floor'] ? $registry['translater']->translate('reports_floor') . ' ' . $dat_lst['floor'] . ',' : ''),
                    ($dat_lst['flat'] ? $registry['translater']->translate('reports_flat') . ' ' . $dat_lst['flat'] . ',' : '')
                );
                $full_address = preg_replace('/[\s,]+$/', '', $full_address);

                $data_list[$dl_k]['full_address'] = $full_address;

                // prepare hours
                if (empty($dat_lst['date']) || empty($dat_lst['start'])) {
                    continue;
                }
                $time_start = date_create($dat_lst['date'] . ' ' . $dat_lst['start'] . ':00');
                if ($dat_lst['end']) {
                    $time_end = date_create($dat_lst['date'] . ' ' . $dat_lst['end'] . ':00');
                    $time_interval = date_diff($time_start, $time_end, true);
                    $data_list[$dl_k]['duration'] = ($time_interval->h*60) + $time_interval->i;
                } else {
                    $time_end = date_add($time_start, new DateInterval('PT' . $dat_lst['duration'] . 'M'));
                    $data_list[$dl_k]['end'] = $time_end->format('H:i');
                }
                $data_list[$dl_k]['height_calendar_event'] = ((floor($data_list[$dl_k]['duration']/30)*32)-1) + ($data_list[$dl_k]['duration']%30);
            }
        }

        return $data_list;
    }

    /**
     * Function to get the technicians list which match the requirements of the report
     *
     * @return array - found results
     */
    public static function getTechniciansList($registry) {
        $sql = 'SELECT c.id as idx, c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label' . "\n" .
               'FROM ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
               'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
               ' ON (c.id=tm.model_id AND tm.model="Customer" AND tm.tag_id="' . TAG_PARTICIPATE_IN_SCHEDULE . '" AND c.active=1 AND c.deleted_by=0 AND c.type="' . PH_CUSTOMER_EMPLOYEE . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
               ' ON (ci18n.parent_id=c.id AND ci18n.lang="' . $registry['lang'] . '")' . "\n";
        $technicians = $registry['db']->GetAssoc($sql);

        return $technicians;
    }

    /*
     * Function to calculate offsets for the events
     */
    public static function calculateOffsets($events) {
        $events_by_employee = array();
        foreach ($events as $event) {
            if (!$event['start'] || !$event['end']) {
                continue;
            }

            $employee_key = intval($event['person']);
            if (!isset($events_by_employee[$employee_key])) {
                $events_by_employee[$employee_key] = array();
            }
            $events_by_employee[$employee_key][$event['id']] = $event;
            $events_by_employee[$employee_key][$event['id']]['start_time'] = new DateTime(sprintf('%s %s:00', $event['date'], $event['start']));
            $events_by_employee[$employee_key][$event['id']]['end_time'] = new DateTime(sprintf('%s %s:00', $event['date'], $event['end']));
        }

        if (isset($events_by_employee[0])) {
            $events_list = $events_by_employee[0];
            unset($events_by_employee[0]);
            $events_by_employee[0] = $events_list;
        }

        $offset_events = array();
        foreach ($events_by_employee as $emp_id => $events_emp) {
            if (!isset($offset_events[$emp_id])) {
                $offset_events[$emp_id] = array();
            }

            foreach ($events_emp as $ev) {
                $count = count($offset_events[$emp_id]);
                $assign_index = 0;
                for ($i=0; $i<$count; $i++) {
                    foreach ($offset_events[$emp_id][$i] as $ofs_ev) {
                        if ($ofs_ev['start_time']<$ev['end_time'] && $ev['start_time']<$ofs_ev['end_time']) {
                            // match
                            $assign_index = $i+1;
                            continue 2;
                        }
                    }
                    $assign_index = $i;
                    break;
                }

                if (!isset($offset_events[$emp_id][$assign_index])) {
                    $offset_events[$emp_id][$assign_index] = array();
                }
                $offset_events[$emp_id][$assign_index][] = $ev;
            }
        }

        $current_offset = 0;
        foreach ($offset_events as $off_ev_emp) {
            foreach ($off_ev_emp as $offs_e) {
                $current_offset++;
                foreach ($offs_e as $ev) {
                    self::$offsets[$ev['id']] = $current_offset-1;
                }
            }
        }
    }
}

?>
