<tr style="display:none;">
    <td class="labelbox">
        <label for="{$filter_settings.custom_id|default:$filter_settings.name}" style="white-space: nowrap;">{help
            label_content=$filter_settings.label text_content=$filter_settings.help}</label>
    </td>
    <td {if $filter_settings.required} class="required">{#required#}{else}>&nbsp;{/if}</td>
    <td nowrap="nowrap" style="vertical-align: top;">
        <input type="hidden" disabled="disabled" value="{$smarty.const.DEADLINE_SOURCE}" id="deadline_source"/>
        <!-- Column fields templates -->
        <script id="templateRowCounter" type="text/x-jsrender">
              {literal}${counter}{/literal}
        </script>

        <script id="templateDocumentNum" type="text/x-jsrender">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${id}{/literal}" style="font-size: 13px;" target="_blank">
                {literal}${full_num}{/literal}
            </a>
            {literal}${if(sub_status)}{/literal}
                <br>
                {literal}${sub_status}{/literal}
            {literal}${/if}{/literal}
        </script>

        <script id="templateAssignmentLetter" type="text/x-jsrender">

            {literal}${if(assignment_letter_num)}{/literal}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${assignment_letter_id}{/literal}" style="font-size: 13px;" target="_blank">
                    {literal}${assignment_letter_num}{/literal}
                </a>
            {literal}${/if}{/literal}
        </script>

        <script id="templatePaymentWay" type="text/x-jsrender">
            {literal}${if(payment_way)}{/literal}
                {literal}${payment_way}{/literal}
            {literal}${/if}{/literal}
        </script>

        <script id="templateAssignor" type="text/x-jsrender">
            {literal}${if(assignor_id)}{/literal}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${assignor_id}{/literal}" style="font-size: 13px;" target="_blank">
                  {literal}${assignor_name}{/literal}
                </a>
            {literal}${/if}{/literal}
        </script>

        <script id="templateCustomer" type="text/x-jsrender">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${customer}{/literal}" style="font-size: 13px;" target="_blank">
              {literal}${customer_name}{/literal}
            </a>
        </script>

        <script id="templateIncomesReason" type="text/x-jsrender">
            {literal}${if(incomes_reason)}{/literal}
                <a href="{literal}${incomes_reason_link}{/literal}" target="_blank">
                    {literal}${incomes_reason}{/literal}
                </a>
            {literal}${else}{/literal}
              <input type="checkbox" value="{literal}${id}{/literal}" id="reason_report_{literal}${id}{/literal}" name="reason_report[{literal}${id}{/literal}]">
            {literal}${/if}{/literal}
        </script>

        <script id="templateIncomesInvoices" type="text/x-jsrender">
            {literal}${for (invoice of invoices)}{/literal}
                <a href="{literal}${invoice.invoice_link}{/literal}" target="_blank">
                    {literal}${invoice.invoice_name}{/literal}
                </a>
            {literal}${/for}{/literal}
        </script>
        <script id="templatePayments" type="text/x-jsrender">
            {literal}${for (payment of payments)}{/literal}
                <a href="{literal}${payment.payment_link}{/literal}" target="_blank">
                    {literal}${payment.payment_name}{/literal}
                </a>
            {literal}${/for}{/literal}
        </script>
    </td>
</tr>
