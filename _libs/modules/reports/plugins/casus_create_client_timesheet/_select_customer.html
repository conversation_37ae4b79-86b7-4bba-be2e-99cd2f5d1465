<form action="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports" method="post" name="incomes_reasons_form" style="color: #666666; font-family: Verdana,Arial,Helvetica,sans-serif; font-size: 11px;">
  <table cellpadding="5" cellspacing="0" border="0">
    <tr>
      <td class="labelbox"><label for="customer_timesheet">{help label_content=$client_label}</label></td>
      <td class="required">{#required#}</td>
      <td>
        <select name="customer_timesheet" id="customer_timesheet" class="selbox undefined" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 250px;">
          <option value="" class="undefined">[{#please_select#|escape}]</option>
          {foreach from=$available_customers item=avb_customer}
            <option value="{$avb_customer.value|escape}">{$avb_customer.option_label|escape}</option>
          {/foreach}
        </select>
      </td>
    </tr>
  </table>
  <br />
  {strip}
  <button type="submit" name="addClientTimesheet" class="button" onclick="submitClientTimesheet(); return false;">{#add#|escape}</button>
  <input type="button" class="button" onclick="lb.deactivate();" value="{#cancel#}" />
  {/strip}
</form>