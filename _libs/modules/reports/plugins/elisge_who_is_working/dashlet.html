  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" width="100%">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">&nbsp;</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_varna#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_sofia1#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_sofia2#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_burgas#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_ruse#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_store#|escape}</div></td>
      <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_administration#|escape}</div></td>
    </tr>
    <tr class="t_odd">
      <td class="t_border" nowrap="nowrap" style="vertical-align: middle; text-align: center;">
        <img src="{$theme->imagesUrl}reports/elisge_work.png" width="16" height="16" border="0" alt="" title="{#reports_working#|escape}" />
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #92D050;">
        {foreach from=$reports_results.varna.working item=varna_users name=vn_user_work}
          {$varna_users}
          {if !$smarty.foreach.vn_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #FFC000;">
        {foreach from=$reports_results.sofia1.working item=sofia1_users name=sf1_user_work}
          {$sofia1_users}
          {if !$smarty.foreach.sf1_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B6DDE8;">
        {foreach from=$reports_results.sofia2.working item=sofia2_users name=sf2_user_work}
          {$sofia2_users}
          {if !$smarty.foreach.sf2_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B2A1C7;">
        {foreach from=$reports_results.burgas.working item=burgas_users name=bg_user_work}
          {$burgas_users}
          {if !$smarty.foreach.bg_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E6B9B8;">
        {foreach from=$reports_results.ruse.working item=ruse_users name=ru_user_work}
          {$ruse_users}
          {if !$smarty.foreach.ru_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td nowrap="nowrap" style="background-color: #FFFF66;">
        {foreach from=$reports_results.store.working item=store_users name=st_user_work}
          {$store_users}
          {if !$smarty.foreach.st_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
        <br />
        <br />
        {foreach from=$reports_results.workshop.working item=workshop_users name=ws_user_work}
          {$workshop_users}
          {if !$smarty.foreach.ws_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E1C9A3;">
        {foreach from=$reports_results.administration.working item=administration_users name=adm_user_work}
          {$administration_users}
          {if !$smarty.foreach.adm_user_work.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
    </tr>
    <tr class="t_even">
      <td class="t_border" nowrap="nowrap" style="vertical-align: middle; text-align: center;">
        <img src="{$theme->imagesUrl}reports/elisge_days_off.png" width="16" height="16" border="0" alt="" title="{#reports_days_off#|escape}" />
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #92D050;">
        {foreach from=$reports_results.varna.days_off item=varna_users name=vn_user_days_off}
          {$varna_users}
          {if !$smarty.foreach.vn_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #FFC000;">
        {foreach from=$reports_results.sofia1.days_off item=sofia1_users name=sf1_user_days_off}
          {$sofia1_users}
          {if !$smarty.foreach.sf1_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B6DDE8;">
        {foreach from=$reports_results.sofia2.days_off item=sofia2_users name=sf2_user_days_off}
          {$sofia2_users}
          {if !$smarty.foreach.sf2_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B2A1C7;">
        {foreach from=$reports_results.burgas.days_off item=burgas_users name=bg_user_days_off}
          {$burgas_users}
          {if !$smarty.foreach.bg_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E6B9B8;">
        {foreach from=$reports_results.ruse.days_off item=ruse_users name=ru_user_days_off}
          {$ruse_users}
          {if !$smarty.foreach.ru_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td nowrap="nowrap" style="background-color: #FFFF66;">
        {foreach from=$reports_results.store.days_off item=store_users name=st_user_days_off}
          {$store_users}
          {if !$smarty.foreach.st_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
        <br />
        <br />
        {foreach from=$reports_results.workshop.days_off item=workshop_users name=ws_user_days_off}
          {$workshop_users}
          {if !$smarty.foreach.ws_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E1C9A3;">
        {foreach from=$reports_results.administration.days_off item=administration_users name=adm_user_days_off}
          {$administration_users}
          {if !$smarty.foreach.adm_user_days_off.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
    </tr>
    <tr class="t_odd">
      <td class="t_border" nowrap="nowrap" style="vertical-align: middle; text-align: center;">
        <img src="{$theme->imagesUrl}reports/elisge_sick.png" width="16" height="16" border="0" alt="" title="{#reports_sick#|escape}" />
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #92D050;">
        {foreach from=$reports_results.varna.sick item=varna_users name=vn_user_sick}
          {$varna_users}
          {if !$smarty.foreach.vn_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #FFC000;">
        {foreach from=$reports_results.sofia1.sick item=sofia1_users name=sf1_user_sick}
          {$sofia1_users}
          {if !$smarty.foreach.sf1_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B6DDE8;">
        {foreach from=$reports_results.sofia2.sick item=sofia2_users name=sf2_user_sick}
          {$sofia2_users}
          {if !$smarty.foreach.sf2_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B2A1C7;">
        {foreach from=$reports_results.burgas.sick item=burgas_users name=bg_user_sick}
          {$burgas_users}
          {if !$smarty.foreach.bg_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E6B9B8;">
        {foreach from=$reports_results.ruse.sick item=ruse_users name=ru_user_sick}
          {$ruse_users}
          {if !$smarty.foreach.ru_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td nowrap="nowrap" style="background-color: #FFFF66;">
        {foreach from=$reports_results.store.sick item=store_users name=st_user_sick}
          {$store_users}
          {if !$smarty.foreach.st_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
        <br />
        <br />
        {foreach from=$reports_results.workshop.sick item=workshop_users name=ws_user_sick}
          {$workshop_users}
          {if !$smarty.foreach.ws_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E1C9A3;">
        {foreach from=$reports_results.administration.sick item=administration_users name=adm_user_sick}
          {$administration_users}
          {if !$smarty.foreach.adm_user_sick.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
    </tr>
    <tr class="t_even">
      <td class="t_border" nowrap="nowrap" style="vertical-align: middle; text-align: center;">
        <img src="{$theme->imagesUrl}reports/elisge_rest.png" width="16" height="16" border="0" alt="" title="{#reports_rest#|escape}" />
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #92D050;">
        {foreach from=$reports_results.varna.rest item=varna_users name=vn_user_rest}
          {$varna_users}
          {if !$smarty.foreach.vn_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #FFC000;">
        {foreach from=$reports_results.sofia1.rest item=sofia1_users name=sf1_user_rest}
          {$sofia1_users}
          {if !$smarty.foreach.sf1_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B6DDE8;">
        {foreach from=$reports_results.sofia2.rest item=sofia2_users name=sf2_user_rest}
          {$sofia2_users}
          {if !$smarty.foreach.sf2_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #B2A1C7;">
        {foreach from=$reports_results.burgas.rest item=burgas_users name=bg_user_rest}
          {$burgas_users}
          {if !$smarty.foreach.bg_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E6B9B8;">
        {foreach from=$reports_results.ruse.rest item=ruse_users name=ru_user_rest}
          {$ruse_users}
          {if !$smarty.foreach.ru_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td nowrap="nowrap" style="background-color: #FFFF66;">
        {foreach from=$reports_results.store.rest item=store_users name=st_user_rest}
          {$store_users}
          {if !$smarty.foreach.st_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
        <br />
        <br />
        {foreach from=$reports_results.workshop.rest item=workshop_users name=ws_user_rest}
          {$workshop_users}
          {if !$smarty.foreach.ws_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
      <td class="t_border" nowrap="nowrap" style="background-color: #E1C9A3;">
        {foreach from=$reports_results.administration.rest item=administration_users name=adm_user_rest}
          {$administration_users}
          {if !$smarty.foreach.adm_user_rest.last}
            <br />
          {/if}
        {foreachelse}
          -
        {/foreach}
      </td>
    </tr>
    <tr>
      <td class="t_footer" colspan="8"></td>
    </tr>
  </table>