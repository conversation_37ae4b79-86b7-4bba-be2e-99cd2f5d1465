<?php

/**
 * Customer_Notification_Sender common report
 *
 * @see Birthday_Congratulations
 * @see Search_Contact_Persons
 */
class Customer_Notification_Sender extends Reports {

    /**
     * Gets the settings from the reports table and applies report-specific processing
     *
     * @param Registry $registry - the main registry
     * @param string $report_name - if not specified, report name is taken from class name
     * @return array - parsed and processed report settings
     */
    public static function getReportSettings(&$registry, $report_name = '') {

        $settings = parent::getReportSettings($registry, $report_name ?: strtolower(get_class()));

        // additional processing:

        $notification_types = array('sms', 'email',);
        if (!empty($settings['notification_types'])) {
            $settings['notification_types'] = preg_split('#\s*,\s*#', $settings['notification_types']);
        }
        $settings['notification_types'] =
            isset($settings['notification_types']) && !array_intersect($settings['notification_types'], array('all', 'both')) ?
            array_intersect($settings['notification_types'], $notification_types) :
            $notification_types;

        if (!empty($settings["query_label_{$registry['lang']}"])) {
            $settings["query_label"] = $settings["query_label_{$registry['lang']}"];
        }
        if (empty($settings["query_label"])) {
            $settings["query_label"] = $registry['translater']->translate('reports_customer_groups');
        }

        if (empty($settings['query_options_align_horizontal_max_num'])) {
            $settings['query_options_align_horizontal_max_num'] = 4;
        }

        $settings['required_filters'] = array('notification_type', 'query',);

        return $settings;
    }

    /**
     * Searches for saved filters for report
     *
     * @param Registry $registry - the main registry
     * @param array $filters - additional search filters
     * @return Filter[] - found filter objects
     */
    public static function getQueryFilters(Registry &$registry, array $filters = array()) {
        return Filters::search(
            $registry,
            array_merge_recursive(
                array(
                    'where' => array(
                        "f.module = 'customers'",
                        "f.controller = 'customers'",
                        "f.module_from = 'reports'",
                        "f.controller_from = '" . strtolower(get_class()) . "'",
                        "f.user_defined = 1",
                        "f.added_by = '" . PH_AUTOMATION_USER . "'",
                    ),
                    'sort' => array(
                        'f.id ASC',
                        'f.active DESC',
                    ),
                    'sanitize' => true,
                ),
                $filters
            )
        );
    }

    /**
     * Prepares saved filters as options for report filter
     *
     * @param Registry $registry - the main registry
     * @return [][] - array of options for dropdown/radio
     */
    public static function getQueryOptions(Registry &$registry) {
        return array_map(
            function(Filter $a) use ($registry) {
                $label_param = "label_{$registry['lang']}";
                $settings = General::slashesStrip($a->getParams());
                if (!empty($settings['search_fields'])) {
                    $settings = General::parseSettings($settings['search_fields'], "#^{$label_param}$#");
                } else {
                    $settings = array();
                }
                return array(
                    'option_value' => $a->get('id'),
                    'label' => !empty($settings[$label_param]) ? $settings[$label_param] : $a->get('name'),
                );
            },
            self::getQueryFilters(
                $registry,
                array(
                    'where' => array(
                        "f.active = 1",
                    ),
                )
            )
        );
    }

    /**
     * Performs query with specified filter values and returns found results
     *
     * @param Registry $registry - the main registry
     * @param array $filters - values of report filters to search with
     * @return mixed[]|[][] - found results with/without pagination
     */
    public static function buildQuery(Registry &$registry, array $filters = array()) {

        // we have to handle triggered errors when ADODB logging is on so that
        // report remains functional and causes of errors can be removed.
        // in debug mode only user errors are handled, in production mode all
        // subsequent non-fatal errors are handled in order not to be displayed to user.
        set_error_handler(array(__CLASS__, 'handleError'), (defined('ADODB_ERROR_LOG_TYPE') ? E_USER_ERROR : error_reporting()));

        // set the model lang filter
        $model_lang = (!empty($filters['model_lang']) ? $filters['model_lang'] : $registry['lang']);

        /** @var ADODB_mysqli $db */
        $db = &$registry['db'];

        // get the report settings in array var (for easier use)
        $settings = self::getReportSettings($registry);

        // prepare the array for the final results
        $final_results = array();
        // prepare total number of results for pagination display
        $total = 0;

        $table_name = self::getTemporaryTableName($registry);

        // check if temporary table exists in current database
        // if not, try to create it
        if (!$db->MetaTables(false, false, $table_name) && !self::createTemporaryTable($registry, $table_name)) {
            // if that fails, report cannot work correctly, display and log error, then exit
            $registry['messages']->setError(
                $registry['translater']->translate('error_reports_report_not_set')
            );
            self::logError(__CLASS__);
            return self::exitReport($registry, $filters, $final_results, false);
        }

        // validate filters
        if (!empty($filters['notification_type'])) {
            if (!in_array($filters['notification_type'], $settings['notification_types'])) {
                unset($filters['notification_type']);
            } elseif ($filters['notification_type'] == 'sms' && !$db->MetaTables(false, false, 'sms')) {
                $registry['messages']->setWarning($registry['translater']->translate('error_reports_sms_sending_not_enabled'));
                if (!defined('DISABLE_EMAIL_SENDING')) {
                    define('DISABLE_EMAIL_SENDING', 1);
                }
            }
        }
        if (array_diff_key(array_flip($settings['required_filters']), array_filter($filters))) {
            $registry['messages']->setError(
                $registry['translater']->translate('error_reports_required_filters'),
                implode(
                    ', ',
                    array_keys(
                        array_diff_key(
                            array_flip($settings['required_filters']),
                            array_filter($filters)
                        )
                    )
                )
            );
            return self::exitReport($registry, $filters, $final_results, false);
        }

        // define constants dynamically according to filters in order to
        // display only corresponding notification form (CKEditor).
        // they should not be specified in report settings.
        if (!defined('SEND_SMS')) {
            define('SEND_SMS', $filters['notification_type'] == 'sms');
        }
        if (!defined('SEND_EMAIL')) {
            define('SEND_EMAIL', $filters['notification_type'] == 'email');
        }

        $current_user_id = $registry['currentUser']->get('id');

        // flag for re-generation of temporary table
        if ($registry['request']->isRequested('custom_generate')) {
            // clear previously selected items
            $registry['session']->remove($filters['session_param'], 'selected_items');

            // clear previous data
            self::dropTemporaryTable($registry);

            // get the 'normal' customer ids from saved filters
            $ids = self::getModelIds($registry, $filters['query']);

            // get normal and contact ids and save data into temporary table
            $where = array(
                "`c`.`active`          = '1'",
                "`c`.`deleted_by`      = '0'",
                "`c`.`subtype`         = 'normal'",
                "`c`.`id` IN ('" . implode("', '", $ids) . "')",
            );

            $sql['insert'] = 'INSERT INTO ' . $table_name;
            $sql['select'] = "
                SELECT
                    c.id AS customer_id,
                    IF(c.is_company AND ccp.id IS NOT NULL, ccp.id, c.id) AS recipient_id,
                    '{$current_user_id}' AS user_id,
                    '{$filters['notification_type']}' AS type,
                    0 AS selected";
            $sql['from'] = "
                FROM " . DB_TABLE_CUSTOMERS . " AS c";
            $sql['join_ccp'] = "
                LEFT JOIN " . DB_TABLE_CUSTOMERS . " AS cb
                  ON c.id = cb.parent_customer AND cb.subtype = 'branch'
                LEFT JOIN " . DB_TABLE_CUSTOMERS . " AS ccp
                  ON cb.id = ccp.parent_customer AND ccp.subtype = 'contact' AND ccp.active = 1 AND ccp.deleted_by = 0
                ";
            $sql['where'] = "
                WHERE " . (!empty($where) ? implode("
                  AND ", $where) : '1');
            $sql['group'] = "
                GROUP BY customer_id, recipient_id
                ";
            $sql['having'] = "
                HAVING customer_id != recipient_id
                  OR (
                    SELECT COUNT(ccp0.id)
                    FROM " . DB_TABLE_CUSTOMERS . " AS cb0
                    JOIN " . DB_TABLE_CUSTOMERS . " AS ccp0
                      ON cb0.id = ccp0.parent_customer AND ccp0.subtype = 'contact' AND ccp0.active = 1 AND ccp0.deleted_by = 0
                    WHERE customer_id = cb0.parent_customer AND cb0.subtype = 'branch'
                  ) = 0
                ";

            $db->Execute(implode("\n", $sql));
        }

        $results = self::getCustomData($registry, $filters);

        restore_error_handler();

        return $results;
    }

    /**
     * Gets the 'normal' customer ids according to selected saved filter
     *
     * @param Registry $registry - the main registry
     * @param number $saved_filter - id of saved filter
     * @return number[] - ids of "normal" customers that match search filters
     */
    private static function getModelIds(Registry &$registry, $saved_filter = 0) {
        $saved_filter = self::getQueryFilters(
            $registry,
            array(
                'where' => array(
                    "f.active = 1",
                    "f.id = '{$saved_filter}'",
                ),
            ));
        $saved_where = array();
        if ($saved_filter) {
            /** @var Filter $saved_filter */
            $saved_filter = reset($saved_filter);
            $saved_where = General::slashesStrip($saved_filter->getParams());
            $saved_where = !empty($saved_where['search_fields']) ? $saved_where['search_fields'] : '';
            $saved_where = General::parseSettings($saved_where, '#^where$#', true);
            // allow search with valid saved filter with no conditions
            $saved_where = !empty($saved_where['where']) ? $saved_where['where'] : array('c.id IS NOT NULL');
        }
        // do not allow search without found saved filter
        if (!$saved_where) {
            $saved_where = array('c.id = 0');
        }
        $search_filters = array(
            'where' => $saved_where,
            'sort' => array(
                'c.id ASC',
                'c.active DESC',
            ),
        );

        $ids = Customers::getIds($registry, $search_filters);

        // display user-error and log system-error
        if ($registry['db']->ErrorNo()) {
            $registry['messages']->setError(
                $registry['translater']->translate('error_reports_report_not_set')
            );
            self::logError(__CLASS__);
        }

        if (!is_array($ids)) {
            $ids = array();
        }

        return $ids;
    }

    /**
     * Keeps first found contact of a type for customer/contact
     *
     * @param array $a - result row
     */
    private static function processContacts(array &$a) {
        foreach (array('gsm', 'email') as $contact) {
            $a["{$contact}_note"] = '';
            if (!empty($a[$contact])) {
                $a[$contact] = explode("\n", $a[$contact]);
                $a[$contact] = reset($a[$contact]);
                if (strpos($a[$contact], '|') !== false) {
                    list($a[$contact], $a["{$contact}_note"]) = explode('|', $a[$contact], 2);
                }
            }
        }
    }

    /**
     * Get data for customers to send e-mails and smses to.
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @return array - array with results
     */
    public static function getCustomData(Registry &$registry, array $filters = array()) {

        $model_lang = $registry['lang'];
        $table_name = self::getTemporaryTableName($registry);
        $current_user_id = $registry['currentUser']->get('id');
        $db = &$registry['db'];

        // filter should always be set to either email or sms value
        if (empty($filters['notification_type'])) {
            $filters['notification_type'] = '';
        }

        // fetch data based on current contents of temporary table
        $sql['insert'] = '';
        $sql['select'] = "
            SELECT SQL_CALC_FOUND_ROWS
                v.recipient_id AS idx,
                v.recipient_id,
                v.customer_id,
                v.type,
                TRIM(CONCAT(ci18n.name, ' ', ci18n.lastname)) AS customer_name,
                cti18n.name AS type_name,
                TRIM(CONCAT(ri18n.name, ' ', ri18n.lastname)) AS recipient_name,
                r.subtype,
                r.ucn,
                ri18n.position,
                IF(r.subtype = 'contact', r.admit_VAT_credit, 0) AS financial_person,
                r.is_main,
                r.email,
                IFNULL(r.gsm, '') AS gsm";
        $sql['from'] = "
            FROM `{$table_name}` AS v
            LEFT JOIN " . DB_TABLE_CUSTOMERS . " AS r
              ON v.recipient_id = r.id
            LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ri18n
              ON r.id = ri18n.parent_id AND ri18n.lang = '{$model_lang}'
            LEFT JOIN " . DB_TABLE_CUSTOMERS . " AS c
              ON v.customer_id = c.id
            LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci18n
              ON c.id = ci18n.parent_id AND ci18n.lang = '{$model_lang}'
            LEFT JOIN " . DB_TABLE_CUSTOMERS_TYPES_I18N . " AS cti18n
              ON c.type = cti18n.parent_id AND cti18n.lang = '{$model_lang}'";
        $sql['where'] = "
            WHERE v.user_id = '{$current_user_id}'
              AND v.type = '{$filters['notification_type']}'" .
            (!empty($filters['selected']) ? "
              AND v.selected = 1" : '') .
            (!empty($filters['recipient_id']) ? "
              AND v.recipient_id = '{$filters['recipient_id']}'" : '');
        $sql['group'] = "
            GROUP BY r.id";
        $sql['order'] = "
            ORDER BY recipient_name ASC";

        // offset and limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? ' LIMIT ' . $filters['limit'] . "\n" : '';

        $final_results = $db->GetAssoc(implode("\n", $sql)) ?: array();

        // if no results, don't display email/sms sending elements in interface
        if (empty($final_results) && !defined('DISABLE_EMAIL_SENDING')) {
            define('DISABLE_EMAIL_SENDING', 1);
        }

        array_walk($final_results, array('self', 'processContacts'));

        if (!empty($filters['paginate'])) {
            $results = array($final_results, $db->GetOne('SELECT FOUND_ROWS()'));
        } else {
            $results = $final_results;
        }

        return $results;
    }

    /**
     * Overwrite function. Sets temporary selection of records selected by
     * current user.
     *
     * @param Registry $registry - the main registry
     * @param array $selected - selected items for report - taken from session
     * @return bool - result of the operation
     */
    public static function setTemporarySelection(&$registry, $selected) {
        $table = self::getTemporaryTableName($registry);

        $where = array();
        $where[] = "user_id = '{$registry['currentUser']->get('id')}'";

        if ($selected['ids']) {
            $where[] = 'CONCAT(type, \'_\', recipient_id) IN (\'' . implode('\', \'', $selected['ids']) . '\')';
        } elseif ($selected['select_all']) {
            if ($selected['ignore_ids']) {
                $where[] = 'CONCAT(type, \'_\', recipient_id) NOT IN (\'' . implode('\', \'', $selected['ignore_ids']) . '\')';
            }
        }

        return !!$registry['db']->Execute("
            UPDATE {$table}
            SET `selected` = 1
            WHERE " . implode("\n  AND ", $where)
        );
    }

    /**
     * Creates additional table for report if it does not exist.
     *
     * @param Registry $registry - the main registry
     * @param string $table_name - name of temporary table to create, defined by report name if not set
     * @return bool - result of the operation
     */
    public static function createTemporaryTable(Registry &$registry, $table_name = '') {
        return !!$registry['db']->Execute("
            CREATE TABLE IF NOT EXISTS " . ($table_name ?: self::getTemporaryTableName($registry)) . " (
            `customer_id` int(11) DEFAULT NULL COMMENT 'id of normal customer',
            `recipient_id` int(11) DEFAULT NULL COMMENT 'id of person customer or contact',
            `user_id` int(11) DEFAULT NULL,
            `type` enum('email','sms') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'email',
            `selected` tinyint(1) NOT NULL DEFAULT 0,
            UNIQUE KEY `recipient_id` (`recipient_id`,`user_id`,`type`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        );
    }

    /**
     * Overwrite function. This report does not drop temporary table
     * but removes records of current user.
     *
     * @param Registry $registry - the main registry
     * @return bool - result of the operation
     */
    public static function dropTemporaryTable(&$registry) {
        return !!$registry['db']->Execute("
            DELETE FROM " . self::getTemporaryTableName($registry) . "
            WHERE `user_id` = '{$registry['currentUser']->get('id')}'"
        );
    }

    private static function exitReport(&$registry, $filters, $final_results, $success = false, $error = '') {

        restore_error_handler();

        if (!$success) {
            $final_results['additional_options']['failed'] = true;
            //$final_results['additional_options']['dont_show_export_button'] = true;
            if (!empty($error)) {
                $final_results['additional_options']['error'] = $registry['translater']->translate($error);
            }
        }

        // don't display email/sms sending elements in interface
        if (!(array_diff_key($final_results, array('additional_options' => ''))) && !defined('DISABLE_EMAIL_SENDING')) {
            define('DISABLE_EMAIL_SENDING', 1);
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }
        return $results;
    }

    /**
     * Logs errors/exceptions to db
     *
     * @param string $log_key - key to identify event by in logs
     * @param string $message - system-generated data for raised error/exception
     */
    private static function logError($log_key = '', $message = '') {
        $registry = $GLOBALS['registry'];
        $message = implode(
            "\n",
            array_merge(
                ($message ? array($message) : array()),
                array_values($registry['messages']->getErrors()),
                ($registry['db']->ErrorNo() ? array("DB error: ({$registry['db']->ErrorNo()}) {$registry['db']->ErrorMsg()}") : array()),
                array('backtrace:', var_export(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), 1))
            ));
        General::log($registry, $log_key ?: __CLASS__, $message);
    }

    /**
     * Handles user-defined errors (E_USER_ERROR) from ADODB when its error
     * logging is turned on, in order to be able to access report and correct
     * the cause for them.
     * Method is public so that it can be called from other classes when
     * error occurs in their methods.
     *
     * @param int $severity - the level of the error raised
     * @param string $message - the error message
     * @param string $file - the filename that the error was raised in
     * @param int $line - the line number the error was raised at
     * @return bool - true = the standard PHP error handler is completely bypassed
     * @see http://php.net/manual/en/function.set-error-handler.php
     */
    public static function handleError($severity, $message, $file, $line) {
        // in development environment non-fatal errors will be displayed
        // we change their level in order not to halt script execution
        if (defined('ADODB_ERROR_LOG_TYPE') && $severity == E_USER_ERROR) {
            trigger_error($message, E_USER_WARNING);
        }
        return true;
    }
}
