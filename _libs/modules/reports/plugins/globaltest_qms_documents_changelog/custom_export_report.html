{if !$prepare_placeholder}
  <html>
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    </head>
    <body>
{/if}
  {if !$prepare_placeholder || $prepare_placeholder eq 'qms_documents_changelog_table'}
    <table border="1" cellpadding="2" cellspacing="0">
      <tr>
        <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;" width="70">{#reports_enter_date#|escape}</td>
        <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;" width="500">{#reports_qms_doc#|escape}</td>
        <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;" width="60">{#reports_current_version#|escape}</td>
        <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;" width="140">{#reports_change_reason#|escape}</td>
        <td style="text-align: center; vertical-align: middle; background-color: #DFDFDF;" width="300">{#reports_proposal_description#|escape}</td>
      </tr>
      {foreach from=$reports_results.qms_documents item=qms_document}
        {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
        <tr>
          <td style="text-align: center">{$qms_document.enter_date|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$qms_document.qms_doc|escape|default:"&nbsp;"}</td>
          <td style="text-align: center">{$qms_document.current_version|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$qms_document.change_reason|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$qms_document.proposal_description|escape|default:"&nbsp;"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="5"><span color="red">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  {/if}
{if !$prepare_placeholder}
    </body>
  </html>
{/if}
