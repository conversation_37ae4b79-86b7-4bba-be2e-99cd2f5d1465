reports_filter_customer                     = Kunden
reports_filter_customer_help                = Kunden

reports_filter_invoice_num                  = Rechnung #
reports_filter_invoice_num_help             = Rechnung #

reports_filter_issue_date                   = Rechnungsdatum
reports_filter_issue_date_help              = Rechnungsdatum

reports_filter_company_data                 = Cashbox / Bank
reports_filter_company_data_help            = Cashbox / Bank

reports_filter_article                      = Lizenz
reports_filter_article_help                 = Lizenz

reports_filter_employee                     = Mitarbeiter
reports_filter_employee_help                = Mitarbeiter

reports_filter_article_deliverer            = Objekt der Ausgaben
reports_filter_article_deliverer_help       = Objekt der Ausgaben

reports_filter_employeeMoney                = Person (Geld gegeben)
reports_filter_employeeMoney_help           = Person (Geld gegeben)

reports_filter_reqStatus                    = Obligations (Status)
reports_filter_reqStatus_help               = Obligations (Status)
reports_filter_reqStatus_option_1           = Bezahlt
reports_filter_reqStatus_option_2           = Teil
reports_filter_reqStatus_option_3           = Unbezahlte

reports_filter_expenses                     = Art der Ausgaben
reports_filter_expenses_help                = Art der Ausgaben
reports_filter_expenses_type                = Dokumentenart
reports_filter_currency                     = Währung
reports_filter_expenses_option_1            = mit Rechnung
reports_filter_expenses_option_2            = ohne Rechnung

reports_error_complete_required             = Bitte füllen Sie die erforderlichen Filter!
reports_error_no_records_found              = <span class = "unverzichtbar"> Keine Ergebnisse gefunden! </ span>


reports_table_customer                      = Kunden
reports_table_expense_type                  = Kosten (Art)
reports_table_invoice_num                   = Datum / №
reports_table_employeeMoney                 = Person (Geld gegeben)
reports_table_total                         = Gesamt
reports_table_total_vat                     = Gesamt Vat
reports_table_total_with_vat                = Gesamt Mit Vat
reports_table_employee                      = Mitarbeiter
reports_table_article                       = Lizenz
reports_table_article_deliverer             = Objekt der Ausgaben
reports_table_tips_expense                  = Ausgaben Tipps
reports_table_measure                       = Maß
reports_table_quantity                      = Lager
reports_table_subtotal                      = Summe
reports_table_issue_date                    = Zahlungen № / Datum / Anzahl
reports_table_summary_article               = Artikel
reports_table_summary_total_novat           = Gesamt (ohne MwSt.)
reports_table_sum_paid                      = Ganz bezahlt
reports_table_sum_left                      = Noch zu zahlen
reports_table_tags                          = Stichworte
reports_table_currency                      = Währung

reports_services_merchandise                = Category services/mucrchandise
reports_results_filter_label                = Results
reports_results_filter_1                    = Detailed
reports_results_filter_2                    = Summary

reports_filter_unallocated_rko_pn           = Nicht verteilt RKO/PN
reports_table_rko_num                       = RKO/PN num
reports_table_payment_date                  = Zahlungsdatum
reports_table_payment_sum                   = Wert
reports_table_payment_reason                = Grund

reports_filter_reason_rko_pn                = Grund RKO/PN
