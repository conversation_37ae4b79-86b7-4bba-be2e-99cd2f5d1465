<?php
    Class Colliers_Insufficient_Collateral Extends Reports {

        public static $vat_multiplier = 1.2;

        public static function buildQuery(&$registry, $filters = array()) {

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $table_name = Reports::getTemporaryTableName($registry);

            if ($registry->get('report_type')) {
                $report = $registry->get('report_type');
                $report = Reports::getReports($registry, $report);
                $report = $report[0];
            } else {
                return false;
            }

            if ($registry['request']->isRequested('custom_generate') || $registry['action'] == 'dashlet') {
                //the following must be here for this kind of report
                $query = 'DROP TABLE IF EXISTS ' . $table_name;
                $records = $registry['db']->Execute($query);

                $sql['create'] = 'CREATE TABLE IF NOT EXISTS ' . $table_name . "\n" .
                                 '  (idx int(11) AUTO_INCREMENT,
                                     selected tinyint(1),
                                     document_id int(11),
                                     document_num varchar(255),
                                     document_validity date,
                                     document_renew_date date,
                                     object_name varchar(255),
                                     contract_id int(11),
                                     contract_num varchar(255),
                                     contract_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                                     customer int(11),
                                     branch int(11),
                                     contact_person int(11),
                                     trademark int(11),
                                     cstm_administrative int(11),
                                     cstm_adm_email varchar(255),
                                     fixed_deposit double(15,6),
                                     fixed_deposit_currency varchar(3),
                                     deposit_periods int(11),
                                     currency varchar(3) NULL DEFAULT NULL,
                                     deposit_for_periods double(15,6) NULL DEFAULT NULL,
                                     paid_deposit double(15,6) NULL DEFAULT NULL,
                                     PRIMARY KEY(idx)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci AS ' . "\n";
                $sql['select'] = 'SELECT NULL as idx, 0 as selected,' . "\n" .
                                 'd.id AS document_id, d.full_num AS document_num, d.validity_term AS document_validity,' . "\n" .
                                 'DATE_SUB(d.validity_term, INTERVAL 15 DAY) AS document_renew_date,' . "\n" .
                                 'd.customer, d.branch, d.contact_person, d.trademark, dcstm3.value AS object_name,' . "\n" .
                                 'c.id AS contract_id, c.num as contract_num, ci18n.name AS contract_name, c.cstm_administrative, c.cstm_adm_email,' . "\n" .
                                 'ccstm1.value AS fixed_deposit, ccstm2.value AS fixed_deposit_currency, ccstm3.value AS deposit_periods,' . "\n" .
                                 'NULL AS currency, NULL AS deposit_for_periods, NULL AS paid_deposit';
                $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                               '    ON fm.model = \'Document\' AND fm.model_type = d.type AND fm.name = \'contract_num\'' . "\n" .
                               'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm' . "\n" .
                               '    ON dcstm.model_id = d.id AND dcstm.var_id = fm.id' . "\n" .
                               'JOIN ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                               '    ON c.id = dcstm.value AND c.status = \'closed\' AND c.type = 1 AND c.substatus = 1 AND c.deleted_by = 0 AND c.active = 1' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS ci18n' .
                               '    ON c.id = ci18n.parent_id AND ci18n.lang = "' . $registry['lang'] . '"' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm3' . "\n" .
                               '    ON fm3.model = \'Document\' AND fm3.model_type = d.type AND fm3.name = \'code_unit\'' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm3' . "\n" .
                               '    ON dcstm3.model_id = d.id AND dcstm3.var_id = fm3.id' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm4' . "\n" .
                               '  ON fm4.model = \'Contract\' AND fm4.model_type = c.type AND fm4.name = \'fix_obezpechenie\'' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm1' . "\n" .
                               '  ON ccstm1.model_id = c.id AND ccstm1.var_id = fm4.id' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm5' . "\n" .
                               '  ON fm5.model = \'Contract\' AND fm5.model_type = c.type AND fm5.name = \'currency_fixo\'' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm2' . "\n" .
                               '  ON ccstm2.model_id = c.id AND ccstm2.var_id = fm5.id' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm6' . "\n" .
                               '  ON fm6.model = \'Contract\' AND fm6.model_type = c.type AND fm6.name = \'num_period_obez\'' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS ccstm3' . "\n" .
                               '  ON ccstm3.model_id = c.id AND ccstm3.var_id = fm6.id';
                $sql['where'][] = 'WHERE d.type = 22' . "\n" .
                                  '  AND d.substatus != 18' . "\n" .
                                  '  AND d.deleted_by = 0' . "\n" .
                                  '  AND d.active = 1';

                //add filter for a customer if requested
                if (!empty($filters['customer'])) {
                    $sql['where'][] = 'c.customer = ' . $filters['customer'];
                }

                //add filter for a trademark if requested
                if (!empty($filters['trademark'])) {
                    $sql['where'][] = 'c.trademark = ' . $filters['trademark'];
                }

                $sql['where'] = implode(' AND ', $sql['where']);

                $query = implode("\n", $sql);
                $records = $registry['db']->Execute($query);
            }

            $sql['create'] = '';
            $sql['select'] = 'SELECT v.*, ' . "\n" .
                             '  CONCAT_WS(\' \', ci18n.name, ci18n.lastname) AS customer_name,' . "\n" .
                             '  ni18n.name AS trademark_name' . "\n";
            $sql['from'] =   'FROM ' . $table_name . ' AS v' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON v.customer = ci18n.parent_id AND ci18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                             '  ON v.trademark = ni18n.parent_id AND ni18n.lang = \'' . $registry['lang'] . '\'' . "\n";

            $sql['where'] = '';
            $sql['having'] = '';

            //!!! NO PAGINATION FOR THIS REPORT !!!
            $filters['limit'] = '';
            //$filters['paginate'] = '';

            //limit (for pagination)
            $sql['limit'] = (!empty($filters['limit'])) ? ' LIMIT ' . $filters['limit'] . "\n" : '';

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);

            //prepare deposit amount
            //The great function of Iliya is used
            //gt2_include_only is a parameter that excludes all the GT2 rows where the field is different from the specified values
            $params = array('gt2_include_only' => array('free_field4' => array('yes_include_collateral')),
                            'date_from' => General::strftime('%Y-%m-01'));
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $prec = $registry['config']->getSectionParams('precision');

            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $main_currency = Finance_Currencies::getMain($registry);

            foreach ($records as $key => $record) {
                $records[$key]['deposit_for_periods'] = 0;
                $records[$key]['deposit_for_periods_currency'] = $main_currency;
                if (!$record['deposit_periods']) {
                    $record['fixed_deposit'] *= Finance_Currencies::getRate($registry, $record['fixed_deposit_currency']);
                    $records[$key]['fixed_deposit'] = round($record['fixed_deposit'], $prec['gt2_total_with_vat']);
                } else {
                    $params['contracts_ids'] = $record['contract_id'];
                    $params['date_to'] = General::strftime('%Y-%m-%d',
                                               strtotime('+ ' . $record['deposit_periods'] . ' month',
                                               strtotime($params['date_from'])));

                    $gt2_rows = Contracts::getAmountDue($registry, $params);
                    $gt2_rows = array_shift($gt2_rows);

                    if (!empty($gt2_rows['rows'])) {
                        foreach ($gt2_rows['rows'] as $idx => $row) {
                            if ($row['template_currency'] != $main_currency) {
                                $rate = Finance_Currencies::getRate($registry, $row['template_currency'], $main_currency);
                            } else {
                                $rate = 1;
                            }
                            $subtotal = $row['price'] * $row['quantity'] * $rate;
                            $records[$key]['deposit_for_periods'] += $subtotal;
                        }
                        $records[$key]['deposit_for_periods'] *= self::$vat_multiplier;
                    }
                }

                $records[$key]['final_currency'] = $main_currency;
                //get contract administrative contact
                $contract = new Contract($registry, array());
                $contract->set('id', $record['contract_id'], true);
                $contract->set('cstm_administrative', $record['cstm_administrative'], true);
                $contract->set('cstm_adm_email', $record['cstm_adm_email'], true);
                $records[$key]['administrative'] = array_merge(array($contract->getCstmAdministrativeData()),
                                                               $contract->getContactCcData('cstm', 'adm'));

                //get last sent date
                $query = 'SELECT sent FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model =\'Report\' AND model_id="' . $report->get('id') . '"' . "\n" .
                         '  AND extra RLIKE \'customer( )*:=( )*' . $record['customer'] . '\'' . "\n" .
                         'ORDER BY sent DESC' . "\n" .
                         'LIMIT 1';
                $records[$key]['last_sent'] = $registry['db']->GetOne($query);

                $records[$key]['deposit_for_periods'] = round($records[$key]['deposit_for_periods'], $prec['gt2_total_with_vat']);

                //get secured deposits
                $query = 'SELECT d.id, dcstm1.value AS value, dcstm2.value AS currency' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm1' . "\n" .
                         '  ON fm1.model = "Document" AND fm1.model_type = d.type' . "\n" .
                         ' AND (fm1.name = "value_bankg" OR fm1.name = "deposit_value")' . "\n" .
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm1' . "\n" .
                         '  ON dcstm1.model_id = d.id AND dcstm1.var_id = fm1.id' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
                         '  ON fm2.model = "Document" AND fm2.model_type = d.type' . "\n" .
                         ' AND (fm2.name = "bankbg_currency" OR fm2.name = "deposit_currency")' . "\n" .
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm2' . "\n" .
                         '  ON dcstm2.model_id = d.id AND dcstm2.var_id = fm2.id' . "\n" .
                         'WHERE (d.type = 22 AND d.substatus IN (41, 17, 27)' . "\n" .
                         '   OR d.type = 25 AND d.substatus IN (42, 43))' . "\n" .
                         '  AND d.customer = ' . $record['customer'] . "\n" .
                         '  AND d.trademark = ' . $record['trademark'] . "\n" .
                         '  AND d.active = 1 AND d.deleted_by = 0';
                $deposits = $registry['db']->GetAssoc($query);

                $records[$key]['paid_deposit'] = 0;
                if (!empty($deposits)) {
                    foreach ($deposits as $k => $v) {
                        if (empty($v['value']) || empty($v['currency'])) {
                            unset($deposits[$k]);
                            continue;
                        }
                        $records[$key]['paid_deposit'] += $v['value'] * Finance_Currencies::getRate($registry, $v['currency']);
                    }

                    if (!empty($deposits)) {

                        //get adopted deposits
                        $query = 'SELECT d.id, dcstm1.value AS value, dcstm2.value AS currency' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                                 ' ON dr.parent_id = d.id AND dr.link_to IN (' . implode(', ', array_keys($deposits)) . ') AND dr.link_to_model_name="Document"' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm1' . "\n" .
                                 '  ON fm1.model = "Document" AND fm1.model_type = d.type' . "\n" .
                                 ' AND (fm1.name = "value_bankg" OR fm1.name = "value_digest")' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm1' . "\n" .
                                 '  ON dcstm1.model_id = d.id AND dcstm1.var_id = fm1.id' . "\n" .
                                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
                                 '  ON fm2.model = "Document" AND fm2.model_type = d.type' . "\n" .
                                 ' AND (fm2.name = "bankbg_currency" OR fm2.name = "value_digest_currency")' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dcstm2' . "\n" .
                                 '  ON dcstm2.model_id = d.id AND dcstm2.var_id = fm2.id' . "\n" .
                                 'WHERE d.type = 33 AND d.active = 1 AND d.deleted_by = 0' . "\n";
                        $deposits = $registry['db']->GetAssoc($query);

                        foreach ($deposits as $k => $v) {
                            if (empty($v['value']) || empty($v['currency'])) {
                                unset($deposits[$k]);
                                continue;
                            }
                            $records[$key]['paid_deposit'] -= $v['value'] * Finance_Currencies::getRate($registry, $v['currency']);
                        }
                    }
                }
                $records[$key]['paid_deposit'] = round($records[$key]['paid_deposit'], $prec['gt2_total_with_vat']);

                //update calculated data
                $set = array(sprintf('currency = "%s"', $records[$key]['final_currency']),
                             sprintf('deposit_for_periods= "%s"', $records[$key]['deposit_for_periods']),
                             sprintf('paid_deposit = "%s"', $records[$key]['paid_deposit']));
                $query = 'UPDATE ' . $table_name . ' SET' . "\n" . implode(",\n", $set) . "\n" .
                         'WHERE idx = ' . $record['idx'];
                $registry['db']->Execute($query);
            }

            if (!empty($filters['paginate'])) {
                //get the total count
                /*if ($sql['limit']) {
                    //get the total number of records for this search
                    $sql['select'] = 'SELECT COUNT(idx) AS total';
                    $sql['limit'] = '';
                    $sql['order'] = '';
                    $sql['group_by'] = '';
                    $query = implode("\n", $sql);
                    $total = $registry['db']->GetOne($query);
                } else {
                    //there is no limit set,
                    //get the count from the found records
                    $total = count($records);
                }
                $results = array($records, $total);*/
                $results = array($records, 0);
            } else {
                //no pagination required return only the models
                $results = $records;
            }

            return $results;
        }

        public static function getCustomData(&$registry, $filters = array()) {

            $table_name = Reports::getTemporaryTableName($registry);

            $sql['select'] = 'SELECT v.*, c.date_validity as contract_validity, ci18n.name AS customer_name, ni18n.name AS trademark_name,' . "\n" .
                             'fci18n.name AS company_name, oi18n.name AS office_name, c.date_sign as contract_sign';
            $sql['from'] = 'FROM ' . $table_name . ' AS v' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                           '    ON c.id = v.contract_id' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                           '  ON v.customer = ci18n.parent_id AND ci18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                           '  ON v.trademark = ni18n.parent_id AND ni18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                           '  ON c.company = fci18n.parent_id AND fci18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                           '  ON c.office = oi18n.parent_id AND oi18n.lang = \'' . $registry['lang'] . '\'' . "\n";

            if (empty($filters)) {
                $sql['where'] = ' WHERE selected = 1';
            } else {
                $sql['where'] = 'WHERE ' . implode("\nAND ", $filters);
            }
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);

            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $prec = $registry['config']->getSectionParams('precision');

            foreach ($records as $k => $v) {
                //get contract administrative contact
                $contract = new Contract($registry, array());
                $contract->set('id', $v['contract_id'], true);
                $contract->set('cstm_administrative', $v['cstm_administrative'], true);
                $contract->set('cstm_adm_email', $v['cstm_adm_email'], true);
                $records[$k]['administrative'] = array_merge(array($contract->getCstmAdministrativeData()),
                                                                   $contract->getContactCcData('cstm', 'adm'));
                if ($v['deposit_for_periods'] > 0) {
                    $records[$k]['document_bank_deposit'] = round($v['deposit_for_periods'], $prec['gt2_total_with_vat']);
                } else {
                    $records[$k]['document_bank_deposit'] = round($v['fixed_deposit'], $prec['gt2_total_with_vat']);
                }
                $records[$k]['paid_deposit'] = round($records[$k]['paid_deposit'], $prec['gt2_total_with_vat']);
                $records[$k]['difference'] = round($records[$k]['document_bank_deposit'] - $records[$k]['paid_deposit'], $prec['gt2_total_with_vat']);
            }

            return $records;
        }
    }
?>
