<?php

/**
 * Byfar_Request_Management custom report
 */
class Byfar_Sales_Status_Report extends Reports {

    /**
     * Performs query with specified filter values and returns found results
     *
     * @param Registry $registry - the main registry
     * @param array $filters - values of report filters to search with
     * @return mixed[]|[][] - found results with/without pagination
     */
    public static function buildQuery(Registry &$registry, array $filters = array()) {
        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        // get variable ids
        $contract_vars = array(
            CON_ORDER_TYPE,
            CON_PO_NUMBER,
            CON_SHIPPING_ADDRESS,
            CON_ACCOUNT_MANAGER,
            CON_WAREHOUSE,
            CON_SEASON,
            CON_YEAR,
            CON_DELIVERY_DATE,
            'currency'
        );
        $customer_vars = array(
            CUST_REGION
        );

        $nom_types_included = preg_split('#\s*,\s*#', NOM_TYPE_ARTICLE);

        // get account managers
        $account_managers = array();
        if (count(array_filter($filters['account_manager']))) {
            $account_managers = array_filter($filters['account_manager']);
        } else {
            $report = $registry->get('report_type');
            require_once PH_MODULES_DIR . 'reports/models/report.filters.php';
            require_once PH_MODULES_DIR . 'reports/plugins/' . $report['name'] . '/custom.report.filters.php';
            $defineCustomFilters = new Custom_Report_Filters($registry, $report['name']);
            $account_managers = array_column($defineCustomFilters->getAccountManagerOptions($registry), 'option_value');
        }

        $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE model="Contract" AND model_type="' . CON_TYPE_ORDER . '"' . "\n" .
                 '  AND name IN ("' . implode('", "', $contract_vars) . '")';
        $contract_vars = $registry['db']->GetAssoc($query);

        $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE model="Customer" AND model_type="' . CUST_TYPE_CUSTOMER . '"' . "\n" .
                 '  AND name IN ("' . implode('", "', $customer_vars) . '")';
        $customer_vars = $registry['db']->GetAssoc($query);

        $sql = 'SELECT c.id as idx, c.id, c.num, c.customer, CONCAT(cust_i18n.name, " ", cust_i18n.lastname) as customer_name, ' . "\n" .
               '       c_ord_tp.value as order_type, order_nm.name as order_type_name, c.date_start as date_start, region_nm.name as city, cl.country_name as country, ' . "\n" .
               '       c_po_nmbr.value as po_number, c_ship_adr.value as shipping_address, c_wh.value as warehouse, wh.name as warehouse_name,' . "\n" .
               '       c_acc.value as account_manager, CONCAT(ui18n.firstname, " ", ui18n.lastname) as account_manager_name, c_curr.value as currency, c_curr.value as filter_currency,' . "\n" .
               '       c_seas.value as season, nom_season.name as season_name, c_delivery_date.value as delivery_date, c_yr.value as year, 0 as rowspan' . "\n" .
               ' FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS ci18n' . "\n" .
               '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS cust' . "\n" .
               '  ON (c.customer=cust.id)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_COUNTRY_LIST . ' AS cl' . "\n" .
               '  ON (cust.country=cl.country_code AND cl.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS cust_i18n' . "\n" .
               '  ON (cust.id=cust_i18n.parent_id AND cust_i18n.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS cust_cstm_region' . "\n" .
               '  ON (cust_cstm_region.model_id=cust.id AND cust_cstm_region.var_id="' . $customer_vars[CUST_REGION] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS region_nm' . "\n" .
               '  ON (cust_cstm_region.value=region_nm.parent_id AND region_nm.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_acc' . "\n" .
               '  ON (c_acc.model_id=c.id AND c_acc.var_id="' . $contract_vars[CON_ACCOUNT_MANAGER] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
               '  ON (c_acc.value=ui18n.parent_id AND ui18n.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_ord_tp' . "\n" .
               '  ON (c_ord_tp.model_id=c.id AND c_ord_tp.var_id="' . $contract_vars[CON_ORDER_TYPE] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS order_nm' . "\n" .
               '  ON (c_ord_tp.value=order_nm.parent_id AND order_nm.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_po_nmbr' . "\n" .
               '  ON (c_po_nmbr.model_id=c.id AND c_po_nmbr.var_id="' . $contract_vars[CON_PO_NUMBER] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_ship_adr' . "\n" .
               '  ON (c_ship_adr.model_id=c.id AND c_ship_adr.var_id="' . $contract_vars[CON_SHIPPING_ADDRESS] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_wh' . "\n" .
               '  ON (c_wh.model_id=c.id AND c_wh.var_id="' . $contract_vars[CON_WAREHOUSE] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_I18N . ' AS wh' . "\n" .
               '  ON (c_wh.value=wh.parent_id AND wh.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_seas' . "\n" .
               '  ON (c_seas.model_id=c.id AND c_seas.var_id="' . $contract_vars[CON_SEASON] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_curr' . "\n" .
               '  ON (c_curr.model_id=c.id AND c_curr.var_id="' . $contract_vars['currency'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_season' . "\n" .
               '  ON (c_seas.value=nom_season.parent_id AND nom_season.lang="' . $model_lang . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_yr' . "\n" .
               '  ON (c_yr.model_id=c.id AND c_yr.var_id="' . $contract_vars[CON_YEAR] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_delivery_date' . "\n" .
               '  ON (c_delivery_date.model_id=c.id AND c_delivery_date.var_id="' . $contract_vars[CON_DELIVERY_DATE] . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
               ' ON (gt2.model_id=c.id AND gt2.model="Contract"' . (!empty(array_filter($filters['article'])) ? ' AND gt2.article_id IN ("' . implode('","', array_filter($filters['article'])) . '")' : '') . ')' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' AS gt2_nom' . "\n" .
               '  ON (gt2_nom.id=gt2.article_id AND gt2_nom.type IN ("' . implode('","', $nom_types_included) . '"))' . "\n";

        $where = [];
        $where[] = 'c.active=1';
        $where[] = 'c.deleted_by=0';
        $where[] = 'c.status="closed"';
        $where[] = 'c.subtype="contract"';
        $where[] = 'c.type="' . CON_TYPE_ORDER . '"';
        $where[] = 'c_acc.value IN ("' . implode('","', $account_managers) . '")';
        if (!empty($filters['customer'])) {
            $where[] = 'c.customer=' . $filters['customer'];
        }
        if (!empty(array_filter($filters['year']))) {
            $where[] = 'c_yr.value IN ("' . implode('","', array_filter($filters['year'])) . '")';
        }
        if (!empty(array_filter($filters['season']))) {
            $where[] = 'c_seas.value IN ("' . implode('","', array_filter($filters['season'])) . '")';
        }
        if (!empty(array_filter($filters['warehouse']))) {
            $where[] = 'c_wh.value IN ("' . implode('","', array_filter($filters['warehouse'])) . '")';
        }
        if (!empty($filters['period_from'])) {
            $where[] = 'c.date_start>="' . $filters['period_from'] . '"';
        }
        if (!empty($filters['period_to'])) {
            $where[] = 'c.date_start<="' . $filters['period_to'] . '"';
        }

        $sql .= 'WHERE ' . implode(' AND ', $where) . "\n" .
                'GROUP BY c.id' ;
        $contracts = $registry['db']->GetAssoc($sql);

        // get the data for products for the selected contracts
        if (!empty($contracts)) {
            foreach ($contracts as $con_id => $con) {
                $contracts[$con_id]['gt2'] = array();
            }

            // get additional vars for nomenclatures
            $nom_vars = array(
                NOM_VAR_COLOR,
                NOM_VAR_MODEL,
                NOM_VAR_SKU_CATEGORY,
                NOM_VAR_MATERIAL,
                NOM_VAR_PARENT_PRODUCT,
            );

            $query = 'SELECT name, GROUP_CONCAT(id) FROM ' . DB_TABLE_FIELDS_META . "\n" .
                     'WHERE model="Nomenclature" AND model_type IN ("' . implode('","', $nom_types_included) . '")' . "\n" .
                     '  AND name IN ("' . implode('", "', $nom_vars) . '")' . "\n" .
                     'GROUP BY name' . "\n";
            $nom_vars = $registry['db']->GetAssoc($query);

            // get the additional vars for nomenclatures
            $sql = 'SELECT c.id as contract_id, gt2.id as gt2_id, ni18n.name as article_name, gt2.article_id as article_id, ' . "\n" .
                   '       gt2.quantity as total_quantity, gt2.free_field4 as size, ni18n_size.name as size_name, ' . "\n" .
                   '       nom_model.value as model, ni18n_mod.name as model_name, nom_parent_product.value as parent_product, ' . "\n" .
                   '       gt2.free_field2 as color, ni18n_col.name as color_name, nom_sku_cat.value as sku_category, n.type as article_type, ' . "\n" .
                   '       nci18n_sku_cat.name as sku_category_name, nom_mat.value as material, gt2.price as unit_price, gt2.subtotal_with_discount as total_price ' . "\n" .
                   'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                   'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                   ' ON (gt2.model="Contract" AND gt2.model_id=c.id AND c.id IN ("' . implode('","', array_keys($contracts)) . '")' . (!empty(array_filter($filters['article'])) ? ' AND gt2.article_id IN ("' . implode('","', array_filter($filters['article'])) . '")' : '') . ')' . "\n" .
                   'INNER JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                   ' ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $model_lang . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   ' ON (n.id=gt2.article_id AND n.type IN ("' . implode('","', $nom_types_included) . '"))' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                   ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_model' . "\n" .
                   ' ON (nom_model.model_id=n.id AND nom_model.var_id IN (' . $nom_vars[NOM_VAR_MODEL] . ')) ' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_mod' . "\n" .
                   ' ON (ni18n_mod.parent_id=nom_model.value AND ni18n_mod.lang="' . $model_lang . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_col' . "\n" .
                   ' ON (ni18n_col.parent_id=gt2.free_field2 AND ni18n_col.lang="' . $model_lang . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_size' . "\n" .
                   ' ON (ni18n_size.parent_id=gt2.free_field4 AND ni18n_size.lang="' . $model_lang . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_parent_product' . "\n" .
                   ' ON (nom_parent_product.model_id=n.id AND nom_parent_product.var_id IN (' . $nom_vars[NOM_VAR_PARENT_PRODUCT] . ')) ' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_sku_cat' . "\n" .
                   ' ON (nom_sku_cat.model_id=n.id AND nom_sku_cat.var_id IN (' . $nom_vars[NOM_VAR_SKU_CATEGORY] . ')) ' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CATEGORIES_I18N . ' AS nci18n_sku_cat' . "\n" .
                   ' ON (nci18n_sku_cat.parent_id=nom_sku_cat.value AND nci18n_sku_cat.lang="' . $model_lang . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_mat' . "\n" .
                   ' ON (nom_mat.model_id=n.id AND nom_mat.var_id IN (' . $nom_vars[NOM_VAR_MATERIAL] . ') AND nom_mat.lang="' . $model_lang . '") ' . "\n";
            $contracts_rows = $registry['db']->GetAll($sql);

            //process the products
            $products_relation_list = array();
            foreach ($contracts_rows as $k => $cr) {
                $products_relation_list[$cr['article_id']] = $cr['parent_product'];
            }

            if (!empty($products_relation_list)) {
                $parent_products = array_unique(array_values($products_relation_list));
                $sql = 'SELECT n.id as idx, n.id as article_id, ni18n.name as article_name, nom_model.value as model, ni18n_mod.name as model_name, ' . "\n" .
                       '       nom_sku_cat.value as sku_category, nci18n_sku_cat.name as sku_category_name, nom_mat.value as material' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . $model_lang . '" AND n.id in ("' . implode('","', $parent_products) . '") AND n.active=1 AND n.deleted_by=0)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_model' . "\n" .
                       ' ON (nom_model.model_id=n.id AND nom_model.var_id IN (' . $nom_vars[NOM_VAR_MODEL] . ')) ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_mod' . "\n" .
                       ' ON (ni18n_mod.parent_id=nom_model.value AND ni18n_mod.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_sku_cat' . "\n" .
                       ' ON (nom_sku_cat.model_id=n.id AND nom_sku_cat.var_id IN (' . $nom_vars[NOM_VAR_SKU_CATEGORY] . ')) ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CATEGORIES_I18N . ' AS nci18n_sku_cat' . "\n" .
                       ' ON (nci18n_sku_cat.parent_id=nom_sku_cat.value AND nci18n_sku_cat.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_mat' . "\n" .
                       ' ON (nom_mat.model_id=n.id AND nom_mat.var_id IN (' . $nom_vars[NOM_VAR_MATERIAL] . ')) ' . "\n";
                $parent_products = $registry['db']->GetAssoc($sql);

                foreach ($contracts_rows as $k => $cr) {
                    if (!empty($parent_products[$products_relation_list[$cr['article_id']]])) {
                        foreach ($parent_products[$products_relation_list[$cr['article_id']]] as $prop => $val) {
                            $contracts_rows[$k][$prop] = $val;
                        }
                    }
                }
            }

            // split the rows by contracts
            $doc_gt2_list = array();
            foreach ($contracts_rows as $cr) {
                if (!isset($contracts[$cr['contract_id']]['gt2'])) {
                    $contracts[$cr['contract_id']]['gt2'] = array();
                }

                $contracts[$cr['contract_id']]['gt2'][$cr['gt2_id']] = $cr;
                $contracts[$cr['contract_id']]['gt2'][$cr['gt2_id']]['invoiced'] = 0;
                $contracts[$cr['contract_id']]['gt2'][$cr['gt2_id']]['sent_to_client'] = 0;
                $contracts[$cr['contract_id']]['gt2'][$cr['gt2_id']]['unsent'] = $cr['total_quantity'];
                $contracts[$cr['contract_id']]['rowspan']++;

                $doc_gt2_list[$cr['gt2_id']] = $cr['contract_id'];
            }

            // get the fullfilments
            $fullfilments_invoiced_statuses = array_filter(preg_split('/\s*,\s*/', DOC_SUBSTATUS_NOT_SHIPPED));
            $fullfilments_sent_statuses = array_filter(preg_split('/\s*,\s*/', DOC_SUBSTATUS_TRANSFFERED));
            $sql = 'SELECT dr.parent_id as fullfilment_id, dr.rows_links' . "\n" .
                   'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS dr' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   ' ON (d.id=dr.parent_id AND dr.parent_model_name="Document"
                         AND dr.link_to IN ("' . implode('","', array_keys($contracts)) . '")
                         AND dr.link_to_model_name="Contract" AND d.deleted_by=0 AND d.active=1 AND d.type="' . DOC_TYPE_FULFILLMENT . '"
                         AND d.substatus IN ("' . implode('","', array_merge($fullfilments_invoiced_statuses, $fullfilments_sent_statuses)) . '"))' . "\n";
            $fulfillments_relations = $registry['db']->GetAll($sql);

            // process gt2 rows
            $gt2_rels = array();
            $fullfilments_gt2 = array();
            foreach ($fulfillments_relations as $ff) {
                preg_match_all('#(\d+)[^\d]*(\d+)#', $ff['rows_links'], $matches);
                foreach ($matches[1]??[] as $k=>$v) {
                    if (isset($doc_gt2_list[$matches[2][$k]])) {
                        $gt2_rels[$v] = $matches[2][$k];
                        $fullfilments_gt2[$v] = $ff['fullfilment_id'];
                    }
                }
            }

            /*
             * Get the fulfillment data
             */
            $sql = 'SELECT d.id as fullfilment_id, d.full_num as fullfilment_num, d.substatus, gt2.id as gt2_id, ' . "\n" .
                   '       d.status_modified as transfered, gt2.article_id, gt2.quantity, fir.id as invoice, fir.num as invoice_num, ' . "\n" .
                   '       fir.issue_date as issue_date, fir.payment_status as payment_status, fir.date_of_payment, 0 as transfered_quantity' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                   ' ON (d.id IN ("' .  implode('","', $fullfilments_gt2). '") AND gt2.model="Document" AND gt2.model_id=d.id' .  (!empty(array_filter($filters['article'])) ? ' AND gt2.article_id IN ("' . implode('","', array_filter($filters['article'])) . '")' : '') . ')' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' AS gt2_nom' . "\n" .
                   '  ON (gt2_nom.id=gt2.article_id AND gt2_nom.type IN ("' . implode('","', $nom_types_included) . '"))' . "\n" .
                   'INNER JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2_i18n' . "\n" .
                   ' ON (gt2_i18n.parent_id=gt2.id AND gt2_i18n.lang="' . $model_lang . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr2' . "\n" .
                   ' ON (dr2.link_to=d.id AND dr2.link_to_model_name="Document" AND dr2.parent_model_name="Finance_Incomes_Reason")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                   ' ON (fir.id=dr2.parent_id AND fir.type="' . PH_FINANCE_TYPE_INVOICE . '" AND fir.advance=0 AND fir.active=1 AND fir.annulled_by=0)' . "\n";
            $fulfillments = $registry['db']->GetAll($sql);

            foreach ($fulfillments as $ff) {
                // find the related gt2 row
                if (!isset($gt2_rels[$ff['gt2_id']])) {
                    continue;
                }
                $related_gt2 = $gt2_rels[$ff['gt2_id']];
                $related_contract = $doc_gt2_list[$related_gt2];

                $contracts[$related_contract]['gt2'][$related_gt2]['fullfilment_id'] = $ff['fullfilment_id'];
                $contracts[$related_contract]['gt2'][$related_gt2]['fullfilment_num'] = $ff['fullfilment_num'];
                $contracts[$related_contract]['gt2'][$related_gt2]['fullfilment_substatus'] = $ff['substatus'];
                $contracts[$related_contract]['gt2'][$related_gt2]['transfered'] = $ff['transfered'];
                $contracts[$related_contract]['gt2'][$related_gt2]['invoice'] = $ff['invoice'];
                $contracts[$related_contract]['gt2'][$related_gt2]['invoice_num'] = $ff['invoice_num'];
                $contracts[$related_contract]['gt2'][$related_gt2]['issue_date'] = $ff['issue_date'];
                $contracts[$related_contract]['gt2'][$related_gt2]['payment_status'] = $ff['payment_status'];
                $contracts[$related_contract]['gt2'][$related_gt2]['date_of_payment'] = $ff['date_of_payment'];
                $contracts[$related_contract]['gt2'][$related_gt2]['transfered_quantity'] = $ff['transfered_quantity'];
                if (!in_array($ff['substatus'], $fullfilments_sent_statuses)) {
                    $contracts[$related_contract]['gt2'][$related_gt2]['transfered'] = '';
                }
                if (in_array($ff['substatus'], $fullfilments_invoiced_statuses)) {
                    $contracts[$related_contract]['gt2'][$related_gt2]['invoiced'] += $ff['quantity'];
                    $contracts[$related_contract]['gt2'][$related_gt2]['unsent'] -= $ff['quantity'];
                } elseif (in_array($ff['substatus'], $fullfilments_sent_statuses)) {
                    $contracts[$related_contract]['gt2'][$related_gt2]['sent_to_client'] += $ff['quantity'];
                    $contracts[$related_contract]['gt2'][$related_gt2]['unsent'] -= $ff['quantity'];
                    $contracts[$related_contract]['gt2'][$related_gt2]['transfered_quantity'] += $ff['quantity'];
                }
                $contracts[$related_contract]['gt2'][$related_gt2]['shipped_amount'] = floatval($contracts[$related_contract]['gt2'][$related_gt2]['transfered_quantity']) * floatval($contracts[$related_contract]['gt2'][$related_gt2]['unit_price']);
            }

            // process the currency if needed
            if (!empty($filters['currency'])) {
                foreach ($contracts as $contr_id => $contr_data) {
                    $contracts[$contr_id]['filter_currency'] = $filters['currency'];
                    if ($filters['currency'] == $contr_data['currency']) {
                        continue;
                    }
                    $currency_rate = Finance_Currencies::getRate($registry, $contr_data['currency'], $filters['currency']);

                    foreach ($contr_data['gt2'] as $gt2_id => $gt2_data) {
                        $contracts[$contr_id]['gt2'][$gt2_id]['unit_price'] = $contracts[$contr_id]['gt2'][$gt2_id]['unit_price'] * $currency_rate;
                        $contracts[$contr_id]['gt2'][$gt2_id]['total_price'] = $contracts[$contr_id]['gt2'][$gt2_id]['total_price'] * $currency_rate;
                        if (!empty($gt2_data['fullfilment_id'])) {
                            $contracts[$contr_id]['gt2'][$gt2_id]['shipped_amount'] = $contracts[$contr_id]['gt2'][$gt2_id]['shipped_amount'] * $currency_rate;
                        }
                    }
                }
            }
        }

        return self::exitReport($registry, $filters, $contracts);
    }

    private static function exitReport(Registry &$registry, array $filters, array $final_results = array()) {

        $final_results['additional_options']['dont_show_export_button'] = true;
        $final_results['additional_options']['include_currency'] = (!empty($filters['currency']) ? $filters['currency'] : '');

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }
        return $results;
    }
}
