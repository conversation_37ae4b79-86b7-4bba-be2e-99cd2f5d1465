<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_email_content':
                $this->_email_content();
                break;
            case 'ajax_issue_documents':
                $this->_issueDocuments();
                break;
            case 'generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    /*
     * Method to create the selected documents
     */
    public function _issueDocuments() {
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        //load plugin i18n files
        $i18n_files = array();
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $i18n_files[] = sprintf('%s%s%s%s',
            PH_MODULES_DIR,
            'documents/i18n/',
            $this->registry['lang'],
            '/documents.ini');
        $this->registry['translater']->loadFile($i18n_files);

        Reports::getReportSettings($this->registry, $report);

        // include needed classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';

        $clinical_examination_list = explode(',', DOCUMENT_CLINICAL_EXAMINATION);
        $microbiology_examination_list = explode(',', DOCUMENT_MICROBIOLOGY_EXAMINATION);
        $histological_examination_list = explode(',', DOCUMENT_HISTOLOGICAL_EXAMINATION);
        $spermogram1_examination_list = explode(',', DOCUMENT_SPERMOGRAM1_EXAMINATION);
        $spermogram2_examination_list = explode(',', DOCUMENT_SPERMOGRAM2_EXAMINATION);
        $dna1_examination_list = explode(',', DOCUMENT_DNA1_EXAMINATION);
        $dna2_examination_list = explode(',', DOCUMENT_DNA2_EXAMINATION);

        $microbiology_create_one_document_noms = explode(',', MICROBIOLOGY_CREATE_ONE_DOCUMENT_NOMS);

        $request = $this->registry['request'];
        $db = $this->registry['db'];

        // source rows
        $create_clinical_rows = array();
        $create_microbiological_rows = array();
        $available_documents = array();
        $available_documents_assoc = array();
        $doctors_data = array();
        $customer_id = 0;
        $trademark_id = 0;
        $source_rows = $request->get('procedures');
        foreach ($source_rows as $idx => $src_dat) {
            $source_rows[$idx] = json_decode(base64_decode($src_dat), true);
            $available_documents = array_merge($available_documents, $source_rows[$idx]['documents']);
            $customer_id = $source_rows[$idx]['customer_id'];
            $trademark_id = $source_rows[$idx]['tm_id'];
            if (!empty($source_rows[$idx]['doctor_id']) && !in_array($source_rows[$idx]['doctor_id'], $doctors_data)) {
                $doctors_data[$source_rows[$idx]['doctor_id']] = '';
            }
        }
        $available_documents = array_values(array_unique($available_documents));

        // search the needed types
        if (!empty($available_documents)) {
            $filters = array('where' => array('dt.id IN ("' . implode('","', $available_documents) . '")',
                                              'dt.active = 1'),
                             'sanitize' => true);
            $available_documents = Documents_Types::search($this->registry, $filters);
            foreach ($available_documents as $avb_doc) {
                $available_documents_assoc[$avb_doc->get('id')] = $avb_doc;
            }
        }
        unset($available_documents);

        if (!empty($doctors_data)) {
            $sql_doctors = 'SELECT `parent_id`, CONCAT(`name`, " ", `lastname`) ' . "\n" .
                           'FROM ' . DB_TABLE_CUSTOMERS_I18N . "\n" .
                           'WHERE `parent_id` IN ("' . implode('","', array_keys($doctors_data)) . '")' . "\n";
            $doctors_data = $db->GetAssoc($sql_doctors);
        }

        $error = false;
        $error_messages = array();
        $successful_messages = array();
        $related_gt2_rows = array();
        $mark_gt2_rows = array();
        $db->StartTrans();
        foreach ($source_rows as $idx => $src_dat) {
            foreach ($src_dat['documents'] as $doc_type) {
                $document_type = '';
                $current_group_documents = '';
                if (in_array($doc_type, $clinical_examination_list)) {
                    $current_group_documents = 'clinical';
                } elseif (in_array($doc_type, $microbiology_examination_list)) {
                    $current_group_documents = 'microbiology';
                } elseif (in_array($doc_type, $histological_examination_list)) {
                    $current_group_documents = 'histological';
                } elseif (in_array($doc_type, $spermogram1_examination_list)) {
                    $current_group_documents = 'spermogram1';
                } elseif (in_array($doc_type, $spermogram2_examination_list)) {
                    $current_group_documents = 'spermogram2';
                } elseif (in_array($doc_type, $dna1_examination_list)) {
                    $current_group_documents = 'dna1';
                } elseif (in_array($doc_type, $dna2_examination_list)) {
                    $current_group_documents = 'dna2';
                }

                if (!$current_group_documents) {
                    $error = true;
                    $error_messages[] = sprintf($this->i18n('error_selected_document_type_not_pointed'), $doc_type);
                    continue;
                }

                if ($current_group_documents == 'clinical') {
                    $create_clinical_rows[$src_dat['gt2_id']] = $src_dat;
                    continue;
                } elseif ($current_group_documents == 'microbiology' && in_array($src_dat['id'], $microbiology_create_one_document_noms)) {
                    $create_microbiological_rows[$src_dat['gt2_id']] = $src_dat;
                    continue;
                }

                if (isset($available_documents_assoc[$doc_type])) {
                    $document_type = $available_documents_assoc[$doc_type];
                } else {
                    $error = true;
                    $error_messages[] = sprintf($this->i18n('error_not_selected_document_type'), $doc_type);
                    continue;
                }

                $current_document_error = false;

                // VALIDATE REQUIRED FIELDS
                if ($current_group_documents == 'dna1' || $current_group_documents == 'dna2') {

                    /* =========  DNA  ========= */
                    // validate selected fields
                    if ($current_group_documents == 'dna1' && !$request->get('dna_examination')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_dna_examination'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                } elseif ($current_group_documents == 'spermogram1') {

                    /* =========  SPERMOGRAM  ========= */
                    // validate selected fields
                    if (!$request->get('spermogram_sexual_abstinence')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_spermogram_sexual_abstinence'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if (!$request->get('spermogram_material_taken')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_spermogram_material_taken'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                } elseif ($current_group_documents == 'histological') {

                    /* =========  HISTOLOGICAL  ========= */
                    // validate selected fields
                    if (!$request->get('examination_date')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_main_examination_data_date'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if (!$request->get('histological_examination_target')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_histological_examination_target'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if (!$request->get('histological_examination_type')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_histological_examination_type'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if (!$request->get('material_taken')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_histological_material_taken'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if (!$request->get('expected_risk')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_histological_expected_risk'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                } elseif ($current_group_documents == 'microbiology') {

                    /* =========  MICROBIOLOGY (SIMPLE)  ========= */
                    // validate selected fields
                    if (!$request->get('microbiological_examination')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_examination'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if (!$request->get('examination_target')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_examination_target'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if (!$request->get('examination_type')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_type_examination'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if ($request->get('therapy_yn') == MICROBIOLOGICAL_ANTIMICROBIOLOGICAL_THERAPY_YES && !$request->get('microbiological_antimicrobiological_notes')) {
                        $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_antimicrobiological_therapy_notes'));
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                    if ($request->get('microbiological_examination') == MICROBIOLOGICAL_TYPE_MATERIAL_OTHER_ID && !$request->get('microbiological_examination_type_material_other')) {
                        $error_msg = $this->i18n('error_microbiological_examination_other');
                        if (!in_array($error_msg, $error_messages)) {
                            $error_messages[] = $error_msg;
                        }
                        $current_document_error = true;
                    }
                }

                if ($current_document_error) {
                    $error = true;
                    continue;
                }

                // document creation
                $document = new Document($this->registry);
                $old_document = clone $document;
                $old_document->sanitize();

                // set main data
                $document->set('type', $document_type->get('id'), true);
                $document->set('type_name', $document_type->get('name'), true);
                $document->set('name', $document_type->get('default_name'), true);
                $document->set('custom_num', $src_dat['ambulatory_sheet'], true);
                $document->set('group', $document_type->getDefaultGroup(), true);
                $document->set('active', 1, true);
                $document->set('department', $document_type->getDefaultDepartment(), true);
                $document->set('customer', $customer_id, true);
                $document->set('description', $src_dat['description'], true);
                $document->set('trademark', $trademark_id, true);
                if ($current_group_documents == 'histological') {
                    $document->set('date', $request->get('examination_date'), true);
                }

                // try to save the document (just the basic vars)
                if ($document->save()) {
                    // GET ADDED DOCUMENT
                    $filters = array('where' => array('d.id = ' . $document->get('id')),
                                     'model_lang' => $document->get('model_lang'));
                    $added_document = Documents::searchOne($this->registry, $filters);

                    // write history
                    Documents_History::saveData($this->registry, array('model' => $added_document, 'action_type' => 'add', 'new_model' => $added_document, 'old_model' => $old_document));

                    $this->registry->set('get_old_vars', true, true);
                    $added_document->getVars();
                    $this->registry->set('get_old_vars', false, true);
                    $assoc_vars = $added_document->getAssocVars();
                    $added_document->unsetProperty('assoc_vars', true);

                    // Prepare the var_id->value pairs
                    $new_values = array();
                    $cstm_insert = array();

                    // prepare the additional data
                    if ($current_group_documents == 'dna1' || $current_group_documents == 'dna2') {
                        if ($current_group_documents == 'dna1') {
                            $new_values[$assoc_vars[DNA1_VAR_BIOLOGICAL_MATERIAL]['id']] = $request->get('dna_examination');
                            if ($request->get('dna_examination')) {
                                $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                                         $document->get('id'), $request->get('dna_examination'),
                                                         $assoc_vars[DNA1_VAR_BIOLOGICAL_MATERIAL]['id']);
                            }
                        }
                        if (isset($assoc_vars[DNA_VAR_DOCTOR_ID]) && isset($assoc_vars[DNA_VAR_DOCTOR_NAME]['id'])) {
                            $new_values[$assoc_vars[DNA_VAR_DOCTOR_ID]['id']] = $src_dat['doctor_id'];
                            $new_values[$assoc_vars[DNA_VAR_DOCTOR_NAME]['id']] = (!empty($src_dat['doctor_id']) ? $doctors_data[$src_dat['doctor_id']] : '');
                            if (!empty($src_dat['doctor_id'])) {
                                $cstm_insert[] = sprintf('(\'Document\', %d, \'Customer\', %d, %d, 1, "")',
                                                         $document->get('id'), $src_dat['doctor_id'],
                                                         $assoc_vars[DNA_VAR_DOCTOR_ID]['id']);
                            }
                        }
                    } elseif ($current_group_documents == 'spermogram1') {
                        $new_values[$assoc_vars[SPERMOGRAM_VAR_SEXUAL_ABSTINENCE]['id']] = $request->get('spermogram_sexual_abstinence');
                        $new_values[$assoc_vars[SPERMOGRAM_MATERIAL_TAKEN]['id']] = $request->get('spermogram_material_taken');
                        $new_values[$assoc_vars[SPERMOGRAM_VAR_DOCTOR_ID]['id']] = $src_dat['doctor_id'];
                        $new_values[$assoc_vars[SPERMOGRAM_VAR_DOCTOR_NAME]['id']] = (!empty($src_dat['doctor_id']) ? $doctors_data[$src_dat['doctor_id']] : '');
                        if (!empty($src_dat['doctor_id'])) {
                            $cstm_insert[] = sprintf('(\'Document\', %d, \'Customer\', %d, %d, 1, "")',
                                                     $document->get('id'), $src_dat['doctor_id'],
                                                     $assoc_vars[SPERMOGRAM_VAR_DOCTOR_ID]['id']);
                        }
                    } elseif ($current_group_documents == 'histological') {
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_DOCTOR_ID]['id']] = $src_dat['doctor_id'];
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_CLINICAL_DIAGNOSIS]['id']] = $request->get('histological_examination_target');
                        $new_values[$assoc_vars[HISTOLOGICAL_PANEL_EXAMINATION_TYPE_VAR]['id']] = $request->get('histological_examination_type');
                        $new_values[$assoc_vars[HISTOLOGICAL_PANEL_MATERIAL_TAKEN_VAR]['id']] = $request->get('material_taken');
                        $new_values[$assoc_vars[HISTOLOGICAL_PANEL_EXPECTED_RISK_VAR]['id']] = $request->get('expected_risk');
                        $new_values[$assoc_vars[HISTOLOGICAL_PANEL_COLPOSCOPIC_IMAGE_VAR]['id']] = $request->get('colposcopic_image');
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_COLPOSCOPIC_IMAGE_NOTES]['id']] = $request->get('colposcopic_image_notes');
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_LAST_REGULAR_MENSTRUATION]['id']] = $request->get('prm');
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_LAST_REGULAR_MENSTRUATION_DAYS]['id']] = $request->get('calculation_prm_interval');
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_AMENORRHEA]['id']] = $request->get('amenorrhea');
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_PRM]['id']] = $request->get('prm');
                        $new_values[$assoc_vars[HISTOLOGICAL_VAR_PRM_CALCULATE]['id']] = $request->get('calculation_prm_interval');
                        if (!empty($src_dat['doctor_id'])) {
                            $cstm_insert[] = sprintf('(\'Document\', %d, \'Customer\', %d, %d, 1, "")',
                                                     $document->get('id'), $src_dat['doctor_id'],
                                                     $assoc_vars[HISTOLOGICAL_VAR_DOCTOR_ID]['id']);
                        }
                        if ($request->get('histological_examination_target')) {
                            $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                                     $document->get('id'), $request->get('histological_examination_target'),
                                                     $assoc_vars[HISTOLOGICAL_VAR_CLINICAL_DIAGNOSIS]['id']);
                        }
                    } elseif ($current_group_documents == 'microbiology') {
                        $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_DOCTOR_ID]['id']] = $src_dat['doctor_id'];
                        $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_CLINICAL_DIAGNOSIS]['id']] = $request->get('examination_target');
                        $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_PREGNANCY_PERIOD]['id']] = $request->get('date_gs');
                        $new_values[$assoc_vars[MICROBIOLOGICAL_PANEL_ANTIMICROBIAL_THERAPY]['id']] = $request->get('therapy_yn');
                        $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_ANTIMICROBIAL_THERAPY]['id']] = $request->get('microbiological_antimicrobiological_notes');
                        $new_values[$assoc_vars[MICROBIOLOGICAL_EXAMINATION_VAR]['id']] = $request->get('microbiological_examination');
                        $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_OTHER_SECRETIONS]['id']] = $request->get('microbiological_examination_type_material_other');
                        $new_values[$assoc_vars[MICROBIOLOGICAL_PANEL_TYPE_EXAMINATION_VAR]['id']] = $request->get('examination_type');
                        $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_RESEARCH_NOTES]['id']] = $request->get('microbiological_notes');
                        if (!empty($src_dat['doctor_id'])) {
                            $cstm_insert[] = sprintf('(\'Document\', %d, \'Customer\', %d, %d, 1, "")',
                                                     $document->get('id'), $src_dat['doctor_id'],
                                                     $assoc_vars[MICROBIOLOGICAL_VAR_DOCTOR_ID]['id']);
                        }
                        if ($request->get('examination_target')) {
                            $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                                     $document->get('id'), $request->get('examination_target'),
                                                     $assoc_vars[MICROBIOLOGICAL_VAR_CLINICAL_DIAGNOSIS]['id']);
                        }
                    }

                    // Save additional data (if this type of document has such)
                    if ($new_values) {
                        foreach ($new_values as $var_id => $new_value) {
                            if (!is_array($new_value)) {
                                $add_var_value = array($new_value);
                            } else {
                                $add_var_value = $new_value;
                            }
                            foreach ($add_var_value as $idx_avv => $avv) {
                                $update = $insert = array();
                                $insert[] = $update[] = 'value = \'' . General::slashesEscape($avv) . '\'';
                                $insert[] = $update[] = 'modified = NOW()';
                                $insert[] = $update[] = 'modified_by = \'' . $this->registry['currentUser']->get('id') . '\'';

                                $insert[] = 'model_id = \'' . $added_document->get('id') . '\'';
                                $insert[] = 'var_id = \'' . $var_id . '\'';
                                $insert[] = 'added = NOW()';
                                $insert[] = 'added_by = \'' . $this->registry['currentUser']->get('id') . '\'';
                                $insert[] = 'num = \'' . ($idx_avv+1) . '\'';
                                $insert[] = 'lang = \'\'';

                                $query = 'INSERT INTO `' . DB_TABLE_DOCUMENTS_CSTM . '` SET ' . implode(', ', $insert) . "\n" .
                                         '  ON DUPLICATE KEY UPDATE ' . implode(', ', $update);
                                $db->Execute($query);
                                if ($db->ErrorNo()) {
                                    $current_document_error = true;
                                    $error_msg = sprintf($this->i18n('error_saving_additional_vars'), $document_type->get('name'), $src_dat['name']);
                                    if (!in_array($error_msg, $error_messages)) {
                                        $error_messages[] = $error_msg;
                                    }
                                    break 2;
                                }
                            }
                        }

                        if ($current_document_error) {
                            $error = true;
                            continue;
                        }

                        // insert model-to-cstm_model relations
                        if ($cstm_insert) {
                            $document->saveCstmRelatives(array(), $cstm_insert);
                        }

                        // Get the new document
                        $new_document_filters = array('where'      => array('d.id = \'' . $added_document->get('id') . '\''),
                                                      'model_lang' => $added_document->get('model_lang'));
                        $new_document = Documents::searchOne($this->registry, $new_document_filters);

                        // Load the additional vars of the new document
                        $this->registry->set('get_old_vars', true, true);
                        $new_document->getVars();
                        $this->registry->set('get_old_vars', false, true);

                        // Write history
                        Documents_History::saveData($this->registry, array('model'       => $new_document,
                                                                           'action_type' => 'edit',
                                                                           'new_model'   => $new_document,
                                                                           'old_model'   => $added_document));
                    }

                    $url = sprintf('<a href="%s?%s=documents&amp;documents=view&amp;view=%s" target="_blank">%s</a>',
                                   $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                   $added_document->get('id'), $document_type->get('name'));
                    $successful_messages[] = $this->i18n('message_successful_added_reprobiomed_document', array($url));
                    $related_gt2_rows[$added_document->get('id')] = array($src_dat['leaving_form_id']);
                    $mark_gt2_rows[] = $src_dat['gt2_id'];
                } else {
                    // ADD ERROR
                    $error_messages[] = sprintf($this->i18n('error_adding_new_reprobiomed_document'), $document_type->get('name'), $src_dat['name']);
                    foreach ($this->registry['messages']->getErrors() as $err) {
                        $error_messages[] = $err;
                    }
                    $error = true;
                    continue;
                }
                $this->registry['messages']->flush();
            }
        }


        if (!empty($create_microbiological_rows)) {
            // CREATE MICROBIOLOGICAL DOCUMENT FOR MULTIPLE ROWS
            $current_document_error = false;
            $document_type = $available_documents_assoc[DOCUMENT_MICROBIOLOGY_EXAMINATION];

            // validate selected fields
            if (!$request->get('microbiological_examination')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_examination'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if (!$request->get('examination_target')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_examination_target'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if (!$request->get('examination_type')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_type_examination'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if ($request->get('therapy_yn') == MICROBIOLOGICAL_ANTIMICROBIOLOGICAL_THERAPY_YES && !$request->get('microbiological_antimicrobiological_notes')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_microbiological_antimicrobiological_therapy_notes'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if ($request->get('microbiological_examination') == MICROBIOLOGICAL_TYPE_MATERIAL_OTHER_ID && !$request->get('microbiological_examination_type_material_other')) {
                $error_msg = $this->i18n('error_microbiological_examination_other');
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }

            // get the leaving form where the data should be taken from
            $latest_leaving_form = 0;
            $doctor_id = 0;
            $description = '';
            $included_services = array();
            foreach ($create_microbiological_rows as $cmr) {
                if ($cmr['leaving_form_id'] > $latest_leaving_form) {
                    $latest_leaving_form = $cmr['leaving_form_id'];
                    $doctor_id = $cmr['doctor_id'];
                    $description = $cmr['description'];
                }
                $included_services[] = $cmr['name'];
            }

            // document creation
            $document = new Document($this->registry);
            $old_document = clone $document;
            $old_document->sanitize();

            // set main data
            $document->set('type', $document_type->get('id'), true);
            $document->set('type_name', $document_type->get('name'), true);
            $document->set('name', $document_type->get('default_name'), true);
            $document->set('group', $document_type->getDefaultGroup(), true);
            $document->set('active', 1, true);
            $document->set('department', $document_type->getDefaultDepartment(), true);
            $document->set('customer', $customer_id, true);
            $document->set('trademark', $trademark_id, true);
            $document->set('description', $description, true);

            // try to save the document (just the basic vars)
            if (!$current_document_error && $document->save()) {
                // GET ADDED DOCUMENT
                $filters = array('where' => array('d.id = ' . $document->get('id')),
                                 'model_lang' => $document->get('model_lang'));
                $added_document = Documents::searchOne($this->registry, $filters);

                // write history
                Documents_History::saveData($this->registry, array('model' => $added_document, 'action_type' => 'add', 'new_model' => $added_document, 'old_model' => $old_document));

                $this->registry->set('get_old_vars', true, true);
                $added_document->getVars();
                $this->registry->set('get_old_vars', false, true);
                $assoc_vars = $added_document->getAssocVars();
                $added_document->unsetProperty('assoc_vars', true);

                // Prepare the var_id->value pairs
                $new_values = array();
                $cstm_insert = array();
                $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_DOCTOR_ID]['id']] = $doctor_id;
                $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_CLINICAL_DIAGNOSIS]['id']] = $request->get('examination_target');
                $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_PREGNANCY_PERIOD]['id']] = $request->get('date_gs');
                $new_values[$assoc_vars[MICROBIOLOGICAL_PANEL_ANTIMICROBIAL_THERAPY]['id']] = $request->get('therapy_yn');
                $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_ANTIMICROBIAL_THERAPY]['id']] = $request->get('microbiological_antimicrobiological_notes');
                $new_values[$assoc_vars[MICROBIOLOGICAL_EXAMINATION_VAR]['id']] = $request->get('microbiological_examination');
                $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_OTHER_SECRETIONS]['id']] = $request->get('microbiological_examination_type_material_other');
                $new_values[$assoc_vars[MICROBIOLOGICAL_PANEL_TYPE_EXAMINATION_VAR]['id']] = $request->get('examination_type');
                $new_values[$assoc_vars[MICROBIOLOGICAL_VAR_RESEARCH_NOTES]['id']] = $request->get('microbiological_notes');
                if ($doctor_id) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Customer\', %d, %d, 1, "")',
                                             $document->get('id'), $doctor_id,
                                             $assoc_vars[MICROBIOLOGICAL_VAR_DOCTOR_ID]['id']);
                }
                if ($request->get('examination_target')) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                             $document->get('id'), $request->get('examination_target'),
                                             $assoc_vars[MICROBIOLOGICAL_VAR_CLINICAL_DIAGNOSIS]['id']);
                }

                // Save additional data
                foreach ($new_values as $var_id => $new_value) {
                    if (!is_array($new_value)) {
                        $add_var_value = array($new_value);
                    } else {
                        $add_var_value = $new_value;
                    }
                    foreach ($add_var_value as $idx_avv => $avv) {
                        $update = $insert = array();
                        $insert[] = $update[] = 'value = \'' . General::slashesEscape($avv) . '\'';
                        $insert[] = $update[] = 'modified = NOW()';
                        $insert[] = $update[] = 'modified_by = \'' . $this->registry['currentUser']->get('id') . '\'';

                        $insert[] = 'model_id = \'' . $added_document->get('id') . '\'';
                        $insert[] = 'var_id = \'' . $var_id . '\'';
                        $insert[] = 'added = NOW()';
                        $insert[] = 'added_by = \'' . $this->registry['currentUser']->get('id') . '\'';
                        $insert[] = 'num = \'' . ($idx_avv+1) . '\'';
                        $insert[] = 'lang = \'\'';

                        $query = 'INSERT INTO `' . DB_TABLE_DOCUMENTS_CSTM . '` SET ' . implode(', ', $insert) . "\n" .
                                 '  ON DUPLICATE KEY UPDATE ' . implode(', ', $update);
                        $db->Execute($query);
                        if ($db->ErrorNo()) {
                            $current_document_error = true;
                            $error_msg = sprintf($this->i18n('error_saving_additional_vars_multiple_documents'), $document_type->get('name'), '"' . implode('", "', $included_services) . '"');
                            if (!in_array($error_msg, $error_messages)) {
                                $error_messages[] = $error_msg;
                            }
                            break 2;
                        }
                    }
                }

                if ($current_document_error) {
                    $error = true;
                } else {
                    // insert model-to-cstm_model relations
                    if ($cstm_insert) {
                        $document->saveCstmRelatives(array(), $cstm_insert);
                    }

                    // Get the new document
                    $new_document_filters = array('where'      => array('d.id = \'' . $added_document->get('id') . '\''),
                        'model_lang' => $added_document->get('model_lang'));
                    $new_document = Documents::searchOne($this->registry, $new_document_filters);

                    // Load the additional vars of the new document
                    $this->registry->set('get_old_vars', true, true);
                    $new_document->getVars();
                    $this->registry->set('get_old_vars', false, true);

                    // Write history
                    Documents_History::saveData($this->registry, array('model'       => $new_document,
                                                                       'action_type' => 'edit',
                                                                       'new_model'   => $new_document,
                                                                       'old_model'   => $added_document));

                    $url = sprintf('<a href="%s?%s=documents&amp;documents=view&amp;view=%s" target="_blank">%s</a>',
                                   $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                   $new_document->get('id'), $document_type->get('name'));
                    $successful_messages[] = $this->i18n('message_successful_added_reprobiomed_document', array($url));
                    $related_gt2_rows[$new_document->get('id')] = array();
                    foreach ($create_microbiological_rows as $cmr) {
                        $related_gt2_rows[$new_document->get('id')][] = $cmr['leaving_form_id'];
                        $mark_gt2_rows[] = $cmr['gt2_id'];
                    }
                }
            } else {
                // ADD ERROR
                $error_messages[] = sprintf($this->i18n('error_adding_new_multiple_reprobiomed_document'), $document_type->get('name'), '"' . implode('", "', $included_services) . '"');
                foreach ($this->registry['messages']->getErrors() as $err) {
                    $error_messages[] = $err;
                }
                $error = true;
            }
            $this->registry['messages']->flush();
        }

        if (!empty($create_clinical_rows)) {
            // CREATE CLINICAL DOCUMENT FOR MULTIPLE ROWS
            $current_document_error = false;
            $document_type = $available_documents_assoc[DOCUMENT_CLINICAL_EXAMINATION];

            // validate selected fields
            $required_fields = $this->registry['config']->getParamAsArray('documents', 'validate_' . $document_type->get('id'));
            if (in_array('custom_num', $required_fields) && !$request->get('clinical_lab_num')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_lab_num'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if (!$request->get('examination_date')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_main_examination_data_date'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if (!$request->get('clinical_examination')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_clincal_examination'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if ($request->isRequested('hormonal_examination') && !$request->get('hormonal_examination')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_clinical_hormonal'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if ($request->isRequested('pkk_biochemistry') && !$request->get('pkk_biochemistry')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_clinical_pkk_biochemistry'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if ($request->isRequested('urine') && !$request->get('urine')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_clinical_urine'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if ($request->isRequested('hemostaziologiya') && !$request->get('hemostaziologiya')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_clinical_hemostaziologiya'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }
            if ($request->isRequested('sexually_transmissible_diseases') && !$request->get('sexually_transmissible_diseases')) {
                $error_msg = sprintf($this->i18n('error_please_complete_empty_field'), $this->i18n('reports_clinical_sexually_transmissible_diseases'));
                if (!in_array($error_msg, $error_messages)) {
                    $error_messages[] = $error_msg;
                }
                $current_document_error = true;
            }

            // get the leaving form where the data should be taken from
            $latest_leaving_form = 0;
            $doctor_id = 0;
            $description = '';
            $included_services = array();
            foreach ($create_clinical_rows as $cmr) {
                if ($cmr['leaving_form_id'] > $latest_leaving_form) {
                    $latest_leaving_form = $cmr['leaving_form_id'];
                    $doctor_id = $cmr['doctor_id'];
                    $description = $cmr['description'];
                }
                $included_services[$cmr['id']] = $cmr['name'];
            }

            // document creation
            $document = new Document($this->registry);
            $old_document = clone $document;
            $old_document->sanitize();

            // set main data
            $document->set('type', $document_type->get('id'), true);
            $document->set('type_name', $document_type->get('name'), true);
            $document->set('name', $document_type->get('default_name'), true);
            $document->set('group', $document_type->getDefaultGroup(), true);
            $document->set('active', 1, true);
            $document->set('department', $document_type->getDefaultDepartment(), true);
            $document->set('customer', $customer_id, true);
            $document->set('trademark', $trademark_id, true);
            $document->set('description', $description, true);
            $document->set('custom_num', $request->get('clinical_lab_num'), true);
            $document->set('date', $request->get('examination_date'), true);

            // try to save the document (just basic vars because additional vars
            // will not pass the required field validation)
            if (!$current_document_error && $document->save()) {
                // GET ADDED DOCUMENT
                $filters = array('where' => array('d.id = ' . $document->get('id')),
                                 'model_lang' => $document->get('model_lang'));
                $added_document = Documents::searchOne($this->registry, $filters);

                // write history
                Documents_History::saveData($this->registry, array('model' => $added_document, 'action_type' => 'add', 'new_model' => $added_document, 'old_model' => $old_document));

                $this->registry->set('get_old_vars', true, true);
                $added_document->getVars();
                $this->registry->set('get_old_vars', false, true);
                $assoc_vars = $added_document->getAssocVars();
                $added_document->unsetProperty('assoc_vars', true);

                // Prepare the var_id->value pairs
                $new_values = array();
                $cstm_insert = array();
                $new_values[$assoc_vars[CLINICAL_VAR_DOCTOR_ID]['id']] = $doctor_id;
                $new_values[$assoc_vars[CLINICAL_VAR_DOCTOR_NAME]['id']] = (!empty($doctor_id) ? $doctors_data[$doctor_id] : '');
                $new_values[$assoc_vars[CLINICAL_EXAMINATION_VAR]['id']] = $request->get('clinical_examination');
                $new_values[$assoc_vars[CLINICAL_VAR_HORMONAL_EXAMINATION]['id']] = $request->get('hormonal_examination');
                $new_values[$assoc_vars[CLINICAL_VAR_PRM]['id']] = $request->get('prm');
                $new_values[$assoc_vars[CLINICAL_VAR_PRM_CALCULATE]['id']] = $request->get('calculation_prm_interval');
                $new_values[$assoc_vars[CLINICAL_VAR_AMENORRHEA]['id']] = $request->get('amenorrhea');
                $new_values[$assoc_vars[CLINICAL_VAR_PROCEDURE_DATE]['id']] = $request->get('date_et');
                $new_values[$assoc_vars[CLINICAL_VAR_PROCEDURE_DATE_CALCULATED]['id']] = $request->get('calculation_date_et_interval');
                $new_values[$assoc_vars[CLINICAL_VAR_PKK_BIOCHEMISTRY]['id']] = $request->get('pkk_biochemistry');
                $new_values[$assoc_vars[CLINICAL_VAR_HEMOSTAZIOLOGIYA]['id']] = $request->get('hemostaziologiya');
                $new_values[$assoc_vars[CLINICAL_VAR_SEXUALLY_TRANSMISSIBLE_DISEASES]['id']] = $request->get('sexually_transmissible_diseases');
                $new_values[$assoc_vars[CLINICAL_VAR_URINE]['id']] = $request->get('urine');
                if ($doctor_id) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Customer\', %d, %d, 1, "")',
                                             $document->get('id'), $doctor_id,
                                             $assoc_vars[CLINICAL_VAR_DOCTOR_ID]['id']);
                }
                if ($request->get('hormonal_examination')) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                             $document->get('id'), $request->get('hormonal_examination'),
                                             $assoc_vars[CLINICAL_VAR_HORMONAL_EXAMINATION]['id']);
                }
                if ($request->get('pkk_biochemistry')) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                             $document->get('id'), $request->get('pkk_biochemistry'),
                                             $assoc_vars[CLINICAL_VAR_PKK_BIOCHEMISTRY]['id']);
                }
                if ($request->get('hemostaziologiya')) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                             $document->get('id'), $request->get('hemostaziologiya'),
                                             $assoc_vars[CLINICAL_VAR_HEMOSTAZIOLOGIYA]['id']);
                }
                if ($request->get('sexually_transmissible_diseases')) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                             $document->get('id'), $request->get('sexually_transmissible_diseases'),
                                             $assoc_vars[CLINICAL_VAR_SEXUALLY_TRANSMISSIBLE_DISEASES]['id']);
                }
                if ($request->get('urine')) {
                    $cstm_insert[] = sprintf('(\'Document\', %d, \'Nomenclature\', %d, %d, 1, "")',
                                             $document->get('id'), $request->get('urine'),
                                             $assoc_vars[CLINICAL_VAR_URINE]['id']);
                }

                // Save additional data
                foreach ($new_values as $var_id => $new_value) {
                    if (!is_array($new_value)) {
                        $add_var_value = array($new_value);
                    } else {
                        $add_var_value = $new_value;
                    }
                    foreach ($add_var_value as $idx_avv => $avv) {
                        $update = $insert = array();
                        $insert[] = $update[] = 'value = \'' . General::slashesEscape($avv) . '\'';
                        $insert[] = $update[] = 'modified = NOW()';
                        $insert[] = $update[] = 'modified_by = \'' . $this->registry['currentUser']->get('id') . '\'';

                        $insert[] = 'model_id = \'' . $added_document->get('id') . '\'';
                        $insert[] = 'var_id = \'' . $var_id . '\'';
                        $insert[] = 'added = NOW()';
                        $insert[] = 'added_by = \'' . $this->registry['currentUser']->get('id') . '\'';
                        $insert[] = 'num = \'' . ($idx_avv+1) . '\'';
                        $insert[] = 'lang = \'\'';

                        $query = 'INSERT INTO `' . DB_TABLE_DOCUMENTS_CSTM . '` SET ' . implode(', ', $insert) . "\n" .
                                 '  ON DUPLICATE KEY UPDATE ' . implode(', ', $update);
                        $db->Execute($query);
                        if ($db->ErrorNo()) {
                            $current_document_error = true;
                            $error_msg = sprintf($this->i18n('error_saving_additional_vars_multiple_documents'), $document_type->get('name'), '"' . implode('", "', $included_services) . '"');
                            if (!in_array($error_msg, $error_messages)) {
                                $error_messages[] = $error_msg;
                            }
                            break 2;
                        }

                    }
                }

                if (!$current_document_error) {
                    // insert model-to-cstm_model relations
                    if ($cstm_insert) {
                        $document->saveCstmRelatives(array(), $cstm_insert);
                    }

                    // find the needed additional variables
                    $nomenclatures_service_additional_vars = array(NOM_DOCUMENT_TYPE_TO_CREATE, NOM_INCLUDE_TEST);
                    $nomenclatures_clinical_additional_vars = array(NOM_TYPE_CLINICAL_TEST_UNIT, NOM_TYPE_CLINICAL_REFERENCE_RANGE);

                    //sql to take the ids of the needed additional vars
                    $sql_add_vars = 'SELECT fm.id, fm.name, fm.model_type FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Nomenclature" AND ' . "\n" .
                                    '  ((fm.name IN ("' . implode('","', $nomenclatures_service_additional_vars) . '") AND fm.model_type="' . NOM_TYPE_SERVICE . '") OR (fm.name IN ("' . implode('","', $nomenclatures_clinical_additional_vars) . '") AND fm.model_type="' . NOM_TYPE_CLINICAL . '"))';
                    $add_vars = $this->registry['db']->GetAll($sql_add_vars);

                    $document_type_to_create_id = '';
                    $include_test_id = '';
                    $price_id = '';
                    $test_unit_id = '';
                    $reference_range_id = '';

                    foreach ($add_vars as $add_var) {
                        if ($add_var['model_type'] == NOM_TYPE_SERVICE) {
                            if ($add_var['name'] == NOM_DOCUMENT_TYPE_TO_CREATE) {
                                $document_type_to_create_id = $add_var['id'];
                            } elseif ($add_var['name'] == NOM_INCLUDE_TEST) {
                                $include_test_id = $add_var['id'];
                            }
                        } elseif ($add_var['model_type'] == NOM_TYPE_CLINICAL) {
                            if ($add_var['name'] == NOM_TYPE_CLINICAL_TEST_UNIT) {
                                $test_unit_id = $add_var['id'];
                            } elseif ($add_var['name'] == NOM_TYPE_CLINICAL_REFERENCE_RANGE) {
                                $reference_range_id = $add_var['id'];
                            }
                        }
                    }

                    $sql_noms = array();
                    $sql_noms['select'] = 'SELECT n_cstm_include_test.value as article_id, nomi18n.name as article_name, "1" as quantity, ' . "\n" .
                                          '  nom_1.sell_price as price, n_cstm_test_unit.value as free_field2, n_cstm_reference_range.value as free_text2' . "\n";
                    $sql_noms['from']   = 'FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                          'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_document_type_to_create' . "\n" .
                                          '  ON (n_cstm_document_type_to_create.model_id=nom.id AND n_cstm_document_type_to_create.var_id="' . $document_type_to_create_id . '" AND n_cstm_document_type_to_create.value="' . DOCUMENT_CLINICAL_EXAMINATION . '")' . "\n" .
                                          'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_include_test' . "\n" .
                                          ' ON (n_cstm_include_test.model_id=nom.id AND n_cstm_include_test.var_id="' . $include_test_id . '" AND n_cstm_include_test.num=n_cstm_document_type_to_create.num AND n_cstm_include_test.value!="" AND n_cstm_include_test.value!="0" AND n_cstm_include_test.value IS NOT NULL)' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom_1' . "\n" .
                                          '  ON (nom_1.id=n_cstm_include_test.value)' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                          '  ON (nomi18n.parent_id=n_cstm_include_test.value AND nomi18n.lang="' . $added_document->get('model_lang') . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_test_unit' . "\n" .
                                          '  ON (n_cstm_test_unit.model_id=n_cstm_include_test.value AND n_cstm_test_unit.var_id="' . $test_unit_id . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_reference_range' . "\n" .
                                          '  ON (n_cstm_reference_range.model_id=n_cstm_include_test.value AND n_cstm_reference_range.var_id="' . $reference_range_id . '")' . "\n";
                    $sql_noms['where']  = 'WHERE nom.id IN ("' . implode('","', array_keys($included_services)) . '")' . "\n";
                    $sql_noms['order']  = 'ORDER BY find_in_set(nom.id, "' . implode(',', array_keys($included_services)) . '"), n_cstm_document_type_to_create.num';

                    $query_noms = implode("\n", $sql_noms);
                    $results_gt2_nom = $this->registry['db']->GetAll($query_noms);

                    // get all the vars
                    $vars_list = $added_document->get('vars');
                    $gt2_vars = '';
                    $gt2_var_idx = '';

                    // find the needed vars var
                    foreach ($vars_list as $var_idx => $vl) {
                        if ($vl['type'] == 'gt2') {
                            $gt2_vars = $vl;
                            $gt2_var_idx = $var_idx;
                            break;
                        }
                    }

                    $gt2_vars['values'] = $results_gt2_nom;

                    // set the new values in the document
                    $this->registry->set('edit_all', true, true);
                    $added_document->set('grouping_table_2', $gt2_vars, true);
                    $added_document->calculateGT2();
                    $added_document->set('table_values_are_set', true, true);
                    $added_document->saveGT2Vars();
                    $this->registry->set('edit_all', false, true);
                }

                if ($current_document_error) {
                    $error = true;
                } else {
                    // Get the new document
                    $new_document_filters = array('where'      => array('d.id = \'' . $added_document->get('id') . '\''),
                                                  'model_lang' => $added_document->get('model_lang'));
                    $new_document = Documents::searchOne($this->registry, $new_document_filters);

                    // Load the additional vars of the new document
                    $this->registry->set('get_old_vars', true, true);
                    $new_document->getVars();
                    $this->registry->set('get_old_vars', false, true);

                    // Write history
                    Documents_History::saveData($this->registry, array('model'       => $new_document,
                                                                       'action_type' => 'edit',
                                                                       'new_model'   => $new_document,
                                                                       'old_model'   => $added_document->sanitize()));

                    $url = sprintf('<a href="%s?%s=documents&amp;documents=view&amp;view=%s" target="_blank">%s</a>',
                                   $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                   $new_document->get('id'), $document_type->get('name'));
                    $successful_messages[] = $this->i18n('message_successful_added_reprobiomed_document', array($url));
                    $related_gt2_rows[$new_document->get('id')] = array();
                    foreach ($create_clinical_rows as $cmr) {
                        $related_gt2_rows[$new_document->get('id')][] = $cmr['leaving_form_id'];
                        $mark_gt2_rows[] = $cmr['gt2_id'];
                    }
                }
            } else {
                // ADD ERROR
                $error_messages[] = sprintf($this->i18n('error_adding_new_multiple_reprobiomed_document'), $document_type->get('name'), '"' . implode('", "', $included_services) . '"');
                foreach ($this->registry['messages']->getErrors() as $err) {
                    $error_messages[] = $err;
                }
                $error = true;
            }
        }

        if (!empty($related_gt2_rows)) {
            $relatives_queries = array();
            $query_pattern = 'INSERT IGNORE INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' (`parent_id`, `parent_model_name`, `link_to`, `link_to_model_name`)' . "\n" .
                             'VALUES ("%d", "Document", "%d", "Finance_Incomes_Reason")' . "\n";
            foreach ($related_gt2_rows as $doc_id => $rel_gt2_rows) {
                foreach ($rel_gt2_rows as $leaving_form_id) {
                    $relatives_queries[] = sprintf($query_pattern, $doc_id, $leaving_form_id);
                }
            }

            foreach ($relatives_queries as $rel_query) {
                $db->Execute($rel_query);
            }
        }

        if (!empty($mark_gt2_rows)) {
            $mark_gt2_rows = array_unique($mark_gt2_rows);
            // build update query
            $update_gt2_query = 'UPDATE ' . DB_TABLE_GT2_DETAILS . ' SET `free_field5`="1" WHERE `id` IN ("' . implode('","', $mark_gt2_rows) . '")';
            $db->Execute($update_gt2_query);
        }

        // check if an uncaught error has failed the whole db transaction
        if (!$error && $db->HasFailedTrans()) {
            $error_messages[] = $this->i18n('error_adding_new_reprobiomed_document_db_fail');
            $error = true;
        }

        if ($error) {
            $db->FailTrans();
            $successful_messages = array();
        }
        $db->CompleteTrans();

        if ($error) {
            // return to ajax function
            print json_encode(array('result' => !$error, 'messages' => $error_messages, 'redirect' => false));
        } else {
            // write messages
            foreach ($successful_messages as $su_mes) {
                $this->registry['messages']->setMessage($su_mes);
            }
            foreach ($error_messages as $err_mes) {
                $this->registry['messages']->setWarning($err_mes);
            }
            $this->registry['messages']->insertInSession($this->registry);

            // redirect
            $redirectUrl = sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $this->registry->get('module_param'), 'reports', 'report_type', $report);
            print json_encode(array('result' => !$error, 'messages' => array(), 'redirect' => $redirectUrl));
        }
        exit;
    }
}

?>
