<?php
Class Brainstorm_Time_Worked Extends Reports {
    public static function buildQuery(&$registry, $filters = array()) {
        // Prepare an array for the final results
        $final_results = array();
        $error = false;
        $settings = self::getReportSettings($registry);

        $month_period = (!empty($filters['month']) && !empty($filters['year']));
        $dates_period = (!empty($filters['date_from']) && !empty($filters['date_to']));

        if (!$month_period && !$dates_period) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            $final_results['additional_options']['dont_show_export_button'] = true;
            $error = true;
        } elseif ($month_period && $dates_period) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_complete_only_one_period_filter'));
            $final_results['additional_options']['dont_show_export_button'] = true;
            $error = true;
        } elseif ($dates_period && $filters['date_from'] > $filters['date_to']) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_date_from_after_date_to'));
            $final_results['additional_options']['dont_show_export_button'] = true;
            $error = true;
        } else {
            // Set model lang
            if (empty($filters['model_lang'])) {
                // Default model language is the interface language
                $model_lang = $registry['lang'];
            } else {
                $model_lang = $filters['model_lang'];
            }

            $filter_employees = self::getCustomers($filters, $model_lang, $registry);

            if (empty($filter_employees)) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_missing_employees'));
                $error = true;
                return false;
            }

            $where_in = '(' . implode(', ', array_keys($filter_employees)) . ')';

            if ($month_period) {
                $year = $filters['year'];
                $month = (strlen($filters['month']) == 1) ? '0'.$filters['month'] : $filters['month'];
                $period_start = new DateTime($year . '-' . $month);
                $period_end = clone($period_start);
                $period_end->add(new DateInterval('P1M'));
                $sql_filter_period = " AND MONTH(d.date) = '{$filters['month']}' AND YEAR(d.date) = '{$filters['year']}'";
                $sql_events_filter = " AND (
                    MONTH(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia')) = '{$month}'
                    AND
                    YEAR(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia')) = '{$year}'
                    OR(
                      # Starts in previous month/year
                      DATE_FORMAT(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia'), '%Y-%m') < '{$year}-{$month}' AND
                      #Ends in future month/year
                      DATE(DATE_ADD(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia'), INTERVAL e.duration MINUTE)) >= '{$year}-{$month}-00'
                    )
                )";
                $sql_nonworking_dates = "`date` LIKE '{$year}-{$month}%' OR `date` LIKE '{$year}-{$month}-%'";
            } else {
                $period_start = new DateTime($filters['date_from']);
                $period_end = new DateTime($filters['date_to']);
                $period_end->add(new DateInterval('P1D'));
                $sql_filter_period = " AND d.date >= '{$period_start->format('Y-m-d')}' AND d.date < '{$period_end->format('Y-m-d')}'";
                $sql_events_filter = " AND (
                    DATE(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia')) >= '{$period_start->format('Y-m-d')}'
                    AND
                    DATE(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia')) < '{$period_end->format('Y-m-d')}'
                    OR(
                      # Starts in previous month/year
                      DATE(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia')) < '{$period_start->format('Y-m-d')}' AND
                      #Ends in future month/year
                      DATE(DATE_ADD(CONVERT_TZ(e.event_start, 'UTC', 'Europe/Sofia'), INTERVAL e.duration MINUTE)) >= '{$period_end->format('Y-m-d')}'
                    )
                )";
                $sql_nonworking_dates = "`date` >= '{$filters['date_from']}' AND `date` < '{$filters['date_to']}'";
            }


            // Get employees' checkin / checkout data from documents
            $sql = "
            SELECT dc.`value` as 'time', dc2.`value` as 'hour_in', dc3.`value` as 'hour_out', d.`date`, c.`id` as 'id', ci18n.`name`
            FROM " . DB_TABLE_DOCUMENTS . " d
            JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc
              ON d.id = dc.model_id
              AND d.active = 1
              AND d.deleted_by = 0
              AND d.date != 0 {$sql_filter_period}
            JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc1
              ON dc1.var_id = 2103
              AND dc.num = dc1.num
              AND dc.model_id = dc1.model_id
              AND dc.var_id = 2108
            JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc2
              ON dc2.var_id = 2106
              AND dc.num = dc2.num
              AND dc.model_id = dc2.model_id
            JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc3
              ON dc3.var_id = 2107
              AND dc.num = dc3.num
              AND dc.model_id = dc3.model_id
            JOIN " . DB_TABLE_CUSTOMERS . " c
              ON c.ucn != ''
              AND c.id IN {$where_in}
              AND dc1.`value` = c.ucn
              AND c.type = 1
              AND c.active = 1
            JOIN " . DB_TABLE_CUSTOMERS_I18N . " ci18n
              ON c.id = ci18n.parent_id
              AND ci18n.lang='{$model_lang}' 
            ";
            $employees_checkin_data = $registry['db']->GetAll($sql);

            // Get event types from report settings
            $event_types_auto_all = preg_split('/\s*,\s*/', $settings['event_types_auto_all']);
            $event_types_auto_assignments = preg_split('/\s*,\s*/', $settings['event_types_auto_assignments']);
            $event_types_manual_assignments = preg_split('/\s*,\s*/', $settings['event_types_manual_assignments']);

            // Get all events customers and events assignments, where event has been created by an automation (If event has a document relative), for specific document types
            $sql = "
                SELECT e.`id`, e.`type`, e.`event_start` as `event_start_utc`, e.`duration`, e.`allday_event`, e.`customer`, u.`employee` AS `participant_id`, e.`added_by`
                FROM " . DB_TABLE_EVENTS . " AS e
                JOIN " . DB_TABLE_EVENTS_TYPES . " AS et
                  ON e.type = et.id
                JOIN " . DB_TABLE_EVENTS_ASSIGNMENTS . " AS ea
                  ON e.id = ea.parent_id
                  AND (
                    ea.user_status = 'confirmed'
                    OR (
                      ea.user_status = 'denied'
                      AND
                      et.crontab = 1
                    )
                  )
                  AND e.type IN (" . implode(', ', $event_types_auto_all) . ")
                  AND e.active = 1
                  AND e.deleted_by = 0
                  AND e.status IN ('planning', 'finished')
                  {$sql_events_filter}
                JOIN " . DB_TABLE_USERS . " AS u
                  ON ea.participant_id = u.id
                  # Event must have the customer as an assigned user
                  AND u.employee = e.customer
                  AND u.employee IN {$where_in}
                LEFT JOIN " . DB_TABLE_EVENTS_RELATIVES . " AS er
                  ON e.id = er.parent_id
                  AND er.origin = 'document'";
            $employees_events_auto_all = $registry['db']->GetAll($sql);

            // Get all events assignments, where event has been created by an automation (If event has a document relative), for specific document types
            $sql = "
                SELECT e.`id`, e.`type`, e.`event_start` as `event_start_utc`, e.`duration`, e.`allday_event`, u.`employee` AS `participant_id`
                FROM " . DB_TABLE_EVENTS . " AS e
                JOIN " . DB_TABLE_EVENTS_TYPES . " AS et
                  ON e.type = et.id
                JOIN " . DB_TABLE_EVENTS_ASSIGNMENTS . " AS ea
                  ON e.id = ea.parent_id
                  AND e.type IN (" . implode(', ', $event_types_auto_assignments) . ")
                  AND e.active = 1
                  AND e.deleted_by = 0
                  AND e.status IN ('planning', 'finished')
                  {$sql_events_filter}
                  AND (
                    ea.user_status = 'confirmed'
                    OR (
                      ea.user_status = 'denied'
                      AND
                      et.crontab = 1
                    )
                  )
                JOIN " . DB_TABLE_USERS . " AS u
                  ON ea.participant_id = u.id
                  AND u.employee IN {$where_in}
                JOIN " . DB_TABLE_EVENTS_RELATIVES . " AS er
                  ON e.id = er.parent_id
                  AND er.origin = 'document';";
            $employees_events_auto_assignments = $registry['db']->GetAll($sql);

            // Get all events assignments, where its been created manually (If event does not have a document relative), for specific document types
            $sql = "
                SELECT e.`id`, e.`type`, e.`event_start` as `event_start_utc`, e.`duration`, e.`allday_event`, u.`employee` AS `participant_id`
                FROM " . DB_TABLE_EVENTS . " AS e
                JOIN " . DB_TABLE_EVENTS_ASSIGNMENTS . " AS ea
                  ON e.id = ea.parent_id
                  AND e.type IN (" . implode(', ', $event_types_manual_assignments) . ")
                  AND e.active = 1
                  AND e.deleted_by = 0
                  AND e.status IN ('planning', 'finished')
                  {$sql_events_filter}
                  AND ea.user_status = 'confirmed'
                JOIN " . DB_TABLE_USERS . " AS u
                  ON ea.participant_id = u.id
                  AND u.employee IN {$where_in}
                LEFT JOIN " . DB_TABLE_EVENTS_RELATIVES . " AS er
                  ON e.id = er.parent_id
                  AND er.origin = 'document'
                WHERE er.parent_id IS NULL";
            $employees_events_manual_assignments = $registry['db']->GetAll($sql);

            // Merge all 3 employees_events together
            $employees_events = array_merge($employees_events_auto_all, array_merge($employees_events_auto_assignments, $employees_events_manual_assignments));

            $organized_employee_events = array();
            // Organize events in easy to use format
            if (!empty($employees_events)) {
                foreach ($employees_events as $employee_event) {
                    list('id' => $event_id, 'allday_event' => $allday_event, 'event_start_utc' => $event_start_utc, 'duration' => $duration, 'participant_id' => $participant_id) = $employee_event;

                    $event_start = $event_start_utc;
                    // Events must be converted to correct timezone when working with hours (Should not matter if event is allday)
                    if (!$allday_event) {
                        $event_start = new DateTime($event_start_utc, new DateTimeZone('UTC'));
                        $event_start->setTimezone(new DateTimeZone('Europe/Sofia'));
                        $event_start = $event_start->format('Y-m-d H:i:s');
                    }

                    // Find the event`s end date
                    $event_start_date_time = new DateTime($event_start);
                    $event_end_date_time = clone $event_start_date_time;
                    $event_end_date_time->add(new DateInterval("PT{$duration}M"));;
                    $event_end = $event_end_date_time->format('Y-m-d H:i:s');

                    // Add event end date to array
                    $employee_event['event_start'] = $event_start;
                    $employee_event['event_end'] = $event_end;

                    // Cleanup unnecessary data
                    unset($employee_event['participant_id']);
                    unset($employee_event['event_start_utc']);

                    if (!empty($participant_id) && array_key_exists($participant_id, $filter_employees)) { // Returned events always contain participants
                        $organized_employee_events[$participant_id][$event_id] = $employee_event;
                    }
                    if (isset($employee_event['customer']) && !empty($employee_event['customer']) && array_key_exists($employee_event['customer'], $filter_employees)) { // Returned events not always contain customer
                        $organized_employee_events[$employee_event['customer']][$event_id] = $employee_event;
                    } else { // Skip event
                        continue;
                    }
                }
            }

            $organized_employee_checkin_data = array();
            // Organize checkin data in easy to use format
            if (!empty($employees_checkin_data)) {
                foreach ($employees_checkin_data as $employee) {
                    list('date' => $date, 'id' => $id) = $employee;

                    // Cleanup unnecessary data
                    unset($employee['date']);
                    unset($employee['id']);

                    // Try to correct an employee's time, when missing
                    if (empty($employee['time'])) {
                        $hour_in = new DateTime($employee['hour_in']);
                        $hour_out = new DateTime($employee['hour_out']);
                        $interval = $hour_in->diff($hour_out);
                        $employee['time'] = $interval->d * 7.5 * 60 + $interval->h * 60 + $interval->i;
                    }

                    $organized_employee_checkin_data[$id][$date][] = $employee;
                }
            }

            // Get official holiday and weekend days.
            $country_code = Calendars_Calendar::getCountryCode($registry);
            $query = "SELECT date FROM " . DB_TABLE_COUNTRY_NONWORKDAYS . " AS any_nonworking_days
                WHERE ({$sql_nonworking_dates}) 
                AND country='{$country_code}'";
            $nonworking_days = $registry['db']->GetCol($query);

            $date_from_string = $period_start->format('Y-m-d');
            $date_to_string = $period_end->format('Y-m-d');
            $date_to_string = date('Y-m-d', strtotime('-1 day', strtotime($date_to_string)));
            // Count of workdays in selected month
            $sum_workdays_in_month = Calendars_Calendar::getWorkingDays($registry, $date_from_string, $date_to_string);

            // Get events for the non working days, marked from the company
            $sql = "
                SELECT e.`id`, e.`type`, e.`event_start` as `event_start_utc`, e.`duration`, e.`allday_event`
                FROM " . DB_TABLE_EVENTS . " AS e
                WHERE e.type=" . EVENT_TYPE_ADDITIONAL_DAYS_OFF . "
                  AND e.active = 1
                  AND e.`allday_event`=1
                  AND e.deleted_by = 0
                  AND e.status IN ('planning', 'finished')
                  AND e.customer='" . CUSTOMER_EVENT_ADDITIONAL_DAYS_OFF . "'
                  {$sql_events_filter}";
            $additional_days_off = $registry['db']->GetAll($sql);
            foreach ($additional_days_off as $additional_day_event) {
                // Find the event`s end date
                $event_start_date_time = new DateTime($additional_day_event['event_start_utc']);
                $event_end_date_time = clone $event_start_date_time;
                $event_end_date_time->add(new DateInterval("PT{$additional_day_event['duration']}M"));;
                $event_start = $event_start_date_time->format('Y-m-d');
                $event_end = $event_end_date_time->format('Y-m-d');

                // compare with the months first date
                if ($date_from_string>$event_start) {
                    $event_start = $date_from_string;
                }
                // compare with the months last date
                if ($event_end > $date_to_string) {
                    $event_end = $date_to_string;
                }

                // get the number of working days between the two
                $additional_days_off_count = Calendars_Calendar::getWorkingDays($registry, $event_start, $event_end);
                if ($additional_days_off_count) {
                    $sum_workdays_in_month -= $additional_days_off_count;
                }
            }

            // Using a lot of comments, since Im sure I wont understand this after a couple of days after finishing.
            // Calculations based on employee's monthly events
            foreach($filter_employees as $employee_id => $employee_name) {
                // Keep track of how many minutes an employee must work each month. May vary based on sick leave and other events.
                $employee_minutes_norm = $sum_workdays_in_month * 7.5 * 60;
                $sum_minutes_worked = 0;
                $paid_leave = 0;
                $sick_leave = 0;
                $business_travel = 0;
                // Stores dates where employee has been on event, or has checked in/out.
                $days_at_work = array();
                $study = 0;
                $home_office_count = 0;
                $home_office_time = 0;
                $meetings_outside_count = 0;
                $meetings_outside_time = 0;
                $absence_allowed_count = 0;
                $absence_allowed_time = 0;
                $absence_worked_off = 0;
                $absence_not_worked_off = 0;
                $in_office_work_break_count = 0;
                $in_office_work_break_time = 0;
                $late_arrival_count = 0;
                $late_arrival_time = 0;
                $unmet_hours_norm_count = 0;
                $unmet_hours_norm_time = 0;
                $early_leave_count = 0;
                $early_leave_time = 0;
                $overtime = 0;
                $work_time_per_day = array();

                if (!empty($organized_employee_events) && array_key_exists($employee_id, $organized_employee_events)) {
                    // Cycle through events
                    foreach ($organized_employee_events[$employee_id] as $event) {
                        $interval = 0;
                        $event_start = new DateTime($event['event_start']);
                        $event_end = new DateTime($event['event_end']);
                        $event_period_dates = new DatePeriod($event_start, new DateInterval('P1D'),$event_end);

                        // Calculate hours worked per day
                        foreach ($event_period_dates as $key => $value) {
                            // If current event day is in another month
                            if($value >= $period_end || $value < $period_start) {
                                continue;
                            }

                            $date = $value->format('Y-m-d');
                            $date_start = $value;
                            $date_end = new DateTime($date);
                            $date_end->add(new DateInterval('P1D'));

                            // Filter out non-workdays
                            if(!array_key_exists($date,array_flip($nonworking_days))){
                                $amount = (isset($work_time_per_day[$date])) ? $work_time_per_day[$date] : 0;
                                // Calculate intersecting interval between selected month and event start/end
                                $start_interval_date = ($event_start >= $date_start ? clone $event_start : clone $date_start);
                                $end_interval_date = ($event_end >= $date_end ? clone $date_end : clone $event_end);
                                $interval = $start_interval_date->diff($end_interval_date);

                                // If event is of these types, add interval to sum variable, else subtract
                                if ($event['type'] == 9 || $event['type'] == 13 || $event['type'] == 1 || $event['type'] == 14 || $event['type'] == 3 || $event['type'] == 7) {
                                    $amount += $interval->i + ($interval->h * 60) + ($interval->d * 7.5 * 60);
                                } elseif ($event['type'] == 16) {
                                    $amount -= $interval->i + ($interval->h * 60) + ($interval->d * 7.5 * 60);
                                }

                                // You cant work more than 7.5 hours on out of office events.
                                if ($amount > 450) {
                                    $amount = 450;
                                } elseif ($amount < 0) {
                                    $amount = 0;
                                }

                                $work_time_per_day[$date] = $amount;
                                $days_at_work[$date] = $date;
                            }
                        }

                        // If event starts in previous month, but ends during selected. Subtract amount of time from start of month, to events end.
                        if ($event_start <= $period_start && $event_end < $period_end) {
                            $interval = $period_start->diff($event_end);
                        }

                        // If event starts in selected month, but ends in future one. Subtract amount of time from start of event, to months end.
                        if ($event_start >= $period_start && $event_end >= $period_end) {
                            $interval = $event_start->diff($period_end);
                        }

                        //If event starts and ends in the selected month.
                        if ($event_start >= $period_start && $event_end < $period_end) {
                            $interval = $event_start->diff($event_end);
                        }

                        // All workdays the event covers. Copying $interval days to variable, since I cant modify it directly.
                        $interval_days = $interval->days;

                        // Subtract holidays and weekends from events duration ($interval_days).
                        foreach ($nonworking_days as $k => $nonworking_day) {
                            $nonworking_day = new DateTime($nonworking_day);
                            $nonworking_day_end = clone $nonworking_day;
                            $nonworking_day_end->add(new DateInterval('P1D'));

                            if($event_start <= $nonworking_day && $event_end >= $nonworking_day_end){
                                $interval_days -= 1;
                            }
                        }

                        // How long the events duration is, in minutes
                        $event_duration = $interval->i + ($interval->h * 60) + ($interval_days * 7.5 * 60);
                        if ($event['type'] == 1) {
                            $meetings_outside_count += 1;
                            $meetings_outside_time += $event_duration;
                        }
                        if ($event['type'] == 3) {
                            $absence_allowed_count += 1;
                            $absence_allowed_time += $event_duration;
                        }
                        if ($event['type'] == 7 && $event['allday_event'] == 1) {
                            $paid_leave += $event_duration;
                        }
                        if ($event['type'] == 8 && $event['allday_event'] == 1) {
                            $sick_leave += $event_duration;
                        }
                        if ($event['type'] == 9) {
                            $business_travel += $event_duration;
                        }
                        if ($event['type'] == 13) {
                            $study += $event_duration;
                        }
                        if ($event['type'] == 14) {
                            $home_office_count += 1;
                            $home_office_time += $event_duration;
                        }
                        if ($event['type'] == 16) {
                            $in_office_work_break_count += 1;
                            $in_office_work_break_time += $event_duration;
                        }
                        // Add event`s time to employee`s hours worked
                        if ($event['type'] == 1 || $event['type'] == 9 || $event['type'] == 13 || $event['type'] == 14) {
                            $sum_minutes_worked += $event_duration;
                        }
                        // Subtract
                        if ($event['type'] == 16) {
                            $sum_minutes_worked -= $event_duration;
                        }
                        // Subtract events duration from monthly work hours
                        if (($event['type'] == 7 && $event['allday_event'] == 1) || ($event['type'] == 8 && $event['allday_event'] == 1) || $event['type'] == 15) {
                            // If event covers entire selected month
                            if ($event_start < $period_start && $event_end > $period_end) {
                                $employee_minutes_norm = 0;
                                continue;
                            }

                            $employee_minutes_norm -= $event_duration;
                            if ($employee_minutes_norm <= 0) {
                                $employee_minutes_norm = 0;
                            }
                        }
                    }
                }

                ksort($work_time_per_day);
                $time_test = array();
                // Calculations based on employee's monthly checkin/checkout data
                if (array_key_exists($employee_id, $organized_employee_checkin_data)) {
                    // Sum minutes between checkin / checkout for each record
                    foreach ($organized_employee_checkin_data[$employee_id] as $date => $records) {
                        $first_hour_in = new DateTime($records[0]['hour_in']);
                        $last_hour_out = new DateTime(end($records)['hour_out']);
                        $start_of_workday = clone $first_hour_in;
                        $start_of_workday = new DateTime($start_of_workday->format('Y-m-d') . ' 09:30:00');
                        $end_of_workday = new DateTime($start_of_workday->format('Y-m-d') . ' 17:00:00');
                        $start_of_workday_event = false;
                        $end_of_workday_event = false;

                        // Check if employee has event for this day, with start or end time equial to start/end time of workday
                        if(array_key_exists($employee_id, $organized_employee_events)) {
                            foreach ($organized_employee_events[$employee_id] as $event) {
                                $event_start_date_time = new DateTime($event['event_start']);
                                $event_end_date_time = new DateTime($event['event_end']);

                                // Does customer have event during start of workday
                                if ($event_start_date_time->format('Y-m-d') == $date && $event_start_date_time->format('H:i:s') <= $start_of_workday->format('H:i:s')) {
                                    $start_of_workday_event = true;
                                }

                                // Does customer have event during end of workday
                                if ($event_end_date_time->format('Y-m-d') == $date && $event_end_date_time->format('H:i:s') >= $end_of_workday->format('H:i:s')) {
                                    $end_of_workday_event = true;
                                }

                                // Does customer have event spanning through the whole workday
                                if ($event_start_date_time->format('Y-m-d H:i:s') <= $start_of_workday->format('Y-m-d H:i:s')   // Event starts <= 09:30
                                    && $event_end_date_time->format('Y-m-d H:i:s') >= $end_of_workday->format('Y-m-d H:i:s')    // Event ends >= 17:00
                                    && ($event['type'] != 15 || $event['type'] != 16) ) {   // Event isnt of type 15 / 16
                                    $start_of_workday_event = true;
                                    $end_of_workday_event = true;
                                }
                            }
                        }

                        // Check if employee's last data is before end of workday (17:00)
                        if ($last_hour_out < $end_of_workday && !$end_of_workday_event) {
                            $early_leave_count += 1;
                            $interval = $end_of_workday->diff($last_hour_out);
                            $early_leave_time += $interval->h * 60 + $interval->i;
                        }

                        // Check if employee's first data is after start of workday (09:30)
                        if ($first_hour_in > $start_of_workday && !$start_of_workday_event) {
                            $interval = $first_hour_in->diff($start_of_workday);
                            $late = $interval->h * 60 + $interval->i;
                            if ($late > 0) {
                                $late_arrival_count += 1;
                                $late_arrival_time += $late;
                            }
                        }

                        // Calculate work time per day, by checking for checkin data and events
                        foreach ($records as $record) {
                            if (array_key_exists($date, $time_test)) {
                                $time_test[$date] += $record['time'];
                            } else {
                                $time_test[$date] = $record['time'];
                            }
                            $amount = (isset($work_time_per_day[$date])) ? $work_time_per_day[$date] : 0;
                            $sum_minutes_worked += $record['time'];
                            $work_time_per_day[$date] = $amount + $record['time'];
                        }

                        if (!array_key_exists($date,$nonworking_days)) {
                            $days_at_work[$date] = $date;
                        }
                    }
                }

                ksort($work_time_per_day);
                // Calculate $unmet_hours_norm_time and overtime
                if (!empty($work_time_per_day)){
                    $count = count($work_time_per_day);
                    if($count < $sum_workdays_in_month){
                        $unmet_hours_norm_count += $sum_workdays_in_month - $count;
                        $unmet_hours_norm_time += $unmet_hours_norm_count * 7.5 * 60;
                    }

                    foreach ($work_time_per_day as $date => $work_time) {
                        if ($work_time / 60 < 7.5) {
                            $unmet_hours_norm_count += 1;
                            $unmet_hours_norm_time += 7.5 * 60 - $work_time;
                        }
                    }
                } else {
                    $unmet_hours_norm_count = $sum_workdays_in_month;
                    $unmet_hours_norm_time = $sum_workdays_in_month * 7.5 * 60;
                }

                if ($sum_minutes_worked > $employee_minutes_norm) {
                    $overtime = $sum_minutes_worked - $employee_minutes_norm;

                    // Calculate how much of the employee's absence has been worked off
                    if ($absence_allowed_time > 0 ) {
                        // Calculate employee's overtime
                        $absence_overtime = $sum_minutes_worked - $employee_minutes_norm;
                        // Subtract overtime from absence time
                        $absence_worked_off = $absence_overtime - $absence_allowed_time;

                        // You cant work off more than your absent amount
                        if ($absence_worked_off >= 0) {
                            $absence_worked_off = $absence_allowed_time;
                        } elseif ($absence_allowed_time > $absence_worked_off) {
                            $absence_not_worked_off = abs($absence_worked_off);
                            $absence_worked_off = $absence_overtime;
                        }
                    }
                }

                $days_at_work = $sum_workdays_in_month - count($days_at_work);
                // All time values are in minutes.
                $result = array(
                    'customer_name' => $employee_name,
                    'hours_norm' => self::convertToHoursMins($employee_minutes_norm),
                    'sum_hours_worked' => self::convertToHoursMins($sum_minutes_worked),
                    'paid_leave' => self::convertToHoursMins($paid_leave),
                    'sick_leave' => self::convertToHoursMins($sick_leave),
                    'business_travel' => self::convertToHoursMins($business_travel),
                    'self_reflection' => self::convertToHoursMins($days_at_work*7.5*60),
                    'study' => self::convertToHoursMins($study),
                    'home_office_count' => self::formatCount($home_office_count),
                    'home_office_time' => self::convertToHoursMins($home_office_time),
                    'meetings_outside_count' => self::formatCount($meetings_outside_count),
                    'meetings_outside_time' => self::convertToHoursMins($meetings_outside_time),
                    'absence_allowed_count' => self::formatCount($absence_allowed_count),
                    'absence_allowed_time' => self::convertToHoursMins($absence_allowed_time),
                    'absence_worked_off' => self::convertToHoursMins($absence_worked_off),
                    'absence_not_worked_off' => self::convertToHoursMins($absence_not_worked_off),
                    'in_office_work_break_count' => self::formatCount($in_office_work_break_count),
                    'in_office_work_break_time' => self::convertToHoursMins($in_office_work_break_time),
                    'late_arrival_count' => self::formatCount($late_arrival_count),
                    'late_arrival_time' => self::convertToHoursMins($late_arrival_time),
                    'early_leave_count' => self::formatCount($early_leave_count),
                    'early_leave_time' => self::convertToHoursMins($early_leave_time),
                    'unmet_hours_norm_count' => self::formatCount($unmet_hours_norm_count),
                    'unmet_hours_norm_time' => self::convertToHoursMins($unmet_hours_norm_time),
                    'overtime_time' => self::convertToHoursMins($overtime)
                );

                $final_results[] = $result;
            }
        }

        $final_results['additional_options']['error'] = $error;
        // Prepare the results
        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }

    /**
     * Formats given minutes to predefined formats, based on if minutes contain a full hour or not.
     *
     * @param int $time Minutes
     * @return string
     */
    private function convertToHoursMins($time) {
        if ($time <= 0) {
            return '';
        }

        $hours = floor($time / 60);
        $minutes = ($time % 60);

        if ($hours != 0 && $minutes != 0) {
            $format = '%2dч. и %2dм.';
            return sprintf($format, $hours, $minutes);
        } elseif ($hours == 0) {
            $format = '%2dм.';
            return sprintf($format, $minutes);
        } else {
            $format = '%2dч.';
            return sprintf($format, $hours);
        }

        return sprintf($format, $hours, $minutes);
    }

    /**
     * Formats given count.
     *
     * @param int $count
     * @return string
     */
    private function formatCount($count) {
        if (!$count) {
            return '';
        } else {
            return $count;
        }
    }


    /**
     * Returns Customers based on filters
     * 
     * @param $filters
     * @param $model_lang
     * @param $registry
     * @return mixed
     */
    private function getCustomers($filters, $model_lang, $registry) {
        $where_customer = array();
        $where_user = '';

        $users_full_access = array_filter(preg_split('#\s*,\s*#', USERS_FULL_ACCESS));

        if (in_array($registry['currentUser']->get('id'), $users_full_access)) {
            $filter_employees = array_values(array_filter(!empty($filters['customer']) ? $filters['customer'] : array()));
        } else {
            $filter_employees = array($registry['currentUser']->get('employee'));
        }

        if (empty($filter_employees)) {
            // check if the filter for department is completed
            $departments = array_filter(!empty($filters['department']) ? $filters['department'] : array());
            if (!empty($departments)) {
                // goes through all the structures and finds their subdepartments
                $departments_list = array();
                foreach ($departments as $dep) {
                    $departments_list = array_merge($departments_list, Departments::getTreeDescendantsIds($registry, $dep));
                }
                $departments_list = array_unique($departments_list);
                $where_customer[] = "AND c.department IN (" . implode(",", $departments_list) . ")";
            } else {
                // both filters are empty - we will need all the employees
                $departments_list_all = Departments::getTreeDescendantsIds($registry, 1);
                $where_customer[] = "AND c.department IN (" . implode(",", $departments_list_all) . ")";
            }

            $filter_offices = array_filter(!empty($filters['office']) ? $filters['office'] : array());
            if (!empty($filter_offices)) {
                $where_user = "AND u.office IN (" . implode(',',$filter_offices) . ")";
            }
        } else {
            $where_customer[] = "AND c.id IN (" . implode(',',$filter_employees) . ")";
        }

        $where_customer = implode(' ', $where_customer);
        $sql = "
            SELECT c.`id`, TRIM(CONCAT(ci18n.`name`, ' ', ci18n.`lastname`)) AS fullname
            FROM " . DB_TABLE_CUSTOMERS . " AS c
            JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci18n
              ON c.id = ci18n.parent_id
              {$where_customer}
              AND ci18n.lang='{$model_lang}'
              AND c.active=1
              AND c.deleted_by=0
              AND c.type=" . PH_CUSTOMER_EMPLOYEE . "
            JOIN " . DB_TABLE_USERS . " AS u
              ON u.employee = c.id
              {$where_user}
              ;";
        $result = $registry['db']->GetAssoc($sql);

        return $result;
    }

}
