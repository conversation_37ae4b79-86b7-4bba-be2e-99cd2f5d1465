<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {

            define('DOCUMENT_INSTALLING_CARD_ID', 8);
            define('DOCUMENT_WORKING_CARD_ID', 13);
            define('WORKER_NAME', 'worker_name');
            define('DATE', 'date_fitting');
            define('OPERATION_NAME', 'operation_name');
            define('ARTICLES_MEASURE', 'articles_measure');
            define('MATERIAL', 'material_work');
            define('WORK_DONE', 'work_num');
            define('WORK_MEASURE', 'work_measure');

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE EMPLOYEE FILTER
            //get employees' options
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_employees = array('model_lang'    => $registry['lang'],
                                       'sanitize'      => true,
                                       'sort'          => array('ci18n.name', 'ci18n.lastname'),
                                       'where'         => array('c.type = ' . PH_CUSTOMER_EMPLOYEE,
                                                                'c.active = 1',
                                                                '(ci18n.company_department="' . $this->i18n('reports_department_produce') . '" OR ci18n.company_department="' . $this->i18n('reports_department_install') . '")')
            );
            $employees = Customers::search($registry, $filters_employees);

            //prepare documents' types groups
            $options_employees = array();

            foreach($employees as $employee) {
                $options_employees[] = array(
                    'label'         => $employee->get('name') . ($employee->get('lastname') ? (' ' . $employee->get('lastname')) : ''),
                    'option_value'  => $employee->get('id')
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'employee',
                'name'      => 'employee',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_employee'),
                'help'      => $this->i18n('reports_employee'),
                'options'   => $options_employees,
            );
            $filters['employee'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE KEY WORDS FILTER
            $filter = array (
                'custom_id' => 'key_words',
                'name'      => 'key_words',
                'type'      => 'text',
                'label'     => $this->i18n('reports_key_words'),
                'help'      => $this->i18n('reports_key_words')
            );
            $filters['key_words'] = $filter;

            return $filters;
        }
    }
?>