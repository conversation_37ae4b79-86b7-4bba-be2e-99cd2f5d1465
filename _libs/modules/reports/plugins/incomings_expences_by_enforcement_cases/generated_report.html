<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border"><div style="width: 190px;">{#reports_type_case#|escape}</div></td>
          <td class="t_border"><div style="width: 140px;">{#reports_total_owed_due#|escape}</div></td>
          <td class="t_border"><div style="width: 90px;">{#reports_count_cases#|escape}</div></td>
          <td class="t_border"><div style="width: 140px;">{#reports_collected_sums_period#|escape}</div></td>
          <td class="t_border"><div style="width: 140px;">{#reports_collected_sums#|escape}</div></td>
          <td><div style="width: 140px;">{#reports_expenses_period#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.table1 item=result name=results key=category_data}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_border" width="194">
              {$result.type|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" align="right" width="144">
              {$result.total_owed_due|string_format:"%.2f"|default:"0.00"}
            </td>
            <td class="t_border" align="right" width="94">
              {$result.total_projects|string_format:"%d"|default:"0"}
            </td>
            <td class="t_border" align="right" width="144">
              {$result.collected_sums_period|string_format:"%.2f"|default:"0.00"}
            </td>
            <td class="t_border" align="right" width="144">
              {$result.collected_sums|string_format:"%.2f"|default:"0.00"}
            </td>
            <td align="right" width="144">
              {$result.expenses_period|string_format:"%.2f"|default:"0.00"}
            </td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td style="padding-top: 20px;">
      <table cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border"><div style="width: 190px;">{#reports_first_lastname#|escape}</div></td>
          <td class="t_border"><div style="width: 140px;">{#reports_total_owed_due#|escape}</div></td>
          <td class="t_border"><div style="width: 90px;">{#reports_count_cases#|escape}</div></td>
          <td class="t_border"><div style="width: 140px;">{#reports_collected_sums_period#|escape}</div></td>
          <td class="t_border"><div style="width: 140px;">{#reports_collected_sums#|escape}</div></td>
          <td><div style="width: 140px;">{#reports_expenses_period#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.table2 item=result name=results key=category_data}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_border" width="194">
              {$result.type|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" align="right" width="144">
              {$result.total_owed_due|string_format:"%.2f"|default:"0.00"}
            </td>
            <td class="t_border" align="right" width="94">
              {$result.total_projects|string_format:"%d"|default:"0"}
            </td>
            <td class="t_border" align="right" width="144">
              {$result.collected_sums_period|string_format:"%.2f"|default:"0.00"}
            </td>
            <td class="t_border" align="right" width="144">
              {$result.collected_sums|string_format:"%.2f"|default:"0.00"}
            </td>
            <td align="right" width="144">
              {$result.expenses_period|string_format:"%.2f"|default:"0.00"}
            </td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>