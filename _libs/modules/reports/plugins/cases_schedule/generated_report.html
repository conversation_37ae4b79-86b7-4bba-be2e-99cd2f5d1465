<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_document_num#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_manager#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_date_hour#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_validity_term#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_notification_type#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_court#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_chamber#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_proceeding_num#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 240px;">{#reports_customer#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_lev_ins_is#|escape}</div></td>
          <td style="vertical-align: middle;"><div>{#reports_department#|escape}</div></td>
        </tr>
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}{if $result.archive}&amp;archive=1{/if}">{$result.document_num|escape|default:"&nbsp;"}</a>
            </td>
            <td class="t_border">
              {$result.manager_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.notification_date|date_format:#date_mid#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.validity_term|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.notification_type|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.court_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.chamber|escape|trim|nl2br|default:"&nbsp;"}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$result.project}">{if $result.case_num}{$result.case_num|escape|default:"&nbsp;"}{else}<i>[{#reports_no_case_num#}]</i>{/if}</a>
            </td>
            <td class="t_border" width="244">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.customer}">{$result.customer_name|escape|default:"&nbsp;"}</a>
            </td>
            <td class="t_border">
              {$result.lev_ins_is|escape|default:"&nbsp;"}
            </td>
            <td>
              {$result.department|escape|default:"&nbsp;"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="11">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="11"></td>
        </tr>
      </table>
      <br />
      <br />

      <h1>{#reports_count_documents#|escape|default:"&nbsp;"}: <strong>{$reports_additional_options.count_filttered_documents|default:'0'}</strong></h1>
      <h1>{#reports_count_notices#|escape|default:"&nbsp;"}: <strong>{$reports_additional_options.added_subpoenas_count|default:'0'}</strong></h1>
      <h1>{#reports_count_court_proceedings#|escape|default:"&nbsp;"}: <strong>{$reports_additional_options.court_proceedings_count|default:'0'}</strong></h1>
    </td>
  </tr>
</table>