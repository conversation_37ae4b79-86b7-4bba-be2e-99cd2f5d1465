<?php
    Class Netreact_Project_Incomes_Expenses Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            if (empty($filters['project'])) {
                $registry['messages']->setError($registry['translater']->translate('error_report_select_project'));
                return array(array(), 0);
            }

            //get project's data
            $query = 'SELECT pcstm1.value as b_type, fo.label as b_type_name, pcstm2.value as b_cost, 0 as total_hours, 0 as total_expenses' . "\n" .
                    'FROM ' . DB_TABLE_PROJECTS . ' p' . "\n" .
                    'JOIN ' . DB_TABLE_FIELDS_META . ' fm1' . "\n" .
                    '  ON fm1.model = "Project" AND fm1.model_type = p.`type` AND fm1.name = "budget_type"' . "\n" .
                    'JOIN ' . DB_TABLE_FIELDS_META . ' fm2' . "\n" .
                    '  ON fm2.model = "Project" AND fm2.model_type = p.`type` AND fm2.name = "budget_allcost"' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pcstm1' . "\n" .
                    '  ON pcstm1.model_id = p.id AND pcstm1.var_id = fm1.id' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pcstm2' . "\n" .
                    '  ON pcstm2.model_id = p.id AND pcstm2.var_id = fm2.id' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' fo' . "\n" .
                    '  ON fo.parent_name = "budget_type" AND fo.option_value = pcstm1.value AND fo.lang = "' . $model_lang . '"' . "\n" .
                    'WHERE p.id = ' . $filters['project'];
            $final_data = $registry['db']->GetRow($query);

            //get all timesheets for tasks related to the project
            $query = 'SELECT tt.* ' . "\n" .
                     'FROM ' . DB_TABLE_TASKS . ' t' . "\n" .
                     'JOIN ' . DB_TABLE_TASKS_TIMESHEETS . ' tt' . "\n" .
                     '  ON t.id = tt.task_id' . "\n" .
                     'WHERE t.project = ' . $filters['project'];
            $ts = $registry['db']->GetAll($query);
            $work = array();
            //get users work duration in minutes
            foreach ($ts as $t) {
                if (!isset($work[$t['user_id']])) {
                    $work[$t['user_id']] = 0;
                }
                $work[$t['user_id']] += $t['duration'];
            }
            $rates = array();
            if (!empty($work)) {
                //get the hour rate for each user via its relatet employee
                $query = 'SELECT DISTINCT(u.id), ccstm.value as rate,' . "\n" .
                         'TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) as name,' . "\n" .
                         'CONCAT(ui18n.firstname, " ", ui18n.lastname) as user_name' . "\n" .
                         'FROM ' . DB_TABLE_USERS . ' u' . "\n" .
                         'JOIN ' . DB_TABLE_USERS_I18N . ' ui18n' . "\n" .
                         '  ON u.id = ui18n.parent_id AND ui18n.lang = "' . $model_lang . '"' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' fm' . "\n" .
                         '  ON fm.model = "Customer" AND fm.model_type = ' . PH_CUSTOMER_EMPLOYEE . ' AND fm.name = "employee_pay_for_hour"' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' ccstm' . "\n" .
                         '  ON ccstm.model_id = u.employee AND ccstm.var_id = fm.id' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' ci18n' . "\n" .
                         '  ON ci18n.parent_id = u.employee AND ci18n.lang = "' . $model_lang . '"' . "\n" .
                         'WHERE u.id IN (' . implode(', ', array_keys($work)) . ')';
                $rates = $registry['db']->GetAssoc($query);
            }

            foreach ($work as $u => $w) {
                if (empty($rates[$u]['rate'])) {
                    $rates[$u]['rate'] = 0;
                }
                if (empty($rates[$u]['name'])) {
                    $rates[$u]['name'] = $registry['translater']->translate('error_no_employee_for_user') . ' (' . $rates[$u]['user_name'] . ')';
                }
                $rates[$u]['hours'] = round($w / 60, 2);
                $rates[$u]['total'] = round($rates[$u]['hours'] * $rates[$u]['rate'], 2);
                $final_data['total_expenses'] += $rates[$u]['total'];
                $final_data['total_hours'] += $rates[$u]['hours'];
            }
            $final_data['rates'] = $rates;
            if ($final_data['b_type'] == 1) {
                $final_data['total_incomes'] = $final_data['total_hours'] * $final_data['b_cost'];
            } else {
                $final_data['total_incomes'] = $final_data['b_cost'];
                $final_data['total_hours'] = '-';
            }
            $final_data['diff'] = $final_data['total_incomes'] - $final_data['total_expenses'];

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_data, 0);
            } else {
                $results = $final_data;
            }

            return $results;
        }
    }
?>