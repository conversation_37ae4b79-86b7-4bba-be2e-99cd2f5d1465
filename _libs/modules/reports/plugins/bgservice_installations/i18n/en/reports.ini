reports_th_info_for = Information for
reports_th_info_main_contact_persons = Main contact person:
reports_th_info_financial_contact_persons = Financial contact persons:
reports_th_info_installation_stage = Installation stage:
reports_th_info_installation_link = Link to installation:
reports_th_info_server_folder_link = Link to folder (server):
reports_th_info_it_support = IT Support:
reports_th_info_location = Location:

reports_th_records = Records
reports_th_records_pcs_for_work = pcs., for work:
reports_th_records_pcs = pcs.
reports_th_records_client_requests = Requests (client):
reports_th_records_internal_requests = Requests (internal):
reports_th_records_offers = Offers:
reports_th_records_software_contracts = Contracts (software):
reports_th_records_tr = Terms of reference:
reports_th_records_ppp = Acceptance protocols:
reports_th_records_guides = Guides:

reports_th_general_customer_information = General client information

reports_th_processes = Processes / Modules at the client
reports_th_processes_process_module = Process / Module
reports_th_processes_description = Description

reports_th_universal_functionalities = Universal functionalities
reports_th_universal_functionalities_name = Name
reports_th_universal_functionalities_description = Description
reports_th_universal_functionalities_bug = BUG №

reports_th_custom_functionalities = Special functionalities
reports_th_custom_functionalities_name = Name
reports_th_custom_functionalities_description = Description
reports_th_custom_functionalities_bug = BUG №

reports_to = to

error_reports_customer = No customer found!
error_reports_no_info = no information