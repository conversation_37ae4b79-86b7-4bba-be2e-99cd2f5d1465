<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="70%">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_document_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_service_description#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_service_measure#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_service_amount#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_service_price#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_total_service_price#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_total_service_price_taxes#|escape}</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border hright" nowrap="nowrap" width="25">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.customer_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.service_description|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.service_measure|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" align="right">
              {$result.service_amount|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" align="right">
              {$result.service_price|escape|default:"&nbsp;"}
            </td>
            <td class="t_border" align="right">
              {$result.total_service_price|escape|default:"&nbsp;"}
            </td>
            <td align="right">
              {$result.total_price_with_taxes|default:"&nbsp;"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="6">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        {if $reports_additional_options.total_price}
          <tr class="t_even">
            <td align="right" colspan="8"><strong>{#reports_total_price#|escape}:</strong></td>
            <td align="right"><strong>{$reports_additional_options.total_price|default:"0.00"}</strong></td>
          </tr>
        {/if}
        <tr>
          <td class="t_footer" colspan="9"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>