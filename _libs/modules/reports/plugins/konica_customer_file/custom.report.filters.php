<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'         => 'customer',
                'name'              => 'customer',
                'type'              => 'autocompleter',
                'required'          => 1,
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_customer'),
                'help'              => $this->i18n('reports_customer'),
                'value'             => ''
            );
            $filters['customer'] = $filter;

            //DEFINE EMPLOYEES FILTER
            require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
            $emp_filters = array('model_lang' => $registry->get('model_lang'),
                                 'where'      => array('c.type = ' . PH_CUSTOMER_EMPLOYEE),
                                 'sort'       => array('CONCAT(ci18n.name, \' \', ci18n.lastname)'),
                                 'sanitize'   => true);
            $customers = Customers::search($registry, $emp_filters);
            $options_employees = array();

            foreach($customers as $customer) {
                $options_employees[] = array(
                    'label'         => $customer->get('name').' '.$customer->get('lastname'),
                    'option_value'  => $customer->get('id')
                );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'employee',
                'name'      => 'employee',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_employee'),
                'help'      => $this->i18n('reports_employee_help'),
                'options'   => $options_employees,
            );
            $filters['employee'] = $filter;

            //DEFINE TYPE TABLE FILTER
            //prepare filters
            $filter = array (
                'custom_id' => 'type_table',
                'name'      => 'type_table',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_type_table'),
                'help'      => $this->i18n('reports_type_table_help'),
                'options'   => array(
                    array(
                        'label'         => $this->i18n('all'),
                        'option_value'  => 'all'
                    ),
                    array(
                        'label'         => $this->i18n('reports_option_calls'),
                        'option_value'  => 'calls'
                    ),
                    array(
                        'label'         => $this->i18n('reports_option_customer_meetings'),
                        'option_value'  => 'customer_meetings'
                    ),
                    array(
                        'label'         => $this->i18n('reports_option_offers'),
                        'option_value'  => 'offers'
                    ),
                    array(
                        'label'         => $this->i18n('reports_option_projects'),
                        'option_value'  => 'projects'
                    ),
                    array(
                        'label'         => $this->i18n('reports_option_current_tasks'),
                        'option_value'  => 'current_tasks'
                    ),
                    array(
                        'label'         => $this->i18n('reports_option_communications'),
                        'option_value'  => 'communications'
                    )
                ),
            );
            $filters['type_table'] = $filter;

            //DEFINE COUNT RECORDS FILTER
            //prepare filters
            $filter = array (
                'custom_id' => 'count_records',
                'name'      => 'count_records',
                'type'      => 'dropdown',
                'required'  => 1,
                'label'     => $this->i18n('reports_count_records'),
                'help'      => $this->i18n('reports_count_records_help'),
                'options'   => array(
                    array(
                        'label'         => 3,
                        'option_value'  => sprintf('%s', 3)
                    ),
                    array(
                        'label'         => 5,
                        'option_value'  => sprintf('%s', 5)
                    ),
                    array(
                        'label'         => 10,
                        'option_value'  => sprintf('%s', 10)
                    ),
                    array(
                        'label'         => 25,
                        'option_value'  => sprintf('%s', 25)
                    ),
                    array(
                        'label'         => 50,
                        'option_value'  => sprintf('%s', 50)
                    ),
                    array(
                        'label'         => 75,
                        'option_value'  => sprintf('%s', 75)
                    ),
                    array(
                        'label'         => 100,
                        'option_value'  => sprintf('%s', 100)
                    ),
                ),
            );
            $filters['count_records'] = $filter;

            return $filters;
        }
    }
?>