<?php
    Class Crm_Customer_Status Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //additional vars
            define('CALL_DESCRIPTION', 'call_description');
            define('CALL_DATE', 'list_to_date');
            define('MEETING_PROTOCOL_DATE', 'meeting_date');
            define('MEETING_PROTOCOL_RESULT', 'result_meeting');
            define('MEETING_PROTOCOL_RESUME', 'resume');
            define('DAILY_REPORTS_CLIENT_NAME', 'client_id');

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            // GET AVAILABLE TYPES
            if (!empty($filters['customer_type'])) {
                $available_types = $filters['customer_type'];
            } else {
                $available_types = explode(',', CUSTOMER_STATUS_CUSTOMER_TYPES);
            }

            //START PREPARING THE SUBQUERIES
            $queries = array();

            /*
             * CONTRACTS, OFFERS AND POOL BLANKS
             */
            $sql_offers_pools_contracts = array();
            $sql_offers_pools_contracts['select'] = 'SELECT d.id as rec_id, "document" as rec_type, ' . "\n" .
                                                    '  d.type as type, dti18n.name as type_name, d.status as last_action, ds.name as substatus, ' . "\n" .
                                                    '  d.customer as customer, d.status_modified as last_action_date, d.modified as date_modified, ' . "\n" .
                                                    '  CONCAT(empi18n.name, " ", empi18n.lastname) as last_action_done_by, 1 as num' . "\n";

            $sql_offers_pools_contracts['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                                                    'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                                                    '  ON (c.id=d.customer AND c.deleted_by="0" AND c.active="1" AND c.type IN (' . implode(',', $available_types) . '))' . "\n" .
                                                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' as ds' . "\n" .
                                                    '  ON (ds.id=d.substatus AND ds.lang="' . $model_lang . '")' . "\n" .
                                                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' as dti18n' . "\n" .
                                                    '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as empi18n' . "\n" .
                                                    '  ON (empi18n.parent_id=d.employee AND empi18n.lang="' . $model_lang . '")' . "\n";

            $where = array();
            $where[] = 'd.type IN (' . CUSTOMER_STATUS_DOCUMENT_OFFER_ENQUIRY . ',' . CUSTOMER_STATUS_DOCUMENT_OFFER  . ',' . CUSTOMER_STATUS_DOCUMENT_ORDER  . ')';
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';
            if (!empty($filters['employee'])) {
                $where[] = 'd.employee IN (' . implode(',', $filters['employee']) . ')';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }
            $sql_offers_pools_contracts['where'] = 'WHERE ' . implode(' AND ', $where);

            $queries[] = implode("\n", $sql_offers_pools_contracts);


            /*
             * DOCUMENTS CALLS LIST
             */
            $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm 
            WHERE fm.model="Document" 
                AND fm.model_type=' . CUSTOMER_STATUS_DOCUMENT_CALLS_LIST . ' 
                AND (fm.name="' . CALL_DATE . '" OR fm.name="' . CALL_DESCRIPTION . '") 
                ORDER BY fm.position';
            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            $call_date_id = '';
            $call_description_id = '';

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == CALL_DATE) {
                    $call_date_id = $vars['id'];
                } elseif ($vars['name'] == CALL_DESCRIPTION) {
                    $call_description_id = $vars['id'];
                }
            }

            $sql_calls_list = array();
            $sql_calls_list['select'] = 'SELECT d.id as rec_id, "document" as rec_type, ' . "\n" .
                                        '  d.type as type, dti18n.name as type_name, d_cstm_description.value as last_action, ds.name as substatus, ' . "\n" .
                                        '  d.customer as customer, d_cstm_call_date.value as last_action_date, d.modified as date_modified, ' . "\n" .
                                        '  CONCAT(empi18n.name, " ", empi18n.lastname) as last_action_done_by, 1 as num' . "\n";

            $sql_calls_list['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' as dti18n' . "\n" .
                                        '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' as ds' . "\n" .
                                        '  ON (ds.id=d.substatus AND ds.lang="' . $model_lang . '")' . "\n" .
                                        'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                                        '  ON (c.id=d.customer AND c.deleted_by="0" AND c.active="1" AND c.type IN (' . implode(',', $available_types) . '))' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as empi18n' . "\n" .
                                        '  ON (empi18n.parent_id=d.employee AND empi18n.lang="' . $model_lang . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_call_date' . "\n" .
                                        '  ON (d_cstm_call_date.model_id=d.id AND d_cstm_call_date.var_id="' . $call_date_id . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_description' . "\n" .
                                        '  ON (d_cstm_description.model_id=d.id AND d_cstm_description.var_id="' . $call_description_id . '")' . "\n";

            $where = array();
            $where[] = 'd.type="' . CUSTOMER_STATUS_DOCUMENT_CALLS_LIST . '"';
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';
            if (!empty($filters['employee'])) {
                $where[] = 'd.employee IN (' . implode(',', $filters['employee']) . ')';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_call_date.value, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_call_date.value, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }
            $sql_calls_list['where'] = 'WHERE ' . implode(' AND ', $where);

            $queries[] = implode("\n", $sql_calls_list);


            /*
             * DOCUMENTS MEETING PROTOCOLS
             */
            $sql_for_add_vars = 'SELECT fm.id, fm.name 
                                  FROM ' . DB_TABLE_FIELDS_META . ' AS fm 
                                  WHERE fm.model="Document" 
                                    AND fm.model_type=' . CUSTOMER_STATUS_DOCUMENT_MEETINGS_PROTOCOL . ' 
                                    AND (fm.name="' . MEETING_PROTOCOL_DATE . '" OR fm.name="' . MEETING_PROTOCOL_RESULT . '") 
                                  ORDER BY fm.position';
            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == MEETING_PROTOCOL_RESULT) {
                    $meeting_protocol_result_id = $vars['id'];
                } else if ($vars['name'] == MEETING_PROTOCOL_DATE) {
                    $meeting_protocol_date_id = $vars['id'];
                }
            }

            //prepare presort
            $documents_meetings_presort = array();

            $where = array();
            $where[] = 'd.type="' . CUSTOMER_STATUS_DOCUMENT_MEETINGS_PROTOCOL . '"';
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';
            if (!empty($filters['employee'])) {
                $where[] = 'd.employee IN (' . implode(',', $filters['employee']) . ')';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_meeting_start.value, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_meeting_start.value, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }

            $get_doc_meet_ids_query = 'SELECT d.id' . "\n" .
                                      'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                                      '  ON (c.id=d.customer AND c.deleted_by="0" AND c.active="1" AND c.type IN (' . implode(',', $available_types) . '))' . "\n" .
                                      'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_meeting_start' . "\n" .
                                      '  ON (d_cstm_meeting_start.model_id=d.id AND d_cstm_meeting_start.var_id="' . $meeting_protocol_date_id . '")' . "\n" .
                                      'WHERE ' . implode(' AND ', $where);

            $documents_meetings_presort = $registry['db']->GetCol($get_doc_meet_ids_query);
            $documents_meetings_presort = array_unique($documents_meetings_presort);

            if (!empty($documents_meetings_presort)) {
                $sql_meetings = array();
                $sql_meetings['select'] = 'SELECT d.id as rec_id, "document" as rec_type, ' . "\n" . 
                                          '  d.type as type, dti18n.name as type_name, d_cstm_meeting_results.value as last_action, ds.name as substatus, ' . "\n" .
                                          '  d.customer as customer, d_cstm_meeting_start.value as last_action_date, d.modified as date_modified, ' . "\n" .
                                          '  GROUP_CONCAT(DISTINCT(CONCAT(empi18n.name, " ", empi18n.lastname)) SEPARATOR ",") as last_action_done_by, 1 as num' . "\n";

                $sql_meetings['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' as dti18n' . "\n" .
                                          '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' as ds' . "\n" .
                                          '  ON (ds.id=d.substatus AND ds.lang="' . $model_lang . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_meeting_results' . "\n" .
                                          '  ON (d_cstm_meeting_results.model_id=d.id AND d_cstm_meeting_results.var_id="' . $meeting_protocol_result_id . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_meeting_start' . "\n" .
                                          '  ON (d_cstm_meeting_start.model_id=d.id AND d_cstm_meeting_start.var_id="' . $meeting_protocol_date_id . '")' . "\n" .

                                          'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as empi18n' . "\n" .
                                          '  ON (empi18n.parent_id=d.employee AND empi18n.lang="' . $model_lang . '")' . "\n";

                $sql_meetings['where'] = 'WHERE d.id IN (' . implode(',', $documents_meetings_presort) . ')';
                $sql_meetings['group'] = 'GROUP BY d.id';

                $queries[] = implode("\n", $sql_meetings);
            }

            /*
             * DOCUMENTS MEETING PROTOCOLS BONUSES
             */
            $sql_for_add_vars = 'SELECT fm.id, fm.name 
                                  FROM ' . DB_TABLE_FIELDS_META . ' AS fm 
                                  WHERE fm.model="Document" 
                                    AND fm.model_type=' . CUSTOMER_STATUS_DOCUMENT_MEETINGS_PROTOCOL_BONUSES . ' 
                                    AND (fm.name="' . MEETING_PROTOCOL_RESUME . '") 
                                  ORDER BY fm.position';
            $varIds = $registry['db']->GetAll($sql_for_add_vars);
            $meetingResumeId = $varIds[0]['id'];

            //prepare presort
            $documents_meetings_presort = array();

            $where = array();
            $where[] = 'd.type="' . CUSTOMER_STATUS_DOCUMENT_MEETINGS_PROTOCOL_BONUSES . '"';
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';
            if (!empty($filters['employee'])) {
                $where[] = 'd.employee IN (' . implode(',', $filters['employee']) . ')';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }

            $get_doc_meet_ids_query = 'SELECT d.id' . "\n" .
                'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                '  ON (c.id=d.customer AND c.deleted_by="0" AND c.active="1" AND c.type IN (' . implode(',', $available_types) . '))' . "\n" .
                'WHERE ' . implode(' AND ', $where);

            $documents_meetings_presort = $registry['db']->GetCol($get_doc_meet_ids_query);
            $documents_meetings_presort = array_unique($documents_meetings_presort);

            if (!empty($documents_meetings_presort)) {
                $sql_meetings = array();
                $sql_meetings['select'] = 'SELECT d.id as rec_id, "document" as rec_type, ' . "\n" .
                    '  d.type as type, dti18n.name as type_name, d_cstm_meeting_results.value as last_action, ds.name as substatus, ' . "\n" .
                    '  d.customer as customer, d.modified as last_action_date, d.modified as date_modified, ' . "\n" .
                    '  GROUP_CONCAT(DISTINCT(CONCAT(empi18n.name, " ", empi18n.lastname)) SEPARATOR ",") as last_action_done_by, 1 as num' . "\n";

                $sql_meetings['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' as dti18n' . "\n" .
                    '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' as ds' . "\n" .
                    '  ON (ds.id=d.substatus AND ds.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_meeting_results' . "\n" .
                    '  ON (d_cstm_meeting_results.model_id=d.id AND d_cstm_meeting_results.var_id="' . $meetingResumeId . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as empi18n' . "\n" .
                    '  ON (empi18n.parent_id=d.employee AND empi18n.lang="' . $model_lang . '")' . "\n";

                $sql_meetings['where'] = 'WHERE d.id IN (' . implode(',', $documents_meetings_presort) . ')';
                $sql_meetings['group'] = 'GROUP BY d.id';

                $queries[] = implode("\n", $sql_meetings);
            }

            /*
             * DOCUMENTS DAILY REPORTS BONUSES
             */
            $sql_for_add_vars = 'SELECT fm.id, fm.name 
                                  FROM ' . DB_TABLE_FIELDS_META . ' AS fm 
                                  WHERE fm.model="Document" 
                                    AND fm.model_type=' . CUSTOMER_STATUS_DOCUMENT_DAILY_REPORT_BONUSES . ' 
                                    AND (fm.name="' . DAILY_REPORTS_CLIENT_NAME . '" OR fm.name="' . MEETING_PROTOCOL_RESUME . '") 
                                  ORDER BY fm.position';

            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == DAILY_REPORTS_CLIENT_NAME) {
                    $clientNameId = $vars['id'];
                } else if ($vars['name'] == MEETING_PROTOCOL_RESUME) {
                    $meetingResumeId = $vars['id'];
                }
            }

            //prepare presort
            $documents_meetings_presort = array();

            $where = array();
            $where[] = 'd.type="' . CUSTOMER_STATUS_DOCUMENT_DAILY_REPORT_BONUSES . '"';
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';
            if (!empty($filters['employee'])) {
                $where[] = 'd.employee IN (' . implode(',', $filters['employee']) . ')';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }

           $avalTypes = implode(',', $available_types);
            $get_doc_meet_ids_query = "
                    SELECT d.id
                    FROM " . DB_TABLE_DOCUMENTS . " AS d
                    JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm
                        ON(dcstm.model_id = d.id
                            AND dcstm.var_id = 1301)
                    JOIN " . DB_TABLE_CUSTOMERS . " AS c
                          ON (c.id = dcstm.value
                          AND c.deleted_by = 0 
                          AND c.active= 1 
                          AND c.type IN ({$avalTypes})
                      )
                    WHERE " . implode(' AND ', $where);


            $documents_meetings_presort = $registry['db']->GetCol($get_doc_meet_ids_query);
            $documents_meetings_presort = array_unique($documents_meetings_presort);
            if (!empty($documents_meetings_presort)) {
                $sql_meetings = array();
                $sql_meetings['select'] = "SELECT d.id                              AS rec_id, 
                                             'document'                             AS rec_type, 
                                              d.`type`                              AS `type`, 
                                              dti18n.name                           AS type_name, 
                                              d_cstm_meeting_resume.value           AS last_action, 
                                              ds.name                               AS substatus, 
                                              d_cstm_client_name.value              AS customer,
                                              d.modified                            AS last_action_date, 
                                              d.modified                            AS date_modified, 
                                              GROUP_CONCAT(DISTINCT(CONCAT(empi18n.name, ' ', empi18n.lastname)) SEPARATOR ',') 
                                                                                    AS last_action_done_by, 
                                              1                                     AS num";

                $sql_meetings['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' as dti18n' . "\n" .
                    '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' as ds' . "\n" .
                    '  ON (ds.id=d.substatus AND ds.lang="' . $model_lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_meeting_resume' . "\n" .
                    '  ON (d_cstm_meeting_resume.model_id=d.id AND d_cstm_meeting_resume.var_id="' . $meetingResumeId . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_client_name' . "\n" .
                    '  ON (d_cstm_client_name.model_id=d.id AND d_cstm_client_name.var_id="' . $clientNameId . '")' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as empi18n' . "\n" .
                     '  ON (empi18n.parent_id=d.employee AND empi18n.lang="' . $model_lang . '")';
                $sql_meetings['where'] = 'WHERE d.id IN (' . implode(',', $documents_meetings_presort) . ')';
                $sql_meetings['group'] = 'GROUP BY d.id';

                $queries[] = implode("\n", $sql_meetings);
            }


            /*
             * EVENTS
             */
            $events_presort = array();

            $where = array();
            $where[] = 'e.type IN (' . CUSTOMER_STATUS_EVENTS_TYPES . ')';
            $where[] = 'e.active=1';
            $where[] = 'e.deleted_by=0';
            if (!empty($filters['employee'])) {
                $where[] = 'u.employee IN (' . implode(',', $filters['employee']) . ')';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(e.event_start, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(e.event_start, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }

            $get_evnt_ids_query = 'SELECT e.id' . "\n" .
                                  'FROM ' . DB_TABLE_EVENTS . ' as e' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' as ea' . "\n" .
                                  '  ON (ea.parent_id=e.id AND ea.participant_type="user" AND ea.ownership="mine")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_USERS . ' as u' . "\n" .
                                  '  ON (u.id=ea.participant_id AND u.employee!=0)' . "\n" .
                                  'JOIN ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                                    '  ON (c.id=e.customer AND c.deleted_by="0" AND c.active="1" AND c.type IN (' . implode(',', $available_types) . '))' . "\n" .
                                  'WHERE ' . implode(' AND ', $where) . "\n";

            $events_presort = $registry['db']->GetCol($get_evnt_ids_query);
            $events_presort = array_unique($events_presort);

            if (!empty($events_presort)) {
                $sql_events = array();
                $sql_events['select'] = 'SELECT e.id as rec_id, "event" as rec_type, ' . "\n" .
                                        '  e.type as type, eti18n.name as type_name, e.status as last_action, "" as substatus, ' . "\n" .
                                        '  e.customer as customer, e.status_modified as last_action_date, e.modified as date_modified, ' . "\n" .
                                        '  GROUP_CONCAT(DISTINCT(CONCAT(empi18n.name, " ", empi18n.lastname)) SEPARATOR ",") as last_action_done_by, 1 as num' . "\n";

                $sql_events['from']   = 'FROM ' . DB_TABLE_EVENTS . ' as e' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES_I18N . ' as eti18n' . "\n" .
                                        '  ON (eti18n.parent_id=e.type AND eti18n.lang="' . $model_lang . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' as ea' . "\n" .
                                        '  ON (ea.parent_id=e.id AND ea.participant_type="user" AND ea.ownership="mine")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_USERS . ' as u' . "\n" .
                                        '  ON (u.id=ea.participant_id AND u.employee!=0)' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as empi18n' . "\n" .
                                        '  ON (empi18n.parent_id=u.employee AND empi18n.lang="' . $model_lang . '")' . "\n";

                $sql_events['where'] = 'WHERE e.id IN (' . implode(',', $events_presort) . ')';
                $sql_events['group'] = 'GROUP BY e.id';

                $queries[] = implode("\n", $sql_events);
            }

            /*
             * PROJECTS
             */
            $users_information = array();
            $projects_presort = array();
            if (!empty($filters['employee'])) {
                //take the employee's user id and departments
                $sql_users = 'SELECT u.id, GROUP_CONCAT(ud.department_id SEPARATOR ",") as departments' . "\n" .
                             'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_DEPARTMENTS . ' AS ud' . "\n" .
                             ' ON (ud.parent_id=u.id)' . "\n" .
                             'WHERE u.employee IN (' . implode(',', $filters['employee']) . ')' . "\n" .
                             'GROUP BY u.id' . "\n";
                $users_information = $registry['db']->GetAll($sql_users);
            }

            // prepare the where clause for the presort
            $where = array();
            $where[] = 'p.deleted_by=0';
            $where[] = 'p.active=1';
            $employees_clauses = array();
            foreach ($users_information as $ui) {
                $employees_clauses[] = '(p.manager="' . $ui['id'] . '" OR (pa.assignments_type="Users" AND pa.assigned_to="' . $ui['id'] . '")' . ($ui['departments'] ? ' OR (pa.assignments_type="Departments" AND pa.assigned_to IN (' . $ui['departments'] . '))' : '') . ')';
            }
            if (!empty($employees_clauses)) {
                $where[] = '(' . implode(' OR ', $employees_clauses) . ')';
            }
            //we create 2 versions of where one for enqueries and one for inactive clients
            $whereEnquiry = $whereInactiveClients = $where;
            unset($where);

            $whereEnquiry[] = 'p.type = ' . CUSTOMER_STATUS_PROJECTS_TYPE_ENQUIRY ;
            $whereInactiveClients[] = 'p.type = ' . CUSTOMER_STATUS_PROJECTS_TYPE_INACTIVE_CLIENTS ;
            if (!empty($filters['from_date'])) {
                $whereEnquiry[] = 'DATE_FORMAT(p.date_start, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
                $whereInactiveClients[] = 'DATE_FORMAT(p.added, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $whereEnquiry[] = 'DATE_FORMAT(p.date_start, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
                $whereInactiveClients[] = 'DATE_FORMAT(p.added, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }

            $presort = function ($where) use ($registry, $available_types) {
                $get_ids_query = 'SELECT p.id' . "\n" .
                    'FROM ' . DB_TABLE_PROJECTS . ' as p' . "\n" .
                    'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    '  ON (c.id=p.customer AND c.deleted_by="0" AND c.active="1" AND c.type IN (' . implode(',',
                        $available_types) . '))' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_PROJECTS_ASSIGNMENTS . ' as pa' . "\n" .
                    '  ON (pa.parent_id=p.id)' . "\n" .
                    'WHERE ' . implode(' AND ', $where) . "\n" .
                    'GROUP BY p.id;';
                return  $registry['db']->GetCol($get_ids_query);
            };
            $projects_presort = array_unique(array_merge($presort($whereEnquiry),$presort($whereInactiveClients)));

            if (!empty($projects_presort)) {
                $sql_projects = array();
                $sql_projects['select'] = 'SELECT p.id as rec_id, "projects" as rec_type, p.type as type, ' . "\n" .
                                          '  pti18n.name as type_name, CONCAT(p.status, "|", IF(si18n.name IS NOT NULL, si18n.name, "")) as last_action, "" as substatus, ' . "\n" .
                                          '  p.customer as customer, p.status_modified as last_action_date, p.modified as date_modified, ' . "\n" .
                                          '  CONCAT(CONCAT(mi18n.name, " ", mi18n.lastname), ",", IF(pa.assignments_type="Users", GROUP_CONCAT(DISTINCT(CONCAT(empi18n.name, " ", empi18n.lastname)) SEPARATOR ","), GROUP_CONCAT(DISTINCT(di18n.name) SEPARATOR ",")))  as last_action_done_by, 1 as num' . "\n";

                $sql_projects['from']   = 'FROM ' . DB_TABLE_PROJECTS . ' as p' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' as pti18n' . "\n" .
                                          '  ON (pti18n.parent_id=p.type AND pti18n.lang="' . $model_lang . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_STAGES_I18N . ' AS si18n' . "\n" .
                                          '  ON (p.stage=si18n.parent_id AND si18n.lang="' . $lang . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_PROJECTS_ASSIGNMENTS . ' as pa' . "\n" .
                                          '  ON (pa.parent_id=p.id)' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                                          '  ON (u.id=pa.assigned_to AND pa.assignments_type="Users" AND u.employee!=0)' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS empi18n' . "\n" .
                                          '  ON (u.employee=empi18n.parent_id AND empi18n.lang="' . $lang . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS di18n' . "\n" .
                                          '  ON (pa.assigned_to=di18n.parent_id AND di18n.lang="' . $lang . '")' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_USERS . ' as um' . "\n" .
                                          '  ON (um.id=p.manager AND um.employee!=0)' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as mi18n' . "\n" .
                                          '  ON (mi18n.parent_id=um.employee AND mi18n.lang="' . $model_lang . '")' . "\n";

                $sql_projects['where'] = 'WHERE p.id IN (' . implode(',', $projects_presort) . ')';
                $sql_projects['group'] = 'GROUP BY p.id';

                $queries[] = implode("\n", $sql_projects);
            }


            /**
             * TASKS
             */

            $where = array();
            $where[] = 't.type IN (' . CUSTOMER_STATUS_TASK_TYPES . ')';
            $where[] = 't.active=1';
            $where[] = 't.deleted_by=0';
            if (!empty($filters['employee'])) {
                $where[] = 'u.employee IN (' . implode(',', $filters['employee']) . ')';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(t.planned_start_date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(t.planned_start_date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }
            $where = implode(' AND ',$where);

            $custTypes = implode(', ', $available_types);
            $query = "
                SELECT 
                      t.id  AS rec_id, 
                      'task' AS rec_type, 
                      t.`type` AS `type`, 
                      tti18n.name AS type_name,
                      CONCAT(t.`status`, ', ', IF(tti18n.name IS NOT NULL, tti18n.name, \"\")) AS last_action, '' AS substatus,
                      t.customer AS customer, 
                      t.status_modified AS last_action_date, 
                      t.status_modified AS date_modified,
                      GROUP_CONCAT(DISTINCT(CONCAT(empi18n.name, ' ', empi18n.lastname)) SEPARATOR ',') AS last_action_done_by,
                      1 AS num
                FROM " . DB_TABLE_TASKS . " AS t
                LEFT JOIN " . DB_TABLE_TASKS_TYPES_I18N  . " AS tti18n
                  ON (tti18n.parent_id=t.`type` 
                  AND tti18n.lang='{$lang}')
                LEFT JOIN " . DB_TABLE_TASKS_ASSIGNMENTS . " AS ta
                  ON (ta.parent_id=t.id)
                LEFT JOIN " . DB_TABLE_USERS . " AS u
                  ON (u.id=ta.assigned_to 
                      AND ta.assignments_type=0 
                        AND u.employee!=0)
                JOIN " . DB_TABLE_CUSTOMERS . " AS c
                    ON (t.customer = c.id 
                        AND c.active = 1 
                        AND c.deleted_by=0
                        AND c.`type` IN ({$custTypes})
                      )
                LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS empi18n
                  ON (u.employee=empi18n.parent_id 
                      AND empi18n.lang='{$lang}')  
                WHERE {$where}
                GROUP BY t.id";

            $queries[] = $query;
            if (!empty($queries)) {
                // MAKE UNION FOR THE WHOLE QUERY
                $union_query = '(' . implode(') UNION (', $queries) . ')' . ' ORDER BY last_action_date DESC, date_modified DESC, num DESC';
                $records_customer_status = $registry['db']->GetAll($union_query);

                $active_customers = array();
                foreach ($records_customer_status as $rcs) {
                    if (!isset($final_results[$rcs['customer']]) && $rcs['customer']!=0) {
                        $final_results[$rcs['customer']] = array(
                            'id'                  => $rcs['rec_id'],
                            'rec_type'            => $rcs['rec_type'],
                            'type_name'           => $rcs['type_name'],
                            'customer'            => $rcs['customer'],
                            'customer_name'       => '',
                            'customer_address'    => '',
                            'substatus'           => $rcs['substatus'],
                            'last_action'         => '',
                            'last_action_date'    => $rcs['last_action_date'],
                            'last_action_done_by' => $rcs['last_action_done_by'],
                        );
                        $active_customers[] = $rcs['customer'];
                        $lastActionDoneBy = explode(',', $rcs['last_action_done_by']);
                        $lastActionDoneBy = array_unique($lastActionDoneBy);
                        $final_results[$rcs['customer']]['last_action_done_by'] = $lastActionDoneBy;
                        if ($rcs['rec_type'] == 'projects') {
                            $model_status_full = explode('|', $rcs['last_action']);
                            $model_status_label = '';
                            $status_label = array();
                            if (isset($model_status_full[0])) {
                                $model_status = $model_status_full[0];
                                $status_label_name = 'reports_projects_' . $model_status;
                                $status_label[] = $registry['translater']->translate($status_label_name);
                            }
                            if (isset($model_status_full[1])) {
                                $status_label[] = $model_status_full[1];
                            }
                            $status_label = array_filter($status_label);
                            $final_results[$rcs['customer']]['last_action'] = $rcs['type_name'] . ' / ' . implode(' / ', $status_label);
                        } else if ($rcs['rec_type'] == 'document') {
                            if ($rcs['type'] == CUSTOMER_STATUS_DOCUMENT_CALLS_LIST  || $rcs['type'] == CUSTOMER_STATUS_DOCUMENT_MEETINGS_PROTOCOL || CUSTOMER_STATUS_DOCUMENT_MEETINGS_PROTOCOL_BONUSES) {
                                $newLines = str_replace('\n','<br>',$rcs['last_action']);
                                $final_results[$rcs['customer']]['last_action'] = $rcs['type_name'] . ' / ' . nl2br($rcs['last_action']);
                            } else {
                                $status_label_name = 'reports_documents_' . $rcs['last_action'];
                                $final_results[$rcs['customer']]['last_action'] = $rcs['type_name'] . ' / ' . $registry['translater']->translate($status_label_name);
                            }
                        } else if ($rcs['rec_type'] == 'event') {
                            $status_label_name = 'reports_events_' . $rcs['last_action'];
                            $final_results[$rcs['customer']]['last_action'] = $rcs['type_name'] . ' / ' . $registry['translater']->translate($status_label_name);
                        }
                        elseif($rcs['rec_type'] == 'task') {
                            $statuses = explode(', ', $rcs['last_action']);
                            $status_label_name = 'reports_tasks_status_' . $statuses[0];
                            $final_results[$rcs['customer']]['last_action'] = (isset($statuses[1])? "{$statuses[1]}, " : '') . $registry['translater']->translate($status_label_name);
                        }
                    }
                }

                if (!empty($active_customers)) {
                    // GET THE CUSTOMERS INFORMATION
                    $customers_info_extended_sql = 'SELECT c.id as customer_id, CONCAT(ci18n.name, " ", ci18n.lastname) as name, ci18n.address as address' . "\n" .
                                                   'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n' . "\n" .
                                                   '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                                   'WHERE c.id IN (' . implode(',', $active_customers) . ')' . "\n";
                    $customerIds = implode(',', $active_customers);
                    $customers_info_extended_sql =
                        "SELECT c.id as customer_id, 
                                CONCAT(ci18n.name, ' ', ci18n.lastname) 
                                as name, ci18n.address AS address, 
                                cl.country_name AS country,
                                ci18n.city AS city
                            FROM " . DB_TABLE_CUSTOMERS . " AS c
                            LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci18n
                              ON (ci18n.parent_id=c.id 
                                    AND ci18n.lang='{$model_lang}')
                            LEFT JOIN " . DB_TABLE_COUNTRY_LIST . " AS cl
                                ON (cl.country_code = c.country 
                                    AND cl.lang = '{$model_lang}')
                            WHERE c.id IN ($customerIds)";

                    $customers_info_extended = $registry['db']->GetAssoc($customers_info_extended_sql);

                    foreach ($customers_info_extended as $key_custm => $customer_info_ext) {
                        if (isset($final_results[$key_custm])) {
                            $final_results[$key_custm]['customer_name'] = trim($customer_info_ext['name']);
                            $address = '';
                            if(!empty($customer_info_ext['country'])) {
                                $address .= $customer_info_ext['country'];
                            }
                            if(!empty($customer_info_ext['city'])) {
                                $address .= ', ' . $customer_info_ext['city'];
                            }
                            if(!empty($customer_info_ext['address'])) {
                                $address .= ', ' . $customer_info_ext['address'];
                            }
                            $final_results[$key_custm]['customer_address'] = $address;

                        }
                    }
                }
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>
