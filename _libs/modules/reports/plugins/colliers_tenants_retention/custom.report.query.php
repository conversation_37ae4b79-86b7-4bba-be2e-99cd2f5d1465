<?php
Class Colliers_Tenants_Retention Extends Reports {
    public static function buildQuery(&$registry, $filters = array()) {
        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        if (empty($filters['from_date']) || empty($filters['to_date'])) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_fill_required_fields'));
            return array(0, 0);
        }

        if (!defined('CONTRACT_TYPE_LEASE')) {
            define('CONTRACT_TYPE_LEASE', 1);
        }
        if (!defined('NOM_TYPE_UNIT')) {
            define('NOM_TYPE_UNIT', 2);
        }
        if (!defined('PART_OF_GLOBAL_LEASE_AREA')) {
            define('PART_OF_GLOBAL_LEASE_AREA', 'part_gla');
        }
        if (!defined('QUADRATURE_REAL')) {
            define('QUADRATURE_REAL', 'quadrature_real');
        }
        if (!defined('EMPTY_GLA')) {
            define('EMPTY_GLA', 'empty_gla');
        }
        if (!defined('OBJECT_ID')) {
            define('OBJECT_ID', 'object_id');
        }
        if (!defined('SUBSTATUS_SIGNED')) {
            define('SUBSTATUS_SIGNED', 1);
        }

        //sql to take the ids of the needed additional vars for nomenclatures
        $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                           '  WHERE fm.model="Nomenclature" AND fm.model_type=' . NOM_TYPE_UNIT . ' AND (fm.name="' . PART_OF_GLOBAL_LEASE_AREA . '" OR fm.name="' . QUADRATURE_REAL . '")';
        $add_vars = $registry['db']->GetAll($sql_for_add_vars);

        $part_gla_id = '';
        $quadrature_real_id = '';

        foreach ($add_vars as $add_var) {
            if ($add_var['name'] == PART_OF_GLOBAL_LEASE_AREA) {
                $part_gla_id = $add_var['id'];
            } elseif ($add_var['name'] == QUADRATURE_REAL) {
                $quadrature_real_id = $add_var['id'];
            }
        }

        //sql to take the id of the needed additional var for contracts
        $sql_for_add_var = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                           '  WHERE fm.model="Contract" AND fm.model_type=' . CONTRACT_TYPE_LEASE . ' AND fm.name="' . OBJECT_ID . '"';
        $object_id_id = $registry['db']->GetOne($sql_for_add_var);

        //sql to get contracts and units for start and end date
        $sql['select'] = 'SELECT co.id AS contract, co.customer, cocstm.value AS nom_id, ncstm2.value AS quadrature_real';

        $sql['from'] = 'FROM ' . DB_TABLE_CONTRACTS . ' AS co ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS cocstm ' . "\n" .
                       '  ON co.id=cocstm.model_id AND co.type=' . CONTRACT_TYPE_LEASE . ' AND cocstm.var_id=' . $object_id_id . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm1 ' . "\n" .
                       '  ON cocstm.value=ncstm1.model_id AND ncstm1.var_id=' . $part_gla_id . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm2 ' . "\n" .
                       '  ON cocstm.value=ncstm2.model_id AND ncstm2.var_id=' . $quadrature_real_id;

        $sql['where'] = 'WHERE co.status="closed" AND co.substatus=' . SUBSTATUS_SIGNED . "\n" .
                        '  AND DATE(co.date_start) <= "[date]" AND DATE(co.date_validity) > "[date]"' . "\n" .
                        '  AND ncstm1.value="' . EMPTY_GLA . '"' . "\n"  .
                        '  AND co.deleted_by=0 AND co.active=1' . "\n";

        //if selected "to" date is in the past, search only for SIGNED contracts of subtype "contract"
        $sql_where_past = '  AND co.subtype="contract"';

        //if selected "to" date is in the future, search for SIGNED contracts: 1) of subtype "contract";
        //2) of subtype "annex" which are in "waiting" subtype status and have annex start date prior to selected "to" date;
        $sql_where_future = '  AND (co.subtype = "contract" ' . "\n" .
                            '  OR (co.subtype = "annex" AND co.subtype_status="waiting" AND DATE(co.date_start_subtype) < "[date]" AND (co.date_end_subtype = "0000-00-00" OR co.date_end_subtype > "[date]")))';


        $query = implode("\n", $sql);

        $query_start = str_replace('[date]', $filters['from_date'],
            $query . ($filters['from_date'] <= date('Y-m-d') ? $sql_where_past : $sql_where_future));
        $units_start = $registry['db']->GetAll($query_start);

        $query_end = str_replace('[date]', $filters['to_date'],
            $query . ($filters['to_date'] <= date('Y-m-d') ? $sql_where_past : $sql_where_future));
        $units_end = $registry['db']->GetAll($query_end);

        $retained = array();
        $lost = array();
        $q_retained = 0;
        $q_lost = 0;

        if ($units_start) {
            foreach ($units_start as $us) {
                if ($units_end) {
                    foreach ($units_end as $ue) {
                        if ($us['nom_id'] == $ue['nom_id']) {
                            if ($us['customer'] == $ue['customer'] && array_key_exists($ue['nom_id'], $retained) === false) {
                                $retained[$ue['nom_id']] = $ue['contract'];
                                $q_retained += $ue['quadrature_real'];
                                break;
                            } elseif ($us['customer'] != $ue['customer'] && array_key_exists($ue['nom_id'], $lost) === false) {
                                $lost[$ue['nom_id']] = $ue['contract'];
                                $q_lost += $ue['quadrature_real'];
                                break;
                            }
                        }
                    }

                    //if start unit is not found among end units, it is not currently rented and therefore is lost
                    if (!(array_key_exists($us['nom_id'], $retained) !== false || array_key_exists($us['nom_id'], $lost) !== false)) {
                        $lost[$us['nom_id']] = $us['contract'];
                        $q_lost += $us['quadrature_real'];
                    }

                //if there are no end units, all start units are lost
                } else {
                    if (array_key_exists($us['nom_id'], $lost) === false) {
                        $lost[$us['nom_id']] = $us['contract'];
                        $q_lost += $us['quadrature_real'];
                    }
                }
            }

            //numbers of unique tenants
            $num_tenants_retained = count(array_unique($retained));
            $num_tenants_lost = count(array_unique($lost));

        //if there are no start units (no signed contracts)
        } else {
            $registry['messages']->setMessage($registry['translater']->translate('message_reports_no_signed_lease_contracts'));
            return array(0, 0);
        }


        /*
         * PREPARE CHART
         */
        $final_results['additional_options']['charts_param'] = $filters['report_type'];
        $export_file_type = 'png';
        if ($registry->get('action') == 'export') {
            $final_results['additional_options']['placeholders'] = array();
        }
        $chart = new Chart($registry, 'pie', $filters['report_type'], 1, true);

        $chart_params = array();
        $chart_params['chart'] = array(
            'backgroundColor' => '#ffffff',
            'borderWidth' => 0,
            'width' => TENANTS_RETAINMENT_TENANTS_CHART_WIDTH,
            'height' => TENANTS_RETAINMENT_TENANTS_CHART_HEIGHT,
            'style' => array(
                'marginTop' => '15px',
                'marginBottom' => '15px'
            )
        );
        $chart_params['plotOptions'] = array(
            'pie' => array(
                'startAngle' => 270
            )
        );
        $chart_params['title'] = array(
            'text' => sprintf('%s - %d',
                              $registry['translater']->translate('reports_tenants'),
                              ($num_tenants_retained + $num_tenants_lost))
        );

        $chart_params['series'] = array(array());
        $chart_params['series'][0]['data'] = array(
            array('name' => $registry['translater']->translate('reports_retained'),
                  'y' => $num_tenants_retained,
                  'color' => '#2A70CA'),
            array('name' => $registry['translater']->translate('reports_lost'),
                  'y' => $num_tenants_lost,
                  'color' => '#FF5555')
        );

        if (isset($final_results['additional_options']['placeholders'])) {
            $tenants_retainment_tenants_chart = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_DIR, $chart->get('image_export_name'), $export_file_type);
            if (file_exists($tenants_retainment_tenants_chart)) {
                $exported_file_url = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_URL, $chart->get('image_export_name'), $export_file_type);
                $exported_file_dir = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_DIR, $chart->get('image_export_name'), $export_file_type);
                $chart->set('exported_file_url', $exported_file_url, true);
                $chart->set('exported_file_dir', $exported_file_dir, true);
                $final_results['additional_options']['placeholders']['tenants_retainment_tenants_chart'] = array(
                    'name'       => 'tenants_retainment_tenants_chart',
                    'type'       => 'chart',
                    'properties' => array(
                        'url' => $exported_file_url,
                        'dir' => $exported_file_dir
                    )
                );
            }
        }
        if ($chart->prepareChart($chart_params)) {
            $final_results['additional_options']['charts'][] = $chart;
        }


        /*
         * PREPARE CHART
         */
        $chart = new Chart($registry, 'pie', $filters['report_type'], 2, true);

        $chart_params = array();
        $chart_params['chart'] = array(
            'backgroundColor' => '#ffffff',
            'borderWidth' => 0,
            'width' => TENANTS_RETAINMENT_GLA_CHART_WIDTH,
            'height' => TENANTS_RETAINMENT_GLA_CHART_HEIGHT,
            'style' => array(
                'marginTop' => '15px',
                'marginBottom' => '15px'
            )
        );
        $chart_params['plotOptions'] = array(
            'pie' => array(
                'startAngle' => 270,
                'dataLabels' => array(
                    'formatter' => 'function() { return this.point.name + \', \' + Highcharts.numberFormat(this.point.y, 2) + \' ' . $registry['translater']->translate('reports_sqm') . '\'; }'
                )
            )
        );
        $chart_params['title'] = array(
            'text' => sprintf('%s - %s %s',
                              $registry['translater']->translate('reports_gla'),
                              number_format(($q_retained + $q_lost), 2, '.', ' '),
                              $registry['translater']->translate('reports_sqm'))
        );

        $chart_params['series'] = array(array());
        $chart_params['series'][0]['data'] = array(
            array('name' => $registry['translater']->translate('reports_retained'),
                  'y' => $q_retained,
                  'color' => '#2A70CA'),
            array('name' => $registry['translater']->translate('reports_lost'),
                  'y' => $q_lost,
                  'color' => '#FF5555')
        );

        if (isset($final_results['additional_options']['placeholders'])) {
            $tenants_retainment_gla_chart = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_DIR, $chart->get('image_export_name'), $export_file_type);
            if (file_exists($tenants_retainment_gla_chart)) {
                $exported_file_url = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_URL, $chart->get('image_export_name'), $export_file_type);
                $exported_file_dir = sprintf('%s%s.%s', PH_CHARTS_UPLOAD_DIR, $chart->get('image_export_name'), $export_file_type);
                $chart->set('exported_file_url', $exported_file_url, true);
                $chart->set('exported_file_dir', $exported_file_dir, true);
                $final_results['additional_options']['placeholders']['tenants_retainment_gla_chart'] = array(
                    'name'       => 'tenants_retainment_gla_chart',
                    'type'       => 'chart',
                    'properties' => array(
                        'url' => $exported_file_url,
                        'dir' => $exported_file_dir
                    )
                );
            }
        }
        if ($chart->prepareChart($chart_params)) {
            $final_results['additional_options']['charts'][] = $chart;
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }
}

?>
