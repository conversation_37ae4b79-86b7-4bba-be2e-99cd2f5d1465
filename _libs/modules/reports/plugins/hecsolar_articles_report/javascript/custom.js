Event.observe(window, 'load', function () {
    let grid1;
    let intl = new ej.base.Internationalization();
    let nFormatter = intl.getNumberFormat({ skeleton: 'N2', minimumFractionDigits:2, maximumFractionDigits: 2, useGrouping: false});

    function numberFormatter(field, data, column) {
        return (typeof data[field] == "number" ? nFormatter(data[field]) : '');
    }

    // Function to generate currency format based on currency code
    function getCurrencyFormat(currencyCode, value) {
        return value.toFixed(6) + ' ' + currencyCode;
    }

    function initFirstTable() {
        // Apply styles to grid div
        const gridDiv = document.querySelector('#hecsolar_articles_report_show_report_results .grid');
        if (!gridDiv) {
            return;
        }
        gridDiv.style.display = 'none';
        gridDiv.style.marginTop = '10px';

        grid1 = new window.ej.grids.Grid({
            dataSource: [],
            gridLines: 'Both',
            allowSorting: true,
            allowTextWrap: true,
            height: 700,
            columns: [
                {
                    field: 'category',
                    headerText: i18n['reports_category'],
                    minWidth: 200,
                    width: 200,
                    maxWidth: 250
                },
                {
                    field: 'article',
                    headerText: i18n['reports_article'],
                    minWidth: 200,
                    width: 200,
                    maxWidth: 250
                },
                {
                    field: 'customer',
                    headerText: i18n['reports_contractor'],
                    minWidth: 180,
                    width: 180,
                    maxWidth: 250
                },
                {
                    field: 'project',
                    headerText: i18n['reports_project'],
                    template: '#templateProjectNum',
                    width: 100
                },
                {
                    field: 'region',
                    headerText: i18n['reports_region'],
                    width: 60
                },
                {
                    field: 'doc_label',
                    headerText: i18n['reports_document'],
                    template: '#templateDocumentNum',
                    width: 100
                },
                {
                    field: 'date',
                    headerText: i18n['reports_date'],
                    format: { type: "date", format: "dd.MM.yyyy" },
                    minWidth: 120,
                    width: 120,
                    maxWidth: 150
                },
                {
                    field: 'status',
                    headerText: i18n['reports_status'],
                    width: 60
                },
                {
                    field: 'delivery_terms',
                    headerText: i18n['reports_delivery_terms'],
                    minWidth: 180,
                    width: 180,
                    maxWidth: 250
                },
                {
                    field: 'quantity',
                    headerText: i18n['reports_quantity'],
                    format: '0.000000',
                    textAlign: 'Right',
                    minWidth: 80,
                    width: 80,
                    maxWidth: 400
                },
                {
                    field: 'measure',
                    headerText: i18n['reports_measure'],
                    minWidth: 120,
                    width: 120,
                    maxWidth: 120
                },
                {
                    field: 'price_org',
                    headerText: i18n['reports_price_org'],
                    format: '0.000000',
                    textAlign: 'Right',
                    minWidth: 80,
                    width: 80,
                    maxWidth: 400
                },
                {
                    field: 'price',
                    headerText: i18n['reports_price'],
                    format: '0.000000',
                    textAlign: 'Right',
                    minWidth: 80,
                    width: 80,
                    maxWidth: 400
                },
                {
                    field: 'total_org',
                    headerText: i18n['reports_total_org_currency'],
                    format: '0.000000',
                    textAlign: 'Right',
                    minWidth: 80,
                    width: 80,
                    maxWidth: 400
                },
                {
                    field: 'total_w_vat',
                    headerText: i18n['reports_total_w_vat'],
                    format: '0.000000',
                    textAlign: 'Right',
                    minWidth: 80,
                    width: 80,
                    maxWidth: 400
                },
                {
                    field: 'total_eur',
                    headerText: i18n['reports_total_eur'],
                    format: '0.000000',
                    textAlign: 'Right',
                    minWidth: 80,
                    width: 80,
                    maxWidth: 400
                }
            ],
            queryCellInfo: function(args) {
                if ((args.column.field === 'total_org' || args.column.field === 'price_org') &&
                    args.data.org_currency) {
                    args.cell.innerText = getCurrencyFormat(args.data.org_currency, args.data.total_org);
                }
            },
            excelQueryCellInfo: function(args){
                if ((args.column.field === 'total_org' || args.column.field === 'price_org') &&
                    args.data.org_currency) {
                    args.value = getCurrencyFormat(args.data.org_currency, args.data.total_org);
                }
            },
            dataBound: onDataBoundTbl1,
            /*
             * Excel
             */
            allowExcelExport: true,
            allowResizing: true,
            toolbar: ['ExcelExport'],
        });

        grid1.appendTo(gridDiv);
        grid1.toolbarClick = function (args) {
            if (args.item.id.match(/_excelexport$/)) {
                grid1.excelExport({fileName: 'Articles_Report.xlsx'});
            }
        };
    }

    function onDataBoundTbl1() {
        this.autoFitColumns();
    }

    async function getJsonData(pushHistory) {
        $$('[name="reports"]')[0].value = 'ajax_generate';
        const url = env.base_url + '?' + Form.serialize($('reports_generated'));
        fetch(url, {
                        credentials: 'same-origin'
                   }
            )
            .then(response => response.json())
            .then(data => {
                updateTableData(grid1, data.table);
                const gridDiv = document.querySelector('#hecsolar_articles_report_show_report_results .grid');
                gridDiv.style.display = '';

                // Change the URL
                formData = new FormData($('reports_generated'));
                formDataParams = {};
                for (var pair of formData.entries()) {
                    formDataParams[pair[0]] = pair[1];
                }
                // super mega hyper giga custom solution for oldvalues fields of the Autocompleter
                $$('#reports_generated [name$="_oldvalue"]').each(element => {
                    formDataParams[element.name] = element.value;
                });

                if (pushHistory) {
                    history.pushState(formDataParams, 'title', data.url);
                }
            });
    }

    function updateTableData(grid, data) {
        // Cleanup
        grid.dataSource.splice(0, grid.dataSource.length);

        // Process results
        data.each(function (value) {
            for (p in grid.columns) {
                if (!grid.columns.hasOwnProperty(p)) {
                    continue;
                }
                if (typeof grid.columns[p].format == 'object' &&
                    grid.columns[p].format.type &&
                    (grid.columns[p].format.type == 'date' || grid.columns[p].format.type == 'datetime')) {
                    if (value[grid.columns[p].field]) {
                        value[grid.columns[p].field] = new Date(value[grid.columns[p].field]);
                    } else {
                        value[grid.columns[p].field] = null;
                    }
                }
            }
            grid.dataSource.push(value);
        });

        // Refresh grid
        grid.refresh();
    }

    /*
     * Event listener to manage the browser BACK button
     */
    window.addEventListener('popstate', (event) => {
        for(i in event.state) {
            if (!event.state.hasOwnProperty(i)) {
                continue;
            }
            current_filter = $$(`[name="${i}"]`);
            if (current_filter.length) {
                // special case for radio buttons
                if (current_filter[0].type == 'radio') {
                    for (b=0; b<current_filter.length; b++) {
                        if (current_filter[b].value == event.state[i]) {
                            current_filter[b].checked=true;
                            break;
                        }
                    }
                } else {
                    current_filter[0].value = event.state[i];
                }
            }
        }

        // load the data
        showLoading();
        getJsonData(false);
        hideLoading();
    });

    if ($('hecsolar_articles_report_show_report_results')) {
        $('reports_generated').onsubmit = null;
        $('reports_generated').observe('submit', function (e) {
            showLoading();
            e.preventDefault();
            getJsonData(true);
            hideLoading();
        });

        initFirstTable();

        // check if the for has to be auto loaded
        showLoading();
        getJsonData(false);
        hideLoading();
    }
});

