/**
 * Process selection of checkboxes for orders
 *
 * @param {Object} element - checkbox whose selection state is toggled
 * @return {Boolean} - result of the operation
 */
function toggleCorrectionSelected(element) {
    if (element.id.match(/^items_\d+\-\d+$/)) {
        var row_class_name = 'selected',
            disabled_class_name = 'input_inactive',
            row = element.up('tr');

        if (element.checked) {
            // process button visibility
            element.form.select('button.button').each(function(el) {
                el.removeClassName('hidden');
            });
            $$('span.selected_items_span').each(function(el) {
                el.addClassName('green');
            });
            // process row
            row.addClassName(row_class_name);
            row.select('input[type="hidden"], input[type="text"], select').each(function(el) {
                if (el != element && !el.hasClassName('system_disabled')) {
                    el.removeAttribute('disabled');
                    if (el.type == 'text' || el.tagName == 'SELECT') {
                        // update class name of visible fields
                        el.removeClassName(disabled_class_name);
                    }
                }
            });
        } else {
            var num_checked = 0;
            $$('input[type=checkbox][name="items[]"]').each(function(el) {
                if (el.checked) {
                    num_checked++;
                    throw $break;
                }
            });

            // process button visibility
            if (!num_checked) {
                element.form.select('button.button').each(function(el) {
                    el.addClassName('hidden');
                });
                $$('span.selected_items_span').each(function(el) {
                    el.removeClassName('green');
                });
            }
            // process row
            row.removeClassName(row_class_name);
            row.select('input[type="hidden"], input[type="text"], select').each(function(el) {
                if (el != element && !el.hasClassName('system_disabled')) {
                    el.setAttribute('disabled', 'disabled');
                    if (el.type == 'text' || el.tagName == 'SELECT') {
                        // update class name of visible fields
                        el.addClassName(disabled_class_name);
                    }
                }
            });
        }
    } else if (element.tagName == 'DIV' && /^check_all_menu_/.test(element.id)) {
        // check all/page/none
        $$('input[type=checkbox][name="items[]"]').each(function(el) {
            toggleCorrectionSelected(el);
        });
    }
    return true;
}
