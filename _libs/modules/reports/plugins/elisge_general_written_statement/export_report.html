<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td nowrap="nowrap">{#num#|escape}</td>
          <td nowrap="nowrap">{#reports_customer_name#|escape}</td>
          <td nowrap="nowrap">{#reports_document_num#|escape}</td>
          <td nowrap="nowrap">{#reports_article_code#|escape}</td>
          <td nowrap="nowrap">{#reports_article_description#|escape}</td>
          <td nowrap="nowrap">{#reports_depot_num#|escape}</td>
          <td nowrap="nowrap">{#reports_articles_measure#|escape}</td>
          <td nowrap="nowrap">{#reports_amount_article#|escape}</td>
          <td nowrap="nowrap">{#reports_pcs_boxes#|escape}</td>
          <td nowrap="nowrap">{#reports_pcs_box#|escape}</td>
          <td nowrap="nowrap">{#reports_palet_num#|escape}</td>
          <td nowrap="nowrap">{#reports_total_kg#|escape}</td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr>
            <td align="right" nowrap="nowrap" width="25" rowspan="{$result.doc_rows}">
              {counter name='item_counter' print=true}
            </td>
            <td rowspan="{$result.doc_rows}">
              {$result.customer|escape|default:"&nbsp;"}
            </td>
            {foreach from=$result.documents item=document name=doc}
              {if !$smarty.foreach.doc.first}
                <tr>
              {/if}
              <td nowrap="nowrap" rowspan="{$document.rows}">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$document.id}">{$document.full_num|numerate:$document.direction|default:"&nbsp;"}</a>
              </td>
                {foreach from=$document.info item=nfo name=protocol_info}
                  {if !$smarty.foreach.protocol_info.first}
                    <tr>
                  {/if}
                  <td style="mso-number-format:\@">
                    {$nfo.articles_code|default:"-"}
                  </td>
                  <td style="mso-number-format:\@">
                    {$nfo.articles_name|escape|default:"&nbsp;"}
                  </td>
                  {if $smarty.foreach.protocol_info.first}
                    <td style="mso-number-format:\@" rowspan="{$document.rows}">
                      {$document.depot_num|default:"&nbsp;"}
                    </td>
                  {/if}
                  <td style="mso-number-format:\@">
                    {$nfo.depot_num|escape|default:"&nbsp;"}
                  </td>
                  <td style="mso-number-format:\@">
                    {$nfo.articles_measure|escape|default:"&nbsp;"}
                  </td>
                  <td style="mso-number-format:\@" align="right">
                    {$nfo.amount_article|escape|default:"0"}
                  </td>
                  <td style="mso-number-format:\@" align="right">
                    {$nfo.pcs_boxes|escape|default:"0"}
                  </td>
                  <td style="mso-number-format:\@" align="right">
                    {$nfo.pcs_box|escape|default:"0"}
                  </td>
                  <td style="mso-number-format:\@" align="right">
                    {$nfo.palet_num|escape|default:"&nbsp;"}
                  </td>
                  {if $smarty.foreach.protocol_info.first}
                    <td align="right" rowspan="{$document.rows}">
                      {$document.total_kg|default:"&nbsp;"}
                    </td>
                  {/if}
                  {if !$smarty.foreach.protocol_info.last}
                    </tr>
                  {/if}
                {/foreach}
              {if !$smarty.foreach.doc.last}
                </tr>
              {/if}
            {/foreach}
        {foreachelse}
          <tr>
            <td colspan="12">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        {if $reports_additional_options.total_kilograms}
          <tr>
            <td align="right" colspan="11"><strong>{#reports_total_kg#|escape}:</strong></td>
            <td align="right"><strong>{$reports_additional_options.total_kilograms|default:"0"}</strong></td>
          </tr>
        {/if}
      </table>
  </body>
</html>