function decodeHtml(html) {
    let txt = document.createElement("textarea");
    txt.innerHTML = html;
    return txt.value;
}

var reportProductionSchedule = {

    markAcRequestAsChanged: function (autocomplete) {
        this.markRequestAsChanged(autocomplete.ac.element);
    },

    markRequestAsChanged: function (element) {
      var order_id = element.name.replace(/^.+\[(\d+)\]$/, '$1');
      if (order_id) {
        $('request_' + order_id).checked = true;
        this.multiChangeDates(element);
      } else {
          alert('Error: Failed to mark record as changed!');
      }
    },

    saveProduced: function (element) {
        // Validate produced_on
        let valid_produced_on = true;
        document.querySelectorAll('input[type="checkbox"][name^="produced["]:checked').forEach(function (checkbox) {
            if (checkbox.closest('tr').querySelector(`#produced_on_${checkbox.value}`).value === '') {
                valid_produced_on = false;
            }
        });

        if (!valid_produced_on) {
            alert(decodeHtml(i18n['reports_alert_produced_on_required']));
            return false;
        }

        element.form.action = env.base_url + '?' + env.module_param + '=reports&reports=save_produced&report_type=' + $('report_type').value;
        return true;
    },

    saveSchedule: function (element) {
        element.form.action = env.base_url + '?' + env.module_param + '=reports&reports=save_schedule&report_type=' + $('report_type').value;
        return true;
    },

    executeActionAutomations: function (element, saveChangesValidationMessage) {
        // If there is a validation message (i.e. if we need to validate)
        // and there are checked requests
        // but there are changes too (i.e. there are checked "request" fields, which are being checked when we change some fields)
        if (saveChangesValidationMessage && document.querySelector('[name^="selected_request["]:checked') && document.querySelector('[name^="request["]:checked')) {
            alert(saveChangesValidationMessage);
            return false;
        }

        element.form.action = env.base_url + '?' + env.module_param + '=reports&reports=execute_action_automations&report_type=' + $('report_type').value;
        return true;
    },

    worked: function () {
        $('report_show').value = 'worked';
        return true;
    },

    standard: function () {
        $('report_show').value = 'standard';
        return true;
    },

    standardByArticles: function () {
        $('report_show').value = 'standard';
        $('report_show_additional').value = 'by_articles';
        return true;
    },

    multiChangeDates: function (e) {
        if (e.className.match(/datebox/)) {
            let visible_checkbox_name = 'selected_request';
            if ($('current_report_show').value != 'standard') {
                visible_checkbox_name = 'produced';
            }
            const current_order_id = e.name.replace(/^.+\[(\d+)\]$/, '$1');
            const current_checkbox = $(`${visible_checkbox_name}[${current_order_id}]`);
            if (current_checkbox.checked) {
                var other_checkboxes = $$(`input[type="checkbox"][id^="${visible_checkbox_name}["]:checked:not([id="${current_checkbox.id}"])`);
                if (other_checkboxes.length) {
                    const date_field_name = e.name.replace(/^(.+)\[\d+\]$/, '$1');
                    const date_field_index = e.name.replace(/^.+\[(\d+)\]$/, '$1');
                    const date = e.value;
                    const date_formatted = $(`${date_field_name}_formatted_${date_field_index}`).value;
                    for (var i = 0; i < other_checkboxes.length; i++) {
                        const order_id = other_checkboxes[i].id.replace(/^.+\[(\d+)\]$/, '$1');
                        $(`${date_field_name}_${order_id}`).value = date;
                        $(`${date_field_name}_formatted_${order_id}`).value = date_formatted;
                        $('request_' + order_id).checked = true;
                    }
                }
            }
        }
    }
};

function getProducedCheckboxRowInputs(checkbox) {
    const index = checkbox.value;
    return checkbox.closest('tr').querySelectorAll(`#workshift_${index}, #workshop_${index}, #produced_on_formatted_${index}`);
}

function toggleProducedCheckbox(checkbox, readonly) {
    getProducedCheckboxRowInputs(checkbox).forEach(function (e) {
        toggleReadonly(e, readonly);
    });
}

function toggleProdusedOrders() {
    $('produced_orders_count').innerHTML = document.querySelectorAll('input[type="checkbox"][id^="produced_"]:checked').length;
}

Event.observe(window, 'load', function () {
    if (document.querySelector('#production_readonly_fields')) {
        $$('input[type="checkbox"][name^="produced["]').each(function (checkbox) {
            checkbox.observe('click', function () {
                toggleProducedCheckbox(this, !this.checked);
            });
            toggleProducedCheckbox(checkbox, 1);
        });
    }
});
