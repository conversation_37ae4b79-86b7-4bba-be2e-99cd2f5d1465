<?php
class Custom_Report_Filters extends Report_Filters {

    /**
     * Defining filters for the certain type of report
     */
    function defineFilters(&$registry) {

        // Prepare array containing description of all filters
        $filters = array();

        // Define required settings
        $required_settings = array(
            'filter_customer_types',
            'projects_types',
            'document_type_reference_project',
            'nomenclature_type_detail',
            'document_type_order',
            'documents_types_request',
            'document_type_ppp',
            'customer_type_supplier'
        );

        // Check required settings
        if ($this->checkRequiredSettings($registry, $required_settings, true)) {
            // Get the report settings
            $settings = Reports::getReportSettings($registry);
            $settings['projects_types'] = preg_split('/\s*,\s*/', $settings['projects_types']);

            // Filter: Status
            $statuses = Projects_Dropdown::getStatuses(array($registry, 'model_types' => $settings['projects_types']));
            $options = array();
            foreach ($statuses as $status) {
                $options[] = array(
                    'option_value' => $status['option_value'],
                    'label' => $status['label']
                );
                if ($status['option_value'] == 'finished') {
                    $options[] = array(
                        'option_value' => 'finished_1',
                        'label' => '&nbsp;&nbsp;&nbsp;&nbsp;' . $this->i18n('projects_substatus_finished_success')
                    );
                    $options[] = array(
                        'option_value' => 'finished_0',
                        'label' => '&nbsp;&nbsp;&nbsp;&nbsp;' . $this->i18n('projects_substatus_finished_failed')
                    );
                }
            }
            $filters['status'] = array(
                'name' => 'status',
                'type' => 'dropdown',
                'options' => $options,
                'label' => $this->i18n('reports_filter_status')
            );

            // Filter: Period
            $this->loadDefaultFilter($registry, $filters, 'period_from_to');
            $filters['period_from']['first_filter_label'] = $this->i18n('reports_filter_period_from');
            $filters['period_from']['help'] = $this->i18n('help_filter_period');

            // Filter: Client
            $this->loadDefaultFilter($registry, $filters, 'autocompleter', array('autocomplete_type' => 'customers'));
            $filters['customer']['label'] = $this->i18n('reports_filter_customer');
            $filters['customer']['autocomplete']['filters'] = array(
                '<type>' => (string)$settings['filter_customer_types']
            );
            $filters['customer']['autocomplete']['search'] = array('<code>', '<name>');
            $filters['customer']['autocomplete']['suggestions'] = '[<code>] <name> <lastname>';
            $filters['customer']['autocomplete']['fill_options'] = array(
                '$customer => <id>',
                '$customer_autocomplete => [<code>] <name> <lastname>',
                '$customer_oldvalue => [<code>] <name> <lastname>'
            );

            // Filter: Supplier
            $this->loadDefaultFilter($registry, $filters, 'autocompleter', array('autocomplete_type' => 'customers', 'filter_name' => 'supplier'));
            $filters['supplier']['label'] = $this->i18n('reports_filter_supplier');
            $filters['supplier']['autocomplete']['filters'] = array(
                '<type>' => (string)$settings['customer_type_supplier']
            );
            $filters['supplier']['autocomplete']['search'] = array('<code>', '<name>');
            $filters['supplier']['autocomplete']['suggestions'] = '[<code>] <name>';
            $filters['supplier']['autocomplete']['fill_options'] = array(
                '$supplier => <id>',
                '$supplier_autocomplete => <name>',
                '$supplier_oldvalue => <name>'
            );
            $filters['supplier']['type'] = 'custom_filter';
            $filters['supplier']['actual_type'] = 'autocompleter';
            $filters['supplier']['custom_template'] = PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html';
            $filters['supplier']['width'] = 244;

            // Filter: Project
            $filter = array (
                'name'            => 'project',
                'type'            => 'custom_filter',
                'actual_type'     => 'autocompleter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'autocomplete'    => array(
                    'type'         => 'projects',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'projects', 'projects'),
                    'suggestions'  => '[<code>] <name>',
                    'search'       => array('<code>', '<name>'),
                    'fill_options' => array(
                        '$project              => <id>',
                        '$project_autocomplete => [<code>] <name>',
                        '$project_oldvalue     => [<code>] <name>'
                    ),
                    'filters' => array(
                        '<type>' => implode(',', $settings['projects_types'])
                    ),
                    'clear' => 1
                ),
                'width' => 244,
                'label' => $this->i18n('reports_filter_project')
            );
            $filters['project'] = $filter;

            // Filter: Detail
            $filter = array (
                'name'            => 'detail',
                'type'            => 'custom_filter',
                'actual_type'     => 'autocompleter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'autocomplete'    => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'fill_options' => array(
                        '$detail              => <id>',
                        '$detail_autocomplete => <name>',
                        '$detail_oldvalue     => <name>'
                    ),
                    'filters' => array(
                        '<type>' => $settings['nomenclature_type_detail']
                    ),
                    'clear' => 1
                ),
                'width' => 244,
                'label' => $this->i18n('reports_filter_details')
            );
            $filters['detail'] = $filter;

            // Load JavaScript
            $custom_scripts = array(
                array(
                    'type' => 'external',
                    'src'  => $this->reportUrl . '/javascript/custom.js'
                )
            );
            $this->loadDefaultFilter($registry, $filters, 'scripts', array('custom_scripts' => $custom_scripts));

            // Prepare fields used for sorting
            $filters['sort'] = array(
                'name' => 'sort',
                'type' => 'hidden',
                'hidden' => true
            );
            $filters['order'] = array(
                'name' => 'order',
                'type' => 'hidden',
                'hidden' => true
            );
        }

        return $filters;
    }

    function processDependentFilters(&$filters) {
        $unset_filters = array();
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }
        }
        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        return $filters;
    }
}

?>
