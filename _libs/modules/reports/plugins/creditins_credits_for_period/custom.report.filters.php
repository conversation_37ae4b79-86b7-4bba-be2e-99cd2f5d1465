<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // Prepare array to collect all filters
            $filters = array();

            // Filter: Period
            $this->loadDefaultFilter($registry, $filters, 'period_from_to', array('required' => true));

            // Filter: Client
            $filters['client'] = array (
                'name'         => 'client',
                'type'         => 'autocompleter',
                'label'        => $this->i18n('reports_filter_client'),
                'autocomplete' => array(
                    'clear'         => 1,
                    'type'          => 'autocompleters',
                    'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'autocompleters', 'autocompleters', 'ajax_select'),
                    'plugin_search' => 'customQuery',
                    'plugin_params' => array(
                        'sql'    => "SELECT DISTINCT(c.id) as id, IF(c.is_company, c.eik, c.ucn) as eik_ucn, TRIM(CONCAT(ci.name, ' ', ci.lastname)) as name FROM " . DB_TABLE_DOCUMENTS . " AS d INNER JOIN " . DB_TABLE_CUSTOMERS . " AS c ON (c.id=d.customer AND c.type='" . CUSTOMER_TYPE_CLIENT . "' AND c.subtype='normal') INNER JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci ON (ci.parent_id = c.id AND ci.lang = '{$registry['lang']}') WHERE d.type='" . DOCUMENT_TYPE_LOAN_AGREEMENT . "' AND d.active=1 AND d.deleted_by=0 AND (<search_string_parts>)",
                        'search' => 'c.eik, ci.name, ci.lastname, c.ucn'
                    ),
                    'suggestions'   => '<name> (<eik_ucn>)',
                    'fill_options'  => array(
                        '$client => <id>',
                        '$client_autocomplete => <name> (<eik_ucn>)',
                        '$client_oldvalue => <name> (<eik_ucn>)',
                    ),
                    'buttons_hide'  => 'search report add'
                )
            );

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }
    }
?>
