<table class="reports_table">
  <tr>
    <th width="70">{#reports_th_num#}</th>
    <th width="100">{#reports_th_date#}</th>
    <th width="100">{#reports_th_first_date_of_paymet#}</th>
    <th width="250">{#reports_th_client#}</th>
    <th width="70">{#reports_th_term#}</th>
    <th width="100">{#reports_th_amount_granted_principal#}</th>
    <th width="100">{#reports_th_contractual_interest_rate#}</th>
    <th width="100">{#reports_th_guarantor_fee#}</th>
    <th width="100">{#reports_th_penalty_interest#}</th>
    <th width="250">{#reports_th_status#}</th>
  </tr>
  {assign var='total_price' value=0}
  {assign var='total_quantity' value=0}
  {assign var='total_article_trademark' value=0}
  {assign var='total_article_delivery_code' value=0}
  {foreach from=$reports_results item='result' key='document_id'}
    <tr>
      <td class="hcenter">{$result.name|escape}</td>
      <td class="hcenter">{$result.date|date_format:#date_short#}</td>
      <td class="hcenter">{$result.article_code|date_format:#date_short#}</td>
      <td class="hcenter">
        {capture assign='client'}{$result.customer_name|escape} ({$result.eik_ucn}){/capture}
        <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports&reports=generate_report&report_type=creditins_customer_file&client={$result.customer_id}&client_autocomplete={$client|escape:'url'}">{$result.customer_name|escape}<br />({$result.eik_ucn})</a>
      </td>
      <td class="hcenter">{$result.repayment_period}</td>
      <td class="hright">{$result.total_price|number_format:"2":".":" "}</td>
      <td class="hright">{$result.total_quantity|number_format:"2":".":" "}</td>
      <td class="hright">{$result.total_article_trademark|number_format:"2":".":" "}</td>
      <td class="hright">{$result.total_article_delivery_code|number_format:"2":".":" "}</td>
      <td class="hleft">
        {capture assign='status_name'}documents_status_{$result.status}{/capture}
        {$smarty.config.$status_name|escape}
        {if !empty($result.substatus_name)}
          / {$result.substatus_name}
        {/if}
      </td>
    </tr>
    {assign var='total_price' value=$total_price+$result.total_price}
    {assign var='total_quantity' value=$total_quantity+$result.total_quantity}
    {assign var='total_article_trademark' value=$total_article_trademark+$result.total_article_trademark}
    {assign var='total_article_delivery_code' value=$total_article_delivery_code+$result.total_article_delivery_code}
  {foreachelse}
    <tr>
      <td class="error" colspan="10">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  {if !empty($reports_results)}
    <tr>
      <th class="hright" colspan="5">{#reports_th_total#}</th>
      <th class="hright">{$total_price|number_format:"2":".":" "}</th>
      <th class="hright">{$total_quantity|number_format:"2":".":" "}</th>
      <th class="hright">{$total_article_trademark|number_format:"2":".":" "}</th>
      <th class="hright">{$total_article_delivery_code|number_format:"2":".":" "}</th>
      <th>&nbsp;</td>
    </tr>
  {/if}
</table>