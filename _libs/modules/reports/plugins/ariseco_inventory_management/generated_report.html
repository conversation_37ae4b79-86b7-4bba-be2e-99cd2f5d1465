{if empty($reports_additional_options.error)}
  {assign var='settings' value=$reports_additional_options.settings}

  <table id="report_inventory_management_results_table" class="reports_table{if $settings.arise_freeze_table_headers} freeze_table_headers{/if}"
      data-materials-warehouses-quantities="{$reports_results.materials_warehouses_quantities|@json_encode|escape}">
    <thead>
      <tr class="reports_title_row">
        <th class="header_atelier freeze_column">{#atelier#}</th>
        <th colspan="4" class="header_material freeze_column">{#material#}</th>
        <th colspan="5" class="header_availability_and_expected_commodities_transfers">{#availability_and_expected_commodities_transfers#}</th>
        <th colspan="5">{#expected_materials_delivery_request#}</th>
        <th colspan="8">{#needed_quantity_for_planned_orders#}</th>
        <th colspan="2">{#availability_other_warehouses#}</th>
        <th colspan="4">{#suggested_transferred_quantity#}</th>
      </tr>
      <tr class="reports_title_row">
        <th rowspan="2" class="warehouse_name freeze_column">{#warehouse#}</th>

        <th rowspan="2" class="material_code freeze_column">{#arise_code#}</th>
        <th rowspan="2" class="material_name freeze_column">{#name#}</th>
        <th rowspan="2" class="material_type freeze_column">{#type#}</th>
        <th rowspan="2" class="material_color freeze_column">{#material_color#}</th>

        <th rowspan="2" class="warehouse_quantity_without_reserved">{#current_availability#}</th>
        <th rowspan="2" class="warehouse_quantity_reserved">{#reserved#}</th>
        <th rowspan="2" class="commodities_transfers">{#commodities_transfers_not_received#}</th>
        <th rowspan="2" class="expected_quantity">{#expected_quantity#}</th>
        <th rowspan="2" class="virtual_availability">{#virtual_availability#}</th>

        <th rowspan="2" class="delivery_request_number_date">{#number_date#}</th>
        <th rowspan="2" class="delivery_request_supplier">{#supplier#}</th>
        <th rowspan="2" class="delivery_request_status">{#status#}</th>
        <th rowspan="2" class="delivery_request_expected_delivery_quantity">{#expected_delivery_quantity#}</th>
        <th rowspan="2" class="delivery_request_total_quantity">{help label_content=#total_quantity# text_content=#help_total_quantity#}</th>

        <th colspan="5">{#production_orders#}</th>
        <th colspan="3">{#cut_orders#}</th>

        <th rowspan="2" class="other_warehouse_name">{#warehouse#}</th>
        <th rowspan="2" class="other_warehouse_available">{#other_warehouse_available#}</th>

        <th rowspan="2" class="transfer_quantity">{#transfer_quantity#}</th>
        <th rowspan="2" class="shortage_quantity">{#shortage_quantity#}</th>
        <th rowspan="2" class="left_to_transfer">{#left_to_transfer#}</th>
        <th rowspan="2" class="total_transferred_quantity">{#total_transferred_quantity#}</th>
      </tr>
      <tr class="reports_title_row">
        <th class="production_orders_nums">{#production_orders_nums#}</th>
        <th class="production_orders_customers">{#production_orders_customers#}</th>
        <th class="production_orders_stock_orders">{#production_orders_stock_orders#}</th>
        <th class="production_orders_total_needed">{#production_orders_total_needed#}</th>
        <th class="production_orders_real_quantity">{#production_orders_real_quantity#}</th>

        <th class="cut_orders_customers">{#cut_orders_customers#}</th>
        <th class="cut_orders_stock_orders">{#cut_orders_stock_orders#}</th>
        <th class="cut_orders_total">{#cut_orders_total#}</th>
      </tr>
    </thead>
    <tbody>
      {foreach from=$reports_results.warehouses_materials key='warehouse_id' item='warehouse_materials_ids'}
        {foreach from=$warehouse_materials_ids item='material_id' name='warehouse_materials'}
          {assign var='material' value=$reports_results.materials[$material_id]}
          <tr>
            {if $smarty.foreach.warehouse_materials.first}
              <td rowspan="{$warehouse_materials_ids|@count}" class="warehouse_name freeze_column">
                <div class="warehouse_name_container">
                  {$reports_results.warehouses[$warehouse_id].name|escape}
                </div>
              </td>
            {/if}

            <td class="material_code freeze_column">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports&amp;reports=generate_report&amp;report_type=ariseco_materials_warehouse_planning&amp;generated_report=1&amp;material[0]={$material_id}&material_autocomplete[0]=%5B{$material.code}%5D%20{$material.name|escape:'url'}" target="_blank">
                {$material.code}
              </a>
            </td>
            <td class="material_name freeze_column">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$material_id}" target="_blank">
                {$material.name}
              </a>
            </td>
            <td class="material_type freeze_column">
              {$material.type_name}
            </td>
            <td class="material_color freeze_column">
              {$material.color}
            </td>

            <td class="warehouse_quantity_without_reserved">
              {if $reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].without_reserved}
                {$reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].without_reserved|number_format_depending_type:2:".":" "}
                {$material.product_measure}
              {/if}
            </td>
            <td class="warehouse_quantity_reserved">
              {if $reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].reserved}
                {if $reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].reservations_search_url}
                  <a href="{$reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].reservations_search_url}" target="_blank">
                {/if}
                {$reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].reserved|number_format_depending_type:2:".":" "}
                {$material.product_measure}
                {if $reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].reservations_search_url}
                  </a>
                {/if}
              {/if}
            </td>
            {capture assign='index_class'}row_table_container_{counter name='row_table_container'}{/capture}
            <td class="commodities_transfers row_table_container {$index_class}" data-index-class="{$index_class}">
              {assign var='material_commodities_transfers_total_quantity' value=0}
              <table>
                {if $reports_results.materials_commodities_transfers && array_key_exists($material_id, $reports_results.materials_commodities_transfers) && array_key_exists($warehouse_id, $reports_results.materials_commodities_transfers[$material_id])}
                    {foreach from=$reports_results.materials_commodities_transfers[$material_id][$warehouse_id] item='material_commodities_transfer'}
                      <tr>
                        <td>
                          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=warehouses_documents&amp;warehouses_documents=view&amp;view={$material_commodities_transfer.id}" target="_blank">
                            {$material_commodities_transfer.date|date_format:#date_short#}
                          </a>
                        </td>
                      </tr>
                      {assign var='material_commodities_transfers_total_quantity' value=$material_commodities_transfers_total_quantity+$material_commodities_transfer.quantity}
                    {/foreach}
                {else}
                  <tr><td>&nbsp;</td></tr>
                {/if}
              </table>
            </td>
            <td class="expected_quantity">
              {if $material_commodities_transfers_total_quantity}
                {$material_commodities_transfers_total_quantity|number_format_depending_type:2:".":" "}
                {$material.product_measure}
              {/if}
            </td>
            <td class="virtual_availability">
              {math assign='virtual_availability'
                equation="current_availability + reserved + expected_quantity"
                current_availability=$reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].without_reserved|default:0
                reserved=$reports_results.materials_warehouses_quantities[$material_id][$warehouse_id].reserved|default:0
                expected_quantity=$material_commodities_transfers_total_quantity|default:0
              }
              {if $virtual_availability}
                {$virtual_availability|number_format_depending_type:2:".":" "}
                {$material.product_measure}
              {/if}
            </td>
            {capture assign='index_class'}row_table_container_{counter name='row_table_container'}{/capture}
            <td class="expected_materials_delivery_request row_table_container {$index_class}" data-index-class="{$index_class}" colspan="5">
              <table>
                {if $reports_results.materials_delivery_requests && array_key_exists($material_id, $reports_results.materials_delivery_requests)}
                  {foreach from=$reports_results.materials_delivery_requests[$material_id] item='material_delivery_request'}
                    <tr>
                      <td class="delivery_request_number_date">
                        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$material_delivery_request.id}" target="_blank">
                          {$material_delivery_request.full_num}/{$material_delivery_request.date|date_format:#date_short#}
                        </a>
                      </td>
                      <td class="delivery_request_supplier">
                        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$material_delivery_request.customer}" target="_blank">
                          {$material_delivery_request.customer_name|escape}
                        </a>
                      </td>
                      <td class="delivery_request_status">
                        <img src="{$theme->imagesUrl}small/documents_{$material_delivery_request.status}.png"/>
                        {capture assign='status_name'}documents_status_{$material_delivery_request.status}{/capture}
                        {$smarty.config.$status_name|escape}
                        {if $material_delivery_request.substatus_name}
                          &raquo; {$material_delivery_request.substatus_name|escape}
                        {/if}
                      </td>
                      <td class="delivery_request_expected_delivery_quantity">
                        {$material_delivery_request.quantity|number_format_depending_type:2:".":" "}
                        {$material.product_measure}
                      </td>
                      <td class="delivery_request_total_quantity">
                        {math assign='delivery_request_total_quantity'
                          equation="virtual_availability + delivery_quantity"
                          virtual_availability=$virtual_availability|default:0
                          delivery_quantity=$material_delivery_request.quantity|default:0
                        }
                        {if $delivery_request_total_quantity}
                          {$delivery_request_total_quantity|number_format_depending_type:2:".":" "}
                          {$material.product_measure}
                        {/if}
                      </td>
                    </tr>
                  {/foreach}
                {else}
                  <tr>
                    <td class="delivery_request_number_date">&nbsp;</td>
                    <td class="delivery_request_supplier">&nbsp;</td>
                    <td class="delivery_request_status">&nbsp;</td>
                    <td class="delivery_request_expected_delivery_quantity">&nbsp;</td>
                    <td class="delivery_request_total_quantity">&nbsp;</td>
                  </tr>
                {/if}
              </table>
            </td>

            <td class="production_orders_nums">
              {if isset($reports_results.orders_quantities[$material_id][$warehouse_id].doc_ids)}
                {foreach from=$reports_results.orders_quantities[$material_id][$warehouse_id].doc_nums key='doc_id' item='doc_num' name='doc_ids'}
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$doc_id}" target="_blank">{$doc_num}</a>
                  {if !$smarty.foreach.doc_ids.last}
                    <br />
                  {/if}
                {/foreach}
              {/if}
            </td>

            {assign var='production_orders_customers' value=0}
            {if array_key_exists($material_id, $reports_results.orders_quantities)
                && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
                && $reports_results.orders_quantities[$material_id][$warehouse_id].production_customers}
              {assign var='production_orders_customers' value=$reports_results.orders_quantities[$material_id][$warehouse_id].production_customers}
            {/if}

            {assign var='cut_orders_customers' value=0}
            {if array_key_exists($material_id, $reports_results.orders_quantities)
                  && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
                  && $reports_results.orders_quantities[$material_id][$warehouse_id].cut_customers}
              {assign var='cut_orders_customers' value=$reports_results.orders_quantities[$material_id][$warehouse_id].cut_customers}
            {/if}

            {assign var='production_orders_customers' value=$production_orders_customers-$cut_orders_customers}

            {assign var='production_orders_stock_orders' value=0}
            {if array_key_exists($material_id, $reports_results.orders_quantities)
                && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
                && $reports_results.orders_quantities[$material_id][$warehouse_id].production_stock}
              {assign var='production_orders_stock_orders' value=$reports_results.orders_quantities[$material_id][$warehouse_id].production_stock}
            {/if}

            {assign var='cut_orders_stock_orders' value=0}
            {if array_key_exists($material_id, $reports_results.orders_quantities)
                  && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
                  && $reports_results.orders_quantities[$material_id][$warehouse_id].cut_stock}
              {assign var='cut_orders_stock_orders' value=$reports_results.orders_quantities[$material_id][$warehouse_id].cut_stock}
            {/if}

            {assign var='production_orders_stock_orders' value=$production_orders_stock_orders-$cut_orders_stock_orders}

            {assign var='production_orders_total_needed' value=$production_orders_customers+$production_orders_stock_orders}
            {assign var='cut_orders_total_needed' value=$cut_orders_customers+$cut_orders_stock_orders}
            <td class="production_orders_customers">
              {if $production_orders_customers}
                {if $production_orders_customers lt 0}
                  <span class="negative-quantity">
                {/if}
                {$production_orders_customers|number_format_depending_type:2:".":" "}
                {if $production_orders_customers lt 0}
                  </span>
                {/if}
                {$material.product_measure}
              {/if}
            </td>
            <td class="production_orders_stock_orders">
              {if $production_orders_stock_orders}
                {if $production_orders_stock_orders lt 0}
                  <span class="negative-quantity">
                {/if}
                {$production_orders_stock_orders|number_format_depending_type:2:".":" "}
                {if $production_orders_stock_orders lt 0}
                  </span>
                {/if}
                {$material.product_measure}
              {/if}
            </td>
            <td class="production_orders_total_needed">
              {if $production_orders_total_needed}
                {if $production_orders_total_needed lt 0}
                  <span class="negative-quantity">
                {/if}
                {$production_orders_total_needed|number_format_depending_type:2:".":" "}
                {if $production_orders_total_needed lt 0}
                  </span>
                {/if}
                {$material.product_measure}
              {/if}
            </td>
            <td class="production_orders_real_quantity">
              {if array_key_exists($material_id, $reports_results.orders_quantities)
                  && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
                  && !empty($reports_results.orders_quantities[$material_id][$warehouse_id].real_quantity)}
                {$reports_results.orders_quantities[$material_id][$warehouse_id].real_quantity|number_format_depending_type:2:".":" "}
              {/if}
            </td>
            <td class="cut_orders_customers">
              {if $cut_orders_customers}
                {$cut_orders_customers|number_format_depending_type:2:".":" "}
                {$material.product_measure}
              {/if}
            </td>
            <td class="cut_orders_stock_orders">
              {if $cut_orders_stock_orders}
                {$cut_orders_stock_orders|number_format_depending_type:2:".":" "}
                {$material.product_measure}
              {/if}
            </td>
            <td class="cut_orders_total_needed">
              {if $cut_orders_total_needed}
                {$cut_orders_total_needed|number_format_depending_type:2:".":" "}
                {$material.product_measure}
              {/if}
            </td>
            {capture assign='index_class'}row_table_container_{counter name='row_table_container'}{/capture}
            <td class="other_warehouses row_table_container {$index_class}" data-index-class="{$index_class}" colspan="3">
              <table>
                {assign var='other_warehouses_html' value=''}
                {if $reports_results.materials_warehouses_quantities[$material_id]}
                  {foreach from=$reports_results.materials_warehouses_quantities[$material_id] key='other_warehouse_id' item='other_warehouse'}
                    {if $other_warehouse_id ne $warehouse_id && (empty($reports_additional_options.filter_other_warehouses) || in_array($other_warehouse_id, $reports_additional_options.filter_other_warehouses))}
                      {capture assign='other_warehouses_html'}{$other_warehouses_html}
                        <tr>
                          <td class="other_warehouse_name">
                            {$reports_results.warehouses[$other_warehouse_id].name|escape}
                          </td>
                          <td class="other_warehouse_available">
                            {$other_warehouse.without_reserved|number_format_depending_type:2:".":" "}
                            {$material.product_measure}
                          </td>
                          <td class="transfer_quantity">
                            {assign var='material_transfer_key' value=`$other_warehouse_id`_`$warehouse_id`_`$material_id`}
                            {capture assign="transfer_quantity_custom_class"}from_warehouse_{$other_warehouse_id} to_warehouse_{$warehouse_id} material_{$material_id}{/capture}
                            {assign var='transfer_quantity' value=''}
                            {if !empty($reports_additional_options.auto_fill_transfer_quantity)
                                && array_key_exists($material_id, $reports_results.orders_quantities)
                                && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
                                && !empty($reports_results.orders_quantities[$material_id][$warehouse_id].left_to_transfer)
                                && $reports_results.orders_quantities[$material_id][$warehouse_id].left_to_transfer gt 0}
                              {assign var='transfer_quantity' value=$reports_results.orders_quantities[$material_id][$warehouse_id].left_to_transfer}
                            {/if}
                            {include file='input_text.html'
                              name='transfer_quantity'
                              eq_indexes=true
                              index=$material_transfer_key
                              standalone=true
                              restrict=insertOnlyFloats
                              onchange="reportArisecoInventoryManagement.validateTransferQuantity(this);"
                              onkeyup="reportArisecoInventoryManagement.validateTransferQuantity(this);"
                              custom_class=$transfer_quantity_custom_class
                              value=$transfer_quantity
                            }
                            {$material.product_measure}
                            {include file='input_hidden.html'
                              name='from_warehouse'
                              eq_indexes=true
                              index=$material_transfer_key
                              standalone=true
                              value=$other_warehouse_id
                            }
                            {include file='input_hidden.html'
                              name='to_warehouse'
                              eq_indexes=true
                              index=$material_transfer_key
                              standalone=true
                              value=$warehouse_id
                            }
                            {include file='input_hidden.html'
                              name='transfer_article_id'
                              eq_indexes=true
                              index=$material_transfer_key
                              standalone=true
                              value=$material_id
                            }
                            {if isset($reports_results.orders_quantities[$material_id][$warehouse_id].doc_ids)}
                              {capture assign='doc_ids_list'}{','|implode:$reports_results.orders_quantities[$material_id][$warehouse_id].doc_ids}{/capture}
                              {include file='input_hidden.html'
                                name='doc_ids'
                                eq_indexes=true
                                index=$material_transfer_key
                                standalone=true
                                value=$doc_ids_list
                              }
                            {/if}
                          </td>
                        </tr>
                      {/capture}
                    {/if}
                  {/foreach}
                {/if}
                {if $other_warehouses_html}
                  {$other_warehouses_html}
                {else}
                  <tr>
                    <td class="other_warehouse_name">&nbsp;</td>
                    <td class="other_warehouse_available">&nbsp;</td>
                    <td class="transfer_quantity">&nbsp;</td>
                  </tr>
                {/if}
              </table>
            </td>
            <td class="shortage_quantity">
              {math assign='shortage_quantity'
                equation='needed_quantity - virtual_availability'
                needed_quantity=$production_orders_total_needed
                virtual_availability=$virtual_availability}
              {if $shortage_quantity gt 0}
                {$shortage_quantity|number_format_depending_type:2:".":" "}
                {$material.product_measure}
              {/if}
            </td>
            <td class="left_to_transfer">
              {if array_key_exists($material_id, $reports_results.orders_quantities)
                  && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
                  && !empty($reports_results.orders_quantities[$material_id][$warehouse_id].left_to_transfer)}
                {$reports_results.orders_quantities[$material_id][$warehouse_id].left_to_transfer|number_format_depending_type:2:".":" "}
              {/if}
            </td>
            <td class="total_transferred_quantity">
              {if array_key_exists($material_id, $reports_results.orders_quantities)
              && array_key_exists($warehouse_id, $reports_results.orders_quantities[$material_id])
              && !empty($reports_results.orders_quantities[$material_id][$warehouse_id].total_transferred_quantity)}
              {$reports_results.orders_quantities[$material_id][$warehouse_id].total_transferred_quantity|number_format_depending_type:2:".":" "}
              {/if}
            </td>
          </tr>
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="29" class="error">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </tbody>
  </table>

  <button type="button" class="button" onclick="reportArisecoInventoryManagement.addTransfers(this.form);">{#transfer_quantities#}</button>
{/if}
