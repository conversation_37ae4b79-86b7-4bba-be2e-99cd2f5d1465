<?php
    Class Eticom_Al_Orders Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            define('DOCUMENT_CONTRACT_TYPE_ID', 3);
            define('DOCUMENT_CONTRACT_TYPE_DIRECTION', 2);
            define('DOCUMENT_ORDER_TYPE_ID', 5);
            define('DOCUMENT_ORDER_TYPE_DIRECTION', 3);
            define('CONTRACT_QUANTITY', 'con_area_alu');
            define('ORDER_QUANTITY', 'ora_area_alu');
            define('FRAME_NAME', 'alu_profile');

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            if (! empty($filters['document_id'])) {
                //sql to take the ids of the needed additional vars
                $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND ((fm.model_type=' . DOCUMENT_CONTRACT_TYPE_ID . ' AND fm.name="' . CONTRACT_QUANTITY . '") OR (fm.model_type=' . DOCUMENT_ORDER_TYPE_ID . ' AND fm.name="' . ORDER_QUANTITY . '") OR (fm.model_type=' . DOCUMENT_ORDER_TYPE_ID . ' AND fm.name="' . FRAME_NAME . '")) ORDER BY fm.position';
                $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                $contract_quantity_id = '';
                $order_quantity_id = '';
                $frame_name_id = '';

                //assign the ids to vars
                foreach ($var_ids as $vars) {
                    if ($vars['name'] == CONTRACT_QUANTITY) {
                        $contract_quantity_id = $vars['id'];
                    } else if ($vars['name'] == ORDER_QUANTITY) {
                        $order_quantity_id = $vars['id'];
                    } else if ($vars['name'] == FRAME_NAME) {
                        $frame_name_id = $vars['id'];
                    }
                }

                //sql to take info from delivers
                $sql_for_contracts['select'] = 'SELECT d_contract.id as contract_id, d_contract.full_num as full_num, ' . "\n" .
                                               DOCUMENT_CONTRACT_TYPE_DIRECTION . ' as contract_direction, ' . "\n" .
                                               'd_cstm_contract_quantity.value as contract_quantity';

                $sql_for_contracts['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d_contract' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_contract_quantity' . "\n" .
                                               '  ON (d_cstm_contract_quantity.model_id=d_contract.id AND d_cstm_contract_quantity.var_id="' . $contract_quantity_id . '")' . "\n";

                $sql_for_contracts['where']  = 'WHERE d_contract.id="' . $filters['document_id'] . '" AND d_contract.deleted_by="0" AND d_contract.type="' . DOCUMENT_CONTRACT_TYPE_ID . '"';

                $query_contracts = implode("\n", $sql_for_contracts);
                $records_for_contract = $registry['db']->GetAll($query_contracts);

                // form the array with the contract info
                $contract_info = array();
                foreach ($records_for_contract as $recs) {
                    $contract_info['id'] = $recs['contract_id'];
                    $contract_info['full_num'] = $recs['full_num'];
                    $contract_info['direction'] =  $recs['contract_direction'];
                    $contract_info['contract_quantity'] =  $recs['contract_quantity'];
                }

                //sql to take info from delivers
                $sql_for_orders['select']    = 'SELECT d_order.id as order_id, d_order.full_num as full_num, ' . "\n" .
                                               DOCUMENT_ORDER_TYPE_DIRECTION . ' as order_direction, ' .
                                               'd_cstm_order_quantity.value as order_quantity, ' . "\n" .
                                               'd_cstm_profile_name.value as profile_name';
                                               //, d_cstm_base_amount.value as articles_amount_contract' . "\n";
                $sql_for_orders['from']      = 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d_order' . "\n" .
                                               '  ON (d_order.id=dr.parent_id AND dr.link_to_model_name="Document" AND d_order.type="' . DOCUMENT_ORDER_TYPE_ID . '")' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_order_quantity' . "\n" .
                                               '  ON (d_cstm_order_quantity.model_id=d_order.id AND d_cstm_order_quantity.var_id="' . $order_quantity_id . '")' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_profile_name' . "\n" .
                                               '  ON (d_cstm_profile_name.model_id=d_order.id AND d_cstm_profile_name.var_id="' . $frame_name_id . '")' . "\n";
                $sql_for_orders['where']     = 'WHERE dr.link_to="' . $filters['document_id'] . '" AND d_order.type="' . DOCUMENT_ORDER_TYPE_ID . '"';

                $query_orders = implode("\n", $sql_for_orders);
                $records_orders = $registry['db']->GetAll($query_orders);

                // form the array with the oreders list
                $orders_list = array();
                $contract_ordered = 0;
                // complete the array with articles
                foreach ($records_orders as $key => $recs) {
                    $orders_list[$key]['id'] = $recs['order_id'];
                    $orders_list[$key]['full_num'] = $recs['full_num'];
                    $orders_list[$key]['direction'] = $recs['order_direction'];
                    $orders_list[$key]['profile_name'] = $recs['profile_name'];
                    $orders_list[$key]['quantity'] = $recs['order_quantity'];
                    $contract_ordered += $recs['order_quantity'];
                }

                if (! empty($contract_info)) {
                    $contract_info['ordered'] = $contract_ordered;
                    $contract_info['left_to_order'] = $contract_info['contract_quantity'] - $contract_info['ordered'];
                }

                $records['additional_options']['contract_info'] = $contract_info;
                $records['records'] = $orders_list;
            } else {
                $records = array();
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($records, 0);
            } else {
                $results = $records;
            }

            return $results;
        }
    }
?>