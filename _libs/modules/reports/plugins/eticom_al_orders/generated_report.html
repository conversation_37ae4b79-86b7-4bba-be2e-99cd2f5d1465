<table border="0" cellpadding="0" cellspacing="0" width="500">
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        {if !empty ($reports_additional_options.contract_info)}
            <tr>
              <td colspan="4" class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_contract_info#|escape}</div></td>
            </tr>
            <tr cellpadding="3">
              <td colspan="3" nowrap="nowrap" style="padding: 3px;">
                <strong>{#reports_contract_number#|escape}</strong>
              </td>
              <td nowrap="nowrap" style="padding: 3px;" class="hright">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$reports_results.contract_info.id}">{$reports_additional_options.contract_info.full_num|numerate:$reports_additional_options.contract_info.direction|default:"&nbsp;"}</a>
              </td>
            </tr>
            <tr>
              <td colspan="3" style="padding: 3px;">
                <strong>{#reports_contract_quantity#|escape}</strong>
              </td>
              <td style="padding: 3px;" class="hright">{$reports_additional_options.contract_info.contract_quantity}</td>
            </tr>
            <tr>
              <td colspan="3" style="padding: 3px;">
                <strong>{#reports_contract_ordered#|escape}</strong>
              </td>
              <td style="padding: 3px;" class="hright">{$reports_additional_options.contract_info.ordered}</td>
            </tr>
            <tr>
              <td colspan="3" style="padding: 3px;">
                <strong>{#reports_contract_left#|escape}</strong>
              </td>
              <td style="padding: 3px;" class="hright">{$reports_additional_options.contract_info.left_to_order}</td>
            </tr>
            <tr>
              <td colspan="4">&nbsp;</td>
            </tr>
        {/if}
        <tr>
          <td class="t_caption t_border" nowrap="nowrap" width="25"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_order_doc_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_order_profile_name#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_order_order_quantity#|escape}</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter' print=false}
        {foreach from=$reports_results.records item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}{if $result.deleted_by} t_deleted{/if}">
            <td class="t_border hright" nowrap="nowrap">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border" nowrap="nowrap">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.profile_name|escape|default:"&nbsp;"}
            </td>
            <td class="hright">
              {$result.quantity|escape|default:"0"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="4">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="4"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>