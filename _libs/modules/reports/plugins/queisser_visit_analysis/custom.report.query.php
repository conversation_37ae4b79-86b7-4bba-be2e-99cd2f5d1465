<?php
    Class Queisser_Visit_Analysis Extends Reports {

        private static $i18n;

        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }
            $db = &$registry['db'];

            self::$i18n = &$registry['translater'];

            //IMPORTANT: use these flags to check if the results should be calculated (instead of $registry['messages']->getErrors())
            $errors = false;

            // validate filter data
            if (empty($filters['search_by']) || !in_array($filters['search_by'], array(1, 2))
                || $filters['search_by'] == 1 && empty($filters['date'])
                || $filters['search_by'] == 2 && (empty($filters['date_from']) || empty($filters['date_to']) || $filters['date_from'] > $filters['date_to'])) {
                $registry['messages']->setError(self::$i18n->translate('error_reports_complete_required_filters'));
                $errors = true;
            }

            if (!$errors) {
                //get all defined constants in the application
                $constants = get_defined_constants(true);
                //filter only user defined constants
                $constants = $constants['user'];

                // define entities
                $entities = array(
                    'pharmacy' => array('model' => 'Customer', 'model_type' => CUST_TYPE_PHARMACY),
                    'doctor' => array('model' => 'Customer', 'model_type' => CUST_TYPE_DOCTOR),
                    'dentist' => array('model' => 'Customer', 'model_type' => CUST_TYPE_DENTIST),
                    'target_visit' => array('model' => 'Document', 'model_type' => DOC_TYPE_TARGET_VISIT),
                    'target_campaign' => array('model' => 'Document', 'model_type' => DOC_TYPE_TARGET_CAMPAIGN),
                    'ray' => array('model' => 'Nomenclature', 'model_type' => NOM_TYPE_RAY)
                );

                // get ids of additional vars from constants
                $add_vars = array();
                $num_vars = 0;
                foreach ($constants as $key => $value) {
                    if (preg_match('#^(' . implode('|', array_keys($entities)) . ')_.*_var#i', $key, $matches)) {
                        $matches[1] = strtolower($matches[1]);
                        if (empty($add_vars[$matches[1]])) {
                            $add_vars[$matches[1]] = array();
                        }
                        $add_vars[$matches[1]][constant($key)] = $value;
                        $num_vars++;
                    }
                }

                // missing settings for additional variables
                if (count($add_vars) != count($entities)) {
                    $registry['messages']->setError(self::$i18n->translate('error_report_not_set'));
                    $errors = true;
                } else {
                    $where = array();
                    foreach ($entities as $entity => $props) {
                        $condition = '(';
                        foreach ($props as $k => $v) {
                            $condition .= $k . '="' . $v . '" AND ';
                        }
                        $condition .= 'name IN ("' . implode('", "', $add_vars[$entity]) . '"))';
                        $where[] = $condition;
                    }
                    $where = implode(' OR ' . "\n", $where);

                    $query = 'SELECT CONCAT(model, model_type) AS model, name, id' . "\n" .
                             'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                             'WHERE ' . $where;
                    $add_vars_data = $db->GetAll($query);

                    // settings do not match variable names
                    if (count($add_vars_data) != $num_vars) {
                        $registry['messages']->setError(self::$i18n->translate('error_report_not_set'));
                        $errors = true;
                    } else {
                        foreach ($entities as $entity => $props) {
                            $mkey = $props['model'] . $props['model_type'];
                            $entities[$entity]['vars'] = array();
                            foreach ($add_vars_data as $idx => $data) {
                                if ($data['model'] == $mkey) {
                                    $setting_name = array_search($data['name'], $add_vars[$entity]);
                                    if ($setting_name !== false) {
                                        $entities[$entity]['vars'][$setting_name] = $data['id'];
                                    }
                                    unset($add_vars_data[$idx]);
                                }
                            }
                        }

                        // aliases of vars arrays
                        $pharmacy_vars = &$entities['pharmacy']['vars'];
                        $doctor_vars = &$entities['doctor']['vars'];
                        $dentist_vars = &$entities['dentist']['vars'];
                        $target_visit_vars = &$entities['target_visit']['vars'];
                        $target_campaign_vars = &$entities['target_campaign']['vars'];

                        // get latest target visit document
                        $search_date = $filters['search_by'] == 1 ? $filters['date'] : $filters['date_to'];
                        $today = date('Y-m-d');
                        $query = 'SELECT d.id, dcstm_fd.value AS from_date, CONVERT(dcstm_mn.value, SIGNED INT) AS months_num' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_VISIT . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_fd.model_id AND dcstm_fd.var_id=\'' . $target_visit_vars[TARGET_VISIT_FROM_DATE_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_mn' . "\n" .
                                 '  ON dcstm_fd.model_id=dcstm_mn.model_id AND dcstm_mn.var_id=\'' . $target_visit_vars[TARGET_VISIT_MONTHS_NUM_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_fd.value!="" AND CONVERT(dcstm_mn.value, SIGNED INT)>0' . "\n" .
                                 '  AND dcstm_fd.value <= \'' . $search_date . '\'' . "\n" .
                                 'ORDER BY dcstm_fd.value DESC, d.id DESC' . "\n" .
                                 'LIMIT 1';
                        $target_visit = $db->GetRow($query);
                        // validate lower boundary of date/date_to filter
                        if (!$target_visit) {
                            $registry['messages']->setError(self::$i18n->translate('error_reports_no_target_visit'));
                            $errors = true;
                        } elseif ($search_date > $today) {
                            // validate upper boundary of date/date_to filter
                            // searched date should not be in the future
                            $registry['messages']->setError(sprintf(self::$i18n->translate('error_reports_target_visit_date'), General::strftime('%d.%m.%Y', $today)));
                            $errors = true;
                        } else {
                            // calculate start and end of period that searched date is in
                            $target_visit['period_from'] = $target_visit['from_date'];
                            // get first date of month for 'from_date' and calculate periods as if document always starts from there
                            $target_visit['next_period_from'] = date_add(
                                date_create(preg_replace('/^(.*)\d{2}$/', '${1}01', $target_visit['from_date'])),
                                new DateInterval('P' . $target_visit['months_num'] . 'M')
                            )->format('Y-m-d');
                            while ($target_visit['next_period_from'] <= $search_date) {
                                $target_visit['period_from'] = $target_visit['next_period_from'];
                                $target_visit['next_period_from'] = date_add(
                                    date_create($target_visit['period_from']), new DateInterval('P' . $target_visit['months_num'] . 'M'))->format('Y-m-d');
                            }
                            $target_visit['period_to'] = date_sub(
                                date_create($target_visit['next_period_from']), new DateInterval('P1D'))->format('Y-m-d');
                            unset($target_visit['next_period_from']);
                        }
                    }
                }
            }

            $final_results = array();
            $summary_data = array(
                'visits_by_date'       => array(),
                'total_visits'         => 0,
                'total_doctors'        => 0,
                'total_dentists'       => 0,
                'total_pharmacies'     => 0,
                'free_days_for_period' => 0,
                'sickness_free_days'   => 0,
                'activities'           => 0
            );

            if (!$errors) {
                // get rays to search in
                $rays = array();
                if (!empty($filters['ray'])) {
                    $rays[] = $filters['ray'];
                } elseif (!empty($filters['agent'])) {
                    $rays = self::getRays($registry, $filters['agent'], false);
                    // sales rep without rays?
                    if (!$rays) {
                        $rays[] = 0;
                    }
                }

                // get class tag names
                $query = 'SELECT t.id, ti18n.name' . "\n" .
                         'FROM ' . DB_TABLE_TAGS . ' AS t' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n' . "\n" .
                         '  ON t.id=ti18n.parent_id AND ti18n.lang=\'' . $model_lang . '\'' . "\n" .
                         'WHERE t.id IN (' . CLASS_TAGS . ')';
                $class_tags = $db->GetAssoc($query);

                // get ids of sales representative users (including inactive and deleted)
                $query = 'SELECT id FROM ' . DB_TABLE_USERS . ' WHERE role=\'' . ROLE_SALES . '\'';
                $sales_reps = $db->GetCol($query);

                // define the days for summary table
                $start_date = '';
                $end_date = '';
                if ($filters['search_by'] == 1) {
                    $start_date = $filters['date'];
                    $end_date = $filters['date'];
                } else {
                    $start_date = $filters['date_from'];
                    $end_date = $filters['date_to'];
                }
                $current_calc_day = strtotime($start_date);
                while($current_calc_day <= strtotime($end_date)) {
                    $summary_data['visits_by_date'][General::strftime('%Y-%m-%d', $current_calc_day)] = 0;
                    $current_calc_day = strtotime('+1 day', $current_calc_day);
                }

                // CALCULATE SUMMARY DAYS IN THE PERIOD
                // get the users
                $included_users = array();
                if ($registry['currentUser']->get('role') == ROLE_SALES) {
                    $included_users[] = $registry['currentUser']->get('employee');
                } else {
                    $sql = 'SELECT `employee`' . "\n" .
                           'FROM ' . DB_TABLE_USERS . "\n" .
                           'WHERE `active`=1 AND `role`="' . ROLE_SALES . '"' . "\n" .
                           ($filters['agent'] ? ' AND `id`="' . $filters['agent'] . '"' : '');
                    $included_users = $db->GetCol($sql);
                }

                if (!empty($included_users)) {
                    require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                    // events in the period
                    $event_type = array(EVENT_FREE_DAYS, EVENT_SICKNESS);
                    $sql = 'SELECT DATE_FORMAT(`event_start`, "%Y-%m-%d") as event_start, DATE_FORMAT(DATE_ADD(`event_start`, interval (`duration`-1) minute), "%Y-%m-%d") as event_end, `type`' . "\n" .
                           'FROM ' . DB_TABLE_EVENTS . "\n" .
                           'WHERE `customer` IN ("' . implode('","', $included_users) . '") AND `type` IN ("' . implode('","', $event_type) . '")' . "\n" .
                           'HAVING event_start <= "' . $end_date . '" AND event_end >= "' . $start_date . '"' . "\n" .
                           'ORDER BY event_start' . "\n";
                    $included_events = $db->GetAll($sql);

                    foreach ($included_events as $event) {
                        $current_free_day = strtotime($event['event_start']);
                        while ($current_free_day<=strtotime($event['event_end'])) {
                            if (General::strftime('%Y-%m-%d', $current_free_day) >= $target_visit['period_from'] && General::strftime('%Y-%m-%d', $current_free_day) <= $target_visit['period_to'] &&
                                Calendars_Calendar::getWorkingDays($registry, General::strftime('%Y-%m-%d', $current_free_day), General::strftime('%Y-%m-%d', $current_free_day))) {
                                $summary_key = '';
                                switch($event['type']) {
                                    case EVENT_FREE_DAYS:
                                        $summary_key = 'free_days_for_period';
                                        break;
                                    case EVENT_SICKNESS:
                                        $summary_key = 'sickness_free_days';
                                        break;
                                }
                                $summary_data[$summary_key]++;
                            }
                            $current_free_day = strtotime('+1 day', $current_free_day);
                        }
                    }

                    // get the activities
                    // get the names of the users
                    $sql = 'SELECT CONCAT(`name`, " ", `lastname`)' . "\n" .
                           'FROM ' . DB_TABLE_CUSTOMERS_I18N . "\n" .
                           'WHERE `parent_id` IN ("' . implode('","', $included_users) . '") AND `lang`="' . $model_lang . '"' . "\n";
                    $included_users_names = $db->GetCol($sql);

                    $representative_search_clause = array();
                    foreach ($included_users_names as $inc_us_name) {
                        $representative_search_clause[] = 'ei18n.description LIKE "%' . $inc_us_name . '%"';
                        $representative_search_clause[] = 'ei18n.name LIKE "%' . $inc_us_name . '%"';
                    }

                    if (!empty($representative_search_clause)) {
                        $representative_search_clause = implode(' OR ', $representative_search_clause);
                        $sql = 'SELECT SQL_CALC_FOUND_ROWS DATE_FORMAT(`event_start`, "%Y-%m-%d") as event_start, DATE_FORMAT(DATE_ADD(`event_start`, interval (`duration`-1) minute), "%Y-%m-%d") as event_end' . "\n" .
                               'FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_EVENTS_I18N . ' AS ei18n' . "\n" .
                               '  ON (ei18n.parent_id=e.id AND ei18n.lang="' . $model_lang . '")' . "\n" .
                               'WHERE e.type="' . EVENT_ACTIVITIES . '" AND (' . $representative_search_clause . ')' . "\n" .
                               'HAVING event_start <= "' . $end_date . '" AND event_end >= "' . $start_date . '"' . "\n" .
                               'ORDER BY event_start' . "\n";
                        $db->Execute($sql);
                        $summary_data['activities'] = $db->GetOne('SELECT FOUND_ROWS();');
                    }


                }

                if ($filters['search_by'] == 1) {
                    // search by date

                    // 1 - Monday ... 7 - Sunday
                    $date_of_week = date('w', strtotime($filters['date']));
                    if ($date_of_week == 0) {
                        $date_of_week = 7;
                    }
                    // 9 - odd or 8 - even
                    $date_odd_even = (intval(substr($filters['date'], 8, 2)) % 2) ? WORK_TIME_ODD : WORK_TIME_EVEN;
                    // prepare condition for where clause
                    $doctor_workdays = $date_of_week . ', ' . $date_odd_even;

                    // get pharmacies
                    $query = 'SELECT CONCAT(c.id, \'_0\') AS idx, c.id, c.type, ctypes_i18n.name AS type_label, "pharmacy" as type_name, c.phone, ' . "\n" .
                             '  c.gsm, IF(c.type="' . CUST_TYPE_PHARMACY . '", ci18n.name, CONCAT(ci18n.name, " ", ci18n.lastname)) as name, ccstm_ray.value AS ray_id, ccstm_address.value AS address,' . "\n" .
                             '  CASE \'' . $date_of_week . '\' WHEN 6 THEN ccstm_sat.value WHEN 7 THEN ccstm_sun.value ELSE ccstm_week.value END AS worktime,' . "\n" .
                             '  CASE \'' . $date_of_week . '\' WHEN 6 THEN ccstm_sat_to.value WHEN 7 THEN ccstm_sun_to.value ELSE ccstm_week_to.value END AS worktime_to' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                             // filter only visited customers on searched date
                             'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                             '  ON fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                             '    AND fir.customer=c.id' . "\n" .
                             '    AND fir.issue_date=\'' . $filters['date'] . '\'' . "\n" .
                             '    AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON c.id=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_ray' . "\n" .
                             '  ON c.id=ccstm_ray.model_id AND ccstm_ray.var_id=\'' . $pharmacy_vars[PHARMACY_RAY_ID_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_address' . "\n" .
                             '  ON c.id=ccstm_address.model_id AND ccstm_address.var_id=\'' . $pharmacy_vars[PHARMACY_ADDRESS_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_workdays' . "\n" .
                             '  ON c.id=ccstm_workdays.model_id AND ccstm_workdays.var_id=\'' . $pharmacy_vars[PHARMACY_WORKDAYS_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_week' . "\n" .
                             '  ON c.id=ccstm_week.model_id AND ccstm_week.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_WEEK_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_week_to' . "\n" .
                             '  ON c.id=ccstm_week_to.model_id AND ccstm_week_to.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_WEEK__TO_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sat' . "\n" .
                             '  ON c.id=ccstm_sat.model_id AND ccstm_sat.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SAT_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sat_to' . "\n" .
                             '  ON c.id=ccstm_sat_to.model_id AND ccstm_sat_to.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SAT__TO_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sun' . "\n" .
                             '  ON c.id=ccstm_sun.model_id AND ccstm_sun.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SUN_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sun_to' . "\n" .
                             '  ON c.id=ccstm_sun_to.model_id AND ccstm_sun_to.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SUN__TO_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS ctypes_i18n' . "\n" .
                             '  ON c.type=ctypes_i18n.parent_id AND ctypes_i18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'WHERE c.type=\'' . CUST_TYPE_PHARMACY . '\' AND c.active=1 AND c.deleted_by=0' . "\n" .
                             '  AND (ccstm_workdays.value IS NULL OR ccstm_workdays.value="" OR ccstm_workdays.value LIKE \'%' . $date_of_week . '%\')' . "\n" .
                             ($rays ? '  AND ccstm_ray.value IN (\'' . implode('\', \'', $rays) . '\')' . "\n" : '') .
                             'GROUP BY c.id';
                    $targets = $db->GetAssoc($query);

                    // prepare var ids for iterative searches for each workplace
                    $workplaces = array();
                    // prepare condition for where clause for doctors with no data for working hours
                    $doctor_no_data = array();
                    $dentist_no_data = array();
                    foreach (array(1, 2, 3) as $place) {
                        $workplaces[$place] = array();
                        foreach (array(1, 2, 3) as $time) {
                            $workplaces[$place]['t' . $time] = array();
                            $workplaces[$place]['t' . $time . '_from'] = array();
                            $workplaces[$place]['t' . $time . '_to'] = array();
                            $workplaces[$place]['t' . $time][] = $doctor_vars[constant('DOCTOR_P' . $place . '_T' . $time . '_VAR')];
                            $workplaces[$place]['t' . $time][] = $dentist_vars[constant('DENTIST_P' . $place . '_T' . $time . '_VAR')];
                            $doctor_no_data[] = $doctor_vars[constant('DOCTOR_P' . $place . '_T' . $time . '_VAR')];
                            $dentist_no_data[] = $dentist_vars[constant('DENTIST_P' . $place . '_T' . $time . '_VAR')];
                            $workplaces[$place]['t' . $time . '_from'][] = $doctor_vars[constant('DOCTOR_P' . $place . '_T' . $time . '_FROM_VAR')];
                            $workplaces[$place]['t' . $time . '_from'][] = $dentist_vars[constant('DENTIST_P' . $place . '_T' . $time . '_FROM_VAR')];
                            $workplaces[$place]['t' . $time . '_to'][] = $doctor_vars[constant('DOCTOR_P' . $place . '_T' . $time . '_TO_VAR')];
                            $workplaces[$place]['t' . $time . '_to'][] = $dentist_vars[constant('DENTIST_P' . $place . '_T' . $time . '_TO_VAR')];
                        }
                    }

                    // get doctors
                    foreach ($workplaces as $place => $times) {
                        $query = 'SELECT CONCAT(c.id, \'_\', ccstm_ray.num) AS idx, c.id, c.type, ctypes_i18n.name AS type_label, IF(c.type="' . CUST_TYPE_DOCTOR . '", "doctor", "dentist") as type_name, ' . "\n" .
                                 '  c.phone, c.gsm, IF(c.type="' . CUST_TYPE_PHARMACY . '", ci18n.name, CONCAT(ci18n.name, " ", ci18n.lastname)) as name, ccstm_ray.value AS ray_id,' . "\n" .
                                 '  CONCAT(IF(ccstm_place.value IS NOT NULL, ccstm_place.value, \'\'), \', \', ' . "\n" .
                                 '    IF(ccstm_address.value IS NOT NULL, ccstm_address.value, \'\'), \' (\', ' . "\n" .
                                 '    IF(ccstm_city.value IS NOT NULL, ccstm_city.value, \'\'), \')\') AS address, ' . "\n" .
                                 '  IF(ccstm_t1.value IN (' . $doctor_workdays . '), ccstm_t1_from.value, IF(ccstm_t2.value IN (' . $doctor_workdays . '), ccstm_t2_from.value, ccstm_t3_from.value)) AS worktime,' . "\n" .
                                 '  IF(ccstm_t1.value IN (' . $doctor_workdays . '), ccstm_t1_to.value, IF(ccstm_t2.value IN (' . $doctor_workdays . '), ccstm_t2_to.value, ccstm_t3_to.value)) AS worktime_to' . "\n" .
                                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                 // filter only visited customers on searched date
                                 'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                                 '  ON fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '    AND fir.customer=c.id' . "\n" .
                                 '    AND fir.issue_date=\'' . $filters['date'] . '\'' . "\n" .
                                 '    AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON c.id=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_ray' . "\n" .
                                 '  ON c.id=ccstm_ray.model_id AND ccstm_ray.var_id IN (\'' . implode('\',\'', array($doctor_vars[DOCTOR_RAY_ID_VAR],$dentist_vars[DENTIST_RAY_ID_VAR])) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_place' . "\n" .
                                 '  ON c.id=ccstm_place.model_id AND ccstm_place.var_id IN (\'' . implode('\',\'', array($doctor_vars[DOCTOR_PLACE_VAR],$dentist_vars[DENTIST_PLACE_VAR])) . '\') AND ccstm_ray.num=ccstm_place.num' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_city' . "\n" .
                                 '  ON c.id=ccstm_city.model_id AND ccstm_city.var_id IN (\'' . implode('\',\'', array($doctor_vars[DOCTOR_CITY_VAR],$dentist_vars[DENTIST_CITY_VAR])) . '\') AND ccstm_ray.num=ccstm_city.num' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_address' . "\n" .
                                 '  ON c.id=ccstm_address.model_id AND ccstm_address.var_id IN (\'' . implode('\',\'', array($doctor_vars[DOCTOR_ADDRESS_VAR],$dentist_vars[DENTIST_ADDRESS_VAR])) . '\') AND ccstm_ray.num=ccstm_address.num' . "\n" .
                                 // config fields
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t1' . "\n" .
                                 '  ON c.id=ccstm_t1.model_id AND ccstm_t1.var_id IN (\'' . implode('\',\'', $times['t1']) . '\') AND ccstm_t1.value IN (' . $doctor_workdays . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t1_from' . "\n" .
                                 '  ON c.id=ccstm_t1_from.model_id AND ccstm_t1_from.var_id IN (\'' . implode('\',\'', $times['t1_from']) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t1_to' . "\n" .
                                 '  ON c.id=ccstm_t1_to.model_id AND ccstm_t1_to.var_id IN (\'' . implode('\',\'', $times['t1_to']) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t2' . "\n" .
                                 '  ON c.id=ccstm_t2.model_id AND ccstm_t2.var_id IN (\'' . implode('\',\'', $times['t2']) . '\') AND ccstm_t2.value IN (' . $doctor_workdays . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t2_from' . "\n" .
                                 '  ON c.id=ccstm_t2_from.model_id AND ccstm_t2_from.var_id IN (\'' . implode('\',\'', $times['t2_from']) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t2_to' . "\n" .
                                 '  ON c.id=ccstm_t2_to.model_id AND ccstm_t2_to.var_id IN (\'' . implode('\',\'', $times['t2_to']) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t3' . "\n" .
                                 '  ON c.id=ccstm_t3.model_id AND ccstm_t3.var_id IN (\'' . implode('\',\'', $times['t3']) . '\') AND ccstm_t3.value IN (' . $doctor_workdays . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t3_from' . "\n" .
                                 '  ON c.id=ccstm_t3_from.model_id AND ccstm_t3_from.var_id IN (\'' . implode('\',\'', $times['t3_from']) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t3_to' . "\n" .
                                 '  ON c.id=ccstm_t3_to.model_id AND ccstm_t3_to.var_id IN (\'' . implode('\',\'', $times['t3_to']) . '\')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS ctypes_i18n' . "\n" .
                                 '  ON c.type=ctypes_i18n.parent_id AND ctypes_i18n.lang=\'' . $model_lang . '\'' . "\n" .
                                 // config fields end
                                 'WHERE c.type IN (\'' . implode('\',\'', array(CUST_TYPE_DOCTOR, CUST_TYPE_DENTIST)) . '\') AND c.active=1 AND c.deleted_by=0' . "\n" .
                                 '  AND ccstm_ray.num=' . $place . "\n" .
                                 '  AND (ccstm_t1.value IN (' . $doctor_workdays . ') OR ccstm_t2.value IN (' . $doctor_workdays . ') OR ccstm_t3.value IN (' . $doctor_workdays . ') OR (SELECT COUNT(ccstm_t0.model_id) FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t0 WHERE ccstm_t0.model_id=c.id AND ccstm_t0.var_id IN (' . implode(',', array_merge($dentist_no_data, $doctor_no_data)) . '))=0)' . "\n" .
                                 ($rays ? '  AND ccstm_ray.value IN (\'' . implode('\', \'', $rays) . '\')' . "\n" : '') .
                                 'GROUP BY c.id';
                        $targets = array_merge($targets, $db->GetAssoc($query));
                    }

                    if ($targets) {
                        // prepare text for duration from start until end of current period
                        $period_text = sprintf(self::$i18n->translate('reports_date_text'),
                                               General::strftime('%d.%m.%Y', $filters['date']),
                                               General::strftime('%d.%m.%Y', $target_visit['period_from']),
                                               General::strftime('%d.%m.%Y', $target_visit['period_to']));

                        // get target visit grouping table
                        $target_visit_data = self::_getMeetingData($registry, $target_visit['id'], DOC_TYPE_TARGET_VISIT, $target_visit_vars);

                        // get target campaigns that overlap with period
                        $query = 'SELECT d.id' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_start' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_CAMPAIGN . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_start.model_id AND dcstm_start.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_START_PERIOD_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_finish' . "\n" .
                                 '  ON dcstm_start.model_id=dcstm_finish.model_id AND dcstm_finish.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_FINISH_PERIOD_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_start.value <= \'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND dcstm_finish.value >= \'' . $target_visit['period_from'] . '\'';
                        $target_campaigns = $db->GetCol($query);

                        // add campaign data to visit data
                        foreach ($target_campaigns as $doc_id) {
                            $target_campaign_data = self::_getMeetingData($registry, $doc_id, DOC_TYPE_TARGET_CAMPAIGN, $target_campaign_vars);
                            foreach ($target_campaign_data as $key => $num) {
                                if (array_key_exists($key, $target_visit_data)) {
                                    $target_visit_data[$key] += $num;
                                } else {
                                    $target_visit_data[$key] = $num;
                                }
                            }
                        }

                        $target_ids = array();
                        foreach ($targets as $tidx => $target) {
                            if (!in_array($target['id'], $target_ids)) {
                                $target_ids[] = $target['id'];
                            } elseif (empty($filters['ray'])) {
                                // each target should be displayed in just one ray
                                // (it doesn't matter if this is the working place of a doctor
                                // that was really visited or not)
                                unset($targets[$tidx]);
                            }
                        }

                        // last visit ever
                        $query = 'SELECT fir.customer, MAX(fir.issue_date) AS last_visited' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'GROUP BY fir.customer';
                        $last_visited = $db->GetAssoc($query);

                        // done visits within period that selected date is in
                        $query = 'SELECT fir.customer, fir.issue_date' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.issue_date>=\'' . $target_visit['period_from'] . '\'' . "\n" .
                                 '  AND fir.issue_date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'ORDER BY fir.issue_date ASC';
                        $done_visits_list = $db->GetAll($query);
                        $done_visits = array();
                        foreach ($done_visits_list as $dvl) {
                            if (!isset($done_visits[$dvl['customer']])) {
                                $done_visits[$dvl['customer']] = array();
                            }
                            $done_visits[$dvl['customer']][] = $dvl['issue_date'];
                        }

                        // planned visits for date from tomorrow until end of current period
                        $planned_visits = array();
                        $tomorrow = date_add(new DateTime(), new DateInterval('P1D'))->format('Y-m-d');
                        if ($tomorrow <= $target_visit['period_to']) {
                            $query = 'SELECT gt2.article_id, COUNT(d.id)' . "\n" .
                                     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                     '  ON d.type=\'' . DOC_TYPE_DAILY_SCHEDULE . '\' AND d.active=1 AND d.deleted_by=0' . "\n" .
                                     '    AND d.id=gt2.model_id AND gt2.model=\'Document\'' . "\n" .
                                     'WHERE d.date>=\'' . $tomorrow . '\' AND d.date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                     '  AND gt2.article_id IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                     'GROUP BY gt2.article_id';
                            $planned_visits = $db->GetAssoc($query);
                        }

                        $query = 'SELECT ct.model_id, MIN(ct.tag_id)' . "\n" .
                                 'FROM ' . DB_TABLE_TAGS_MODELS . ' AS ct' . "\n" .
                                 'WHERE ct.model=\'Customer\' AND ct.model_id IN (\'' . implode('\', \'', $target_ids) . '\') AND ct.tag_id IN (' . CLASS_TAGS . ')' . "\n" .
                                 'GROUP BY ct.model_id';
                        $tags = $db->GetAssoc($query);

                        // group results by ray
                        foreach ($targets as $tidx => $res) {
                            if (!isset($final_results[$res['ray_id']])) {
                                $final_results[$res['ray_id']] = array(
                                    'targets' => array()
                                );
                            }

                            // customer id
                            $cid = $res['id'];

                            if (!array_key_exists($cid, $final_results[$res['ray_id']]['targets'])) {

                                $phone = $res['phone'] ? explode("\n", $res['phone']) : array();
                                $res['gsm'] = $res['gsm'] ? explode("\n", $res['gsm']) : array();
                                $phone = array_merge($phone, $res['gsm']);
                                foreach ($phone as $idx => $data) {
                                    if (preg_match('/\|/', $data)) {
                                        $phone[$idx] = preg_replace('/(\|.*)$/', '', $data);
                                    }
                                    $phone[$idx] = trim($phone[$idx]);
                                }
                                unset($res['gsm']);
                                $res['phone'] = $phone;

                                $res['tag'] = array_key_exists($cid, $tags) ? $tags[$cid] : '';
                                $res['class'] = array_key_exists($res['tag'], $class_tags) ? $class_tags[$res['tag']] : '-';

                                $key = $res['type'] . '_' . $res['tag'];
                                $res['target_visits'] = array_key_exists($key, $target_visit_data) ? $target_visit_data[$key] : 0;

                                $res['last_visited'] = array_key_exists($cid, $last_visited) ? $last_visited[$cid] : '';

                                $res['done_visits'] = array_key_exists($cid, $done_visits) ? count($done_visits[$cid]) : 0;

                                $res['planned_visits'] = array_key_exists($cid, $planned_visits) ? $planned_visits[$cid] : 0;

                                $final_results[$res['ray_id']]['targets'][$cid] = $res;

                                if (isset($done_visits[$cid])) {
                                    foreach ($done_visits[$cid] as $vis_dat) {
                                        if (isset($summary_data['visits_by_date'][$vis_dat])) {
                                            $summary_data['visits_by_date'][$vis_dat]++;
                                            $summary_data['total_visits']++;
                                        }
                                    }
                                }
                            }

                            $res['worktime'] = sprintf('%s - %s', ($res['worktime'] ?: 'N/A'), ($res['worktime_to'] ?: 'N/A'));

                            // number of the workplace; pharmacies - 0, doctors - 1, 2 or 3
                            $tnum = intval(preg_replace('#(\d*)_?(\d*)#', '$2', $tidx));
                            $final_results[$res['ray_id']]['targets'][$cid]['locations'][] = array(
                                'num'      => $tnum,
                                'address'  => $res['address'],
                                'worktime' => $res['worktime']
                            );
                            unset($final_results[$res['ray_id']]['targets'][$cid]['address']);
                            unset($final_results[$res['ray_id']]['targets'][$cid]['worktime']);
                            unset($final_results[$res['ray_id']]['targets'][$cid]['worktime_to']);
                        }
                    }
                } else {
                    // search by period

                    $query = 'SELECT c.id AS idx, c.id, c.type, ctypes_i18n.name AS type_label, IF(c.type="' . CUST_TYPE_PHARMACY . '", ci18n.name, CONCAT(ci18n.name, " ", ci18n.lastname)) as name, IF(c.type ="' . CUST_TYPE_PHARMACY . '", "pharmacy", IF(c.type ="' . CUST_TYPE_DENTIST . '", "dentist", "doctor")) as type_name, ccstm_ray.value AS ray_id' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                             // filter only visited customers within searched period
                             'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                             '  ON fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                             '    AND fir.customer=c.id' . "\n" .
                             '    AND fir.issue_date>=\'' . $filters['date_from'] . '\'' . "\n" .
                             '    AND fir.issue_date<=\'' . $filters['date_to'] . '\'' . "\n" .
                             '    AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON c.id=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_ray' . "\n" .
                             '  ON c.id=ccstm_ray.model_id AND ccstm_ray.var_id IN (\'' . $pharmacy_vars[PHARMACY_RAY_ID_VAR] . '\', \'' . $doctor_vars[DOCTOR_RAY_ID_VAR] . '\', \'' . $dentist_vars[DENTIST_RAY_ID_VAR] . '\')' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS ctypes_i18n' . "\n" .
                             '  ON c.type=ctypes_i18n.parent_id AND ctypes_i18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'WHERE c.type IN (\'' . CUST_TYPE_PHARMACY . '\', \'' . CUST_TYPE_DOCTOR . '\', \'' . CUST_TYPE_DENTIST . '\') AND c.active=1 AND c.deleted_by=0' . "\n" .
                             ($rays ? '  AND ccstm_ray.value IN (\'' . implode('\', \'', $rays) . '\')' . "\n" : '') .
                             'GROUP BY c.id';

                    $targets = $db->GetAssoc($query);
                    if ($targets) {

                        // build timeline
                        $timeline = array();
                        $timeline[$target_visit['from_date']] = $target_visit;

                        // get earliest target visit document
                        $query = 'SELECT d.id, dcstm_fd.value AS from_date, CONVERT(dcstm_mn.value, SIGNED INT) AS months_num' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_VISIT . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_fd.model_id AND dcstm_fd.var_id=\'' . $target_visit_vars[TARGET_VISIT_FROM_DATE_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_mn' . "\n" .
                                 '  ON dcstm_fd.model_id=dcstm_mn.model_id AND dcstm_mn.var_id=\'' . $target_visit_vars[TARGET_VISIT_MONTHS_NUM_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_fd.value!="" AND CONVERT(dcstm_mn.value, SIGNED INT)>0' . "\n" .
                                 '  AND dcstm_fd.value <= \'' . $filters['date_from'] . '\'' . "\n" .
                                 'ORDER BY dcstm_fd.value DESC, d.id DESC' . "\n" .
                                 'LIMIT 1';
                        $first_target_visit = $db->GetRow($query);
                        if ($first_target_visit && $first_target_visit['id'] != $target_visit['id']) {
                            $timeline[$first_target_visit['from_date']] = $first_target_visit;
                        }

                        $in_between_target_visits = array();
                        if (!$first_target_visit || $first_target_visit['id'] != $target_visit['id']) {
                            $query = 'SELECT d.id, dcstm_fd.value AS from_date, CONVERT(dcstm_mn.value, SIGNED INT) AS months_num' . "\n" .
                                     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                                     '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_VISIT . '\' AND d.status!="opened"' . "\n" .
                                     '    AND d.id=dcstm_fd.model_id AND dcstm_fd.var_id=\'' . $target_visit_vars[TARGET_VISIT_FROM_DATE_VAR] . '\'' . "\n" .
                                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_mn' . "\n" .
                                     '  ON dcstm_fd.model_id=dcstm_mn.model_id AND dcstm_mn.var_id=\'' . $target_visit_vars[TARGET_VISIT_MONTHS_NUM_VAR] . '\'' . "\n" .
                                     'WHERE dcstm_fd.value!="" AND CONVERT(dcstm_mn.value, SIGNED INT)>0' . "\n" .
                                     '  AND dcstm_fd.value > \'' . ($first_target_visit ? $first_target_visit['from_date'] : $filters['date_from']) . '\'' . "\n" .
                                     '  AND dcstm_fd.value < \'' . $target_visit['from_date'] . '\'' . "\n" .
                                     'ORDER BY dcstm_fd.value ASC, d.id DESC';
                            $in_between_target_visits = $db->GetAll($query);
                            foreach ($in_between_target_visits as $tv) {
                                if (!array_key_exists($tv['from_date'], $timeline)) {
                                    $timeline[$tv['from_date']] = $tv;
                                }
                            }
                        }
                        ksort($timeline);

                        $target_visit_data = array();
                        // get the start date of the visit period that 'date_from' is in
                        $start_of_first_period = '';
                        // calculate start and end of periods for selected period filter
                        $timeline = array_values($timeline);
                        $num_target_visits = count($timeline);

                        for ($i = 0; $i < $num_target_visits; $i++) {
                            $tv = &$timeline[$i];
                            $tv['period_from'] = $tv['from_date'];

                            // get first date of month for 'from_date' and calculate periods as if document always starts from there
                            $tv['next_period_from'] = date_add(
                                date_create(preg_replace('/^(.*)\d{2}$/', '${1}01', $tv['from_date'])),
                                new DateInterval('P' . $tv['months_num'] . 'M')
                            )->format('Y-m-d');

                            // count how many visit periods current document covers (within the searched dates)
                            $tv['period_count'] = 0;
                            if ($i > 0) {
                                $tv['period_count'] = 1;
                            } elseif ($tv['next_period_from'] > $filters['date_from']) {
                                $start_of_first_period = $tv['period_from'];
                                $tv['period_count'] = 1;
                            }

                            // figure out the last date that current target visit document is valid for
                            $tv_stop = isset($timeline[$i+1]) ?
                                       date_sub(date_create(preg_replace('/^(.*)\d{2}$/', '${1}01', $timeline[$i+1]['from_date'])), new DateInterval('P1D'))->format('Y-m-d') :
                                       $target_visit['period_to'];

                            while ($tv['next_period_from'] <= $tv_stop) {
                                $tv['period_from'] = $tv['next_period_from'];
                                $tv['next_period_from'] = date_add(date_create($tv['period_from']), new DateInterval('P' . $tv['months_num'] . 'M'))->format('Y-m-d');

                                if ($tv['next_period_from'] > $filters['date_from']) {
                                    $tv['period_count']++;
                                    if (!$i && !$start_of_first_period) {
                                        $start_of_first_period = $tv['period_from'];
                                    }
                                }
                            }
                            unset($tv['next_period_from']);

                            // get target visit grouping table
                            $tv_data = self::_getMeetingData($registry, $tv['id'], DOC_TYPE_TARGET_VISIT, $target_visit_vars);
                            foreach ($tv_data as $key => $num) {
                                if (array_key_exists($key, $target_visit_data)) {
                                    $target_visit_data[$key] += $num * $tv['period_count'];
                                } else {
                                    $target_visit_data[$key] = $num * $tv['period_count'];
                                }
                            }
                        }

                        // get target campaigns that overlap with period
                        $query = 'SELECT d.id' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_start' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_CAMPAIGN . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_start.model_id AND dcstm_start.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_START_PERIOD_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_finish' . "\n" .
                                 '  ON dcstm_start.model_id=dcstm_finish.model_id AND dcstm_finish.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_FINISH_PERIOD_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_start.value <= \'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND dcstm_finish.value >= \'' . $start_of_first_period . '\'';
                        $target_campaigns = $db->GetCol($query);

                        // add campaign data to visit data
                        foreach ($target_campaigns as $doc_id) {
                            $target_campaign_data = self::_getMeetingData($registry, $doc_id, DOC_TYPE_TARGET_CAMPAIGN, $target_campaign_vars);
                            foreach ($target_campaign_data as $key => $num) {
                                if (array_key_exists($key, $target_visit_data)) {
                                    $target_visit_data[$key] += $num;
                                } else {
                                    $target_visit_data[$key] = $num;
                                }
                            }
                        }

                        // prepare text for duration from start of first period until end of last period
                        $period_text = sprintf(self::$i18n->translate('reports_period_text'),
                                               General::strftime('%d.%m.%Y', $filters['date_from']),
                                               General::strftime('%d.%m.%Y', $filters['date_to']),
                                               General::strftime('%d.%m.%Y', $start_of_first_period),
                                               General::strftime('%d.%m.%Y', $target_visit['period_to']));

                        $target_ids = array_keys($targets);

                        // last visit ever
                        $query = 'SELECT fir.customer, MAX(fir.issue_date) AS last_visited' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'GROUP BY fir.customer';
                        $last_visited = $db->GetAssoc($query);

                        // done visits within all periods overlapping with period filters
                        $query = 'SELECT fir.customer, fir.issue_date' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.issue_date>=\'' . $start_of_first_period . '\'' . "\n" .
                                 '  AND fir.issue_date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'ORDER BY fir.issue_date ASC';
                        $done_visits_list = $db->GetAll($query);
                        $done_visits = array();
                        foreach ($done_visits_list as $dvl) {
                            if (!isset($done_visits[$dvl['customer']])) {
                                $done_visits[$dvl['customer']] = array();
                            }
                            $done_visits[$dvl['customer']][] = $dvl['issue_date'];
                        }

                        // planned visits for date from tomorrow until end of current period
                        $planned_visits = array();
                        $tomorrow = date_add(new DateTime(), new DateInterval('P1D'))->format('Y-m-d');
                        if ($tomorrow <= $target_visit['period_to']) {
                            $query = 'SELECT gt2.article_id, COUNT(d.id)' . "\n" .
                                     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                     '  ON d.type=\'' . DOC_TYPE_DAILY_SCHEDULE . '\' AND d.active=1 AND d.deleted_by=0' . "\n" .
                                     '    AND d.id=gt2.model_id AND gt2.model=\'Document\'' . "\n" .
                                     'WHERE d.date>=\'' . $tomorrow . '\' AND d.date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                     '  AND gt2.article_id IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                     'GROUP BY gt2.article_id';
                            $planned_visits = $db->GetAssoc($query);
                        }

                        $query = 'SELECT ct.model_id, MIN(ct.tag_id)' . "\n" .
                                 'FROM ' . DB_TABLE_TAGS_MODELS . ' AS ct' . "\n" .
                                 'WHERE ct.model=\'Customer\' AND ct.model_id IN (\'' . implode('\', \'', $target_ids) . '\') AND ct.tag_id IN (' . CLASS_TAGS . ')' . "\n" .
                                 'GROUP BY ct.model_id';
                        $tags = $db->GetAssoc($query);

                        // group results by ray
                        foreach ($targets as $cid => $res) {

                            if (!isset($final_results[$res['ray_id']])) {
                                $final_results[$res['ray_id']] = array(
                                    'targets' => array()
                                );
                            }

                            $res['tag'] = array_key_exists($cid, $tags) ? $tags[$cid] : '';
                            $res['class'] = array_key_exists($res['tag'], $class_tags) ? $class_tags[$res['tag']] : '-';

                            $key = $res['type'] . '_' . $res['tag'];
                            $res['target_visits'] = array_key_exists($key, $target_visit_data) ? $target_visit_data[$key] : 0;

                            $res['last_visited'] = array_key_exists($cid, $last_visited) ? $last_visited[$cid] : '';

                            $res['done_visits'] = array_key_exists($cid, $done_visits) ? count($done_visits[$cid]) : 0;

                            $res['planned_visits'] = array_key_exists($cid, $planned_visits) ? $planned_visits[$cid] : 0;

                            // dummy data just to comply with format of results when searching by date
                            $res['locations'] = array(array());

                            $final_results[$res['ray_id']]['targets'][$cid] = $res;

                            if (isset($done_visits[$cid])) {
                                foreach ($done_visits[$cid] as $vis_dat) {
                                    if (isset($summary_data['visits_by_date'][$vis_dat])) {
                                        $summary_data['visits_by_date'][$vis_dat]++;
                                        $summary_data['total_visits']++;
                                    }
                                }
                            }
                        }
                    }
                }

                if ($final_results) {
                    // ray name and agent name
                    $query = 'SELECT n.id, IF(ni18n.name IS NOT NULL, ni18n.name, \'\') AS ray_name,' . "\n" .
                             'CONCAT(ci18n.name, " ", ci18n.lastname) AS agent_name' . "\n" .
                             'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                             '  ON n.id=ni18n.parent_id AND ni18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_region' . "\n" .
                             '  ON n.id=ncstm_region.model_id AND ncstm_region.var_id=\'' . $entities['ray']['vars'][RAY_REGION_ID_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_agent' . "\n" .
                             '  ON n.id=ncstm_agent.model_id AND ncstm_agent.var_id=\'' . $entities['ray']['vars'][RAY_AGENT_ID_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON ncstm_agent.value=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'WHERE n.id IN (\'' . implode('\', \'', array_keys($final_results)) . '\')';
                    $ray_data = $db->GetAssoc($query);

                    $pharmacies_list = array();
                    $medics_list = array();
                    $dentists_list = array();
                    foreach ($final_results as $id => $ray) {
                        $ray_name = $agent_name = '';
                        if (array_key_exists($id, $ray_data)) {
                            $ray_name = $ray_data[$id]['ray_name'];
                            $agent_name = $ray_data[$id]['agent_name'];
                        }
                        $final_results[$id]['name'] = $ray_name;
                        $final_results[$id]['num'] = $ray_name ? preg_replace('/^.*\s(\d+).*/u', '$1', $ray_name) : '';
                        $final_results[$id]['agent_name'] = $agent_name;
                        $final_results[$id]['caption'] =
                            $filters['search_by'] == 1 ?
                            sprintf(self::$i18n->translate('reports_date_caption'),
                                General::strftime('%A, %d.%m.%Y', $filters['date']),
                                $ray_name ?: '-', $agent_name ?: '-') :
                            sprintf(self::$i18n->translate('reports_period_caption'),
                                General::strftime('%d.%m.%Y', $filters['date_from']),
                                General::strftime('%d.%m.%Y', $filters['date_to']),
                                $ray_name ?: '-', $agent_name ?: '-');
                        uasort($final_results[$id]['targets'], function ($a, $b) { return $a['name'] > $b['name'] ? 1 : -1; } );

                        foreach ($final_results[$id]['targets'] as $trg) {
                            if ($trg['type_name'] == 'pharmacy') {
                                if (!in_array($trg['id'], $pharmacies_list)) {
                                    $pharmacies_list[] = $trg['id'];
                                }
                            } elseif ($trg['type_name'] == 'doctor') {
                                if (!in_array($trg['id'], $medics_list)) {
                                    $medics_list[] = $trg['id'];
                                }
                            } elseif ($trg['type_name'] == 'dentist') {
                                if (!in_array($trg['id'], $dentists_list)) {
                                    $dentists_list[] = $trg['id'];
                                }
                            }
                        }
                    }

                    $summary_data['total_doctors'] = count($medics_list);
                    $summary_data['total_dentists'] = count($dentists_list);
                    $summary_data['total_pharmacies'] = count($pharmacies_list);

                    // sort results by agent name first and then by the number part of ray name
                    uasort($final_results, function ($a, $b) {
                        if ($a['agent_name'] > $b['agent_name']) {
                            return 1;
                        } elseif ($a['agent_name'] < $b['agent_name']) {
                            return -1;
                        } else {
                            return $a['num'] > $b['num'] ? 1 : -1;
                        }
                    } );
                }
            }

            // prepare all the additional options for report
            $final_results['additional_options'] = array();

            // hide export button
            $final_results['additional_options']['dont_show_export_button'] = true;
            if (!$errors) {
                $final_results['additional_options']['summary_table'] = $summary_data;
            }
            // explanation text for span of period that data is searched within
            if (!empty($period_text)) {
                $final_results['additional_options']['period_text'] = $period_text;
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        /**
         * Gets rays (of specified sales rep or all) as dropdown optgroups or gets just ids
         *
         * @param object $registry - the main registry
         * @param int $rep - user id to get rays in regions of specified sales rep, 0 - to get all rays
         * @param bool $get_options - get as dropdown optgroups or get just ids
         * @return array - found rays
         */
        public static function getRays(&$registry, $rep = 0, $get_options = true) {

            $lang = $registry['lang'];

            $sql_add_vars = 'SELECT name, id ' . "\n" .
                            'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                            'WHERE model="Nomenclature"' . "\n" .
                            '  AND (model_type="' . NOM_TYPE_RAY . '" AND (name="' . RAY_AGENT_ID_VAR . '" OR `name`="' . RAY_REGION_ID_VAR . '"))';
            $add_vars = $registry['db']->GetAssoc($sql_add_vars);

            $query = 'SELECT ' .
                     ($get_options ?
                     'IF(ni18n.name IS NOT NULL, ni18n.name, \'\') AS label, n.id AS option_value, ' . "\n" .
                     '  n.active AS active_option, n2i18n.name AS region_name, ' . "\n" .
                     '  CONCAT(\'user_\', ncstm_agent.value) AS class_name ' :
                     'n.id') . "\n" .
                     'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                     '  ON n.id=ni18n.parent_id AND ni18n.lang=\'' . $lang . '\'' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_region' . "\n" .
                     '  ON n.type=\'' . NOM_TYPE_RAY . '\' AND n.active=1' . "\n" .
                     '    AND n.id=ncstm_region.model_id AND ncstm_region.var_id=\'' . $add_vars[RAY_REGION_ID_VAR] . '\'' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS n2i18n' . "\n" .
                     '  ON n2i18n.parent_id=ncstm_region.value AND n2i18n.lang=\'' . $lang . '\'' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_agent' . "\n" .
                     '  ON n.id=ncstm_agent.model_id AND ncstm_agent.var_id=\'' . $add_vars[RAY_AGENT_ID_VAR] . '\'' . "\n" .
                     ($rep > 0 ? '    AND ncstm_agent.value=\'' . $rep . '\'' . "\n" : '') .
                     ($get_options ?
                     'ORDER BY ni18n.name ASC, REPLACE(label, \'Лъч \', \'\') ASC' :
                     '');

            if ($get_options) {
                $rays_options = $registry['db']->GetAll($query);

                $rays = array();
                foreach ($rays_options as $option) {
                    $region_name = $option['region_name'];
                    unset($option['region_name']);
                    if (!isset($rays[$region_name])) {
                        $rays[$region_name] = array();
                    }
                    $rays[$region_name][] = $option;
                }
            } else {
                $rays = $registry['db']->GetCol($query);
            }

            return $rays;
        }

        /**
         * Get grouping table data of target visit or target campaign document
         *
         * @param object $registry - the main registry
         * @param int $doc_id - document id
         * @param int $doc_type - document type
         * @param array $add_vars - pass data for additional vars if it is already available
         * @return array - result data
         */
        private static function _getMeetingData(&$registry, $doc_id, $doc_type, &$add_vars = array()) {
            if (!$doc_id || !in_array($doc_type, array(DOC_TYPE_TARGET_VISIT, DOC_TYPE_TARGET_CAMPAIGN))) {
                return array();
            }

            $prefix = ($doc_type == DOC_TYPE_TARGET_VISIT ? 'TARGET_VISIT' : 'TARGET_CAMPAIGN');
            $vars = array (
                'type' => constant($prefix . '_MEETINGS_CTYPE_VAR'),
                'tag' => constant($prefix . '_MEETINGS_CTAG_VAR'),
                'num' => constant($prefix . '_MEETINGS_NUM_VAR')
            );
            // get additional vars if not passed as param
            if (!$add_vars) {
                $sql_add_vars =
                    'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                    'WHERE model=\'Document\' AND model_type=\'' . $doc_type . '\' AND name IN (\'' . implode('\', \'', $vars) . '\')';
                $add_vars = $registry['db']->GetAssoc($sql_add_vars);
            }
            foreach ($vars as $alias => $var_name) {
                $vars[$alias] = array_key_exists($var_name, $add_vars) ? $add_vars[$var_name] : '0';
            }

            $query = 'SELECT CONCAT(dcstm_type.value, \'_\', dcstm_tag.value) AS idx, dcstm_num.value AS num' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_type' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_tag' . "\n" .
                     '  ON dcstm_type.model_id=\'' . $doc_id . '\'' . "\n" .
                     '    AND dcstm_type.model_id=dcstm_tag.model_id AND dcstm_type.num=dcstm_tag.num' . "\n" .
                     '    AND dcstm_type.var_id=\'' . $vars['type'] . '\' AND dcstm_tag.var_id=\'' . $vars['tag'] . '\'' . "\n" .
                     '    AND dcstm_type.value!=\'\' AND dcstm_tag.value!=\'\'' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_num' . "\n" .
                     '  ON dcstm_tag.model_id=dcstm_num.model_id AND dcstm_tag.num=dcstm_num.num' . "\n" .
                     '    AND dcstm_num.var_id=\'' . $vars['num'] . '\'' . "\n" .
                     'ORDER BY dcstm_type.num ASC';
            $meeting_rows = $registry['db']->GetAll($query);

            $meeting_data = array();
            // iterate all rows to get last row for each key
            foreach ($meeting_rows as $row) {
                $meeting_data[$row['idx']] = $row['num'];
            }

            return $meeting_data;
        }
    }

?>
