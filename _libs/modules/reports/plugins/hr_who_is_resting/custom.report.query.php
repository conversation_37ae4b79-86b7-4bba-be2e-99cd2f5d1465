<?php
    Class Hr_Who_Is_Resting Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            $results = array();
            $total_rowspan = 6;

            $error = false;
            $settings = array(
                'document_leave_type',
                'free_days_type',
                'free_days_year',
                'free_days_start_date',
                'free_days_end_date',
                'free_days_replacement',
                'days_off_substatus_approved',
                'days_off_substatus_disapproved',
            );

            // check settings
            foreach ($settings as $setting) {
                if (!defined(strtoupper($setting)) || !constant(strtoupper($setting))) {
                    $error = true;
                    break;
                }
            }

            if ($error) {
                $registry['messages']->setError($registry['translater']->translate('reports_error_missing_settings'));
                return self::returnResults($registry, $filters, $results);
            }

            // check required filters
            if (empty($filters['period_from']) || empty($filters['period_to']) || $filters['period_from']>$filters['period_to']) {
                $registry['messages']->setError($registry['translater']->translate('reports_error_complete_required_filters'));
                return self::returnResults($registry, $filters, $results);
            }

            if (!empty($filters['department'])) {
                $related_departments_list = Departments::getTreeDescendantsIds($registry, $filters['department']);
            } else {
                $related_departments_list = self::getResponsibleDepartmentsList($registry);
            }

            $sql = 'SELECT c.id FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                   'WHERE c.department IN ("' . implode('","', $related_departments_list) . '") AND c.active=1 AND c.deleted_by=0 AND c.type="' . PH_CUSTOMER_EMPLOYEE . '"';
            $employee_users = $registry['db']->GetCol($sql);

            if (!empty($filters['employee'])) {
                $employee_users = array_intersect($employee_users, array($filters['employee']));
            }

            if (empty($employee_users)) {
                // no results to display
                return self::returnResults($registry, $filters, $results);
            }

            // get the names of the employees
            $sql = 'SELECT ci18n.parent_id as idx, ci18n.parent_id as id, CONCAT(ci18n.name, " ", ci18n.lastname) as name, ' . "\n" .
                   '  c.department, dep.name as department_name' . "\n" .
                   'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n' . "\n" .
                   '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS dep' . "\n" .
                   '  ON (dep.parent_id=c.department AND dep.lang="' . $registry['lang'] . '")' . "\n" .
                   'WHERE c.type="' . PH_CUSTOMER_EMPLOYEE . '" AND c.active=1 AND c.deleted_by=0' . (!empty($employee_users) ? ' AND c.id IN ("' . implode('","', $employee_users) . '")' : '') . "\n" .
                   'ORDER BY CONCAT(ci18n.name, " ", ci18n.lastname)';
            $employees_data = $registry['db']->GetAssoc($sql);

            if (empty($employees_data)) {
                // no results to display
                return self::returnResults($registry, $filters, $results);
            }

            // set periods
            $periods = array();
            $nonworking_days = array();
            if (defined(strtoupper('INCLUDE_ONLY_WORKING_DAYS')) && constant('INCLUDE_ONLY_WORKING_DAYS')) {
                $country_code = Calendars_Calendar::getCountryCode($registry);
                $query = 'SELECT `date` FROM ' . DB_TABLE_COUNTRY_NONWORKDAYS . "\n" .
                         '  WHERE `date`>="' . $filters['period_from'] . '" AND `date`<="' . $filters['period_to'] . '" AND country="' . $country_code . '"';
                $nonworking_days = $registry['db']->getCol($query);
            }

            $current_period = new DateTime($filters['period_from']);
            while ($current_period->format('Y-m-d') <= $filters['period_to']) {
                if (!in_array($current_period->format('Y-m-d'), $nonworking_days)) {
                    $week_idx = $current_period->format('Y_W');
                    if (!isset($periods[$week_idx])) {
                        $periods[$week_idx] = array(
                            'label' => sprintf('%s %d / %d %s', $registry['translater']->translate('week'), $current_period->format('W'), $current_period->format('Y'), $registry['translater']->translate('reports_week_shorten')),
                            'days'  => array()
                        );
                    }
                    $periods[$week_idx]['days'][] = $current_period->format('Y-m-d');
                    $total_rowspan++;
                }
                $current_period->add(new DateInterval('P1D'));
            }

            foreach ($employees_data as $key_res => $res) {
                if (!isset($results[$res['department']])) {
                    $results[$res['department']] = array(
                        'id'        => $res['department'],
                        'name'      => $res['department_name'],
                        'employees' => array(),
                        'rowspan'   => 0
                    );
                }

                $results[$res['department']]['employees'][$res['id']] = array(
                    'id'                => $res['id'],
                    'name'              => $res['name'],
                    'total_days_off'    => 0,
                    'total_sickness'    => 0,
                    'total_home_office' => 0,
                    'replacement'       => array(),
                    'days'              => array(),
                );
                $results[$res['department']]['rowspan']++;

                foreach ($periods as $per_days) {
                    $results[$res['department']]['employees'][$res['id']]['days'] = $results[$res['department']]['employees'][$res['id']]['days'] + array_fill_keys(array_values($per_days['days']), array());
                }
            }

            // get additional vars for the document
            $add_vars = array(FREE_DAYS_TYPE, FREE_DAYS_YEAR, FREE_DAYS_START_DATE, FREE_DAYS_END_DATE, FREE_DAYS_REPLACEMENT);
            $sickness_vars = array(SICKNESS_REST_START_DATE, SICKNESS_REST_END_DATE, SICKNESS_REST_COUNT_DAYS);
            $home_office_vars = array(HOME_OFFICE_DOCUMENT_START_DATE, HOME_OFFICE_DOCUMENT_END_DATE);
            if (SICKNESS_REST_TYPE_MATERNITY_FIELD) {
                $sickness_vars[] = SICKNESS_REST_TYPE_MATERNITY_FIELD;
            }

            // include the home office documents
            if (HOME_OFFICE_DOCUMENT_ID) {
                $total_rowspan += 1;
                $sql_for_add_vars = 'SELECT fm.name, fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . HOME_OFFICE_DOCUMENT_ID . ' AND fm.name IN ("' . implode('","', $home_office_vars) . '")';
                $home_office_vars = $registry['db']->GetAssoc($sql_for_add_vars);

                // PREPARE HOME OFFICE DOCUMENTS
                $sql = array();
                $sql['select'] = 'SELECT d.id AS id, d_cstm_home_office_from.value AS date_from, d_cstm_home_office_to.value AS date_to, d.customer as employee, c.department' . "\n";

                $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                               '  ON (c.id=d.customer)' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_home_office_from' . "\n" .
                               '  ON (d_cstm_home_office_from.model_id=d.id AND d_cstm_home_office_from.var_id="' . (isset($home_office_vars[HOME_OFFICE_DOCUMENT_START_DATE]) ? $home_office_vars[HOME_OFFICE_DOCUMENT_START_DATE] : '') . '" AND (d_cstm_home_office_from.lang="' . $registry['lang'] . '" OR d_cstm_home_office_from.lang=""))' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_home_office_to' . "\n" .
                               '  ON (d_cstm_home_office_to.model_id=d.id AND d_cstm_home_office_to.var_id="' . (isset($home_office_vars[HOME_OFFICE_DOCUMENT_END_DATE]) ? $home_office_vars[HOME_OFFICE_DOCUMENT_END_DATE] : '') . '" AND (d_cstm_home_office_to.lang="' . $registry['lang'] . '" OR d_cstm_home_office_to.lang=""))' . "\n";
                $sql['where'] = 'WHERE d.active=1 AND d.deleted_by=0 AND d.type="' . HOME_OFFICE_DOCUMENT_ID . '" AND d.customer IN ("' . implode('","', array_keys($employees_data)) . '") AND DATE_FORMAT(d_cstm_home_office_from.value, "%Y-%m-%d")<="' . $filters['period_to'] . '" AND DATE_FORMAT(d_cstm_home_office_to.value, "%Y-%m-%d")>="' . $filters['period_from'] . '"';
                if (HOME_OFFICE_APPROVE_STATUS) {
                    $sql['where'] .= ' AND d.substatus="' . HOME_OFFICE_APPROVE_STATUS . '"';
                }

                //search basic details with current lang parameters
                $query = implode("\n", $sql);
                $home_office_list = $registry['db']->GetAll($query);

                foreach ($home_office_list as $doc_list) {
                    if (isset($results[$doc_list['department']]['employees'][$doc_list['employee']])) {
                        // complete the days in the days list
                        $current_period = new DateTime($doc_list['date_from']);
                        while ($current_period->format('Y-m-d') <= $doc_list['date_to']) {
                            if (isset($results[$doc_list['department']]['employees'][$doc_list['employee']]['days'][$current_period->format('Y-m-d')])) {
                                $results[$doc_list['department']]['employees'][$doc_list['employee']]['days'][$current_period->format('Y-m-d')] = array(
                                    'id'              => $doc_list['id'],
                                    'status'          => 'home_office',
                                    'additional_info' => ''
                                );
                            }
                            $current_period->add(new DateInterval('P1D'));
                        }
                    }
                }
            }


            $sql_for_add_vars = 'SELECT fm.name, fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_LEAVE_TYPE . ' AND fm.name IN ("' . implode('","', $add_vars) . '")';
            $var_ids = $registry['db']->GetAssoc($sql_for_add_vars);

            //sql to take the needed documents
            $sql = array();
            $sql['select'] =  'SELECT d.id AS id, d.customer as employee, CONCAT(ci18n.name, " ", ci18n.lastname) as employee_name, ' . "\n" .
                              '  c.department, DATE_FORMAT(d_cstm_free_days_start_date.value, "%Y-%m-%d") AS start_date,' . "\n" .
                              '  DATE_FORMAT(d_cstm_free_days_finish_date.value, "%Y-%m-%d") AS finish_date, d_cstm_free_days_year.value as year,  ' . "\n" .
                              '  d_cstm_deputy.value AS deputy, CONCAT(ci18n_1.name, " ", ci18n_1.lastname) as deputy_name, ' . "\n" .
                              '  d_cstm_free_days_type.value AS days_type, d_cstm_free_days_year.value as year, d.substatus ' . "\n";

            $sql['from']   =   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                               '  ON (c.id=d.customer)' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                               '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_start_date' . "\n" .
                               '  ON (d_cstm_free_days_start_date.model_id=d.id AND d_cstm_free_days_start_date.var_id="' . (isset($var_ids[FREE_DAYS_START_DATE]) ? $var_ids[FREE_DAYS_START_DATE] : '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_finish_date' . "\n" .
                               '  ON (d_cstm_free_days_finish_date.model_id=d.id AND d_cstm_free_days_finish_date.var_id="' . (isset($var_ids[FREE_DAYS_END_DATE]) ? $var_ids[FREE_DAYS_END_DATE] : '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_type' . "\n" .
                               '  ON (d_cstm_free_days_type.model_id=d.id AND d_cstm_free_days_type.var_id="' . (isset($var_ids[FREE_DAYS_TYPE]) ? $var_ids[FREE_DAYS_TYPE] : '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_year' . "\n" .
                               '  ON (d_cstm_free_days_year.model_id=d.id AND d_cstm_free_days_year.var_id="' . (isset($var_ids[FREE_DAYS_YEAR]) ? $var_ids[FREE_DAYS_YEAR] : '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_deputy' . "\n" .
                               '  ON (d_cstm_deputy.model_id=d.id AND d_cstm_deputy.var_id="' . (isset($var_ids[FREE_DAYS_REPLACEMENT]) ? $var_ids[FREE_DAYS_REPLACEMENT] : '') . '")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' ci18n_1' . "\n" .
                               '  ON (ci18n_1.parent_id=d_cstm_deputy.value AND ci18n_1.lang="' . $registry['lang'] . '")' . "\n";

            $where = array();
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.type="' . DOCUMENT_LEAVE_TYPE . '"';
            $where[] = 'd.customer IN ("' . implode('","', array_keys($employees_data)) . '")';
            $where[] = 'DATE_FORMAT(d_cstm_free_days_start_date.value, "%Y-%m-%d") <= "' . $filters['period_to'] . '"';
            $where[] = 'DATE_FORMAT(d_cstm_free_days_finish_date.value, "%Y-%m-%d") >= "' . $filters['period_from'] . '"';
            if (!empty($filters['status'])) {
                if ($filters['status'] == 'approved') {
                    $where[] = 'd.substatus="' . DAYS_OFF_SUBSTATUS_APPROVED . '"';
                } elseif ($filters['status'] == 'disapproved') {
                    $where[] = 'd.substatus="' . DAYS_OFF_SUBSTATUS_DISAPPROVED . '"';
                } elseif ($filters['status'] == 'requested') {
                    $where[] = sprintf('d.substatus NOT IN ("%d", "%d")', DAYS_OFF_SUBSTATUS_APPROVED, DAYS_OFF_SUBSTATUS_DISAPPROVED);
                    $where[] = 'DATE_FORMAT(d_cstm_free_days_start_date.value, "%Y-%m-%d")>"' . date('Y-m-d') . '"';
                }
            } else {
                $ors = array();
                $ors[] = 'd.substatus="' . DAYS_OFF_SUBSTATUS_APPROVED . '"';
                $ors[] = sprintf('(d.substatus NOT IN ("%d", "%d") AND DATE_FORMAT(d_cstm_free_days_start_date.value, "%%Y-%%m-%%d")>"%s")', DAYS_OFF_SUBSTATUS_APPROVED, DAYS_OFF_SUBSTATUS_DISAPPROVED, date('Y-m-d'));
                $where[] = '(' . implode(' OR ', $ors) . ')';
                unset($ors);
            }
            $sql['where'] = 'WHERE ' . implode(' AND ', $where);

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $documents_list = $registry['db']->GetAll($query);

            foreach ($documents_list as $doc_list) {
                if (isset($results[$doc_list['department']]['employees'][$doc_list['employee']])) {
                    $results[$doc_list['department']]['employees'][$doc_list['employee']]['replacement'][] = sprintf('%s (%s - %s)',
                        $doc_list['deputy_name'],
                        General::strftime('%d.%m', strtotime($doc_list['start_date'])),
                        General::strftime('%d.%m', strtotime($doc_list['finish_date']))
                    );

                    // define current status
                    $current_document_status = '';
                    if ($doc_list['substatus'] == DAYS_OFF_SUBSTATUS_APPROVED) {
                        $current_document_status = 'approved';
                    } elseif ($doc_list['substatus'] == DAYS_OFF_SUBSTATUS_DISAPPROVED) {
                        $current_document_status = 'disapproved';
                    } else {
                        $current_document_status = 'requested';
                    }

                    // complete the days in the days list
                    $current_period = new DateTime($doc_list['start_date']);
                    while ($current_period->format('Y-m-d') <= $doc_list['finish_date']) {
                        if (isset($results[$doc_list['department']]['employees'][$doc_list['employee']]['days'][$current_period->format('Y-m-d')])) {
                            $results[$doc_list['department']]['employees'][$doc_list['employee']]['days'][$current_period->format('Y-m-d')] = array(
                                'id'     => $doc_list['id'],
                                'status' => $current_document_status
                            );
                        }
                        $current_period->add(new DateInterval('P1D'));
                    }
                }
            }


            if (SHOW_SICKNESS_DAYS) {
                $sql_for_add_vars = 'SELECT fm.name, fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . SICKNESS_REST_DOCUMENT_ID . ' AND fm.name IN ("' . implode('","', $sickness_vars) . '")';
                $sickness_vars = $registry['db']->GetAssoc($sql_for_add_vars);

                // PREPARE SICKNESS REST DOCUMENTS
                $sql = array();
                $sql['select'] = 'SELECT d.id AS id, d_cstm_sickness_date_from.value AS date_from, d_cstm_sickness_date_to.value AS date_to, ' . "\n" .
                                 '  d_cstm_sickness_count_days.value AS count_days, c.department, d.employee' . "\n";

                $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                               '  ON (c.id=d.employee)' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sickness_date_from' . "\n" .
                               '  ON (d_cstm_sickness_date_from.model_id=d.id AND d_cstm_sickness_date_from.var_id="' . (isset($sickness_vars[SICKNESS_REST_START_DATE]) ? $sickness_vars[SICKNESS_REST_START_DATE] : '') . '" AND (d_cstm_sickness_date_from.lang="' . $registry['lang'] . '" OR d_cstm_sickness_date_from.lang=""))' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sickness_date_to' . "\n" .
                               '  ON (d_cstm_sickness_date_to.model_id=d.id AND d_cstm_sickness_date_to.var_id="' . (isset($sickness_vars[SICKNESS_REST_END_DATE]) ? $sickness_vars[SICKNESS_REST_END_DATE] : '') . '" AND (d_cstm_sickness_date_to.lang="' . $registry['lang'] . '" OR d_cstm_sickness_date_to.lang=""))' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sickness_count_days' . "\n" .
                               '  ON (d_cstm_sickness_count_days.model_id=d.id AND d_cstm_sickness_count_days.var_id="' . (isset($sickness_vars[SICKNESS_REST_COUNT_DAYS]) ? $sickness_vars[SICKNESS_REST_COUNT_DAYS] : '') . '" AND (d_cstm_sickness_count_days.lang="' . $registry['lang'] . '" OR d_cstm_sickness_count_days.lang=""))' . "\n";
                if (SICKNESS_REST_TYPE_MATERNITY_FIELD) {
                    $sql['select'] .= ', d_cstm_maternity.value as maternity' . "\n";
                    $sql['from']   .= 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_maternity' . "\n" .
                                      '  ON (d_cstm_maternity.model_id=d.id AND d_cstm_maternity.var_id="' . (isset($sickness_vars[SICKNESS_REST_TYPE_MATERNITY_FIELD]) ? $sickness_vars[SICKNESS_REST_TYPE_MATERNITY_FIELD] : '') . '" AND (d_cstm_maternity.lang="' . $registry['lang'] . '" OR d_cstm_maternity.lang=""))' . "\n";
                }
                $sql['where'] = 'WHERE d.active=1 AND d.deleted_by=0 AND d.type="' . SICKNESS_REST_DOCUMENT_ID . '" AND d.employee IN ("' . implode('","', array_keys($employees_data)) . '") AND DATE_FORMAT(d_cstm_sickness_date_from.value, "%Y-%m-%d")<="' . $filters['period_to'] . '" AND DATE_FORMAT(d_cstm_sickness_date_to.value, "%Y-%m-%d")>="' . $filters['period_from'] . '"';
                if (SICKNESS_REST_APPROVE_STATUS) {
                    $sql['where'] .= ' AND d.substatus="' . SICKNESS_REST_APPROVE_STATUS . '"';
                }

                //search basic details with current lang parameters
                $query = implode("\n", $sql);
                $sickness_days_list = $registry['db']->GetAll($query);

                foreach ($sickness_days_list as $doc_list) {
                    if (isset($results[$doc_list['department']]['employees'][$doc_list['employee']])) {
                        // complete the days in the days list
                        $current_period = new DateTime($doc_list['date_from']);
                        while ($current_period->format('Y-m-d') <= $doc_list['date_to']) {
                            if (isset($results[$doc_list['department']]['employees'][$doc_list['employee']]['days'][$current_period->format('Y-m-d')])) {
                                $results[$doc_list['department']]['employees'][$doc_list['employee']]['days'][$current_period->format('Y-m-d')] = array(
                                    'id'              => $doc_list['id'],
                                    'status'          => 'sickness',
                                    'additional_info' => ((isset($doc_list['maternity']) && $doc_list['maternity'] == SICKNESS_REST_TYPE_MATERNITY_OPTION_YES) ? $registry['translater']->translate('reports_maternity') : '')
                                );
                            }
                            $current_period->add(new DateInterval('P1D'));
                        }
                    }
                }
            }

            unset($employees_data);

            // go through all the results and calculate the totals
            foreach ($results as $dep_id => $department_info) {
                foreach ($department_info['employees'] as $emp_id => $employee) {
                    $results[$dep_id]['employees'][$emp_id]['total_days_off'] = count(array_keys(array_filter(array_column($employee['days'], 'status')), "approved"));
                    $results[$dep_id]['employees'][$emp_id]['total_sickness'] = count(array_keys(array_filter(array_column($employee['days'], 'status')), "sickness"));
                    $results[$dep_id]['employees'][$emp_id]['total_home_office'] = count(array_keys(array_filter(array_column($employee['days'], 'status')), "home_office"));
                }
            }

            $results['additional_options']['periods'] = $periods;
            $results['additional_options']['total_rowspan'] = $total_rowspan;

            return self::returnResults($registry, $filters, $results);
        }

        /**
         * Function to prepare the results for returning
         */
        public static function returnResults(&$registry, $filters = array(), $results = array()) {
            // query if paginate is needed
            if (!empty($filters['paginate'])) {
                $results = array($results, 0);
            } else {
                //no pagination required return only the models
                $results = $results;
            }

            return $results;
        }

        /**
         * Function to get the departments which the current user is responsible for
         */
        public static function getResponsibleDepartmentsList(&$registry) {
            $departments_included = array();
            $powerusers = array();
            if (defined(strtoupper('POWERUSERS')) && constant('POWERUSERS')) {
                $powerusers = preg_split('/\s*,\s*/', POWERUSERS);
                $powerusers = array_filter($powerusers);
            }
            if (defined(strtoupper('POWERROLES')) && constant('POWERROLES')) {
                $powerroles = preg_split('/\s*,\s*/', POWERROLES);
                $powerroles = array_filter($powerroles);
                $sql = 'SELECT `id`' . "\n" .
                       'FROM ' . DB_TABLE_USERS . "\n" .
                       'WHERE `active`=1 AND `deleted_by`=0 AND `role` IN ("' . implode('","', $powerroles) . '")' . "\n";
                $roles_users = $registry['db']->GetCol($sql);
                $powerusers = array_merge($powerusers, $roles_users);
                $powerusers = array_unique($powerusers);
            }

            if (!in_array($registry['currentUser']->get('id'), $powerusers)
                //&& $registry['currentUser']->get('role') != PH_ROLES_ADMIN
                ) {
                // get the current user's employee
                $responsible_for_departments = array();
                $sql = 'SELECT department FROM ' . DB_TABLE_CUSTOMERS . ' WHERE id=' . $registry['currentUser']->get('employee');
                $responsible_for_departments[] = $registry['db']->getOne($sql);

                if (defined('EMPLOYEE_RESPONSIBLE_FOR_DEPARTMENT') && !empty(EMPLOYEE_RESPONSIBLE_FOR_DEPARTMENT)) {
                    $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Customer" AND `name`="' . EMPLOYEE_RESPONSIBLE_FOR_DEPARTMENT . '" AND model_type="' . PH_CUSTOMER_EMPLOYEE . '"';
                    $var_responsible_for_id = $registry['db']->GetOne($sql);

                    if (!empty($var_responsible_for_id)) {
                        $sql = 'SELECT cstm.value ' . "\n" .
                               'FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' AS cstm' . "\n" .
                               'WHERE cstm.model_id="' . $registry['currentUser']->get('employee') . '" AND cstm.var_id="' . $var_responsible_for_id . '"' . "\n";
                        $responsible_for_departments = array_merge($responsible_for_departments, $registry['db']->GetCol($sql));
                    }
                }
                $responsible_for_departments = array_filter($responsible_for_departments);
                if (!empty($responsible_for_departments)) {
                    $departments_list = Departments::getTreeDescendants($registry, array('where' => array('d1.id IN (' . implode(',', $responsible_for_departments) . ')')));
                    foreach ($departments_list as $dep) {
                        $departments_included[] = $dep->get('id');
                    }
                    unset($departments_list);
                }
            } else {
                $departments_included = Departments::getTreeDescendantsIds($registry, 1);
            }

            return array_filter($departments_included);
        }
    }
?>
