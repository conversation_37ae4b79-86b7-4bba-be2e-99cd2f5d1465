var reportAddressReport = {
    export: function () {
        /**
         * Validate
         */
        if (!$('company').value || !$('month').value || !$('year').value) {
            alert('Моля, попълнете задължителните филтри!');
            return false;
        }

        /**
         * Export
         */
        Cookie.set('custom_export', 1, 1);

        Effect.Center('loading');
        Effect.Appear('loading');

        this.interval = window.setInterval(
            function () {
                if (!Cookie.get('custom_export')) {
                    Effect.Fade('loading');
                    clearInterval(reportAddressReport.interval);
                }
            },
            1000
        );

        window.location = env.base_url + '?' + Form.serialize($('reports_generated'));
    },

    exportDrillDown: function (month, entity) {
        /**
         * Export
         */
        Cookie.set('custom_export_address_report', 1, 1);

        Effect.Center('loading');
        Effect.Appear('loading');

        this.interval = window.setInterval(
            function () {
                if (!Cookie.get('custom_export_address_report')) {
                    Effect.Fade('loading');
                    clearInterval(reportAddressReport.interval);
                }
            },
            1000
        );

        window.location = env.base_url + '?' + env.module_param + '=reports&reports=ajax_export_drilldown&report_type=' + $('report_type').value + '&month=' + month + '&entity=' + entity;
    }
}