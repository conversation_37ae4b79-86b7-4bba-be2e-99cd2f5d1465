<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    public $documentsErrors = array();
    public $documentsTypesUsed = array();

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'ajax_save_schedule':
                $this->_saveSchedule();
                break;
            case 'ajax_change_department':
                $this->_changeDepartment();
                break;
            default:
                parent::execute();
        }
    }

    /**
     * Saves activities schedule document
     */
    private function _saveSchedule() {
        /** @var Registry $registry */
        $registry = &$this->registry;
        /** @var Request $request */
        $request = &$this->registry['request'];

        // get current report
        $report = $this->getReportModel();

        // load plugin i18n files
        $i18n_files = array(
            sprintf(
                '%s%s%s%s%s%s',
                PH_MODULES_DIR,
                'reports/plugins/',
                $report->get('type'),
                '/i18n/',
                $registry['lang'],
                '/reports.ini'
            ),
            PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini',
        );
        $this->registry['translater']->loadFile($i18n_files);

        /** @var Activities_Schedule $report_class */
        $report_class = Reports::getPluginFactory($report->get('type'));
        $report_class::getReportSettings($registry);

        // get the shifts info
        $shifts = $report_class::getShiftList($registry);

        /** @var ADODB_mysqli $db */
        $db = &$registry['db'];
        $request = $registry['request'];

        // process the data per employee
        $employee_list = array();
        $previous_schedule = $request->get('shift_schedule');
        $current_schedule = $request->get('shift_current');
        $absence_doc = $request->get('absence_doc');

        // get the names of the employees to use for error data if needed
        $sql = 'SELECT ci18n.parent_id, CONCAT(ci18n.name, " ", ci18n.lastname) as name' . "\n" .
               'FROM ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
               'WHERE ci18n.parent_id IN ("' . implode('","', array_keys($current_schedule)) . '") AND ci18n.lang="' . $this->registry['lang'] . '"' . "\n";
        $employee_names = $db->GetAssoc($sql);

        foreach ($request->get('employee_schedule') as $emp => $schedule) {
            if (!$schedule && !count(array_filter($current_schedule[$emp]))) {
                continue;
            }
            $employee_list[$emp] = array(
                'id'                => $emp,
                'schedule'          => $schedule,
                'days'              => array()
            );

            foreach ($current_schedule[$emp] as $key => $curr) {
                $employee_list[$emp]['days'][$key] = array(
                    'date'        => sprintf('%s-%02d', $request->get('current_selected_month'), $key),
                    'current'     => $curr,
                    'previous'    => $previous_schedule[$emp][$key],
                    'absence_doc' => $absence_doc[$emp][$key]
                );
            }
        }

        // prepare the data to delete from the existing schedules
        //sql to take the ids of the needed additional vars from schedule
        $sql = 'SELECT fm2.name FROM ' . DB_TABLE_FIELDS_META . ' as fm1' . "\n" .
               'INNER JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
               ' ON (fm1.grouping=fm2.grouping AND fm2.type!="group" AND fm1.model=fm2.model AND fm1.model_type=fm2.model_type)' . "\n" .
               'WHERE fm1.model="Document" AND fm1.name="' . DOC_SCHEDULE_VAR_WORKSHIFT . '" AND fm1.model_type="' . DOC_SCHEDULE_TYPE . '"';
        $schedule_group_vars = $registry['db']->GetCol($sql);

        // delete the existing schedule data
        $existing_schedules = array_filter(array_unique(array_values($request->get('employee_schedule'))));

        $schedules_lst = array();
        if (!empty($existing_schedules)) {
            $filters = array(
                'where' => array('d.id IN (' . implode(',', $existing_schedules) . ')')
            );
            $schedules_lst = Documents::search($registry, $filters);
        }

        $schedules = array();
        foreach ($schedules_lst as $sch) {
            $schedules[$sch->get('id')] = clone $sch;
        }
        unset($schedules_lst);

        $schedule_document_type = false;

        $get_old_vars = $registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        // prepare the absence document info
        $absence_document_list = array(
            'selfsusp' => DOC_SELFSUSPENSION_TYPE,
            'compens'  => DOC_COMPENSATION_TYPE
        );
        if (DOC_SICKENSS_ALLOW_ADD) {
            $absence_document_list['mater'] = DOC_SICKENSS_TYPE;
            $absence_document_list['sick'] = DOC_SICKENSS_TYPE;
        }
        $sql = 'SELECT `parent_id`, `name` FROM ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' WHERE `parent_id` IN ("' . implode('","', $absence_document_list) . '") AND `lang`="' . $registry['lang'] . '"';
        $absence_document_names = $registry['db']->GetAssoc($sql);

        $db->StartTrans();
        foreach ($employee_list as $emp) {
            if ($db->HasFailedTrans()) {
                break;
            }

            if (!$emp['schedule'] || !isset($schedules[$emp['schedule']])) {
                if (empty($schedule_document_type)) {
                    // validate schedule document type
                    /** @var Documents_Type $type */
                    $schedule_document_type = Documents_Types::searchOne(
                        $this->registry,
                        array('where' => array('dt.id = ' . DOC_SCHEDULE_TYPE))
                    );
                }

                $current_schedule = new Document($this->registry, array('type' => DOC_SCHEDULE_TYPE));
                $current_schedule->getVars();
                $old_schedule = clone $current_schedule;

                $current_schedule->set('name', ($schedule_document_type->get('default_name') ?: $schedule_document_type->get('name')) ?: $this->i18n('reports_schedule_default_name'), true);
                $current_schedule->set('group', $schedule_document_type->getDefaultGroup(), true);
                $current_schedule->set('department', $schedule_document_type->getDefaultDepartment(), true);
                $current_schedule->set('date', General::strftime('%Y-%m-%d'), true);
                $current_schedule->set('active', 1, true);
                $current_schedule->set('customer', $schedule_document_type->get('default_customer'), true);

                // fill the additional vars
                $assoc_vars = $current_schedule->getAssocVars();
                $current_schedule->unsetProperty('assoc_vars', true);
                $month_elements = explode('-', $request->get('current_selected_month'));
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_MONTH])) {
                    $assoc_vars[DOC_SCHEDULE_VAR_MONTH]['value'] = sprintf('%d', isset($month_elements[1]) ? $month_elements[1] : 0);
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_YEAR])) {
                    $assoc_vars[DOC_SCHEDULE_VAR_YEAR]['value'] = sprintf('%d', isset($month_elements[0]) ? $month_elements[0] : 0);
                }

                if (isset($assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE])) {
                    if ($assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE]['grouping']) {
                        $assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE]['value'][1] = $emp['id'];
                    } else {
                        $assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE]['value'] = $emp['id'];
                    }
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE_NAME])) {
                    if ($assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE_NAME]['grouping']) {
                        $assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE_NAME]['value'][1] = $employee_names[$emp['id']];
                    } else {
                        $assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE_NAME]['value'] = $employee_names[$emp['id']];
                    }
                }

                foreach ($schedule_group_vars as $gr_var) {
                    if (isset($assoc_vars[$gr_var])) {
                        $assoc_vars[$gr_var]['value'] = array(0 => '');
                    }
                }

                $current_schedule->set('vars', array_values($assoc_vars), true);
                $current_schedule_action = 'add';
            } else {
                $current_schedule = clone $schedules[$emp['schedule']];
                $current_schedule->getVars();
                $old_schedule = clone $current_schedule;
                $current_schedule_action = 'edit';

                // edit existing schedule
                $sql = 'DELETE FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' WHERE `model_id`="' . $emp['schedule'] . '" AND `var_id` IN ("' . implode('", "', $schedule_group_vars) . '")';
                $db->Execute($sql);
            }

            // begin preparing the new schedule
            $current_row = 0;
            $assoc_vars = $current_schedule->getAssocVars();
            $current_schedule->unsetProperty('assoc_vars', true);

            foreach ($schedule_group_vars as $gr_var) {
                if (isset($assoc_vars[$gr_var])) {
                    $assoc_vars[$gr_var]['value'] = array();
                }
            }

            foreach ($emp['days'] as $dd => $day) {
                $current_shift = 0;
                if ($day['absence_doc']) {
                    // ABSENCE DOCUMENT EXITS SO SPECIAL BEHAVIOUR IS TRIGGERED
                    list($absence_doc_type, $absence_doc_id) = explode('_', $day['absence_doc']);

                    if (!in_array($absence_doc_type, $absence_document_list)) {
                        // if the absence document is not from the types which are meant to be changed - skip it
                        // (write whatever is provided in the current schedule field)
                        $current_shift = intval($day['current']);
                    } else {
                        if (isset($absence_document_list[$day['current']]) && $absence_document_list[$day['current']] == $absence_doc_type) {
                            // if the currently selected absence document is the same as the previously selected
                            // write whatever was previously selected - no change was made
                            $current_shift = intval($day['previous']);
                        } else {
                            // the currently selected option differs from the previously selected absence document
                            // so the existing document has to be deactivated
                            if (!$this->deactivateRelatedDocument($absence_doc_id)) {
                                // error
                                $this->documentsErrors[] = sprintf(
                                    $this->i18n('error_documents_absence_deactivate'),
                                    mb_strtolower($absence_document_names[$absence_doc_id], mb_detect_encoding($absence_document_names[$absence_doc_id])),
                                    $employee_names[$emp['id']],
                                    General::strftime('%d.%m.%Y', strtotime($day['date']))
                                );
                                $db->FailTrans();
                            }

                            if (array_key_exists($day['current'], $absence_document_list)) {
                                // if the new option points to absence document - try to add it
                                $error = $this->addAbsenceDocument($absence_document_list[$day['current']], $emp['id'], $day['date'], $day['current']);
                                if (!$error) {
                                    // success
                                    $current_shift = $day['previous'];
                                    if ($absence_document_list[$day['current']] == DOC_SICKENSS_TYPE && DOC_SICKNESS_DEFAULT_SHIFT && !$current_shift) {
                                        $current_shift = DOC_SICKNESS_DEFAULT_SHIFT;
                                    }
                                } else {
                                    // error
                                    $this->documentsErrors[] = sprintf(
                                        $this->i18n('error_documents_add_absence_failed_' . $error),
                                        mb_strtolower($absence_document_names[$absence_document_list[$day['current']]], mb_detect_encoding($absence_document_names[$absence_document_list[$day['current']]])),
                                        $employee_names[$emp['id']],
                                        General::strftime('%d.%m.%Y', strtotime($day['date']))
                                    );
                                    $db->FailTrans();
                                }
                            } else {
                                // the new option is a work shift - so just complete it in the schedule
                                $current_shift = intval($day['current']);
                            }
                        }
                    }
                } else {
                    if (array_key_exists($day['current'], $absence_document_list)) {
                        // ADD NEW ABSENCE DOCUMENT
                        // write the previous work shift for this day in the schedule
                        $current_shift = intval($day['previous']);
                        if ($absence_document_list[$day['current']] == DOC_SICKENSS_TYPE && DOC_SICKNESS_DEFAULT_SHIFT && !$current_shift) {
                            $current_shift = DOC_SICKNESS_DEFAULT_SHIFT;
                        }
                        $error = $this->addAbsenceDocument($absence_document_list[$day['current']], $emp['id'], $day['date'], $day['current']);

                        if ($error) {
                            // process error
                            $this->documentsErrors[] = sprintf(
                                $this->i18n('error_documents_add_absence_failed_' . $error),
                                mb_strtolower($absence_document_names[$absence_document_list[$day['current']]], mb_detect_encoding($absence_document_names[$absence_document_list[$day['current']]])),
                                $employee_names[$emp['id']],
                                General::strftime('%d.%m.%Y', strtotime($day['date']))
                            );
                            $db->FailTrans();
                        }
                    } else {
                        // no absence document to be added so just write whatever is selected in the report
                        $current_shift = intval($day['current']);
                    }
                }

                // if no current shift is set, skip the row
                if (!$current_shift) {
                    continue;
                }

                // prepare the insert clauses
                $current_row++;

                if (isset($assoc_vars[DOC_SCHEDULE_VAR_MONTH_DATE])) {
                    $assoc_vars[DOC_SCHEDULE_VAR_MONTH_DATE]['value'][$current_row] = $dd;
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_WEEKDAY])) {
                    $week_dat = General::strftime('%w', strtotime($day['date']));
                    $week_dat = ($week_dat == 7 ? 0 :  $week_dat);
                    $assoc_vars[DOC_SCHEDULE_VAR_WEEKDAY]['value'][$current_row] = $week_dat;
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT])) {
                    $assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT]['value'][$current_row] = (isset($shifts[$current_shift]) ? $shifts[$current_shift]['shift_id'] : 0);
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT_NAME])) {
                    $assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT_NAME]['value'][$current_row] = (isset($shifts[$current_shift]) ? $shifts[$current_shift]['name'] : 0);
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_START_HOUR])) {
                    $assoc_vars[DOC_SCHEDULE_VAR_START_HOUR]['value'][$current_row] = (isset($shifts[$current_shift]) ? $shifts[$current_shift]['start'] : 0);
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_DURATION])) {
                    $assoc_vars[DOC_SCHEDULE_VAR_DURATION]['value'][$current_row] = (isset($shifts[$current_shift]) ? $shifts[$current_shift]['duration'] : 0);
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT_START])) {
                    if ($shifts[$current_shift]) {
                        $curr_start_hour = sprintf('%s %s:00', $day['date'], $shifts[$current_shift]['start']);
                    } else {
                        $curr_start_hour = '';
                    }
                    $assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT_START]['value'][$current_row] = $curr_start_hour;
                }
                if (isset($assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT_END])) {
                    if ($shifts[$current_shift]) {
                        $curr_end_hour = new DateTime(sprintf('%s %s:00', $day['date'], $shifts[$current_shift]['start']));
                        $curr_end_hour->add(new DateInterval('PT' . $shifts[$current_shift]['duration'] . 'H'));
                    } else {
                        $curr_end_hour = '';
                    }
                    $assoc_vars[DOC_SCHEDULE_VAR_WORKSHIFT_END]['value'][$current_row] = $curr_end_hour->format('Y-m-d H:i:s');
                }
            }

            $current_schedule->set('vars', array_values($assoc_vars), true);
            $current_schedule->unsanitize();
            if ($current_schedule->save()) {
                // write history for the changes
                $filters = array(
                    'where'                  => array('d.id = ' . $current_schedule->get('id')),
                    'model_lang'             => $this->registry['lang'],
                    'skip_assignments'       => true,
                    'skip_permissions_check' => true
                );
                $new_schedule = Documents::searchOne($this->registry, $filters);
                $new_schedule->getVars();

                Documents_History::saveData($this->registry, array(
                    'model'       => $new_schedule,
                    'action_type' => $current_schedule_action,
                    'new_model'   => $new_schedule,
                    'old_model'   => $old_schedule
                ));
            } else {
                // ERROR
                $this->documentsErrors[] = sprintf($this->i18n('error_documents_edit_schedule_failed'), $employee_names[$emp['id']]);
                $db->FailTrans();
            }
        }
        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        $this->registry->set('get_old_vars', $get_old_vars, true);
        $operation_result = array(
            'result'   => $result,
            'errors'   => $this->documentsErrors,
            'messages' => array()
        );

        if ($result) {
            $this->registry['messages']->setMessage($this->i18n('reports_message_schedules_changed_successfully'));
            $this->registry['messages']->insertInSession($this->registry);
            $operation_result['messages'][] = $this->i18n('reports_message_schedules_changed_successfully');
        }

        print json_encode($operation_result);
        exit;
    }

    public function deactivateRelatedDocument($doc_id) {
        $filters = array(
            'where'                  => array('d.id = ' . $doc_id),
            'model_lang'             => $this->registry['lang'],
            'skip_assignments'       => true,
            'skip_permissions_check' => true
        );
        $related_doc = Documents::searchOne($this->registry, $filters);

        if (Documents::changeStatus($this->registry, array($doc_id), 'deactivate')) {
            Documents_History::saveData(
                $this->registry,
                array(
                    'model'       => $related_doc,
                    'action_type' => 'deactivate'
                )
            );
            return true;
        } else {
            return false;
        }
    }

    public function addAbsenceDocument($doc_type_id, $employee, $date, $type_index = '') {
        // define vars and type
        $doc_add_vars = array();
        switch ($doc_type_id) {
            case DOC_COMPENSATION_TYPE:
                $doc_add_vars[DOC_COMPENSATION_VAR_DAY_START] = $date;
                $doc_add_vars[DOC_COMPENSATION_VAR_DAY_END] = $date;
                $doc_add_vars[DOC_COMPENSATION_VAR_DAYS] = 1;
                break;
            case DOC_SELFSUSPENSION_TYPE:
                $doc_add_vars[DOC_SELFSUSPENSION_VAR_DAY_START] = $date;
                $doc_add_vars[DOC_SELFSUSPENSION_VAR_DAY_END] = $date;
                $doc_add_vars[DOC_SELFSUSPENSION_VAR_DAYS] = 1;
                break;
            case DOC_SICKENSS_TYPE:
                $doc_add_vars[DOC_SICKENSS_VAR_DAY_START] = $date;
                $doc_add_vars[DOC_SICKENSS_VAR_DAY_END] = $date;
                $doc_add_vars[DOC_SICKENSS_VAR_PRESENT_DATE] = $date;
                $doc_add_vars[DOC_SICKENSS_VAR_ISSUE_DATE] = $date;
                $doc_add_vars[DOC_SICKENSS_VAR_DAYS] = 1;
                if ($type_index == 'mater') {
                    $doc_add_vars[DOC_SICKENSS_VAR_REASON] = DOC_SICKENSS_REASON_MATERNITY;
                } else {
                    $doc_add_vars[DOC_SICKENSS_VAR_REASON] = DOC_SICKENSS_REASON_SICKNESS;
                }
                break;
        }

        $filter_add_vars = array_filter($doc_add_vars);
        if (!$doc_type_id || empty($filter_add_vars) || count($filter_add_vars)!=count($doc_add_vars)) {
            return 'missing_settings';
        }

        /** @var Documents_Type $type */
        if (!isset($this->documentsTypesUsed[$doc_type_id])) {
            $this->documentsTypesUsed[$doc_type_id] = Documents_Types::searchOne(
                $this->registry,
                array('where' => array('dt.id = ' . $doc_type_id))
            );
            $this->documentsTypesUsed[$doc_type_id]->sanitize();
        }

        $new_doc = new Document($this->registry, array('type' => $doc_type_id));
        $new_doc->getVars();
        $old_doc = clone $new_doc;

        $new_doc->set('name', ($this->documentsTypesUsed[$doc_type_id]->get('default_name') ?: $this->documentsTypesUsed[$doc_type_id]->get('name')) ?: $this->documentsTypesUsed[$doc_type_id]->get('name'), true);
        $new_doc->set('group', $this->documentsTypesUsed[$doc_type_id]->getDefaultGroup(), true);
        $new_doc->set('department', $this->documentsTypesUsed[$doc_type_id]->getDefaultDepartment(), true);
        $new_doc->set('active', 1, true);
        if ($doc_type_id == DOC_SICKENSS_TYPE) {
            $new_doc->set('customer', $this->documentsTypesUsed[$doc_type_id]->get('default_customer'), true);
            $new_doc->set('employee', $employee, true);
        } else {
            $new_doc->set('customer', $employee, true);
        }

        $assoc_vars = $new_doc->getAssocVars();
        $new_doc->unsetProperty('assoc_vars', true);

        foreach ($doc_add_vars as $var_name => $var_value) {
            $assoc_vars[$var_name]['value'] = $var_value;
        }

        $new_doc->set('vars', array_values($assoc_vars), true);
        if ($new_doc->save()) {
            // write history for the changes
            $filters = array(
                'where'                  => array('d.id = ' . $new_doc->get('id')),
                'model_lang'             => $this->registry['lang'],
                'skip_assignments'       => true,
                'skip_permissions_check' => true
            );
            $new_doc = Documents::searchOne($this->registry, $filters);
            $new_doc->getVars();

            Documents_History::saveData($this->registry, array(
                'model'       => $new_doc,
                'action_type' => 'add',
                'new_model'   => $new_doc,
                'old_model'   => $old_doc
            ));
        } else {
            return 'failed_add';
        }

        return '';
    }

    /*
     * AJAX function to reset the employee filter to match the selected department
     */
    private function _changeDepartment() {
        $request = $this->registry['request'];

        //get current report
        $report = $this->getReportType();
        $report = $report['name'];
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];
        Reports::getReportSettings($this->registry, $report->get('type'));

        // get the filter settings
        require_once PH_MODULES_DIR . 'reports/models/report.filters.php';
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report->get('type') . '/custom.report.filters.php';
        $defineCustomFilters = new Custom_Report_Filters($this->registry, $report->get('type'));
        $filters = $defineCustomFilters->defineFilters($this->registry);
        if ($request->get('department')) {
            $filters['employee']['autocomplete']['filters']['<department>'] = strval($request->get('department'));
        } else {
            $filters['employee']['autocomplete']['filters']['<department>'] = implode(',', array_filter(array_column($filters['department']['options'], 'option_value')));
        }

        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset(PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html');
        $this->viewer->data['filter_settings'] = $filters['employee'];

        print $this->viewer->fetch();
        exit;
    }
}
