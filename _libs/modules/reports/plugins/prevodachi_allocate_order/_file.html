<table id="file_data_{$file_index}">
  <tr>
    <td style="width: 194px;">{#reports_field_file#|escape}: {#required#}</td>
    <td style="width: 180px;">{#reports_field_competence#|escape}: {#required#}</td>
    <td style="width:  60px;">{#reports_field_pages#|escape}:</td>
    <td style="width:  60px;">{#reports_field_complexity#|escape}: {#required#}</td>
    <td style="width:  70px;">{#reports_field_pages_left#|escape}:</td>
    <td style="text-align: right;">
      <img src="{$theme->imagesUrl}small/delete.png" onclick="if (confirm('{#reports_confirm_remove_file#}')) {ldelim} report_prevodachi_allocate_order.removeFileVars('file_data_{$file_index}'); report_prevodachi_allocate_order.checkTranslatedPagesSoFar(); {rdelim}" style="cursor: pointer;" alt="{#reports_delete_selected_file#|escape}" title="{#reports_delete_selected_file#|escape}" />
    </td>
  </tr>
  <tr>
    <td>
      {include file='input_file_upload.html'
        name="file_`$file_index`"
        custom_id="file_`$file_index`"
        standalone=true
        required=1
        onchange="report_prevodachi_allocate_order.showFileName('`$file_index`');"}
      <div id="file_name_{$file_index}" style="width: 170px; overflow: hidden; display: inline-block; height: 14px; margin-left: 3px; white-space: nowrap; text-overflow: ellipsis;" title=""></div>
    </td>
    <td style="padding-right: 25px;">
      {include file='input_dropdown.html'
        name="competence_`$file_index`"
        custom_id="competence_`$file_index`"
        options=$file_vars.competence_options
        standalone=true
        required=1
        width=180}
    </td>
    <td style="padding-right: 25px;">
      {include file='input_text.html'
        name="pages_`$file_index`"
        restrict=$js_filter_pages
        onkeyup="report_prevodachi_allocate_order.calculateRemainingPages(this, 'file')"
        standalone=true
        width=60}
    </td>
    <td style="padding-right: 25px;">
      {include file='input_dropdown.html'
        name="complexity_`$file_index`"
        custom_id="complexity_`$file_index`"
        options=$file_vars.complexity_options
        standalone=true
        really_required=1
        required=1
        width=60}
    </td>
    <td style="padding-right: 25px;" colspan="2">
      {include file='input_text.html'
        name="pages_left_`$file_index`"
        standalone=true
        readonly=true
        width=60}
    </td>
  </tr>
  <tr>
    <td colspan="6">
      {assign var='file_vars_table' value=$file_vars.table}
      {array assign='file_vars_table'
        grouping=$file_index}
      {include file='_configurator_group_edit.html'
        var=$file_vars_table}
    </td>
  </tr>
</table>