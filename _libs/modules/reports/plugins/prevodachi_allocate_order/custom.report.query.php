<?php
    Class Prevodachi_Allocate_Order Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            // Prepare an array for the final results
            $final_results = array();

            // Prepare an array for errors
            $final_results['additional_options']['errors'] = array();

            // If all report settings are set
            if (Report_Filters::checkRequiredSettings($registry, array('REQUEST_TRANSLATION_COMPLEXITY', 'NOMENCLATURES_LANGUAGE', 'REQUEST_LANGUAGE', 'REQUEST_PAGES', 'REQUEST_ID'))) {
                // Get model lang
                if (empty($filters['model_lang'])) {
                    // Default model language is the interface language
                    $model_lang = $registry['lang'];
                } else {
                    $model_lang = $filters['model_lang'];
                }

                $file_vars = array();

                $competence_options_params = array(
                    0            => $registry,
                    'table'      => 'DB_TABLE_NOMENCLATURES',
                    'table_i18n' => 'DB_TABLE_NOMENCLATURES_I18N',
                    'where'      => 'type like 9');
                $file_vars['competence_options'] = Dropdown::getCustomDropdown($competence_options_params);

                // Prepare to get complexity options
                $complexity_options = Report_Filters::getFieldOptions($registry, REQUEST_TRANSLATION_COMPLEXITY);
                $query = 'SELECT `id`' . "\n" .
                         '  FROM `' . DB_TABLE_FIELDS_META . '`' . "\n" .
                         '  WHERE `model` = \'Nomenclature\'' . "\n" .
                         '    AND `model_type` = \'' . NOMENCLATURE_TYPE_TRANSLATION . '\'' . "\n" .
                         '    AND `name` = \'' . FIELD_NOM_TRANSLATION_CLASS . '\'';
                $field_nom_translation_class = $registry['db']->GetOne($query);
                $query = 'SELECT `value`, GROUP_CONCAT(`model_id`)' . "\n" .
                         '  FROM `' . DB_TABLE_NOMENCLATURES_CSTM . '`' . "\n" .
                         '  WHERE `var_id` = \'' . $field_nom_translation_class . '\'' . "\n" .
                         '    AND `value` != \'\'' . "\n" .
                         '  GROUP BY `value`';
                $classes_noms = $registry['db']->GetAssoc($query);
                foreach ($classes_noms as $classes_noms_key => $classes_noms_value) {
                    $classes_noms[$classes_noms_key] = explode(',', $classes_noms_value);
                }
                // Prepare array to check which xomplexity options ahe already been collected
                $collected_complexity_classes = array();
                foreach ($complexity_options as $complexity_option) {
                    $collected_complexity_classes[$complexity_option['option_value']] = false;
                }

                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                $document_filters = array('where'      => array('d.id = \'' . $filters['document_id'] . '\''),
                                          'model_lang' => $model_lang);
                $document = Documents::searchOne($registry, $document_filters);
                // Get the GT2
                $get_old_vars = $registry->get('get_old_vars');
                $registry->set('get_old_vars', true, true);
                $document->getGT2Vars();
                $registry->set('get_old_vars', $get_old_vars, true);
                $gt2 = $document->get('grouping_table_2');

                $language_nomenclatures = $registry['db']->GetCol('SELECT `id` FROM `nom` WHERE `type` = \'' . NOMENCLATURES_LANGUAGE . '\'');
                $languages_options = array();
                $used_languages = array();
                $languages = array();
                $languages_pages = array();
                foreach ($gt2['values'] as $gt2_row) {
                    if (!empty($gt2_row['article_alternative_deliverer'])
                            && !empty($gt2_row['article_alternative_deliverer_name'])
                            && in_array($gt2_row['article_alternative_deliverer'], $language_nomenclatures)
                            && !in_array($gt2_row['article_alternative_deliverer'], $used_languages)) {
                        $languages_options[] = array(
                            'label' => $gt2_row['article_alternative_deliverer_name'],
                            'option_value' => $gt2_row['article_alternative_deliverer']);
                        $used_languages[] = $gt2_row['article_alternative_deliverer'];
                        $languages[$gt2_row['article_alternative_deliverer']] = $gt2_row['article_alternative_deliverer_name'];
                    }
                    if (!isset($languages_pages[$gt2_row['article_alternative_deliverer']])) {
                        $languages_pages[$gt2_row['article_alternative_deliverer']] = 0;
                    }
                    $languages_pages[$gt2_row['article_alternative_deliverer']] += $gt2_row['quantity'];

                    foreach ($collected_complexity_classes as $collected_complexity_class_key => $collected_complexity_class_is_collected) {
                        if (!$collected_complexity_class_is_collected) {
                            if (in_array($gt2_row['article_id'], $classes_noms[$collected_complexity_class_key])) {
                                $collected_complexity_classes[$collected_complexity_class_key] = true;
                            }
                        }
                    }
                }

                // Remove the complexity options that are not collected (i.e. that do not exists into the languages fro mthe GT2)
                foreach ($complexity_options as $complexity_option_key => $complexity_option) {
                    if (!$collected_complexity_classes[$complexity_option['option_value']]) {
                        unset($complexity_options[$complexity_option_key]);
                    }
                }
                $file_vars['complexity_options'] = $complexity_options;

                $file_vars['languages'] = $languages;

                $file_vars['table'] = array(
                    'types'  => array('dropdown', 'text', 'autocompleter', 'textarea', 'text', 'text', 'datetime'),
                    'names'  => array('language_1', 'translator_id_1', 'translator_1', 'note_1', 'pages_1', 'price_1', 'deadline_1'),
                    'width'  => array('150', '0', '150', '130', '65', '50', '100'),
                    'hidden' => array(false, true, false, false, false, false, false),
                    'required' => array(true, false, true, false, true, true, true),
                    'labels' => array(
                        $registry['translater']->translate('reports_th_language'),
                        '',
                        $registry['translater']->translate('reports_th_translator'),
                        $registry['translater']->translate('reports_th_note'),
                        $registry['translater']->translate('reports_th_pages'),
                        $registry['translater']->translate('reports_th_price'),
                        $registry['translater']->translate('reports_th_deadline')),
                    'values' => array(),
                    'language_1' => array('options' => $languages_options),
                    'last_visible_column' => 6,
                    'dont_copy_values' => 1,
                    'js_filters' => array(false, false, false, false, (defined('JS_FILTER_PAGES') && JS_FILTER_PAGES != '' ? JS_FILTER_PAGES : 'insertOnlyDigits'), 'insertOnlyFloats', false),
                    'js_methods' => array(false, false, false, false, array('onkeyup' => 'report_prevodachi_allocate_order.calculateRemainingPages(this, \'gt2\'); report_prevodachi_allocate_order.checkTranslatedPagesSoFar();'), false, false),
                    'autocomplete' => array(
                        'translator_1' => array(
                            'type'         => 'customers',
                            'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                            'suggestions'  => '<name> <lastname>',
                            'search'       => array('<name>'),
                            'execute_after'=> 'report_prevodachi_allocate_order.calculatePricePerPage',
                            'filters'      => array('<type>' => strval(PH_CUSTOMER_EMPLOYEE)),
                            'fill_options' => array(
                                '$translator_1 => <name> <lastname>',
                                '$translator_id_1 => <id>',
                                '$translator_old_value_1 => <name> <lastname>'),
                            'clear'        => 1)));

                $file_vars['document_id'] = $filters['document_id'];
                $file_vars['document_deadline'] = $document->getPlainVarValue('date_time_delivery');

                // check the language
                $languages_check = array();
                $issued_requests_pages = array();
                if (!empty($languages)) {
                    // define needed additinal vars
                    $request_vars = array(REQUEST_LANGUAGE, REQUEST_PAGES);

                    $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type="' . REQUEST_ID . '" AND fm.name IN ("' . implode('","', $request_vars) . '") ORDER BY fm.position';
                    $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                    $request_language_id = 0;
                    $request_pages_id = 0;

                    foreach ($var_ids as $vars) {
                        if ($vars['name'] == REQUEST_LANGUAGE) {
                            $request_language_id = $vars['id'];
                        } else if ($vars['name'] == REQUEST_PAGES) {
                            $request_pages_id = $vars['id'];
                        }
                    }

                    // get related requests to the current document
                    $query_relations = 'SELECT d_cstm_lang.value as lang, SUM(d_cstm_pages.value) as pages ' . "\n" .
                                       'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                                       'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                       '  ON (dr.parent_model_name="Document" AND dr.parent_id=d.id AND d.type="' . REQUEST_ID . '")' . "\n" .
                                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_lang' . "\n" .
                                       '  ON (d_cstm_lang.model_id=d.id AND d_cstm_lang.var_id="' . $request_language_id . '" AND d_cstm_lang.value IN ("' . implode('","', array_keys($languages)) . '"))' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pages' . "\n" .
                                       '  ON (d_cstm_pages.model_id=d.id AND d_cstm_pages.var_id="' . $request_pages_id . '")' . "\n" .
                                       'WHERE dr.link_to_model_name="Document" AND dr.link_to="' . $filters['document_id'] . '"' . "\n" .
                                       'GROUP BY d_cstm_lang.value' . "\n";
                    $issued_requests_pages = $registry['db']->GetAssoc($query_relations);

                    foreach ($issued_requests_pages as $irp_lang => $irp_pages) {
                        if (isset($languages[$irp_lang])) {
                            if (isset($languages_pages[$irp_lang])) {
                                $document_language_pages = $languages_pages[$irp_lang];
                            } else {
                                $document_language_pages = 0;
                            }
                            $difference = $document_language_pages - $irp_pages;
                            if ($difference<0) {
                                $languages_check[$irp_lang] = sprintf($registry['translater']->translate('messages_reports_language_pages_exceeded'), $languages[$irp_lang], abs($difference));
                            }
                        }
                    }
                }

                $final_results['file_vars'] = $file_vars;
                $final_results['js_filter_pages'] = (defined('JS_FILTER_PAGES') && JS_FILTER_PAGES != '' ? JS_FILTER_PAGES : 'insertOnlyDigits');
                $final_results['pages_overrun_msgs'] = $languages_check;
                $final_results['pages_data'] = array(
                    'document_pages' => $languages_pages,
                    'requests_pages' => $issued_requests_pages,
                    'languages_options' => $languages
                );
                $final_results['pages_data'] = base64_encode(json_encode($final_results['pages_data']));
                $registry['session']->set('report_prevodachi_allocate_order_file_vars', $file_vars, '', true);
            } else {
                // Error: some report settings are missing
                $final_results['additional_options']['errors'][] = $registry['translater']->translate('error_reports_required_settings');
            }

            // Hide the export button
            $final_results['additional_options']['dont_show_export_button'] = true;

            // Prepare the results
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>