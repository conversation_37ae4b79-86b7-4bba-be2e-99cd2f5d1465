filter_period = Period
filter_offer = Offer
filter_customer = Customer
filter_trading_company = Trading Company
filter_point_of_sell = Point of Sell
filter_designer = Designer

th_designer = Designer
th_type_bonus = Type Bonus
th_bonus_percent = Bonus %
th_offer = Offer
th_supplier = Supplier
th_sell_price = Sell Price Products w/o VAT /EUR/
th_international_transport = International Transport /EUR/
th_commission = Commission /EUR/
th_cost_price_assembly_and_delivery = Cost Price Assembly and Delivery /EUR/
th_price = Price /EUR/
th_price_title = Sell Price - (International Transport + Commission + Cost Price Assembly and Delivery)
th_bonus_amount = Bonus Amount w/o VAT /EUR/
th_total_bonus_amounts = Total Bonus w/o VAT /EUR/
th_bonus_expense =  Expense Document Bonus Designer

net_salary = Net Salary

chart_title = Bonuses Comparison by Periods
chart_y_title = Bonuses /EUR/

btn_create_bonus_expenses = Create Bonuses

btn_yes = Yes
btn_no = No

are_you_sure_to_create_bonus_expenses = Are you sure you want to create expense documents for designers bonuses?

expense_bonus_description_bonuses_for_periods = Bonuses for Period: %s - %s
expense_bonus_description_bonuses_for_date = Bonuses for: %s

successfully_added_bonuse_expenses = Successfully added bonus expenses: %s

total_selected_bonuses = Total selected bonuses