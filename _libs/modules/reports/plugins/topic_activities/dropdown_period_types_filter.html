<tr>
  <td class="labelbox">
    <a name="error_period_types"></a>
    <label for="period_types">{#reports_period#}:</label>
  </td>
  <td class="required">
    <span class="required">{#required#}</span>
  </td>
  <td nowrap="nowrap">
    <select onblur="unhighlight(this);" onfocus="highlight(this);" onchange="this.options[this.selectedIndex].onclick();" title="{#reports_period#}" class="selbox" id="period_types" name="period_types">
      {foreach from=$filter_settings.options item=option}
      <option value="{$option.option_value}" onclick="{$option.onclick}"{if $filter_settings.value eq $option.option_value || (empty($filter_settings.value) && $option.option_value eq 'from_to_date')} selected="selected"{/if}>{$option.label}</option>
      {/foreach}
    </select>
  </td>
</tr>