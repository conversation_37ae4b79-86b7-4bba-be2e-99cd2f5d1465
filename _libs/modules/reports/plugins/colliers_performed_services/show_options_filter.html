<tr>
  <td class="labelbox">
    <a name="error_{$filter_settings.name}"></a>
    <label for="{$filter_settings.name}">Show:</label>
  </td>
  <td>&nbsp;</td>
  <td nowrap="nowrap">
    <table cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td valign="top" nowrap="nowrap" style="padding: 0px;">
          {foreach name='cb' from=$filter_settings.options item='option'}
            <input type="checkbox" onblur="unhighlight(this)" onfocus="highlight(this)" title="{$option.label}" value="{$option.option_value}" id="{$filter_settings.custom_id}_{$smarty.foreach.cb.iteration}" name="{$filter_settings.name}[]"{if is_array($filter_settings.value) && in_array($option.option_value, $filter_settings.value)} checked="checked"{/if}{if $option.on_click} onclick="{$option.on_click}"{/if} /><label for="{$filter_settings.custom_id}_{$smarty.foreach.cb.iteration}">{$option.label}</label>{if !$smarty.foreach.cb.last}<br />{/if}
          {/foreach}
        </td>
      </tr>
    </table>
  </td>
</tr>
