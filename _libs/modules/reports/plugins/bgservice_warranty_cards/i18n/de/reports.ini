reports_filter_num = Rechnungsnummer
reports_filter_num_help = <PERSON> Bericht <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, für die der Wert aus diesem Feld wird in ihrer Zahl enthalten.
reports_filter_period = Zeitraum
reports_filter_period_from = von
reports_filter_provider = Zusteller
reports_filter_client = Client
reports_filter_sn = Seriennummer
reports_filter_sn_help = <PERSON> Bericht zeigt, <PERSON><PERSON><PERSON><PERSON>n, für die der Wert aus diesem Feld wird in identisch mit den Seriennummern der Artikel.
reports_filter_description = Beschreibung
reports_filter_description_help = Der Bericht zeigt, <PERSON><PERSON><PERSON><PERSON><PERSON>, für die der Wert aus diesem Feld wird in den Artikeln Beschreibung enthalten.
reports_filter_warranty_cards = Garantiekarten
reports_filter_warranty_cards_all = Alle
reports_filter_warranty_cards_issued = Ausgestellt
reports_filter_warranty_cards_unissued = Nicht ausgestellt

reports_th_invoice_num = Rechnungsnummer
reports_th_date = Datum
reports_th_provider = Zusteller
reports_th_client = Klient
reports_th_article = Artikel
reports_th_description = Beschreibung
reports_th_measure = Measure
reports_th_quantity = Lager
reports_th_price = Einzelpreis
reports_th_currency = Währungs
reports_th_serial_num = Seriennummer
reports_th_warranty = Garantie
reports_th_file = Datei
reports_th_warranty_card_files = Dateien der Garantiekarte
reports_th_warranty_card = Garantiekarte

reports_button_save = Speichern

message_reports_save_success = Alle Änderungen wurden erfolgreich gespeichert!
error_reports_invoices = Ein Fehler ist aufgetreten mit den Rechnungsdaten!
error_reports_warranty_cards_save = Beim Speichern der Änderungen an den Garantiekarten ist ein Fehler aufgetreten!
error_reports_invoice_attachments = Fehler beim Anhängen von Dateien an den Rechnungen aufgetreten!
error_reports_failed_changes = Es wurden keine Änderungen aufgrund eines Fehlers gemacht!