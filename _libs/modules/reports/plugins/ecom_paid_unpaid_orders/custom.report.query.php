<?php
    Class Ecom_Paid_Unpaid_Orders Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            // take the main currency
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $main_currency = Finance_Currencies::getMain($registry);
            $total_value = 0;
            $total_left_to_pay = 0;
            $total_expenses = 0;

            $final_results = array();
            $reason_document_relation = array();

            $currency_multipliers = array();
            $currency_multipliers[$main_currency . '->' . $main_currency] = 1;

            $total_var = '';
            $currency_var = '';

            $sql_total_var = 'SELECT id, name FROM ' . DB_TABLE_FIELDS_META . ' WHERE model_type="' . DOCUMENT_ORDER_ID . '" AND model="Document" AND (name="' . DOCUMENT_ORDER_TOTAL . '" OR name="' . DOCUMENT_ORDER_CURRENCY . '")';
            $result_vars = $registry['db']->GetAll($sql_total_var);

            foreach($result_vars as $res_var) {
                if ($res_var['name'] == DOCUMENT_ORDER_TOTAL) {
                    $total_var = $res_var['id'];
                } else if ($res_var['name'] == DOCUMENT_ORDER_CURRENCY) {
                    $currency_var = $res_var['id'];
                }
            }

            $available_customers = array();
            if (!empty($filters['customers'])) {
                foreach ($filters['customers'] as $cstm) {
                    if (!empty($cstm)) {
                        $available_customers[] = $cstm;
                    }
                }
            }

            // find the reasons for the
            $sql_documents = array();
            $sql_documents['select'] = 'SELECT d.id, d.full_num, di18n.name as name, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ' . "\n" .
                                       '  d_cstm_total.value as total, d_cstm_currency.value as currency, fir.id as reason_id, ' . "\n" .
                                       '  DATE_FORMAT(fir.date_of_payment, "%Y-%m-%d") as reason_deadline' . "\n";

            $sql_documents['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                       '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                       '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_total' . "\n" .
                                       '  ON (d_cstm_total.model_id=d.id AND d_cstm_total.var_id="' . $total_var . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_currency' . "\n" .
                                       '  ON (d_cstm_currency.model_id=d.id AND d_cstm_currency.var_id="' . $currency_var . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                       '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to=d.id AND frr.link_to_model_name="Document")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                                       '  ON (frr.parent_id=fir.id AND fir.type="' . INCOME_REASON_ID . '" AND fir.status="finished" AND fir.annulled_by=0 AND fir.active=1)' . "\n";

            $documents_where = array();
            $documents_where[] = 'd.deleted_by=0';
            $documents_where[] = 'd.active=1';
            $documents_where[] = 'd.type="' . DOCUMENT_ORDER_ID . '"';
            $documents_where[] = 'd.substatus!="' . DOCUMENT_ORDER_STATUS_FAILED . '"';
            if ($registry['currentUser']->get('role') != MANAGERS_ROLE_ID) {
                $documents_where[] = 'd.added_by="' . $registry['currentUser']->get('id') . '"';
            }

            if (!empty($available_customers)) {
                $documents_where[] = 'd.customer IN (' . implode(',', $available_customers) . ')';
            }
            if (!empty($filters['from_date'])) {
                $documents_where[] = 'DATE_FORMAT(d.added, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $documents_where[] = 'DATE_FORMAT(d.added, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }
            if (!empty($filters['dealer'])) {
                $documents_where[] = 'd.added_by="' . $filters['dealer'] . '"';
            }

            $sql_documents['where']  = 'WHERE ' . implode(' AND ', $documents_where);

            $query_documents = implode("\n", $sql_documents);
            $records_documents = $registry['db']->GetAll($query_documents);

            // start to construct the final results array
            // the main key is the customer
            foreach ($records_documents as $document) {
                if (!isset($final_results[$document['customer']])) {
                    $final_results[$document['customer']] = array(
                        'id'                    => $document['customer'],
                        'name'                  => $document['customer_name'],
                        'documents'             => array(),
                        'unallocated_payments'  => array(),
                    );
                }

                $final_results[$document['customer']]['documents'][$document['id']] = array(
                    'id'            => $document['id'],
                    'num'           => $document['full_num'],
                    'name'          => $document['name'],
                    'value'         => $document['total'],
                    'currency'      => $document['currency'],
                    'payments'      => array(),
                    'deadline'      => '',
                    'paid'          => 0,
                    'remain_to_pay' => $document['total']
                );

                if ($document['reason_id']) {
                    $reason_document_relation[$document['reason_id']] = $document['id'];
                    $final_results[$document['customer']]['documents'][$document['id']]['deadline'] = $document['reason_deadline'];
                }
            }

            if (!empty($reason_document_relation)) {
                // finds all the information for the invoices
                $related_reasons = array_keys($reason_document_relation);
                $customers_list = array_keys($final_results);

                $sql_reasons = array();
                $sql_reasons['select'] = 'SELECT fir.id as reason_id, fir.customer as customer, fir2.id as invoice_id, fir2.num as invoice_num, DATE_FORMAT(fir2.issue_date, "%Y-%m-%d") as invoice_issue_date, ' . "\n" .
                                         '  fr.parent_id as payment_id, fr.paid_amount as payment_amount, fr.paid_currency as payment_currency, ' . "\n" .
                                         '  fp.num as payment_num, DATE_FORMAT(fp.added, "%Y-%m-%d") as payment_date' . "\n";

                $sql_reasons['from']   = 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                         '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Finance_Incomes_Reason" AND fir.id=frr.link_to)' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir2' . "\n" .
                                         '  ON (fir2.id=frr.parent_id AND fir2.active=1 AND fir2.annulled_by=0 AND fir2.type="' . PH_FINANCE_TYPE_INVOICE . '")' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                                         '  ON (fr.paid_to_model_name="Finance_Incomes_Reason" AND fr.parent_model_name="Finance_Payment" AND fr.paid_to=fir2.id)' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                                         '  ON (fr.parent_id=fp.id AND fp.annulled_by=0)' . "\n";

                $sql_reasons['where']  = sprintf('WHERE fir.id IN (%s) AND fir.customer IN (%s)', implode(',', $related_reasons), implode(',', $customers_list));

                $query_reasons = implode("\n", $sql_reasons);
                $records_reasons = $registry['db']->GetAll($query_reasons);

                foreach ($records_reasons as $rec_reason) {
                    if (empty($rec_reason['invoice_id']) && empty($rec_reason['payment_id'])) {
                        continue;
                    }

                    $customer_key = $rec_reason['customer'];
                    $document_key = $reason_document_relation[$rec_reason['reason_id']];

                    $payment_key = '';
                    if ($rec_reason['payment_id']) {
                        $payment_key = $rec_reason['payment_id'];
                    } else if ($rec_reason['invoice_id']) {
                        // no payment for this invoice
                        $payment_key = 0;
                    }

                    if ($payment_key == '') {
                        continue;
                    }

                    if (!isset($final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key])) {
                        if ($payment_key) {
                            $final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key] = array(
                                'id'       => $rec_reason['payment_id'],
                                'num'      => $rec_reason['payment_num'],
                                'sum'      => 0,
                                'date'     => $rec_reason['payment_date'],
                                'rowspan'  => 0,
                                'invoices' => array()
                            );
                        } else {
                            $final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key] = array(
                                'id'       => 0,
                                'rowspan'  => 0,
                                'invoices' => array()
                            );
                        }
                    }

                    $currency_key = $rec_reason['payment_currency'] . '->' . $main_currency;
                    if (!isset($currency_multipliers[$currency_key])) {
                        $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $rec_reason['payment_currency'], $main_currency);
                    }
                    $current_multiplier = $currency_multipliers[$currency_key];

                    if ($payment_key) {
                        $current_invoice_sum = round(($rec_reason['payment_amount']*$current_multiplier), 2);
                        $final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key]['sum'] = round(($final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key]['sum'] + $current_invoice_sum), 2);
                        $final_results[$customer_key]['documents'][$document_key]['paid'] += $current_invoice_sum;
                        $final_results[$customer_key]['documents'][$document_key]['remain_to_pay'] = round(($final_results[$customer_key]['documents'][$document_key]['remain_to_pay'] - $current_invoice_sum), 2);
                    }

                    $final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key]['invoices'][] = array(
                        'id'            => $rec_reason['invoice_id'],
                        'num'           => $rec_reason['invoice_num'],
                        'issue_date'    => $rec_reason['invoice_issue_date']
                    );
                }

                // query to take directly related payments
                $sql_payments = array();
                $sql_payments['select'] = 'SELECT fir.id as reason_id, fir.customer as customer, ' . "\n" .
                                          '  fr.parent_id as payment_id, fr.paid_amount as payment_amount, fr.paid_currency as payment_currency, ' . "\n" .
                                          '  fp.num as payment_num, DATE_FORMAT(fp.added, "%Y-%m-%d") as payment_date' . "\n";

                $sql_payments['from']   = 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                                          '  ON (fr.paid_to_model_name="Finance_Incomes_Reason" AND fr.parent_model_name="Finance_Payment" AND fr.paid_to=fir.id)' . "\n" .
                                          'LEFT JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                                          '  ON (fr.parent_id=fp.id AND fp.annulled_by=0)' . "\n";

                $sql_payments['where']  = sprintf('WHERE fir.id IN (%s) AND fir.customer IN (%s)', implode(',', array_keys($reason_document_relation)), implode(',', $customers_list));

                $query_payments = implode("\n", $sql_payments);
                $records_payments = $registry['db']->GetAll($query_payments);

                foreach ($records_payments as $rec_payment) {
                    $customer_key = $rec_payment['customer'];
                    $document_key = $reason_document_relation[$rec_payment['reason_id']];
                    $payment_key = $rec_payment['payment_id'];

                     if ($payment_key && !isset($final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key])) {

                        $currency_key = $rec_payment['payment_currency'] . '->' . $main_currency;
                        if (!isset($currency_multipliers[$currency_key])) {
                            $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $rec_payment['payment_currency'], $main_currency);
                        }
                        $current_multiplier = $currency_multipliers[$currency_key];

                        $current_payment_sum = round(($rec_payment['payment_amount']*$current_multiplier), 2);
                        $final_results[$customer_key]['documents'][$document_key]['payments'][$payment_key] = array(
                            'id'       => $rec_payment['payment_id'],
                            'num'      => $rec_payment['payment_num'],
                            'sum'      => $current_payment_sum,
                            'date'     => $rec_payment['payment_date'],
                            'rowspan'  => 0,
                            'invoices' => array()
                        );

                        $final_results[$customer_key]['documents'][$document_key]['paid'] += $current_payment_sum;
                        $final_results[$customer_key]['documents'][$document_key]['remain_to_pay'] = round(($final_results[$customer_key]['documents'][$document_key]['remain_to_pay'] - $current_payment_sum), 2);
                    }
                }
            }

            krsort($final_results);

            if (!empty($filters['status_order']) && $filters['status_order'] != 'all') {
                // foreach to clear the paid/unpaid/partially paid documents
                foreach ($final_results as $customer_id => $customer) {
                    foreach($customer['documents'] as $cstm_doc => $document) {
                        switch($filters['status_order']) {
                            case 'paid':
                                if ($document['value'] == 0 || $document['remain_to_pay'] > 0) {
                                    unset($final_results[$customer_id]['documents'][$cstm_doc]);
                                }
                                break;
                            case 'partial':
                                if (!($document['remain_to_pay'] > 0 && $document['value'] > $document['remain_to_pay'])) {
                                    unset($final_results[$customer_id]['documents'][$cstm_doc]);
                                }
                                break;
                            case 'unpaid':
                                if ($document['paid']) {
                                    unset($final_results[$customer_id]['documents'][$cstm_doc]);
                                }
                                break;
                        }
                    }

                    if (empty($final_results[$customer_id]['documents'])) {
                        unset($final_results[$customer_id]);
                    }
                }
            }

            $full_paid_offers = 0;
            $partial_paid_offers = 0;
            $not_paid_offers = 0;

            foreach ($final_results as $customer_id => $customer) {
                $customer_rowspan = 0;
                foreach($customer['documents'] as $cstm_doc => $document) {
                    if (!$document['paid']) {
                        $not_paid_offers += $document['remain_to_pay'];
                    } elseif ($document['paid'] && $document['remain_to_pay']) {
                        $partial_paid_offers += $document['paid'];
                    } elseif (!$document['remain_to_pay']) {
                        $full_paid_offers += $document['paid'];
                    }

                    $documents_rowspan = 0;
                    $total_payments_rowspan = 0;
                    foreach ($document['payments'] as $key_p => $payment) {
                        if (count($payment['invoices'])==0) {
                            $payments_rowspan = 1;
                        } else {
                            $payments_rowspan = count($payment['invoices']);
                        }
                        $total_payments_rowspan += $payments_rowspan;
                        $final_results[$customer_id]['documents'][$cstm_doc]['payments'][$key_p]['rowspan'] = $payments_rowspan;
                    }
                    if (!$total_payments_rowspan) {
                        $total_payments_rowspan = 1;
                    }
                    $documents_rowspan += $total_payments_rowspan+1;

                    $final_results[$customer_id]['documents'][$cstm_doc]['payments'] = array_values($final_results[$customer_id]['documents'][$cstm_doc]['payments']);
                    if (!$documents_rowspan) {
                        $documents_rowspan = 2;
                    }
                    $customer_rowspan += $documents_rowspan;

                    $final_results[$customer_id]['documents'][$cstm_doc]['rowspan'] = $documents_rowspan;
                }

                $final_results[$customer_id]['documents'] = array_values($final_results[$customer_id]['documents']);

                if ($customer_rowspan==0) {
                    $customer_rowspan = 2;
                }
                $final_results[$customer_id]['rowspan'] = $customer_rowspan;
            }

            if (!empty($final_results)) {
                // unallocated payments
                $query = 'SELECT fr.parent_id as idx, fp.id as id, fp.num as num, (fp.amount-SUM(fr.paid_amount)) AS left_amount, fp.customer as customer' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp ' . "\n" .
                         '  ON (fr.parent_id=fp.id AND fp.annulled_by=0)' . "\n" .
                         'WHERE fp.customer in (' . implode(',', array_keys($final_results)) . ') AND fr.parent_model_name="Finance_Payment"' . "\n" .
                         ' GROUP BY fr.parent_id' . "\n" .
                         ' HAVING left_amount>0';
                $unallocated_payments = $registry['db']->GetAssoc($query);

                foreach ($unallocated_payments as $up) {
                    if (isset($final_results[$up['customer']])) {
                        $final_results[$up['customer']]['unallocated_payments'][] = $up;
                    }
                }
            }

            $final_results['additional_options']['full_paid_offers'] = $full_paid_offers;
            $final_results['additional_options']['partial_paid_offers'] = $partial_paid_offers;
            $final_results['additional_options']['not_paid_offers'] = $not_paid_offers;
            $final_results['additional_options']['main_currency'] = $main_currency;

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>