<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table cellpadding="4" cellspacing="0" class="t_table bordered_cell">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_document_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_total_value#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap" colspan="4"><div class="t_caption_title">{#reports_data#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap" colspan="3"><div class="t_caption_title">{#reports_dates#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_paid#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_left_to_pay#|escape}</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr>
            <td class="t_border hright" nowrap="nowrap" width="25" rowspan="{$result.rowspan}">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border" nowrap="nowrap" rowspan="{$result.rowspan}">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
            </td>
            <td class="t_border" nowrap="nowrap" rowspan="{$result.rowspan}" align="right">
              {$result.total_price|escape|default:"0.00"}
            </td>
            <td class="t_border" nowrap="nowrap" style="color: #888888; background-color: #E5E5E5;">
              {#reports_income_value#}
            </td>
            <td class="t_border" nowrap="nowrap" style="color: #888888; background-color: #E5E5E5;">
              {#reports_paid_money#}
            </td>
            <td class="t_border" nowrap="nowrap" style="color: #888888; background-color: #E5E5E5;">
              {#reports_income_type#}
            </td>
            <td class="t_border" nowrap="nowrap" style="color: #888888; background-color: #E5E5E5;">
              {#reports_faktura_num#}
            </td>
            <td class="t_border" nowrap="nowrap" style="color: #888888; background-color: #E5E5E5;">
              {#reports_faktura_date#}
            </td>
            <td class="t_border" nowrap="nowrap" style="color: #888888; background-color: #E5E5E5;">
              {#reports_falling#}
            </td>
            <td class="t_border" nowrap="nowrap" style="color: #888888; background-color: #E5E5E5;">
              {#reports_paying_date#}
            </td>
            <td class="t_border" nowrap="nowrap" rowspan="{$result.rowspan}" align="right">
              {$result.paid|string_format:"%.2f"|default:"0.00"}
            </td>
            <td nowrap="nowrap" rowspan="{$result.rowspan}" align="right">
              {$result.left_to_pay|string_format:"%.2f"|default:"0.00"}
            </td>
          </tr>
          {foreach from=$result.incomes item=income name=inc}
            <tr{if $income.colored} class="row_pink"{/if}>
              <td class="t_border hright" nowrap="nowrap">
                {$income.paying_value|string_format:"%.2f"|default:"0.00"} {$income.paying_currency|escape|default:"&nbsp;"}
              </td>
              <td class="t_border">
                {$income.take_money_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border">
                {$income.type_income|default:"&nbsp;"}
              </td>
              <td class="t_border">
                {$income.faktura_num|escape|default:"&nbsp;"}
              </td>
              <td class="t_border">
                {$income.faktura_date|date_format:#date_short#|default:"&nbsp;"}
              </td>
              <td class="t_border hright">
                {$income.case_faktura_date|date_format:#date_short#|default:"&nbsp;"}
              </td>
              <td class="t_border">
                {$income.money_date|date_format:#date_short#|default:"&nbsp;"}
              </td>
            </tr>
          {foreachelse}
            <tr>
              <td class="error" colspan="7">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
        {foreachelse}
          <tr>
            <td class="error" colspan="12">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td colspan="2" align="right">
            <strong>{#reports_total_owed#}</strong>
          </td>
          <td style="background-color:#98BCFF;" align="right">
            {$reports_additional_options.total_value|string_format:"%.2f"|default:"0.00"}
          </td>
          <td colspan="7" align="right">
            <strong>{#reports_total_left_to_pay#}</strong>
          </td>
          <td style="background-color:#98BCFF;" align="right">
            {$reports_additional_options.total_paid|string_format:"%.2f"|default:"0.00"}
          </td>
          <td style="background-color:#98BCFF;" align="right">
            {$reports_additional_options.total_left_to_pay|string_format:"%.2f"|default:"0.00"}
          </td>
        </tr>
        <tr>
          <td class="t_footer" colspan="12"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>