<?php
    Class Eset_Task_Working_Time Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $sql['select']  =   'SELECT tt.duration as duration , t.*, ti18n.name as task_name, ' . "\n" . 
                                '  pi18n.name as project_name, ' . "\n" . 
                                '  CONCAT(ui18n.firstname, " ", ui18n.lastname) as employee_name ' . "\n";

            //from clause
            $sql['from'] =  'FROM ' . DB_TABLE_TASKS_TIMESHEETS . ' AS tt' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                            '  ON (ui18n.parent_id=tt.added_by AND ui18n.lang="' . $model_lang . '")' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_TASKS . ' AS t' . "\n" .
                            '  ON (tt.task_id=t.id)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                            '  ON (pi18n.parent_id=t.project AND pi18n.lang="' . $model_lang . '")' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS ti18n' . "\n" .
                            '  ON (ti18n.parent_id=t.id AND ti18n.lang="' . $model_lang . '")';

            $where[] = 't.deleted_by=0';
            $where[] = 't.active!=0';
            $where[] = 't.type!="' . PH_TASK_SYSTEM_TYPE . '"';

            if (! empty($filters['employee'])) {
                $where[] = 'tt.added_by="' . $filters['employee'] . '"';
            }
            if (! empty($filters['from_date'])) {
                $where[] = 'tt.added>="' . $filters['from_date'] . '"';
            }
            if (! empty($filters['to_date'])) {
                $where[] = 'tt.added<="' . $filters['to_date'] . '"';
            }
            if (! empty($filters['status'])) {
                $where[] = 't.status="' . $filters['status'] . '"';
            }

            $sql['where'] = 'WHERE ' . implode($where, ' AND ');

            $sql['order'] = ' ORDER BY t.full_num ASC ' . "\n";

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);

            $final_records = array();
            $total_tasks = 0;
            $total_projects = 0;
            $total_lating_task = 0;
            $total_late_time = 0;
            $total_late_time_hours = 0;
            $total_late_time_minutes = 0;
            $total_working_time = 0;
            $total_working_time_hours = 0;
            $total_working_time_minutes = 0;
            $projects = array();

            $actual_time = General::strftime("%Y-%m-%d %H:%M:%S");

            foreach ($records as $recs) {
                if (! isset($final_records[$recs['id']])) {
                    $final_records[$recs['id']]['id'] = $recs['id'];
                    $final_records[$recs['id']]['full_num'] = $recs['full_num'];
                    $final_records[$recs['id']]['employee_name'] = $recs['employee_name'];
                    $final_records[$recs['id']]['project'] = $recs['project'];
                    if ($recs['project'] && (! in_array($recs['project'], $projects))) {
                        $projects[] = $recs['project'];
                    }
                    $final_records[$recs['id']]['project_name'] = $recs['project_name'];
                    $final_records[$recs['id']]['duration'] = 0;
                    $final_records[$recs['id']]['status'] = $recs['status'];
                    if (!empty ($recs['planned_finish_date']) && ($recs['planned_finish_date'] < $actual_time)) {
                        $final_records[$recs['id']]['late_time'] = intval((strtotime($actual_time) - strtotime($recs['planned_finish_date'])) / 60);
                        $final_records[$recs['id']]['late_hours'] = intval(($final_records[$recs['id']]['late_time'] / 60));
                        $final_records[$recs['id']]['late_minutes'] = intval($final_records[$recs['id']]['late_time'] - $final_records[$recs['id']]['late_hours'] * 60);
                        $total_lating_task++;
                        $total_late_time += $final_records[$recs['id']]['late_time'];
                    } else {
                        $final_records[$recs['id']]['late_time'] = 0;
                        $final_records[$recs['id']]['hours'] = 0;
                        $final_records[$recs['id']]['minutes'] = 0;
                    }
                }
                $final_records[$recs['id']]['duration'] += $recs['duration'];
                $total_working_time += $recs['duration'];
            }

            foreach ($final_records as $key => $fin_rec) {
                $final_records[$key]['duration_hours'] = intval($final_records[$key]['duration'] / 60);
                $final_records[$key]['duration_minutes'] = intval($final_records[$key]['duration'] - $final_records[$key]['duration_hours'] * 60);
            }

            $total_tasks = count($final_records);
            $total_projects = count($projects);

            $total_late_time_hours = intval($total_late_time / 60);
            $total_late_time_minutes = intval($total_late_time - $total_late_time_hours * 60);

            $total_working_time_hours = intval($total_working_time / 60);
            $total_working_time_minutes = intval($total_working_time - $total_working_time_hours * 60);

            $final_records['additional_options']['total_tasks'] = $total_tasks;
            $final_records['additional_options']['total_projects'] = $total_projects;
            $final_records['additional_options']['total_lating_task'] = $total_lating_task;
            $final_records['additional_options']['total_working_time'] = $total_working_time;
            $final_records['additional_options']['total_working_time_hours'] = $total_working_time_hours;
            $final_records['additional_options']['total_working_time_minutes'] = $total_working_time_minutes;
            $final_records['additional_options']['total_late_time'] = $total_late_time;
            $final_records['additional_options']['total_late_time_hours'] = $total_late_time_hours;
            $final_records['additional_options']['total_late_time_minutes'] = $total_late_time_minutes;

            if (!empty($filters['paginate'])) {
                $results = array($final_records, 0);
            } else {
                //no pagination required return only the models
                $results = $final_records;
            }

            return $results;
        }
    }
?>