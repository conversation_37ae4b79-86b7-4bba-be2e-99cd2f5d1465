<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
    <table border="1">
      <tr>
        <td nowrap="nowrap"><strong>{#num#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_article_trademark#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_article_code#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_article_name#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_amount#|escape}</strong></td>
      </tr>
      {counter start=0 name='item_counter' print=false}
      {foreach from=$reports_results key=k name=list_res item=result}
        <tr>
          <td align="right" nowrap="nowrap" width="25">
            {counter name='item_counter' print=true}
          </td>
          <td nowrap="nowrap">
            {$result.article_trademark|escape|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap">
            {$result.article_code|escape|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap">
            {$result.name|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@" align="right" nowrap="nowrap">
            {$result.amount|string_format:"%.2f"|default:"0.00"} {$result.measure|escape|default:"&nbsp;"}
          </td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="5">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>