{if !isset($reports_results.stations_by_city)}
  <h2>{#no_items_found#|escape}</h2>
{else}
  <input type="hidden" name="schedule_date" id="schedule_date" value="{$reports_results.schedule_date|default:'&nbsp;'}">
  <input type="hidden" name="schedule_direction" id="schedule_direction" value="{$reports_results.schedule_direction|default:'&nbsp;'}">
  <input type="hidden" name="schedule_time" id="schedule_time" value="{$reports_results.schedule_time|default:'&nbsp;'}">
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list t_table_transport_schedule">
    <tr class="hcenter reports_title_row">
      <td class="hright tbl_sch_titles" style="vertical-align: middle;" colspan="2"><div style="width:200px;">{#reports_departure_schedules_for_today#|escape}</div></td>
      {foreach from=$reports_results.vehicles item=vehicle}
        <td class="tbl_sch_content hleft" style="vertical-align: middle;">
          {foreach from=$vehicle.schedules item=schedule name=sch}
            <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$schedule.id}" class="sch_link">
              {if $schedule.id eq $vehicle.schedule}<strong>{/if}
              {$schedule.direction_name} ({$schedule.start_time_lbl})
              {if $schedule.id eq $vehicle.schedule}</strong>{/if}
            </a>
            {if !$smarty.foreach.sch.last}
              <br />
            {/if}
          {foreachelse}
            &nbsp;
          {/foreach}
        </td>
      {/foreach}
    </tr>
    <tr class="hcenter reports_title_row">
      <td class="hright tbl_sch_titles" style="vertical-align: middle;" colspan="2"><div style="width:200px;">{#reports_driver#|escape}</div></td>
      {foreach from=$reports_results.vehicles item=vehicle}
        <td class="tbl_sch_content" style="vertical-align: middle;">
          {include file="input_dropdown.html"
            standalone=true
            name='driver'
            value=$vehicle.driver
            options=$reports_results.drivers
            index=$vehicle.id
            eq_indexes=true
          }
        </td>
      {/foreach}
    </tr>
    <tr class="hcenter reports_title_row">
      <td class="hright tbl_sch_titles split_title_cell" style="vertical-align: middle;" colspan="2">
        <div><span class="split_title_bottom">{#reports_city_stop#|escape}</span>
          <span class="split_title_top">{#reports_vehicle#|escape}</span>
          <div class="split_title_line"></div>
        </div>
      </td>
      {foreach from=$reports_results.vehicles item=vehicle}
        <td class="tbl_sch_content tbl_sch_content_bold" style="vertical-align: middle;">
          <strong>{$vehicle.name|escape}</strong>
          {include file='input_hidden.html'
            name='vehicle_schedule'
            eq_indexes=true
            index=$vehicle.id
            standalone=true
            value=$vehicle.schedule
          }
          {include file='input_hidden.html'
            name='vehicle_id'
            eq_indexes=true
            index=$vehicle.id
            standalone=true
            value=$vehicle.id
          }
        </td>
      {/foreach}
    </tr>
    {foreach from=$reports_results.stations_by_city item=city name=ct}
      {foreach from=$city.stations item=station name=stat}
        <tr>
          {if $smarty.foreach.stat.first}
            <td class="hcenter tbl_sch_titles" style="vertical-align: middle;" rowspan="{$city.rowspan}">
              {$city.name|escape}
            </td>
          {/if}
          <td class="hcenter tbl_sch_titles" style="vertical-align: middle;">
            {$station.name|escape}
          </td>
          {foreach from=$reports_results.vehicles item=vehicle}
            <td class="tbl_sch_content">
              <div style="white-space: normal;">
                {foreach from=$reports_results.employees_by_stop[$station.id] item=employee name=emp}
                  <input type="checkbox" name="transport_emp_{$vehicle.id}_{$station.id}[]"
                         {if in_array($employee.id, $reports_results.employees_transported) && !in_array($employee.id, $vehicle.employees)}
                           disabled="disabled"
                         {/if}
                         {if in_array($employee.id, $vehicle.employees)}
                           checked="checked"
                         {/if}
                         id="transport_emp_{$vehicle.id}_{$station.id}_{$smarty.foreach.emp.iteration}"
                         value="{$employee.id}" title="{$employee.full_name|escape}"
                         onchange="processCheckboxEmployees(this)" />
                  <label for="transport_emp_{$vehicle.id}_{$station.id}_{$smarty.foreach.emp.iteration}">
                    {$employee.full_name|escape}
                  </label>
                  {if !$smarty.foreach.emp.last}<br />{/if}
                {/foreach}
              </div>
            </td>
          {/foreach}
        </tr>
      {/foreach}
    {/foreach}
    <tr class="hcenter">
      <td class="hright tbl_sch_titles" style="vertical-align: middle;" colspan="2"><div style="width:200px;">{#reports_capacity#|escape}</div></td>
      {foreach from=$reports_results.vehicles item=vehicle}
        <td id="transport_capacity_container_{$vehicle.id}" class="tbl_sch_content tbl_sch_content_bold" style="vertical-align: middle;">
          <span id="transport_capacity_{$vehicle.id}">{$vehicle.taken}</span> / <span id="transport_max_capacity_{$vehicle.id}">{$vehicle.capacity}</span>
        </td>
      {/foreach}
    </tr>
  </table>

  <div class="buttons_container_rep">
    <button type="button" class="button" onclick="editSchedules(this)">
      {#reports_save_schedule#|escape}
    </button>
    {if $export_permission && !empty($available_patterns)}
      <button type="button" class="button reports_export_button"
              onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;report_type={$report_type}&amp;{$action_param}=export&amp;pattern={$available_patterns.0.id}'">
        {#export_report#|escape}
      </button>
    {/if}
  </div>
{/if}
