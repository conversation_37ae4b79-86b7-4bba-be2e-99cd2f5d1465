<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    public $report_name = '';
    public $drivers = array();
    public $vehicles = array();
    public $cities = array();
    public $stations = array();
    public $employees = array();

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'ajax_save_schedules':
                $this->_saveSchedules();
                break;
            default:
                parent::execute();
        }
    }

    /*
     * Function to create debit note in the selected invoice
     */
    public function _saveSchedules() {
        set_time_limit(0);
        $registry = &$this->registry;
        $this->getReportSettings();

        // get request and all post params
        $request = $registry['request'];

        $request_vars = $request->getAll();
        $vehicles_list = $request->get('vehicle_id');
        $exisiting_schedules = $request->get('vehicle_schedule');
        $drivers = $request->get('driver');
        $schedules = array_fill_keys(array_keys($vehicles_list), array(
            'vehicle'   => 0,
            'schedule'  => 0,
            'driver'    => 0,
            'employees' => array(),
            'date'      => $request->get('schedule_date'),
            'time'      => $request->get('schedule_time'),
            'direction' => $request->get('schedule_direction'),
        ));

        // get the data from the request
        foreach ($schedules as $key => $sch) {
            $schedules[$key]['vehicle'] = $key;
            $schedules[$key]['schedule'] = (isset($exisiting_schedules[$key]) ? $exisiting_schedules[$key] : 0);
            $schedules[$key]['driver'] = (isset($drivers[$key]) ? $drivers[$key] : 0);

            // get the employees
            $employees_included = preg_grep('#^transport_emp_' . $key . '_([0-9]+)$#', array_keys($request_vars));
            foreach ($employees_included as $emp_inc) {
                $schedules[$key]['employees'] = array_merge($schedules[$key]['employees'], array_filter($request->get($emp_inc)));
            }
        }

        // process the schedules
        $registry['db']->StartTrans();
        foreach ($schedules as $sch) {
            if (empty($sch['employees'])) {
                if (!$sch['schedule']) {
                    // do nothing
                    continue;
                }
                // deactivate the schedule
                $this->changeScheduleActive($sch['schedule'], 0);
                continue;
            }

            if (!$sch['driver']) {
                // we need driver to record so skip this iteration
                continue;
            }

            // record the schedule
            $result = $this->editSchedule($sch);
            if (!$result) {
                $registry['db']->FailTrans();
            }
        }

        $operation_result = array(
            'result'   => !$registry['db']->HasFailedTrans(),
            'message'  => ''
        );
        $registry['db']->CompleteTrans();

        if ($operation_result['result']) {
            // set messages
            $this->registry['messages']->setMessage($this->i18n('message_reports_schedule_edited_successfully'));
            $this->registry['messages']->insertInSession($this->registry);
        } else {
            // return error
            $operation_result['message'] = $this->i18n('error_reports_schedule_edit_failed');
        }

        echo(json_encode($operation_result));
        exit;
    }

    /*
     * Function to edit the schedule
     */
    private function editSchedule($schedule_data): bool {
        $result = true;
        $get_old_vars = $this->registry->get('get_old_vars');

        // define the schedule
        $this->registry->set('get_old_vars', true, true);

        $schedule = $this->defineModel($schedule_data);
        $old_model = clone $schedule;

        $action = 'edit';
        if (!$schedule->get('id')) {
            // complete the default type data for the new document
            $doc_type = Documents_Types::searchOne($this->registry, [
                'where'      => ["dt.id = " . DOC_SCHEDULE_TYPE ],
                'model_lang' => $this->registry['lang']
            ]);
            $schedule->set('customer', $doc_type->get('default_customer'), true);
            $schedule->set('name', $doc_type->get('default_name'), true);
            $schedule->set('group', $doc_type->get('default_group'), true);
            $schedule->set('department', $doc_type->get('default_department'), true);
            $action = 'add';
        }

        if ($schedule->get('update_employee_id')) {
            $schedule->set('employee', $this->registry['currentUser']->get('employee'), true);
            $schedule->unsetProperty('update_employee_id', true);
        }

        // get the vehicles, stops and drivers data (will need it later)
        if (empty($this->vehicles)) {
            // include the report class
            require_once PH_MODULES_DIR . "reports/plugins/" . $this->report_name . "/custom.report.query.php";
            $report_name_elements = explode('_', $this->report_name);
            foreach ($report_name_elements as $key => $element) {
                $report_name_elements[$key] = ucfirst($element);
            }
            $report_class_name = implode ('_', $report_name_elements);
            $report_class_name::$registry = $this->registry;
            $this->vehicles = $report_class_name::getVehicles();
            $this->drivers = $report_class_name::getDrivers();
            $this->cities = $report_class_name::getRouteCities($schedule_data['direction']);
            $this->stations = $report_class_name::getRouteStations(array_keys($this->cities), $schedule_data['direction']);
            $this->employees = $report_class_name::getEmployeesList(array_keys($this->stations));
        }

        // get the cars of the schedule
        $assoc_vars = $schedule->getAssocVars();

        // complete the data which is outside the grouping table
        $schedule->set('date', $schedule_data['date'], true);
        $assoc_vars[DOC_SCHEDULE_VAR_DIRECTION]['value'] = $schedule_data['direction'];
        $assoc_vars[DOC_SCHEDULE_VAR_START_TIME]['value'] = $schedule_data['time'];
        $assoc_vars[DOC_SCHEDULE_VAR_VEHICLE]['value'] = $schedule_data['vehicle'];
        $assoc_vars[DOC_SCHEDULE_VAR_VEHICLE_NAME]['value'] = $this->vehicles[$schedule_data['vehicle']]['name'];
        $assoc_vars[DOC_SCHEDULE_VAR_CAPACITY]['value'] = $this->vehicles[$schedule_data['vehicle']]['capacity'];
        $assoc_vars[DOC_SCHEDULE_VAR_DRIVER]['value'] = $schedule_data['driver'];
        $assoc_vars[DOC_SCHEDULE_VAR_DRIVER_NAME]['value'] = $this->drivers[$schedule_data['driver']]['label'];

        // PREPARE THE DATA FOR THE GROUPING TABLE
        // get all the vars in the grouping table
        $grouping_index = !empty($assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE]['grouping']) ? $assoc_vars[DOC_SCHEDULE_VAR_EMPLOYEE]['grouping'] : '';
        $vars_included = array();
        if ($grouping_index) {
            foreach ($assoc_vars as $var_nm => $var_dat) {
                if ($var_dat['grouping'] == $grouping_index && $var_dat['type'] != 'group') {
                    $vars_included[] = $var_nm;
                }
            }
        }

        // clear the currently completed values
        foreach ($vars_included as $var_gr) {
            $assoc_vars[$var_gr]['value'] = [];
        }

        // complete the new values
        foreach ($schedule_data['employees'] as $row_idx => $employee) {
            $employee_station = $this->employees[$employee]['station'];
            $employee_city = $this->stations[$employee_station]['city'];
            foreach ($vars_included as $var_inc) {
                $new_val = '';
                switch ($var_inc) {
                    case DOC_SCHEDULE_VAR_EMPLOYEE:
                        $new_val = $employee;
                        break;
                    case DOC_SCHEDULE_VAR_EMPLOYEE_NAME:
                        $new_val = $this->employees[$employee]['full_name'];
                        break;
                    case DOC_SCHEDULE_VAR_STOP:
                        $new_val = $employee_station;
                        break;
                    case DOC_SCHEDULE_VAR_STOP_NAME:
                        $new_val = $this->stations[$employee_station]['name'];
                        break;
                    case DOC_SCHEDULE_VAR_CITY:
                        $new_val = $employee_city;
                        break;
                    case DOC_SCHEDULE_VAR_CITY_NAME:
                        $new_val = $this->cities[$employee_city]['name'];
                        break;
                    default:
                        break;
                }
                $assoc_vars[$var_inc]['value'][$row_idx] = $new_val;
            }
        }

         $schedule->set('vars', array_values($assoc_vars), true);

        // try to save the document
        if ($schedule->save()) {
            $filters = array('where'      => array('d.id = ' . $schedule->get('id')),
                             'model_lang' => $schedule->get('model_lang'));
            $new_document = Documents::searchOne($this->registry, $filters);
            $new_document->getVars();

            // write history
            Documents_History::saveData($this->registry, array('schedule'    => $new_document,
                                                               'action_type' => $action,
                                                               'new_model'   => $new_document,
                                                               'old_model'   => $old_model));
        } else {
            $result = false;
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);
        return $result;
    }

    /*
     * Function to define the model which will be changed
     */
    private function defineModel($schedule_data): object {
        if ($schedule_data['schedule']) {
            return Documents::searchOne(
                $this->registry,
                array('where' => array("d.id = '{$schedule_data['schedule']}'"))
            );
        }

        // search for existing but deactivated model
        $deactivated_model = Documents::searchOne($this->registry, [
            'where' => [
                "d.type=" . DOC_SCHEDULE_TYPE,
                "d.date = '{$schedule_data['date']}'",
                "d.active=0",
                "a__" . DOC_SCHEDULE_VAR_VEHICLE . " = '{$schedule_data['vehicle']}'" ,
                "a__" . DOC_SCHEDULE_VAR_DRIVER . " = '{$schedule_data['driver']}'" ,
                "a__" . DOC_SCHEDULE_VAR_START_TIME . " = '{$schedule_data['time']}'"
            ],
            'model_lang' => $this->registry['lang'],
        ]);

        if ($deactivated_model) {
            // activate the model and return it
            $model = $this->changeScheduleActive($deactivated_model->get('id'));
            $model->set('update_employee_id', true, true);
            return $model;
        }

        return new Document($this->registry, array('type' => DOC_SCHEDULE_TYPE, 'update_employee_id' => true));
    }

    /*
     * Function to change the activity of the document
     */
    private function changeScheduleActive($document_id, $activity=1): ? Document {
        //activate/deactivate
        if (!Documents::changeStatus($this->registry, array($document_id), ($activity ? 'activate' : 'deactivate'))) {
            return null;
        }

        $filters_sch = array(
            'where'                  => array("d.id = '{$document_id}'"),
            'model_lang'             => $this->registry['lang'],
            'skip_assignments'       => true,
            'skip_permissions_check' => true
        );
        $schedule = Documents::searchOne($this->registry, $filters_sch);

        Documents_History::saveData(
            $this->registry,
            array(
                'model'       => $schedule,
                'action_type' => ($activity ? 'activate' : 'deactivate')
            )
        );
        return $schedule;
    }


    /**
     * @return void
     */
    private function getReportSettings(): void {
        $report = $this->getReportType();

        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $this->report_name = $report['name'];

        //load plugin i18n files
        $i18n_files = array();
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $this->report_name,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');

        // Load the documents main i18n file
        $i18n_files[] = sprintf('%s%s%s%s',
            PH_MODULES_DIR,
            'documents/i18n/',
            $this->registry['lang'],
            '/documents.ini');
        $this->registry['translater']->loadFile($i18n_files);

        Reports::getReportSettings($this->registry, $this->report_name);
    }
}

?>
