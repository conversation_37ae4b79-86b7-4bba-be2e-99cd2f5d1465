<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_email_content':
                $this->_email_content();
                break;
            case 'ajax_svg_img':
                $this->_svgImg();
                break;
            case 'ajax_select_customer_contracts':
                $this->_selectCustomerContracts();
                break;
            case 'ajax_select_contract_period':
                $this->_selectContractPeriod();
                break;
            case 'ajax_generate_summary_protocol':
                $this->_generateSummaryProtocol();
                break;
            case 'generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    public function _selectCustomerContracts() {
        $registry = $this->registry;
        $request = $registry['request'];
        $selected_report = $request->get('selected_report');
        require_once PH_MODULES_DIR . "reports/plugins/" . $selected_report . "/custom.report.query.php";

        $report = Reports::getReports($registry, array('name' => $selected_report));
        Reports::getReportSettings($registry, $selected_report);
        $report = $report[0];

        // form the name of the required report class
        $report_name_elements = explode('_', $report->get('type'));
        foreach ($report_name_elements as $key => $element) {
            $report_name_elements[$key] = ucfirst($element);
        }
        $report_class_name = implode ('_', $report_name_elements);

        $filters = array(
            'customer' => $request->get('selected_customer')
        );
        $contracts_data = $report_class_name::getRequiredContracts($this->registry, $filters);

        $results = array_values($contracts_data);
        if (count($results)==1) {
            $results[0] = $report_class_name::definePeriods($this->registry, $results[0]);
        }

        echo json_encode($results);
        exit;
    }

    public function _selectContractPeriod() {
        $registry = $this->registry;
        $request = $registry['request'];
        $filters = array();
        $filters['customer'] = $request->get('selected_customer');
        $filters['id'] = $request->get('selected_contract');
        $selected_report = $request->get('selected_report');

        require_once PH_MODULES_DIR . "reports/plugins/" . $selected_report . "/custom.report.query.php";
        $report = Reports::getReports($registry, array('name' => $selected_report));
        Reports::getReportSettings($registry, $selected_report);
        $report = $report[0];

        // form the name of the required report class
        $report_name_elements = explode('_', $report->get('type'));
        foreach ($report_name_elements as $key => $element) {
            $report_name_elements[$key] = ucfirst($element);
        }
        $report_class_name = implode ('_', $report_name_elements);

        $report = Reports::getReports($registry, array('name' => $selected_report));
        Reports::getReportSettings($registry, $selected_report);
        $report = $report[0];

        $contracts_data = $report_class_name::getRequiredContracts($this->registry, $filters);

        $contract_result = reset($contracts_data);
        $contracts_data = $report_class_name::definePeriods($this->registry, $contract_result, $request->get('include_invoiced'));

        echo json_encode($contracts_data['periods']);
        exit;
    }

    public function _generateSummaryProtocol() {
        $registry = $this->registry;
        $request = $registry['request'];

        $quantities = $request->get('quantity');
        $prices = $request->get('price');
        $articles = $request->get('articles');
        $measures = $request->get('measures');
        $included_gt2_rows = array();
        $affected_month_rows = array();
        $operation_result = array(
            'result'       => false,
            'redirect_url' => false,
            'messages'     => array()
        );

        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        $i18n_files = array();
        // load plugin i18n files
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        // load i18n files for documents
        $i18n_files[] = sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $registry['lang'], '/documents.ini');
        $registry['translater']->loadFile($i18n_files);

        Reports::getReportSettings($registry, $report);

        // clear gt2 rows without quantity
        foreach ($quantities as $key => $quantity) {
            if ((float)$quantity) {
                $included_gt2_rows[] = $key;
            }
        }

        if (!empty($included_gt2_rows)) {
            // prepare the summary protocol document
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

            // new protocol have to be added
            $filters_type = array('where' => array('dt.id = ' . SUMMARY_PROTOCOL,
                                                   'dt.active = 1'),
                                  'sanitize'   => true,
                                  'model_lang' => $registry['lang']);
            $document_type = Documents_Types::searchOne($registry, $filters_type);

            // prepare date
            $period_year_month = $request->get('selected_period') . '-01';
            $document_date = General::strftime('%Y-%m-%d', strtotime('+1 month -1 day', strtotime($period_year_month)));

            // get the needed extra data from the contract
            $add_vars_contract = array(CONTRACT_DEADLINE_COUNT, CONTRACT_DEADLINE_TYPE, CONTRACT_BANK_ACCOUNT,
                                       'total_vat_rate', 'currency', 'total_no_vat_reason', 'total_no_vat_reason_text');
            $sql = 'SELECT fm.name, c_cstm.value' . "\n" .
                   'FROM contracts as c' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                   ' ON (fm.model="Contract" AND fm.model_type="' . CONTRACT_ID . '" AND fm.name IN ("' . implode('","', $add_vars_contract) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm' . "\n" .
                   '  ON (c_cstm.model_id=c.id AND c_cstm.var_id=fm.id)' . "\n" .
                   'WHERE c.id="' . $request->get('selected_contract') . '"' . "\n";
            $vars_data = $registry['db']->GetAssoc($sql);

            // start preparing the model
            $new_protocol = '';
            if ($request->get('edit_protocol')) {
                // edit existing model
                $filters = array('where'      => array('d.id = ' . $request->get('edit_protocol')),
                                 'model_lang' => $registry->get('lang'));
                $new_protocol = Documents::searchOne($registry, $filters);
            } else {
                // create new model
                $document = Documents::buildModel($registry);

                $document->set('name', $document_type->get('default_name'), true);
                $document->set('type', SUMMARY_PROTOCOL, true);
                $document->set('department', $registry['currentUser']->get('default_department'), true);
                $document->set('group', $registry['currentUser']->get('default_group'), true);
                $document->set('office', $registry['currentUser']->get('office'), true);
                $document->set('employee', $registry['currentUser']->get('employee'), true);
                $document->set('customer', $request->get('selected_customer'), true);
                $document->set('contract', $request->get('selected_contract'), true);
                $document->set('active', 1, true);
                $document->set('date', $document_date, true);

                $registry->set('get_old_vars', true, true);
                $document->getVars();
                $registry->set('get_old_vars', false, true);

                // get all the vars
                $vars_list = $document->get('vars');

                // find the needed var
                foreach ($vars_list as $var_idx => $vl) {
                    if ($vl['name'] == DOCUMENT_DEADLINE_COUNT) {
                        $vars_list[$var_idx]['value'] = isset($vars_data[CONTRACT_DEADLINE_COUNT]) ? $vars_data[CONTRACT_DEADLINE_COUNT] : '';
                    } elseif ($vl['name'] == DOCUMENT_DEADLINE_TYPE) {
                        $vars_list[$var_idx]['value'] = isset($vars_data[CONTRACT_DEADLINE_TYPE]) ? $vars_data[CONTRACT_DEADLINE_TYPE] : '';
                    } elseif ($vl['name'] == DOCUMENT_BANK_ACCOUNT) {
                        $vars_list[$var_idx]['value'] = isset($vars_data[CONTRACT_BANK_ACCOUNT]) ? $vars_data[CONTRACT_BANK_ACCOUNT] : '';
                    }
                }
                $document->set('vars', $vars_list, true);

                $registry['db']->StartTrans();
                if ($document->save()) {
                    $filters = array('where' => array('d.id = ' . $document->get('id')),
                                     'model_lang' => $document->get('model_lang'));
                    $new_protocol = Documents::searchOne($registry, $filters);
                    $old_model = new Document($registry);
                    $old_model->sanitize();
                    Documents_History::saveData($registry, array('model' => $document, 'action_type' => 'add', 'new_model' => $new_protocol, 'old_model' => $old_model));
                } else {
                    $operation_result['result'] = false;
                    $operation_result['messages'][] = $registry['translater']->translate('error_reports_add_summary_protocol_failed');
                    foreach ($document->registry['messages']->getErrors() as $error) {
                        $operation_result['messages'][] = $error;
                    }
                    $registry['db']->FailTrans();
                }
            }

            if (!$registry['db']->HasFailedTrans() && $new_protocol) {
                $registry->set('get_old_vars', true, true);
                $new_protocol->getVars();
                $registry->set('get_old_vars', false, true);
                $old_protocol = clone $new_protocol;
                $old_protocol->sanitize();

                // get all the vars
                $vars_list = $new_protocol->get('vars');
                $gt2_vars = '';

                // find the needed var
                foreach ($vars_list as $var_idx => $vl) {
                    if ($vl['type'] == 'gt2') {
                        $gt2_vars = $vl;
                        break;
                    }
                }

                // delete old rows
                if ($request->get('edit_protocol')) {
                    foreach ($gt2_vars['values'] as $row_id => $row_data) {
                        $gt2_vars['values'][$row_id]['deleted'] = 1;
                    }
                }

                // get all the groups
                $groups_info = $request->get('groups');
                $summary_protocol_rows = $request->get('summary');
                $included_rows = array();
                foreach ($groups_info as $group => $group_gt2_rows) {
                    $current_group_rows = array_intersect($included_gt2_rows, $group_gt2_rows);
                    if (count(array_intersect($included_gt2_rows, $group_gt2_rows))) {
                        $affected_month_rows = array_merge($affected_month_rows, $group_gt2_rows);

                        // create the new rows
                        $new_gt2_row = array(
                            'article_id'           => $summary_protocol_rows[$group]['article_id'],
                            'article_name'         => $summary_protocol_rows[$group]['article_name'],
                            'free_field1'          => $summary_protocol_rows[$group]['branch'],
                            'free_field2'          => $summary_protocol_rows[$group]['branch_name'],
                            'article_barcode'      => $summary_protocol_rows[$group]['office'],
                            'article_measure_name' => $summary_protocol_rows[$group]['measure'],
                            'quantity'             => 1,
                            'price'                => $summary_protocol_rows[$group]['subtotal'],
                            'subtotal'             => $summary_protocol_rows[$group]['subtotal'],
                            'free_field3'          => $summary_protocol_rows[$group]['quantity'],
                            'free_field4'          => $summary_protocol_rows[$group]['price'],
                            'free_field5'          => ''
                        );

                        // construct the related rows array to keep the relations
                        $relations = array();
                        foreach ($current_group_rows as $cgr) {
                            $relations[$cgr]['article_id'] = $articles[$cgr];
                            $relations[$cgr]['quantity'] = $quantities[$cgr];
                            $relations[$cgr]['article_measure_name'] = $measures[$cgr];
                            $relations[$cgr]['price'] = $prices[$cgr];

                            $included_rows[] = $cgr;
                        }

                        $new_gt2_row['free_field5'] = json_encode($relations);
                        $gt2_vars['values'][] = $new_gt2_row;
                    }
                }

                // inherit the vat rate, currency and no vat reason from the contract
                $gt2_vars['plain_values']['total_vat_rate'] = $vars_data['total_vat_rate'];
                $gt2_vars['plain_values']['currency'] = $vars_data['currency'];
                $gt2_vars['plain_values']['total_no_vat_reason'] = $vars_data['total_no_vat_reason'];
                $gt2_vars['plain_values']['total_no_vat_reason_text'] = $vars_data['total_no_vat_reason_text'];

                // set the new values in the document
                $new_protocol->set('grouping_table_2', $gt2_vars, true);
                $new_protocol->calculateGT2();
                $new_protocol->set('table_values_are_set', true, true);

                // record the gt2
                if ($new_protocol->saveGT2Vars()) {
                    // get all protocols to change the GT2 table
                    $sql = 'SELECT `model_id` FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE `id` IN ("' . implode('","', $included_rows) . '")';
                    $protocol_rows_to_change = $registry['db']->GetCol($sql);

                    // record relations
                    $new_protocol->set('referers', array_unique($protocol_rows_to_change));
                    $new_protocol->updateRelatives();

                    // GET THE CHANGED DOCUMENT
                    $filters = array('where'      => array('d.id="' . $new_protocol->get('id') . '"'),
                                     'model_lang' => $registry['lang']
                    );
                    $updated_document = Documents::searchOne($registry, $filters);

                    $registry->set('get_old_vars', true, true);
                    $updated_document->getVars();
                    $registry->set('get_old_vars', false, true);

                    Documents_History::saveData($registry, array('model' => $new_protocol, 'action_type' => 'edit', 'new_model' => $updated_document, 'old_model' => $old_protocol));


                    if (!empty($protocol_rows_to_change) && !$request->get('edit_protocol')) {
                        // ONLY IF THERE ARE MONTH PROTOCOLS AND IF A SUMMARY PROTOCOL IS NOT EDITED
                        // change the status of the existing protocols
                        $filters_small_protocols = array('where'      => array('d.id IN ("' . implode('","', $protocol_rows_to_change) . '")'),
                                                         'model_lang' => $updated_document->get('model_lang'));
                        $small_protocols = Documents::search($registry, $filters_small_protocols);

                        foreach ($small_protocols as $sm_protocol) {
                            $old_sm_protocol = clone $sm_protocol;

                            // prepare changing of status
                            $sm_protocol->set('status', 'closed', true);
                            $sm_protocol->set('substatus', '0', true);

                            if ($sm_protocol->setStatus()) {
                                $filters_new_sm_protocol = array('where' => array('d.id = ' . $sm_protocol->get('id')),
                                    'model_lang' => $sm_protocol->get('model_lang'));
                                $new_sm_protocol = Documents::searchOne($registry, $filters_new_sm_protocol);

                                // write history
                                Documents_History::saveData($registry, array('model' => $sm_protocol, 'action_type' => 'status', 'new_model' => $new_sm_protocol, 'old_model' => $old_sm_protocol));
                            }
                        }
                    }

                    // edit the month protocols
                    $filters_mnth_protocols = array('where'      => array('d.id IN ("' . implode('","', $protocol_rows_to_change) . '")'),
                                                    'model_lang' => $registry['lang']);
                    $month_protocols = Documents::search($registry, $filters_mnth_protocols);

                    foreach ($month_protocols as $month_prot) {
                        $registry->set('get_old_vars', true, true);
                        $month_prot->getVars();
                        $registry->set('get_old_vars', false, true);
                        $old_protocol = clone $month_prot;
                        $old_protocol->sanitize();

                        // get all the vars
                        $vars_list = $month_prot->get('vars');
                        $gt2_vars = '';

                        // find the needed var
                        foreach ($vars_list as $var_idx => $vl) {
                            if ($vl['type'] == 'gt2') {
                                $gt2_vars = $vl;
                                break;
                            }
                        }

                        foreach ($gt2_vars['values'] as $key => $val) {
                            $protocol_quantity = 0;

                            if ($val['quantity'] != $quantities[$key]) {
                                if ((int)$val['article_measure_name'] == MEASURE_PERCENT) {
                                    $protocol_quantity = (100 * $prices[$key])/$quantities[$key];
                                } else if ((int)$val['article_measure_name'] == MEASURE_PROMILLE) {
                                    $protocol_quantity = (1000 * $prices[$key])/$quantities[$key];
                                } else if ($val['article_id'] == ARTICLE_SAFE_KEEPING) {
                                    $protocol_quantity = $quantities[$key]*floatval($val['free_field3']);
                                } else {
                                    $protocol_quantity = $quantities[$key];
                                }
                            } else {
                                $protocol_quantity = $val['quantity'];
                            }

                            $val['quantity'] = $quantities[$key];
                            $val['price'] = $prices[$key];
                            $val['free_field4'] = $protocol_quantity;

                            // get the article for the invoice
                            foreach ($groups_info as $gr_idx => $grouped_rows) {
                                if (in_array($val['id'], $grouped_rows)) {
                                    $val['free_text1'] = $summary_protocol_rows[$gr_idx]['article_name'];
                                    $val['free_text3'] = $summary_protocol_rows[$gr_idx]['quantity'];
                                    $val['free_text4'] = $summary_protocol_rows[$gr_idx]['price'];
                                    break;
                                }
                            }
                            $gt2_vars['values'][$key] = $val;
                        }

                        $month_prot->set('grouping_table_2', $gt2_vars, true);
                        $month_prot->calculateGT2();
                        $month_prot->set('table_values_are_set', true, true);
                        $month_prot->unsanitize();

                        if ($month_prot->saveGT2Vars()) {
                            // GET THE CHANGED DOCUMENT
                            $filters = array('where'      => array('d.id="' . $month_prot->get('id') . '"'),
                                             'model_lang' => $registry['lang']
                            );
                            $updated_protocol = Documents::searchOne($registry, $filters);

                            $registry->set('get_old_vars', true, true);
                            $updated_protocol->getVars();
                            $registry->set('get_old_vars', false, true);

                            Documents_History::saveData($registry, array('model' => $month_prot, 'action_type' => 'edit', 'new_model' => $updated_protocol, 'old_model' => $old_protocol));
                        } else {
                            $operation_result['result'] = false;
                            $operation_result['messages'][] = $registry['translater']->translate('error_correcting_related_month_protocols');
                            $registry['db']->FailTrans();
                        }
                    }

                    // redirect to the summary protocol
                    $operation_result['result'] = true;
                    $operation_result['redirect_url'] = sprintf('%s?%s=%s&%s=%s&%s=%d', $_SERVER['PHP_SELF'], $registry->get('module_param'), 'documents', 'documents', 'view', 'view', $updated_document->get('id'));
                } else {
                    $operation_result['result'] = false;
                    $operation_result['messages'][] = $registry['translater']->translate('error_report_protocol_recording_gt2_failed');
                    $registry['db']->FailTrans();
                }
            }

            $registry['db']->CompleteTrans();
        } else {
            $operation_result['result'] = false;
            $operation_result['messages'][] = $this->i18n('error_complete_at_least_one_row_with_quantity');
        }

        echo json_encode($operation_result);
        exit;
    }
}

?>
