<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    public function customCreateModel(&$settings) {

        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        //check permissions to add document of this type
        $type = $settings['create_type'];
        $type_permission = $this->checkActionPermissions(General::singular2Plural($settings['create_model']) . $type, 'add');

        $error = !$type_permission;

        $method = 'create' . ucfirst($settings['create_model']);
        if (!$error && method_exists($this, $method)) {
            require_once PH_MODULES_DIR . 'reports/plugins/' . $report . '/custom.report.query.php';
            $report_factory = preg_replace('#\s+#', '_', ucwords(preg_replace('#_#', ' ', $report)));

            if (method_exists($report_factory, 'prepareModelData')) {
                //prepares properties of requested model
                $record = $report_factory::prepareModelData($this->registry);

                //no items selected
                if (empty($record)) {
                    $error = true;
                } else {
                    //creates the requested model (document, finance)
                    $model = $this->$method($record, $settings);

                    if ($model && $model->get('id')) {
                        $filters = array('where' => array('d.id = ' . $model->get('id')),
                                         'model_lang' => $model->get('model_lang'),
                                         'skip_assignments' => true,
                                         'skip_permissions_check' => true);
                        $new_model = Documents::searchOne($this->registry, $filters);
                        $old_model = new Document($this->registry);
                        $old_model->sanitize();

                        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

                        $audit_parent = Documents_History::saveData($this->registry,
                                                                    array('model' => $model,
                                                                          'action_type' => 'add',
                                                                          'new_model' => $new_model,
                                                                          'old_model' => $old_model));

                        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');

                        //redirect to model and display success message
                        $this->registry['messages']->setMessage($this->i18n('message_documents_add_success', array($new_model->getModelTypeName())), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        $this->redirect('documents', 'edit', array('edit' => $model->get('id')));

                    } else {
                        $error = true;
                    }
                }
            } else {
                $error = true;
            }
        } else {
            $error = true;
        }

        if ($error) {
            //redirect back to report and display error message
            $this->registry['messages']->setError($this->i18n('error_reports_create_equipment_maintenance_protocol'), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, '', array('report_type' => $report));
        }
    }
}

?>
