<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('CATEGORIES_NAME', 'purpose_unit');

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'required'  => 1,
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date_help')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'required'  => 1,
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date_help')
            );
            $filters['to_date'] = $filter;

            // DEFINE TRADEMARK AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'trademarks',
                'name'              => 'trademarks',
                'type'              => 'custom_filter',
                'actual_type'       => 'autocompleter',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'custom_buttons'    => 'search',
                'width'             => 222,
                'label'             => $this->i18n('reports_trademarks'),
                'help'              => $this->i18n('reports_trademarks'),
                'autocomplete'      => array('search' => array('<name>', '<customer_name>'),
                                  'sort'         => array('<name>', '<customer_name>'),
                                  'type'         => 'nomenclatures',
                                  'clear'        => 1,
                                  'suggestions'  => '<name> [<customer_name>]',
                                  'buttons_hide' => 'search',
                                  'id_var'       => 'trademarks',
                                  'fill_options' => array('$trademarks => <trademark>',
                                                          '$trademarks_autocomplete => <name> [<customer_name>]',
                                                         ),
                                  'filters'      => array('<type_keyword>' => 'trademark',
                                                          '<customer_trademark>' => '1'),
                                  'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
                )
            );
            $filters['trademarks'] = $filter;

            //DEFINE CATEGORIES FILTER
            //prepare options
            $sql_categories = 'SELECT label, option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE parent_name="' . CATEGORIES_NAME . '" AND lang="' . $registry['lang'] . '" ORDER BY position ASC';
            $categories_options = $registry['db']->getAll($sql_categories);

            //prepare filters
            $filter = array (
                'custom_id' => 'categories',
                'name'      => 'categories',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_categories'),
                'help'      => $this->i18n('reports_categories'),
                'do_not_show_check_all_button' => true,
                'options'   => $categories_options
            );
            $filters['categories'] = $filter;

            // DEFINE REPORT TYPE
            //prepare filters
            $filter = array (
                'custom_id' => 'report_for',
                'name'      => 'report_for',
                'type'      => 'radio',
                'required'  => 1,
                'label'     => $this->i18n('reports_report_for'),
                'help'      => $this->i18n('reports_report_for'),
                'options'   => array(
                    array(
                        'label'         => $this->i18n('reports_type_daily'),
                        'option_value'  => 'daily'
                    ),
                    array(
                        'label'         => $this->i18n('reports_type_weekly'),
                        'option_value'  => 'weekly'
                    ),
                    array(
                        'label'         => $this->i18n('reports_type_monthly'),
                        'option_value'  => 'monthly'
                    ),
                )
            );
            $filters['report_for'] = $filter;

            // DEFINE SHOW EVENTS FILTER
            //prepare filters
            $filter = array (
                'custom_id' => 'show_events',
                'name'      => 'show_events',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_show_events'),
                'help'      => $this->i18n('reports_show_events'),
                'options'   => array(
                    array(
                        'label'         => '',
                        'option_value'  => 'show_event'
                    )
                )
            );
            $filters['show_events'] = $filter;

            return $filters;
        }
    }
?>