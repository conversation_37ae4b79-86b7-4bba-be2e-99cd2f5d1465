<?php

class Custom_Report_Filters extends Report_Filters
{
    private static $registry;

    function defineFilters(&$registry)
    {
        self::$registry = $registry;
        $settings = Reports::getReportSettings($registry);
        $db = $registry['db'];
        $filters['manufacturer_ids'] = [
            'type' => 'hidden',
            'name' => 'manufacturer_ids',
            'hidden' => true,
        ];

        $filters['subreport_type'] = [
            'custom_id' => 'subreport_type',
            'name' => 'subreport_type',
            'type' => 'dropdown',
            'required' => true,
            'label' => $this->i18n('reports_subreport_type'),
            'custom_class' => "primary_filter",
            "onchange" => "updateFilters(this)",
            'options' => [
                [
                    'label' => $registry['translater']->translate('reports_subreport_type_main'),
                    'option_value' => 'main',
                ],
                [
                    'label' => $registry['translater']->translate('reports_subreport_type_individual'),
                    'option_value' => 'individual',
                ]
            ],
        ];

        $filters['device_type_main'] = [
            'name' => 'device_type_main',
            'custom_id' => 'device_type_main',
            'type' => 'checkbox_group',
            'options' => self::getDeviceTypeOptions($settings['device_types_main']),
            'custom_class' => "main_filter filter",
            'hidden' => true,
            'width' => '64',
            'label' => $this->i18n('device_type'),
        ];

        $filters['device_type_individual'] = [
            'name' => 'device_type_individual',
            'custom_id' => 'device_type_individual',
            'type' => 'checkbox_group',
            'options' => self::getDeviceTypeOptions($settings['device_types_individual']),
            'custom_class' => "individual_filter filter",
            'hidden' => true,
            'width' => '64',
            'label' => $this->i18n('device_type'),
        ];

        $filters['serial_num'] = [
            'custom_id' => 'serial_num',
            'name' => 'serial_num',
            'type' => 'text',
            'label' => $this->i18n('serial_num'),
            'custom_class' => "primary_filter",
        ];

        $sql_nom_status = 'SELECT fo.label as label, fo.option_value as option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . ' as fo WHERE fo.parent_name = "nom_status" AND fo.lang="' . $registry['lang'] . '" ORDER BY fo.id ASC';
        $nom_status_options = $db->getAll($sql_nom_status);

        $sql_appliances_report_type = 'SELECT fo.label as label, fo.option_value as option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . ' as fo WHERE fo.parent_name = "appliances_report_type" AND fo.lang="' . $registry['lang'] . '" ORDER BY fo.id ASC';
        $appliances_type_options = $db->getAll($sql_appliances_report_type);

        $filters['manufacturer'] = [
            'custom_id'         => 'manufacturer',
            'name'              => 'manufacturer',
            'type'              => 'autocompleter',
            'width'             => 222,
            'label'             => $this->i18n('manufacturer'),
            'help'              => $this->i18n('manufacturer'),
            'custom_class' => "individual_filter filter",
            'hidden' => true,
            'autocomplete'      => [
                'search'       => [
                    '<name>'
                ],
                'sort'         => [
                    '<active> DESC',
                    '<name>'],
                'type'         => 'nomenclatures',
                'clear'        => 1,
                'suggestions'  => '<name>',
                'buttons_hide' => 'search',
                'id_var'       => 'manufacturer',
                'fill_options' => [
                    '$manufacturer => <id>',
                    '$manufacturer_autocomplete => <name>',
                ],
                'filters' => [
                    '<type>'    => '25'
                ],
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select'),
            ]
        ];

        $filters['status'] = [
            'name' => 'status',
            'custom_id' => 'status',
            'type' => 'dropdown',
            'options' => $nom_status_options,
            'width' => '64',
            'label' => $this->i18n('reports_nom_status'),
            'custom_class' => "primary_filter",
        ];

        $filters['report_method'] = [
            'name' => 'report_method',
            'custom_id' => 'report_method',
            'type' => 'dropdown',
            'options' => $appliances_type_options,
            'width' => '64',
            'label' => $this->i18n('report_method'),
            'custom_class' => "individual_filter filter",
            'hidden' => true,
        ];


        $filters['user_station'] = [
            'custom_id'         => 'user_station',
            'name'              => 'user_station',
            'type'              => 'autocompleter',
            'label'             => $this->i18n('user_station'),
            'custom_class' => "primary_filter",
            'autocomplete'      =>
                array(
                    'search' => array('<name>','<a__psiro_code>'),
                'sort'         => array('<name>'),
                'type'         => 'nomenclatures',
                'clear'        => 1,
                'suggestions'  => '[<a__psiro_code>] <name> ',
                'buttons_hide' => 'search',
                'id_var'       => 'user_station_id',
                'fill_options' => array('$user_station => <id>',
                    '$user_station_autocomplete => [<a__psiro_code>] <name>',
                ),
                'filters' => [
                    '<type>'    => '15'
                ],
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
            )
        ];
        $filters['residence'] = [
            'custom_id'         => 'residence',
            'name'              => 'residence',
            'type'              => 'autocompleter',
            'width'             => 222,
            'label'             => $this->i18n('residence'),
            'help'              => $this->i18n('residence'),
            'custom_class' => "individual_filter filter",
            'hidden' => true,
            'autocomplete'      => [
                'search'       => [
                    '<a__building>',
                    '<a__entrance>',
                    '<name>'
                ],
                'sort'         => [
                    '<active> DESC',
                    '<name>'],
                'type'         => 'nomenclatures',
                'clear'        => 1,
                'suggestions'  => sprintf($this->i18n('residence_suggestion'),'<a__building>', '<a__entrance>' ,'<name>'),
                'buttons_hide' => 'search',
                'id_var'       => 'residence',
                'fill_options' => [
                    '$residence => <id>',
                    '$residence_autocomplete => '. sprintf($this->i18n('residence_suggestion'),'<a__building>', '<a__entrance>' ,'<name>'),
                ],
                'filters' => [
                    '<type>'    => '11'
                ],
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
            ]
        ];

        $filters['lot'] = [
            'custom_id'         => 'lot',
            'name'              => 'lot',
            'type'              => 'autocompleter',
            'width'             => 222,
            'label'             => $this->i18n('lot'),
            'help'              => $this->i18n('lot'),
            'custom_class' => "individual_filter filter",
            'hidden' => true,
            'autocomplete'      => [
                'search'       => [
                    '<name>'
                ],
                'sort'         => [
                    '<active> DESC',
                    '<name>'],
                'type'         => 'nomenclatures',
                'clear'        => 1,
                'suggestions'  => '<name>',
                'buttons_hide' => 'search',
                'id_var'       => 'lot',
                'fill_options' => [
                    '$lot => <id>',
                    '$lot_autocomplete => <name>',
                ],
                'filters' => [
                    '<type>'    => '1'
                ],
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
            ]
        ];

        $filters['owner'] = array(
            'custom_id'         => 'owner',
            'name'              => 'owner',
            'type'              => 'autocompleter',
            'width'             => 222,
            'label'             => $this->i18n('owner'),
            'help'              => $this->i18n('owner'),
            'custom_class' => "individual_filter filter",
            'hidden' => true,
            'autocomplete'      => array(
                'search'       => array('<name>'),
                'sort'         => array('<active> DESC', '<name>'),
                'type'         => 'customers',
                'clear'        => 1,
                'suggestions'  => '<name> <lastname>',
                'buttons_hide' => 'search',
                'id_var'       => 'owner',
                'fill_options' => array('$owner => <id>',
                    '$owner_autocomplete => <name> <lastname>',
                ),
                'filters' => array(
                    '<type>'    => '2'
                ),
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
            )
        );

        $filters['measure_from'] = [
            'name' => 'measure_from',
            'custom_id' => 'measure_from',
            'type' => 'custom_filter',
            'custom_class' => "primary_filter",
            'restrict'  => 'insertOnlyFloats',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_from_to.html',
            'help' => "",
            'additional_filter' => 'measure_to',
            'width' => '85',
            'label' => $this->i18n('measure_from'),
        ];

        $filters['measure_to'] =  [
            'name' => 'measure_to',
            'custom_id' => 'measure_to',
            'restrict'  => 'insertOnlyFloats',
            'width' => '85',
            'label' => $this->i18n('to')
        ];

        $filters['measure_date_from'] = [
            'name' => 'measure_date_from',
            'custom_id' => 'measure_date_from',
            'type' => 'custom_filter',
            'custom_class' => "primary_filter",
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'first_filter_label' => $this->i18n('from'),
            'additional_filter' => 'measure_date_to',
            'width' => '64',
            'label' => $this->i18n('measure_period'),
            'help' => $this->i18n('reports_payment_date')
        ];

        $filters['measure_date_to'] = [
            'name' => 'measure_date_to',
            'custom_id' => 'measure_date_to',
            'type' => 'date',
            'width' => '64',
            'label' => $this->i18n('to')
        ];

        $month_options = array();
        for ($i = 1; $i <= 12; $i++) {
            $month_option_value = sprintf('%02d', $i);

            $month_options[] = array(
                'option_value' => $month_option_value,
                'label' => General::mb_ucfirst(General::strftime('%B', strtotime(sprintf('01-%s-%s', $month_option_value, date('Y')))))
            );
        }


        $filters['metrology_to'] = [
            'name' => 'metrology_to',
            'custom_id' => 'metrology_to',
            'type' => 'custom_filter',
            'custom_template' => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/month_year_filter.html',
            'options' => $month_options,
            'width' => 95,
            'additional_filter' => 'year_from',
            'first_filter_label' => $this->i18n('reports_month'),
            'label' => $this->i18n('metrology'),
        ];

        $filters['year_from'] = [
            'custom_id' => 'year_from',
            'name' => 'year_from',
            'type' => 'dropdown',
            'options' => self::getYearOptions($settings),
            'width' => 95,
        ];

        $filters['reporter'] = [
            'custom_id'         => 'reporter',
            'name'              => 'reporter',
            'type'              => 'autocompleter',
            'width'             => 222,
            'label'             => $this->i18n('reporter'),
            'help'              => $this->i18n('reporter'),
            'custom_class' => "individual_filter filter",
            'hidden' => true,
            'autocomplete'      => [
                'search'       => [
                    '<name>'
                ],
                'sort'         => [
                    '<active> DESC',
                    '<name>'],
                'type'         => 'customers',
                'clear'        => 1,
                'suggestions'  => '<name> <lastname>',
                'buttons_hide' => 'search',
                'id_var'       => 'reporter',
                'fill_options' => [
                    '$reporter => <id>',
                    '$reporter_autocomplete => <name> <lastname>',
                ],
                'filters' => [
                    '<type>'    => '1'
                ],
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
            ]
        ];

        $filters['fdr'] = [
            'custom_id'         => 'fdr',
            'name'              => 'fdr',
            'type'              => 'autocompleter',
            'width'             => 222,
            'label'             => $this->i18n('fdr'),
            'help'              => $this->i18n('fdr'),
            'custom_class'      => "individual_filter filter",
            'hidden'            => true,
            'autocomplete'      => [
                'search'        => [
                    '<name>'
                ],
                'sort'         => [
                    '<active> DESC',
                    '<name>'],
                'type'         => 'customers',
                'clear'        => 1,
                'suggestions'  => '<name> <lastname>',
                'buttons_hide' => 'search',
                'id_var'       => 'fdr',
                'fill_options' => [
                    '$fdr => <id>',
                    '$fdr_autocomplete => <name> <lastname>',
                ],
                'filters' => [
                    '<type>'    => '5,7'
                ],
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
            ]
        ];

        return $filters;
    }

    public function processDependentFilters($filters) {
        $reportFilterType = (empty($filters['subreport_type']['value']) ? "main" : $filters['subreport_type']['value']) . "_filter";
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                unset($filters[$filter['additional_filter']]);
            }
            if (isset($filter['custom_class']) && strpos($filter['custom_class'], $reportFilterType) !== false) {
                $filters[$name]['hidden'] = false;
            }
        }

        return $filters;
    }

    private function getDeviceTypeOptions(string $ids)
    {
        $db = self::$registry['db'];
        $sql = "SELECT nti.`name_plural` AS label, nti.parent_id AS option_value
        FROM nom_types_i18n nti
        WHERE nti.parent_id IN ({$ids})";
        return $db->getAll($sql);
    }

    private function getYearOptions($settings) {
        $res = [];
        for($year = $settings['year_options_start']; $year <= $settings['year_options_end']; $year++) {
            $res[] = [
                'label' => strval($year),
                'option_value' =>  strval($year),
            ];
        }
        return $res;
    }
}
