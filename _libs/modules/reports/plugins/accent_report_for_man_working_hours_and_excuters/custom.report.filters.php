<?php
    class Custom_Report_Filters extends Report_Filters {
        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            $filters = array();

            //DEFINE WEEK FILTER
            $filter = array (
                'custom_id' => 'working_week_number',
                'name' => 'working_week_number',
                'type' => 'text',
                'label' => $this->i18n('reports_working_week_number'),
                'help' => $this->i18n('reports_working_week_number_help')
            );
            $filters['working_week_number'] = $filter;

            //DEFINE DOCUMENT NUMBER FILTER
            $filter = array (
                'custom_id' => 'doument_number',
                'name' => 'doument_number',
                'type' => 'text',
                'label' => $this->i18n('reports_document_number'),
                'help' => $this->i18n('reports_document_number_help')
            );
            $filters['doument_number'] = $filter;

            //DEFINE EXECUTERS FILTER
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $filters_users = array('model_lang' => $registry['lang'], 
                                   'sanitize' => true,
                                   'where' => array('u.hidden <> 1'));
            $users = Users::search($registry, $filters_users);

            $_options_users = array();
            foreach($users as $user) {
                $_options_users[] = array(
                    'label' => $user->get('firstname') . ' ' . $user->get('lastname'), 'option_value' => $user->get('id'));
            }

            $filter = array (
                'custom_id' => 'executer',
                'name' => 'executer',
                'type' => 'dropdown',
                'first_option_label' => $this->i18n('all'),
                'label' => $this->i18n('reports_owner'),
                'help' => $this->i18n('reports_owner_help'),
                'options' => $_options_users,
            );
            $filters['executer'] = $filter;

            return $filters;
        }
    }
?>