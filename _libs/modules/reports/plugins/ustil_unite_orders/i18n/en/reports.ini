reports_filter_provider = Client/Provider
reports_filter_date = Document date
reports_filter_date_from = from
reports_filter_date_to = to
reports_filter_invoice_to = Invoice to
reports_filter_orders_types = Order type
reports_filter_customer = Client
reports_filter_for = Create
reports_filter_for_sale = Sale/invoice for orders
reports_filter_for_payment = Payment for orders

reports_th_customer = Customer
reports_th_order_num = Order num
reports_th_date = Date
reports_th_nomenclature = Name
reports_th_quantity = Quantity
reports_th_measure = Measure
reports_th_total = Total
reports_th_paid = Paid
reports_th_to_pay = To pay
reports_th_pay = Pay
reports_container = Cashbox/Bank account
reports_bp = Bank transfer
reports_pko = Petty cash credit order
reports_bp_short = BT
reports_pko_short = PCO
reports_payment_for_orders = Payment for orders
reports_available_amount = Amount to allocate
reports_allocated_amount = Allocated amount
reports_reason = Reason
reports_sale = Sale
reports_unfinished_sale = [unfinished]

reports_create_income_reason = Add sale
reports_create_payment = Add payment
reports_allocate_payments = Allocate payments to orders
reports_balance_info = *Suggested amounts are according to allocation of payments to sales and invoices.

error_reports_add_sell_failed = An error occurred while trying to create incomes reason!
error_reports_tag_order_as_processed = An error occurred while marking the order(%s) as 'Processed'!
reports_payments_created = Payments were added and allocated successfully!
reports_payments_error = An error occurred while adding and allocating payments!
reports_payments_allocated_success = Payments were successfully allocated to orders!
reports_payments_allocated_error = An error occurred while allocating payments to orders!
reports_payments_partial_allocated = Specified amount of payment %s was not fully allocated to order %s because that would result in overpaying it.
reports_payments_not_allocated = Specified amount of payment %s was not allocated to order %s because that would result in overpaying it.
reports_payments_partial_added = A payment for less than specified amount was aded to %s because that would result in overpaying it.
reports_payments_not_added = No payment was added to order %s because that would result in overpaying it.

error_reports_complete_required_fields = Please complete required filters!
