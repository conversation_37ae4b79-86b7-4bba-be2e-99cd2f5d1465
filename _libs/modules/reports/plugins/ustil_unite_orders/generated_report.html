<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="margin-bottom: 10px;">
        <tr class="reports_title_row hcenter">
          <td width="15" class="t_border vmiddle">&nbsp;</td>
          <td width="200" class="t_border vmiddle"><div style="width: 200px;">{#reports_th_customer#|escape|default:"&nbsp;"}</div></td>
          <td width="80" class="t_border vmiddle"><div style="width: 80px;">{#reports_th_order_num#|escape|default:"&nbsp;"}</div></td>
          <td width="60" class="t_border vmiddle"><div style="width:  60px;">{#reports_th_date#|escape|default:"&nbsp;"}</div></td>
          <td width="139" class="t_border vmiddle"><div style="width: 209px;">{#reports_th_nomenclature#|escape|default:"&nbsp;"}</div></td>
          <td width="49" class="t_border vmiddle"><div style="width:  49px;">{#reports_th_quantity#|escape|default:"&nbsp;"}</div></td>
          <td width="49" class="t_border vmiddle"><div style="width:  49px;">{#reports_th_measure#|escape|default:"&nbsp;"}</div></td>
          {assign var='num_payments' value=0}
          {if $reports_additional_options.filters.for eq 'payment'}
            {assign var='num_payments' value=0}
            {if is_array($reports_additional_options.payments)}{assign var='num_payments' value=$reports_additional_options.payments|@count}{/if}
            <td width="64" class="t_border vmiddle"><div style="width:  64px;">{#reports_th_total#|escape|default:"&nbsp;"}</div></td>
            <td width="64" class="t_border vmiddle"><div style="width:  64px;">{#reports_th_paid#|escape|default:"&nbsp;"}</div></td>
            <td width="64" class="t_border vmiddle"><div style="width:  64px;">{#reports_th_to_pay#|escape|default:"&nbsp;"}</div></td>
            <td width="64" class="{if $num_payments}t_border_double {/if}vmiddle"><div style="width:  64px;">{#reports_th_pay#|escape|default:"&nbsp;"}</div></td>
            {if $num_payments}
            <td width="80" class="t_border vmiddle"><div style="width: 80px;">{#reports_sale#|escape}</div></td>
            <td width="80" class="t_border vmiddle" id="allocate_caption"><div style="width: 80px;">{#reports_available_amount#|escape}</div></td>
            {/if}
            {foreach from=$reports_additional_options.payments item='payment' key='pk' name='pi'}
            <td width="80" class="{if !$smarty.foreach.pi.last}t_border {/if}vmiddle">
              {include file=`$theme->templatesDir`input_checkbox.html
                       standalone=true
                       name="check_payment"
                       index=$pk
                       eq_indexes=true
                       value=1
                       onclick='return togglePaymentFields(this);'
              }
              {if !empty($payment.paid_to)}
                {include file=`$theme->templatesDir`input_hidden.html
                         value=$payment.paid_to
                         name='paid_to'
                         index=$pk
                         eq_indexes=true
                         standalone=true
                         disabled=true
                }
              {/if}
              {capture assign='payment_label'}{if $payment.type eq "PKO"}{#reports_pko#}{elseif $payment.type eq "BP"}{#reports_bp#}{/if} {$payment.num}/{$payment.issue_date|date_format:#date_short#}{/capture}
              {capture assign='payment_text'}<span class="strong">{#reports_th_total#}:</span> {$payment.amount|string_format:"%.2f"} {$payment.currency|default:'BGN'}<br /><span class="strong">{#reports_available_amount#}:</span> {$payment.order_available|string_format:"%.2f"} {$payment.currency|default:'BGN'}<br /><span class="strong">{#reports_reason#}:</span> {$payment.reason|nl2br}{/capture}
              <div style="font-weight: normal!important;">
                <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=payments&amp;payments=view&amp;view={$pk}" {help label_content=$payment_label text_content=$payment_text|default:"&nbsp;" popup_only=1 width=250}>{if $payment.type eq "PKO"}{#reports_pko_short#}{elseif $payment.type eq "BP"}{#reports_bp_short#}{/if} {$payment.num|escape}</a>
              </div>
            </td>
            {/foreach}
          {else}
           <td width="64" class="vmiddle"><div style="width:  64px;">{#reports_th_total#|escape|default:"&nbsp;"}</div></td>
          {/if}
        </tr>
        {if $reports_additional_options.filters.for eq 'payment'}
          {assign var=fn value="calcTotalPaidAmount()"}
          {assign var=total_amount value=0}
          {assign var=total_paid value=0}
          {assign var=total_topay value=0}
        {/if}
        {foreach from=$reports_results key='model_id' item='result'}
          {capture assign='current_row_class'}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          {foreach from=$result.rows key='gt2_row_id' item='gt2_row' name='gt2_rows'}
            <tr class="{$current_row_class}">
              {if $smarty.foreach.gt2_rows.first}
                <td class="t_border vmiddle" rowspan="{$result.rowspan}">
                  <input onclick="{$fn};return checkOrdersCustomersList(this);"
                         type="checkbox"
                         name='items[]'
                         id="items_{$result.id}"
                         value="{$result.id}"
                         title="{#check_to_include#|escape}" />
                  <input type="hidden"
                         name="customers[{$result.id}]"
                         id="customer_{$result.id}"
                         value="{$result.customer}" />
                </td>
                <td class="t_border vmiddle" rowspan="{$result.rowspan}">
                  {$result.customer_name|escape|default:"&nbsp;"}
                </td>
                <td class="t_border vmiddle" rowspan="{$result.rowspan}">
                  <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|escape|default:"&nbsp;"}</a>
                </td>
                <td class="t_border vmiddle" rowspan="{$result.rowspan}">
                  {$result.date|date_format:#date_short#|escape}
                </td>
              {/if}
              <td class="t_border">
                {$gt2_row.article_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border hright">
                {$gt2_row.quantity|string_format:"%.2f"|escape|default:"0.00"}
              </td>
              <td class="t_border">
                {$gt2_row.measure|escape|default:"&nbsp;"}
              </td>
              {if $reports_additional_options.filters.for eq 'payment'}
                {if $smarty.foreach.gt2_rows.first}
                  <td class="t_border hright vmiddle" rowspan="{$result.rowspan}">
                    {$result.total|string_format:"%.2f"|escape|default:"0.00"}
                  </td>
                  <td class="t_border hright vmiddle" rowspan="{$result.rowspan}">
                    {$result.paid|string_format:"%.2f"|escape|default:"0.00"}
                  </td>
                  {math assign=to_pay equation=x-y x=$result.total y=$result.paid|default:0 format="%.2f"}
                  {assign var=total_amount value=$total_amount+$result.total}
                  {assign var=total_paid value=$total_paid+$result.paid}
                  {assign var=total_topay value=$total_topay+$to_pay}
                  <td class="t_border hright vmiddle" rowspan="{$result.rowspan}">
                    {$to_pay|string_format:"%.2f"|escape|default:"0.00"}
                    {include file=`$theme->templatesDir`input_hidden.html
                             name="to_pay"
                             index=$model_id
                             eq_indexes=true
                             standalone=true
                             value=$to_pay
                             disabled=true
                             custom_class="to_pay"
                    }
                  </td>
                  <td class="{if $num_payments}t_border_double {/if}hright vmiddle" rowspan="{$result.rowspan}">
                    {include file=`$theme->templatesDir`input_text.html
                             value=$to_pay|string_format:'%.2f'
                             restrict=insertOnlyFloats
                             width=80
                             name=pay
                             index=$result.id
                             eq_indexes=true
                             standalone=true
                             custom_class='hright doc_paid_amount'
                             onkeyup=$fn
                    }
                  </td>
                  {if $num_payments}
                  {if $result.reason_rowspan}
                  <td class="t_border hcenter vmiddle" rowspan="{$result.reason_rowspan}" style="white-space: nowrap;">
                    {if $result.reason_id && $result.reason}
                      {capture assign='reason_label'}{#reports_sale#} {$result.reason.num}/{$result.reason.issue_date|date_format:#date_short#}{/capture}
                      {capture assign='reason_text'}<span class="strong">{#reports_th_total#}:</span> {$result.reason.total_with_vat|string_format:"%.2f"} {$result.reason.currency|default:'BGN'}{if isset($result.reason.total_remaining_amount)}<br /><span class="strong">{#reports_th_to_pay#}:</span> {$result.reason.total_remaining_amount|string_format:"%.2f"} {$result.reason.currency|default:'BGN'}{/if}{/capture}
                      <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=incomes_reasons&amp;incomes_reasons=view&amp;view={$result.reason_id}" {help label_content=$reason_label text_content=$reason_text|default:"&nbsp;" popup_only=1 width=250}>{$result.reason.num|escape}</a>
                    {else}
                      -
                    {/if}
                  </td>
                  {/if}
                  <td class="t_border hright vmiddle" rowspan="{$result.rowspan}" style="white-space: nowrap;">
                    <span id="o_unallocated_{$model_id}" class="unallocated green" title="{#reports_available_amount#|escape}">{$to_pay|string_format:"%.2f"|escape|default:"0.00"}</span>
                  </td>
                  {/if}
                  {foreach from=$reports_additional_options.payments item='payment' key='pk' name='pi'}
                  <td class="{if !$smarty.foreach.pi.last}t_border {/if}hright vmiddle" rowspan="{$result.rowspan}">
                    {if $payment.customer eq $result.customer}
                      {capture assign='suggested_value'}{if !empty($payment.order_balance) && isset($payment.order_balance.$model_id)}{$payment.order_balance.$model_id}{/if}{/capture}
                      {capture assign='distinctive_class'}{if $suggested_value gt 0}distinctive{/if}{/capture}
                      {include file=`$theme->templatesDir`input_text.html
                               value=$suggested_value|string_format:'%.2f'
                               restrict='insertOnlyFloats'
                               width=80
                               name=allocated[`$model_id`]
                               custom_id=allocated_`$model_id`
                               index=$pk
                               eq_indexes=true
                               standalone=true
                               custom_class="hright p_`$pk` o_`$model_id` allocated input_inactive `$distinctive_class`"
                               disabled=true
                               onkeyup='updateTotalAmount(this)'
                               label=#reports_allocated_amount#
                      }
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                  {/foreach}
                {/if}
              {else}
                {if $smarty.foreach.gt2_rows.first}
                  <td class="hright vmiddle" rowspan="{$result.rowspan}">
                    {$result.total|string_format:"%.2f"|escape|default:"0.00"}
                  </td>
                {/if}
              {/if}
            </tr>
          {/foreach}
        {foreachelse}
          <tr class="t_odd1 t_odd2">
            <td class="error" colspan="11">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        {if $reports_additional_options.filters.for eq 'payment'}
          <tr>
            <td colspan="7" class="hright vmiddle t_border"><strong>{#reports_th_total#}:</strong></td>
            <td class="hright vmiddle t_border">{$total_amount|string_format:"%.2f"|escape|default:"0.00"}</td>
            <td class="hright vmiddle t_border">{$total_paid|string_format:"%.2f"|escape|default:"0.00"}</td>
            <td class="hright vmiddle t_border">{$total_topay|string_format:"%.2f"|escape|default:"0.00"}</td>
            <td id="total_paid_amount" class="{if $num_payments}t_border_double {/if}hright vmiddle">&nbsp;</td>
            {if $num_payments}
              <td class="t_border hcenter vmiddle strong"><div style="width: 80px;">&nbsp;</div></td>
              <td class="t_border hcenter vmiddle strong"><div style="width: 80px;">{#reports_available_amount#|escape}</div></td>
            {/if}
            {foreach from=$reports_additional_options.payments item='payment' key='pk' name='pi'}
              <td class="{if !$smarty.foreach.pi.last}t_border {/if}hright vmiddle" style="white-space: nowrap;">
                <span id="p_unallocated_{$pk}" class="unallocated green" title="{#reports_available_amount#|escape}">{$payment.order_available|string_format:"%.2f"}</span>
                {include file=`$theme->templatesDir`input_hidden.html
                         name="to_allocate"
                         index=$pk
                         eq_indexes=true
                         standalone=true
                         value=$payment.order_available|string_format:"%.2f"
                         disabled=true
                         custom_class="to_allocate"
                }
              </td>
            {/foreach}
          </tr>
          {if $reports_results}
          <tr class="t_selected_row_for_edit">
            <td colspan="11" class="{if $num_payments}t_border_double{/if}">
              <div style="float: left">
                <label for="container">{help label_content=#reports_container#}</label>
              </div>
              <div style="float: left; margin-left: 5px;">
              {include file=`$theme->templatesDir`input_dropdown.html
                  name=container
                  standalone=true
                  width=200
                  value=$reports_additional_options.default_container
                  optgroups=$reports_additional_options.containers
                  label=#reports_container#
              }
              </div>
              <div style="float: left; margin-left: 5px;">
                <label for="date_formatted">{help label_content=#reports_filter_date#}</label>
              </div>
              <div style="float: left; margin-left: 5px;">
              {include file=`$theme->templatesDir`input_date.html
                  name=date
                  standalone=true
                  width=80
                  value=$smarty.now|date_format:#date_iso_short#
                  label=#reports_filter_date#
              }
              </div>
              <div style="float: left; margin-left: 5px;">
                <button type="submit" class="button" onclick="createPayments(this.form); return false;">{#reports_create_payment#|escape}</button>
              </div>
              <div class="clear"></div>
            </td>
            {if $num_payments}
              <td colspan="{math equation='t+2' t=$num_payments}">
                <button type="button" class="button floatl" onclick="allocatePayments(this.form);">{#reports_allocate_payments#|escape}</button>
                <span class="legend">{#reports_balance_info#|escape}</span>
              </td>
            {/if}
          </tr>
          {/if}
        {/if}
        <tr>
          <td class="t_footer" colspan="{math equation='11+t+2*(t>0)' t=$num_payments}"></td>
        </tr>
      </table>
      {if $reports_additional_options.filters.for eq 'payment'}
        {if $reports_results}
          <script type="text/javascript">calcTotalPaidAmount()</script>
        {/if}
      {else}
        <button type="submit" class="button" onclick="checkSubmitOrders(this); return false;">{#reports_create_income_reason#|escape}</button>
        {if $export_permission}
          <button type="button" name="export_selected_records" class="button reports_export_button" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=export&amp;report_type={$report_type}&amp;pattern=';">{#export_report#|escape}</button>
        {/if}
      {/if}
    </td>
  </tr>
</table>
