{strip}
{if $reports_results}
  {assign var='design' value=$reports_results.design}
  {if $prepare_placeholder eq 'design_name'}
    {$design.name|escape}
  {elseif $prepare_placeholder eq 'size'}
    {$reports_results.sizes|escape}
  {elseif $prepare_placeholder eq 'brand_name'}
    {$design.brand_name|escape}
  {elseif $prepare_placeholder eq 'season_info'}
    {$design.season_info|escape}
  {elseif $prepare_placeholder eq 'date_creation'}
    {$design.date_creation|escape}
  {elseif $prepare_placeholder eq 'main_fabric_name'}
    {$design.main_fabric_name|escape}
  {elseif $prepare_placeholder eq 'content_fabric'}
    {$design.content_fabric|escape}
  {elseif $prepare_placeholder eq 'stage_production'}
    {$design.stage_production|escape}
  {elseif $prepare_placeholder eq 'primary_atelier_prod'}
    {$design.primary_atelier_prod|escape}
  {elseif $prepare_placeholder eq 'path_product_sketch'}
    {$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.product_sketch|encrypt:'_viewfile_'|escape:'url'}
  {elseif $prepare_placeholder eq 'path_picture_front'}
    {$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_front|encrypt:'_viewfile_'|escape:'url'}
  {elseif $prepare_placeholder eq 'path_picture_back'}
    {$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_back|encrypt:'_viewfile_'|escape:'url'}
  {elseif $prepare_placeholder eq 'note_info'}
    {$design.note_info|nl2br}
  {elseif $prepare_placeholder eq 'reserves_processing'}
    {$design.reserves_processing|nl2br}
  {elseif $prepare_placeholder eq 'labeling_instructions'}
    {$design.labeling_instructions|nl2br}
  {elseif $prepare_placeholder eq 'packing_instructions'}
    {$design.packing_instructions|nl2br}
  {elseif $prepare_placeholder eq 'cut_info'}
    {$design.cut_info|nl2br}
  {elseif $prepare_placeholder eq 'compound_label'}
    {$design.compound_label|nl2br}
  {elseif $prepare_placeholder eq 'path_inst_file'}
    {$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_label_instructions|encrypt:'_viewfile_'|escape:'url'}
  {elseif $prepare_placeholder eq 'path_pack_file'}
    {$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_packiging|encrypt:'_viewfile_'|escape:'url'}
  {elseif $prepare_placeholder eq 'path_label_file'}
    {$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_label_compound|encrypt:'_viewfile_'|escape:'url'}
  {elseif in_array($prepare_placeholder, array('css_styles', 'result', 'table_technical_description', 'table_child_skus'))}
    {if in_array($prepare_placeholder, array('css_styles', 'result'))}
    {literal}
      <style>
        .page1 {
          page-break-after: always;
        }

        .brand-container {
          width: 100%;
          height: 30px;
          border: 1px solid black;
        }
        .brand-container td {
          border: none;
          vertical-align: middle;
          text-align: center;
          font-size: 24px;
          font-weight: bold;
        }

        table {
          border-top: 1px solid black;
          border-left: 1px solid black;
          width: 100%;
        }
        th, td {
          border-right: 1px solid black;
          border-bottom: 1px solid black;
          padding: 3px;
        }
        th {
          background-color: #a8cef9;
        }

        .margin-top-20 {
          margin-top: 20px;
        }

        th,
        .table1 td,
        .table3 td {
          min-height: 46px;
        }

        .table1 td,
        .table4 .subtable td {
          text-align: center;
        }

        .images-table {
          width: 100%;
          height: 600px;
          border: 1px solid black;
        }
        .images-table td {
          width: 50%;
          vertical-align: middle;
          text-align: center;
          border: none;
          padding: 1px;
        }

        .table2 {
          border-top: 1px solid black;
          border-left: 1px solid black;
        }
        .table2 td {
          border-right: 1px solid black;
          border-bottom: 1px solid black;
        }
        .table2 th {
          height: 50px;
        }
        .table2 td {
          vertical-align: top;
        }

        /*.table2 tr,*/
        /*.table3 tr {*/
        /*  page-break-inside: avoid;*/
        /*  page-break-after: avoid;*/
        /*  page-break-before: avoid;*/
        /*}*/

        .table3 td {
          vertical-align: top;
        }

        .table4,
        .table4 td {
          border: none;
        }
        .table4 .images-cell {
          width: 300px;
        }
        .table4 .images-table,
        .table4 .subtable {
          height: 230px;
        }
        .table4 .images-table {
          border: 1px solid black;
        }
        .table4 .images-table td {
          width: 50%;
          vertical-align: middle;
          text-align: center;
          border: none;
          padding: 1px;
        }
        .table4 .space {
          width: 20px;
        }
        .table4 .subtable {
          border-top: 1px solid black;
          border-left: 1px solid black;
        }
        .table4 .subtable td {
          border-right: 1px solid black;
          border-bottom: 1px solid black;
        }

        .product_group td {
          min-height: 25px;
        }
      </style>
    {/literal}
    {/if}

    {if $prepare_placeholder eq 'result'}
    <div class="page1">
      <table class="brand-container" cellpadding="0" cellspacing="0">
        <tr>
          <td>{$design.brand_name|escape}</td>
        </tr>
      </table>
    {/if}
    {if $prepare_placeholder eq 'result' || $prepare_placeholder eq 'table_technical_description'}
      <table class="table1 margin-top-20" cellpadding="0" cellspacing="0">
        <tr><th colspan="20">{#technical_description#}</th></tr>
        <tr>
          <th>{#model#}</th>
          <th>{#season#}</th>
          <th width="130">{#date_of_creation#}</th>
          <th>{#kind_fabric#}</th>
          {foreach from=$design.colors item='color' name='colors'}
            <th>{#color#} {$smarty.foreach.colors.iteration}</th>
          {/foreach}
        </tr>
        <tr>
          <td>{$design.name|escape}</td>
          <td>{$design.season_info|escape}</td>
          <td style="text-align: center;">{$design.date_creation|date_format:#date_short#}</td>
          <td>{$design.main_fabric_name|escape}</td>
          {foreach from=$design.colors item='color'}
            <td>[{$color.code}] {$color.name}</td>
          {/foreach}
        </tr>
      </table>
    {/if}

    {if $prepare_placeholder eq 'result'}
      <table class="images-table margin-top-20" cellpadding="0" cellspacing="0">
        <tr>
          <td>
            {if !empty($design.picture_front)}
              <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_front|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=500&amp;maxheight=800" />
            {else}
              &nbsp;
            {/if}
          </td>
          <td>
            {if !empty($design.picture_back)}
              <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_back|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=500&amp;maxheight=800" />
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      </table>

      <table class="table2 margin-top-20" cellpadding="0" cellspacing="0">
        <tr>
          <th class="technical_description_and_instructions">{#technical_description_and_instructions#}</th>
          <th class="reserves_and_processing">{#reserves_and_processing#}</th>
        </tr>
        <tr>
          <td class="technical_description_and_instructions">{$design.note_info|nl2br}</td>
          <td class="reserves_processing">{$design.reserves_processing|nl2br}</td>
        </tr>
      </table>

      <table class="table3 margin-top-20" cellpadding="0" cellspacing="0">
        <tr>
          <th class="labeling_instructions">{#labeling_instructions#}</th>
          <th class="packing_instructions">{#packing_instructions#}</th>
        </tr>
        <tr>
          <td class="labeling_instructions">{$design.labeling_instructions|nl2br}</td>
          <td class="packing_instructions">{$design.packing_instructions|nl2br}</td>
        </tr>
      </table>
    </div>

    <div class="page2">
      <table class="brand-container margin-top-20" cellpadding="0" cellspacing="0">
        <tr>
          <td>{$design.brand_name|escape}</td>
        </tr>
      </table>

      <table class="table4 margin-top-20" cellpadding="0" cellspacing="0">
        <tr>
          <td class="images-cell">
            <table class="images-table" cellpadding="0" cellspacing="0">
              <tr>
                <td>
                  {if !empty($design.picture_front)}
                    <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_front|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=150&amp;maxheight=230" />
                  {else}
                    &nbsp;
                  {/if}
                </td>
                <td>
                  {if !empty($design.picture_back)}
                    <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$design.picture_back|encrypt:'_viewfile_'|escape:'url'}&amp;maxwidth=150&amp;maxheight=230" />
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
            </table>
          </td>
          <td class="space">&nbsp;</td>
          <td>
            <table class="subtable" cellpadding="0" cellspacing="0">
              <tr>
                <th colspan="4">&nbsp;</th>
              </tr>
              <tr>
                <th>{#model#}</th>
                <th>{#date_of_creation#}</th>
                <th>{#kind_fabric#}</th>
                <th>{#content_fabric#}</th>
              </tr>
              <tr>
                <td>{$design.name|escape}</td>
                <td class="hcenter">{$design.date_creation|date_format:#date_short#}</td>
                <td>{$design.main_fabric_name|escape}</td>
                <td>{$design.content_fabric|escape}</td>
              </tr>
              <tr>
                <th>{#season#}</th>
                <th>{#stage#}</th>
                <th>{#primary_atelier_production#}</th>
                <th>{#size#}</th>
              </tr>
              <tr>
                <td>{$design.season_info|escape}</td>
                <td>{$design.stage_production|escape}</td>
                <td>{$design.primary_atelier_prod|escape}</td>
                <td>{$reports_results.sizes|escape}</td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    {/if}

      {if !empty($reports_results.child_skus) && ($prepare_placeholder eq 'result' || $prepare_placeholder eq 'table_child_skus')}
        {foreach from=$reports_results.child_skus item='child_sku'}
          <table class="product_group margin-top-20" cellpadding="0" cellspacing="0">
            <tr>
              <th colspan="6">{$child_sku.code}</th>
            </tr>
            <tr>
              <th style="width: 460px;">{$child_sku.product_group.labels[$child_sku.product_group_names.product_name]}</th>
              <th style="width: 109px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_code]}</th>
              <th style="width: 93px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_color_code]}</th>
              <th style="width: 133px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_color_name]}</th>
              <th style="width: 70px;">{$child_sku.product_group.labels[$child_sku.product_group_names.product_num]}</th>
              <th style="width: 70px;">{$child_sku.product_group.labels[$child_sku.product_group_names.material_measure]}</th>
            </tr>
            {foreach from=$child_sku.product_group.values item='row'}
              <tr>
                <td>{$row[$child_sku.product_group_names.product_name]|escape|replace:'[':'&#91;'|replace:']':'&#93;'}</td>
                <td>{$row[$child_sku.product_group_names.material_code]|escape}</td>
                <td>{$row[$child_sku.product_group_names.material_color_code]|escape|replace:'[':'&#91;'|replace:']':'&#93;'}</td>
                <td>{$row[$child_sku.product_group_names.material_color_name]|escape|replace:'[':'&#91;'|replace:']':'&#93;'}</td>
                <td>{$row[$child_sku.product_group_names.product_num]|escape}</td>
                <td style="text-align: right;">{$row[$child_sku.product_group_names.material_measure]|escape}</td>
              </tr>
            {foreachelse}
              <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
              </tr>
            {/foreach}
          </table>
        {/foreach}
      {/if}
    {if $prepare_placeholder eq 'result'}
    </div>
    {/if}
  {/if}
{/if}
{/strip}
