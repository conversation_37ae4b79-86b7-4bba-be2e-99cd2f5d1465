<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'generate_report':
                $this->preIndex();
                break;
            default:
                parent::execute();
        }
    }

    /*
     * Defines whether the results should be exported or not
     */
    public function preIndex() {
        parent::execute();

        //no limitations just leave the default behaviour
        if (!defined('EXPORT_OVER') || empty(EXPORT_OVER)) {
            return true;
        }

        // execute the report and define the number of results
        $report = $this->getReportType();
        $report = $report['name'];
        $this->report = $report;

        $filters = Reports::saveSearchParams($this->registry, $this->registry->get('filters_values'), 'reports_' . $report . '_');

        Reports::getReportSettings($this->registry, $report);

        // include the report class
        require_once PH_MODULES_DIR . "reports/plugins/" . $report . "/custom.report.query.php";

        // form the name of the required report class
        $report_name_elements = explode('_', $report);
        foreach ($report_name_elements as $key => $element) {
            $report_name_elements[$key] = ucfirst($element);
        }
        $report_class_name = implode ('_', $report_name_elements);
        $filters['pre_sort'] = true;

        // get reports results
        $report_results = $report_class_name::buildQuery($this->registry, $filters);

        // check the actual results
        $total_rows = count($report_results['records']);

        //export
        if ($total_rows > EXPORT_OVER) {
            $this->setAction('export');
            require_once $this->viewersDir . 'reports.export.viewer.php';
            $this->viewer = new Reports_Export_Viewer($this->registry);
        }

        return true;
    }
}

?>
