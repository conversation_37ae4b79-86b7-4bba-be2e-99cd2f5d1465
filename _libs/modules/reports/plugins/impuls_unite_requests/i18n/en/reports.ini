reports_general_report_option = General
reports_financial_resources_option = Financial resources
reports_transport_option = Mobile transport
reports_materials_option = Materials
reports_werehouse_option = Warehouse/ZBUT/IST

reports_general_report = General:
reports_werehouse = Warehouse/ZBUT/IST:
reports_transport = Mobile transport:
reports_financial = Financial resources:
reports_materials = Materials:
reports_request_status = Request status
reports_from_date = From
reports_to_date = To
reports_project = Branch / Project
reports_pps_machine = PPS / Machine
reports_requested_by = Requested by unit
reports_company = Company
reports_nomenclature = Nomenclature
reports_expense_description = Expense description
reports_deliverer = Received by (deliverer)
reports_employee = Received by (employee)
reports_werehouse_type_expense = Type expense
reports_transport_type_expense = Type expense
reports_type_pps = Type vehicle
reports_financial_type_expense = Type expense
reports_materials_type_expense = Type expense
reports_spec_materials = Specialized materials

reports_undefined_type_expense = [undefined expenses]

reports_all_statuses = all requests
reports_approved = approved
reports_approved_and_executed = approved and executed
reports_approved_and_partialy_executed = approved and partially executed
reports_disapproved = disapproved
reports_company_1 = Impulse Co
reports_company_2 = Impulse Project

reports_exp_invoice_prefix = I
reports_exp_proforma_prefix = PI

reports_werehouse_optlabel_werehouse = Warehouse
reports_werehouse_optlabel_zbut = ZBUT
reports_werehouse_optlabel_ist = IST

reports_request_num = Request num
reports_request_date = Date
reports_request_type = Type request
reports_receive_branch = Receive branch
reports_expense_type = Type expense
reports_expense_description_lbl = Description
reports_quantity = Quantity
reports_price = Price
reports_value = Value
reports_total_value = Total
reports_total_value_with_vat = Total with VAT
reports_total_value_invoice = Value I/PI
reports_difference = Difference
reports_invoice_num_date = I/PI num (to date)
reports_note = Notes
reports_received_by = Received by
reports_requested_by_unit = Requested by unit

reports_chose_container = Petty Cash/Bank Account
reports_create_expense_reason = Issue proform/invoice
reports_add_expense_reason = Create expense reason
reports_add_expense_reason_instructions = Choose type for the expense reason:

message_reports_no_rows_to_issue_expense_reason = No selected row to issue an expense reason!
message_reports_no_deliverer_data = No data for the deliverers on the selected rows!
message_reports_select_only_one_deliverer = Please select articles to exactly one deliverer!
message_reports_one_or_more_rows_has_no_deliverer = One or more of the rows does not have selected deliverer!

reports_no_number = [no number]

error_reports_complete_required_fields = Please, fill the request status filter!
error_reports_complete_type_table_filter = Fill the filter for report type (General / Financial resources / Mobile transport / Materials / Warehouse/ZBUT)!
