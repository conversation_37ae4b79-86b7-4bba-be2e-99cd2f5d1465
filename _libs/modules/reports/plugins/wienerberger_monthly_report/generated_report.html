<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table border="0" cellpadding="3" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border" rowspan="2" style="vertical-align: middle;"><div style="width: 20px;">{#reports_table_num#|escape}</div></td>
          <td class="t_border" rowspan="2" style="vertical-align: middle;"><div style="width: 140px;">{#reports_table_period#|escape}</div></td>
          <td class="t_border" rowspan='2' style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_region#|escape}</div></td>
          <td class="t_border" rowspan='2' style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_sales_representer#|escape}</div></td>
          <td class="t_border hcenter" colspan="5" style="vertical-align: middle;"><div>{#reports_table_market_state#|escape}</div></td>
          <td class="t_border hcenter" colspan="4" style="vertical-align: middle;"><div>{#reports_table_forecast#|escape}</div></td>
          <td class="hcenter" colspan="3" style="vertical-align: middle;"><div>{#reports_table_tnf#|escape}</div></td>
        </tr>
        <tr class="reports_title_row hcenter">
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_grade#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_grade_advanced#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_tend#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_posibilities#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_risks#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_total_bricks#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_PTH#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_FOR#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_Lintels#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_negotiated#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_launched#|escape}</div></td>
          <td style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_launched_count#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.first_table.records item=period name=periods key=num}
            {capture assign="current_period_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
            {foreach from=$period.records item=region name=regions}
                {foreach from=$region.records item=record name=records}
                    <tr class="{$current_period_class}">
                        {if $smarty.foreach.records.first && $smarty.foreach.regions.first}
                        <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                            {$period.index|escape|default:"&nbsp;"}
                        </td>
                        <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                            {$period.parent|escape|default:"&nbsp;"}
                        </td>
                        {/if}
                        {if $smarty.foreach.records.first}
                        <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$region.rowspan+1}">
                            {$region.parent|escape|default:"&nbsp;"}
                        </td>
                        {/if}
                        <td class="t_border" style="vertical-align: middle;" data-document="{$record.doc_id|escape|default:"&nbsp;"}" >
                          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$record.doc_id}" target="_blank">
                            {$record.customer_name|escape|default:"&nbsp;"}
                          </a>
                        </td>
                        <td class="t_border" style="vertical-align: middle;" >{$record.assestment|escape|nl2br|default:"&nbsp;"}</td>
                        <td class="t_border" style="vertical-align: middle;" >{$record.assestment_adv|escape|nl2br|default:"&nbsp;"}</td>
                        <td class="t_border" style="vertical-align: middle;" >{$record.trends|escape|nl2br|default:"&nbsp;"}</td>
                        <td class="t_border" style="vertical-align: middle;" >{$record.resources|escape|nl2br|default:"&nbsp;"}</td>
                        <td class="t_border" style="vertical-align: middle;" >{$record.risks|escape|nl2br|default:"&nbsp;"}</td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >{$record.bricks|escape|default:"&nbsp;"}</td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >{$record.pth_value|escape|default:"&nbsp;"}</td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >{$record.for_value|escape|default:"&nbsp;"}</td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >{$record.lintels|escape|default:"&nbsp;"}</td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >{$record.agreed|escape|default:"&nbsp;"}</td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >{$record.started|escape|default:"&nbsp;"}</td>
                        <td class="hcenter" style="vertical-align: middle;" >{$record.started_count|escape|default:"&nbsp;"}</td>
                    </tr>
                    {if $smarty.foreach.records.last}
                        <tr class="{$current_period_class}">
                          <td class="t_border hright vmiddle" colspan="6">{#reports_table_sum#|escape}</td>
                          <td class="t_border hcenter vmiddle">{$region.sum_bricks|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter vmiddle">{$region.sum_pth_value|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter vmiddle">{$region.sum_for_value|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter vmiddle">{$region.sum_lintels|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter vmiddle">{$region.sum_agreed|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter vmiddle">{$region.sum_started|escape|default:"&nbsp;"}</td>
                          <td class="hcenter vmiddle">{$region.sum_started_count|escape|default:"&nbsp;"}</td>
                        </tr>
                    {/if}
                {/foreach}
            {/foreach}
        {foreachelse}
        <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="16">{#no_items_found#|escape}</td>
        </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="16"></td>
        </tr>
      </table>
      <br /><button type="button" name="export_selected_records" class="button reports_export_button" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=export&amp;report_type={$report_type}&amp;pattern=&amp;custom_export_table=1';">{#export_report#|escape}</button>
      <br />
      <br />
      <br />
    </td>
  </tr>
  <tr>
    <td>
      <table border="0" cellpadding="3" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border" rowspan="2" style="vertical-align: middle;"><div style="width: 20px;">{#reports_table_num#|escape}</div></td>
          <td class="t_border" rowspan="2" style="vertical-align: middle;"><div style="width: 140px;">{#reports_table_period#|escape}</div></td>
          <td class="t_border" rowspan='2' style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_region#|escape}</div></td>
          <td class="t_border" rowspan='2' style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_sales_representer#|escape}</div></td>
          <td class="t_border hcenter" colspan="4" style="vertical-align: middle;"><div>{#reports_table_competition_fact#|escape}</div></td>
          <td class="hcenter" colspan="6" style="vertical-align: middle;"><div>{#reports_table_competition_price#|escape}</div></td>
        </tr>
        <tr class="reports_title_row hcenter">
          <td class="t_border" style="vertical-align: middle;"><div style="width: 200px;">{#reports_table_factory#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_table_status#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 80px;">{#reports_table_date_stop_start#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 80px;">{#reports_table_quantity#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 140px;">{#reports_table_city#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 190px;">{#reports_table_CPT_number#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 240px;">{#reports_table_CPT_TNF#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_competitor#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_EXW_number#|escape}</div></td>
          <td style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_EXW_TNF#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.third_table.records item=period name=periods}
            {capture assign="current_period_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
            {foreach from=$period.records item=region name=regions}
                {foreach from=$region.records item=factory name=factories}
                    {foreach from=$factory.factories item=record name=records}
                        <tr class="{$current_period_class}">
                            {if $smarty.foreach.records.first && $smarty.foreach.regions.first && $smarty.foreach.factories.first}
                                <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                                    {$period.index|escape|default:"&nbsp;"}
                                </td>
                                <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                                    {$period.parent|escape|default:"&nbsp;"}
                                </td>
                            {/if}
                            {if $smarty.foreach.records.first && $smarty.foreach.factories.first}
                                <td class="t_border" style="vertical-align: middle;" rowspan="{$region.rowspan}">
                                    {$region.parent|escape|default:"&nbsp;"}
                                </td>
                            {/if}
                            {if $smarty.foreach.records.first}
                                <td class="t_border" rowspan="{$factory.rowspan}" style="vertical-align: middle;" >
                                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$factory.doc_id}" target="_blank">
                                    {$factory.customer_name|escape|default:"&nbsp;"}
                                  </a>
                                </td>
                            {/if}
                            <td class="t_border" style="vertical-align: middle;" >{$record.factory_name|escape|default:"&nbsp;"}</td>
                            <td class="t_border" style="vertical-align: middle;" >{$record.status_name|escape|default:"&nbsp;"}</td>
                            <td class="t_border" style="vertical-align: middle;" >{$record.date_start_stop|escape|default:"&nbsp;"}</td>
                            <td class="t_border" style="vertical-align: middle;" >{$record.quantity|escape|default:"&nbsp;"}</td>
                            {if $smarty.foreach.records.first}
                                {if $factory.prices}
                                    <td class="hcenter" rowspan="{$factory.rowspan}" colspan="6" style="padding: 0; border-top: none; vertical-align: middle;" width="400">
                                      <table class="report_prices" cellpadding="5" cellspacing="0" style="width: 100%; vertical-align: middle;">
                                        {foreach from=$factory.prices item=price name=prices}
                                          <tr>
                                            <td class="t_border hleft" width="140" style="width:140px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.city_name|escape|default:"&nbsp;"}</td>
                                            <td class="t_border hleft" width="190" style="width:190px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.cpt_price|escape|default:"&nbsp;"}</td>
                                            <td class="t_border hleft" width="240" style="width:240px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.cpt_tnf|escape|default:"&nbsp;"}</td>
                                            <td class="t_border hleft" width="90" style="width:90px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.competitor_name|escape|default:"&nbsp;"}</td>
                                            <td class="t_border hleft" width="90" style="width:90px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.exw_price|escape|default:"&nbsp;"}</td>
                                            <td class="hleft" width="90" style="width:90px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.exw_tnf|escape|default:"&nbsp;"}</td>
                                          </tr>
                                        {/foreach}
                                      </table>
                                    </td>
                                {else}
                                    <td class="hcenter" rowspan="{$factory.rowspan}" colspan="6" style="padding: 0; border-top: none; vertical-align: middle;" width="400"></td>
                                {/if}
                            {/if}
                        </tr>

                    {foreachelse}
                        <tr class="{$current_period_class}">
                            {if $smarty.foreach.factories.first && $smarty.foreach.regions.first}
                                <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                                    {$period.index|escape|default:"&nbsp;"}
                                </td>
                                <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                                    {$period.parent|escape|default:"&nbsp;"}
                                </td>
                            {/if}
                            {if $smarty.foreach.factories.first}
                                <td class="t_border" style="vertical-align: middle;" rowspan="{$region.rowspan}">
                                    {$region.parent|escape|default:"&nbsp;"}
                                </td>
                            {/if}
                            <td class="t_border" style="vertical-align: middle;" >{$factory.customer_name|escape|default:"&nbsp;"}</td>
                            <td class="t_border hcenter" style="vertical-align: middle;" colspan="4" ></td>
                            {if $smarty.foreach.records.first}
                                {if $factory.prices}
                                <td class="hcenter" rowspan="{$factory.rowspan}" colspan="6" style="padding: 0; border-top: none; vertical-align: middle;" width="400">
                                  <table class="report_prices" cellpadding="5" cellspacing="0" style="display: compact !important; width: 100%; vertical-align: middle;">
                                    {foreach from=$factory.prices item=price name=prices}
                                      <tr style='height: 100% !important;'>
                                        <td class="t_border hleft" width="140" style="height:100% !important;width:140px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.city_name|escape|default:"&nbsp;"}</td>
                                        <td class="t_border hleft" width="190" style="width:190px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.cpt_price|escape|default:"&nbsp;"}</td>
                                        <td class="t_border hleft" width="240" style="width:240px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.cpt_tnf|escape|default:"&nbsp;"}</td>
                                        <td class="t_border hleft" width="90" style="width:90px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.competitor_name|escape|default:"&nbsp;"}</td>
                                        <td class="t_border hleft" width="90" style="width:90px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.exw_price|escape|default:"&nbsp;"}</td>
                                        <td class="hleft" width="90" style="width:90px !important;vertical-align: middle;{if $smarty.foreach.prices.last} border-bottom: none;{/if}" >{$price.exw_tnf|escape|default:"&nbsp;"}</td>
                                      </tr>
                                    {/foreach}
                                  </table>
                              </td>
                                {else}
                                    <td class="hcenter" rowspan="{$factory.rowspan}" colspan="6" style="padding: 0; border-top: none; vertical-align: middle;" width="400"></td>
                                {/if}
                            {/if}
                        </tr>
                    {/foreach}
                {/foreach}
            {/foreach}
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="14">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="14"></td>
        </tr>
      </table>
      <br /><button type="button" name="export_selected_records" class="button reports_export_button" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=export&amp;report_type={$report_type}&amp;pattern=&amp;custom_export_table=3';">{#export_report#|escape}</button>
      <br />
      <br />
      <br />
    </td>
  </tr>
  <tr>
    <td>
      <table border="0" cellpadding="3" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border" rowspan="2" style="vertical-align: middle;"><div style="width: 20px;">{#reports_table_num#|escape}</div></td>
          <td class="t_border" rowspan="2" style="vertical-align: middle;"><div style="width: 140px;">{#reports_table_period#|escape}</div></td>
          <td class="t_border" rowspan='2' style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_region#|escape}</div></td>
          <td class="t_border" rowspan='2' style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_sales_representer#|escape}</div></td>
          <td class="t_border hcenter" colspan="2" style="vertical-align: middle;"><div>{#reports_table_meetings_builders_by_regions#|escape}</div></td>
          <td class="t_border hcenter" colspan="3" style="vertical-align: middle;"><div>{#reports_table_meetings_total#|escape}</div></td>
          <td rowspan='2' style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_protocols#|escape}</div></td>
        </tr>
        <tr class="reports_title_row hcenter">
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_partners#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_builders#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_investors#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_salesmen#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_table_architects#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.second_table.records item=period name=periods key=num}
            {capture assign="current_period_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
            {foreach from=$period.records item=region name=regions}
              {assign var='region_sum_partners' value=0}
              {assign var='region_sum_builders' value=0}
              {assign var='region_sum_investors' value=0}
              {assign var='region_sum_traders' value=0}
              {assign var='region_sum_designers' value=0}
              {assign var='region_sum_protocols' value=0}
                {foreach from=$region.records item=record name=records}
                  {assign var='record_protocols' value=0}
                    <tr class="{$current_period_class}">
                        {if $smarty.foreach.records.first && $smarty.foreach.regions.first}
                        <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                            {$period.index|escape|default:"&nbsp;"}
                        </td>
                        <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$period.rowspan}">
                            {$period.parent|escape|default:"&nbsp;"}
                        </td>
                        {/if}
                        {if $smarty.foreach.records.first}
                        <td class="t_border hcenter" style="vertical-align: middle;" rowspan="{$region.rowspan+1}">
                            {$region.parent|escape|default:"&nbsp;"}
                        </td>
                        {/if}
                        <td class="t_border" style="vertical-align: middle;" data-document="{$record.doc_id|escape|default:"&nbsp;"}" >
                          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$record.doc_id}" target="_blank">
                            {$record.customer_name|escape|default:"&nbsp;"}
                          </a>
                        </td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >
                          {$record.partners|escape|default:"&nbsp;"}
                          {assign var='region_sum_partners' value=$region_sum_partners+$record.partners}
                          {assign var='record_protocols' value=$record_protocols+$record.partners}
                        </td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >
                          {assign var='record_build_sum' value=$record.build-$record.partners}
                          {$record_build_sum|escape|default:"&nbsp;"}
                          {assign var='region_sum_builders' value=$region_sum_builders+$record_build_sum}
                          {assign var='record_protocols' value=$record_protocols+$record_build_sum}
                        </td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >
                          {$record.invest|escape|default:"&nbsp;"}
                          {assign var='region_sum_investors' value=$region_sum_investors+$record.invest}
                          {assign var='record_protocols' value=$record_protocols+$record.invest}
                        </td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >
                          {$record.trade|escape|default:"&nbsp;"}
                          {assign var='region_sum_traders' value=$region_sum_traders+$record.trade}
                          {assign var='record_protocols' value=$record_protocols+$record.trade}
                        </td>
                        <td class="t_border hcenter" style="vertical-align: middle;" >
                          {$record.design|escape|default:"&nbsp;"}
                          {assign var='region_sum_designers' value=$region_sum_designers+$record.design}
                          {assign var='record_protocols' value=$record_protocols+$record.design}
                        </td>
                        <td class="hcenter" style="vertical-align: middle;" >
                          {$record_protocols|escape|default:"0"}
                          {assign var='region_sum_protocols' value=$region_sum_protocols+$record_protocols}
                        </td>
                    </tr>
                    {if $smarty.foreach.records.last}
                        <tr class="{$current_period_class}">
                          <td class="t_border hright" style="vertical-align: middle;">{#reports_table_sum#|escape}</td>
                          <td class="t_border hcenter" style="vertical-align: middle;">{$region_sum_partners|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter" style="vertical-align: middle;">{$region_sum_builders|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter" style="vertical-align: middle;">{$region_sum_investors|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter" style="vertical-align: middle;">{$region_sum_traders|escape|default:"&nbsp;"}</td>
                          <td class="t_border hcenter" style="vertical-align: middle;">{$region_sum_designers|escape|default:"&nbsp;"}</td>
                          <td class="hcenter" style="vertical-align: middle;">{$region_sum_protocols|escape|default:"0"}</td>
                        </tr>
                    {/if}
                {/foreach}
            {/foreach}
        {foreachelse}
        <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="10">{#no_items_found#|escape}</td>
        </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="10"></td>
        </tr>
      </table>
      <br /><button type="button" name="export_selected_records" class="button reports_export_button" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=export&amp;report_type={$report_type}&amp;pattern=&amp;custom_export_table=2';">{#export_report#|escape}</button>
      <br />
      <br />
      <br />
    </td>
  </tr>
</table>
{literal}
<script>
    function nresize(){
        $$(".report_prices").each(function(e){
            var height = e.parentNode.clientHeight;
            e.style.height = height+"px";
        });
    }
    Event.observe(window, 'load', nresize);
</script>
{/literal}