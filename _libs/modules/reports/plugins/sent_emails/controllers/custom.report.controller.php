<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'resent_emails':
                $this->_resentEmails();
                break;
            default:
                parent::execute();
                break;
        }
    }

    public function _resentEmails() {
        $result = array(
            'messages' => array(),
            'redirect' => ''
        );
        $registry = $this->registry;
        $request = $registry['request'];

        $report = $this->getReportType();
        $report = $report['name'];

        $i18n_files = array();
        // load plugin i18n files
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $registry['translater']->loadFile($i18n_files);

        // get report settings and define them as constants
        Reports::getReportSettings($registry, $report);

        $session_param = 'reports_' . $report . '_report';
        // get the report filters from session
        $selected_items = $registry['session']->get('reports_sent_emails_report', 'selected_items');

        $emails_ids = array();
        if (!empty($selected_items['ids'])) {
            $emails_ids = $selected_items['ids'];
        }

        if (!empty($emails_ids)) {
            require_once PH_MODULES_DIR . 'communications/models/communications.factory.php';
            $registry['session']->remove('reports_sent_emails_report', 'selected_items');
            $result['messages'] = Communications::resendEmails($this->registry, $emails_ids);
            $result['redirect'] = sprintf(
                '%s://%s%sindex.php?%s=reports&report_type=%s',
                (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'], $report
            );
        } else {
            $result['messages'][] = $registry['translater']->translate('error_reports_no_emails_to_resend');
        }

        print json_encode($result);
        exit;
    }

}

?>
