<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_date',
                'name'              => 'from_date',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
                'type'              => 'custom_filter',
                'width'             => 65,
                'required'          => 1,
                'additional_filter' => 'to_date',
                'first_filter_label'=> $this->i18n('reports_from_date'),
                'label'             => $this->i18n('reports_date_filter'),
                'help'              => $this->i18n('reports_date_filter')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_date',
                'name'            => 'to_date',
                'type'            => 'date',
                'width'           => 65,
                'label'           => $this->i18n('reports_to_date'),
                'help'            => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            // DEFINE SEND BY FILTER
            $filter = array (
                'custom_id'    => 'send_by',
                'name'         => 'send_by',
                'type'         => 'autocompleter',
                'width'        => 222,
                'label'        => $this->i18n('reports_send_by'),
                'help'         => $this->i18n('reports_send_by'),
                'autocomplete'      => array(
                    'search' => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'users',
                    'clear'        => 1,
                    'suggestions'  => '<firstname> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'send_by',
                    'fill_options' => array('$send_by => <id>',
                                            '$send_by_autocomplete => <firstname> <lastname>',
                    ),
                    'filters' => array(
                        '<active>' => '1,0',
                        '<id>'     => 'IN (' . implode(', ', $registry['db']->GetCol('SELECT id FROM ' . DB_TABLE_USERS . ' WHERE `active` IS NOT NULL AND `deleted_by` IS NOT NULL AND `active` IS NOT NULL')) . ')',
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'users', 'users'),
                )
            );
            $filters['send_by'] = $filter;

            //DEFINE SEND BY EMAIL FILTER
            $filter = array (
                'custom_id' => 'send_by_email',
                'name'      => 'send_by_email',
                'type'      => 'text',
                'label'     => $this->i18n('reports_send_by_email'),
                'help'      => $this->i18n('reports_send_by_email')
            );
            $filters['send_by_email'] = $filter;

            // DEFINE RECEIVED BY FILTER
            $filter = array (
                'custom_id'    => 'received_by',
                'name'         => 'received_by',
                'type'         => 'autocompleter',
                'width'        => 222,
                'label'        => $this->i18n('reports_received_by'),
                'help'         => $this->i18n('reports_received_by'),
                'autocomplete'      => array(
                    'search'       => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'received_by',
                    'fill_options' => array('$received_by => <id>',
                                            '$received_by_autocomplete => <name> <lastname>',
                    ),
                    'filters' => array(

                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['received_by'] = $filter;

            //DEFINE RECEIVED BY EMAIL FILTER
            $filter = array (
                'custom_id' => 'received_by_email',
                'name'      => 'received_by_email',
                'type'      => 'text',
                'label'     => $this->i18n('reports_received_by_name'),
                'help'      => $this->i18n('reports_received_by_name')
            );
            $filters['received_by_email'] = $filter;

            //DEFINE EMAIL SUBJECT FILTER
            $filter = array (
                'custom_id' => 'email_subject',
                'name'      => 'email_subject',
                'type'      => 'text',
                'label'     => $this->i18n('reports_email_subject'),
                'help'      => $this->i18n('reports_email_subject')
            );
            $filters['email_subject'] = $filter;

            //DEFINE EMAIL BODY FILTER
            $filter = array (
                'custom_id' => 'email_body',
                'name'      => 'email_body',
                'type'      => 'text',
                'label'     => $this->i18n('reports_email_body'),
                'help'      => $this->i18n('reports_email_body')
            );
            $filters['email_body'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }
            return $filters;
        }
    }
?>
