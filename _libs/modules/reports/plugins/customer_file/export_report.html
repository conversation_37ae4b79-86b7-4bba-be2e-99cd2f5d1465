<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_name#}</td>
        <td>{$reports_results.name|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_type#}</td>
        <td>{$reports_results.type|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_address#}</td>
        <td>{$reports_results.address|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_phone#}</td>
        <td>{$reports_results.phone|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_contact_person#}</td>
        <td>{$reports_results.contact_person_name|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_contact_person_phone#}</td>
        <td>{$reports_results.contact_person_phone|escape|default:"&nbsp;"}</td>
      </tr>
    </table>
    {if isset($reports_additional_options.calls_list)}
      <br />
      <br />
      <h1>{#reports_option_calls#}</h1><br />
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="text-align: center; vertical-align: middle;"><strong>{#num#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_call_document_date#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_call_description#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_call_next_step_status#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_call_next_step_description#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_call_employee#|escape}</strong></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_additional_options.calls_list item=calls}
          <tr>
            <td align="right" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
            <td style="vertical-align: middle; width: 150px;">
              {$calls.doc_num|escape} / {$calls.doc_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">{$calls.call_description|escape|default:"&nbsp;"}</td>
            <td style="vertical-align: middle;">{$calls.call_status_name|escape|default:"&nbsp;"}</td>
            <td style="vertical-align: middle;">{$calls.call_comment|escape|default:"&nbsp;"}</td>
            <td>{$calls.employee_name|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="6"><span style="color: red;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
    {/if}
    {if isset($reports_additional_options.customer_meetings)}
      <br />
      <br />
      <h1>{#reports_option_customer_meetings#}</h1><br />
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="text-align: center; vertical-align: middle;"><strong>{#num#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_meetings_document_date#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_meetings_type#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_meetings_note_next_step#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_meetings_employee#|escape}</strong></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_additional_options.customer_meetings item=meeting}
          <tr>
            <td align="right" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
            <td style="vertical-align: middle;">
              {$meeting.rec_num|escape} / {$meeting.rec_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">{$meeting.rec_type|escape|default:"&nbsp;"}</td>
            <td style="vertical-align: middle;">{$meeting.note_next_step|escape|default:"&nbsp;"}</td>
            <td style="vertical-align: middle;">
              {foreach from=$meeting.employees item=employee name=emp}
                {$employee|escape|default:"&nbsp;"}
                {if !$smarty.foreach.emp.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="5"><span style="color: red;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
    {/if}
    {if isset($reports_additional_options.offers)}
      <br />
      <br />
      <h1>{#reports_option_offers#}</h1><br />
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="text-align: center; vertical-align: middle;"><strong>{#num#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_offers_document_date#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_offers_document_type#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_offers_status#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_offers_employee#|escape}</strong></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_additional_options.offers item=offer}
          <tr>
            <td align="right" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
            <td style="vertical-align: middle;">
              {$offer.rec_num|escape} / {$offer.rec_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">{$offer.rec_type|escape|default:"&nbsp;"}</td>
            {capture assign='status_name'}reports_offers_documents_status_{$offer.status}{/capture}
            <td style="vertical-align: middle;">{$smarty.config.$status_name|escape|default:"&nbsp;"}{if $offer.substatus} / {$offer.substatus|escape|default:"&nbsp;"}{/if}</td>
            <td style="vertical-align: middle;">
              {$offer.employee|escape|default:"&nbsp;"}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="5"><span style="color: red;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
    {/if}
    {if isset($reports_additional_options.projects)}
      <br />
      <br />
      <h1>{#reports_option_projects#}</h1><br />
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="text-align: center; vertical-align: middle;"><strong>{#num#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_projects_code_date#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_projects_type#|escape}</strong></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_additional_options.projects item=project}
          <tr>
            <td align="right" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
            <td style="vertical-align: middle;">
              {$project.code|escape} / {$project.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">{$project.type|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="3"><span style="color: red;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
    {/if}
    {if isset($reports_additional_options.current_tasks)}
      <br />
      <br />
      <h1>{#reports_option_current_tasks#}</h1><br />
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="text-align: center; vertical-align: middle;"><strong>{#num#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_tasks_num_date#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_tasks_description#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_tasks_executed_by#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_tasks_status#|escape}</strong></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_additional_options.current_tasks item=task}
          <tr>
            <td align="right" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
            <td style="vertical-align: middle;">
              {if $task.minitask}({#reports_minitask#|mb_lower}){else}{$task.full_num|escape}{/if} / {$task.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">{$task.description|escape|default:"&nbsp;"}</td>
            {if $task.minitask}
              {capture assign='status_name'}reports_minitask_status_{$task.status}{/capture}
            {else}
              {capture assign='status_name'}reports_tasks_status_{$task.status}{/capture}
            {/if}
            <td style="vertical-align: middle;">{$smarty.config.$status_name|escape|default:"&nbsp;"}{if $task.substatus} / {$task.substatus|escape|default:"&nbsp;"}{/if}{if $task.minitask_comment} <i>({$task.minitask_comment})</i>{/if}</td>
            <td style="vertical-align: middle;">
              {foreach from=$task.employee item=empl name=emp}
                {$empl|escape|default:"&nbsp;"}
                {if !$smarty.foreach.emp.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="5"><span style="color: red;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
    {/if}
    {if isset($reports_additional_options.communications)}
      <br />
      <br />
      <h1>{#reports_option_communications#}</h1><br />
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="text-align: center; vertical-align: middle;"><strong>{#num#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_communications_date#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_communications_type_record#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_communications_description#|escape}</strong></td>
          <td style="text-align: center; vertical-align: middle;"><strong>{#reports_communications_added_by#|escape}</strong></td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_additional_options.communications item=communication}
          <tr>
            <td align="right" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
            <td style="vertical-align: middle;">
              {$communication.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">
              {capture assign='type_record'}reports_type_record_{$communication.parent_type}{/capture}
              {capture assign='record_communication_type'}reports_communication_type_{$communication.type_record}{/capture}
              {$smarty.config.$type_record}{if $communication.parent_type ne 'minitask'} / {$communication.type_name|escape|default:"&nbsp;"}{/if} ({$smarty.config.$record_communication_type|escape})
            </td>
            <td style="vertical-align: middle;">{$communication.content|default:"&nbsp;"}</td>
            <td style="vertical-align: middle;">{$communication.added_by|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="5"><span style="color: red;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
      </table>
    {/if}
  </body>
</html>