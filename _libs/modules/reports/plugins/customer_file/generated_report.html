{if !empty($reports_results)}
  <table border="0" cellpadding="0" cellspacing="0">
    <tr>
      <td>
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="t_odd">
            <td class="t_border" style="background-color:#DFDFDF; color:#555555; font-weight:bold; width: 180px;">{#reports_customer_name#}</td>
            <td style="width: 240px;"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$reports_results.id}">{$reports_results.name|escape|default:"&nbsp;"}</a></td>
          </tr>
          <tr class="t_even">
            <td class="t_border" style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_type#}</td>
            <td>{$reports_results.type|escape|default:"&nbsp;"}</td>
          </tr>
          <tr class="t_odd">
            <td class="t_border" style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_address#}</td>
            <td>{$reports_results.address|escape|default:"&nbsp;"}</td>
          </tr>
          <tr class="t_even">
            <td class="t_border" style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_phone#}</td>
            <td>{$reports_results.phone|escape|default:"&nbsp;"}</td>
          </tr>
          <tr class="t_odd">
            <td class="t_border" style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_contact_person#}</td>
            <td>{$reports_results.contact_person_name|escape|default:"&nbsp;"}</td>
          </tr>
          <tr class="t_even">
            <td class="t_border" style="background-color:#DFDFDF; color:#555555; font-weight:bold;">{#reports_customer_contact_person_phone#}</td>
            <td>{$reports_results.contact_person_phone|escape|default:"&nbsp;"}</td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>&nbsp;</td>
    </tr>
    {if isset($reports_additional_options.calls_list)}
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><h1>{#reports_option_calls#}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
            <tr class="reports_title_row">
              <td class="t_border" style="text-align: center; vertical-align: middle;">{#num#|escape}</td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 130px;">{#reports_call_document_date#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 240px;">{#reports_call_description#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 80px;">{#reports_call_next_step_status#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 240px;">{#reports_call_next_step_description#|escape}</div></td>
              <td style="text-align: center; vertical-align: middle;"><div style="width: 190px;">{#reports_call_employee#|escape}</div></td>
            </tr>
            {counter start=0 name='item_counter' print=false}
            {foreach from=$reports_additional_options.calls_list item=calls name='cl'}
              <tr class="{cycle name='table1' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="t_border hright" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
                <td class="t_border" style="vertical-align: middle; width: 130px;">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$calls.doc_id}">{$calls.doc_num|escape} / {$calls.call_date|escape|default:"&nbsp;"}</a>
                </td>
                {capture assign='call_description_full'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#full_comment#|escape}" title="{#full_comment#|escape}" onclick="toggleContent('call_description', {$smarty.foreach.cl.iteration});" />
                {/strip}{/capture}
                {capture assign='call_description_part'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#part_comment#|escape}" title="{#part_comment#|escape}" onclick="toggleContent('call_description', {$smarty.foreach.cl.iteration});" />
                {/strip}{/capture}
                <td class="t_border" style="width: 240px; vertical-align: middle;">
                  <div id="call_description_part_{$smarty.foreach.cl.iteration}">
                    {$calls.call_description|escape|mb_truncate:40:$call_description_full|nl2br}
                  </div>
                  <div id="call_description_full_{$smarty.foreach.cl.iteration}" style="display: none;">
                    {$calls.call_description|nl2br}{$call_description_part}
                  </div>
                </td>
                <td class="t_border" style="width: 80px; vertical-align: middle;">{$calls.call_status_name|escape|default:"&nbsp;"}</td>
                <td class="t_border" style="width: 240px; vertical-align: middle;">{$calls.call_comment|escape|default:"&nbsp;"}</td>
                <td style="width: 190px;">{$calls.employee_name|escape|default:"&nbsp;"}</td>
              </tr>
            {foreachelse}
              <tr class="{cycle name='table1' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="error" colspan="6">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
    {if isset($reports_additional_options.customer_meetings)}
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><h1>{#reports_option_customer_meetings#}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
            <tr class="reports_title_row">
              <td class="t_border" style="text-align: center; vertical-align: middle;">{#num#|escape}</td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 130px;">{#reports_meetings_document_date#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 240px;">{#reports_meetings_type#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 330px;">{#reports_meetings_note_next_step#|escape}</div></td>
              <td style="text-align: center; vertical-align: middle;"><div style="width: 190px;">{#reports_meetings_employee#|escape}</div></td>
            </tr>
            {counter start=0 name='item_counter' print=false}
            {foreach from=$reports_additional_options.customer_meetings item=meeting name=mt}
              <tr class="{cycle name='table2' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="t_border hright" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
                <td class="t_border" style="vertical-align: middle; width: 130px;">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$meeting.module}&amp;{$meeting.module}=view{if $meeting.module eq 'documents'}vars{/if}&amp;view{if $meeting.module eq 'documents'}vars{/if}={$meeting.rec_id}">{$meeting.rec_num|escape} / {$meeting.rec_date|date_format:#date_short#|escape|default:"&nbsp;"}</a>
                </td>
                <td class="t_border" style="vertical-align: middle; width: 240px;">{$meeting.rec_type|escape|default:"&nbsp;"}</td>
                {capture assign='meeting_note_next_step_full'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#full_comment#|escape}" title="{#full_comment#|escape}" onclick="toggleContent('meeting_note_next_step', {$smarty.foreach.mt.iteration});" />
                {/strip}{/capture}
                {capture assign='meeting_note_next_step_part'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#part_comment#|escape}" title="{#part_comment#|escape}" onclick="toggleContent('meeting_note_next_step', {$smarty.foreach.mt.iteration});" />
                {/strip}{/capture}
                <td class="t_border" style="width: 330px; vertical-align: middle;">
                  <div id="meeting_note_next_step_part_{$smarty.foreach.mt.iteration}">
                    {$meeting.note_next_step|escape|mb_truncate:40:$meeting_note_next_step_full|nl2br}
                  </div>
                  <div id="meeting_note_next_step_full_{$smarty.foreach.mt.iteration}" style="display: none;">
                    {$meeting.note_next_step|nl2br}{$meeting_note_next_step_part}
                  </div>
                </td>
                <td style="vertical-align: middle; width: 190px;">
                  {foreach from=$meeting.employees item=employee name=emp}
                    {$employee|escape|default:"&nbsp;"}
                    {if !$smarty.foreach.emp.last}<br />{/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
              </tr>
            {foreachelse}
              <tr class="{cycle name='table2' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="error" colspan="5">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
    {if isset($reports_additional_options.offers)}
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><h1>{#reports_option_offers#}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
            <tr class="reports_title_row">
              <td class="t_border" style="text-align: center; vertical-align: middle;">{#num#|escape}</td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 130px;">{#reports_offers_document_date#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 240px;">{#reports_offers_document_type#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 330px;">{#reports_offers_status#|escape}</div></td>
              <td style="text-align: center; vertical-align: middle;"><div style="width: 190px;">{#reports_offers_employee#|escape}</div></td>
            </tr>
            {counter start=0 name='item_counter' print=false}
            {foreach from=$reports_additional_options.offers item=offer}
              <tr class="{cycle name='table3' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="t_border hright" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
                <td class="t_border" style="vertical-align: middle; width: 130px;">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$offer.rec_id}">{$offer.rec_num|escape} / {$offer.rec_date|date_format:#date_short#|escape|default:"&nbsp;"}</a>
                </td>
                <td class="t_border" style="vertical-align: middle; width: 240px;">{$offer.rec_type|escape|default:"&nbsp;"}</td>
                {capture assign='status_name'}reports_offers_documents_status_{$offer.status}{/capture}
                <td class="t_border" style="vertical-align: middle; width: 330px;">{$smarty.config.$status_name|escape|default:"&nbsp;"}{if $offer.substatus} / {$offer.substatus|escape|default:"&nbsp;"}{/if}</td>
                <td style="vertical-align: middle; width: 190px;">
                  {$offer.employee|escape|default:"&nbsp;"}
                </td>
              </tr>
            {foreachelse}
              <tr class="{cycle name='table3' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="error" colspan="5">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
    {if isset($reports_additional_options.projects)}
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><h1>{#reports_option_projects#}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
            <tr class="reports_title_row">
              <td class="t_border" style="text-align: center; vertical-align: middle;">{#num#|escape}</td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 130px;">{#reports_projects_code_date#|escape}</div></td>
              <td style="text-align: center; vertical-align: middle;"><div style="width: 240px;">{#reports_projects_type#|escape}</div></td>
            </tr>
            {counter start=0 name='item_counter' print=false}
            {foreach from=$reports_additional_options.projects item=project}
              <tr class="{cycle name='table4' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="t_border hright" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
                <td class="t_border" style="vertical-align: middle; width: 130px;">
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$project.id}">{$project.code|escape} / {$project.date|date_format:#date_short#|escape|default:"&nbsp;"}</a>
                </td>
                <td style="vertical-align: middle; width: 240px;">{$project.type|escape|default:"&nbsp;"}</td>
              </tr>
            {foreachelse}
              <tr class="{cycle name='table4' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="error" colspan="3">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
    {if isset($reports_additional_options.current_tasks)}
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><h1>{#reports_option_current_tasks#}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
            <tr class="reports_title_row">
              <td class="t_border" style="text-align: center; vertical-align: middle;">{#num#|escape}</td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 130px;">{#reports_tasks_num_date#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 380px;">{#reports_tasks_description#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 190px;">{#reports_tasks_status#|escape}</div></td>
              <td style="text-align: center; vertical-align: middle;"><div style="width: 190px;">{#reports_tasks_executed_by#|escape}</div></td>
            </tr>
            {counter start=0 name='item_counter' print=false}
            {foreach from=$reports_additional_options.current_tasks item=task name=tas}
              <tr class="{cycle name='table5' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="t_border hright" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
                <td class="t_border" style="vertical-align: middle; width: 130px;">
                  {if $task.minitask}
                    ({#reports_minitask#|mb_lower}) / {$task.date|date_format:#date_short#|escape|default:"&nbsp;"}
                  {else}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=tasks&amp;tasks=view&amp;view={$task.id}">{$task.full_num|escape} / {$task.date|date_format:#date_short#|escape|default:"&nbsp;"}</a>
                  {/if}
                </td>
                {capture assign='task_description_full'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#full_comment#|escape}" title="{#full_comment#|escape}" onclick="toggleContent('task_description', {$smarty.foreach.tas.iteration});" />
                {/strip}{/capture}
                {capture assign='task_description_part'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#part_comment#|escape}" title="{#part_comment#|escape}" onclick="toggleContent('task_description', {$smarty.foreach.tas.iteration});" />
                {/strip}{/capture}
                <td class="t_border" style="width: 380px; vertical-align: middle;">
                  <div id="task_description_part_{$smarty.foreach.tas.iteration}">
                    {$task.description|escape|mb_truncate:40:$task_description_full|nl2br}
                  </div>
                  <div id="task_description_full_{$smarty.foreach.tas.iteration}" style="display: none;">
                    {$task.description|nl2br}{$task_description_part}
                  </div>
                </td>
                {if $task.minitask}
                  {capture assign='status_name'}reports_minitask_status_{$task.status}{/capture}
                {else}
                  {capture assign='status_name'}reports_tasks_status_{$task.status}{/capture}
                {/if}
                <td class="t_border" style="vertical-align: middle; width: 190px;">{$smarty.config.$status_name|escape|default:"&nbsp;"}{if $task.substatus} / {$task.substatus|escape|default:"&nbsp;"}{/if}{if $task.minitask_comment} <img src="{$theme->imagesUrl}info.png" width="12" height="12" border="0" alt="" class="help" {popup text=$task.minitask_comment|escape caption=#reports_tasks_minitask_comment#|escape} />{/if}</td>
                <td style="vertical-align: middle; width: 190px;">
                  {foreach from=$task.employee item=empl name=emp}
                    {$empl|escape|default:"&nbsp;"}
                    {if !$smarty.foreach.emp.last}<br />{/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
              </tr>
            {foreachelse}
              <tr class="{cycle name='table5' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="error" colspan="5">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
    {if isset($reports_additional_options.communications)}
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><h1>{#reports_option_communications#}</h1></td>
      </tr>
      <tr>
        <td>
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
            <tr class="reports_title_row">
              <td class="t_border" style="text-align: center; vertical-align: middle;">{#num#|escape}</td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 130px;">{#reports_communications_date#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 240px;">{#reports_communications_type_record#|escape}</div></td>
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 330px;">{#reports_communications_description#|escape}</div></td>
              <td style="text-align: center; vertical-align: middle;"><div style="width: 190px;">{#reports_communications_added_by#|escape}</div></td>
            </tr>
            {counter start=0 name='item_counter' print=false}
            {foreach from=$reports_additional_options.communications item=communication name=commmu}
              <tr class="{cycle name='table6' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="t_border hright" style="vertical-align: middle;">{counter name='item_counter' print=true}</td>
                <td class="t_border" style="vertical-align: middle; width: 130px;">
                  {$communication.date|date_format:#date_short#|escape|default:"&nbsp;"}
                </td>
                <td class="t_border" style="vertical-align: middle; width: 240px;">
                  {capture assign='type_record'}reports_type_record_{$communication.parent_type}{/capture}
                  {capture assign='record_communication_type'}reports_communication_type_{$communication.type_record}{/capture}
                  {$smarty.config.$type_record}{if $communication.parent_type ne 'minitask'} / {$communication.type_name|escape|default:"&nbsp;"}{/if} ({$smarty.config.$record_communication_type|escape})
                </td>
                {capture assign='communication_content_full'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#full_comment#|escape}" title="{#full_comment#|escape}" onclick="toggleContent('communication_content', {$smarty.foreach.commmu.iteration});" />
                {/strip}{/capture}
                {capture assign='communication_content_part'}{strip}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#part_comment#|escape}" title="{#part_comment#|escape}" onclick="toggleContent('communication_content', {$smarty.foreach.commmu.iteration});" />
                {/strip}{/capture}
                <td class="t_border" style="width: 330px; vertical-align: middle;">
                  <div id="communication_content_part_{$smarty.foreach.commmu.iteration}">
                    {$communication.content|strip_tags|escape|mb_truncate:40:$communication_content_full|nl2br}
                  </div>
                  <div id="communication_content_full_{$smarty.foreach.commmu.iteration}" style="display: none;">
                    {$communication.content|nl2br}{$communication_content_part}
                  </div>
                </td>
                <td style="vertical-align: middle; width: 190px;">{$communication.added_by|escape|default:"&nbsp;"}</td>
              </tr>
            {foreachelse}
              <tr class="{cycle name='table6' values='t_odd1 t_odd2,t_even1 t_even2'}">
                <td class="error" colspan="5">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
    {/if}
  </table>
{/if}
