{literal}
  <style type="text/css">
    .paid_value {
      color:#669900!important;
    }
    .partial_value {
      color: #ff9900!important;
    }
    .unpaid_value {
      color: #FF0000!important;
    }

    .width_num {
      width:     15px;
      min-width: 15px;
      max-width: 15px;
      text-align: center;
    }
    .width_patient {
      width:     85px;
      min-width: 85px;
      max-width: 85px;
    }
    .width_visit_date {
      width:     65px;
      min-width: 65px;
      max-width: 65px;
    }
    .width_visit_hour {
      width:     30px;
      min-width: 30px;
      max-width: 30px;
    }
    .width_visit_for {
      width:     65px;
      min-width: 65px;
      max-width: 65px;
    }
    .width_visiting_sheet {
      width:     120px;
      min-width: 120px;
      max-width: 120px;
    }
    .width_procedures {
      width:     130px;
      min-width: 130px;
      max-width: 130px;
    }
    .width_procedure_made_by {
      width:     75px;
      min-width: 75px;
      max-width: 75px;
    }
    .width_payment {
      width:     55px;
      min-width: 55px;
      max-width: 55px;
    }
    .width_procedure_price {
      width:     50px;
      min-width: 50px;
      max-width: 50px;
    }
    .width_procedure_quantity {
      width:     30px;
      min-width: 30px;
      max-width: 30px;
    }
    .width_currency {
      width:     40px;
      min-width: 40px;
      max-width: 40px;
      text-align: center;
    }
    .width_procedure_procedures_total {
      width:     50px;
      min-width: 50px;
      max-width: 50px;
    }
    .width_procedure_cosmetics_total {
      width:     50px;
      min-width: 50px;
      max-width: 50px;
    }
    .width_procedures_total {
      width:     50px;
      min-width: 50px;
      max-width: 50px;
    }
    .width_payment_status {
      width:     60px;
      min-width: 60px;
      max-width: 60px;
    }
    .width_payment_num_date {
      width:     60px;
      min-width: 60px;
      max-width: 60px;
    }
    .width_payment_value {
      width:     70px;
      min-width: 70px;
      max-width: 70px;
    }
  </style>
{/literal}

{if !$reports_results.error_flag}
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td colspan="2">
        <table border="0" cellpadding="5" cellspacing="0" class="reports_table">
          <tr class="reports_title_row">
            <th class="width_num">{#num#|escape}</th>
            <th class="width_patient">{#reports_patient#|escape}</th>
            <th class="width_visit_date">{#reports_visit_date#|escape}</th>
            <th class="width_visit_hour">{#reports_visit_hour#|escape}</th>
            <th class="width_visit_for">{#reports_visit_for#|escape}</th>
            <th class="width_visiting_sheet">{#reports_visiting_sheet#|escape}</th>
            <th class="width_procedures">{#reports_procedures#|escape}</th>
            <th class="width_procedure_made_by">{#reports_procedure_made_by#|escape}</th>
            <th class="width_payment">{#reports_payment#|escape}</th>
            <th class="width_procedure_price">{#reports_procedure_price#|escape}</th>
            <th class="width_procedure_quantity">{#reports_procedure_quantity#|escape}</th>
            <th class="width_procedure_procedures_total">{#reports_procedure_procedures_total#|escape}</th>
            <th class="width_procedure_cosmetics_total">{#reports_procedure_cosmetics_total#|escape}</th>
            <th class="width_procedures_total">{#reports_procedures_total#|escape}</th>
            <th class="width_currency">{#reports_currency#|escape}</th>
            <th class="width_payment_status">{#reports_payment_status#|escape}</th>
            <th class="width_payment_num_date">{#reports_payment_num_date#|escape}</th>
            <th class="width_payment_value">{#reports_payment_value#|escape}</th>
          </tr>
          {foreach from=$reports_results.first_table item=result name=results}
            {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
            {foreach from=$result.sheets item=sheet name=sh}
              {foreach from=$sheet.procedures item=proc name=prc}
                <tr class="{$current_row_class}">
                  {if $smarty.foreach.prc.first}
                    <td class="width_num hright" style="vertical-align: middle;" rowspan="{$sheet.rowspan}">
                      {counter name='item_counter' print=true}
                    </td>
                    <td class="width_patient" style="vertical-align: middle;" rowspan="{$sheet.rowspan}">
                      {$sheet.customer_name|escape|default:"&nbsp;"}
                    </td>
                  {/if}
                  {if $smarty.foreach.sh.first && $smarty.foreach.prc.first}
                    <td class="width_visit_date hcenter" style="vertical-align: middle;" rowspan="{$result.rowspan}">
                      {$result.date|date_format:#date_short#|escape|default:"&nbsp;"}
                    </td>
                  {/if}
                  {if $smarty.foreach.prc.first}
                    <td class="width_visit_hour" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {$sheet.visit_hour|escape|default:"&nbsp;"}
                    </td>
                    <td class="width_visit_for" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$sheet.visit}">{$sheet.visit_num|escape|default:"&nbsp;"}</a>
                    </td>
                    <td class="width_visiting_sheet" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view={$sheet.id}">{$sheet.num|escape|default:"&nbsp;"}</a>
                    </td>
                  {/if}
                  <td class="width_procedures" style="vertical-align: middle;">
                    {$proc.name|escape|default:"&nbsp;"} ({$proc.category|escape|default:"&nbsp;"})
                  </td>
                  <td class="width_procedure_made_by" style="vertical-align: middle;">
                    {$proc.made_by_name|escape|default:"&nbsp;"}
                  </td>
                  <td class="width_payment" style="vertical-align: middle;">
                    {$proc.payment|escape|default:"&nbsp;"}
                  </td>
                  <td class="width_procedure_price hright" style="vertical-align: middle;">
                    {$proc.price|string_format:"%.2f"|escape|default:"-"}
                  </td>
                  <td class="width_procedure_quantity hright" style="vertical-align: middle;">
                    {$proc.quantity|string_format:"%.2f"|escape|default:"-"}
                  </td>
                  {if $smarty.foreach.prc.first}
                    <td class="width_procedure_procedures_total hright" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {$sheet.total_procedures|string_format:"%.2f"|escape|default:"-"}
                    </td>
                    <td class="width_procedure_cosmetics_total hright" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {$sheet.total_cosmetics|string_format:"%.2f"|escape|default:"-"}
                    </td>
                    <td class="width_procedures_total hright" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {$sheet.total|string_format:"%.2f"|escape|default:"-"}
                    </td>
                    <td class="width_currency hright" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {$sheet.currency|escape}
                    </td>
                    <td class="width_payment_status {$sheet.payment_status}_value" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {capture assign='payment_status'}finance_payment_status_{$sheet.payment_status}{/capture}
                      {$smarty.config.$payment_status|escape|default:"&nbsp;"}
                    </td>
                    <td class="width_payment_num_date" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {foreach from=$sheet.payments item=payment name=pay}
                        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=payments&amp;payments=view&amp;view={$payment.id}">{$payment.full_num}</a> / {$payment.date|date_format:#date_short#|escape|default:"&nbsp;"}
                        {if !$smarty.foreach.pay.last}<br />{/if}
                      {foreachelse}
                        &nbsp;
                      {/foreach}
                    </td>
                    <td class="width_payment_value hright" nowrap="nowrap" rowspan="{$sheet.rowspan}" style="vertical-align: middle;">
                      {foreach from=$sheet.payments item=payment name=pay}
                        {$payment.sum|string_format:"%.2f"|escape|default:"0.00"} {$payment.currency|escape}{if !$smarty.foreach.pay.last}<br />{/if}
                      {foreachelse}
                        &nbsp;
                      {/foreach}
                    </td>
                  {/if}
                </tr>
              {/foreach}
            {/foreach}
          {foreachelse}
            <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
              <td class="error" colspan="18">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
        </table>
      </td>
    </tr>
  </table>

  {if $reports_results.show_second_table}
    <div style="margin-top: 20px; margin-right: 50px; float: left;">
      <h1 style="text-align: center;">{$reports_results.table2_name}</h1>
      <table border="0" cellpadding="0" cellspacing="0" class="reports_table">
        <tr class="reports_title_row hcenter">
          <th width="200">{#reports_company_cashbox_bank_account#|escape}</th>
          <th width="100">{#reports_starting_balance#|escape}</th>
          <th width="100">{#reports_ending_balance#|escape}</th>
          <th width="120" colspan="2">{#reports_ending_currencies#|escape}</th>
        </tr>
        {assign var='second_table_is_empty' value=false}
        {foreach from=$reports_results.second_table.containers item='container'}
          {foreach from=$container.currencies_end key='currency_name' item='currency_value' name='currencies'}
            <tr class="t_odd1 t_odd2">
              {if $smarty.foreach.currencies.first}
                {capture assign='rowspan'}{if is_array($container.currencies_end)}{$container.currencies_end|@count}{else}0{/if}{/capture}
                <td class="vmiddle" rowspan="{$rowspan}">
                  {$container.name|escape|default:"&nbsp;"}
                </td>
                <td class="hright vmiddle" rowspan="{$rowspan}">
                  {$container.total_start|default:0|string_format:"%.2f"|escape} {$container.currency|default:$smarty.const.SECOND_TABLE_CURRENCY}
                </td>
                <td class="hright vmiddle" rowspan="{$rowspan}">
                  {$container.total_end|default:0|string_format:"%.2f"|escape} {$container.currency|default:$smarty.const.SECOND_TABLE_CURRENCY}
                </td>
              {/if}
              <td class="hcenter" width="20">
                {$currency_name|escape}
              </td>
              <td class="hright" width="100">
                {$currency_value|default:0|string_format:"%.2f"|escape}
              </td>
            </tr>
          {foreachelse}
            {assign var='second_table_is_empty' value=true}
          {/foreach}
        {foreachelse}
          {assign var='second_table_is_empty' value=true}
        {/foreach}
        {if $second_table_is_empty}
          <tr class="t_odd1 t_odd2">
            <td class="error" colspan="5">{#no_items_found#|escape}</td>
          </tr>
        {/if}
        <tr class="reports_title_row">
          <th class="hright">
            {#reports_total#|escape}:
          </th>
          <th class="hright">
            {$reports_results.second_table.totals.start|default:0|string_format:"%.2f"|escape} {$smarty.const.SECOND_TABLE_CURRENCY}
          </th>
          <th class="hright">
            {$reports_results.second_table.totals.end|default:0|string_format:"%.2f"|escape} {$smarty.const.SECOND_TABLE_CURRENCY}
          </th>
          <th colspan="2">
            &nbsp;
          </th>
        </tr>
      </table>
    </div>
  {/if}

  <div style="margin-top: 20px; float: left;">
    <h1 style="text-align: center;">{$reports_results.table3_name}</h1>
    <table border="0" cellpadding="0" cellspacing="0" class="reports_table">
      <tr class="reports_title_row hcenter">
        <th>{#reports_company_cashbox_bank_account#|escape}</th>
        <th>{#reports_paid#|escape}</th>
        <th>{#reports_partially_paid#|escape}</th>
        <th>{#reports_unpaid#|escape}</th>
      </tr>
      {foreach from=$reports_results.third_table.containers item='container' name='containers'}
        <tr class="t_odd1 t_odd2">
          <td>
            {$container.name|escape|default:"&nbsp;"}
          </td>
          <td class="hright">
            {$container.paid|default:0|string_format:"%.2f"|escape}
          </td>
          <td class="hright">
            {$container.partial|default:0|string_format:"%.2f"|escape}
          </td>
          {if $smarty.foreach.containers.first}
            <td class="hright vmiddle" style="background-color: #F8F8F8; font-weight: normal; color: #000000;" rowspan="{$reports_results.third_table.containers|@count}">
              {$reports_results.third_table.total_unpaid|default:0|string_format:"%.2f"|escape}
            </td>
          {/if}
        </tr>
      {foreachelse}
        <tr class="t_odd1 t_odd2">
          <td class="error" colspan="4">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
      <tr class="reports_title_row">
        <th class="hright">
          {#reports_total#|escape}:
        </th>
        <th class="hright paid_value">
          {$reports_results.third_table.total_paid|string_format:"%.2f"|escape|default:"0.00"}
        </th>
        <th class="hright partial_value">
          {$reports_results.third_table.total_partial|string_format:"%.2f"|escape|default:"0.00"}
        </th>
        <th class="hright unpaid_value">
          {$reports_results.third_table.total_unpaid|string_format:"%.2f"|escape|default:"0.00"}
        </th>
      </tr>
    </table>
  </div>
{else}
  <span><h1 style="color:red">{#reports_no_results#|escape}</h1></span>
{/if}
