<table border="0" cellpadding="0" cellspacing="0" width="100%">
  {if ! $reports_additional_options.dont_show_main_report}
    <tr>
      <td>
        <a name="main_report" />
        <span style="font-size: 13px; color: #000000; margin-bottom: 0px; font-weight: bold;">{#reports_main_report#}</span> |
        {if isset($reports_additional_options.employees)}<a href="#report_for_employees">{if $smarty.const.LAWYER_INSTALLATION}{#reports_report_for_lawyers#|escape}{else}{#reports_report_for_employees#|escape}{/if}</a> | {/if}
        {if isset($reports_additional_options.projects)}<a href="#report_for_projects">{#reports_report_for_projects#}</a> | {/if}
        {if isset($reports_additional_options.customers)}<a href="#report_for_customers">{#reports_report_for_customers#}</a>{/if}
        <br />
        <br />
        {capture assign='first_table_rowspan'}{if $reports_additional_options.show_billing}12{else}11{/if}{/capture}
        {if $smarty.const.LAWYER_INSTALLATION}{math equation="x - y" x=$first_table_rowspan y=1 assign='first_table_rowspan'}{/if}
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row">
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.num}px;">{#num#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.full_num}px;">{#reports_full_num#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.type}px;">{#reports_type#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.customer}px;">{#reports_customer#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.project}px;">{if $smarty.const.LAWYER_INSTALLATION}{#reports_project#|escape}{else}{#reports_project_filter#|escape}{/if}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.employee}px;">{if $smarty.const.LAWYER_INSTALLATION}{#reports_lawyer#|escape}{else}{#reports_employee#|escape}{/if}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.date_begin}px;">{#reports_date_begin#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.date_end}px;">{#reports_date_end#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.description}px;">{#reports_description#|escape}</div></td>
            <td class="{if $reports_additional_options.show_billing || !$smarty.const.LAWYER_INSTALLATION}t_border {/if}reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.duration}px;">{#reports_duration#|escape}</div></td>
            {if $reports_additional_options.show_billing}
              <td class="{if !$smarty.const.LAWYER_INSTALLATION}t_border {/if}reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.duration_billing}px;">{#reports_duration_billing#|escape}</div></td>
            {/if}
            {if !$smarty.const.LAWYER_INSTALLATION}
              <td class="reports_title_centered_middle"><div style="width: {math equation="x - 10" x=$reports_additional_options.main_table_widths.status}px;">{#reports_status#|escape}</div></td>
            {/if}
          </tr>
          {counter start=$pagination.start name='item_counter' print=false}
          {foreach from=$reports_results item=result name=results}
            <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}{if $result.deleted_by} t_deleted{/if}{if !$result.include} row_orange{/if}">
              <td class="t_border hright" nowrap="nowrap" width="{$reports_additional_options.main_table_widths.num}">
                {counter name='item_counter' print=true}
              </td>
              <td class="t_border" width="{$reports_additional_options.main_table_widths.full_num}">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$result.record_type}s&amp;{$result.record_type}s=view&amp;view={$result.id}">{$result.full_num|default:"&nbsp;"}</a>
              </td>
              <td class="t_border" width="{$reports_additional_options.main_table_widths.type}">
                {$result.type_name|default:"&nbsp;"}
              </td>
              <td class="t_border" width="{$reports_additional_options.main_table_widths.customer}">
                {$result.customer_name|default:"&nbsp;"}
              </td>
              <td class="t_border" width="{$reports_additional_options.main_table_widths.project}">
                {$result.project_name|default:"&nbsp;"}
              </td>
              <td class="t_border" width="{$reports_additional_options.main_table_widths.employee}">
                {$result.made_by_name|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right" width="{$reports_additional_options.main_table_widths.date_begin}">
                {$result.start_period|date_format:#date_mid#|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right" width="{$reports_additional_options.main_table_widths.date_end}">
                {$result.end_period|date_format:#date_mid#|default:"&nbsp;"}
              </td>
              <td class="t_border" width="{$reports_additional_options.main_table_widths.description}">
                {if $result.subject && $smarty.const.LAWYER_INSTALLATION}<strong>{$result.subject|escape}</strong><br />{/if}
                {$result.content|escape|nl2br|url2href|default:"&nbsp;"}
              </td>
              <td class="{if $reports_additional_options.show_billing || !$smarty.const.LAWYER_INSTALLATION}t_border{/if}" align="right" width="{$reports_additional_options.main_table_widths.duration}">
                {$result.formated_duration|default:"&nbsp;"}
              </td>
              {if $reports_additional_options.show_billing}
                <td class="{if !$smarty.const.LAWYER_INSTALLATION}t_border{/if}" align="right" width="{$reports_additional_options.main_table_widths.duration_billing}">{$result.formated_duration_billing|default:"&nbsp;"}</td>
              {/if}
              {if !$smarty.const.LAWYER_INSTALLATION}
                <td align="right" width="{$reports_additional_options.main_table_widths.status}">
                  {capture assign='status_icon'}{$theme->imagesUrl}{if $result.record_type eq 'project'}small/{/if}{$result.record_type}s_{$result.status}.png{/capture}
                  {if $result.substatus}{$result.substatus}&nbsp;{/if}<img src="{$status_icon}" border="0" alt="" title="" />
                </td>
              {/if}
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="{$first_table_rowspan}">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          {if $smarty.const.LAWYER_INSTALLATION}
            <tr class="row_blue">
              <td colspan="9" class="t_border" align="right">
                <strong>{#reports_total#|escape}:</strong>
              </td>
              <td align="right"{if $reports_additional_options.show_billing} class="t_border"{/if}>
                <strong>{$reports_additional_options.total_duration_formated|default:"0:00"}</strong>
              </td>
              {if $reports_additional_options.show_billing}
                <td align="right">
                  <strong>{$reports_additional_options.total_billing_duration_formated|default:"0:00"}</strong>
                </td>
              {/if}
            </tr>
          {/if}
          <tr>
            <td class="t_footer" colspan="{$first_table_rowspan}"></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td height="30">&nbsp;</td>
    </tr>
  {/if}


  {if isset($reports_additional_options.employees)}
    <tr>
      <td>
        <a name="report_for_employees" />
        {if ! $reports_additional_options.dont_show_main_report}<a href="#main_report">{#reports_main_report#}</a> | {/if}
        <span style="font-size: 13px; color: #000000; margin-bottom: 0px; font-weight: bold;">{if $smarty.const.LAWYER_INSTALLATION}{#reports_report_for_lawyers#|escape}{else}{#reports_report_for_employees#|escape}{/if}</span> |
        {if isset($reports_additional_options.projects)}<a href="#report_for_projects">{#reports_report_for_projects#}</a> | {/if}
        {if isset($reports_additional_options.customers)}<a href="#report_for_customers">{#reports_report_for_customers#}</a>{/if}
        <br />
        <br />
      </td>
    </tr>
    <tr>
      <td>
        {assign var='employee_table_colspan' value='3'}
        {if !$smarty.const.LAWYER_INSTALLATION}
          {math equation="x + y" x=$employee_table_colspan y=1 assign='employee_table_colspan'}
        {/if}
        {if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing}
          {math equation="x + y" x=$employee_table_colspan y=1 assign='employee_table_colspan'}
        {/if}
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row">
            <td class="t_border reports_title_centered_middle"><div style="width: 120px;">{if $smarty.const.LAWYER_INSTALLATION}{#reports_lawyer_name#|escape}{else}{#reports_employee_name#|escape}{/if}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: 60px;">{#reports_projects#|escape}</div></td>
            {if !$smarty.const.LAWYER_INSTALLATION}
              <td class="t_border reports_title_centered_middle"><div style="width: 60px;">{#reports_tasks#|escape}</div></td>
            {/if}
            <td class="reports_title_centered_middle{if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing} t_border{/if}"><div style="width: 60px;">{#reports_duration#|escape}</div></td>
            {if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing}
              <td class="reports_title_centered_middle"><div style="width: 60px;">{#reports_duration_billing#|escape}</div></td>
            {/if}
          </tr>
          {foreach from=$reports_additional_options.employees item=result name=results_employee}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" width="130">
                {$result.employee_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" align="right" width="70">
                {$result.projects|default:"0"}
              </td>
              {if !$smarty.const.LAWYER_INSTALLATION}
                <td class="t_border" align="right" width="70">
                  {$result.tasks|default:"0"}
                </td>
              {/if}
              <td align="right" class="{if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing}t_border{/if}" width="70">
                {$result.formated_duration|default:"0:00"}
              </td>
              {if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing}
                <td align="right" width="70">
                  {$result.formated_billing_duration|default:"0:00"}
                </td>
              {/if}
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="{$employee_table_colspan}">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="{$employee_table_colspan}"></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td height="30">&nbsp;</td>
    </tr>
  {/if}


  {if isset($reports_additional_options.projects)}
    <tr>
      <td>
        <a name="report_for_projects" />
        {if ! $reports_additional_options.dont_show_main_report}<a href="#main_report">{#reports_main_report#}</a> | {/if}
        {if isset($reports_additional_options.employees)}<a href="#report_for_employees">{if $smarty.const.LAWYER_INSTALLATION}{#reports_report_for_lawyers#|escape}{else}{#reports_report_for_employees#|escape}{/if}</a> | {/if}
        <span style="font-size: 13px; color: #000000; margin-bottom: 0px; font-weight: bold;">{#reports_report_for_projects#}</span> |
        {if isset($reports_additional_options.customers)}<a href="#report_for_customers">{#reports_report_for_customers#}</a>{/if}
        <br />
        <br />
      </td>
    </tr>
    <tr>
      <td>
        {if $smarty.const.LAWYER_INSTALLATION}
          {if $reports_additional_options.show_billing}
            {assign var='project_table_colspan' value='6'}
          {else}
            {assign var='project_table_colspan' value='5'}
          {/if}
        {else}
          {assign var='project_table_colspan' value='7'}
        {/if}

        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row">
            <td class="t_border reports_title_centered_middle"><div style="width: 90px;">{#reports_project_code#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: 190px;">{if $smarty.const.LAWYER_INSTALLATION}{#reports_project#|escape}{else}{#reports_project_filter#|escape}{/if}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: 290px;">{#reports_customer#|escape}</div></td>
            <td class="t_border reports_title_centered_middle"><div style="width: 120px;">{if $smarty.const.LAWYER_INSTALLATION}{#reports_lawyer_name#|escape}{else}{#reports_employee_name#|escape}{/if}</div></td>
            {if $smarty.const.LAWYER_INSTALLATION}
              <td class="reports_title_centered_middle{if $reports_additional_options.show_billing} t_border{/if}"><div style="width: 60px;">{#reports_duration#|escape}</div></td>
              {if $reports_additional_options.show_billing}
                <td class="reports_title_centered_middle"><div style="width: 60px;">{#reports_duration_billing#|escape}</div></td>
              {/if}
            {else}
              <td class="t_border reports_title_centered_middle"><div style="width: 60px;">{#reports_tasks#|escape}</div></td>
              <td class="t_border reports_title_centered_middle"><div style="width: 60px;">{#reports_duration#|escape}</div></td>
              <td class="reports_title_centered_middle"><div style="width: 60px;">{#reports_duration#|escape}</div></td>
            {/if}
          </tr>
          {foreach from=$reports_additional_options.projects item=result name=results_projects}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" width="100">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$result.project_id}">{$result.project_code|escape|default:"&nbsp;"}</a>
              </td>
              <td class="t_border" width="200">
                {$result.project_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" width="300">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.project_customer}">{$result.project_customer_name|escape|default:"&nbsp;"}</a>
              </td>
              <td class="t_border" width="130">
                {foreach from=$result.employees_names item=employee name=emps}
                  {$employee|escape|default:"&nbsp;"}
                  {if !$smarty.foreach.emps.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              {if $smarty.const.LAWYER_INSTALLATION}
                <td align="right" width="70"{if $reports_additional_options.show_billing} class="t_border"{/if}>
                  {$result.formated_total_duration|default:"0:00"}
                </td>
                {if $reports_additional_options.show_billing}
                  <td align="right" width="70">
                    {$result.formated_total_billing_duration|default:"0:00"}
                  </td>
                {/if}
              {else}
                <td class="t_border" align="right" width="70">
                  {foreach from=$result.tasks item=task name=tas}
                    {$task|escape|default:"0"}{if !$smarty.foreach.tas.last}<br />{/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
                <td class="t_border" align="right" width="70">
                  {foreach from=$result.formated_duration item=duration name=dur}
                    {$duration|escape|default:"0:00"}{if !$smarty.foreach.dur.last}<br />{/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
                <td align="right" width="70">
                  {$result.formated_total_duration|default:"0:00"}
                </td>
              {/if}
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="{$project_table_colspan}">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="{$project_table_colspan}"></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td height="30">&nbsp;</td>
    </tr>
  {/if}


  {if isset($reports_additional_options.customers)}
    <tr>
      <td>
        <a name="report_for_customers" />
        {if ! $reports_additional_options.dont_show_main_report}<a href="#main_report">{#reports_main_report#}</a> | {/if}
        {if isset($reports_additional_options.employees)}<a href="#report_for_employees">{if $smarty.const.LAWYER_INSTALLATION}{#reports_report_for_lawyers#|escape}{else}{#reports_report_for_employees#|escape}{/if}</a> | {/if}
        {if isset($reports_additional_options.projects)}<a href="#report_for_projects">{#reports_report_for_projects#}</a> | {/if}
        <span style="font-size: 13px; color: #000000; margin-bottom: 0px; font-weight: bold;">{#reports_report_for_customers#}</span>
        <br />
        <br />
      </td>
    </tr>
    <tr>
      <td style="vertical-align: top;">
        {if isset($reports_additional_options.customers)}
          {if $smarty.const.LAWYER_INSTALLATION}
            {if $reports_additional_options.show_billing}
              {assign var='customer_table_colspan' value='4'}
            {else}
              {assign var='customer_table_colspan' value='3'}
            {/if}
          {else}
            {assign var='customer_table_colspan' value='4'}
          {/if}
          <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
            <tr class="reports_title_row">
              <td class="t_border reports_title_centered_middle"><div style="width: 290px;">{#reports_customer_name#|escape}</div></td>
              <td class="t_border reports_title_centered_middle"><div style="width: 60px;">{#reports_projects#|escape}</div></td>
              {if !$smarty.const.LAWYER_INSTALLATION}<td class="t_border reports_title_centered_middle"><div style="width: 60px;">{#reports_tasks#|escape}</div></td>{/if}
              <td class="reports_title_centered_middle{if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing} t_border{/if}"><div style="width: 60px;">{#reports_duration#|escape}</div></td>
              {if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing}
                <td class="reports_title_centered_middle"><div style="width: 60px;">{#reports_duration_billing#|escape}</div></td>
              {/if}
            </tr>
            {foreach from=$reports_additional_options.customers item=result name=results_customers}
              <tr class="{cycle values='t_odd,t_even'}">
                <td class="t_border" width="300">
                  {$result.customer_name|escape|default:"&nbsp;"}
                </td>
                <td class="t_border" align="right" width="70">
                  {$result.projects|default:"0"}
                </td>
                {if !$smarty.const.LAWYER_INSTALLATION}
                  <td class="t_border" align="right" width="70">
                    {$result.tasks|default:"0"}
                  </td>
                {/if}
                <td align="right" width="70"{if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing} class="t_border"{/if}>
                  {$result.formated_duration|default:"0:00"}
                </td>
                {if $smarty.const.LAWYER_INSTALLATION && $reports_additional_options.show_billing}
                  <td align="right" width="70">
                    {$result.formated_billing_duration|default:"0:00"}
                  </td>
                {/if}
              </tr>
            {foreachelse}
              <tr class="{cycle values='t_odd,t_even'}">
                <td class="error" colspan="{$customer_table_colspan}">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
            <tr>
              <td class="t_footer" colspan="{$customer_table_colspan}"></td>
            </tr>
          </table>
        {else}
          &nbsp;
        {/if}
      </td>
    </tr>
  {/if}


  <tr>
    <td style="vertical-align: top;">
      {if $reports_additional_options.charts}
        <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; height: 100%;">
          {foreach from=$reports_additional_options.charts item='single_chart'}
            <tr>
              <td style="vertical-align: top; width: 100%;">
                {include file="chart.html" chart=$single_chart}
              </td>
            </tr>
          {/foreach}
        </table>
      {else}
        &nbsp;
      {/if}
    </td>
  </tr>
</table>
