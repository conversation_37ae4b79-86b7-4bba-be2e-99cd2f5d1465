<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'create_daily_schedule':
                $this->_createDailySchedule();
                break;
            case 'ajax_load_ray_results':
                $this->_loadRayResults();
                break;
            case 'ajax_prepare_redirect_link':
                $this->_prepareRedirectLink();
                break;
            default:
                parent::execute();
        }
    }

    /**
     * Function to create/update daily schedule document with selected rows
     */
    private function _createDailySchedule() {
        $registry = $this->registry;

        // get request and all post params
        $request = $registry['request'];

        $items = array_filter($request['items']);
        asort($items);

        if (!$items) {
            $registry['messages']->setError(sprintf($this->i18n('reports_no_selected_records')));
            $registry['messages']->insertInSession($this->registry);

            // redirect to the report
            // IMPORTANT: do not set genarate_report as action to preserve the selected items in the session
            // (items are not set in session any more because they do not use standard list checkbox functionality)
            $redirectUrl = sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry->get('module_param'), 'reports', 'report_type', $request['report_type']);

            header('Location: ' . $redirectUrl);
            exit;
        }

        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        $i18n_files = array();
        // load plugin i18n files
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        // load i18n files for documents
        $i18n_files[] = sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $registry['lang'], '/documents.ini');
        $registry['translater']->loadFile($i18n_files);

        Reports::getReportSettings($registry, $report);

        // PREPARE THE DATA FOR THE NEW DOCUMENT
        // include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

        // get the report filters from session (user of employee - agent, date - date, ray_id - ray)
        $report_filters = $registry['session']->get('reports_' . $report . '_report');

        // this could happen if report is run from dashlet after it is has been generated in module,
        // and then "create daily schedule" is run immediately
        $filter_ray = array_filter($report_filters['ray']);
        $filter_ray = reset($filter_ray);
        if (empty($report_filters['date']) || empty($report_filters['agent']) || empty($filter_ray)) {
            $registry['messages']->setError($this->i18n('error_reports_create_daily_schedule'));
            $registry['messages']->insertInSession($this->registry);

            // redirect to the report
            $redirectUrl = sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry->get('module_param'), 'reports', 'report_type', $report);
            header('Location: ' . $redirectUrl);
            exit;
        }

        $registry['db']->StartTrans();

        // get employee of user
        $query = 'SELECT employee FROM ' . DB_TABLE_USERS . ' WHERE id = \'' . $report_filters['agent'] . '\'';
        $report_filters['agent'] = $registry['db']->GetOne($query);

        // get name of selected ray
        $query = 'SELECT name FROM ' . DB_TABLE_NOMENCLATURES_I18N . "\n" .
                 'WHERE parent_id="' . $filter_ray . '" AND lang="' . $registry['lang'] . '"';
        $ray_name = $registry['db']->GetOne($query);

        // try to find document for the selected date and agent (do not check for ray match)
        $filters = array(
            'where' => array(
                'd.type = \'' . DOC_TYPE_DAILY_SCHEDULE . '\'',
                'd.date = \'' . $report_filters['date'] . '\'',
                'd.employee = \'' . $report_filters['agent'] . '\'',
                'd.active = 1',
                'd.deleted_by = 0'
            ),
            'sort' => array('d.id DESC'),
            'limit' => '0, 1',
            'sanitize' => false
        );
        $document = Documents::search($this->registry, $filters);
        if (!empty($document)) {
            $document = reset($document);
        }

        if (!$document) {
            $document = new Document($this->registry);

            // get default options for this type
            $sql_default_options = 'SELECT dti18n.name as default_name, dt.default_department, dt.default_group, dt.default_media, dt.default_customer' . "\n" .
                                   'FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                   'LEFT JOIN  ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                   '  ON (dt.id=dti18n.parent_id AND dti18n.lang="' . $registry['lang'] . '")' . "\n" .
                                   'WHERE dt.id="' . DOC_TYPE_DAILY_SCHEDULE . '"';
            $default_document_data = $registry['db']->GetRow($sql_default_options);

            $document->set('type', DOC_TYPE_DAILY_SCHEDULE, true);

            // prepare old values of additional vars for audit
            $this->registry->set('get_old_vars', true, true);
            $document->getVars();
            $this->registry->set('get_old_vars', false, true);

            $old_document = clone $document;
            $old_document->sanitize();

            $document->set('name', $default_document_data['default_name'], true);
            $document->set('customer', $default_document_data['default_customer'], true);
            $document->set('department', $default_document_data['default_department'], true);
            $document->set('group', $default_document_data['default_group'], true);
            $document->set('active', 1, true);
            $document->set('employee', $report_filters['agent'], true);
            $document->set('date', $report_filters['date'], true);

            $vars = $document->get('vars');
            foreach ($vars as $idx => $var) {
                if ($var['name'] == DAILY_SCHEDULE_RAY_ID_VAR) {
                    $vars[$idx]['value'] = $filter_ray;
                } elseif ($var['name'] == DAILY_SCHEDULE_RAY_NAME_VAR) {
                    $vars[$idx]['value'] = $ray_name;
                }
            }
            $document->set('vars', $vars, true);
            unset($vars);

            // try to save the document
            if ($document->save()) {
                $filters = array('where' => array('d.id = ' . $document->get('id')),
                                 'model_lang' => $document->get('model_lang'));
                $added_document = Documents::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $added_document->getVars();
                $this->registry->set('get_old_vars', false, true);

                // write history
                Documents_History::saveData($this->registry, array('model' => $added_document, 'action_type' => 'add', 'new_model' => $added_document, 'old_model' => $old_document));

                $old_document = clone $added_document;
                $old_document->sanitize();
            }
        } else {
            $old_document = clone $document;
            // prepare old values of additional vars for audit
            $this->registry->set('get_old_vars', true, true);
            $old_document->getVars();
            $this->registry->set('get_old_vars', false, true);
            $added_document = clone $old_document;
            $old_document->sanitize();
        }

        if (!$registry['db']->HasFailedTrans()) {

            // SAVE GT2
            $current_document_ray = 0;
            if ($added_document->get('vars')) {
                $vars = $added_document->get('vars');
                foreach ($vars as $var) {
                    if ($var['type'] == 'gt2') {
                        $gt2 = $var;
                    } elseif ($var['name'] == DAILY_SCHEDULE_RAY_ID_VAR) {
                        $current_document_ray = $var['value'];
                    }
                }
                unset($vars);
            }
            if (empty($gt2)) {
                $this->registry->set('get_old_vars', true, true);
                $added_document->getGT2Vars();
                $gt2 = $added_document->get('grouping_table_2');
            }

            // get the already added targets
            $added_targets = array();
            foreach ($gt2['values'] as $row) {
                if (!empty($row['article_id']) && !empty($row['free_field3'])) {
                    $added_targets[] = $row['article_id'] . '_' . $row['free_field3'];
                }
            }

            $entities = array(
                'pharmacy' => array('model' => 'Customer', 'model_type' => preg_split('#\s*,\s*#', CUST_TYPE_PHARMACY)),
                'doctor' => array('model' => 'Customer', 'model_type' => preg_split('#\s*,\s*#', CUST_TYPE_DOCTOR))
            );
            foreach ($entities as $entity => &$props) {
                $add_vars_entity = array(
                    constant(strtoupper($entity) . '_CITY_VAR'),
                    constant(strtoupper($entity) . '_ADDRESS_VAR'),
                    constant(strtoupper($entity) . '_RAY_ID_VAR'),
                    constant(strtoupper($entity) . '_SECOND_RAY_ID_VAR'),
                    constant(strtoupper($entity) . '_THIRD_RAY_ID_VAR'),
                    constant(strtoupper($entity) . '_FOURTH_RAY_ID_VAR'),
                    constant(strtoupper($entity) . '_FIFTH_RAY_ID_VAR')
                );
                $condition = '(';
                foreach ($props as $k => $v) {
                    if (is_array($v)) {
                        $condition .= $k . ' IN ("' . implode('", "', $v) . '") AND ';
                    } else {
                        $condition .= $k . ' = "' . $v . '" AND ';
                    }
                }
                $condition .= 'name IN ("' . implode('", "', $add_vars_entity) . '"))';

                $props['vars'] = array_map(
                    function($a) {
                        return preg_split('#\s*,\s*#', $a);
                    },
                    $registry['db']->GetAssoc(
                        'SELECT name, GROUP_CONCAT(id)' . "\n" .
                        'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                        'WHERE ' . $condition . "\n" .
                        'GROUP BY name'
                    ) + array_fill_keys($add_vars_entity, '')
                );
            }
            unset($prop);

            $doctors_pharmacies_data = array();

            $idx = 0;
            // build the rows for gt2 table
            foreach ($items as $item => $position) {
                if (in_array($item, $added_targets)) {
                    // it is alredy added, skip it silently
                    // ToDo: make it verbose with some warnings?!
                    continue;
                }

                // item is in format <customer_id>_<number_of_doctor_cabinet_row>
                $item = explode('_', $item);
                $article_id = $item[0];
                $num = !empty($item[1]) ? $item[1] : '0';

                if (empty($doctors_pharmacies_data[$article_id])) {
                    // get name and address of the doctor/pharmacy
                    $entity = !$num ? 'pharmacy' : 'doctor';
                    $query =
                        'SELECT TRIM(CONCAT(ci18n.name, \' \', ci18n.lastname)) AS name, ccstm1.value as city, ccstm2.value as address, ccstm_ray.value as ray, ni18n.name as ray_name' . "\n" .
                        'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                        '  ON c.id=ci18n.parent_id AND ci18n.lang=\'' . $added_document->get('model_lang') . '\'' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm1' . "\n" .
                        '  ON ccstm1.num=1 AND c.id=ccstm1.model_id AND ccstm1.var_id IN (\'' . implode('\', \'', $entities[$entity]['vars'][constant(strtoupper($entity) . '_CITY_VAR')]) . '\')' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm2' . "\n" .
                        '  ON ccstm1.num=ccstm2.num AND c.id=ccstm2.model_id AND ccstm2.var_id IN (\'' . implode('\', \'', $entities[$entity]['vars'][constant(strtoupper($entity) . '_ADDRESS_VAR')]) . '\')  AND (ccstm2.lang="" OR ccstm2.lang="' . $this->registry['lang'] . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_ray' . "\n" .
                                 '  ON c.id = ccstm_ray.model_id' . "\n" .
                                 '    AND ccstm_ray.var_id IN (\'' . implode('\', \'', array_merge(
                                    ($entities[$entity]['vars'][constant(strtoupper($entity) . '_RAY_ID_VAR')] ?? []),
                                    ($entities[$entity]['vars'][constant(strtoupper($entity) . '_SECOND_RAY_ID_VAR')] ?? []),
                                    ($entities[$entity]['vars'][constant(strtoupper($entity) . '_THIRD_RAY_ID_VAR')] ?? []),
                                    ($entities[$entity]['vars'][constant(strtoupper($entity) . '_FOURTH_RAY_ID_VAR')] ?? []),
                                    ($entities[$entity]['vars'][constant(strtoupper($entity) . '_FIFTH_RAY_ID_VAR')] ?? []),
                                 )) . '\') AND ccstm_ray.value != \'\'' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                        '  ON ccstm_ray.value=ni18n.parent_id AND ni18n.lang=\'' . $added_document->get('model_lang') . '\'' . "\n" .
                        'WHERE c.type IN (\'' . implode('\', \'', $entities[$entity]['model_type']) . '\') AND c.active=1 AND c.deleted_by=0 AND c.id=\'' . $article_id . '\'' . "\n" .
                        'GROUP BY c.id, ccstm_ray.value' . "\n";
                    $doctors_pharmacies_data[$article_id] = $registry['db']->GetRow($query);
                    // not found
                    if (!$doctors_pharmacies_data[$article_id]) {
                        continue;
                    }
                }

                $article_name = $doctors_pharmacies_data[$article_id]['name'];
                $free_field2 = implode(', ', array_filter([$doctors_pharmacies_data[$article_id]['city'], $doctors_pharmacies_data[$article_id]['address']]));
                $free_field4 = $current_document_ray;

                // if rows are for another ray than the one of the document, add ray name to the address field
                if ($doctors_pharmacies_data[$article_id]['ray'] != $current_document_ray) {
                    $free_field2 = implode(', ', array_filter([$doctors_pharmacies_data[$article_id]['ray_name'], $free_field2]));
                    $free_field4 = $doctors_pharmacies_data[$article_id]['ray'];
                }

                // prepare the rows to be added (hence negative row indices)
                $gt2['values'][--$idx] = array(
                    'article_id'                         => $article_id,
                    'article_name'                       => $article_name,
                    'free_field1'                        => DAILY_SCHEDULE_STATUS_PLANNED,
                    'free_field2'                        => trim($free_field2),
                    // store the number of the row of addresses for doctors, for pharmacies it is 0
                    'free_field3'                        => $num,
                    'free_field4'                        => $free_field4,
                    'free_field5'                        => intval($position),
                    'price'                              => 0,
                    'quantity'                           => 1,
                );
            }

            // set the new values in the document
            $added_document->set('grouping_table_2', $gt2, true);
            $added_document->calculateGT2();
            $added_document->set('table_values_are_set', true, true);
            $added_document->saveGT2Vars();

            $filters = array('where' => array('d.id = ' . $added_document->get('id')),
                             'model_lang' => $added_document->get('model_lang'));
            $new_document = Documents::searchOne($this->registry, $filters);

            $this->registry->set('get_old_vars', true, true);
            $new_document->getVars();
            $this->registry->set('get_old_vars', false, true);

            // write history
            Documents_History::saveData($this->registry, array('model' => $new_document, 'action_type' => 'edit', 'new_model' => $new_document, 'old_model' => $old_document));

            // Execute action type automations
            if (!$this->executeActionAutomations($old_document, $new_document, 'edit')) {
                $registry['db']->FailTrans();
            }
        }

        // the transaction status could be checked only before CompleteTrans()
        $dbTransError = $registry['db']->HasFailedTrans();

        if ($dbTransError) {
            // Doh! Something went wrong... in Timbuktu!
            $registry['messages']->setError(sprintf($this->i18n('error_reports_creating_daily_schedule'), General::strftime($this->i18n('date_short'), $report_filters['date']), -100));
            $registry['messages']->insertInSession($this->registry);

            // redirect to the report
            // IMPORTANT: do not set genarate_report as action to preserve the selected items in the session
            $redirectUrl = sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry->get('module_param'), 'reports', 'report_type', $report);
        } else {
            // Surprise, surprise. The document is added. Let's have fun now! :)
            $registry['db']->CompleteTrans();

            // redirect to the daily schedule document
            $redirectUrl = sprintf('%s?%s=%s&%s=%s&%s=%d', $_SERVER['PHP_SELF'], $registry->get('module_param'), 'documents', 'documents', 'view', 'view', $new_document->get('id'));
        }
        header('Location: ' . $redirectUrl);
        exit;
    }

    /**
     * Loads results for selected ray from dashlet
     */
    private function _loadRayResults() {

        $registry = $this->registry;
        $request = $registry['request'];

        $result = array();

        $report = $this->getReportType();
        if (empty($report)) {
            $result['errors'] = array($this->i18n('error_reports_required_settings'));
            print 'var result = ' . json_encode($result) . ';';
            exit;
        }
        $report = $report['name'];

        $i18n_files = array();
        // load plugin i18n files
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $registry['translater']->loadFile($i18n_files);

        $post = $request->isPost() ? $request->getAll('post') : array();
        if (empty($post['ray'])) {
            $result['errors'] = array($this->i18n('error_reports_dashlet_ray'));
            print 'var result = ' . json_encode($result) . ';';
            exit;
        }

        // get report settings and define them as constants
        Reports::getReportSettings($registry, $report);

        $session_param = 'reports_' . $report . '_report';
        // get the report filters from session (they have already been prepared
        // and set when the dashlet was loaded)
        $report_filters = $registry['session']->get($session_param);

        // update posted filters in session because they might have been
        // modified by the report in the meantime
        $report_filters = array_intersect_key($post, $report_filters);

        $registry['session']->set($session_param, $report_filters, '', true);

        // set filters in registry for the dashlet viewer
        $old_filters = $registry->get('filters');
        $registry->set('filters', $report_filters, true);

        // set flag in registry for the search to be performed
        $old_generated_report = $registry->get('generated_report');
        $registry->set('generated_report', 1, true);

        // we will use the dashlet viewer to do all the work and
        // the generated_report.html template to display the results
        // (because we are lazy obviously)
        require_once PH_MODULES_DIR . 'reports/viewers/reports.dashlet.viewer.php';
        $viewer = new Reports_Dashlet_Viewer($registry);
        // do the work
        $viewer->prepare();
        // set custom template
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $report . '/';
        $viewer->template = 'generated_report.html';
        // fetch results
        $result['data'] = $viewer->fetch();
        // display errors from report, if any
        $errors = $registry['messages']->getErrors();
        if ($errors) {
            $result['errors'] = $errors;
        }

        // set back the old values in registry (if there were such)
        $registry->set('filters', $old_filters, true);
        $registry->set('generated_report', $old_generated_report, true);

        print 'var result = ' . json_encode($result) . ';';
        exit;
    }

   /**
     * Function to prepare the link to redirect to the other report
     */
    private function _prepareRedirectLink() {
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        // get report settings and define them as constants
        Reports::getReportSettings($this->registry, $report);

        // get the report filters from session (user of employee - agent, date - date, ray_id - ray)
        $report_filters = $this->registry['session']->get('reports_' . $report . '_report');

        $filters_for_other_report = array();
        // add the client and the client name
        $filters_for_other_report['type_results'] = 'client';
        $filters_for_other_report['client'] = $this->registry['request']->get('customer');
        // get client name
        $sql = 'SELECT CONCAT(`name`, " ", `lastname`) FROM ' . DB_TABLE_CUSTOMERS_I18N . ' WHERE `parent_id`="' . $filters_for_other_report['client'] . '" AND `lang`="' . $this->registry['lang'] . '"' . "\n";
        $filters_for_other_report['client_autocomplete'] = $this->registry['db']->GetOne($sql);

        $filters_for_other_report['date_from'] = General::strftime('%Y-%m-%d', strtotime('-' . COMMENTS_PERIOD));
        $filters_for_other_report['date_from_formatted'] = General::strftime('%d.%m.%Y', strtotime('-' . COMMENTS_PERIOD));
        $filters_for_other_report['date_to'] = date('Y-m-d');
        $filters_for_other_report['date_to_formatted'] = date('d.m.Y');

        // match the filters with the other report
        if (isset($report_filters['agent'])) {
            $filters_for_other_report['agent'] = $report_filters['agent'];
        }
        if (!empty($report_filters['customer_type'])) {
            $filters_for_other_report['customer_type'] = $report_filters['customer_type'];
        }
        if (!empty($report_filters['city'])) {
            $filters_for_other_report['city'] = array($report_filters['city']);
        }
        if (!empty($report_filters['loyalty_tags'])) {
            $filters_for_other_report['loyalty_tags'] = $report_filters['loyalty_tags'];
        }

        $link = sprintf(
            '%s://%s%sindex.php?%s=reports&reports=generate_report&report_type=queisser_activity_analysis&%s',
            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
            $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'],
            http_build_query($filters_for_other_report)
        );

        echo $link;
        return;
    }
}

?>
