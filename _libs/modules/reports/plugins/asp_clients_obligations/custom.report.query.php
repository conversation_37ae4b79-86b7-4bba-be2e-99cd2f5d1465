<?php
    Class Asp_Clients_Obligations Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            if (empty($filters['type'])) {
                $filters['type'] = array(CONTRACT_TYPE_SOT, CONTRACT_TYPE_FO);
            }

            if (!defined('COMPANY_ASP')) {
                define('COMPANY_ASP', 1);
            }

            // take id of additional var for id_object of SOT contracts
            $object_id_sot_var = 0;
            if (in_array(CONTRACT_TYPE_SOT, $filters['type'])) {
                $query = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                         'WHERE fm.model="Contract" AND fm.model_type="' . CONTRACT_TYPE_SOT . '"' . "\n" .
                         '  AND fm.name="' . OBJECT_ID_SOT . '"';
                $object_id_sot_var = $registry['db']->GetOne($query);
            }

            // exclude GT2 rows having system articles for discount in article_id field
            $skip_nomenclatures = array();
            if (defined('SKIP_NOMENCLATURES')) {
                $skip_nomenclatures = array_filter(preg_split('#\s*,\s*#', SKIP_NOMENCLATURES));
            }

            // get data for the final results
            $sql = array();
            $sql['select']  = 'SELECT fir.id AS idx, fir.id,' . "\n" .
                              '  con.id AS contract_id, con.num AS contract_num, con.type AS contract_type, con.cstm_financial,' . "\n" .
                              '  con.customer, TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) AS customer_name,' . "\n" .
                              '  TRIM(CONCAT(empi18n.name, " ", empi18n.lastname)) AS self_administrative_name,' . "\n" .
                              (in_array(CONTRACT_TYPE_SOT, $filters['type']) ?
                              '  con_cstm_object.value AS object, n_object.code AS object_code,' . "\n" : '') .
                              '  fir.type, fir.num, fir.issue_date, fir.date_of_payment,' . "\n" .
                              '  fir.payment_status, fir.total_with_vat, fir.currency,'. "\n" .
                              '  GROUP_CONCAT(DISTINCT CASE WHEN g.article_id NOT IN ("' . implode('","', $skip_nomenclatures) . '") ' . "\n" .
                              '    THEN IF(gi.free_text5 IS NOT NULL, gi.free_text5, "")' .
                              '    ELSE NULL END SEPARATOR "\n") AS periods' . "\n";

            $sql['from']    = 'FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                              'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                              ' ON c.id = con.customer' . (!empty($filters['self_administrative']) ? ' AND con.self_administrative = ' . $filters['self_administrative'] : ''). "\n" .
                              'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                              '  ON (ci18n.parent_id=con.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                              (in_array(CONTRACT_TYPE_SOT, $filters['type']) ?
                              'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_cstm_object' . "\n" .
                              '  ON (con_cstm_object.model_id=con.id AND con_cstm_object.var_id="' . $object_id_sot_var . '")' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n_object' . "\n" .
                              '  ON (n_object.id=con_cstm_object.value)' . "\n" : '') .
                              'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                              '  ON con.id=fiti.contract_id ' . "\n" .
                              'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                              '  ON fiti.invoice_id=fir.id AND fir.type=' . PH_FINANCE_TYPE_INVOICE . "\n" .
                              'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS g' . "\n" .
                              '  ON g.model="Finance_Incomes_Reason" AND g.model_id=fir.id' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gi' . "\n" .
                              '  ON g.id=gi.parent_id AND gi.lang="' . $model_lang . '"' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS empi18n' . "\n" .
                              '  ON empi18n.parent_id=con.self_administrative AND empi18n.lang="' . $model_lang . '"' . "\n";

            // construct where
            $where = array();
            // conditions for contracts
            //$where[] = 'con.annulled_by=0';
            //$where[] = 'con.deleted_by=0';
            //$where[] = 'con.active=1';
            $where[] = 'con.company="' . COMPANY_ASP . '"';
            if (!empty($filters['office'])) {
                $where[] = 'con.office="' . $filters['office'] . '"';
            }
            $where[] = 'con.subtype="contract"';
            $where[] = 'con.type IN ("' . implode('","', $filters['type']) . '")';
            if (!empty($filters['customer'])) {
                $where[] = 'con.customer="' . $filters['customer'] . '"';
            }
            if (!empty($filters['status'])) {
                if ($filters['status'] == 'cancelled') {
                    $where[] = 'con.status="closed" AND con.substatus IN (27,36)';
                } elseif ($filters['status'] == 'active') {
                    $where[] = 'con.status="closed" AND con.substatus NOT IN (27,36)';
                }
            }
            if (!empty($filters['object'])) {
                // we assume that invoices from SOT contracts have the object id in all rows - as it is set in the template;
                // we assume that all rows of an invoice from composite contract are for the same period (1 month)
                // so if we get one row, we are good
                $where[] = 'g.' . OBJECT_ID_FO . '="' . $filters['object'] . '"';
            }
            // conditions for invoices
            $where[] = 'fir.status="finished"';
            $where[] = 'fir.annulled_by=0';
            $where[] = 'fir.payment_status IN ("unpaid", "partial")';
            if (!empty($filters['overdue'])) {
                $where[] = 'fir.date_of_payment <= CURDATE() AND fir.date_of_payment > 0';
            }
            if (!empty($skip_nomenclatures)) {
                $where[] = 'g.article_id NOT IN ("' . implode('","', $skip_nomenclatures) . '")';
            }

            $sql['where'] = 'WHERE ' . implode("\n" . '  AND ', $where);

            $sql['group'] = 'GROUP BY fir.id';

            $query = implode("\n", $sql);

            $fin_documents = $registry['db']->GetAssoc($query);

            // now we will reuse the same query to search for credit and debit notes
            $sql['select']  = str_replace('fir.', 'fir2.', $sql['select']) . ', fir.id AS invoice_id';

            $sql['from'] .=   'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                              '  ON frr.link_to=fir.id AND frr.link_to_model_name="Finance_Incomes_Reason"' . "\n" .
                              'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir2' . "\n" .
                              '  ON frr.parent_id=fir2.id AND frr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                              '    AND fir2.type IN (' . PH_FINANCE_TYPE_CREDIT_NOTICE . ',' . PH_FINANCE_TYPE_DEBIT_NOTICE . ')' . "\n";

            $sql['where'] =   preg_replace('#(fir)\.(payment_status|date_of_payment)#', '${1}2.$2', $sql['where']) . "\n" .
                              '  AND fir2.annulled_by=0';

            $sql['group'] = str_replace('fir.', 'fir2.', $sql['group']);

            $query = implode("\n", $sql);

            $fin_documents = $fin_documents + $registry['db']->GetAssoc($query);

            // get names of contract types
            $params = array(
                0 => $registry,
                'table' => 'DB_TABLE_CONTRACTS_TYPES',
                'table_i18n' => 'DB_TABLE_CONTRACTS_TYPES_I18N',
                'where' => 't.active=1 AND t.deleted=0 AND t.id IN (' . implode(',', $filters['type']) . ')',
                'assoc' => 1
            );
            $contracts_types = Dropdown::getCustomDropdown($params);
            foreach ($contracts_types as $k => $v) {
                $contracts_types[$k] = $v['label'];
            }

            // get default currency of company (or main currency of installation by default)
            $query = 'SELECT default_currency FROM ' . DB_TABLE_FINANCE_COMPANIES . "\n" .
                     'WHERE id="' . COMPANY_ASP . '"';
            $main_currency = $registry['db']->GetOne($query);
            if (!$main_currency) {
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $main_currency = Finance_Currencies::getMain($registry);
            }

            // store conversion rates (to be reused)
            $conversion_rates = array();

            // keep relations between contract and cstm_financial contact and between contract and customer
            $contracts_contact = $contracts_cust = array();

            // keys containing data for invoice/CN/DN
            $inv_keys = array(
                'id', 'type', 'num', 'issue_date', 'date_of_payment', 'invoice_id', 'periods',
                'payment_status', 'total_with_vat', 'currency',
            );
            $inv_keys = array_combine($inv_keys, array_fill(0, count($inv_keys), ''));

            // collect ids of partially paid invoices/DN/CN to get paid amount for
            $fin_ids_pos = array_keys(array_filter($fin_documents,
                function ($a) { return $a['payment_status'] == 'partial' && $a['type'] != PH_FINANCE_TYPE_CREDIT_NOTICE; }));
            $fin_ids_neg = array_keys(array_filter($fin_documents,
                function ($a) { return $a['payment_status'] == 'partial' && $a['type'] == PH_FINANCE_TYPE_CREDIT_NOTICE; }));

            if ($fin_ids_pos) {
                $query = 'SELECT fb.paid_to, SUM(fb.paid_amount)' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fb' . "\n" .
                         'WHERE fb.paid_to IN (' . implode(',', $fin_ids_pos) . ')' . "\n" .
                         '  AND fb.paid_to_model_name="Finance_Incomes_Reason"' . "\n" .
                         'GROUP BY fb.paid_to' . "\n";
                $fin_ids_pos = $registry['db']->GetAssoc($query);
                foreach ($fin_ids_pos as $k => $v) {
                    $fin_documents[$k]['total_with_vat'] -= $v; // rounding will be done later
                }
            }
            if ($fin_ids_neg) {
                $query = 'SELECT fb.parent_id, SUM(fb.paid_amount)' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fb' . "\n" .
                         'WHERE fb.parent_id IN (' . implode(',', $fin_ids_neg) . ')' . "\n" .
                         '  AND fb.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                         'GROUP BY fb.parent_id' . "\n";
                $fin_ids_neg = $registry['db']->GetAssoc($query);
                foreach ($fin_ids_neg as $k => $v) {
                    $fin_documents[$k]['total_with_vat'] += $v; // rounding will be done later
                }
            }

            foreach ($fin_documents as $fin_id => $fd) {
                if (!isset($final_results[$fd['customer']])) {
                    $final_results[$fd['customer']] = array(
                        'id'                => $fd['customer'],
                        'name'              => $fd['customer_name'],
                        'rowspan'           => 0,
                        'contracts'         => array(),
                        'unallocated_amount'=> 0,
                        'balance'           => 0,
                    );
                }

                // add the unpaid amount of each financial document to the balance
                if ($fd['currency'] != $main_currency) {
                    // convert amounts from other currency to main currency using today's (or latest available) rate
                    if (empty($conversion_rates[$fd['currency']])) {
                        $conversion_rates[$fd['currency']] = Finance_Currencies::getRate($registry, $fd['currency'], $main_currency);
                    }
                    $fd['total_with_vat'] = $fd['total_with_vat']*$conversion_rates[$fd['currency']];
                }
                // precision for balance - round to 2nd digit
                $fd['total_with_vat'] = round($fd['total_with_vat'], 2);
                $final_results[$fd['customer']]['balance'] += $fd['total_with_vat'];

                if (!isset($final_results[$fd['customer']]['contracts'][$fd['contract_id']])) {
                    $final_results[$fd['customer']]['contracts'][$fd['contract_id']] = array(
                        'id'                        => $fd['contract_id'],
                        'num'                       => $fd['contract_num'],
                        'self_administrative_name'  => $fd['self_administrative_name'],
                        'type_name'                 => $contracts_types[$fd['contract_type']],
                        'object'                    => isset($fd['object']) ? $fd['object'] : '',
                        'object_code'               => isset($fd['object_code']) ? $fd['object_code'] : '',
                        'rowspan'                   => 0,
                        'invoices'                  => array(),
                        'contact_name'              => '',
                        'phones'                    => array(),
                        'comments'                  => 0,
                    );
                    $contracts_cust[$fd['contract_id']] = $fd['customer'];
                    if ($fd['cstm_financial']) {
                        $contracts_contact[$fd['contract_id']] = $fd['cstm_financial'];
                    }
                }
                if (!empty($fd['invoice_id']) &&
                isset($final_results[$fd['customer']]['contracts'][$fd['contract_id']]['invoices'][$fd['invoice_id']])) {
                    // move credit/debit note after invoice
                    General::injectInArray(
                        array($fin_id => array_intersect_key($fd, $inv_keys)),
                        $final_results[$fd['customer']]['contracts'][$fd['contract_id']]['invoices'],
                        'after', $fd['invoice_id']);
                } else {
                    $final_results[$fd['customer']]['contracts'][$fd['contract_id']]['invoices'][$fin_id] =
                        array_intersect_key($fd, $inv_keys);
                }
                $final_results[$fd['customer']]['contracts'][$fd['contract_id']]['rowspan']++;
                $final_results[$fd['customer']]['rowspan']++;
            }

            foreach ($final_results as $k => $v) {
                uasort($final_results[$k]['contracts'], function ($a, $b) { return $a['num'] > $b['num']; });
            }

            if ($contracts_cust) {
                // prepare the total of unallocated payments per customer
                $cust_ids = array_unique(array_values($contracts_cust));
                $query = 'SELECT fp.id,' . "\n" .
                         '  fp.receive_flag * (fp.amount - SUM(IF(fb.paid_amount IS NOT NULL, fb.paid_amount, 0))) AS amount,' . "\n" .
                         '  fp.currency, fp.customer' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fb' . "\n" .
                         '  ON fp.id=fb.parent_id AND fb.parent_model_name="Finance_Payment"' . "\n" .
                         'WHERE fp.status="finished" AND fp.annulled_by=0 AND fp.type IN ("BP", "PKO")' . "\n" .
                         '  AND fp.customer IN (' . implode(',', $cust_ids) . ')' . "\n" .
                         '  AND fp.company="' . COMPANY_ASP . '"' . "\n" .
                         'GROUP BY fp.id' . "\n" .
                         'HAVING amount!=0' . "\n" .
                         'UNION' . "\n" .
                         'SELECT fp.id,' . "\n" .
                         '  fp.receive_flag * (fp.amount - SUM(IF(fb.paid_amount IS NOT NULL, fb.paid_amount, 0))) AS amount,' . "\n" .
                         '  fp.currency, fp.customer' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fb' . "\n" .
                         '  ON fp.id=fb.paid_to AND fb.paid_to_model_name="Finance_Payment"' . "\n" .
                         'WHERE fp.status="finished" AND fp.annulled_by=0 AND fp.type IN ("PN", "RKO")' . "\n" .
                         '  AND fp.customer IN (' . implode(',', $cust_ids) . ')' . "\n" .
                         '  AND fp.company="' . COMPANY_ASP . '"' . "\n" .
                         'GROUP BY fp.id' . "\n" .
                         'HAVING amount!=0' . "\n";
                $unallocated_payments = $registry['db']->GetAll($query);

                foreach($unallocated_payments as $up) {
                    if ($up['currency'] != $main_currency) {
                        // convert amounts from other currency to main currency using today's (or latest available) rate
                        if (empty($conversion_rates[$up['currency']])) {
                            $conversion_rates[$up['currency']] = Finance_Currencies::getRate($registry, $up['currency'], $main_currency);
                        }
                        $up['amount'] = round($up['amount']*$conversion_rates[$up['currency']], 2);
                    }
                    $final_results[$up['customer']]['unallocated_amount'] += $up['amount'];
                }

                // subtract unallocated amount from balance of customer
                foreach($cust_ids as $cid) {
                    $final_results[$cid]['balance'] -= $final_results[$cid]['unallocated_amount'];
                }

                // prepare comments per contract
                $query = 'SELECT model_id, COUNT(id)' . "\n" .
                         'FROM ' . DB_TABLE_COMMENTS . "\n" .
                         'WHERE model = "Contract" AND model_id IN (' . implode(',', array_keys($contracts_cust)) . ')' . "\n" .
                         ($registry['currentUser']->get('is_portal') ? ' AND is_portal = "1"' . "\n" : '') .
                         'GROUP BY model, model_id';
                $comments = $registry['db']->getAssoc($query);
                foreach ($comments as $k => $v) {
                    $final_results[$contracts_cust[$k]]['contracts'][$k]['comments'] = $v;
                }

                // prepare phones and names for contacts
                $contacts_found = $contacts_names = array();
                $contacts = array_unique(array_values($contracts_contact));
                if ($contacts) {
                    // prepare names of financial contacts
                    $query = 'SELECT parent_id, TRIM(CONCAT(IF(name IS NOT NULL, name, ""), " ", IF(lastname IS NOT NULL, lastname, "")))' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS_I18N . "\n" .
                             'WHERE parent_id IN (' . implode(',', $contacts) . ') AND lang="' . $model_lang . '"';
                    $contacts_names = $registry['db']->getAssoc($query);

                    $query = 'SELECT id, subtype, parent_customer, phone, gsm' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . "\n" .
                             'WHERE id IN (' . implode(',', $contacts) . ')';
                    $contacts = $registry['db']->getAssoc($query);
                }

                $parent_customers = array();
                foreach ($contacts as $cid => $cdata) {
                    // next iteration will be checked
                    if (!($cdata['phone'] || $cdata['gsm']) && $cdata['subtype'] == 'contact') {
                        $parent_customers[$cid] = $cdata['parent_customer'];
                        continue;
                    }
                    foreach (array('gsm', 'phone') as $c) {
                        if ($cdata[$c]) {
                            $cdata[$c] = explode("\n", $cdata[$c]);
                            $contacts_found[$cid] = array();
                            foreach($cdata[$c] as $contact_data) {
                                $contacts_found[$cid][] = str_replace('|', ' ', $contact_data);
                            }
                        }
                    }
                    unset($contacts[$cid]);
                }

                // search in parent branches and normal customers
                while ($parent_customers) {
                    $query = 'SELECT id, subtype, parent_customer, phone, gsm, is_main' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . "\n" .
                             'WHERE id IN (' . implode(',', $parent_customers) . ')';
                    $parent_contacts = $registry['db']->getAssoc($query);

                    $parent_customers = array();
                    foreach ($parent_contacts as $pcid => $pcdata) {
                        // next iteration will be checked
                        if (!($pcdata['phone'] || $pcdata['gsm']) && $pcdata['subtype'] == 'branch' && !$pcdata['is_main']) {
                            $parent_customers[$pcid] = $contacts[$cid]['parent_customer'] = $pcdata['parent_customer'];
                            continue;
                        }
                        $parent_contacts_found = array();
                        foreach (array('gsm', 'phone') as $c) {
                            if ($pcdata[$c]) {
                                $cdata[$c] = explode("\n", $pcdata[$c]);
                                foreach($cdata[$c] as $contact_data) {
                                    $parent_contacts_found[] = str_replace('|', ' ', $contact_data);
                                }
                            }
                        }
                        // now set value for all contact persons of branch/customer
                        foreach ($contacts as $cid => $cdata) {
                            if ($cdata['parent_customer'] == $pcid) {
                                if ($parent_contacts_found) {
                                    $contacts_found[$cid] = $parent_contacts_found;
                                }
                                unset($contacts[$cid]);
                            }
                        }
                        unset($parent_contacts_found);
                    }
                    unset($parent_contacts);
                };
                unset($parent_customers);

                // finally set contact data to final results
                foreach ($contracts_contact as $contract => $contact) {
                    if (array_key_exists($contact, $contacts_found)) {
                        $final_results[$contracts_cust[$contract]]['contracts'][$contract]['phones'] =
                            $contacts_found[$contact];
                    }
                    if (array_key_exists($contact, $contacts_names)) {
                        $final_results[$contracts_cust[$contract]]['contracts'][$contract]['contact_name'] =
                            $contacts_names[$contact];
                    }
                }
            }

            uasort($final_results, function($a, $b) { return $a['name'] > $b['name']; });

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }

?>
