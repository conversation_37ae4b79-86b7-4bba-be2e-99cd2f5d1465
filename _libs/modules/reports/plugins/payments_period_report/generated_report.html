<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
<tr class="reports_title_row hcenter">
  <td class="t_border"><div style="width: 220px;">{#reports_history#|escape}</div></td>
  <td class="t_border"><div style="width: 90px;">{#reports_during#|escape} {$reports_results.past_period_start|date_format:#date_short#} - {$report_filters.from_date.value|date_format:#date_short#}</div></td>
  <td class="t_border"><div style="width: 90px;">{#reports_period_invoiced#|escape}</div></td>
  <td class="t_border"><div style="width: 90px;">{#reports_period_incomes#|escape}</div></td>
  <td class="t_border"><div style="width: 90px;">{#reports_rest#|escape}</div></td>
  <td class="t_border"><div style="width: 90px;">{#reports_collected#|escape}</div></td>
  <td><div style="width: 90px;">{#reports_not_collected#|escape}</div></td>
</tr>
{foreach from=$reports_results.result key=k name=i item=result}
  <tr class="{cycle values='t_odd,t_even'}  hright">
    <td class="t_border hleft" style="text-align:left!important" width="25">
      {if $k eq 'before'}
        {#reports_before#} {$report_filters.from_date.value|date_format:#date_short#}
      {elseif $k eq 'during'}
        {#reports_during#} {$report_filters.from_date.value|date_format:#date_short#} - {$report_filters.to_date.value|date_format:#date_short#}
      {elseif $k eq 'total'}
        <strong>{#reports_total#}</strong>
      {/if}
    </td>
    <td class="t_border">
    {if $k eq 'total'}<strong>{$result.claims|default:0|string_format:"%.2F"}</strong>
    {else}{$result.claims|default:0|string_format:"%.2F"}{/if}
    </td>
    <td class="t_border">
      {if $k eq 'total'}<strong>{$result.invoices|default:0|string_format:"%.2F"}</strong>
      {else}{$result.invoices|default:0|string_format:"%.2F"}{/if}
    </td>
    <td class="t_border">
      {if $k eq 'total'}<strong>{$result.paid|default:0|string_format:"%.2F"}</strong>
      {else}{$result.paid|default:0|string_format:"%.2F"}{/if}
    </td>
    <td class="t_border">
      {if $k eq 'total'}<strong>{$result.rest|default:0|string_format:"%.2F"}</strong>
      {else}{$result.rest|default:0|string_format:"%.2F"}{/if}
    </td>
    <td class="t_border">
    {if $smarty.foreach.i.last}
    <strong>-</strong>
    {else}
      {$result.collected|default:'-'|string_format:"%.2F"}
    {/if}
    </td>
    <td>
    {if $smarty.foreach.i.last}
    <strong>-</strong>
    {else}
      {$result.not_collected|default:'-'|string_format:"%.2F"}
    {/if}
    </td>
  </tr>
{foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error" colspan="7">{#no_items_found#|escape}</td>
  </tr>
{/foreach}
<tr>
  <td class="t_footer" colspan="7"></td>
</tr>
</table>
<br />
{#reports_changes#}: {if $reports_results.result.total.claims}{math equation=(x-y)/y*100 format='%.2f' y=$reports_results.result.total.claims x=$reports_results.result.total.rest}{else}0{/if}%
<br /><br />
{if !empty($reports_results.articles)}
<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
<tr class="reports_title_row hcenter">
  <td class="t_border">{#reports_income_type#|escape}</td>
  <td class="t_border">{#reports_to_date#|escape} {$report_filters.from_date.value|date_format:#date_short#}</td>
  <td class="t_border">{#reports_period_invoiced#|escape}</td>
  <td class="t_border">{#reports_old_paid#|escape}</td>
  <td class="t_border">{#reports_new_paid#|escape}</td>
  <td>{#reports_rest#|escape}</td>
</tr>
{foreach from=$reports_results.articles key=k name=i item=result}
  <tr class="{cycle values='t_odd,t_even'}  hright">
    <td class="t_border hleft" style="text-align:left!important" nowrap="nowrap" width="25">
      {$result.name|escape}
    </td>
    <td class="t_border">
      {$result.total_before|string_format:"%.2F"}
    </td>
    <td class="t_border">
      {$result.total_during|string_format:"%.2F"}
    </td>
    <td class="t_border">
      {$result.paid_old|string_format:"%.2F"}
    </td>
    <td class="t_border">
      {$result.paid_new|string_format:"%.2F"}
    </td>
    <td>
      {$result.rest|string_format:"%.2F"}
    </td>
  </tr>
{/foreach}
<tr>
  <td class="t_footer" colspan="6"></td>
</tr>
</table>
<br /><br />
{#report_note#}
<br /><br />
{/if}
