<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('CATEGORIES_NAME', 'purpose_unit');

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date_help')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date_help')
            );
            $filters['to_date'] = $filter;

            //DEFINE AREA FILTER
            $filter = array (
                'custom_id' => 'area',
                'name'      => 'area',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_area'),
                'help'      => $this->i18n('reports_area'),
                'options'   => array(
                    array (
                        'label'         => $this->i18n('reports_less_than_50'),
                        'option_value'  => 50
                    ),
                    array (
                        'label'         => $this->i18n('reports_less_than_100'),
                        'option_value'  => 100
                    ),
                    array (
                        'label'         => $this->i18n('reports_less_than_250'),
                        'option_value'  => 250
                    ),
                    array (
                        'label'         => $this->i18n('reports_less_than_500'),
                        'option_value'  => 500
                    ),
                    array (
                        'label'         => $this->i18n('reports_less_than_1000'),
                        'option_value'  => 1000
                    ),
                    array (
                        'label'         => $this->i18n('reports_more_than_1000'),
                        'option_value'  => 'more'
                    )
                )
            );
            $filters['area'] = $filter;

            //DEFINE PURPOSE FILTER
            //prepare options
            $sql_categories = 'SELECT label, option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE parent_name="' . CATEGORIES_NAME . '" AND lang="' . $registry['lang'] . '" ORDER BY position ASC';
            $purpose_options = $registry['db']->getAll($sql_categories);

            //prepare filters
            $filter = array (
                'custom_id' => 'purpose',
                'name'      => 'purpose',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_purpose'),
                'help'      => $this->i18n('reports_purpose'),
                'options'   => $purpose_options
            );
            $filters['purpose'] = $filter;

            return $filters;
        }
    }
?>