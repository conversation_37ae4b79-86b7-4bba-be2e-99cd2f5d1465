<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry;

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = $registry;
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'required'  => 1,
                'disallow_date_before' => true,
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'required'  => 1,
                'disallow_date_before' => true,
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            // DEFINE TRADEMARK AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'renters',
                'name'              => 'renters',
                'type'              => 'custom_filter',
                'actual_type'       => 'autocompleter',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'custom_buttons'    => 'search',
                'width'             => 222,
                'label'             => $this->i18n('reports_renters'),
                'help'              => $this->i18n('reports_renters'),
                'autocomplete'      => array('search' => array('<name>', '<customer_name>'),
                                  'sort'         => array('<name>', '<customer_name>'),
                                  'type'         => 'nomenclatures',
                                  'clear'        => 1,
                                  'suggestions'  => '<name> [<customer_name>]',
                                  'buttons_hide' => 'search',
                                  'id_var'       => 'renters',
                                  'fill_options' => array('$renters => <trademark>',
                                                          '$renters_autocomplete => <name> [<customer_name>]',
                                                         ),
                                  'filters'      => array('<type_keyword>' => 'trademark',
                                                          '<customer_trademark>' => '1'),
                                  'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
                )
            );
            $filters['renters'] = $filter;

            //DEFINE SHOW FROM
            $options_show = array(
                array(
                    'label'         => $this->i18n('reports_option_rent'),
                    'option_value'  => 'rent'
                ),
                array(
                    'label'         => $this->i18n('reports_option_tax_service'),
                    'option_value'  => 'tax_service'
                )
            );

            $filter = array (
                'custom_id' => 'show',
                'name'      => 'show',
                'type'      => 'checkbox_group',
                'options'   => $options_show,
                'label'     => $this->i18n('reports_show'),
                'help'      => $this->i18n('reports_show_help')
            );
            $filters['show'] = $filter;

            //DEFINE CURRENCY FILTER
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $available_currencies = Finance_Currencies::getAvailableCurrencies($registry, array('active' => 1));

            $options_currency = array();
            foreach($available_currencies as $curr) {
                $options_currency[] = array(
                    'label'         => $curr['currency_code'],
                    'option_value'  => $curr['currency_code']
                );
            }

            $filter = array (
                'custom_id' => 'currency',
                'name'      => 'currency',
                'type'      => 'dropdown',
                'required'  => 1,
                'options'   => $options_currency,
                'label'     => $this->i18n('reports_currency'),
                'help'      => $this->i18n('reports_currency_help')
            );
            $filters['currency'] = $filter;

            //DEFINE PESSIMISTIC FORECAST FILTER
            $filter = array (
                'custom_id'     => 'pesimistic_forecast',
                'name'          => 'pesimistic_forecast',
                'type'          => 'text',
                'restrict'      => 'insertOnlyDigits',
                'label'         => $this->i18n('reports_pesimistic_forecast'),
                'help'          => $this->i18n('reports_pesimistic_forecast')
            );
            $filters['pesimistic_forecast'] = $filter;

            //DEFINE OPTIMISTIC FORECAST FILTER
            $filter = array (
                'custom_id'     => 'optimistic_forecast',
                'name'          => 'optimistic_forecast',
                'type'          => 'text',
                'restrict'      => 'insertOnlyDigits',
                'label'         => $this->i18n('reports_optimistic_forecast'),
                'help'          => $this->i18n('reports_optimistic_forecast')
            );
            $filters['optimistic_forecast'] = $filter;

            //DEFINE RECEIVE SUM DATE
            $filter = array (
                'custom_id' => 'show_sums_acording_to',
                'name'      => 'show_sums_acording_to',
                'type'      => 'radio',
                'label'     => $this->i18n('reports_show_sums_according_to'),
                'help'      => $this->i18n('reports_show_sums_according_to'),
                'options'   => array(array('label' => $this->i18n('according_to_issue_date'),
                                           'option_value' => 'issue'),
                                     array('label' => $this->i18n('according_to_payment_date'),
                                           'option_value' => 'payment'))
            );
            $filters['show_sums_acording_to'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            if (empty($filters['currency']['value'])) {
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

                $main_currency = Finance_Currencies::getMain(self::$registry);
                $filters['currency']['value'] = sprintf('%s', $main_currency);
            }

            if (empty($filters['show_sums_acording_to']['value'])) {
                $filters['show_sums_acording_to']['value'] = 'issue';
            }

            return $filters;
        }

    }
?>