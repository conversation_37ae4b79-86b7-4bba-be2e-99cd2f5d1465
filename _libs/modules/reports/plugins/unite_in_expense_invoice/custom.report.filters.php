<?php
    class Custom_Report_Filters extends Report_Filters {
        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // Prepare an array containing description of all filters
            $filters = array();

            // DEFINE FILTER: Company
            $options_companies = array();
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $filters_companies = array('model_lang' => $registry['lang'],
                                       'sanitize'   => true,
                                       'sort'       => array('fc.position', 'fci18n.name'),
                                       'where'      => array('fc.deleted=0',
                                                             'fc.active=1'));
            $companies = Finance_Companies::search($registry, $filters_companies);

            foreach($companies as $company) {
                $company_id = $company->get('id');
                $options_companies[$company_id] = array(
                    'label'        => $company->get('name'),
                    'option_value' => $company_id);
            }
            $filter = array (
                'custom_id'          => 'company',
                'name'               => 'company',
                'type'               => 'dropdown',
                'label'              => $this->i18n('reports_company'),
                'help'               => $this->i18n('reports_company'),
                'options'            => $options_companies,
                'value'              => ''
            );
            $filters['company'] = $filter;

            // DEFINE FILTER: Customer
            $filter = array (
                'custom_id'    => 'customer',
                'name'         => 'customer',
                'type'         => 'autocompleter',
                'required'     => true,
                'label'        => $this->i18n('reports_filter_customer'),
                'help'         => $this->i18n('reports_filter_customer'),
                'autocomplete' => array(
                    'type'         => 'customers',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions'  => '[<code>] <name> <lastname>',
                    'search'       => array('<name>'),
                    'clear'        => 1,
                    'buttons_hide' => 'search'));

            if (defined('CUSTOMERS_TYPES_INCLUDED') && trim(CUSTOMERS_TYPES_INCLUDED)) {
                $filter['autocomplete']['filters'] = array('<type>' => CUSTOMERS_TYPES_INCLUDED);
            }
            $filters['customer'] = $filter;

            //DEFINE FIN DOCUMENTS TYPES FILTER
            $expenses_reasons_types_included = array_filter(preg_split('/\s*\,\s*/', EXPENSES_REASONS_TYPES_INCLUDED));
            $sql_expenses_types = 'SELECT fdti18n.parent_id as option_value, fdti18n.name as label' . "\n" .
                                  'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                                  'INNER JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                                  ' ON (fdt.id=fdti18n.parent_id AND fdti18n.lang="' . $registry['lang'] . '")' . "\n" .
                                  'WHERE fdt.active=1 AND fdt.deleted_by=0 AND fdt.model="Finance_Expenses_Reason" AND fdt.add_invoice=1 AND fdt.id' . (!empty($expenses_reasons_types_included) ? ' IN ("' . implode('","', $expenses_reasons_types_included) . '")' : '>100') . "\n" .
                                  'ORDER BY fdti18n.name DESC';

            //prepare filter
            $filter = array (
                'custom_id' => 'document_type',
                'name'      => 'document_type',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_document_type'),
                'help'      => $this->i18n('reports_document_type_help'),
                'options'   => $registry['db']->GetAll($sql_expenses_types),
            );
            $filters['document_type'] = $filter;

            //DEFINE CURRENCY FILTER
            // take the main currency
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

            $currency_list = Finance_Currencies::getAvailableCurrencies($registry, array('active' => 1));
            $options_currencies = array();
            foreach ($currency_list as $curr) {
                $options_currencies[] = array(
                    'option_value' => $curr['currency_code'],
                    'label'        => $curr['currency_code']
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'currency',
                'name'      => 'currency',
                'type'      => 'dropdown',
                'required'  => true,
                'label'     => $this->i18n('reports_currency'),
                'help'      => $this->i18n('reports_currency_help'),
                'options'   => $options_currencies
            );
            $filters['currency'] = $filter;

            //DEFINE PERIOD FILTER
            self::loadDefaultFilter($registry, $filters, 'period_from_to', array());

            return $filters;
        }

    /**
     * Process some filters that depend on the request or on each other
     *
     * @param array $filters - the report filters
     * @return array - report filters after processing
     */
    function processDependentFilters(array &$filters) {
        if (count($filters['company']['options']) == 1) {
            $first_option_value = reset($filters['company']['options']);
            $filters['company']['value'] = $first_option_value['option_value'];
        }

        $unset_filters = array();
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }
        }

        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        return $filters;
    }
    }
?>
