<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_date',
                'name'              => 'from_date',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/date_from_to_filter.html',
                'type'              => 'custom_filter',
                'width'             => 65,
                'additional_filter' => 'to_date',
                'first_filter_label'=> $this->i18n('reports_from_date'),
                'label'             => $this->i18n('reports_report_date'),
                'help'              => $this->i18n('reports_report_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_date',
                'name'            => 'to_date',
                'type'            => 'date',
                'width'           => 65,
                'label'           => $this->i18n('reports_to_date'),
                'help'            => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            // DEFINE CUSTOMERS AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'customers',
                'name'              => 'customers',
                'type'              => 'custom_filter',
                'actual_type'       => 'autocompleter',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'width'             => 222,
                'label'             => $this->i18n('reports_customers'),
                'help'              => $this->i18n('reports_customers'),
                'autocomplete'      => array('search'       => array('<name>'),
                                             'sort'         => array('<name>'),
                                             'type'         => 'customers',
                                             'clear'        => 1,
                                             'suggestions'  => '[<code>] <name> <lastname>',
                                             'buttons_hide' => 'search',
                                             'id_var'       => 'customers',
                                             'fill_options' => array('$customers => <id>',
                                                                     '$customers_autocomplete => [<code>] <name> <lastname>',
                                                               ),
                                             'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['customers'] = $filter;

            //DEFINE PROJECTS' FILTER
            $filter = array (
                'custom_id'             => 'project',
                'name'                  => 'project',
                'type'                  => 'autocompleter',
                'autocomplete_type'     => 'projects',
                'autocomplete_buttons'  => 'clear',
                'label'                 => $this->i18n('reports_project'),
                'help'                  => $this->i18n('reports_project'),
                'value'                 => ''
            );
            $filters['project'] = $filter;

            // prepare options for status filter
            $sql_options_sustatuses = 'SELECT id as option_value, name as label FROM ' . DB_TABLE_DOCUMENTS_STATUSES . ' WHERE id IN (' . INCLUDED_SUBSTATUSES . ')';
            $options_sustatuses = $registry['db']->getAll($sql_options_sustatuses);

            //DEFINE STATUS FILTER
            $filter = array (
                'custom_id'             => 'status',
                'name'                  => 'status',
                'type'                  => 'dropdown',
                'label'                 => $this->i18n('reports_status'),
                'help'                  => $this->i18n('reports_status'),
                'options'               => $options_sustatuses,
                'value'                 => ''
            );
            $filters['status'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }
    }
?>