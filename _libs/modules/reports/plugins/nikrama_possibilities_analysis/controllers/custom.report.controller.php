<?php
require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'ajax_generate':
                $this->_ajaxGenerate();
                break;
            default:
                parent::execute();
        }
    }

    private function _ajaxGenerate() {
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];
        $this->report = $report;

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->report_lang_file = $i18n_file;
        $this->registry['translater']->loadFile($this->report_lang_file);
        Reports::getReportSettings($this->registry, $report);

        // take the filters
        $this->setAction('generate_report');
        $this->registry['request']->set('reports', 'generate_report', 'all', true);
        $this->registry->set('generated_report', 1, true);
        $this->_index();
        $filters = Reports::saveSearchParams($this->registry, $this->registry->get('filters_values'), 'reports_' . $report . '_');

        $redirect_parameters = array();
        foreach ($this->registry['request']->getAll() as $post_var => $post_value) {
            if (is_array($post_value)) {
                foreach ($post_value as $key => $val) {
                    $redirect_parameters[] = array(
                        'param' => sprintf('%s[%s]', $post_var, $key),
                        'value' => $val
                    );
                }
            } else {
                $redirect_parameters[] = array(
                    'param' => $post_var,
                    'value' => $post_value
                );
            }
        }
        $redirect_url = sprintf('%s://%s%sindex.php?d=%s',
                        (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                        $_SERVER["HTTP_HOST"], PH_BASE_URL,
                        General::encodeUrlData($redirect_parameters));

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $this->registry['lang'];
        }

        $probability_vars = array(DOCUMENT_VAR_PERIOD_FINISH, DOCUMENT_VAR_DATE_FINISH, DOCUMENT_VAR_PROBABILITY, DOCUMENT_VAR_KIND, DOCUMENT_VAR_PARTNER, DOCUMENT_VAR_NEXT_DATE_ACTION);
        $tags_included = array_filter(preg_split('/\s*\,\s*/', POSSIBILITIES_TAGS_INCLUDED));

        //sql to take the ids of the needed additional vars
        $sql_for_add_vars = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `name` IN ("' . implode('","', $probability_vars) . '") AND `model_type`="' . DOCUMENT_TYPE_POSSIBILITY . '"';
        $probability_vars = $this->registry['db']->GetAssoc($sql_for_add_vars);

        // sql to take the add vars for the customer
        $sql_for_add_vars_cust = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Customer" AND `name`="' . CUSTOMER_VAR_SECTION . '"';
        $section_vars = $this->registry['db']->GetCol($sql_for_add_vars_cust);

        $sql = array(
            'select' => '',
            'from'   => '',
            'where'  => '',
            'sort'   => '',
        );
        $sql['select'] = 'SELECT d.id, d.full_num, "" as tag, doc_prob_name.label as probability, d.date, doc_date.value as date_finish, ' . "\n" .
                         '       doc_q_name.label as q, doc_next_action_date.value as date_next_action, CONCAT(ci18n.name, " ", ci18n.lastname) as client, d.status, ds.name as substatus, ' . "\n" .
                         '       0 as comments, 0 as tasks, 0 as files, ni18n_product.name as product, gt2.price_with_discount as price, di18n.description, di18n.notes, ' . "\n" .
                         '       CONCAT(c_del_i18n.name, " ", c_del_i18n.lastname) as deliverer, gt2.price as delivery_price, gt2.surplus_value as difference,' . "\n" .
                         '       CONCAT(cp_i18n.name, " ", cp_i18n.lastname) as contact, cp_i18n.position as position, cp.gsm, cp.email, doc_kind.label as kind, ' . "\n" .
                         '       doc_kind.option_value as kind_opt, ci18n.city as client_city, ci18n.address as client_address, dm.name as media, cust_sect_name.name as sector, ' . "\n" .
                         '       CONCAT(ui18n.firstname, " ", ui18n.lastname) as assigned, CONCAT(part_i18n.name, " ", part_i18n.lastname) as business_partner, cti18n.name as client_type' . "\n";
        $sql['from'] =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                        'JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                        ' ON (di18n.parent_id=d.id AND di18n.lang="' . $model_lang . '")' . "\n" .
                        'JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                        ' ON (c.id=d.customer)' . "\n" .
                        'JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                        ' ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang . '")' . "\n" .
                        'JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n" .
                        ' ON (cti18n.parent_id=c.type AND cti18n.lang="' . $model_lang . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                        ' ON (ds.id=d.substatus AND ds.lang="' . $model_lang . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_prob' . "\n" .
                        '  ON (doc_prob.model_id=d.id AND doc_prob.var_id="' . (!empty($probability_vars[DOCUMENT_VAR_PROBABILITY]) ? $probability_vars[DOCUMENT_VAR_PROBABILITY] : '') . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS doc_prob_name' . "\n" .
                        '  ON (doc_prob.value=doc_prob_name.option_value AND doc_prob_name.parent_name="' . DOCUMENT_VAR_PROBABILITY . '" AND doc_prob_name.lang="' . $this->registry['lang'] . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_q' . "\n" .
                        '  ON (doc_q.model_id=d.id AND doc_q.var_id="' . (!empty($probability_vars[DOCUMENT_VAR_PERIOD_FINISH]) ? $probability_vars[DOCUMENT_VAR_PERIOD_FINISH] : '') . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS doc_q_name' . "\n" .
                        '  ON (doc_q.value=doc_q_name.option_value AND doc_q_name.parent_name="' . DOCUMENT_VAR_PERIOD_FINISH . '" AND doc_q_name.lang="' . $this->registry['lang'] . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_date' . "\n" .
                        '  ON (doc_date.model_id=d.id AND doc_date.var_id="' . (!empty($probability_vars[DOCUMENT_VAR_DATE_FINISH]) ? $probability_vars[DOCUMENT_VAR_DATE_FINISH] : '') . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_next_action_date' . "\n" .
                        '  ON (doc_next_action_date.model_id=d.id AND doc_next_action_date.var_id="' . (!empty($probability_vars[DOCUMENT_VAR_NEXT_DATE_ACTION]) ? $probability_vars[DOCUMENT_VAR_NEXT_DATE_ACTION] : '') . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                        '  ON (gt2.model="Document" AND gt2.model_id=d.id)' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS g2i18n' . "\n" .
                        '  ON (g2i18n.parent_id=gt2.id AND g2i18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_product' . "\n" .
                        ' ON (ni18n_product.parent_id=gt2.article_id AND ni18n_product.lang="' . $model_lang . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS c_del_i18n' . "\n" .
                        ' ON (c_del_i18n.parent_id=gt2.article_deliverer AND c_del_i18n.lang="' . $model_lang . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS doc_kind' . "\n" .
                        '  ON (g2i18n.free_text1=doc_kind.option_value AND doc_kind.parent_name="' . DOCUMENT_VAR_KIND . '" AND doc_kind.lang="' . $this->registry['lang'] . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS cust_sect' . "\n" .
                        '  ON (cust_sect.model_id=d.customer AND cust_sect.var_id IN ("' . implode('","', $section_vars) . '"))' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS cust_sect_name' . "\n" .
                        ' ON (cust_sect_name.parent_id=cust_sect.value AND cust_sect_name.lang="' . $model_lang . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_MEDIAS . ' AS dm' . "\n" .
                        '  ON (dm.id=d.media AND dm.lang="' . $this->registry['lang'] . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS cp' . "\n" .
                        ' ON (cp.id=d.contact_person)' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS cp_i18n' . "\n" .
                        ' ON (cp_i18n.parent_id=cp.id AND cp_i18n.lang="' . $model_lang . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                        '  ON (c.assigned=ui18n.parent_id AND ui18n.lang="' . $this->registry['lang'] . '")'. "\n" .
                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS doc_partner' . "\n" .
                        '  ON (doc_partner.model_id=d.id AND doc_partner.var_id="' . (!empty($probability_vars[DOCUMENT_VAR_PARTNER]) ? $probability_vars[DOCUMENT_VAR_PARTNER] : '') . '")' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS part_i18n' . "\n" .
                        ' ON (part_i18n.parent_id=doc_partner.value AND part_i18n.lang="' . $model_lang . '")' . "\n";

        $where = array();
        $where[] = 'd.active=1';
        $where[] = 'd.deleted_by=0';
        $where[] = 'd.type=' . DOCUMENT_TYPE_POSSIBILITY;

        if (!empty($filters['possibility_num'])) {
            $where[] = 'd.full_num LIKE "%' . General::slashesEscape($filters['possibility_num']) . '%"';
        }
        if (!empty($filters['business_partner'])) {
            $business_partners = array_filter($filters['business_partner']);
            if (!empty($business_partners)) {
                $where[] = 'doc_partner.value IN ("' . implode('","', $business_partners) . '")';
            }
        }
        if (!empty($filters['product'])) {
            $products = array_filter($filters['product']);
            if (!empty($products)) {
                $sql['from'] = str_replace('LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2', 'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2', $sql['from']);
                $where[] = 'gt2.article_id IN ("' . implode('","', $products) . '")';
            }
        }
        if (!empty($filters['deliverer'])) {
            $deliverers = array_filter($filters['deliverer']);
            if (!empty($deliverers)) {
                $sql['from'] = str_replace('LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2', 'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2', $sql['from']);
                $where[] = 'gt2.article_deliverer IN ("' . implode('","', $deliverers) . '")';
            }
        }
        if (!empty($filters['tag'])) {
            $sql['from'] .= "\n" . 'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm ON (tm.model="Document" AND tm.model_id=d.id AND tm.tag_id IN ("' . implode('","', $filters['tag']) . '"))';
        }
        if (!empty($filters['percent'])) {
            $where[] = 'doc_prob.value IN ("' . implode('","', $filters['percent']) . '")';
        }
        if (!empty($filters['date_from'])) {
            $where[] = 'd.date>="' . $filters['date_from'] . '"';
        }
        if (!empty($filters['date_to'])) {
            $where[] = 'd.date<="' . $filters['date_to'] . '"';
        }
        if (!empty($filters['win_from'])) {
            $where[] = 'DATE_FORMAT(doc_date.value, "%Y-%m-%d")>="' . $filters['win_from'] . '"';
        }
        if (!empty($filters['win_to'])) {
            $where[] = 'DATE_FORMAT(doc_date.value, "%Y-%m-%d")<="' . $filters['win_to'] . '"';
        }
        if (!empty($filters['fil_q'])) {
            $where[] = 'doc_q.value IN ("' . implode('","', $filters['fil_q']) . '")';
        }
        if (!empty($filters['client'])) {
            $client = array_filter($filters['client']);
            if (!empty($client)) {
                $where[] = 'd.customer IN ("' . implode('","', $client) . '")';
            }
        }
        if (!empty($filters['trader'])) {
            $where[] = 'c.assigned="' . $filters['trader'] . '"';
        }
        if (!empty($filters['media'])) {
            $where[] = 'd.media="' . $filters['media'] . '"';
        }
        if (!empty($filters['sector'])) {
            $sector = array_filter($filters['sector']);
            if (!empty($sector)) {
                $where[] = 'cust_sect.value IN ("' . implode('","', $sector) . '")';
            }
        }
        if (!empty($filters['city'])) {
            $where[] = 'ci18n.city LIKE "%' . General::slashesEscape($filters['city']) . '%"';
        }
        if (!empty($filters['stage'])) {
            $stage = array_filter($filters['stage']);
            $stage_clauses = array();
            foreach ($stage as $stg) {
                if (preg_match('#^substatus_([0-9]+)$#', $stg)) {
                    $stage_clauses[] = 'd.substatus="' . preg_replace('#^substatus_([0-9]+)$#', '$1', $stg) . '"';
                } else {
                    $stage_clauses[] = 'd.status="' . $stg . '"';
                }
            }
            if (!empty($stage_clauses)) {
                $where[] = '(' . implode(' OR ', $stage_clauses) . ')';
            }
        }
        if (!empty($filters['kind'])) {
            $where[] = 'g2i18n.free_text1="' . $filters['kind'] . '"';
        }
        if (!empty($filters['type_customer'])) {
            $where[] = 'c.type="' . $filters['type_customer'] . '"';
        }
        $sql['where'] = 'WHERE ' . implode(' AND ', $where);
        $sql['sort'] = 'ORDER BY d.date DESC, d.id ASC, gt2.id ASC';
        $query = implode("\n", $sql);

        $data = $this->registry['db']->GetAll($query);

        $final_results_tbl1 = array();
        $final_results_tbl2 = array();

        // prepare the options for the second table
        $sql_kind = 'SELECT label, option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE parent_name="' . DOCUMENT_VAR_KIND . '" AND lang="' . $this->registry['lang'] . '" ORDER BY position ASC';
        $kinds_included = $this->registry['db']->getAll($sql_kind);
        foreach ($kinds_included as $knd_incl) {
            $final_results_tbl2[$knd_incl['option_value']] = array(
                'total_count'    => 0,
                'label'          => $knd_incl['label'],
                'total_delivery' => 0,
                'total_sell'     => 0,
                'difference'     => 0
            );
        }

        $documents_ids = array();
        $numeric_values = array('probability', 'comments', 'tasks', 'files', 'price', 'delivery_price', 'difference', 'total_delivery', 'total_sell', 'total_count');
        foreach ($data as $dat) {
            if (!in_array($dat['id'], $documents_ids)) {
                $documents_ids[] = $dat['id'];
            }

            $new_row = $dat;
            if ($dat['substatus']) {
                $new_row['status'] = $dat['substatus'];
            } else {
                $new_row['status'] = $this->registry['translater']->translate('reports_possibility_status_' . $dat['status']);
            }
            unset($new_row['substatus']);
            unset($new_row['kind_opt']);

            $contact_data = array('gsm', 'email');
            foreach ($contact_data as $cont_dat) {
                $current_contract_data = array();

                $contacts = explode("\n", $dat[$cont_dat]);
                foreach ($contacts as $idx => $contact_data) {
                    @list($contact, $contact_note) = explode("|", $contact_data);
                    $current_contract_data[] = $contact;
                }
                $new_row[$cont_dat] = implode("\n", array_filter($current_contract_data));
            }

            // complete the data in the second table
            if (isset($final_results_tbl2[$dat['kind_opt']])) {
                $final_results_tbl2[$dat['kind_opt']]['total_count']++;
                $final_results_tbl2[$dat['kind_opt']]['total_delivery'] += doubleval($dat['delivery_price']);
                $final_results_tbl2[$dat['kind_opt']]['total_sell'] += doubleval($dat['price']);
                $final_results_tbl2[$dat['kind_opt']]['difference'] = $final_results_tbl2[$dat['kind_opt']]['total_sell'] - $final_results_tbl2[$dat['kind_opt']]['total_delivery'];
            }


            foreach ($numeric_values as $num_values) {
                if (!isset($new_row[$num_values])) {
                    continue;
                }
                if (is_numeric($new_row[$num_values])) {
                    $new_row[$num_values] = doubleval($new_row[$num_values]);
                } else {
                    $new_row[$num_values] = NULL;
                }
            }
            $new_row['tag'] = array();
            $final_results_tbl1[] = $new_row;
        }

        // format the numeric values in the second table
        foreach ($final_results_tbl2 as $idx => $item) {
            foreach ($item as $dat_idx => $dat_val) {
                if (in_array($dat_idx, $numeric_values)) {
                    if (is_numeric($final_results_tbl2[$idx][$dat_idx])) {
                        $final_results_tbl2[$idx][$dat_idx] = doubleval($final_results_tbl2[$idx][$dat_idx]);
                    } else {
                        $final_results_tbl2[$idx][$dat_idx] = NULL;
                    }
                }
            }
        }
        $final_results_tbl2 = array_values($final_results_tbl2);

        // calculate the documents, tasks and files
        if (!empty($documents_ids)) {
            $sql = 'SELECT `model_id`, COUNT(`id`) FROM ' . DB_TABLE_FILES . ' WHERE `model`="Document" AND `model_id` IN ("' . implode('","', $documents_ids) .  '") AND `deleted_by`=0 GROUP BY `model_id`' . "\n";
            $files_count = $this->registry['db']->GetAssoc($sql);

            $sql = 'SELECT `link_to`, COUNT(`parent_id`) FROM ' . DB_TABLE_TASKS_RELATIVES . ' WHERE `origin`="document" AND `link_to` IN ("' . implode('","', $documents_ids) .  '") GROUP BY `link_to`' . "\n";
            $tasks_count = $this->registry['db']->GetAssoc($sql);

            $sql = 'SELECT `model_id`, COUNT(`id`) FROM ' . DB_TABLE_COMMENTS . ' WHERE `model`="Document" AND `model_id` IN ("' . implode('","', $documents_ids) .  '") AND `deleted_by`=0 GROUP BY `model_id`' . "\n";
            $comments_count = $this->registry['db']->GetAssoc($sql);

            $sql = 'SELECT tm.model_id, GROUP_CONCAT(ti18n.name SEPARATOR "|") FROM ' . DB_TABLE_TAGS_MODELS . ' as tm' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n ON (ti18n.parent_id=tm.tag_id AND ti18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                   'WHERE tm.model="Document" AND tm.model_id IN ("' . implode('","', $documents_ids) . '") AND tm.tag_id IN ("' . implode('","', $tags_included)  . '")' . "\n" .
                   'GROUP BY tm.model_id';
            $tags_models = $this->registry['db']->GetAssoc($sql);

            foreach ($final_results_tbl1 as $idx => $tbl_data) {
                if (isset($files_count[$tbl_data['id']])) {
                    $final_results_tbl1[$idx]['files'] = (in_array('files', $numeric_values) ? doubleval($files_count[$tbl_data['id']]) : $files_count[$tbl_data['id']]);
                }
                if (isset($tasks_count[$tbl_data['id']])) {
                    $final_results_tbl1[$idx]['tasks'] = (in_array('tasks', $numeric_values) ? doubleval($tasks_count[$tbl_data['id']]) : $tasks_count[$tbl_data['id']]);
                }
                if (isset($comments_count[$tbl_data['id']])) {
                    $final_results_tbl1[$idx]['comments'] = (in_array('comments', $numeric_values) ? doubleval($comments_count[$tbl_data['id']]) : $comments_count[$tbl_data['id']]);
                }
                if (isset($tags_models[$tbl_data['id']])) {
                    $final_results_tbl1[$idx]['tag'] = explode('|', $tags_models[$tbl_data['id']]);
                }
            }
        }

        echo json_encode(array(
            'table1' => $final_results_tbl1,
            'table2' => $final_results_tbl2,
            'url'    => $redirect_url
        ));
        exit;
    }

}
