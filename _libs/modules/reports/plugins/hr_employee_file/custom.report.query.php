<?php
    Class Hr_Employee_File Extends Reports {
        /**
         * Name of the model
         */
        public static $filters = array();
        public static $lang = array();
        public static $models = array();
        public static $registry;


        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }
            self::$filters = $filters;
            self::$lang = $model_lang;
            self::$registry = $registry;

            $final_results = array();
            if (!empty($filters['employee'])) {
                $final_results['visible_panels'] = preg_split('/\s*,\s*/', REPORT_VISIBLE_PANELS);
                if (!empty($filters['validation_required_days_off']) && !in_array('days_off', $final_results['visible_panels'])) {
                    $final_results['visible_panels'][] = 'days_off';
                }

                // define the year for the rest of the report
                if (!empty($filters['years'])) {
                    $year = $filters['years'];
                } else {
                    $year = date('Y');
                }
                $get_old_vars = $registry->get('get_old_vars');

                // include customers factory file
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $filters_employee = array(
                    'where'      => array('c.id = ' . $filters['employee']),
                    'sanitize'   => true,
                    'model_lang' => $registry['lang'],
                    'skip_permissions_check' => true
                );
                $selected_employee = Customers::searchOne($registry, $filters_employee);
                if (!$selected_employee) {
                    $selected_employee = new Customer($registry);
                    $selected_employee->sanitize();
                }

                // get the data
                $final_results['id'] = $selected_employee->get('id');
                $final_results['year'] = $year;
                $final_results['name'] = sprintf('%s %s', $selected_employee->get('name'), $selected_employee->get('lastname'));
                $final_results['contacts'] = array();
                $final_results['occasional_days_active'] = LEAVE_REQUEST_OCCASIONAL;
                $final_results['school_days_active'] = LEAVE_REQUEST_SCHOOL;
                // TODO: fix this when checkPermissions method is corrected to check the rights the right way
                $rights = $selected_employee->setPermissions(array(), false, 'customers');
                $final_results['employees_active_link'] = $rights['list'];

                $get_old_vars = $registry->get('get_old_vars');
                $registry->set('get_old_vars', true, true);
                $employee_assoc_vars = $selected_employee->getAssocVars();
                if (EMPLOYEE_PICTURE && isset($employee_assoc_vars[EMPLOYEE_PICTURE]['value'])) {
                    $final_results['picture'] = $employee_assoc_vars[EMPLOYEE_PICTURE]['value'];
                }

                $employee_contacts = preg_split('#\s*,\s*#', EMPLOYEE_CONTACTS);
                $employee_contacts = array_filter($employee_contacts);

                foreach ($employee_contacts as $contact) {
                    $final_results['contacts'][$contact] = $selected_employee->get($contact);
                }

                $final_results['show_full_report'] = false;
                self::$models[strtolower(General::plural2singular($selected_employee->modelName)) . '_' . $selected_employee->get('id')] = $selected_employee->sanitize();

                // get the employee's user id
                $sql_user = sprintf('SELECT u.id as user_id, u.employee as employee, CONCAT("%d,", GROUP_CONCAT(DISTINCT ud.department_id SEPARATOR ",")) as department' . "\n" .
                                    'FROM %s as u' . "\n" .
                                    'LEFT JOIN %s as ud' . "\n" .
                                    '  ON (ud.parent_id=u.id)' . "\n" .
                                    'WHERE u.employee="%d"',
                    $selected_employee->get('department'),
                    DB_TABLE_USERS,
                    DB_TABLE_USERS_DEPARTMENTS,
                    $selected_employee->get('id')
                );

                $employee_user_info = $registry['db']->GetRow($sql_user);

                // check if the current user is running the report for him/herself
                if ($registry['currentUser']->get('id') == $employee_user_info['user_id']) {
                    $final_results['show_full_report'] = true;
                } else {
                    // check if the user has special permissions for running the report
                    if (defined('USER_' . $registry['currentUser']->get('id'))) {
                        //permission to user should be defined in such manner: user_<current_user_id> := user_X, user_Y, user_Z, department_X, department_Y, department_Z, employee_A, employee_B
                        $user_permissions = constant('USER_' . $registry['currentUser']->get('id'));
                        $user_permissions = preg_split('#\s*,\s*#', $user_permissions);

                        $permitted_users = array();
                        $permitted_departments = array();
                        $permitted_employees = array();

                        foreach ($user_permissions as $usr_perm) {
                            if (preg_match('#^user_#', $usr_perm)) {
                                $permitted_users[] = trim(str_replace('user_', '', $usr_perm));
                            } elseif (preg_match('#^department_#', $usr_perm)) {
                                $permitted_departments[] = trim(str_replace('department_', '', $usr_perm));
                            } elseif (preg_match('#^employee_#', $usr_perm)) {
                                $permitted_employees[] = trim(str_replace('employee_', '', $usr_perm));
                            }
                        }

                        if (in_array($employee_user_info['user_id'], $permitted_users) ||
                            in_array($employee_user_info['employee'], $permitted_employees) ||
                            array_intersect($permitted_departments, array_filter(preg_split('#\s*,\s*#', $employee_user_info['department']))) ||
                            !$employee_user_info['user_id']) {
                            $final_results['show_full_report'] = true;
                        }
                    }
                }

                $files_filter = array();
                if (in_array('documents_list', $final_results['visible_panels'])) {
                    $final_results['documents_received'] = array();
                    $final_results['documents_departure'] = array();

                    // get the documents
                    if (!empty($employee_assoc_vars[EMPLOYEE_COMING_DOC_RECEIVED])) {
                        foreach ($employee_assoc_vars[EMPLOYEE_COMING_DOC_RECEIVED]['value'] as $key => $document_received) {
                            if (!empty($document_received)) {
                                $current_document_info = array(
                                    'type'  => '',
                                    'date'  => '',
                                    'file'  => ''
                                );

                                foreach ($employee_assoc_vars[EMPLOYEE_COMING_DOC_RECEIVED]['options'] as $opt) {
                                    if ($opt['option_value'] == $document_received) {
                                        $current_document_info['type'] = $opt['label'];
                                        break;
                                    }
                                }

                                if (!empty($employee_assoc_vars[EMPLOYEE_COMING_DOC_RECEIVED_DATE]['value'][$key])) {
                                    $current_document_info['date'] = $employee_assoc_vars[EMPLOYEE_COMING_DOC_RECEIVED_DATE]['value'][$key];
                                }
                                if (!empty($employee_assoc_vars[EMPLOYEE_COMING_DOC_RECEIVED_FILE]['value'][$key])) {
                                    $current_document_info['file'] = $employee_assoc_vars[EMPLOYEE_COMING_DOC_RECEIVED_FILE]['value'][$key];
                                }

                                $final_results['documents_received'][] = $current_document_info;
                            }
                        }
                    }

                    if (!empty($employee_assoc_vars[EMPLOYEE_DEPARTURE_DOC_RECEIVED])) {
                        foreach ($employee_assoc_vars[EMPLOYEE_DEPARTURE_DOC_RECEIVED]['value'] as $key => $document_received) {
                            if (!empty($document_received)) {
                                $current_document_info = array(
                                    'type'  => '',
                                    'date'  => '',
                                    'file'  => ''
                                );

                                foreach ($employee_assoc_vars[EMPLOYEE_DEPARTURE_DOC_RECEIVED]['options'] as $opt) {
                                    if ($opt['option_value'] == $document_received) {
                                        $current_document_info['type'] = $opt['label'];
                                        break;
                                    }
                                }

                                if (!empty($employee_assoc_vars[EMPLOYEE_DEPARTURE_DOC_RECEIVED_DATE]['value'][$key])) {
                                    $current_document_info['date'] = $employee_assoc_vars[EMPLOYEE_DEPARTURE_DOC_RECEIVED_DATE]['value'][$key];
                                }
                                if (!empty($employee_assoc_vars[EMPLOYEE_DEPARTURE_DOC_RECEIVED_FILE]['value'][$key])) {
                                    $current_document_info['file'] = $employee_assoc_vars[EMPLOYEE_DEPARTURE_DOC_RECEIVED_FILE]['value'][$key];
                                }

                                $final_results['documents_departure'][] = $current_document_info;
                            }
                        }
                    }
                }


                // GET CONTRACTS DATA
                // get all working contracts for the current customer
                $final_results['error_working_contracts'] = false;
                $final_results['date_start_working'] = '';
                $final_results['date_end_working'] = '';
                $final_results['working_period'] = '';
                $final_results['contracts'] = array();
                $final_results['available_days_off'] = 0;
                $final_results['year_extra_days_off'] = 0;
                $final_results['position'] = '';
                $final_results['education'] = '';
                $final_results['office'] = '';
                $final_results['room'] = '';
                $final_results['working_place'] = '';
                $final_results['technics_at_disposal'] = array();

                $model_defined = false;
                $working_contract = '';
                if (defined('WORKING_CONTRACT_MODEL')) {
                    if (WORKING_CONTRACT_MODEL == 'contract') {
                        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                        $filters_working_contract = array(
                            'where'      => array(
                                'co.customer = ' . $filters['employee'],
                                'co.subtype != "annex"',
                                'co.parent_record = 0',
                                'co.type = ' . WORKING_CONTRACT_MODEL_TYPE,
                                'co.active = 1',
                                'co.annulled_by = 0'
                            ),
                            'sort'       => array('co.date_start DESC'),
                            'model_lang' => $registry['lang']
                        );
                        $model_defined = true;
                    } elseif (WORKING_CONTRACT_MODEL == 'document') {
                        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                        $filters_working_contract = array(
                            'where'      => array(
                                'd.customer = ' . $filters['employee'],
                                'd.type = ' . WORKING_CONTRACT_MODEL_TYPE,
                                'd.deleted_by = 0',
                                'd.active = 1'
                            ),
                            'sort'       => array('d.id DESC'),
                            'model_lang' => $registry['lang']
                        );
                        $model_defined = true;
                    }
                }

                $working_contracts_documents = array();
                $working_contracts = array();
                if ($model_defined) {
                    $factory_name = ucfirst(General::singular2plural(WORKING_CONTRACT_MODEL));
                    $working_contracts = $factory_name::search($registry, $filters_working_contract);

                    $working_contract = '';
                    $first_working_contract = '';
                    foreach ($working_contracts as $wc) {
                        $current_contract_related_documents = array();
                        $current_contract_related_documents[] = $wc;
                        $wc->unsanitize();
                        if (empty($working_contract)) {
                            $working_contract = $wc;
                        }
                        $first_working_contract = $wc;

                        $wc->getAllVars();
                        self::$models[strtolower(General::plural2singular($wc->modelName)) . '_' . $wc->get('id')] = $wc->sanitize();

                        $current_contract_related_documents[] = $wc;
                        // search the records related with the working contracts
                        if (WORKING_CONTRACT_MODEL == 'contract') {
                            $filters_related_data = array(
                                'where'      => array(
                                    'co.customer = ' . $filters['employee'],
                                    'co.subtype = "annex"',
                                    'co.parent_record = ' . $wc->get('id'),
                                    'co.type = ' . WORKING_CONTRACT_MODEL_TYPE,
                                    'co.active = 1',
                                    'co.annulled_by = 0'
                                ),
                                'sort'       => array('co.date_start ASC'),
                                'sanitize'   => true,
                                'model_lang' => $registry['lang']
                            );
                            $current_contract_related_documents = array_merge($current_contract_related_documents, Contracts::search($registry, $filters_related_data));
                        } elseif (WORKING_CONTRACT_MODEL == 'document') {
                            // search for the related documents marked as additional agreements
                            $sql_relatives = 'SELECT `parent_id` FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . "\n" .
                                             'WHERE `link_to`="' . $wc->get('id') . '" AND `link_to_model_name`="Document" AND `parent_model_name`="Document"' . "\n";
                            $related_documents = $registry['db']->GetCol($sql_relatives);

                            if (!empty($related_documents)) {
                                $filters_related_data = array(
                                    'where'      => array(
                                        'd.id IN (' . implode(',', $related_documents) . ')',
                                        'd.type = ' . WORKING_CONTRACT_ANNEXES_MODEL_TYPE,
                                        'd.active = 1',
                                        'd.deleted_by = 0'
                                    ),
                                    'sort'       => array('d.added ASC'),
                                    'sanitize'   => true,
                                    'model_lang' => $registry['lang']
                                );
                                $current_contract_related_documents = array_merge($current_contract_related_documents, Documents::search($registry, $filters_related_data));
                            }
                        }

                        // reverse the array
                        $current_contract_related_documents = array_reverse($current_contract_related_documents);

                        $working_contracts_documents = array_merge($working_contracts_documents, $current_contract_related_documents);
                    }
                }

                foreach ($working_contracts_documents as $wcd) {
                    // define the name
                    if ($wcd->get('type') == WORKING_CONTRACT_MODEL_TYPE && $wcd->get('subtype') != 'annex') {
                        $current_contract_type = '';
                        $current_contract_type_label = $registry['translater']->translate('reports_working_contract');
                    } else {
                        $current_contract_type = '_annex';
                        $current_contract_type_label = $registry['translater']->translate('reports_additional_agreement');
                    }

                    $current_contract_date = self::processVarData('employee_start_working_date' . $current_contract_type, $wcd->get('id'));
                    $current_contract_finished = '';
                    if (WORKING_CONTRACT_MODEL == 'contract') {
                        $current_contract_finished = (($wcd->get('status') != 'closed') ? $registry['translater']->translate('reports_working_contract_not_finished') : '');
                    }

                    // define the label
                    $label = '(' . General::strftime('%d.%m.%Y', strtotime($current_contract_date)) . (!empty($current_contract_finished) ? ', ' . $current_contract_finished : '') . ')';

                    // TODO: fix this when checkPermissions method is corrected to check the rights the right way
                    $rights = $wcd->setPermissions(array(), false, 'contracts');

                    $final_results['contracts'][$wcd->get('id')] = array(
                        'id'            => $wcd->get('id'),
                        'link'          => sprintf('%s://%s%sindex.php?%s=%s&%s=%s&%s=%d',
                                                         (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                                         $_SERVER["HTTP_HOST"],
                                                         PH_BASE_URL,
                                                         $registry['module_param'],
                                                         General::singular2plural(WORKING_CONTRACT_MODEL),
                                                         General::singular2plural(WORKING_CONTRACT_MODEL),
                                                         (WORKING_CONTRACT_MODEL=='document' ? 'view' : 'viewtopic'),
                                                         (WORKING_CONTRACT_MODEL=='document' ? 'view' : 'viewtopic'),
                                                         $wcd->get('id')),
                        'label'         => $label,
                        'contract_type' => $current_contract_type_label,
                        // TODO: fix this when checkPermissions method is corrected to check the rights the right way
                        'permissions'   => $rights['list'],
                        'file'          => array()
                    );

                    // define file filters
                    $files_filter[] = array(
                        'model'      => ucfirst(WORKING_CONTRACT_MODEL),
                        'model_id'   => $wcd->get('id'),
                        'pattern_id' => ($current_contract_type=='contract_annex' ? WORKING_AGREEMENT_PATTERN : WORKING_CONTRACT_PATTERN),
                        'placement'  => General::singular2plural(WORKING_CONTRACT_MODEL),
                        'origin'     => 'generated'
                    );
                }

                // define start_working_date, end_working_date, working position, education, office and available days off
                if (defined('EMPLOYEE_START_WORKING_DATE_MODEL') && EMPLOYEE_START_WORKING_DATE_MODEL) {
                    if (EMPLOYEE_START_WORKING_DATE_MODEL == 'customer') {
                        $final_results['date_start_working'] = self::processVarData('employee_start_working_date', $filters['employee']);
                    } elseif (!empty($first_working_contract)) {
                        $final_results['date_start_working'] = self::processVarData('employee_start_working_date', $first_working_contract->get('id'));
                    }
                    if (!empty($final_results['date_start_working'])) {
                        $final_results['date_start_working'] = General::strftime('%Y-%m-%d', $final_results['date_start_working']);
                    }
                }
                if (defined('EMPLOYEE_END_WORKING_DATE_MODEL') && EMPLOYEE_END_WORKING_DATE_MODEL) {
                    if (EMPLOYEE_END_WORKING_DATE_MODEL == 'customer') {
                        $final_results['date_end_working'] = self::processVarData('employee_end_working_date', $filters['employee']);
                    } elseif (!empty($working_contract)) {
                        $final_results['date_end_working'] = self::processVarData('employee_end_working_date', $working_contract->get('id'));
                    }
                    if (!empty($final_results['date_end_working'])) {
                        $final_results['date_end_working'] = General::strftime('%Y-%m-%d', $final_results['date_end_working']);
                    }
                }
                if (defined('EMPLOYEE_POSITION_MODEL') && EMPLOYEE_POSITION_MODEL) {
                    if (EMPLOYEE_POSITION_MODEL == 'customer') {
                        $final_results['position'] = self::processVarData('employee_position', $filters['employee']);
                    } elseif (!empty($working_contract)) {
                        $final_results['position'] = self::processVarData('employee_position', $working_contract->get('id'));
                    }
                }
                if (defined('EMPLOYEE_EDUCATION_MODEL') && EMPLOYEE_EDUCATION_MODEL) {
                    if (EMPLOYEE_EDUCATION_MODEL == 'customer') {
                        $final_results['education'] = self::processVarData('employee_education', $filters['employee']);
                    } elseif (!empty($working_contract)) {
                        $final_results['education'] = self::processVarData('employee_education', $working_contract->get('id'));
                    }
                }
                if (defined('EMPLOYEE_OFFICE_MODEL') && EMPLOYEE_OFFICE_MODEL) {
                    if (EMPLOYEE_OFFICE_MODEL == 'customer') {
                        $final_results['office'] = self::processVarData('employee_office', $filters['employee']);
                    } elseif (!empty($working_contract)) {
                        $final_results['office'] = self::processVarData('employee_office', $working_contract->get('id'));
                    }

                    if (ctype_digit($final_results['office'])) {
                        // if the office is digit then try to get the name of the office
                        $sql = 'SELECT `name` FROM ' . DB_TABLE_OFFICES_I18N . ' WHERE `parent_id`="' . $final_results['office'] . '" AND `lang`="' . $registry['lang'] . '"';
                        $office_name = $registry['db']->GetOne($sql);
                        if ($office_name) {
                            $final_results['office'] = $office_name;
                        }
                    }
                }

                // calculate the company working days
                $final_results['working_periods'] = array();
                $total_date_diff = '';
                if (EMPLOYEE_START_WORKING_DATE_MODEL != 'customer') {
                    foreach ($working_contracts as $wc) {
                        $date_start_working = General::strftime('%Y-%m-%d', self::processVarData('employee_start_working_date', $wc->get('id')));
                        $date_end_working = self::processVarData('employee_end_working_date', $wc->get('id'));
                        if (empty($date_end_working)) {
                            $date_end_working = date('Y-m-d');
                        } else {
                            $date_end_working = General::strftime('%Y-%m-%d', $date_end_working);
                        }

                        if (empty($total_date_diff)) {
                            $total_date_diff = date_diff(date_create($date_start_working), date_create($date_end_working));
                        } else {
                            $total_date_diff = date_diff(date_create($date_start_working), date_add(date_create($date_end_working), $total_date_diff));
                        }
                        $final_results['working_periods'][] = array(
                            'from' => $date_start_working,
                            'to'   => ($date_end_working < date('Y-m-d') ? $date_end_working : '')
                        );
                    }
                } elseif (!empty($final_results['date_start_working'])) {
                    // calculate working period
                    if (!$final_results['date_end_working']) {
                        $date_end_working = date('Y-m-d');
                    } else {
                        $date_end_working = $final_results['date_end_working'];
                    }

                    $total_date_diff = date_diff(date_create($final_results['date_start_working']), date_create($date_end_working));

                    $final_results['working_periods'][] = array(
                        'from' => $final_results['date_start_working'],
                        'to'   => ($date_end_working < date('Y-m-d') ? $date_end_working : '')
                    );
                }

                if ($total_date_diff) {
                    $final_results['working_period'] = sprintf($registry['translater']->translate('report_working_period'), $total_date_diff->y, $total_date_diff->m, $total_date_diff->d);
                }

                // available days off
                // clears the years which are not included in the reports options
                $sql_years = 'SELECT option_value, "0" as free_days, active_option as active FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE parent_name="' . FREE_DAYS_YEAR . '" AND (active_option=1 OR option_value="' . $year . '") AND lang="' . $registry['lang'] . '" ORDER BY position DESC';
                $options_years = $registry['db']->GetAssoc($sql_years);

                $latest_year = date('Y');
                if (defined('INCLUDE_FUTURE_YEARS') && INCLUDE_FUTURE_YEARS) {
                    foreach ($options_years as $key => $opt_year) {
                        if ($key > $latest_year) $latest_year = $key;
                    }
                }

                $final_results['contract_available_days_off'] = 0;
                // check the model of the working contract
                $contract_chronology = false;
                $contract_chronology_list = array();
                $constant_name_model = strtoupper('employee_start_working_date') . '_MODEL';
                if (defined($constant_name_model) && strtolower(constant($constant_name_model)) == 'contract') {
                    $contract_chronology = true;
                }

                if (EMPLOYEE_START_WORKING_DATE_MODEL != 'customer') {
                    foreach ($working_contracts as $wc) {
                        $day_start_working = self::processVarData('employee_start_working_date', $wc->get('id'));
                        $day_end_working = self::processVarData('employee_end_working_date', $wc->get('id'));

                        $day_start_working = date_create($day_start_working);
                        if ($day_end_working) {
                            $day_end_working = date_create($day_end_working);
                        } else {
                            $day_end_working = date_create($latest_year . '-12-31');
                        }

                        if ($contract_chronology) {
                            if (defined('USE_UNFINISHED_CONTRACTS') && USE_UNFINISHED_CONTRACTS) {
                                $contract_chronology_list[] = array(
                                    'from'         => $day_start_working->format('Y-m-d'),
                                    'to'           => $day_end_working->format('Y-m-d'),
                                    'agreement'    => $wc->get('id'),
                                    'subtype'      => 'original'
                                );
                            } else {
                                $contract_chronology_list = $wc->getTimeline(array(
                                    'from'         => $day_start_working->format('Y-m-d'),
                                    'to'           => $day_end_working->format('Y-m-d'),
                                    'use_end_date' => $day_end_working->format('Y-m-d')
                                ));
                            }
                        }

                        for ($i = date_format($day_start_working, 'Y'); $i <= date_format($day_end_working, 'Y'); $i++) {
                            if (!array_key_exists($i, $options_years)) {
                                continue;
                            }
                            if (defined('EMPLOYEE_AVAILABLE_DAYS_OFF_MODEL') && EMPLOYEE_AVAILABLE_DAYS_OFF_MODEL && EMPLOYEE_AVAILABLE_DAYS_OFF_MODEL == 'customer') {
                                $days_off_model_id = $filters['employee'];
                            } else {
                                $days_off_model_id = $wc->get('id');
                            }

                            $final_results['contract_available_days_off'] = self::processVarData('employee_available_days_off', $days_off_model_id);
                            if ($contract_chronology) {
                                // go through all the periods and get the needed working days
                                // define inner periods
                                $year_start_date = date_create($i . '-01-01');
                                $year_end_date = date_create($i . '-12-31');
                                $periods = self::processPeriods($year_start_date, $year_end_date, $contract_chronology_list);
                                foreach($periods as $per) {
                                    $options_years[$i]['free_days'] += self::calculateAvailableFreeDaysPerYear(
                                        $i,
                                        clone($per['from']),
                                        clone($per['to']),
                                        $per['days_off']
                                    );
                                }
                            } else {
                                $options_years[$i]['free_days'] += self::calculateAvailableFreeDaysPerYear($i, $day_start_working, $day_end_working, $final_results['contract_available_days_off']);
                            }
                        }
                    }
                } elseif (!empty($final_results['date_start_working'])) {
                    // define the start and the end working year
                        $final_results['contract_available_days_off'] = self::processVarData('employee_available_days_off', $filters['employee']);
                        $day_start_working = date_create($final_results['date_start_working']);

                    if (!$final_results['date_end_working']) {
                        $day_end_working = date_create($latest_year . '-12-31');
                    } else {
                        $day_end_working = date_create($final_results['date_end_working']);
                    }

                    foreach ($options_years as $opt_year => $year_included) {
                        $start_of_year = date_create($opt_year . '-01-01');
                        $end_of_year = date_create($opt_year . '-12-31');

                        if (!($end_of_year >= $day_start_working && $day_end_working >= $start_of_year)) {
                            continue;
                        }
                        $options_years[$opt_year]['free_days'] = self::calculateAvailableFreeDaysPerYear($opt_year, $day_start_working, $day_end_working, $final_results['contract_available_days_off']);
                    }
                }

                $final_results['days_off_list'] = array();
                $final_results['contract_chronology_list'] = $contract_chronology_list;

                foreach ($options_years as $opt_year => $opt_years) {
                    $year_in_working_period = false;
                    foreach ($final_results['working_periods'] as $period_duration) {
                        $per_from = strftime('%Y', strtotime($period_duration['from']));
                        $per_to = strftime('%Y', (!empty($period_duration['to']) ? strtotime($period_duration['to']) : strtotime($opt_year . '-12-31')));
                        if ($per_from <= $opt_year && $per_to >= $opt_year) {
                            $year_in_working_period = true;
                        }
                    }

                    if (!$year_in_working_period) {
                        continue;
                    }
                    $final_results['days_off_list'][$opt_year] = array(
                        'year'           => (!$opt_years['active'] ? '*' : '') . $opt_year,
                        'used_days'      => 0,
                        'requested_days' => 0,
                        'left_days'      => $opt_years['free_days'],
                        'active_year'    => $opt_years['active'],
                        'used_by_year'   => array()
                    );
                }
                unset($options_years);

                $final_results['special_days'] = array();

                // Prepare calendar and the information for available days off
                if (in_array('calendar', $final_results['visible_panels']) || in_array('days_off', $final_results['visible_panels'])) {
                    if (!empty($final_results['days_off_list'][$year])) {
                        $final_results['available_days_off'] = $final_results['days_off_list'][$year]['left_days'];
                    } else {
                        $final_results['available_days_off'] = 0;
                    }

                    // define the used free days and sickness rest documents
                    $leave_request_vars = array(FREE_DAYS_TYPE, FREE_DAYS_COUNT, FREE_DAYS_YEAR, FREE_DAYS_START_DATE, FREE_DAYS_END_DATE);
                    $sickness_rest_vars = array(SICKNESS_REST_START_DATE, SICKNESS_REST_END_DATE, SICKNESS_REST_COUNT_DAYS);

                    //sql to take the ids of the needed additional vars
                    $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND ((fm.model_type="' . DOCUMENT_LEAVE_TYPE . '" AND fm.name IN ("' . implode('","', $leave_request_vars) . '")) OR (fm.model_type="' . SICKNESS_REST_DOCUMENT_ID . '" AND fm.name IN ("' . implode('","', $sickness_rest_vars) . '"))) ORDER BY fm.position';
                    $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                    $free_days_type_id = '';
                    $free_days_count_id = '';
                    $free_days_year_id = '';
                    $free_days_start_date_id = '';
                    $free_days_end_date_id = '';
                    $sickness_rest_start_date_id = '';
                    $sickness_rest_end_date_id = '';
                    $sickness_rest_count_days_id = '';

                    //assign the ids to vars
                    foreach ($var_ids as $vars) {
                        if ($vars['name'] == FREE_DAYS_TYPE) {
                            $free_days_type_id = $vars['id'];
                        } else if ($vars['name'] == FREE_DAYS_COUNT) {
                            $free_days_count_id = $vars['id'];
                        } else if ($vars['name'] == FREE_DAYS_YEAR) {
                            $free_days_year_id = $vars['id'];
                        } else if ($vars['name'] == FREE_DAYS_START_DATE) {
                            $free_days_start_date_id = $vars['id'];
                        } else if ($vars['name'] == FREE_DAYS_END_DATE) {
                            $free_days_end_date_id = $vars['id'];
                        } else if ($vars['name'] == SICKNESS_REST_START_DATE) {
                            $sickness_rest_start_date_id = $vars['id'];
                        } else if ($vars['name'] == SICKNESS_REST_END_DATE) {
                            $sickness_rest_end_date_id = $vars['id'];
                        } else if ($vars['name'] == SICKNESS_REST_COUNT_DAYS) {
                            $sickness_rest_count_days_id = $vars['id'];
                        }
                    }

                    // if the selected year is NOT before the start working year
                    if (!empty($final_results['date_start_working']) && $year >= General::strftime('%Y', $final_results['date_start_working']) || empty($final_results['date_start_working']) || !empty($filters['validation_required_days_off'])) {
                        // find special days to use
                        if (!empty($employee_assoc_vars[EMPLOYEE_SPECIAL_YEAR_DAYS_OFF]['value']) && !empty($employee_assoc_vars[EMPLOYEE_SPECIAL_DAYS_OFF]['value'])) {
                            $special_days_off = $employee_assoc_vars[EMPLOYEE_SPECIAL_YEAR_DAYS_OFF]['value'];
                            foreach ($special_days_off as $key => $sdo) {
                                if (!empty($sdo)) {
                                    $curr_yr = preg_replace('#' . SPECIAL_YEAR_DAYS_OFF_PREFIX . '#', '', $sdo);
                                    if (isset($final_results['days_off_list'][$curr_yr])) {
                                        $final_results['days_off_list'][$curr_yr]['left_days'] += (int)$employee_assoc_vars[EMPLOYEE_SPECIAL_DAYS_OFF]['value'][$key];
                                        // increase the base available days with the number of special days
                                        if ($curr_yr == $year) {
                                            $final_results['available_days_off'] += (int)$employee_assoc_vars[EMPLOYEE_SPECIAL_DAYS_OFF]['value'][$key];
                                            $final_results['year_extra_days_off'] += (int)$employee_assoc_vars[EMPLOYEE_SPECIAL_DAYS_OFF]['value'][$key];
                                        }
                                    }
                                }
                            }
                        }

                        // if the school days option is turned on, the school days are taken
                        if (LEAVE_REQUEST_SCHOOL) {
                            // get all the school days off for this employee
                            $final_results['school_days_off'] = array();
                            $school_periods_from = (!empty($employee_assoc_vars[SCHOOL_DAYS_OFF_DATE_FROM]['value']) ? array_filter($employee_assoc_vars[SCHOOL_DAYS_OFF_DATE_FROM]['value']) : array());
                            $school_periods_to = (!empty($employee_assoc_vars[SCHOOL_DAYS_OFF_DATE_TO]['value']) ? array_filter($employee_assoc_vars[SCHOOL_DAYS_OFF_DATE_TO]['value']) : array());
                            $school_days_count = (!empty($employee_assoc_vars[SCHOOL_DAYS_COUNT]['value']) ? array_filter($employee_assoc_vars[SCHOOL_DAYS_COUNT]['value']) : array());
                            if (!empty($school_periods_from) && !empty($school_periods_to) && !empty($school_days_count)) {
                                foreach ($school_periods_from as $key => $vals) {
                                    $date_from = new DateTime($school_periods_from[$key]);
                                    $date_to = new DateTime($school_periods_to[$key]);
                                    $final_results['school_days_off'][] = array(
                                        'period_from'    => $date_from->format('Y-m-d'),
                                        'period_to'      => $date_to->format('Y-m-d'),
                                        'available_days' => $school_days_count[$key],
                                        'used_days'      => 0,
                                        'left_days'      => $school_days_count[$key],
                                        'upcoming'       => 0,
                                        'label'          => sprintf('%s - %s', $date_from->format('d.m.Y'), $date_to->format('d.m.Y'))
                                    );
                                }
                            } else {
                                $final_results['school_days_off'] = array();
                            }
                        }

                        //sql to take all rows of documents_cstm table where the needed employee is found
                        $sql = array();
                        $sql['select']    =  'SELECT d.id AS id, d.status, d.substatus, d_cstm_free_days_type.value AS free_days_type, ' . "\n" .
                                             '  d_cstm_free_days_count.value AS free_days_count, DATE_FORMAT(d_cstm_free_days_start_date.value, "%Y-%m-%d") AS free_days_start_date, ' . "\n" .
                                             '  DATE_FORMAT(d_cstm_free_days_end_date.value, "%Y-%m-%d") AS free_days_end_date, d_cstm_free_days_year.value AS free_days_year' . "\n";

                        $sql['from']      =   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_type' . "\n" .
                                              '  ON (d_cstm_free_days_type.model_id=d.id AND d_cstm_free_days_type.var_id="' . $free_days_type_id . '" AND (d_cstm_free_days_type.lang="' . $registry['lang'] . '" OR d_cstm_free_days_type.lang=""))' . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_count' . "\n" .
                                              '  ON (d_cstm_free_days_count.model_id=d.id AND d_cstm_free_days_count.var_id="' . $free_days_count_id . '" AND (d_cstm_free_days_count.lang="' . $registry['lang'] . '" OR d_cstm_free_days_count.lang=""))' . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_year' . "\n" .
                                              '  ON (d_cstm_free_days_year.model_id=d.id AND d_cstm_free_days_year.var_id="' . $free_days_year_id . '" AND (d_cstm_free_days_year.lang="' . $registry['lang'] . '" OR d_cstm_free_days_year.lang=""))'  . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_start_date' . "\n" .
                                              '  ON (d_cstm_free_days_start_date.model_id=d.id AND d_cstm_free_days_start_date.var_id="' . $free_days_start_date_id . '" AND (d_cstm_free_days_start_date.lang="' . $registry['lang'] . '" OR d_cstm_free_days_start_date.lang=""))' . "\n" .
                                              'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_end_date' . "\n" .
                                              '  ON (d_cstm_free_days_end_date.model_id=d.id AND d_cstm_free_days_end_date.var_id="' . $free_days_end_date_id . '" AND (d_cstm_free_days_end_date.lang="' . $registry['lang'] . '" OR d_cstm_free_days_end_date.lang=""))' . "\n";

                        $sql['where']  =  'WHERE d.active=1 AND d.deleted_by=0 AND d.type="' . DOCUMENT_LEAVE_TYPE . '" AND d.customer="' . $filters['employee'] . '"';
                        if (!empty($filters['exclude_documents'])) {
                            $sql['where'] .= ' AND d.id NOT IN ("' . implode('","', $filters['exclude_documents']) . '")';
                        }
                        $sql['order'] = ' ORDER BY d_cstm_free_days_start_date.value' . "\n";

                        //search basic details with current lang parameters
                        $query = implode("\n", $sql);
                        $records = $registry['db']->GetAll($query);

                        // CALCULATING THE DAYS
                        $final_results['free_days_left'] = $final_results['available_days_off'];
                        $final_results['free_days_paid_asked'] = 0;
                        $final_results['free_days_paid_requested'] = 0;
                        $final_results['free_days_unpaid_asked'] = 0;
                        $final_results['free_days_unpaid_used'] = 0;
                        $final_results['free_days_occasional_used'] = 0;
                        $final_results['free_days_occasional_asked'] = 0;

                        $today = General::strftime("%Y-%m-%d");

                        $leave_requests_paid_options = array_filter(preg_split('#\s*,\s*#', LEAVE_REQUEST_PAID));
                        $leave_requests_unpaid_options = array_filter(preg_split('#\s*,\s*#', LEAVE_REQUEST_UNPAID));
                        $leave_requests_occasional_options = array_filter(preg_split('#\s*,\s*#', LEAVE_REQUEST_OCCASIONAL));
                        $leave_requests_school_options = array_filter(preg_split('#\s*,\s*#', LEAVE_REQUEST_SCHOOL));

                        $days_off_substatus_approved = array_filter(preg_split('#\s*,\s*#', DAYS_OFF_SUBSTATUS_APPROVED));
                        $days_off_substatus_disapproved = array_filter(preg_split('#\s*,\s*#', DAYS_OFF_SUBSTATUS_DISAPPROVED));

                        foreach ($records as $key_rec => $recs) {
                            // check if the days off are for the period when the employee works in the company
                            $inside_period = false;
                            foreach ($final_results['working_periods'] as $work_period) {
                                if ($recs['free_days_start_date'] >= $work_period['from']) {
                                    $inside_period = true;
                                    break;
                                }
                            }

                            // if the days off request is disapproved the requested days are not marked anywhere
                            if (!in_array($recs['substatus'], $days_off_substatus_disapproved) && array_key_exists($recs['free_days_year'], $final_results['days_off_list']) && $inside_period) {
                                if (in_array($recs['free_days_type'], $leave_requests_paid_options)) {
                                    if (in_array($recs['substatus'], $days_off_substatus_approved)) {
                                        $final_results['days_off_list'][$recs['free_days_year']]['left_days'] = $final_results['days_off_list'][$recs['free_days_year']]['left_days'] - $recs['free_days_count'];
                                        if ($today >= $recs['free_days_start_date']) {
                                            $final_results['days_off_list'][$recs['free_days_year']]['used_days'] += $recs['free_days_count'];
                                        } else {
                                            $final_results['free_days_paid_asked'] += $recs['free_days_count'];
                                        }
                                        if ($recs['free_days_year'] == $year) {
                                            $final_results['free_days_left'] = $final_results['free_days_left'] - $recs['free_days_count'];
                                        }
                                        if (!isset($final_results['days_off_list'][$recs['free_days_year']]['used_by_year'][General::strftime('%Y', strtotime($recs['free_days_start_date']))])) {
                                            $final_results['days_off_list'][$recs['free_days_year']]['used_by_year'][General::strftime('%Y', strtotime($recs['free_days_start_date']))] = 0;
                                            ksort($final_results['days_off_list'][$recs['free_days_year']]['used_by_year']);
                                        }
                                        $final_results['days_off_list'][$recs['free_days_year']]['used_by_year'][General::strftime('%Y', strtotime($recs['free_days_start_date']))] += $recs['free_days_count'];
                                    } else {
                                        $final_results['days_off_list'][$recs['free_days_year']]['requested_days'] += $recs['free_days_count'];
                                        $final_results['free_days_paid_requested'] += $recs['free_days_count'];
                                    }
                                }

                                // if the days are not yet approved, skip the rest of the calculation
                                if (!in_array($recs['substatus'], $days_off_substatus_approved)) {
                                    continue;
                                }

                                if (in_array($recs['free_days_type'], $leave_requests_unpaid_options)) {
                                    if ($today >= $recs['free_days_start_date']) {
                                        $final_results['free_days_unpaid_used'] += intval($recs['free_days_count']);
                                    } else {
                                        $final_results['free_days_unpaid_asked'] += intval($recs['free_days_count']);
                                    }
                                }

                                if (in_array($recs['free_days_type'], $leave_requests_occasional_options)) {
                                    if ($today >= $recs['free_days_start_date']) {
                                        $final_results['free_days_occasional_used'] += intval($recs['free_days_count']);
                                    } else {
                                        $final_results['free_days_occasional_asked'] += intval($recs['free_days_count']);
                                    }
                                }

                                if (in_array($recs['free_days_type'], $leave_requests_school_options)) {
                                    // check for which period this request applies
                                    //$days_in_this_request = $recs['free_days_count'];
                                    foreach ($final_results['school_days_off'] as $k_sdp => $school_days_periods) {
                                        if ($school_days_periods['period_from'] <= $recs['free_days_start_date'] && $recs['free_days_end_date']<=$school_days_periods['period_to']) {
                                            $period_start = date_create($recs['free_days_start_date']);
                                            $period_end = date_create($recs['free_days_end_date']);
                                            //$working_days_in_period = Calendars_Calendar::getWorkingDays($registry, $period_start->format('Y-m-d'), $period_end->format('Y-m-d'));
                                            $final_results['school_days_off'][$k_sdp]['left_days'] = $final_results['school_days_off'][$k_sdp]['left_days'] - $recs['free_days_count'];
                                            if ($today >= $period_start->format('Y-m-d')) {
                                                $final_results['school_days_off'][$k_sdp]['used_days'] = $final_results['school_days_off'][$k_sdp]['used_days'] + $recs['free_days_count'];
                                            } else {
                                                $final_results['school_days_off'][$k_sdp]['upcoming'] = $final_results['school_days_off'][$k_sdp]['upcoming'] + $recs['free_days_count'];
                                            }
                                        }
                                    }
                                }

                                $current_day_off = $recs['free_days_start_date'];
                                while ($current_day_off <= $recs['free_days_end_date']) {
                                    $final_results['special_days'][$current_day_off] = array(
                                        'link'  => sprintf('%s://%s%sindex.php?%s=documents&documents=view&view=%d',
                                            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                            $_SERVER["HTTP_HOST"], PH_BASE_URL, $registry['module_param'],
                                            $recs['id']),
                                        'class' => ''
                                    );
                                    if (in_array($recs['free_days_type'], $leave_requests_school_options)) {
                                        if ($today >= $recs['free_days_start_date']) {
                                            $final_results['special_days'][$current_day_off]['class'] = 'reports_school_day_off_used';
                                        } else {
                                            $final_results['special_days'][$current_day_off]['class'] = 'reports_school_day_off_requested';
                                        }
                                    } else {
                                        if ($recs['free_days_year'] == $year) {
                                            if (in_array($recs['free_days_type'], $leave_requests_paid_options)) {
                                                if ($today >= $recs['free_days_start_date']) {
                                                    $final_results['special_days'][$current_day_off]['class'] = 'reports_day_off_used';
                                                } else {
                                                    $final_results['special_days'][$current_day_off]['class'] = 'reports_day_off_requested';
                                                }
                                            } elseif (in_array($recs['free_days_type'], $leave_requests_unpaid_options)) {
                                                if ($today >= $recs['free_days_start_date']) {
                                                    $final_results['special_days'][$current_day_off]['class'] = 'reports_unpaid_day_off_used';
                                                } else {
                                                    $final_results['special_days'][$current_day_off]['class'] = 'reports_day_off_requested';
                                                }
                                            } elseif (in_array($recs['free_days_type'], $leave_requests_occasional_options)) {
                                                if ($today >= $recs['free_days_start_date']) {
                                                    $final_results['special_days'][$current_day_off]['class'] = 'reports_occasional_day_off_used';
                                                } else {
                                                    $final_results['special_days'][$current_day_off]['class'] = 'reports_occasional_day_off_requested';
                                                }
                                            }
                                        } else {
                                            if ($recs['free_days_year'] < $year) {
                                                $final_results['special_days'][$current_day_off]['overlib'] = sprintf($registry['translater']->translate('reports_days_left_from_year'), $recs['free_days_year']);
                                            } else {
                                                $final_results['special_days'][$current_day_off]['overlib'] = sprintf($registry['translater']->translate('reports_days_from_following_year'), $recs['free_days_year']);
                                            }
                                            $final_results['special_days'][$current_day_off]['class'] = 'reports_different_year_day_off';
                                        }
                                    }
                                    $current_day_off = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($current_day_off)));
                                }
                            }
                        }
                    }

                    if (defined('SICKNESS_REST_DOCUMENT_ID') && SICKNESS_REST_DOCUMENT_ID) {
                        // PREPARE SICKNESS REST DOCUMENTS
                        $sql = array();
                        $sql['select']    = 'SELECT d.id AS id, d_cstm_sickness_date_from.value AS date_from, d_cstm_sickness_date_to.value AS date_to, ' . "\n" .
                                            '  d_cstm_sickness_count_days.value AS count_days' . "\n";

                        $sql['from']      = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sickness_date_from' . "\n" .
                                            '  ON (d_cstm_sickness_date_from.model_id=d.id AND d_cstm_sickness_date_from.var_id="' . $sickness_rest_start_date_id . '" AND (d_cstm_sickness_date_from.lang="' . $registry['lang'] . '" OR d_cstm_sickness_date_from.lang=""))' . "\n" .
                                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sickness_date_to' . "\n" .
                                            '  ON (d_cstm_sickness_date_to.model_id=d.id AND d_cstm_sickness_date_to.var_id="' . $sickness_rest_end_date_id . '" AND (d_cstm_sickness_date_to.lang="' . $registry['lang'] . '" OR d_cstm_sickness_date_to.lang=""))' . "\n" .
                                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sickness_count_days' . "\n" .
                                            '  ON (d_cstm_sickness_count_days.model_id=d.id AND d_cstm_sickness_count_days.var_id="' . $sickness_rest_count_days_id . '" AND (d_cstm_sickness_count_days.lang="' . $registry['lang'] . '" OR d_cstm_sickness_count_days.lang=""))'  . "\n";

                        $sql['where']  =  'WHERE d.active=1 AND d.deleted_by=0 AND d.type="' . SICKNESS_REST_DOCUMENT_ID . '" AND d.employee="' . $filters['employee'] . '" AND (DATE_FORMAT(d_cstm_sickness_date_from.value, "%Y")="' . $year . '" OR DATE_FORMAT(d_cstm_sickness_date_to.value, "%Y")="' . $year . '")';
                        if (!empty($filters['exclude_documents'])) {
                            $sql['where'] .= ' AND d.id NOT IN ("' . implode('","', $filters['exclude_documents']) . '")';
                        }
                        if (SICKNESS_REST_APPROVE_STATUS) {
                            $sql['where'] .= ' AND d.substatus="' . SICKNESS_REST_APPROVE_STATUS . '"';
                        }

                        //search basic details with current lang parameters
                        $query = implode("\n", $sql);
                        $records = $registry['db']->GetAll($query);

                        $final_results['sickness_rest'] = 0;
                        foreach ($records as $rec) {
                            if (General::strftime('%Y', strtotime($rec['date_from'])) == $year) {
                                $final_results['sickness_rest'] += (float) $rec['count_days'];
                            }

                            $current_sickness_day = $rec['date_from'];
                            while ($current_sickness_day <= $rec['date_to']) {
                                $final_results['special_days'][$current_sickness_day] = array(
                                    'link'  => sprintf('%s://%s%sindex.php?%s=documents&documents=view&view=%d',
                                        (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                        $_SERVER["HTTP_HOST"], PH_BASE_URL, $registry['module_param'],
                                        $rec['id']),
                                    'class' => 'reports_sickness_day'
                                );
                                $current_sickness_day = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($current_sickness_day)));
                            }
                        }
                    }
                }

                // check overlapping periods
                foreach ($final_results['working_periods'] as $wp_idx => $wp) {
                    foreach ($final_results['working_periods'] as $wp_idx_ch => $wp_ch) {
                        if ($wp_idx_ch > $wp_idx) {
                            $base_period_end = ($wp['to'] ? $wp['to'] : date('Y-m-d'));
                            $check_period_end = ($wp_ch['to'] ? $wp_ch['to'] : date('Y-m-d'));
                            if ($wp['from'] <= $check_period_end && $base_period_end >= $wp_ch['from'] ) {
                                $registry['messages']->setWarning($registry['translater']->translate('warning_working_contracts_overlapping_dates'));
                                break 2;
                            }
                        }
                    }
                }

                foreach ($final_results['working_periods'] as $wp_idx => $wp) {
                    $final_results['working_periods'][$wp_idx] = sprintf(
                        '%s - %s',
                        General::strftime($registry['translater']->translate('date_short'), $wp['from']),
                        (!empty($wp['to']) ? General::strftime($registry['translater']->translate('date_short'), $wp['to']) : $registry['translater']->translate('report_working_period_till_today'))
                    );
                }


                // PREPARE THE FILES AS MODELS
                if (!empty($files_filter)) {
                    $clauses = array();
                    foreach ($files_filter as $ff) {
                        $current_clause_list = array();
                        foreach ($ff as $f_field => $f_value) {
                            if ($f_field != 'placement') {
                                $current_clause_list[] = sprintf('`%s`="%s"', $f_field, $f_value);
                            }
                        }
                        $clauses[] = '(' . implode(' AND ', $current_clause_list) . ')';
                    }
                    $files_sql = 'SELECT `id` FROM ' . DB_TABLE_FILES . ' WHERE `deleted_by`=0 AND (' . implode(' OR ', $clauses) . ') ORDER BY `revision` DESC';
                    $files_ids = $registry['db']->GetCol($files_sql);

                    if (!empty($files_ids)) {
                        // include files factory file
                        require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                        $filters_files = array(
                            'where'      => array('f.id IN (' . implode(',', $files_ids) . ')'),
                            'sanitize'   => true,
                            'model_lang' => $registry['request']->get('model_lang'),
                            'sort'       => array('f.id DESC')
                        );
                        $files_list = Files::search($registry, $filters_files);

                        foreach ($files_filter as $f_filt) {
                            foreach ($files_list as $fl) {
                                if ($fl->get('model')==$f_filt['model'] && $fl->get('model_id')==$f_filt['model_id'] && $fl->get('origin')==$f_filt['origin']) {
                                    $final_results[$f_filt['placement']][$f_filt['model_id']]['file'] = $fl;
                                    break;
                                }
                            }
                        }
                    }
                }

                if (in_array('calendar', $final_results['visible_panels'])) {
                    require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                    $calendar = new Calendars_Calendar(date('Y-m-d', strtotime($year.'-01-01')));
                    $final_results['calendar'] = $calendar;

                    $calendar_settings = Calendars_Calendar::getSettings($registry);
                    $final_results['monday_start'] = $calendar_settings['week_start_day'];
                }

                $registry->set('get_old_vars', $get_old_vars, true);

                if (in_array('working_place', $final_results['visible_panels'])) {
                    // GET WORKING PLACE DATA
                    // check if the working place has to be taken from customer or from the nomenclature
                    if (WORKING_PLACE_SOURCE == 'nomenclature') {
                        $working_place_add_vars = array(NOMENCLATURE_EMPLOYEE, NOMENCLATURE_WORK_PLACE);
                        $sql_for_nom_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Nomenclature" AND fm.model_type="' . NOMENCLATURE_ROOM . '" AND fm.name IN ("' . implode('","', $working_place_add_vars) . '")';
                        $nom_ids = $registry['db']->GetAll($sql_for_nom_add_vars);

                        $nomenclature_employee_id = '';
                        $nomenclature_work_place_id = '';

                        //assign the ids to vars
                        foreach ($nom_ids as $vars) {
                            if ($vars['name'] == NOMENCLATURE_EMPLOYEE) {
                                $nomenclature_employee_id = $vars['id'];
                            } else if ($vars['name'] == NOMENCLATURE_WORK_PLACE) {
                                $nomenclature_work_place_id = $vars['id'];
                            }
                        }

                        // prepare sql query for getting working place data
                        $sql_nom_data = 'SELECT nom.id, CONCAT("[", nom.code, "] ", nomi18n.name) as room, n_cstm_place.value as place, nom_wp_i18n.name as working_place' . "\n" .
                                        'FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                        'LEFT JOIN ' .  DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                        ' ON (nom.id=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                        'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_employee' . "\n" .
                                        '  ON (n_cstm_employee.model_id=nom.id AND n_cstm_employee.var_id="' . $nomenclature_employee_id . '" AND n_cstm_employee.value="' . $filters['employee'] . '" AND (n_cstm_employee.lang="' . $registry['lang'] . '" OR n_cstm_employee.lang=""))' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_place' . "\n" .
                                        '  ON (n_cstm_place.model_id=nom.id AND n_cstm_place.var_id="' . $nomenclature_work_place_id . '" AND n_cstm_employee.num=n_cstm_place.num AND (n_cstm_place.lang="' . $registry['lang'] . '" OR n_cstm_place.lang=""))' . "\n" .
                                        'LEFT JOIN ' .  DB_TABLE_NOMENCLATURES_I18N . ' AS nom_wp_i18n' . "\n" .
                                        ' ON (n_cstm_place.value=nom_wp_i18n.parent_id AND nom_wp_i18n.lang="' . $model_lang . '")' . "\n" .
                                        'WHERE nom.type="' . NOMENCLATURE_ROOM . '"' . "\n";
                        $working_place_data = $registry['db']->GetRow($sql_nom_data);
                    } elseif (WORKING_PLACE_SOURCE == 'customer') {
                        $sql_for_nom_add_vars = 'SELECT `model`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE' . "\n" .
                                                '       (`model`="Nomenclature" AND `model_type`="' . NOMENCLATURE_ROOM . '" AND `name`="' . NOMENCLATURE_WORK_PLACE . '") OR' . "\n" .
                                                '       (`model`="Customer" AND `model_type`="' . PH_CUSTOMER_EMPLOYEE . '" AND `name`="' . CUSTOMER_WORK_PLACE . '")' . "\n";
                        $var_ids = $registry['db']->GetAll($sql_for_nom_add_vars);

                        $customer_work_place_id = '';
                        $nomenclature_work_place_id = '';

                        //assign the ids to vars
                        foreach ($var_ids as $vars) {
                            if ($vars['model'] == 'Customer') {
                                $customer_work_place_id = $vars['id'];
                            } else if ($vars['model'] == 'Nomenclature') {
                                $nomenclature_work_place_id = $vars['id'];
                            }
                        }

                        // prepare sql query for getting working place data
                        $sql_nom_data = 'SELECT nom.id, CONCAT("[", nom.code, "] ", nomi18n.name) as room, c_cstm_place.value as place, nom_wp_i18n.name as working_place' . "\n" .
                                        'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_place' . "\n" .
                                        ' ON (c_cstm_place.model_id=c.id AND c_cstm_place.var_id="' . $customer_work_place_id . '" AND (c_cstm_place.lang="' . $registry['lang'] . '" OR c_cstm_place.lang=""))' . "\n" .
                                        'LEFT JOIN ' .  DB_TABLE_NOMENCLATURES_I18N . ' AS nom_wp_i18n' . "\n" .
                                        ' ON (c_cstm_place.value=nom_wp_i18n.parent_id AND nom_wp_i18n.lang="' . $model_lang . '")' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_room' . "\n" .
                                        ' ON (c_cstm_place.value!="" AND n_cstm_room.var_id="' . $nomenclature_work_place_id . '" AND n_cstm_room.value=c_cstm_place.value AND (n_cstm_room.lang="' . $registry['lang'] . '" OR n_cstm_room.lang=""))' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                        ' ON (nom.id=n_cstm_room.model_id AND nom.active=1 AND nom.deleted_by=0)' . "\n" .
                                        'LEFT JOIN ' .  DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                        ' ON (nom.id=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                        'WHERE c.id="' . $filters['employee'] . '"';
                        $working_place_data = $registry['db']->GetRow($sql_nom_data);
                    }

                    if (!empty($working_place_data)) {
                        $final_results['room'] = $working_place_data['room'];
                        $final_results['working_place'] = $working_place_data['working_place'];
                    }


                    // GET ASSIGNED INVENTORY
                    $document_vars = array(INVENTORY_ID,INVENTORY_EMPLOYEE,INVENTORY_DATE_RECEIVED,INVENTORY_DATE_RETURNED);

                    //sql to take the ids of the needed additional vars
                    $sql_for_add_vars = 'SELECT fm.name, fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE (fm.model="Document" AND fm.model_type="' . INVENTORY_REPORT . '" AND fm.name IN ("' . implode('","', $document_vars) . '"))';
                    $inventory_vars = $registry['db']->GetAssoc($sql_for_add_vars);

                    //sql to take all the employees and equipment matching the criteria
                    $sql = array();
                    $where = array();

                    $sql['select'] = 'SELECT d_cstm_inventory_id.value AS inventory_id, ' . "\n" .
                                     ' CONCAT (n.code, " ", ni18n.name) AS name, d_cstm_date.value as date' . "\n";

                    $sql['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n";

                    if (!empty($inventory_vars[INVENTORY_EMPLOYEE])) {
                        $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_emp' . "\n" .
                                        '  ON (d_cstm_emp.model_id=d.id AND d_cstm_emp.var_id="' . (!empty($inventory_vars[INVENTORY_EMPLOYEE]) ? $inventory_vars[INVENTORY_EMPLOYEE] : '') . '" AND d_cstm_emp.value="' . $filters['employee'] . '" AND (d_cstm_emp.lang="' . $registry['lang'] . '" OR d_cstm_emp.lang=""))' . "\n";
                        $where[] = 'd_cstm_emp.value IS NOT NULL';
                    } else {
                        $where[] = 'd.employee="' . $filters['employee'] . '"';
                    }

                    $sql['from'] .=  'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_inventory_id' . "\n" .
                                     '  ON (d_cstm_inventory_id.model_id=d.id AND d_cstm_inventory_id.var_id="' . (!empty($inventory_vars[INVENTORY_ID]) ? $inventory_vars[INVENTORY_ID] : '') . '" AND (d_cstm_inventory_id.value!="" AND d_cstm_inventory_id.value!="0") AND (d_cstm_inventory_id.lang="' . $registry['lang'] . '" OR d_cstm_inventory_id.lang=""))' . "\n" .
                                     'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date' . "\n" .
                                     '  ON (d_cstm_date.model_id=d.id AND d_cstm_date.var_id="' . (!empty($inventory_vars[INVENTORY_DATE_RECEIVED]) ? $inventory_vars[INVENTORY_DATE_RECEIVED] : '') . '" AND (d_cstm_date.value!="" AND d_cstm_date.value!="0000-00-00") AND d_cstm_inventory_id.num=d_cstm_date.num AND (d_cstm_date.lang="' . $registry['lang'] . '" OR d_cstm_date.lang=""))' . "\n" .
                                     'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                                     '  ON (d_cstm_inventory_id.value=n.id)' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                     '  ON (n.id=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date_returned' . "\n" .
                                     '  ON (d_cstm_date_returned.model_id=d.id AND d_cstm_date_returned.var_id="' . (!empty($inventory_vars[INVENTORY_DATE_RETURNED]) ? $inventory_vars[INVENTORY_DATE_RETURNED] : '') . '" AND d_cstm_inventory_id.num=d_cstm_date_returned.num AND (d_cstm_date.lang="' . $registry['lang'] . '" OR d_cstm_date.lang=""))' . "\n";

                    $where[] = 'd.deleted_by=0';
                    $where[] = 'd.active=1';
                    $where[] = 'd.type="' . INVENTORY_REPORT . '"';
                    $where[] = '(d_cstm_date_returned.value IS NULL OR d_cstm_date_returned.value="")';

                    $sql['where'] = 'WHERE ' . implode(' AND ', $where);
                    $sql['order'] = 'ORDER BY d.added ASC' . "\n";

                    //search basic details with current lang parameters
                    $query = implode("\n", $sql);
                    $final_results['technics_at_disposal'] = $registry['db']->GetAssoc($query);
                }

                if (!$final_results['show_full_report']) {
                    if (!empty($final_results['visible_panels'])) {
                        foreach ($final_results['visible_panels'] as $vsb_key => $vsb_pan) {
                            if ($vsb_pan == 'documents_list' ||
                                $vsb_pan == 'days_off' ||
                                $vsb_pan == 'calendar' ||
                                $vsb_pan == 'working_contracts' ||
                                $vsb_pan == 'working_place') {
                                unset($final_results['visible_panels'][$vsb_key]);
                            }
                        }
                    }
                }
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            }


            // query if pagination is needed
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                //no pagination required, return only the models
                $results = $final_results;
            }

            return $results;
        }

        /**
         * Function to sort the passed documents by date
         *
         * @param string $a
         * @param string $b
         * @return number
         */
        public static function sortingDocumentsByDate($a, $b) {
            return ($a['date'] > $b['date']) ? 1 : -1;
        }

        /**
         * Function to get the value of the passed variable
         *
         * @param string $var_name - variable name
         * @param string $apply_filter - filter value to apply when searching
         * @return string - found value
         */
        public static function processVarData($var_name, $apply_filter = '') {
            // value of the searched var
            $value = '';

            // registry
            $registry = self::$registry;

            // list of the models which are already found
            $models = self::$models;

            // process all the constants related to the searched var
            $constant_name_model = strtoupper($var_name) . '_MODEL';
            $constant_name_model_type = strtoupper($var_name) . '_MODEL_TYPE';
            $constant_name_model_filter = strtoupper($var_name) . '_FILTER';
            $constant_name_var = strtoupper($var_name) . '_VAR';
            $constant_name_var_type = strtoupper($var_name) . '_VAR_TYPE';

            // if no model name is defined the function returns an empty result
            if (!defined($constant_name_model)) {
                return $value;
            }

            // get the value of the constants and dependent values
            $current_var_model = constant($constant_name_model);
            $current_var_model_type = (defined($constant_name_model_type) ? constant($constant_name_model_type) : '');
            $current_var_filter = (defined($constant_name_model_filter) ? constant($constant_name_model_filter) : '');
            $current_var_name = (defined($constant_name_var) ? constant($constant_name_var) : $var_name);
            $current_var_type = (defined($constant_name_var_type) ? constant($constant_name_var_type) : 'basic');

            // get the table name depending on the model selected and the default filters for that model
            $table_name = '';
            $id_search_filters = array();
            switch ($current_var_model) {
                case 'document':
                    $table_name = DB_TABLE_DOCUMENTS;
                    require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                    $id_search_filters = array(
                        '`deleted_by`="0"',
                        '`active`="1"'
                    );
                    break;
                case 'contract':
                    $table_name = DB_TABLE_CONTRACTS;
                    require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                    $id_search_filters = array(
                        '`deleted_by`="0"',
                        '`active`="1"',
                        '`annulled_by`="0"'
                    );
                    break;
                case 'customer':
                    $table_name = DB_TABLE_CUSTOMERS;
                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                    $id_search_filters = array(
                        '`deleted_by`="0"'
                    );
                    break;
                default:
                    break;
            }

            // adds the filter for type
            if ($current_var_model_type) {
                $id_search_filters[] = '`type`="' . $current_var_model_type . '"';
            }

            // if there is a filter and the argument of the function has value then that filter is applied to the variable from the argument
            if ($apply_filter && $current_var_filter) {
                $id_search_filters[] = $current_var_filter . '="' . $apply_filter . '"';
            }

            // search only the id of the given model
            $id = '';
            if ($table_name) {
                $query = 'SELECT `id` FROM ' . $table_name . ' WHERE ' . (!empty($id_search_filters) ? implode(' AND ', $id_search_filters) : '1');
                $id = $registry['db']->GetOne($query);
            }

            if ($id) {
                // process model
                $found_model = '';
                if (isset($models[strtolower($current_var_model) . '_' . $id])) {
                    // if the searched model already exists in the list of previously found models, it is taken from there
                    $found_model = $models[strtolower($current_var_model) . '_' . $id];
                } else {
                    // find out the factory name and the alias of the filter
                    $factory_name = ucfirst(General::singular2plural($current_var_model));
                    $alias = $factory_name::getAlias($current_var_model, '');

                    // search for the model
                    $filters_model = array(
                        'where'      => array($alias . '.id = ' . $id),
                        'model_lang' => $registry['lang']
                    );
                    $found_model = $factory_name::searchOne($registry, $filters_model);
                    // if a model is found, all of its additional variables are taken
                    if ($found_model) {
                        $found_model->getAllVars();
                        $found_model->sanitize();
                        $models[strtolower($current_var_model) . '_' . $found_model->get('id')] = $found_model;
                    }
                }

                if ($found_model) {
                    // define the method which will take the required var
                    if ($current_var_type == 'basic') {
                        $value = $found_model->get($current_var_name);
                    } else {
                        $value = $found_model->getVarValue($current_var_name, false, true);
                    }
                    //IMPORTANT: empty dates (0000-00-00) should really be empty
                    $value = preg_match('#0000-00-00.*#', $value) ? '' : $value;
                }
            }

            // set the models back to the object so it can be used later
            self::$models = $models;

            return $value;
        }

        /**
         * Function to calculate free days available per year
         *
         * @param string $year - the year which function will return days for
         * @param DateTime $work_start - the date when the employee start working as stated in his working contract
         * @param DateTime $work_end - the date when the employee start working as stated in his working contract
         * @param int $available_days_to_use - the free days available according to employee's working contract
         * @return int - available days for this year
         */
        public static function calculateAvailableFreeDaysPerYear($year, $work_start, $work_end, $available_days_to_use) {
            $start_of_year = date_create($year . '-01-01');
            $end_of_year = date_create($year . '-12-31');
            $days_of_the_year = date_diff($start_of_year, date_add($end_of_year, new DateInterval('P1D')))->days;

            $available_days_per_day = (int)$available_days_to_use/(int)$days_of_the_year;

            $start_of_period = $start_of_year;
            if ($work_start > $start_of_year) {
                $start_of_period = $work_start;
            }

            $end_of_period = $end_of_year;
            if ($work_end < $end_of_year) {
                $end_of_period = $work_end;
            }

            return round(date_diff($start_of_period, date_add($end_of_period, new DateInterval('P1D')))->days * $available_days_per_day);
        }

        /**
         * @param $match_period_start - DateTime object with the date with the start of the period
         * @param $match_period_end - DateTime object with the date with the end of the period
         * @param $contracts_chronology - contracts chronology list containing the dates when agreements were enforced
         * @return array $periods - the split of the inner periods
         */
        public static function processPeriods($match_period_start, $match_period_end, $contracts_chronology = array()) {
            $periods = array();
            foreach ($contracts_chronology as $chron) {
                $per_start = date_create($chron['from']);
                $per_end = date_create($chron['to']);
                if ($per_start<=$match_period_end && $per_end>=$match_period_start) {
                    // period match - complete the needed dates
                    $periods[] = array(
                        'from'     => ($match_period_start >= $per_start ? $match_period_start : $per_start),
                        'to'       => ($match_period_end >= $per_end ? $per_end : $match_period_end),
                        'days_off' => self::processVarData('employee_available_days_off', $chron['agreement']),
                        'agreement' => $chron['agreement']
                    );
                }
            }

            return $periods;
        }
    }

?>
