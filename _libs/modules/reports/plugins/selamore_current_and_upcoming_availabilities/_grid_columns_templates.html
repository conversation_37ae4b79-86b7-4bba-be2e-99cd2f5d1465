<script id="template_supplier_information" type="text/x-template">
  <div>
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${supplier_id}{/literal}" target="_blank" title="{#supplier#}">
      {literal}${supplier_name}{/literal}
    </a>
  </div>
  {literal}${if (logistics_names)}{/literal}
    {literal}${for (logistician_name of logistics_names)}{/literal}
      <div title="{#logistician#}">{literal}${logistician_name}{/literal}</div>
    {literal}${/for}{/literal}
  {literal}${/if}{/literal}
</script>
<script id="template_product_name" type="text/x-template">
  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={literal}${id}{/literal}" target="_blank" title="{#supplier#}">
    {literal}${name}{/literal}
  </a>
</script>
<script id="template_specifications" type="text/x-template">
  {literal}${if (upcoming_quantities && upcoming_quantities.specifications)}{/literal}
    {literal}${for (specification of upcoming_quantities.specifications)}{/literal}
      <div class="specification_container">
        <div>
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${specification.id}{/literal}" target="_blank" title="{#specification#}">
            {literal}${specification.full_num}/${specification.date_formatted}/${specification.customer_name}{/literal}
          </a>
        </div>
        <div>{literal}${specification.delivery_dates}{/literal}</div>
      </div>
    {literal}${/for}{/literal}
  {literal}${/if}{/literal}
</script>
