<?php
    Class Argumentum_Employees_Rating Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $projects_type = array(PROJECT_TYPE_NOTARY_LEGAL_PROCEDURE_ID, PROJECT_TYPE_DEAL_ID, PROJECT_TYPE_MEDITION_ID, PROJECT_TYPE_ARBITRATION_ID, PROJECT_TYPE_ADMINISTRATIVE_DISCUSION_ID);

            //sql to take all rows of documents_cstm table where the needed employee is find
            $sql['select']    =  'SELECT p.id AS id, p.code AS code, p.manager as manager, pa.assigned_to as participant, ' . "\n" . 
                                 '  p_cstm_work_rate.value as work_rate, p_cstm_work_rate_name.label as work_rate_name, ' . "\n" .
                                 '  CONCAT(ui18n.firstname, " ", ui18n.lastname) AS manager_name, ' . "\n" .
                                 '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) AS participant_name ';

            $sql['from']      =   'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" . 
                                  'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' .  "\n" . 
                                  '  ON (fm.model="Project" AND fm.model_type=p.type AND fm.name="' . WORK_RATE . '")' . "\n" . 
                                  'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_work_rate' .  "\n" . 
                                  '  ON (p_cstm_work_rate.model_id=p.id AND p_cstm_work_rate.var_id=fm.id)' . "\n" . 
                                  'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS p_cstm_work_rate_name' .  "\n" . 
                                  '  ON (p_cstm_work_rate_name.option_value=p_cstm_work_rate.value AND p_cstm_work_rate_name.parent_name="' . WORK_RATE . '")' . "\n" . 
                                  'LEFT JOIN ' . DB_TABLE_PROJECTS_ASSIGNMENTS . ' AS pa' . "\n" .
                                  '  ON (pa.parent_id=p.id)' . "\n" . 
                                  'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                                  '  ON (p.manager=ui18n.parent_id AND ui18n.lang="' . $model_lang . '")' . "\n" . 
                                  'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                                  '  ON (pa.assigned_to=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n";

            $where = array();
            $where[] = 'p.deleted_by=0';
            $where[] = 'p.type IN (' . implode(', ', $projects_type) . ')';
            if (! empty($filters['customer'])) {
                $where[] = 'p.customer="' . $filters['customer'] . '"';
            }
            if (! empty($filters['project'])) {
                $where[] = 'p.id="' . $filters['project'] . '"';
            }
            if (! empty($filters['rating'])) {
                $where[] = 'p_cstm_work_rate.value="' . $filters['rating'] . '"';
            }
            if (! empty($filters['rating'])) {
                $where[] = 'p_cstm_work_rate.value="' . $filters['rating'] . '"';
            }
            if (!empty($filters['from_date']) || !empty($filters['to_date'])) {
                if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                    $where[] = '((p.date_start>="' . $filters['from_date'] . '" AND p.date_start<="' . $filters['to_date'] . ') OR (p.date_end>="' . $filters['from_date'] . '" AND p.date_end<="' . $filters['to_date'] . '))';
                } elseif (!empty($filters['from_date'])) {
                    $where[] = '(p.date_start>="' . $filters['from_date'] . '" AND p.date_end>="' . $filters['from_date'] . '")';
                } elseif (!empty($filters['from_date'])) {
                    $where[] = '(p.date_start<="' . $filters['from_date'] . '" AND p.date_end>="' . $filters['from_date'] . '")';
                }
            }

            $sql['where'] =  'WHERE ' . implode(' AND ', $where);

            $sql['order'] = ' ORDER BY p.id' . "\n";

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);

            $projects_list = array();
            $users_list = array();
            foreach ($records as $key => $record) {
                if (! isset($projects_list[$record['id']])) {
                    $projects_list[$record['id']]['id'] = $record['id'];
                    $projects_list[$record['id']]['code'] = $record['code'];
                    $projects_list[$record['id']]['work_rate'] = $record['work_rate'];
                    $projects_list[$record['id']]['work_rate_name'] = $record['work_rate_name'];
                    $projects_list[$record['id']]['manager'] = array();
                    if (! empty ($record['manager'])) {
                        $projects_list[$record['id']]['manager'][$record['manager']] = $record['manager_name'];
                    }
                    $projects_list[$record['id']]['participants'] = array();
                }
                if (!empty($record['participant'])) {
                    $projects_list[$record['id']]['participants'][$record['participant']] = $record['participant_name'];
                }
            }

            if (! empty($filters['employee'])) {
                foreach ($projects_list as $proj_id => $project) {
                    if (! array_key_exists($filters['employee'], $project['manager']) && ! array_key_exists($filters['employee'], $project['participants'])) {
                        unset($projects_list[$proj_id]);
                    } else {
                        if (! array_key_exists($filters['employee'], $project['manager'])) {
                            $projects_list[$proj_id]['manager'] = array();
                        }
                        if (! array_key_exists($filters['employee'], $project['participants'])) {
                            $projects_list[$proj_id]['participants'] = array();
                        } else {
                            foreach($project['participants'] as $part_id => $participant) {
                                if ($part_id != $filters['employee']) {
                                    unset($projects_list[$proj_id]['participants'][$part_id]);
                                }
                            }
                        }
                    }
                }
            }

                        // $users_list[$record['participant']] = array(
                            // 'id'            => $record['participant'],
                            // 'name'          => $record['participant_name'],
                            // 'a'             => array(),
                            // 'a_count'       => 0,
                            // 'b'             => array(),
                            // 'b_count'       => 0,
                            // 'c'             => array(),
                            // 'c_count'       => 0,
                            // 'd'             => array(),
                            // 'd_count'       => 0,
                            // 'e'             => array(),
                            // 'e_count'       => 0,
                            // 'f'             => array(),
                            // 'f_count'       => 0,
                            // 'no_rate'       => array(),
                            // 'no_rate_count' => 0,
                            // 'total_rates'   => 0,
                            // 'projects'      => array($record['id'])
                        // );
            $user_list = array();

            foreach ($projects_list as $project) {
                foreach ($project['manager'] as $manager_id => $manager_name) {
                    if (!isset($user_list[$manager_id])) {
                        $user_list[$manager_id] = array(
                            'id'            => $manager_id,
                            'name'          => $manager_name,
                            'a'             => array(),
                            'a_count'       => 0,
                            'b'             => array(),
                            'b_count'       => 0,
                            'c'             => array(),
                            'c_count'       => 0,
                            'd'             => array(),
                            'd_count'       => 0,
                            'e'             => array(),
                            'e_count'       => 0,
                            'f'             => array(),
                            'f_count'       => 0,
                            'no_rate'       => array(),
                            'no_rate_count' => 0,
                            'total_rates'   => 0,
                            'projects'      => array()
                        );
                    }
                    if (!in_array($project['id'], $user_list[$manager_id]['projects'])) {
                        $user_list[$manager_id]['projects'][] = $project['id'];
                    }
                }
                foreach ($project['participants'] as $participant_id => $participant_name) {
                    if (!isset($user_list[$participant_id])) {
                        $user_list[$participant_id] = array(
                            'id'            => $participant_id,
                            'name'          => $participant_name,
                            'a'             => array(),
                            'a_count'       => 0,
                            'b'             => array(),
                            'b_count'       => 0,
                            'c'             => array(),
                            'c_count'       => 0,
                            'd'             => array(),
                            'd_count'       => 0,
                            'e'             => array(),
                            'e_count'       => 0,
                            'f'             => array(),
                            'f_count'       => 0,
                            'no_rate'       => array(),
                            'no_rate_count' => 0,
                            'total_rates'   => 0,
                            'projects'      => array()
                        );
                    }
                    if (!in_array($project['id'], $user_list[$participant_id]['projects'])) {
                        $user_list[$participant_id]['projects'][] = $project['id'];
                    }
                }
            }

            $total_a = 0;
            $total_b = 0;
            $total_c = 0;
            $total_d = 0;
            $total_e = 0;
            $total_f = 0;
            $total_no_rates = 0;

            foreach ($user_list as $user_id => $user) {
                foreach ($user['projects'] as $project_id) {
                    if (isset ($projects_list[$project_id])) {
                        $place_to_put = '';
                        switch($projects_list[$project_id]['work_rate']) {
                            case A_RATE:
                                $place_to_put = 'a';
                                $user_list[$user_id]['a_count']++;
                                $user_list[$user_id]['total_rates']++;
                                $total_a++;
                                break;
                            case B_RATE:
                                $place_to_put = 'b';
                                $user_list[$user_id]['b_count']++;
                                $user_list[$user_id]['total_rates']++;
                                $total_b++;
                                break;
                            case C_RATE:
                                $place_to_put = 'c';
                                $user_list[$user_id]['c_count']++;
                                $user_list[$user_id]['total_rates']++;
                                $total_c++;
                                break;
                            case D_RATE:
                                $place_to_put = 'd';
                                $user_list[$user_id]['d_count']++;
                                $user_list[$user_id]['total_rates']++;
                                $total_d++;
                                break;
                            case E_RATE:
                                $place_to_put = 'e';
                                $user_list[$user_id]['e_count']++;
                                $user_list[$user_id]['total_rates']++;
                                $total_e++;
                                break;
                            case F_RATE:
                                $place_to_put = 'f';
                                $user_list[$user_id]['f_count']++;
                                $user_list[$user_id]['total_rates']++;
                                $total_f++;
                                break;
                            default:
                                $place_to_put = 'no_rate';
                                $user_list[$user_id]['no_rate_count']++;
                                $total_no_rates++;
                        }
                        $user_list[$user_id][$place_to_put][] = array(
                            'id'    => $projects_list[$project_id]['id'],
                            'code'  => $projects_list[$project_id]['code']
                        );
                    }
                }
            }

            $user_list['additional_options']['total_a'] = $total_a;
            $user_list['additional_options']['total_b'] = $total_b;
            $user_list['additional_options']['total_c'] = $total_c;
            $user_list['additional_options']['total_d'] = $total_d;
            $user_list['additional_options']['total_e'] = $total_e;
            $user_list['additional_options']['total_f'] = $total_f;

            // query if paginate is needed
            if (!empty($filters['paginate'])) {
                $results = array($user_list, 0);
            } else {
                //no pagination required return only the models
                $results = $user_list;
            }

            return $results;
        }
    }
?>