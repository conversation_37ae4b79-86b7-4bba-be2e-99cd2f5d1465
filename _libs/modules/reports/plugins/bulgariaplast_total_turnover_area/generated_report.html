{if $reports_results.type_results eq 'detailed'}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table reports_table">
    <tr class="reports_title_row reports_title_centered_middle">
      <td width="100" class="t_border"><div style="width: 100px;">{#reports_th_num#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 200px;">{#reports_th_customer#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_order_date#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_delivery_date#|escape}</div></td>
      <td width="150" class="t_border"><div style="width: 150px;">{#reports_th_office#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 200px;">{#reports_th_employee#|escape}</div></td>
      <td width="150" class="t_border"><div style="width: 150px;">{#reports_th_article_code#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 200px;">{#reports_th_article_name#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 200px;">{#reports_th_description#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_quantity#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_measure#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width:  80px;">{#reports_th_price#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width:  80px;">{#reports_th_value#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_total_value#|escape}</div></td>
      <td width="80"><div style="width: 80px;">{#reports_th_total_value_w_vat#|escape}</div></td>
    </tr>
    {foreach from=$reports_results.results item='order'}
      {cycle values='t_odd1 t_odd2,t_even1 t_even2' assign=row_class}
      {foreach from=$order.rows item='gt2_row' name='order_for'}
        <tr class="{$row_class}">
          {if $smarty.foreach.order_for.first}
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$order.id}" target="_blank">{$order.full_num|escape|default:#no_number#}</a>
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$order.customer}" target="_blank">{$order.customer_name|escape|default:#no_number#}</a>
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.deadline|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.office_name|escape|default:"&nbsp;"}
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.employee_name|escape|default:"&nbsp;"}
            </td>
          {/if}
          <td class="hleft t_border">
            {$gt2_row.article_code|escape|default:"&nbsp;"}
          </td>
          <td class="hleft t_border">
            {$gt2_row.article_name|escape|default:"&nbsp;"}
          </td>
          <td class="hleft t_border">
            {$gt2_row.article_description|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border">
            {$gt2_row.quantity|number_format:4:".":" "|escape|default:"0.0000"}
          </td>
          <td class="hleft t_border">
            {$gt2_row.article_measure_name|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border">
            {$gt2_row.price|number_format:4:".":" "|escape|default:"0.0000"}
          </td>
          <td class="hright t_border">
            {$gt2_row.subtotal|number_format:4:".":" "|escape|default:"0.0000"}
          </td>
          {if $smarty.foreach.order_for.first}
            <td class="hright t_border" rowspan="{$order.rowspan}">
              {$order.total|number_format:4:".":" "|escape|default:"0.0000"}
            </td>
            <td class="hright" rowspan="{$order.rowspan}">
              {$order.total_with_vat|number_format:4:".":" "|escape|default:"0.0000"}
            </td>
          {/if}
        </tr>
      {/foreach}
    {foreachelse}
      <tr class="{$row_class}">
        <td class="hleft t_border error" colspan="15">
          {#reports_no_results#|escape}
        </td>
      </tr>
    {/foreach}
    {foreach from=$reports_results.totals.measures item='total' name='tot'}
      <tr class="row_blue">
        {if $smarty.foreach.tot.first}
          <td class="hright" colspan="9" rowspan="{$reports_results.totals.rowspan|escape|default:"1"}"><strong>{#reports_th_total#|escape}</strong></td>
        {/if}
        <td class="hright t_border">
          {$total.quantity|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
        <td class="hleft t_border">
          {$total.measures_name|escape|default:"&nbsp;"}
        </td>
        {if $smarty.foreach.tot.first}
          <td class="hright t_border" rowspan="{$reports_results.totals.rowspan|escape|default:"1"}">&nbsp;</td>
          <td class="hright t_border" rowspan="{$reports_results.totals.rowspan|escape|default:"1"}">{$reports_results.totals.subtotal|number_format:4:".":" "|escape|default:"0.0000"}</td>
          <td class="hright t_border" rowspan="{$reports_results.totals.rowspan|escape|default:"1"}">{$reports_results.totals.total|number_format:4:".":" "|escape|default:"0.0000"}</td>
          <td class="hright" rowspan="{$reports_results.totals.rowspan|escape|default:"1"}">{$reports_results.totals.total_with_vat|number_format:4:".":" "|escape|default:"0.0000"}</td>
        {/if}
      </tr>
    {/foreach}
  </table>
{elseif $reports_results.type_results eq 'summarized'}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table reports_table">
    <tr class="reports_title_row reports_title_centered_middle">
      <td width="150" class="t_border"><div style="width: 150px;">{#reports_th_article_code#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 200px;">{#reports_th_article_name#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_quantity#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_measure#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_subtotal#|escape}</div></td>
      <td width="80"><div style="width: 80px;">{#reports_th_subtotal_with_vat#|escape}</div></td>
    </tr>
    {foreach from=$reports_results.results item='gt2_row'}
      <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
        <td class="hleft t_border">
          {$gt2_row.code|escape|default:"&nbsp;"}
        </td>
        <td class="hleft t_border">
          {$gt2_row.name|escape|default:"&nbsp;"}
        </td>
        <td class="hright t_border">
          {$gt2_row.quantity|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
        <td class="hleft t_border">
          {$gt2_row.article_measure_name|escape|default:"&nbsp;"}
        </td>
        <td class="hright t_border">
          {$gt2_row.subtotal|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
        <td class="hright">
          {$gt2_row.subtotal_with_vat|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
        <td class="hleft t_border error" colspan="6">
          {#reports_no_results#|escape}
        </td>
      </tr>
    {/foreach}
  </table>
{elseif $reports_results.type_results eq 'offices'}
  {math equation='a+5' a=$reports_results.articles_included|@count assign='colspan_total'}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table reports_table">
    <tr class="reports_title_row reports_title_centered_middle">
      <td width="150" class="t_border"><div style="width: 150px;">{#reports_th_office#|escape}</div></td>
      {foreach from=$reports_results.articles_included item='article_name' key='art_id'}
        <td width="100" class="t_border"><div style="width: 100px;">{$article_name|escape}</div></td>
      {/foreach}
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_total_area#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_others_total#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_th_subtotal#|escape}</div></td>
      <td width="80"><div style="width: 80px;">{#reports_th_subtotal_with_vat#|escape}</div></td>
    </tr>
    {foreach from=$reports_results.results item='office'}
      <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
        <td class="hleft t_border">
          {$office.name|escape|default:"&nbsp;"}
        </td>
        {foreach from=$reports_results.articles_included item='article_name' key='art_id'}
          <td class="hright t_border">
            {$office.articles[$art_id]|number_format:4:".":" "|escape|default:"0.0000"}
          </td>
        {/foreach}
        <td class="hright t_border">
          {$office.total_area|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
        <td class="hright t_border">
          {$office.total_other|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
        <td class="hright t_border">
          {$office.total_wo_vat|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
        <td class="hright t_border">
          {$office.total_w_vat|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
        <td class="hleft t_border error" colspan="{$colspan_total}">
          {#reports_no_results#|escape}
        </td>
      </tr>
    {/foreach}
    <tr class="row_blue">
      <td class="hright t_border"><strong>{#reports_th_total#|escape}</strong></td>
      {foreach from=$reports_results.totals item='total' name='tot'}
        <td class="hright{if !$smarty.foreach.tot.last} t_border{/if}">
          {$total|number_format:4:".":" "|escape|default:"0.0000"}
        </td>
      {/foreach}
    </tr>
  </table>
{/if}
