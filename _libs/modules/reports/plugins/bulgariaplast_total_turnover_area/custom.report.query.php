<?php

Class Bulgariaplast_Total_Turnover_Area Extends Reports {

    public static function buildQuery(&$registry, $filters = array()) {
        // Prepare the array for the final results
        $final_results = array();

        // Get the report settings in array var (for easier use)
        $documents_types_included = array_filter(preg_split('/\s*,\s*/', DOCUMENTS_TYPES_INCLUDED));
        $articles_types_included = array_filter(preg_split('/\s*,\s*/', ARTICLES_TYPES_INCLUDED));
        $customers_types_included = array_filter(preg_split('/\s*,\s*/', CUSTOMERS_TYPES_INCLUDED));
        $documents_substatuses_exclude = array_filter(preg_split('/\s*,\s*/', DOCUMENTS_SUBSTATUSES_EXCLUDE));
        $articles_included = array();

        // get the nomenclatures and their categories
        $sql = 'SELECT nc.parent_id as nom_id' . "\n" .
               'FROM ' . DB_TABLE_NOM_CATS . ' as nc' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
               ' ON (n.id=nc.parent_id AND n.active="1" AND n.deleted_by="0" AND n.type IN ("' . implode('","', $articles_types_included) . '"))' . "\n" .
               'WHERE nc.model="Nomenclature"' . (!empty($filters['category']) ? ' AND nc.cat_id="' . $filters['category'] . '"' . "\n" : '');
        $articles_ids = $registry['db']->GetCol($sql);

        // get the customers
        $sql = 'SELECT c.id' . "\n" .
               'FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
               'WHERE c.active=1 AND c.deleted_by=0 AND c.subtype="normal" AND c.type IN ("' . implode('","', $customers_types_included) . '")';
        $customers_ids = $registry['db']->GetCol($sql);

        if (!empty($filters['article'])) {
            $articles_ids = array_intersect($articles_ids, array($filters['article']));
        }
        if (!empty($filters['customer'])) {
            $customers_ids = array_intersect($customers_ids, array($filters['customer']));
        }

        $sql = [
            'select' => '',
            'from'   => '',
            'where'  => '',
            'sort'   => ''
        ];

        $sql['select'] = 'SELECT d.id as doc_id, d.full_num, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ' . "\n" .
                         '       d.date, d.deadline, d.employee, CONCAT(еi18n.name, " ", еi18n.lastname) as employee_name, ' . "\n" .
                         '       d.office, oi18n.name as office_name, gt2.*, gti18n.*' . "\n";

        $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS еi18n' . "\n" .
                       '  ON (еi18n.parent_id=d.employee AND еi18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                       '  ON (oi18n.parent_id=d.office AND oi18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                       '  ON (gt2.model_id=d.id AND gt2.model="Document")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gti18n' . "\n" .
                       '  ON (gti18n.parent_id=gt2.id AND gti18n.lang="' . $registry['lang'] . '")' . "\n";

        $where = array();
        $where[] = 'd.active=1';
        $where[] = 'd.deleted_by=0';
        $where[] = 'gt2.article_id IN ("' . (!empty($articles_ids) ? implode('","', $articles_ids) : '0') . '")';
        $where[] = 'd.customer IN ("' . (!empty($customers_ids) ? implode('","', $customers_ids) : '0') . '")';
        if (!empty($filters['documents_types'])) {
            $documents_types_included = array_intersect($documents_types_included, array($filters['documents_types']));
        }
        if (!empty($documents_substatuses_exclude)) {
            $where[] = 'd.substatus NOT IN ("' . implode('","', $documents_substatuses_exclude) . '")';
        }
        $where[] = 'd.type IN ("' . implode('","', $documents_types_included) . '")';
        if (!empty($filters['period_from'])) {
            $where[] = 'd.date>="' . $filters['period_from'] . '"';
        }
        if (!empty($filters['period_to'])) {
            $where[] = 'd.date<="' . $filters['period_to'] . '"';
        }
        if (!empty($filters['deadline_from'])) {
            $where[] = 'DATE_FORMAT(d.deadline, "%Y-%m-%d")>="' . $filters['deadline_from'] . '"';
        }
        if (!empty($filters['deadline_to'])) {
            $where[] = 'DATE_FORMAT(d.deadline, "%Y-%m-%d")<="' . $filters['deadline_to'] . '"';
        }
        $offices = array_filter($filters['office']);
        if (!empty($offices)) {
            $where[] = 'd.office IN ("' . implode('","', $offices) . '")';
        }
        $employees = array_filter($filters['employee']);
        if (!empty($employees)) {
            $where[] = 'd.employee IN ("' . implode('","', $employees) . '")';
        }
        $order_nums = array_filter($filters['order_num']);
        if (!empty($order_nums)) {
            $where[] = 'd.full_num IN ("' . implode('","', $order_nums) . '")';
        }
        if (!empty($filters['delivery'])) {
            $sql_for_add_vars = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . "\n" .
                                'WHERE `model`="Document" AND `model_type` IN ("' . implode('","', $documents_types_included) . '") AND `name`="' . DOCUMENT_VAR_DELIVERY. '"';
            $add_var_ids = $registry['db']->GetCol($sql_for_add_vars);

            $sql['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_del' . "\n" .
                            ' ON (dcstm_del.model_id=d.id AND dcstm_del.var_id IN (' . ($add_var_ids ? implode(',', $add_var_ids) : '""') . ') AND dcstm_del.value="' . $filters['delivery'] . '")' . "\n";
        }

        $rights = $registry['currentUser']->get('rights');
        $current_rights_where = array();
        foreach ($documents_types_included as $p_type) {
            $current_right = isset($rights['documents' . $p_type]['list']) ? $rights['documents' . $p_type]['list'] : '';

            //additional 'where' for hiding not allowed models
            if (!$registry['currentUser']->get('id') || !$current_right) {
                continue;
            }
            if ($current_right == 'all') {
                if ($registry['currentUser']->get('is_portal')) {
                    if ($registry['currentUser']->get('default_customer')) {
                        $current_rights_where[] = sprintf("((d.user_permissions like('%%,%d,%%') OR d.added_by=%d OR d.customer='%d') AND d.type='%d')",
                            $registry['currentUser']->get('id'),
                            $registry['currentUser']->get('id'),
                            $registry['currentUser']->get('default_customer'),
                            $p_type
                        );
                    } else {
                        $current_rights_where[] = "0";
                    }
                } else {
                    $current_rights_where[] = "d.type='" . $p_type . "'";
                }
            } elseif ($current_right == 'mine') {
                $current_rights_where[] = sprintf("((d.user_permissions like('%%,%d,%%') OR d.added_by=%d OR d.customer='%d') AND d.type='%d')",
                    $registry['currentUser']->get('id'),
                    $registry['currentUser']->get('id'),
                    $registry['currentUser']->get('default_customer'),
                    $p_type
                );
            } elseif ($current_right == 'group') {
                $user_groups = $registry['currentUser']->get('groups');
                $user_departments = $registry['currentUser']->get('departments');
                $current_rights_where[] = sprintf("((d.user_permissions like('%%,%d,%%') OR d.added_by=%d%s%s) AND d.type='%d')",
                    $registry['currentUser']->get('id'),
                    $registry['currentUser']->get('id'),
                    (count($user_groups)?' OR d.`group` IN ('.implode(',', $user_groups).')':''),
                    (count($user_departments)?' OR d.department in ('.implode(',', $user_departments).')':''),
                    $p_type
                );
            } elseif ($current_right == 'none') {
                $current_rights_where[] = "0";
            }
        }

        if (!empty($current_rights_where)) {
            $where[] = '(' . implode(' OR ' . "\n", $current_rights_where) . ')';
        }

        $sql['where'] = 'WHERE ' . implode(' AND ', $where) . "\n";
        $sql['sort']  = 'ORDER BY d.date DESC' . "\n";

        $query = implode("\n", array_filter($sql));
        $temp_results = $registry['db']->GetAll($query);

        $results = array();
        $measures = Dropdown::getMeasures(array($registry));
        $measures = array_combine(array_column($measures, 'option_value'), array_column($measures, 'label'));

        $totals = [];
        if ($filters['results'] == 'detailed') {
            $totals = array(
                'quantity'       => 0,
                'subtotal'       => 0,
                'total'          => 0,
                'total_with_vat' => 0,
                'rowspan'        => 0,
                'measures'       => array()
            );

            foreach ($temp_results as $tmp) {
                if (!isset($results[$tmp['doc_id']])) {
                    $results[$tmp['doc_id']] = array(
                        'id'             => $tmp['doc_id'],
                        'full_num'       => $tmp['full_num'],
                        'customer'       => $tmp['customer'],
                        'customer_name'  => $tmp['customer_name'],
                        'date'           => $tmp['date'],
                        'deadline'       => $tmp['deadline'],
                        'office'         => $tmp['office'],
                        'office_name'    => $tmp['office_name'],
                        'employee'       => $tmp['employee'],
                        'employee_name'  => $tmp['employee_name'],
                        'rowspan'        => 0,
                        'rows'           => array(),
                        'total'          => 0,
                        'total_with_vat' => 0
                    );
                }

                $results[$tmp['doc_id']]['rows'][$tmp['id']] = array(
                    'article_code'         => $tmp['article_code'],
                    'article_name'         => $tmp['article_name'],
                    'article_description'  => $tmp['article_description'],
                    'quantity'             => $tmp['quantity'],
                    'article_measure_name' => $tmp['article_measure_name'] ? $measures[$tmp['article_measure_name']] : '',
                    'price'                => $tmp['price'],
                    'subtotal'             => $tmp['subtotal']
                );
                $results[$tmp['doc_id']]['total'] += $tmp['subtotal'];
                $results[$tmp['doc_id']]['total_with_vat'] += $tmp['subtotal_with_vat'];
                $results[$tmp['doc_id']]['rowspan']++;

                $totals['quantity'] += $tmp['quantity'];
                $totals['subtotal'] += $tmp['subtotal'];
                $totals['total'] += $tmp['subtotal'];
                $totals['total_with_vat'] += $tmp['subtotal_with_vat'];
                if (!$tmp['article_measure_name']) {
                    continue;
                }
                if (!isset($totals['measures'][$tmp['article_measure_name']])) {
                    $totals['measures'][intval($tmp['article_measure_name'])] = array(
                        'measures_name' => $tmp['article_measure_name'] ? $measures[$tmp['article_measure_name']] : '',
                        'quantity'      => 0
                    );
                }
                $totals['measures'][intval($tmp['article_measure_name'])]['quantity'] += $tmp['quantity'];
                $totals['rowspan'] = count($totals['measures']);
            }
        } elseif ($filters['results'] == 'summarized') {
            foreach ($temp_results as $tmp) {
                if (!$tmp['article_id']) {
                    continue;
                }
                if (!isset($results[$tmp['article_id']])) {
                    $results[$tmp['article_id']] = array(
                        'id'                => $tmp['article_id'],
                        'code'              => $tmp['article_code'],
                        'name'              => $tmp['article_name'],
                        'quantity'          => 0,
                        'article_measure_name' => $tmp['article_measure_name'] ? $measures[$tmp['article_measure_name']] : '',
                        'subtotal'          => 0,
                        'subtotal_with_vat' => 0
                    );
                }

                if (!empty($tmp['article_measure_name'])) {
                    $results[$tmp['article_id']]['article_measure_name'] = $measures[$tmp['article_measure_name']];
                }
                $results[$tmp['article_id']]['quantity'] += $tmp['quantity'];
                $results[$tmp['article_id']]['subtotal'] += $tmp['subtotal'];
                $results[$tmp['article_id']]['subtotal_with_vat'] += $tmp['subtotal_with_vat'];
            }
            uasort($results, array('self', 'sortingResultsByName'));
        } elseif ($filters['results'] == 'offices') {
            // get the articles names
            $sql = 'SELECT n.id, CONCAT(ni18n.name, " ' . General::slashesEscape($registry['translater']->translate('reports_square_meters_measure_lbl')) . '") as name' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                   '  ON (ni18n.parent_id=n.id AND ni18n.lang="' . $registry['lang'] . '" AND
                          n.active=1 AND n.deleted_by=0)' . "\n".
                   'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                   '  ON (tm.model=\'Nomenclature\' AND tm.model_id=n.id AND tm.tag_id="' . OFFICES_ARTICLES_TAG_INCLUDED . '")' . "\n" .
                   'ORDER BY n.id ASC';
            $articles_included = $registry['db']->GetAssoc($sql);

            $totals = array_fill_keys(array_keys($articles_included), 0);
            $totals = $totals + [
                'total_area'   => 0,
                'total_other'  => 0,
                'total_wo_vat' => 0,
                'total_w_vat'  => 0
            ];

            foreach ($temp_results as $tmp) {
                if (!isset($results[$tmp['office']])) {
                    $results[$tmp['office']] = array(
                        'id'           => $tmp['office'],
                        'name'         => $tmp['office_name'],
                        'articles'     => array(),
                        'total_area'   => 0,
                        'total_other'  => 0,
                        'total_wo_vat' => 0,
                        'total_w_vat'  => 0,
                    );
                    $results[$tmp['office']]['articles'] = array_fill_keys(array_keys($articles_included), 0);
                }

                $total_key = 'total_other';
                if (isset($results[$tmp['office']]['articles'][$tmp['article_id']])) {
                    $results[$tmp['office']]['articles'][$tmp['article_id']] += floatval($tmp['quantity']);
                    $totals[$tmp['article_id']] += floatval($tmp['quantity']);
                    $total_key = 'total_area';
                }
                $results[$tmp['office']][$total_key] += floatval($tmp['quantity']);
                $results[$tmp['office']]['total_wo_vat'] += floatval($tmp['subtotal']);
                $results[$tmp['office']]['total_w_vat'] += floatval($tmp['subtotal_with_vat']);
            }
            foreach ($totals as $k => $val) {
                if (!preg_match('#^total_#', $k)) {
                    continue;
                }
                $totals[$k] = array_sum(array_column($results, $k));
            }
            uasort($results, array('self', 'sortingResultsByName'));
        }

        $final_results['articles_included'] = $articles_included;
        $final_results['results'] = $results;
        $final_results['totals'] = $totals;
        $final_results['type_results'] = $filters['results'];

        return self::exitReport($registry, $filters, $final_results, true);
    }

    private static function exitReport(&$registry, $filters, $final_results, $success = false, $error = '') {
        if (!$success) {
            $final_results['additional_options']['failed'] = true;
            $final_results['additional_options']['dont_show_export_button'] = true;
            if (!empty($error)) {
                $final_results['additional_options']['error'] = $registry['translater']->translate($error);
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }
        return $results;
    }

    public static function sortingResultsByName($a, $b) {
        return ($a['name'] > $b['name']) ? 1 : -1;
    }
}

?>
