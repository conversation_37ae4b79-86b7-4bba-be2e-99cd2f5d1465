<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            if (!defined('DOCUMENT_ORDER_ID')) {
                define('DOCUMENT_ORDER_ID', 7);
            }
            if (!defined('DOCUMENT_VAT_PERCENT')) {
                define('DOCUMENT_VAT_PERCENT', 20);
            }
            if (!defined('CUSTOMERS_OWN_COMPANY_TYPE')) {
                define('CUSTOMERS_OWN_COMPANY_TYPE', 6);
            }
            if (!defined('ORDER_COMPANY')) {
                define('ORDER_COMPANY', 'customer_id');
            }
            if (!defined('ORDER_ARTICLE')) {
                define('ORDER_ARTICLE', 'order_article_id');
            }
            if (!defined('ORDER_PROCESSING_FACTORY')) {
                define('ORDER_PROCESSING_FACTORY', 'order_processing_factory');
            }
            if (!defined('ORDER_PRODUCT_NAME')) {
                define('ORDER_PRODUCT_NAME', 'order_product_name');
            }
            if (!defined('ORDER_ARTICLE_COUNT')) {
                define('ORDER_ARTICLE_COUNT', 'order_article_number');
            }
            if (!defined('ORDER_ARTICLE_HEIGHT')) {
                define('ORDER_ARTICLE_HEIGHT', 'order_article_height');
            }
            if (!defined('ORDER_ARTICLE_WIDTH')) {
                define('ORDER_ARTICLE_WIDTH', 'order_article_width');
            }
            if (!defined('ORDER_ARTICLE_QUANTITY')) {
                define('ORDER_ARTICLE_QUANTITY', 'order_article_quantity');
            }
            if (!defined('ORDER_ARTICLE_MEASURE')) {
                define('ORDER_ARTICLE_MEASURE', 'order_article_measure_name');
            }
            if (!defined('ORDER_ARTICLE_PRICE')) {
                define('ORDER_ARTICLE_PRICE', 'order_article_price');
            }
            if (!defined('ORDER_ARTICLE_TOTAL_VALUE')) {
                define('ORDER_ARTICLE_TOTAL_VALUE', 'order_total_value');
            }
            if (!defined('ORDER_REDUCTION_TOTAL')) {
                define('ORDER_REDUCTION_TOTAL', 'reduction_total');
            }
            if (!defined('CUSTOMER_DELIVERER_TYPE_ID')) {
                define('CUSTOMER_DELIVERER_TYPE_ID', 4);
            }

            if (!defined('NOMENCLATURE_ITEM_ID')) {
                define('NOMENCLATURE_ITEM_ID', 16);
            }
            if (!defined('NOMENCLATURE_STONE_ID')) {
                define('NOMENCLATURE_STONE_ID', 6);
            }
            if (!defined('NOMENCLATURE_DETERGENT_ID')) {
                define('NOMENCLATURE_DETERGENT_ID', 7);
            }
            if (!defined('NOMENCLATURE_CUTTING_ID')) {
                define('NOMENCLATURE_CUTTING_ID', 8);
            }
            if (!defined('NOMENCLATURE_TRANSPORT_ID')) {
                define('NOMENCLATURE_TRANSPORT_ID', 9);
            }
            if (!defined('NOMENCLATURE_PROCESSING_ID')) {
                define('NOMENCLATURE_PROCESSING_ID', 12);
            }
            if (!defined('NOMENCLATURE_INSTALLATION_ID')) {
                define('NOMENCLATURE_INSTALLATION_ID', 11);
            }

            if (!defined('FACTORY_PROCESSING_TYPE')) {
                define('FACTORY_PROCESSING_TYPE', 14);
            }
            if (!defined('STONE_TYPE_TYPE')) {
                define('STONE_TYPE_TYPE', 13);
            }
            if (!defined('STONE_COLOR_TYPE')) {
                define('STONE_COLOR_TYPE', 10);
            }
            if (!defined('ITEM_TYPE')) {
                define('ITEM_TYPE', 15);
            }
            if (!defined('STONE_COLOR')) {
                define('STONE_COLOR', 'color_name');
            }
            if (!defined('STONE_TYPE')) {
                define('STONE_TYPE', 'article_kind');
            }

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $options_companies = array();

            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_companies = array('model_lang' => $registry['lang'],
                                       'sanitize'   => true,
                                       'sort'       => array('CONCAT (ci18n.name, " ", ci18n.lastname)'),
                                       'where'      => array('c.deleted_by=0',
                                                             'c.type=' . CUSTOMERS_OWN_COMPANY_TYPE,
                                                             'c.active=1')
            );
            $companies = Customers::search($registry, $filters_companies);

            foreach($companies as $company) {
                $options_companies[$company->get('id')] = array(
                    'label'         => $company->get('name'),
                    'option_value'  => $company->get('id'));
            }

            $filter = array (
                'custom_id'             => 'company',
                'name'                  => 'company',
                'type'                  => 'dropdown',
                'required'              => 1,
                'label'                 => $this->i18n('reports_company'),
                'help'                  => $this->i18n('reports_company'),
                'options'               => $options_companies,
                'value'                 => ''
            );
            $filters['company'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            // DEFINE CUSTOMERS AUTOCOMPLETER
            $filter = array(
                'custom_id'         => 'customers',
                'name'              => 'customers',
                'type'              => 'autocompleter',
                'width'             => 222,
                'label'             => $this->i18n('reports_customers'),
                'help'              => $this->i18n('reports_customers'),
                'autocomplete'      => array('search' => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'customers',
                    'fill_options' => array('$customers => <id>',
                                            '$customers_autocomplete => <name> <lastname>',
                    ),
                    'filters' => array(
                        '<type>'    => CUSTOMER_DELIVERER_TYPE_ID
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['customers'] = $filter;

            //DEFINE DOCUMENT NUM FILTER
            $filter = array (
                'custom_id' => 'document_num',
                'name'      => 'document_num',
                'type'      => 'text',
                'label'     => $this->i18n('reports_document_num'),
                'help'      => $this->i18n('reports_document_num')
            );
            $filters['document_num'] = $filter;

            //DEFINE TYPE REPORT FILTER
            $nom_types_list = array(NOMENCLATURE_ITEM_ID, NOMENCLATURE_STONE_ID, NOMENCLATURE_DETERGENT_ID, NOMENCLATURE_CUTTING_ID, NOMENCLATURE_TRANSPORT_ID, NOMENCLATURE_PROCESSING_ID, NOMENCLATURE_INSTALLATION_ID);

            $options_type_report = array();
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.types.factory.php';

            $nom_types_filters = array('model_lang' => $registry['lang'],
                                       'sanitize'   => true,
                                       'sort'       => array('nti18n.name'),
                                       'where'      => array('nt.deleted=0',
                                                             'nt.active=1',
                                                             'nt.id IN (' . implode(',', $nom_types_list) . ')')
            );

            $nom_types = Nomenclatures_Types::search($registry, $nom_types_filters);
            $options_type_report[] = array (
                'label'        => '[' . mb_strtolower($this->i18n('please_select'), mb_detect_encoding($this->i18n('please_select'))) . ']',
                'option_value' => implode(',', $nom_types_list)
            );

            foreach ($nom_types as $nom_type) {
                $options_type_report[] = array(
                    'label'        => $nom_type->get('name'),
                    'option_value' => $nom_type->get('id')
                );
            }

            //prepare filters
            $filter = array (
                'custom_id'          => 'type_report',
                'name'               => 'type_report',
                'type'               => 'dropdown',
                'skip_please_select' => true,
                'label'              => $this->i18n('reports_type_report'),
                'help'               => $this->i18n('reports_type_report'),
                'options'            => $options_type_report,
            );
            $filters['type_report'] = $filter;

            // PREPARE ORDER STATUSES FILTER
            require_once (PH_MODULES_DIR . 'documents/models/documents.dropdown.php');
            $params = array(0 => $registry,
                            'model_types' => array(DOCUMENT_ORDER_ID));
            $options_statuses = Documents_Dropdown::getStatuses($params);

            $current_status = '';
            foreach ($options_statuses as $opt_key => $opt_status) {
                if (!preg_match('#substatus_#', $opt_status['option_value'])) {
                    $current_status = $opt_status['option_value'];
                } else {
                    $options_statuses[$opt_key]['option_value'] = $current_status . '|' . preg_replace('#substatus_#', '', $opt_status['option_value']);
                }
            }

            $filter = array(
                'custom_id'     => 'status',
                'name'          => 'status',
                'type'          => 'dropdown',
                'label'         => $this->i18n('reports_status'),
                'help'          => $this->i18n('reports_status'),
                'options'       => $options_statuses
            );
            $filters['status'] = $filter;

            //DEFINE NOMENCLATURES' FILTER
            $filter = array (
                'custom_id'             => 'nomenclature',
                'name'                  => 'nomenclature',
                'type'                  => 'autocompleter',
                'autocomplete_type'     => 'nomenclatures',
                'autocomplete_buttons'  => 'clear',
                'autocomplete'      => array(
                    'type'          => 'nomenclatures',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'   => '<name> <a__processing_factory>',
                    'fill_options'  => array(
                        '$nomenclature_autocomplete => [<code>] <name>',
                        '$nomenclature_oldvalue => [<code>] <name>',
                        '$nomenclature => <id>'
                    ),
                    'filters'       => array(
                        '<type>' => '$type_report'
                    ),
                    'clear'         => 1,
                    'buttons_hide'  => 'search',
                ),
                'label'                 => $this->i18n('reports_nomenclature'),
                'help'                  => $this->i18n('reports_nomenclature')
            );
            $filters['nomenclature'] = $filter;

            //DEFINE NOMENCLATURE NAME FILTER
            $filter = array (
                'custom_id'         => 'nomenclature_name',
                'name'              => 'nomenclature_name',
                'type'              => 'text',
                'label'             => $this->i18n('reports_nomenclature_name'),
                'help'              => $this->i18n('reports_nomenclature_name'),
                'value'             => ''
            );
            $filters['nomenclature_name'] = $filter;

            //DEFINE FACTORY PROCESSING FILTER
            $options_factory_processing = array();

            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters_nomenclatures = array('model_lang' => $registry['lang'],
                                           'sanitize'   => true,
                                           'sort'       => array('ni18n.name'),
                                           'where'      => array('n.deleted=0',
                                                                 'n.active=1',
                                                                 'n.type=' . FACTORY_PROCESSING_TYPE)
            );
            $factory_processing_nomenclatures = Nomenclatures::search($registry, $filters_nomenclatures);

            foreach($factory_processing_nomenclatures as $nom) {
                $options_factory_processing[] = array(
                    'label'         => $nom->get('name'),
                    'option_value'  => $nom->get('id'));
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'factory_processing',
                'name'      => 'factory_processing',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_factory_processing'),
                'help'      => $this->i18n('reports_factory_processing_help'),
                'options'   => $options_factory_processing,
            );
            $filters['factory_processing'] = $filter;

            //DEFINE ITEM FILTER
            $options_item = array();

            $filter_items = array('model_lang' => $registry['lang'],
                                  'sanitize'   => true,
                                  'sort'       => array('ni18n.name'),
                                  'where'      => array('n.deleted=0',
                                                        'n.active=1',
                                                        'n.type=' . ITEM_TYPE)
            );
            $item_nomenclatures = Nomenclatures::search($registry, $filter_items);

            foreach($item_nomenclatures as $nom) {
                $options_item[] = array(
                    'label'         => $nom->get('name'),
                    'option_value'  => $nom->get('id'));
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'item',
                'name'      => 'item',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_item'),
                'help'      => $this->i18n('reports_item_help'),
                'options'   => $options_item,
            );
            $filters['item'] = $filter;

            //DEFINE STONE TYPE FILTER
            $options_stone_type = array();

            $filter_stone_type = array('model_lang' => $registry['lang'],
                                       'sanitize'   => true,
                                       'sort'       => array('ni18n.name'),
                                       'where'      => array('n.deleted=0',
                                                             'n.active=1',
                                                             'n.type=' . STONE_TYPE_TYPE)
            );
            $stone_type_nomenclatures = Nomenclatures::search($registry, $filter_stone_type);

            foreach($stone_type_nomenclatures as $nom) {
                $options_stone_type[] = array(
                    'label'         => $nom->get('name'),
                    'option_value'  => $nom->get('id'));
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'stone_type',
                'name'      => 'stone_type',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_stone_type'),
                'help'      => $this->i18n('reports_stone_type_help'),
                'options'   => $options_stone_type,
            );
            $filters['stone_type'] = $filter;

            //DEFINE STONE COLOR FILTER
            $options_stone_color = array();

            $filter_stone_color = array('model_lang' => $registry['lang'],
                                       'sanitize'   => true,
                                       'sort'       => array('ni18n.name'),
                                       'where'      => array('n.deleted=0',
                                                             'n.active=1',
                                                             'n.type=' . STONE_COLOR_TYPE)
            );
            $stone_color_nomenclatures = Nomenclatures::search($registry, $filter_stone_color);

            foreach($stone_color_nomenclatures as $nom) {
                $options_stone_color[] = array(
                    'label'         => $nom->get('name'),
                    'option_value'  => $nom->get('id'));
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'stone_color',
                'name'      => 'stone_color',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_stone_color'),
                'help'      => $this->i18n('reports_stone_color_help'),
                'options'   => $options_stone_color,
            );
            $filters['stone_color'] = $filter;

            return $filters;
        }
    }
?>