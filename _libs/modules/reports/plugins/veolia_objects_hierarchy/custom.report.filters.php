<?php

    class Custom_Report_Filters extends Report_Filters {

        private static $reg;
        private static $filters;

        /**
         * Defining filters for the certain type report
         */
        public function defineFilters(&$registry) {
            $filters = array();
            self::$reg = &$registry;

            $this->prepareEj2($registry);

            // -- DEFINE FILTERS -- //
            // STATION filter
            $filters['station'] = array(
                'custom_id'            => 'station',
                'name'                 => 'station',
                'type'                 => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width'                => '222',
                'autocomplete'         => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'  => '<name>',
                    'fill_options' => array(
                        '$station              => <id>',
                        '$station_autocomplete => <name>',
                        '$station_oldvalue     => <name>'
                    ),
                    'filters' => array(
                        '<type>' => strval(NOM_TYPE_STATION),
                        '<deleted_by>' => '0',
                        '<active>'     => '1'
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_station'),
                'help'  => $this->i18n('reports_station')
            );

            // BUILDING filter
            $filters['building'] = array(
                'custom_id'            => 'building',
                'name'                 => 'building',
                'type'                 => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width'                => '222',
                'autocomplete'         => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'  => '<name>',
                    'fill_options' => array(
                        '$building              => <id>',
                        '$building_autocomplete => <name>',
                        '$building_oldvalue     => <name>'
                    ),
                    'filters' => array(
                        '<type>' => strval(NOM_TYPE_BUILDING),
                        '<deleted_by>' => '0',
                        '<active>'     => '1'
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_building'),
                'help'  => $this->i18n('reports_building')
            );

            // ENTRANCE filter
            $filters['entrance'] = array(
                'custom_id'            => 'entrance',
                'name'                 => 'entrance',
                'type'                 => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width'                => '222',
                'autocomplete'         => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'  => '[<a_building>] <name>',
                    'fill_options' => array(
                        '$entrance              => <id>',
                        '$entrance_autocomplete => [<a_building>] <name>',
                        '$entrance_oldvalue     => [<a_building>] <name>'
                    ),
                    'filters' => array(
                        '<type>' => strval(NOM_TYPE_ENTRANCE),
                        '<deleted_by>' => '0',
                        '<active>'     => '1'
                    ),
                    'search'       => ['<a__building>', '<name>'],
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_entrance'),
                'help'  => $this->i18n('reports_entrance')
            );

            // OBJECT filter
            $filters['object'] = array(
                'custom_id'            => 'object',
                'name'                 => 'object',
                'type'                 => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width'                => '222',
                'autocomplete'         => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'  => '[<a_lot>] <a_building>, <a_entrance>, <name>',
                    'search'       => ['<a__lot>', '<name>',  '<a__building>', '<a__entrance>'],
                    'fill_options' => array(
                        '$object              => <id>',
                        '$object_autocomplete => [<a_lot>] <a_building>, <a_entrance>, <name>',
                        '$object_oldvalue     => [<a_lot>] <a_building>, <a_entrance>, <name>'
                    ),
                    'filters' => array(
                        '<type>' => strval(NOM_TYPE_OBJECT),
                        '<deleted_by>' => '0',
                        '<active>'     => '1'
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_object'),
                'help'  => $this->i18n('reports_object')
            );

            // CONTACT filter
            $filters['contact'] = array(
                'custom_id' => 'contact',
                'name' => 'contact',
                'type' => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width' => '222',
                'autocomplete' => array(
                    'type' => 'customers',
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions' => '<name> <lastname>',
                    'fill_options' => array(
                        '$contact               => <id>',
                        '$contact_name          => <name> <lastname>',
                        '$contact_autocomplete  => <name> <lastname>',
                        '$contact_oldvalue      => <name> <lastname>',
                    ),
                    'filters' => array(
                        '<type>' => 'IN (' . CONTACT_TYPES . ')'
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_contact'),
                'help' => $this->i18n('reports_contact')
            );

            // BATCH filter
            $filters['lot'] = array(
                'custom_id'            => 'lot',
                'name'                 => 'lot',
                'type'                 => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width'                => '222',
                'autocomplete'         => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'  => '<name>',
                    'fill_options' => array(
                        '$lot              => <id>',
                        '$lot_autocomplete => <name>',
                        '$lot_oldvalue     => <name>'
                    ),
                    'filters' => array(
                        '<type>' => strval(NOM_TYPE_LOT),
                        '<deleted_by>' => '0',
                        '<active>'     => '1'
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_lbl_lot'),
                'help'  => $this->i18n('reports_lbl_lot')
            );

            self::$filters = &$filters;
            return $filters;
        }

        public function processDependentFilters($filters) {
            $unset_filters = array();

            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter'])) {
                    if (isset($filters[$filter['additional_filter']])) {
                        $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                        $unset_filters[] = $filter['additional_filter'];
                    }
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }

    }

?>
