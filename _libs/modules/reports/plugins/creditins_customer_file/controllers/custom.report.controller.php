<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'ajax_redirect_to_customer_proceedings':
                $this->_redirectToCustomerProceedings();
                break;
            case 'ajax_save_customer_note':
                $this->_saveCustomerNote();
                break;
            case 'ajax_advance_loan_cover':
                $this->_advanceLoanCover();
                break;
            case 'ajax_advance_loan_cover_calculate':
                $this->_advanceLoanCoverCalculate();
                break;
            case 'ajax_fully_cover_loan_contract':
                $this->_fullyCoverLoanContract();
                break;
            case 'ajax_activate_extend_contract_with_one_period':
                $this->_activateExtendContractWithOnePeriod();
                break;
            case 'ajax_extend_contract_with_one_period':
                $this->_ajaxExtendContractWithOnePeriod();
                break;
            case 'ajax_activate_edit_contract':
                $this->_activateEditContract();
                break;
            case 'ajax_validate_edit_contract':
                $this->_validateEditContract();
                break;
            case 'ajax_edit_contract':
                $this->_editContract();
                break;
            case 'ajax_annul_contract':
                $this->_annulContract();
                break;
            case 'search_related_persons':
                $this->_searchRelatedPersons();
                break;
            case 'ajax_show_paid_repayment_plan_gt2':
                $this->_showPaidRepaymentPlanGT2();
                break;
            case 'ajax_show_ckr_noi_add_panel':
                $this->_showCkrNoiAddPanel();
                break;
            case 'ajax_save_ckr_noi_data':
                $this->_saveCkrNoiData();
                break;
            case 'ajax_submit_ckr_noi_file':
                $this->_submitCkrNoiFile();
                break;
            case 'ajax_load_sms_panel':
                $this->_loadSMSPanel();
                break;
            default:
                parent::execute();
                break;
        }
    }

    /*
     * Function to create debit note in the selected invoice
     */
    public function _redirectToCustomerProceedings() {
        $this->prepareReportSettings();

        $search_data = $this->registry['request']->get('search_data');
        $search_type = $this->registry['request']->get('search_type');

        $redirect_url = '';

        // get the infomration for the customer
        $sql = 'SELECT TRIM(CONCAT("[", c.code, "] ", CONCAT(ci18n.name, " ", ci18n.lastname)))' . "\n" .
               'FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
               'LEFT JOIN '. DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
               '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE c.id="' . $this->registry['request']->get('customer_id') . '"' . "\n";
        $customer_name = $this->registry['db']->GetOne($sql);

        $search_filters = array(
            'hidden_type'      => PROJECT_PROCEEDING_TYPE,
            'search_fields'    => array(),
            'compare_options'  => array(),
            'values'           => array(),
            'logical_operator' => array(),
            'date_period'      => array(),
            'sort'             => array('p.added'),
            'order'            => array('DESC'),
            'display'          => 10
        );

        $search_filters['search_fields'][0] = 'p.type';
        $search_filters['compare_options'][0] = "= '%s'";
        $search_filters['values'][0] = PROJECT_PROCEEDING_TYPE;
        $search_filters['logical_operator'][0] = 'AND';
        $search_filters['date_period'][0] = '';

        $search_filters['search_fields'][1] = 'p.customer';
        $search_filters['compare_options'][1] = "= '%s'";
        $search_filters['values'][1] = $this->registry['request']->get('customer_id');
        $search_filters['logical_operator'][1] = 'AND';
        $search_filters['date_period'][1] = '';
        $search_filters['values_autocomplete'][1] = $customer_name;

        $this->registry['session']->set('search_project', $search_filters, '', true);
        $redirect_url = sprintf('%s://%s%sindex.php?%s=projects&projects=search',
                                (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param']);

        echo($redirect_url);
        exit;
    }

    /*
     * Function to save customer note entered in the report
     */
    public function _saveCustomerNote() {
        $this->prepareReportSettings();

        $message = '';
        $request = $this->registry['request'];

        $request->get('credit_ins_customer_note');

        // get the model
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.history.php';

        // complete the additional data
        $filters = array('where' => array('c.id="' . $request->get('customer_id') . '"'));
        $customer = Customers::searchOne($this->registry, $filters);

        if ($customer->get('notes') != $request->get('credit_ins_customer_note')) {
            $old_customer = clone $customer;

            $customer->set('notes', $request->get('credit_ins_customer_note'), true);

            $this->registry['db']->StartTrans();
            if ($customer->save()) {
                // write history
                $new_customer = Customers::searchOne($this->registry, $filters);
                Customers_History::saveData($this->registry, array('model' => $new_customer, 'action_type' => 'edit', 'new_model' => $new_customer, 'old_model' => $old_customer));

                $message = $this->i18n('reports_customer_note_edit_successfully');
            } else {
                $message = $this->i18n('error_reports_customer_note_edit_failed');
                $this->registry['db']->FailTrans();
            }
            $this->registry['db']->CompleteTrans();
        } else {
            $message = $this->i18n('reports_customer_note_edit_successfully');
        }

        echo($message);
        return true;
    }

    /*
     * Function actvate the form for advance loan cover
     */
    public function _advanceLoanCover() {
        $this->prepareReportSettings();

        // prepare viewer for the user to select the required container
        // build the viewer
        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);

        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_advance_loan_cover.html';

        $viewer->data['repayment_plan_id'] = $this->registry['request']->get('contract_id');

        $operation_result = array(
            'template' => array(),
        );
        $operation_result['template']['content'] = $viewer->fetch();
        $operation_result['template']['title'] = $this->i18n('reports_repayment_plan_advanced_full_cover');

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function actvate the form for advance loan cover
     */
    public function _advanceLoanCoverCalculate() {
        $this->prepareReportSettings();

        $operation_result = array(
            'content' => '',
            'title'   => ''
        );

        $changed_gt2_table = $this->advancedLoanCoverRecalculate($this->registry['request']->get('repayment_plan_id'), $this->registry['request']->get('advance_pay_date'));
        $sums_to_pay = $changed_gt2_table['sums_to_pay'];

        // prepare viewer for the user to select the required container
        // build the viewer
        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);

        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_advance_loan_cover_sums.html';
        $viewer->data['sums_to_pay'] = $sums_to_pay;
        $viewer->data['repayment_date'] = $this->registry['request']->get('advance_pay_date');
        $viewer->data['repayment_plan'] = $this->registry['request']->get('repayment_plan_id');

        $operation_result['content'] = $viewer->fetch();
        $operation_result['title'] = $this->i18n('reports_repayment_plan_total_sums');

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to fully cover a loan contract based on certain date
     */
    public function _fullyCoverLoanContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'result'                   => true,
            'message'                  => '',
            'template_repayment_plans' => '',
            'template_credits_list'    => '',
            'template_all_credits_list'=> ''
        );


        $changed_data = $this->advancedLoanCoverRecalculate($this->registry['request']->get('contract_id'), $this->registry['request']->get('repayment_date'));

        // make the changes for the document
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('contract_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $document->getVars();
        $document->getGT2Vars();
        $old_document = clone $document;

        $assoc_vars = $document->getAssocVars();

        // start the transaction
        $this->registry['db']->StartTrans();

        // get the new sum of the incomes reason
        $new_total = 0;
        foreach ($changed_data['gt2']['values'] as $row_id => $row_values) {
            $new_total += $row_values['article_second_code'];
        }

        $issue_correction_result = $this->issueReasonCorrection($document->get('id'), $new_total);
        if ($issue_correction_result['result']) {
            // update the GT2 table
            $document->set('grouping_table_2', $changed_data['gt2'], true);
            $document->calculateGT2();
            $document->set('table_values_are_set', true, true);

            if ($document->saveGT2Vars()) {
                // write the date for the repayment
                $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) VALUES ' . "\n" .
                         sprintf('("%d", "%d", "1", "%s", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EARLY_REPAYMENT_DATE]['id'], $this->registry['request']->get('repayment_date'), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EARLY_REPAYMENT_DATE]['multilang'] ? $this->registry['lang'] : '')) . "\n" .
                         'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $this->registry['db']->Execute($query);

                // write history
                $new_document = Documents::searchOne($this->registry, $doc_filters);
                $new_document->getVars();

                $history_params = array(
                    'model'       => $document,
                    'action_type' => 'edit',
                    'new_model'   => $new_document,
                    'old_model'   => $old_document
                );

                Documents_History::saveData($this->registry, $history_params);
            } else {
                // error occured
                $operation_result['result'] = false;
                $operation_result['message'] = $this->i18n('error_repayment_plan_edit_contract_failed');
                $this->registry['db']->FailTrans();
            }
        } else {
            $operation_result['result'] = false;
            $operation_result['message'] = $this->i18n('error_repayment_plan_issue_correction_failed') . "\n\n" . implode("\n", $issue_correction_result['messages']);
            $this->registry['db']->FailTrans();
        }

        // complete the transaction
        $this->registry['db']->CompleteTrans();

        $this->registry->set('get_old_vars', $get_old_vars, true);

        if ($operation_result['result']) {
            $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_successfully');

            // get the data which have to refreshed
            require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

            // prepare the table which have to be reloaded
            $repayment_plans = Creditins_Customer_File::getRepaymentPlans($this->registry, array('customer' => $document->get('customer')));
            $credits_list = Creditins_Customer_File::getCredits($this->registry, $document->get('customer'));
            $all_credits_list = Creditins_Customer_File::getAllClientCredits($this->registry, $document->get('customer'));

            $viewer = new Viewer($this->registry);
            $viewer->loadCustomI18NFiles($this->report_lang_file);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $viewer->template = '_repayment_plans.html';
            $viewer->data['repayment_plans'] = $repayment_plans;
            $operation_result['template_repayment_plans'] = $viewer->fetch();

            $crd_viewer = new Viewer($this->registry);
            $crd_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crd_viewer->setFrameset('frameset_blank.html');
            $crd_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crd_viewer->template = '_credits_list.html';
            $crd_viewer->data['credits_list'] = $credits_list;
            $operation_result['template_credits_list'] = $crd_viewer->fetch();

            $crdall_viewer = new Viewer($this->registry);
            $crdall_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crdall_viewer->setFrameset('frameset_blank.html');
            $crdall_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crdall_viewer->template = '_customers_contract.html';
            $crdall_viewer->data['customers_contract'] = $all_credits_list;
            $operation_result['template_all_credits_list'] = $crdall_viewer->fetch();
        }

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to activate the panel for extending the contract with one period
     */
    public function _activateExtendContractWithOnePeriod() {
        $this->prepareReportSettings();

        $operation_result = array(
            'result'  => true,
            'message' => '',
            'content' => '',
            'title'   => ''
        );

        // Include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('contract_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $document->getVars();
        $old_document = clone $document;

        $gt2_var = $document->getGT2Vars();

        $payments_options = array();

        // check for rows which have no principal paid (discount_value = 0)
        $rows_counter = 0;
        foreach ($gt2_var['values'] as $row_id => $values) {

            $rows_counter++;
            if ($values['discount_value'] == 0 && $values['price'] > 0) {
                $payments_options[] = array(
                    'label'        => sprintf($this->i18n('reports_repayment_plan_option_extend_payment_label'), $rows_counter, General::strftime('%d.%m.%Y', $values['article_code'])),
                    'option_value' => $row_id
                );
            }
        }

        if (empty($payments_options)) {
            $operation_result['result'] = false;
            $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_no_rows_to_extend');
        } else {
            $viewer = new Viewer($this->registry);
            $viewer->loadCustomI18NFiles($this->report_lang_file);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $viewer->template = '_extend_payment.html';
            $viewer->data['payments_options'] = $payments_options;
            $viewer->data['contract_id'] = $this->registry['request']->get('contract_id');

            $operation_result['content'] = $viewer->fetch();
            $operation_result['title'] = $this->i18n('reports_repayment_plan_extend_with_one_period_label');
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to add an additional, extra row to the repayment plan
     */
    public function _ajaxExtendContractWithOnePeriod() {
        $this->prepareReportSettings();

        $operation_result = array(
            'result'                   => true,
            'message'                  => '',
            'template_repayment_plans' => '',
            'template_credits_list'    => '',
            'template_all_credits_list'=> ''
        );
        $messages = array();

        // Include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

        $extended_payment = $this->registry['request']->get('extended_payment');

        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('repayment_plan_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $document->getVars();
        $assoc_vars = $document->getAssocVars();
        $old_document = clone $document;

        $gt2_var = $document->getGT2Vars();

        // get the row to extend
        $new_row = array();
        $last_row = array();
        $new_total = 0;
        $current_row_num = 0;
        $extended_installment_interest = 0;
        $extended_installment_warranty = 0;
        foreach($gt2_var['values'] as $row_id => $row_values) {
            $current_row_num++;
            if ($extended_payment == $row_id) {
                $new_row = $gt2_var['values'][$row_id];
                $extended_installment_interest = $gt2_var['values'][$row_id]['quantity'];
                $extended_installment_warranty = $gt2_var['values'][$row_id]['article_trademark'];
                $gt2_var['values'][$row_id]['article_second_code'] = sprintf('%.2f', ($gt2_var['values'][$row_id]['article_second_code'] - $gt2_var['values'][$row_id]['price']));
                $gt2_var['values'][$row_id]['free_field5'] = sprintf('%.2f', ($gt2_var['values'][$row_id]['free_field5'] - $gt2_var['values'][$row_id]['price']));
                $gt2_var['values'][$row_id]['price'] = '0.00';
                $gt2_var['values'][$row_id]['average_weighted_delivery_price'] = '0.00';
            }
            $new_total += $gt2_var['values'][$row_id]['article_second_code'];
        }
        $last_row = end($gt2_var['values']);

        // process the data of the row
        $new_row['article_delivery_code'] = '0.00';
        $new_row['discount_value'] = '0.00';
        $new_row['article_barcode'] = '0.00';
        $new_row['article_height'] = '0.00';
        $new_row['article_width'] = '0.00';
        $new_row['article_weight'] = '0.00';
        $new_row['article_volume'] = '0.00';
        $new_row['average_weighted_delivery_price'] = $new_row['price'];
        $new_row['free_field1'] = sprintf('%.2f', $new_row['quantity']);
        $new_row['free_field2'] = sprintf('%.2f', $new_row['article_trademark']);
        $new_row['free_field3'] = sprintf('%.2f', $new_row['last_delivery_price']);
        $new_row['free_field4'] = '0.00';
        $new_row['article_second_code'] = $new_row['free_field5'] = sprintf('%.2f', ($new_row['price'] + $new_row['last_delivery_price']));
        $new_row['article_code'] = General::strftime('%Y-%m-%d', strtotime('+1 month', strtotime($last_row['article_code'])));
        // flag for extended row
        $new_row['article_alternative_deliverer'] = $new_row['article_id'];
        $new_total += $new_row['free_field5'];

        $gt2_var['values'][] = $new_row;

        $document->set('grouping_table_2', $gt2_var, true);
        $document->calculateGT2();
        $document->set('table_values_are_set', true, true);

        // start the transaction
        $this->registry['db']->StartTrans();

        // Update the repayment period
        if (isset($assoc_vars[DOCUMENT_CONTRACT_DEADLINE])) {
            // check the lang of the var
            $query = 'SELECT `lang` FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' WHERE `model_id`="' . $document->get('id') . '" AND `var_id`="' . $assoc_vars[DOCUMENT_CONTRACT_DEADLINE]['id'] . '"';
            $var_lang = $this->registry['db']->GetOne($query);

            $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) VALUES ' . "\n" .
                     sprintf('("%d", "%d", "1", "%s", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_DEADLINE]['id'], count($gt2_var['values']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), $var_lang) . "\n" .
                     'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($query);
        }

        if ($document->saveGT2Vars()) {
            // update the table for extended installments
            if (isset($assoc_vars[DOCUMENT_CONTRACT_EXTENDED_INSTALLMENT])) {
                $last_completed_row = 0;

                // find the row which have to be updated
                foreach ($assoc_vars[DOCUMENT_CONTRACT_EXTENDED_INSTALLMENT]['value'] as $row => $row_value) {
                    if (!$row_value) {
                        break;
                    } else {
                        $last_completed_row = $row;
                    }
                }

                // add 1 to define the new row which will be added
                $last_completed_row++;

                // prepare all the queries
                $queries = array();
                $queries[] = sprintf('("%d", "%d", "%d", NOW(), NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EXTENSION_DATE]['id'], $last_completed_row, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EXTENSION_DATE]['multilang'] ? $this->registry['lang'] : ''));
                $queries[] = sprintf('("%d", "%d", "%d", "%d", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EXTENSION_USER]['id'], $last_completed_row, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EXTENSION_USER]['multilang'] ? $this->registry['lang'] : ''));
                $queries[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EXTENSION_USER_NAME]['id'], $last_completed_row, ($this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname')), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EXTENSION_USER_NAME]['multilang'] ? $this->registry['lang'] : ''));
                $queries[] = sprintf('("%d", "%d", "%d", "%d", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EXTENDED_INSTALLMENT]['id'], $last_completed_row, count($gt2_var['values']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EXTENDED_INSTALLMENT]['multilang'] ? $this->registry['lang'] : ''));
                $queries[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EXTENDED_INSTALLMENT_DATE]['id'], $last_completed_row, $new_row['article_code'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EXTENDED_INSTALLMENT_DATE]['multilang'] ? $this->registry['lang'] : ''));
                $queries[] = sprintf('("%d", "%d", "%d", "%.2f", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EXTENSION_DIFFERENCE_INTEREST]['id'], $last_completed_row, $extended_installment_interest, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EXTENSION_DIFFERENCE_INTEREST]['multilang'] ? $this->registry['lang'] : ''));
                $queries[] = sprintf('("%d", "%d", "%d", "%.2f", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[DOCUMENT_CONTRACT_EXTENSION_DIFFERENCE_WARRANTY]['id'], $last_completed_row, $extended_installment_warranty, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[DOCUMENT_CONTRACT_EXTENSION_DIFFERENCE_WARRANTY]['multilang'] ? $this->registry['lang'] : ''));

                $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) VALUES ' . "\n" .
                         implode(',' . "\n", $queries) .
                         'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $this->registry['db']->Execute($query);
            }

            // write history
            $new_document = Documents::searchOne($this->registry, $doc_filters);
            $new_document->getVars();

            $history_params = array(
                'model'       => $document,
                'action_type' => 'edit',
                'new_model'   => $new_document,
                'old_model'   => $old_document
            );

            Documents_History::saveData($this->registry, $history_params);

            // get the automation
            require_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php';
            $automations_controller = new Automations_Controller($this->registry);

            // GET THE AUTOMATIONS
            $where = array();
            $where[] = 'a.module="documents"';
            $where[] = 'a.start_model_type="' . $document->get('type') . '"';
            $where[] = 'a.active = 1';
            $where[] = 'a.method LIKE "%deadline_for_repayment%"';
            $where[] = 'a.method LIKE "%method := setAdditionalVar%"';
            $query = 'SELECT a.* ' . "\n" .
                     'FROM ' . DB_TABLE_AUTOMATIONS . ' as a ' . "\n" .
                     'WHERE ' . implode(' AND ', $where). "\n" .
                     'ORDER BY a.position';
            $automation = $this->registry['db']->GetRow($query);

            $automations_controller->executeMethod($automation, $document);

            $issue_correction_result = $this->issueReasonCorrection($new_document->get('id'), $new_total);
            if (!$issue_correction_result['result']) {
                $this->registry['db']->FailTrans();
                $operation_result['result'] = false;
                $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_extend_correct_reason') . "\n\n" . implode("\n", $issue_correction_result['messages']);
            } else {
                $old_document = clone $new_document;
                $old_document->getModelTagsForAudit();
                $old_document->sanitize();

                $new_document->getTags();
                $tags = $new_document->get('tags');
                $tags[] = DOCUMENT_FULL_PAID_TAG;
                $new_document->set('tags', $tags, true);

                if ($new_document->updateTags(array('skip_permissions' => true))) {
                    // get the updated document
                    $filters = array('where'      => array('d.id="' . $new_document->get('id') . '"'),
                                     'model_lang' => $this->registry['lang']);
                    $new_document = Documents::searchOne($this->registry, $filters);
                    $new_document->getModelTagsForAudit();
                    $new_document->sanitize();

                    Documents_History::saveData($this->registry, array('model' => $new_document, 'action_type' => 'tag', 'new_model' => $new_document, 'old_model' => $old_document));
                } else {
                    $this->registry['db']->FailTrans();
                    $operation_result['result'] = false;
                    $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_extend_tag_edit');
                }

                // PREPARE AND SEND THE EMAIL
                //get email template
                require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
                $filters_mail = array('where' => array('e.id = ' . DOCUMENT_EMAIL_TEMPLATE), 'sanitize' => true);
                $mail = Emails::searchOne($this->registry, $filters_mail);

                if (!empty($mail)) {
                    // get the customer email
                    $sql = 'SELECT `email` FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id` = "' . $new_document->get('customer') . '"' . "\n";
                    $available_emails = $this->registry['db']->GetOne($sql);
                    $available_emails = explode("\n", $available_emails);

                    $email_to_send_to = '';
                    foreach ($available_emails as $cr) {
                        if (strpos($cr, '|') !== false) {
                            list($email_to_send_to) = explode("|", $cr);
                            $email_to_send_to = trim($email_to_send_to);
                        } else {
                            $email_to_send_to = $cr;
                        }
                        if (!empty($email_to_send_to)) {
                            break;
                        }
                    }

                    if (!empty($email_to_send_to)) {
                        $new_document->set('body', $mail->get('body'), true);
                        $new_document->set('email_subject', $mail->get('subject'), true);
                        $new_document->set('email_template', $mail->get('id'), true);
                        $new_document->set('add_signature', true, true);
                        $new_document->set('customer_email', $new_document->get('customer_name') . ' <' . $email_to_send_to . '>', true);
                        $new_document->set('attached_files', array('pattern_' . DOCUMENT_FILE_TEMPLATE), true);

                        if ($new_document->sanitized) {
                            $new_document->unsanitize();
                            $sa = true;
                        }

                        $send_results = $new_document->sendAsMail();
                        if (!$send_results['sent']) {
                            // ERROR
                            $messages[] = $this->i18n('error_reports_extension_email_sent_error');
                        }
                        if ($sa) {
                            $new_document->sanitize();
                        }
                    } else {
                        // ERROR
                        $messages[] = $this->i18n('error_reports_extension_email_not_sent');
                    }
                }
            }
        } else {
            // error occured
            $operation_result['result'] = false;
            $messages[] = $this->i18n('error_reports_repayment_plan_edit_extend_add_row');
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();

        $this->registry->set('get_old_vars', $get_old_vars, true);

        if ($operation_result['result']) {
            $messages[] = $this->i18n('reports_repayment_plan_edit_add_row_successfully');

            // get the data which have to refreshed
            require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

            // prepare the table which have to be reloaded
            $repayment_plans = Creditins_Customer_File::getRepaymentPlans($this->registry, array('customer' => $new_document->get('customer')));
            $credits_list = Creditins_Customer_File::getCredits($this->registry, $new_document->get('customer'));
            $all_credits_list = Creditins_Customer_File::getAllClientCredits($this->registry, $new_document->get('customer'));

            $viewer = new Viewer($this->registry);
            $viewer->loadCustomI18NFiles($this->report_lang_file);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $viewer->template = '_repayment_plans.html';
            $viewer->data['repayment_plans'] = $repayment_plans;
            $operation_result['template_repayment_plans'] = $viewer->fetch();

            $crd_viewer = new Viewer($this->registry);
            $crd_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crd_viewer->setFrameset('frameset_blank.html');
            $crd_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crd_viewer->template = '_credits_list.html';
            $crd_viewer->data['credits_list'] = $credits_list;
            $operation_result['template_credits_list'] = $crd_viewer->fetch();

            $crdall_viewer = new Viewer($this->registry);
            $crdall_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crdall_viewer->setFrameset('frameset_blank.html');
            $crdall_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crdall_viewer->template = '_customers_contract.html';
            $crdall_viewer->data['customers_contract'] = $all_credits_list;
            $operation_result['template_all_credits_list'] = $crdall_viewer->fetch();
        }

        $operation_result['message'] = implode("\n", $messages);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to prepare the gt2 table for edit and prepare the form where the table will be situated
     */
    public function _activateEditContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'result'  => true,
            'message' => '',
            'content' => '',
            'title'   => ''
        );

        // Include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';

        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('contract_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');

        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $document->sanitize();

        $document_vars = $document->get('vars');

        // goes through all of the vars to find the GT2 table
        foreach ($document_vars as $var_idx => $var) {
            if ($var['type'] == 'gt2') {
                if (!$this->registry['request']->get('annul')) {
                    $vars_to_make_editable = array('article_trademark', 'quantity', 'article_delivery_code', 'discount_value', 'article_barcode', 'article_height', 'article_weight');
                } else {
                    $vars_to_make_editable = array();
                }

                $gt2_vars = $var['vars'];
                foreach ($gt2_vars as $var_name => $var_data) {
                    if (in_array($var_name, $vars_to_make_editable)) {
                        $document_vars[$var_idx]['vars'][$var_name]['readonly'] = false;
                        $document_vars[$var_idx]['vars'][$var_name]['js_methods']['onblur'] = 'creditInsRoundEditedValues(this);' . (!empty($document_vars[$var_idx]['vars'][$var_name]['js_methods']['onblur']) ? $document_vars[$var_idx]['vars'][$var_name]['js_methods']['onblur'] : '');
                        $document_vars[$var_idx]['vars'][$var_name]['js_filter'] = 'insertOnlyReals';
                    } else {
                        $document_vars[$var_idx]['vars'][$var_name]['readonly'] = true;
                    }
                }

                $document_vars[$var_idx]['hide_delete'] = true;
                $document_vars[$var_idx]['hide_multiple_rows_buttons'] = true;
                $document->set('grouping_table_2', $document_vars[$var_idx], true);
            }
        }

        $this->registry['include_gt2'] = true;
        $document->set('vars', $document_vars, true);
        $document->getLayoutVars();

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = $viewer->theme->templatesDir;
        $viewer->template = '_gt2_edit.html';
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->data['model'] = $document;

        $template = $viewer->fetch();
        $title = '';
        if ($this->registry['request']->get('annul')) {
            $template = sprintf(
                '<form action="" method="post">%s<button type="button" class="button" style="margin-left: 5px;" onclick="creditInsAnnulContract(this, \'%d\'); return false;">%s</button></form>',
                $template,
                $document->get('id'),
                $this->i18n('reports_repayment_plan_option_annul')
            );
            $title = $this->i18n('reports_repayment_plan_annul_title');
        } else {
            $template = sprintf(
                '<form action="" method="post">%s<button type="button" class="button" style="margin-left: 5px;" onclick="creditInsEditContract(this, \'%d\'); return false;">%s</button></form>',
                $template,
                $document->get('id'),
                $this->i18n('edit')
            );
            $title = $this->i18n('reports_repayment_plan_edit_title');
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        $operation_result['content'] = $template;
        $operation_result['title'] = $title;

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to validate the edition of the contract
     */
    public function _validateEditContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'error'   => false,
            'message' => array()
        );

        // get old document
        $doc_filters = array('where'    => array('d.id = ' . $this->registry['request']->get('contract_id')),
                             'sanitize' => false);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $current_gt2_var = $document->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);
        $old_document = clone $document;

        // make empty new contract
        $request_document = new Document($this->registry);
        $request_document->set('type', $document->get('type'), true);
        $request_document->getVars();

        // find the gt2 var in the request model and get its values
        $request_gt2 = $request_document->getGT2Vars();

        $new_owed = 0;
        $new_paid_total = 0;
        foreach ($request_gt2['values'] as $row_vals) {
            $new_owed += sprintf('%.2f', floatval($row_vals['article_second_code']));
            $new_paid_total += sprintf('%.2f', floatval($row_vals['article_volume']));
        }
        $new_paid_total = sprintf('%.2f', round($new_paid_total, 2));
        $new_owed = sprintf('%.2f', round($new_owed, 2));

        require_once PH_MODULES_DIR . 'automations/plugins/creditins/controllers/creditins.automations.controller.php';
        $automations_controller = new Creditins_Automations_Controller($this->registry);
        $payments_total = $automations_controller->getPaymentsTotal($document->get('id'), INCOMES_REASON_TYPE_ID);
        $payments_total = sprintf('%.2f', round($payments_total, 2));

        if ($new_paid_total > $payments_total) {
            $operation_result['error'] = true;
            $operation_result['message'][] = sprintf($this->i18n('error_repayment_plan_edit_contract_paid_value_mismatch_more'), $payments_total);
        }

        if ($new_owed < $new_paid_total) {
            $operation_result['error'] = true;
            $operation_result['message'][] = $this->i18n('error_repayment_plan_edit_contract_paid_value_more_than_current_value');
        }

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to edit the contract
     */
    public function _editContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'error'                     => false,
            'message'                   => '',
            'template_repayment_plans'  => '',
            'template_credits_list'     => '',
            'template_all_credits_list' => ''
        );

        // include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

        // include the lang files for contracts
        $lang_files = array(PH_MODULES_DIR . 'contracts/i18n/' . $this->registry['lang'] . '/documents.ini');
        $this->loadI18NFiles($lang_files);

        // get old document
        $doc_filters = array('where'    => array('d.id = ' . $this->registry['request']->get('contract_id')),
                             'sanitize' => false);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $current_gt2_var = $document->getGT2Vars();
        $old_assoc_vars = $document->getAssocVars();
        $old_principal = sprintf('%.2f', round(floatval($old_assoc_vars[DOCUMENT_CONTRACT_CREDIT]['value']), 2));
        $this->registry->set('get_old_vars', false, true);
        $old_document = clone $document;

        // make empty new contract
        $request_document = new Document($this->registry);
        $request_document->set('type', $document->get('type'), true);
        $request_document->getVars();

        $old_paid_total = 0;
        $old_total = 0;
        foreach ($current_gt2_var['values'] as $row_vals) {
            $old_total += $row_vals['article_second_code'];
            $old_paid_total += $row_vals['article_volume'];
        }
        $old_paid_total = sprintf('%.2f', round(floatval($old_paid_total), 2));
        $old_total = sprintf('%.2f', round(floatval($old_total), 2));

        // find the gt2 var in the request model and get its values
        $request_gt2 = $request_document->getGT2Vars();
        $current_gt2_var['values'] = $request_gt2['values'];
        unset($request_gt2);

        $new_total = 0;
        $new_paid_total = 0;
        $new_principal = 0;
        foreach ($current_gt2_var['values'] as $row_id => $row_vals) {
            $new_total += $row_vals['article_second_code'];
            $new_paid_total += $row_vals['article_volume'];
            $new_principal += $row_vals['price'];
            if (round(floatval($row_vals['discount_value']), 2) > 0) {
                $current_gt2_var['values'][$row_id]['discount_surplus_field'] = 'discount_value';
            }
        }
        $new_paid_total = sprintf('%.2f', round(floatval($new_paid_total), 2));
        $new_principal = sprintf('%.2f', round(floatval($new_principal), 2));
        $new_total = sprintf('%.2f', round(floatval($new_total), 2));

        $document->set('grouping_table_2', $current_gt2_var, true);
        $document->calculateGT2();
        $document->set('table_values_are_set', true, true);

        $this->registry->set('get_old_vars', true, true);

        $this->registry['db']->StartTrans();
        if ($document->saveGT2Vars()) {
            // write history
            $new_document = Documents::searchOne($this->registry, $doc_filters);
            $new_document->getVars();

            $history_params = array(
                'model'       => $document,
                'action_type' => 'edit',
                'new_model'   => $new_document,
                'old_model'   => $old_document
            );

            Documents_History::saveData($this->registry, $history_params);

            if ($old_principal != $new_principal) {
                // check the lang of the var
                $query = 'SELECT `lang` FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' WHERE `model_id`="' . $new_document->get('id') . '" AND `var_id`="' . $old_assoc_vars[DOCUMENT_CONTRACT_CREDIT]['id'] . '"';
                $var_lang = $this->registry['db']->GetOne($query);

                // update the principal
                $insert = array();
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $new_document->get('id'), $old_assoc_vars[DOCUMENT_CONTRACT_CREDIT]['id'], 1, $new_principal, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), $var_lang);

                // perform the insert queries
                $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                       'VALUES ' . implode(",\n", $insert) . "\n" .
                       'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $this->registry['db']->Execute($sql);

                // set tags
                $tag_document = clone $new_document;
                $old_document = clone $tag_document;
                $old_document->getModelTagsForAudit();
                $old_document->sanitize();

                $tag_document->getTags();
                $tags = $tag_document->get('tags');
                $tags[] = DOCUMENT_FULL_PAID_TAG;
                $tag_document->set('tags', $tags, true);

                if ($tag_document->updateTags(array('skip_permissions' => true))) {
                    // get the updated document
                    $filters = array('where'      => array('d.id="' . $tag_document->get('id') . '"'),
                                     'model_lang' => $this->registry['lang']);
                    $tag_document = Documents::searchOne($this->registry, $filters);
                    $tag_document->getModelTagsForAudit();
                    $tag_document->getVars();
                    $tag_document->sanitize();

                    Documents_History::saveData($this->registry, array('model' => $tag_document, 'action_type' => 'tag', 'new_model' => $tag_document, 'old_model' => $old_document));
                    Documents_History::saveData($this->registry, array('model' => $tag_document, 'action_type' => 'edit', 'new_model' => $tag_document, 'old_model' => $old_document));
                } else {
                    $operation_result['error'] = true;
                    $operation_result['message'] = $this->i18n('error_reports_repayment_plan_principal_tag_status_failed');
                    $this->registry['db']->FailTrans();
                }
            }

            if (!$this->registry['db']->HasFailedTrans()) {
                // success occured
                $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_successfully');

                if ($new_total != $old_total) {
                    $issue_correction_result = $this->issueReasonCorrection($new_document->get('id'), $new_total);

                    if (!$issue_correction_result['result']) {
                        $this->registry['db']->FailTrans();
                        $operation_result['error'] = true;
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed_correction_error') . "\n\n" . implode("\n", $issue_correction_result['messages']);
                    }
                }

                if ($new_paid_total != $old_paid_total && !$operation_result['error']) {
                    $additional_sum_to_distribute = $new_paid_total - $old_paid_total;
                    $distribute_result = $this->distributeAdditionalSum($new_document->get('id'), $additional_sum_to_distribute);

                    if (!$distribute_result) {
                        $this->registry['db']->FailTrans();
                        $operation_result['error'] = true;
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed_additional_sum_distribution');
                    }
                }
            }

            if (!$this->registry['db']->HasFailedTrans()) {
                // activate the function that will change the status and the tag of the contract
                require_once PH_MODULES_DIR . 'automations/plugins/creditins/controllers/creditins.automations.controller.php';
                $automations_controller = new Creditins_Automations_Controller($this->registry);

                $params_payment_check = array(
                    'status'                      => DOCUMENT_FULL_PAID_STATUS,
                    'incomes_reason_type'         => INCOMES_REASON_TYPE_ID,
                    'full_payment_var'            => DOCUMENT_CONTRACT_REPAYMENT_DATE,
                    'repayment_status'            => DOCUMENT_CONTRACT_TYPE_REPAYMENT,
                    'repayment_status_in_advance' => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_ADVANCE,
                    'repayment_status_in_time'    => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_TIME
                );

                if (!$automations_controller->checkFullPayment($new_document, $params_payment_check)) {
                    $operation_result['error'] = true;
                    $operation_result['message'] = $this->i18n('error_reports_repayment_plan_tag_status_failed');
                    $this->registry['db']->FailTrans();
                } else {
                    // distribute payments
                    $gt2 = $new_document->getGT2Vars();

                    // recalculate grouping table and get payments
                    $result = $this->recalculateGroupingTable($new_document, $gt2, 'edit');

                    if (!$result) {
                        $this->registry['db']->FailTrans();
                        $operation_result['error'] = true;
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_redestribute_failed');
                    }
                }
            }
        } else {
            // error occured
            $operation_result['error'] = true;
            $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed');
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();

        if (!$operation_result['error']) {
            // get the data which have to refreshed
            require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

            // prepare the table which have to be reloaded
            $repayment_plans = Creditins_Customer_File::getRepaymentPlans($this->registry, array('customer' => $new_document->get('customer')));
            $credits_list = Creditins_Customer_File::getCredits($this->registry, $new_document->get('customer'));
            $all_credits_list = Creditins_Customer_File::getAllClientCredits($this->registry, $new_document->get('customer'));

            $viewer = new Viewer($this->registry);
            $viewer->loadCustomI18NFiles($this->report_lang_file);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $viewer->template = '_repayment_plans.html';
            $viewer->data['repayment_plans'] = $repayment_plans;
            $operation_result['template_repayment_plans'] = $viewer->fetch();

            $crd_viewer = new Viewer($this->registry);
            $crd_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crd_viewer->setFrameset('frameset_blank.html');
            $crd_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crd_viewer->template = '_credits_list.html';
            $crd_viewer->data['credits_list'] = $credits_list;
            $operation_result['template_credits_list'] = $crd_viewer->fetch();

            $crdall_viewer = new Viewer($this->registry);
            $crdall_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crdall_viewer->setFrameset('frameset_blank.html');
            $crdall_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crdall_viewer->template = '_customers_contract.html';
            $crdall_viewer->data['customers_contract'] = $all_credits_list;
            $operation_result['template_all_credits_list'] = $crdall_viewer->fetch();
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to ajax annul the contract
     */
    public function _annulContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'error'                     => false,
            'message'                   => '',
            'template_repayment_plans'  => '',
            'template_credits_list'     => '',
            'template_all_credits_list' => ''
        );

        // include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

        // include the lang files for contracts
        $lang_files = array(PH_MODULES_DIR . 'contracts/i18n/' . $this->registry['lang'] . '/documents.ini');
        $this->loadI18NFiles($lang_files);

        // get old document
        $doc_filters = array('where'    => array('d.id = ' . $this->registry['request']->get('contract_id')),
                             'sanitize' => false);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();

        $old_document = clone $document;

        $gt2_var = $document->getGT2Vars();

        $current_date = date('Y-m-d');
        $previous_date = $document->get('date_start');
        $new_total = 0;
        $current_payment = '';
        foreach ($gt2_var['values'] as $row_id => $row_values) {
            if ($previous_date>$current_date) {
                $gt2_var['values'][$row_id]['quantity'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['article_trademark'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['last_delivery_price'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['article_second_code'] = $row_values['price'];
            } else {
                $current_payment = $row_id;
            }
            $gt2_var['values'][$row_id]['discount_value'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_barcode'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_height'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_width'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_weight'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_volume'] = sprintf('%.2f', 0);

            $gt2_var['values'][$row_id]['average_weighted_delivery_price'] = $gt2_var['values'][$row_id]['price'];
            $gt2_var['values'][$row_id]['free_field1'] = $gt2_var['values'][$row_id]['quantity'];
            $gt2_var['values'][$row_id]['free_field2'] = $gt2_var['values'][$row_id]['article_trademark'];
            $gt2_var['values'][$row_id]['free_field3'] = $gt2_var['values'][$row_id]['last_delivery_price'];
            $gt2_var['values'][$row_id]['free_field4'] = $gt2_var['values'][$row_id]['article_delivery_code'];
            $gt2_var['values'][$row_id]['free_field5'] = $gt2_var['values'][$row_id]['article_second_code'];

            $previous_date = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($row_values['article_code'])));

            $new_total += $gt2_var['values'][$row_id]['article_second_code'];
        }

        // get the paid sum and redistribute all the sums per different values
        $sql = 'SELECT SUM(fb.paid_amount)' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fb' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=fb.paid_to AND fir.type="' . INCOMES_REASON_TYPE_ID . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS p' . "\n" .
               '  ON (fb.parent_model_name="Finance_Payment" AND p.id=fb.parent_id)' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
               '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id AND frr.link_to_model_name="Document" AND frr.link_to="' . $document->get('id') . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               '  ON (frr.link_to=d.id)' . "\n" .
               'WHERE fb.parent_model_name="Finance_Payment" AND fb.paid_to_model_name="Finance_Incomes_Reason" AND fb.paid_amount>0' . "\n";
        $amount_left = $this->registry['db']->GetOne($sql);

        $overpay = 0;
        if ($amount_left > $new_total) {
            $overpay = $amount_left - $new_total;
        }
        $new_total += $overpay;

        $order_completion = array(
            'penalty' => array(
                'paid' => 'article_weight',
                'left' => 'free_field4',
            ),
            'interest' => array(
                'paid' => 'article_barcode',
                'left' => 'free_field1',
            ),
            'warranty' => array(
                'paid' => 'article_height',
                'left' => 'free_field2',
            )
        );

        foreach ($gt2_var['values'] as $key => $vals) {
            // add the overpay sum to the warranty
            if ($key == $current_payment) {
                $gt2_var['values'][$key]['article_trademark'] = sprintf('%.2f', round($gt2_var['values'][$key]['article_trademark'] + $overpay, 2));
                $gt2_var['values'][$key]['last_delivery_price'] = sprintf('%.2f', round($gt2_var['values'][$key]['last_delivery_price'] + $overpay, 2));
                $gt2_var['values'][$key]['article_second_code'] = sprintf('%.2f', round($gt2_var['values'][$key]['article_second_code'] + $overpay, 2));
                $gt2_var['values'][$key]['free_field2'] = sprintf('%.2f', round($gt2_var['values'][$key]['free_field2'] + $overpay, 2));
                $gt2_var['values'][$key]['free_field3'] = sprintf('%.2f', round($gt2_var['values'][$key]['free_field3'] + $overpay, 2));
                $gt2_var['values'][$key]['free_field5'] = sprintf('%.2f', round($gt2_var['values'][$key]['free_field5'] + $overpay, 2));
            }

            if ($vals['average_weighted_delivery_price'] > 0 && $amount_left) {
                if ($vals['average_weighted_delivery_price'] > $amount_left) {
                    $gt2_var['values'][$key]['average_weighted_delivery_price'] = sprintf('%.2f', $gt2_var['values'][$key]['average_weighted_delivery_price'] - $amount_left);
                    $gt2_var['values'][$key]['discount_value'] = sprintf('%.2f', $gt2_var['values'][$key]['discount_value'] + $amount_left);
                    $amount_left = 0;
                } else {
                    $gt2_var['values'][$key]['average_weighted_delivery_price'] = sprintf('%.2f', 0);
                    $gt2_var['values'][$key]['discount_value'] = sprintf('%.2f', $gt2_var['values'][$key]['discount_value'] + $vals['average_weighted_delivery_price']);
                    $amount_left = $amount_left - $vals['average_weighted_delivery_price'];
                }

                // update sum fields
                $gt2_var['values'][$key]['article_width'] = sprintf('%.2f', $gt2_var['values'][$key]['article_barcode'] + $gt2_var['values'][$key]['article_height']);
                $gt2_var['values'][$key]['article_volume'] = sprintf('%.2f', $gt2_var['values'][$key]['discount_value'] + $gt2_var['values'][$key]['article_width'] + $gt2_var['values'][$key]['article_weight']);
                $gt2_var['values'][$key]['free_field3'] = sprintf('%.2f', $gt2_var['values'][$key]['free_field1'] + $gt2_var['values'][$key]['free_field2']);
                $gt2_var['values'][$key]['free_field5'] = sprintf('%.2f', $gt2_var['values'][$key]['average_weighted_delivery_price'] + $gt2_var['values'][$key]['free_field3'] + $gt2_var['values'][$key]['free_field4']);
                $gt2_var['values'][$key]['discount_surplus_field'] = 'discount_value';
            }
        }

        foreach ($gt2_var['values'] as $key => $vals) {
            if ($vals['free_field5'] > 0 && $amount_left) {
                foreach ($order_completion as $cover => $cover_var) {
                    if ($vals[$cover_var['left']] > 0 && $amount_left) {
                        if ($vals[$cover_var['left']] > $amount_left) {
                            $current_payments_data[$cover] = $amount_left;
                            $gt2_var['values'][$key][$cover_var['left']] = sprintf('%.2f', $gt2_var['values'][$key][$cover_var['left']] - $amount_left);
                            $gt2_var['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2_var['values'][$key][$cover_var['paid']] + $amount_left);
                            $amount_left = 0;
                        } else {
                            $current_payments_data[$cover] = $vals[$cover_var['left']];
                            $gt2_var['values'][$key][$cover_var['left']] = sprintf('%.2f', 0);
                            $gt2_var['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2_var['values'][$key][$cover_var['paid']] + $vals[$cover_var['left']]);
                            $amount_left = $amount_left - $vals[$cover_var['left']];
                        }
                    }
                }

                // update sum fields
                $gt2_var['values'][$key]['article_width'] = sprintf('%.2f', $gt2_var['values'][$key]['article_barcode'] + $gt2_var['values'][$key]['article_height']);
                $gt2_var['values'][$key]['article_volume'] = sprintf('%.2f', $gt2_var['values'][$key]['discount_value'] + $gt2_var['values'][$key]['article_width'] + $gt2_var['values'][$key]['article_weight']);
                $gt2_var['values'][$key]['free_field3'] = sprintf('%.2f', $gt2_var['values'][$key]['free_field1'] + $gt2_var['values'][$key]['free_field2']);
                $gt2_var['values'][$key]['free_field5'] = sprintf('%.2f', $gt2_var['values'][$key]['average_weighted_delivery_price'] + $gt2_var['values'][$key]['free_field3'] + $gt2_var['values'][$key]['free_field4']);
                $gt2_var['values'][$key]['discount_surplus_field'] = 'discount_value';
            }
        }

        $this->registry['db']->StartTrans();
        $document->set('grouping_table_2', $gt2_var, true);
        $document->calculateGT2();
        $document->set('table_values_are_set', true, true);

        if ($document->saveGT2Vars()) {
            // write history
            $new_document = Documents::searchOne($this->registry, $doc_filters);
            $new_document->getVars();
            $gt2_var = $new_document->getGT2Vars();

            $history_params = array(
                'model'       => $document,
                'action_type' => 'edit',
                'new_model'   => $new_document,
                'old_model'   => $old_document
            );

            Documents_History::saveData($this->registry, $history_params);

            $gt2_var = $new_document->getGT2Vars();
            $old_document = clone $new_document;

            require_once PH_MODULES_DIR . 'automations/plugins/creditins/controllers/creditins.automations.controller.php';
            $automations_controller = new Creditins_Automations_Controller($this->registry);

            $params_payment_check = array(
                'status'                      => DOCUMENT_FULL_PAID_STATUS,
                'incomes_reason_type'         => INCOMES_REASON_TYPE_ID,
                'full_payment_var'            => DOCUMENT_CONTRACT_REPAYMENT_DATE,
                'repayment_status'            => DOCUMENT_CONTRACT_TYPE_REPAYMENT,
                'repayment_status_in_advance' => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_ADVANCE,
                'repayment_status_in_time'    => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_TIME
            );

            if (!$automations_controller->checkFullPayment($new_document, $params_payment_check)) {
                $operation_result['error'] = true;
                $operation_result['message'] = $this->i18n('error_reports_repayment_plan_tag_status_failed');
                $this->registry['db']->FailTrans();
            } else {
                // recalculate grouping table and get payments
                $result = $this->recalculateGroupingTable($new_document, $gt2_var, 'annul');
                $issue_correction_result = $this->issueReasonCorrection($new_document->get('id'), sprintf('%.2f', $new_total));

                if ($result && $issue_correction_result['result']) {
                    $new_document = Documents::searchOne($this->registry, $doc_filters);
                    $new_document->getVars();

                    $history_params = array(
                        'model'       => $new_document,
                        'action_type' => 'edit',
                        'new_model'   => $new_document,
                        'old_model'   => $old_document
                    );

                    Documents_History::saveData($this->registry, $history_params);
                } else {
                    // error occured
                    $operation_result['error'] = true;
                    if (!$result) {
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed');
                    } else {
                        $operation_result['message'] = implode("\n", array_merge(array($this->i18n('error_repayment_plan_issue_correction_failed')), $issue_correction_result['messages']));
                    }
                    $this->registry['db']->FailTrans();
                }
            }
        } else {
            // error occured
            $operation_result['error'] = true;
            $operation_result['message'] = $this->i18n('error_repayment_plan_edit_contract_failed');
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();

        if (!$operation_result['error']) {
            $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_successfully');

            // get the data which have to refreshed
            require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

            // prepare the table which have to be reloaded
            $repayment_plans = Creditins_Customer_File::getRepaymentPlans($this->registry, array('customer' => $new_document->get('customer')));
            $credits_list = Creditins_Customer_File::getCredits($this->registry, $new_document->get('customer'));
            $all_credits_list = Creditins_Customer_File::getAllClientCredits($this->registry, $new_document->get('customer'));

            $viewer = new Viewer($this->registry);
            $viewer->loadCustomI18NFiles($this->report_lang_file);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $viewer->template = '_repayment_plans.html';
            $viewer->data['repayment_plans'] = $repayment_plans;
            $operation_result['template_repayment_plans'] = $viewer->fetch();

            $crd_viewer = new Viewer($this->registry);
            $crd_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crd_viewer->setFrameset('frameset_blank.html');
            $crd_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crd_viewer->template = '_credits_list.html';
            $crd_viewer->data['credits_list'] = $credits_list;
            $operation_result['template_credits_list'] = $crd_viewer->fetch();

            $crdall_viewer = new Viewer($this->registry);
            $crdall_viewer->loadCustomI18NFiles($this->report_lang_file);
            $crdall_viewer->setFrameset('frameset_blank.html');
            $crdall_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $crdall_viewer->template = '_customers_contract.html';
            $crdall_viewer->data['customers_contract'] = $all_credits_list;
            $operation_result['template_all_credits_list'] = $crdall_viewer->fetch();
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to ajax search the related persons
     */
    public function _searchRelatedPersons() {
        $this->prepareReportSettings();

        // get the data which have to refreshed
        require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

        // prepare the table which have to be reloaded
        list($bonds, $bonds_pagination) = Creditins_Customer_File::getBonds($this->registry, $this->registry['request']->get('customer'), $this->registry['request']->get('page'));

        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_related_persons.html';
        $viewer->data['customer_id'] = $this->registry['request']->get('customer');
        $viewer->data['related_persons'] = $bonds;
        $viewer->data['related_persons_pagination'] = $bonds_pagination;
        $viewer->data['report_type'] = $this->report;

        echo $viewer->fetch();
    }

    /*
     * Function to ajax search the related persons
     */
    public function _showPaidRepaymentPlanGT2() {
        $this->prepareReportSettings();

        $operation_result = array(
            'content' => '',
            'title'   => ''
        );

        // Include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';

        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('contract_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');

        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $document->sanitize();

        $document_vars = $document->get('vars');

        // goes through all of the vars to find the GT2 table
        foreach ($document_vars as $var_idx => $var) {
            if ($var['type'] == 'gt2') {
                $document->set('grouping_table_2', $var, true);
                break;
            }
        }

        $this->registry['include_gt2'] = true;
        $document->set('vars', $document_vars, true);
        $document->getLayoutVars();

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = $viewer->theme->templatesDir;
        $viewer->template = '_gt2_view.html';
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->data['model'] = $document;
        $template = $viewer->fetch();

        $this->registry->set('get_old_vars', $get_old_vars, true);

        $operation_result['content'] = $template;
        $operation_result['title'] = sprintf($this->i18n('reports_repayment_plan_for_contract'), $document->get('name'));

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to activate a panel for adding NOI or CKR report
     */
    public function _showCkrNoiAddPanel() {
        $this->prepareReportSettings();

        $operation_result = array(
            'content' => '',
            'title'   => ''
        );

        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_add_ckr_noi_panel.html';
        $viewer->data['customer_id'] = $this->registry['request']->get('customer_id');
        $viewer->data['type_report_add'] = $this->registry['request']->get('type_report_to_add');
        $template = $viewer->fetch();

        $operation_result['content'] = $template;
        $operation_result['title'] = ($this->registry['request']->get('type_report_to_add') == 'ckr' ? $this->i18n('reports_report_add_ckr_report') : $this->i18n('reports_report_add_noi_report'));

        print json_encode($operation_result);
        exit;
    }

    /*
     * Save the data CKR and NOI
     */
    public function _saveCkrNoiData() {
        $this->prepareReportSettings();

        $operation_result = array(
            'result'        => true,
            'message'       => array(),
            'table_num_row' => ''
        );

        // Include required classes
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.history.php';

        // get the model of the customer
        $cust_filters = array('where'      => array('c.id = \'' . $this->registry['request']->get('report_ckr_noi_customer') . '\''),
                              'model_lang' => $this->registry['lang']);
        $customer = Customers::searchOne($this->registry, $cust_filters);

        $get_old_vars = $this->registry->get('get_old_vars');

        $this->registry->set('get_old_vars', true, true);
        $customer->getVars();
        $customer->sanitize();
        $old_customer = clone $customer;

        $assoc_vars = $customer->getAssocVars();

        $noi_ckr_value = '';
        if ($this->registry['request']->get('type_report_add') == 'ckr') {
            $noi_ckr_option = CUSTOMER_CLIENT_CKR_OPTION;
        } else {
            $noi_ckr_option = CUSTOMER_CLIENT_NOI_OPTION;
        }

        if (!empty($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE])) {
            // define the row
            $new_row = 1;

            $type_report = $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE]['value'];
            $file_report = $assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['value'];
            $date_report = $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_DATE]['value'];
            $notes_report = $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_NOTES]['value'];

            foreach ($type_report as $row_id => $row_value) {
                if (empty($row_value) && empty($file_report[$row_id]) && empty($date_report[$row_id]) && empty($notes_report[$row_id])) {
                    $new_row = $row_id;
                    break;
                }
                $new_row++;
            }

            // prepare the queries
            $insert = array();
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE]['id'], $new_row, $noi_ckr_option, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE]['multilang'] ? $this->registry['lang'] : ''));
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['id'], $new_row, '', $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['multilang'] ? $this->registry['lang'] : ''));
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_DATE]['id'], $new_row, $this->registry['request']->get('ckr_noi_add_date'), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_DATE]['multilang'] ? $this->registry['lang'] : ''));
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_NOTES]['id'], $new_row, General::slashesEscape($this->registry['request']->get('ckr_noi_add_notes')), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_NOTES]['multilang'] ? $this->registry['lang'] : ''));

            $this->registry['db']->StartTrans();

            // perform the insert queries
            $sql = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . implode(",\n", $insert) . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($sql);

            $new_customer = Customers::searchOne($this->registry, $cust_filters);

            Customers_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'edit',
                    'model'       => $customer,
                    'new_model'   => $new_customer,
                    'old_model'   => $old_customer
                )
            );

            if ($this->registry['db']->HasFailedTrans()) {
                // error occured
                $operation_result['result'] = false;
                $operation_result['message'] = $this->i18n('error_reports_ckr_noi_save_failed');
            } else {
                $operation_result['table_num_row'] = $new_row;
            }

            $this->registry['db']->CompleteTrans();
        } else {
            $operation_result['result'] = false;
            $operation_result['message'] = $this->i18n('error_reports_ckr_noi_installation_not_set_properly');
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to save the file submited via AJAX
     */
    public function _submitCkrNoiFile() {
        $this->prepareReportSettings();
        $result = array(
            'result'      => true,
            'message'     => '',
            'template'    => ''
        );

        $new_row_id = $this->registry['request']->get('table_num_row');

        if (!empty($_FILES['ckr_noi_add_file']) && !$_FILES['ckr_noi_add_file']['error']) {
            $file = $_FILES['ckr_noi_add_file'];

            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            // main panel translations
            $filters = array('where'      => array('c.id = ' . $this->registry['request']->get('model_id')),
                             'model_lang' => $this->registry['lang']);
            $customer = Customers::searchOne($this->registry, $filters);

            $get_old_vars =  $this->registry->get('get_old_vars');
            $this->registry->set('get_old_vars', true, true);
            $customer->getVars();
            $old_customer = clone $customer;

            // attach the file
            $params = array(
                'id'          => '',
                'name'        => $file['name'],
                'filename'    => '',
                'description' => '',
                'revision'    => '',
                'permission'  => 'all');
            $cstm_file_id = Files::attachFile($this->registry, $file, $params, $customer->sanitize());

            $customer->unsanitize();
            $assoc_vars = $customer->getAssocVars();

            // prepare the queries
            $insert_row = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['id'], $new_row_id, $cstm_file_id, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['multilang'] ? $this->registry['lang'] : ''));

            $this->registry['db']->StartTrans();

            // perform the insert queries
            $sql = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . $insert_row . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($sql);

            $new_customer = Customers::searchOne($this->registry, $filters);
            $new_customer->getVars();

            Customers_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'edit',
                    'model'       => $new_customer,
                    'new_model'   => $new_customer,
                    'old_model'   => $old_customer
                )
            );

            $result['result'] = !$this->registry['db']->HasFailedTrans();
            $this->registry['db']->CompleteTrans();
        }

        if (!$result['result']) {
            $result['message'] = $this->i18n('error_reports_ckr_noi_save_file_failed');
        } else {
            // get the data which have to refreshed
            require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

            // prepare the table which have to be reloaded
            $ckr_data = Creditins_Customer_File::getCkrNoiData($this->registry, $this->registry['request']->get('model_id'));
            $ckr_noi_viewer = new Viewer($this->registry);
            $ckr_noi_viewer->loadCustomI18NFiles($this->report_lang_file);
            $ckr_noi_viewer->setFrameset('frameset_blank.html');
            $ckr_noi_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $ckr_noi_viewer->template = '_report_ckr_noi.html';
            $ckr_noi_viewer->data['ckr_noi'] = $ckr_data;
            $ckr_noi_viewer->data['customer_id'] = $this->registry['request']->get('model_id');
            $result['template'] = $ckr_noi_viewer->fetch();
        }

        echo('<script type="text/javascript">');
        echo('var operation_result=' . json_encode($result) . ';');
        echo('</script>');
        exit;
    }


    /*
     * Function to prepare the main report settings
     */
    public function prepareReportSettings() {
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];
        $this->report = $report;

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->report_lang_file = $i18n_file;
        $this->registry['translater']->loadFile($this->report_lang_file);
        Reports::getReportSettings($this->registry, $report);

        return true;
    }

    /*
     * Function to prepare the main report settings
     */
    public function issueReasonCorrection($document_id, $reason_total) {
        $operation_result = array(
            'result'   => true,
            'messages' => true,
        );

        // issue correction document
        // get the incomes reason
        $incomes_reason = $this->getRelatedIncomesReason($document_id);

        if ($incomes_reason && $incomes_reason->get('total_with_vat') != $reason_total) {
            $incomes_reason->getVars();
            $gt2_var = $incomes_reason->getGT2Vars();

            $old_income_reason = clone $incomes_reason;

            foreach ($gt2_var['values'] as $key => $edited_row) {
                $gt2_var['values'][$key]['price'] = sprintf('%.2f', round($reason_total, 2));
                break;
            }

            $incomes_reason->set('grouping_table_2', $gt2_var, true);
            $incomes_reason->calculateGT2();
            $incomes_reason->set('table_values_are_set', true, true);

            $lang_files = array(PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance.ini',
                                PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance_incomes_reasons.ini');
            $this->loadI18NFiles($lang_files);

            if ($incomes_reason->validate('edit') && $incomes_reason->saveCorrect($old_income_reason)) {
                $filters = array('where' => array('fir.id = ' . $incomes_reason->get('id'),
                                                  'fir.annulled_by = 0'));
                $new_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $new_incomes_reason->getGT2Vars();

                Finance_Incomes_Reasons_History::saveData(
                    $this->registry,
                    array(
                        'action_type' => ($old_income_reason->get('correction_id') ? 'addcorrect' : 'edit'),
                        'new_model'   => $new_incomes_reason,
                        'old_model'   => $old_income_reason
                    )
                );
            } else {
                $operation_result['result'] = false;
                $operation_result['messages'] = $this->registry['messages']->getErrors();

                foreach ($operation_result['messages'] as $msg_id => $msg) {
                    $operation_result['messages'][$msg_id] = strip_tags($msg);
                }
            }
        }

        return $operation_result;
    }

    /*
     * Function to get the incomes reason related to the current repayment_plan
     */
    public function getRelatedIncomesReason($document_id) {
        $sql = 'SELECT frr.parent_id' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=frr.parent_id AND fir.type="' . INCOMES_REASON_TYPE_ID . '")' . "\n" .
               'WHERE frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document" AND frr.link_to="' . $document_id . '"' . "\n";
        $incomes_reason = $this->registry['db']->GetOne($sql);

        if ($incomes_reason) {
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';

            $filters = array(
                'where' => array('fir.id = \'' . $incomes_reason . '\'')
            );
            $incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        return $incomes_reason;
    }

    /*
     * Function to distribute additional sum to the related incomes reason
     */
    public function distributeAdditionalSum($document_id, $distribute_sum) {
        $distribute_sum = sprintf('%.2f', round($distribute_sum, 2));
        $incomes_reason = $this->getRelatedIncomesReason($document_id);

        $payments = $incomes_reason->getDirectPayments();
        $result = true;

        if ($payments) {
            $amount_to_distribute = array();
            $payments = array_flip($payments);
            $payment_params['parent_model_name'] = 'Finance_Payment';
            foreach ($payments as $payment_id => $not_imporant) {
                $payment_params['parent_id'] = $payment_id;
                $payments[$payment_id] = $incomes_reason->getPaidAmount($payment_params);
            }

            require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.history.php';
            $filters = array(
                'where'    => array('fp.id IN (\'' . implode('\',\'', array_keys($payments)) . '\')',
                                    'fp.not_distributed_amount>0'
                                   ),
                'sanitize' => false
            );
            $payments_models = Finance_Payments::search($this->registry, $filters);
            $assoc_payment_models = array();
            foreach ($payments_models as $payment_model) {
                $assoc_payment_models[$payment_model->get('id')] = $payment_model;
            }
            unset($payments_models);

            foreach ($assoc_payment_models as $pm_key => $payment_model) {
                if ($distribute_sum) {
                    if ($distribute_sum > $payment_model->get('not_distributed_amount')) {
                        $amount_to_distribute[$payment_model->get('id')] = $payment_model->get('not_distributed_amount');
                        $distribute_sum = $distribute_sum - $payment_model->get('not_distributed_amount');
                    } else {
                        $amount_to_distribute[$payment_model->get('id')] = $distribute_sum;
                        $distribute_sum = 0;
                    }
                } else {
                    unset($assoc_payment_models[$pm_key]);
                }
            }

            if (!empty($amount_to_distribute)) {
                foreach ($amount_to_distribute as $payment_id => $distribute_sum) {
                    $current_payment = $assoc_payment_models[$payment_id];
                    $old_payment = clone $current_payment;

                    $current_payment->set('relatives_payments', array($incomes_reason->get('id') => $distribute_sum), true);
                    $current_payment->set('selected_tab', 'reason', true);
                    $current_payment->getCustomerDocuments('reason');
                    $current_payment->getRepaymentPlanDocuments('reason');

                    if ($current_payment->updateIncomesBalance()) {
                        Finance_Payments_History::saveData($this->registry,
                                                           array('model' => $current_payment,
                                                                 'action_type' => 'balance',
                                                                 'new_model' => $current_payment,
                                                                 'old_model' => $old_payment));

                        // check if the payment has the undistributed tag and remove it if necessary
                        $current_payment->getTags();
                        $current_payment->getModelTagsForAudit();

                        $filters_pay = array('where' => array('fp.id IN (\'' . $current_payment->get('id') . '\')'));
                        $new_payment = Finance_Payments::searchOne($this->registry, $filters_pay);
                        $new_payment->getTags();

                        if (in_array(PAYMENT_TAG_NOT_DISTRIBUTED_PAYMENT, $new_payment->get('tags')) && !floatval($new_payment->get('not_distributed_amount'))) {
                            $tags = $new_payment->get('tags');
                            foreach ($tags as $k => $tag) {
                                if ($tag == PAYMENT_TAG_NOT_DISTRIBUTED_PAYMENT) {
                                    unset($tags[$k]);
                                    break;
                                }
                            }
                            $new_payment->set('tags', array_values($tags), true);
                            $new_payment->getModelTagsForAudit();
                            $old_payment = clone($new_payment);

                            if ($new_payment->updateTags(array('skip_permissions' => true))) {
                                // get the updated document
                                $tag_payment = Finance_Payments::searchOne($this->registry, $filters_pay);
                                $tag_payment->getModelTagsForAudit();
                                $tag_payment->sanitize();
                                Finance_Payments_History::saveData($this->registry, array('model' => $tag_payment, 'action_type' => 'tag', 'new_model' => $tag_payment, 'old_model' => $old_payment));
                            } else {
                                $result = false;
                            }
                        }
                    } else {
                        $result = false;
                    }
                }
            }
        }

        return $result;
    }

    /*
     * Function to prepare the main report settings
     */
    public function advancedLoanCoverRecalculate($repayment_plan, $advanced_payment_date) {
        // included needed classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';

        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $repayment_plan . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $document->getVars();
        $gt2 = $document->getGT2Vars();

        $sums_to_pay = array(
            'principal' => 0,
            'interest'  => 0,
            'penalty'   => 0,
            'warranty'  => 0,
            'total'     => 0
        );

        if (!empty($gt2['values'])) {
            $previous_period_end = '';
            foreach ($gt2['values'] as $row_id => $row_values) {
                if (!$previous_period_end) {
                    $payment_period_start = $document->get('date');
                } else {
                    $payment_period_start = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($previous_period_end)));
                }
                $previous_period_end = $row_values['article_code'];
                $payment_period_end = $row_values['article_code'];

                if ($advanced_payment_date < $payment_period_start) {
                    // the period of the payment is not started yet
                    $sums_to_pay['interest'] += 0;
                    $sums_to_pay['warranty'] += 0;

                    $gt2['values'][$row_id]['quantity'] = $gt2['values'][$row_id]['article_barcode'];
                    $gt2['values'][$row_id]['article_trademark'] = $gt2['values'][$row_id]['article_height'];
                } elseif ($advanced_payment_date > $payment_period_end) {
                    // the period of the payment is passed
                    $sums_to_pay['interest'] += $row_values['free_field1'];
                    $sums_to_pay['warranty'] += $row_values['free_field2'];
                } else {
                    // the searched date is within the current payment so apply the formula
                    $sums_to_pay['interest'] += $row_values['free_field1'];
                    $sums_to_pay['warranty'] += $row_values['free_field2'];
                }

                $gt2['values'][$row_id]['last_delivery_price'] = $gt2['values'][$row_id]['article_width'] = ($gt2['values'][$row_id]['article_barcode'] + $gt2['values'][$row_id]['article_height']);

                $sums_to_pay['principal'] += $row_values['average_weighted_delivery_price'];
                $sums_to_pay['penalty'] += $row_values['free_field4'];

                $gt2['values'][$row_id]['quantity'] = sprintf('%.2f', $gt2['values'][$row_id]['quantity']);
                $gt2['values'][$row_id]['article_trademark'] = sprintf('%.2f', $gt2['values'][$row_id]['article_trademark']);
                $gt2['values'][$row_id]['last_delivery_price'] = sprintf('%.2f', ($gt2['values'][$row_id]['quantity'] + $gt2['values'][$row_id]['article_trademark']));
                $gt2['values'][$row_id]['article_second_code'] = sprintf('%.2f', ($gt2['values'][$row_id]['price'] + $gt2['values'][$row_id]['last_delivery_price'] + $gt2['values'][$row_id]['article_delivery_code']));

                $gt2['values'][$row_id]['average_weighted_delivery_price'] = sprintf('%.2f', ($gt2['values'][$row_id]['price'] - $gt2['values'][$row_id]['discount_value']));
                $gt2['values'][$row_id]['free_field1'] = sprintf('%.2f', ($gt2['values'][$row_id]['quantity'] - $gt2['values'][$row_id]['article_barcode']));
                $gt2['values'][$row_id]['free_field2'] = sprintf('%.2f', ($gt2['values'][$row_id]['article_trademark'] - $gt2['values'][$row_id]['article_height']));
                $gt2['values'][$row_id]['free_field3'] = sprintf('%.2f', ($gt2['values'][$row_id]['last_delivery_price'] - $gt2['values'][$row_id]['article_width']));
                $gt2['values'][$row_id]['free_field4'] = sprintf('%.2f', ($gt2['values'][$row_id]['article_delivery_code'] - $gt2['values'][$row_id]['article_weight']));
                $gt2['values'][$row_id]['free_field5'] = sprintf('%.2f', ($gt2['values'][$row_id]['article_second_code'] - $gt2['values'][$row_id]['article_volume']));
            }
        }
        $this->registry->set('get_old_vars', $get_old_vars, true);

        $sums_to_pay['total'] = $sums_to_pay['interest'] + $sums_to_pay['warranty'] + $sums_to_pay['principal'] + $sums_to_pay['penalty'];

        return array('gt2' => $gt2, 'sums_to_pay' => $sums_to_pay);
    }

    /*
     * Function to prepare the main report settings
     */
    public function recalculateGroupingTable($document, $gt2, $action) {
        $result = true;

        $assoc_vars = $document->getAssocVars();

        // delete the existing grouping table
        $group_table_vars = array(SCHEDULE_VAR_GT2_ID, SCHEDULE_VAR_GT2_ROW, SCHEDULE_VAR_PAYMENT_ID, SCHEDULE_VAR_PAYMENT_NUM, SCHEDULE_VAR_DISTRIBUTE_DATE, SCHEDULE_VAR_PAYMENT_DATE, SCHEDULE_VAR_PRINCIPAL, SCHEDULE_VAR_WARRANTY, SCHEDULE_VAR_INTEREST, SCHEDULE_VAR_PENALTY);
        $group_table_vars = array_fill_keys($group_table_vars, '');

        foreach ($group_table_vars as $key_var => $gtv) {
            $group_table_vars[$key_var] = $assoc_vars[$key_var]['id'];
        }

        $this->registry['db']->StartTrans();
        $delete_sql = 'DELETE FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' WHERE `model_id`="' . $document->get('id') . '" AND `var_id` IN ("' . implode('","', $group_table_vars) . '")' . "\n";
        $this->registry['db']->Execute($delete_sql);

        $order_completion = array();
        if ($action == 'edit') {
            $order_completion = array(
                'penalty' => array(
                    'paid' => 'article_weight',
                    'left' => 'free_field4',
                ),
                'interest' => array(
                    'paid' => 'article_barcode',
                    'left' => 'free_field1',
                ),
                'warranty' => array(
                    'paid' => 'article_height',
                    'left' => 'free_field2',
                ),
                'principal' => array(
                    'paid' => 'discount_value',
                    'left' => 'average_weighted_delivery_price',
                )
            );
        } else {
            $order_completion = array(
                'penalty' => array(
                    'paid' => 'article_weight',
                    'left' => 'free_field4',
                ),
                'interest' => array(
                    'paid' => 'article_barcode',
                    'left' => 'free_field1',
                ),
                'warranty' => array(
                    'paid' => 'article_height',
                    'left' => 'free_field2',
                )
            );
        }

        // query to get the needed data
        $sql = 'SELECT p.id as idx, p.*, fb.paid_amount' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fb' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=fb.paid_to AND fir.type="' . INCOMES_REASON_TYPE_ID . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS p' . "\n" .
               '  ON (fb.parent_model_name="Finance_Payment" AND p.id=fb.parent_id)' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
               '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id AND frr.link_to_model_name="Document" AND frr.link_to="' . $document->get('id') . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               '  ON (frr.link_to=d.id)' . "\n" .
               'WHERE fb.parent_model_name="Finance_Payment" AND fb.paid_to_model_name="Finance_Incomes_Reason" AND fb.paid_amount>0' . "\n";
        $schedule_payments = $this->registry['db']->GetAssoc($sql);

        $current_payments = array();

        if ($action == 'edit') {
            $left_paid_sums = array();
            foreach ($schedule_payments as $payment_id => $payment_info) {
                $amount_left = $payment_info['paid_amount'];
                $row_num = 0;
                foreach ($gt2['values'] as $key => $vals) {
                    if (!isset($left_paid_sums[$key])) {
                        $left_paid_sums[$key] = array(
                            'principal' => $vals['discount_value'],
                            'interest'  => $vals['article_barcode'],
                            'warranty'  => $vals['article_height'],
                            'penalty'   => $vals['article_weight'],
                        );
                    }

                    if (!$amount_left) {
                        break;
                    }
                    $row_num++;

                    if (!floatval($left_paid_sums[$key]['principal']) && !floatval($left_paid_sums[$key]['interest']) && !floatval($left_paid_sums[$key]['warranty']) && !floatval($left_paid_sums[$key]['penalty'])) {
                        continue;
                    }

                    $current_payments_data = array(
                        'gt2_id'          => '',
                        'gt2_row'         => '',
                        'payment_id'      => '',
                        'payment_num'     => '',
                        'payment_date'    => '',
                        'distribute_date' => '',
                        'interest'        => 0,
                        'warranty'        => 0,
                        'principal'       => 0,
                        'penalty'         => 0
                    );

                    foreach ($order_completion as $cover => $cover_var) {
                        if ($left_paid_sums[$key][$cover] > 0 && $amount_left) {
                            if ($left_paid_sums[$key][$cover] > $amount_left) {
                                $current_payments_data[$cover] = $amount_left;
                                $left_paid_sums[$key][$cover] = sprintf('%.2f', floatval($left_paid_sums[$key][$cover] - $amount_left));
                                $amount_left = 0;
                            } else {
                                $current_payments_data[$cover] = $left_paid_sums[$key][$cover];
                                $amount_left = $amount_left - $left_paid_sums[$key][$cover];
                                $left_paid_sums[$key][$cover] = 0;
                            }
                        }
                    }

                    // complete the data for the payment row
                    $current_payments_data['gt2_id'] = $key;
                    $current_payments_data['gt2_row'] = $row_num;
                    $current_payments_data['payment_id'] = $payment_info['id'];
                    $current_payments_data['payment_num'] = $payment_info['num'];
                    $current_payments_data['distribute_date'] = $payment_info['status_modified'];
                    $current_payments_data['payment_date'] = $payment_info['issue_date'];

                    $current_payments[] = $current_payments_data;
                }
            }
        } else {
            $left_paid_sums = array();
            foreach ($schedule_payments as $payment_id => $payment_info) {
                $row_num = 0;

                foreach ($gt2['values'] as $key => $vals) {
                    $row_num++;
                    if (!isset($left_paid_sums[$key])) {
                        $left_paid_sums[$key] = array(
                            'principal' => $vals['discount_value'],
                            'interest'  => $vals['article_barcode'],
                            'warranty'  => $vals['article_height'],
                            'penalty'   => $vals['article_weight'],
                        );
                    }
                    if (!$schedule_payments[$payment_id]['paid_amount'] || !$left_paid_sums[$key]['principal']) {
                        continue;
                    }
                    $row_key = sprintf('%d_%d', $payment_id, $vals['id']);

                    if (empty($current_payments[$row_key])) {
                        $current_payments[$row_key] = array(
                            'gt2_id'          => $key,
                            'gt2_row'         => $row_num,
                            'payment_id'      => $schedule_payments[$payment_id]['id'],
                            'payment_num'     => $schedule_payments[$payment_id]['num'],
                            'distribute_date' => $schedule_payments[$payment_id]['status_modified'],
                            'payment_date'    => $schedule_payments[$payment_id]['issue_date'],
                            'interest'        => 0,
                            'warranty'        => 0,
                            'principal'       => 0,
                            'penalty'         => 0
                        );
                    }

                    if ($vals['discount_value'] > 0 && $schedule_payments[$payment_id]['paid_amount']) {
                        if ($vals['discount_value'] > $schedule_payments[$payment_id]['paid_amount']) {
                            $current_payments[$row_key]['principal'] = $schedule_payments[$payment_id]['paid_amount'];
                            $schedule_payments[$payment_id]['paid_amount'] = 0;
                            $left_paid_sums[$key]['principal'] = $left_paid_sums[$key]['principal'] - $schedule_payments[$payment_id]['paid_amount'];
                        } else {
                            $current_payments[$row_key]['principal'] = $vals['discount_value'];
                            $schedule_payments[$payment_id]['paid_amount'] = $schedule_payments[$payment_id]['paid_amount'] - $vals['discount_value'];
                            $left_paid_sums[$key]['principal'] = 0;
                        }
                    }
                }
            }

            foreach ($schedule_payments as $payment_id => $payment_info) {
                $row_num = 0;
                foreach ($gt2['values'] as $key => $vals) {
                    $row_num++;
                    if (!$schedule_payments[$payment_id]['paid_amount'] || (!$left_paid_sums[$key]['interest'] && !$left_paid_sums[$key]['warranty'] && !$left_paid_sums[$key]['penalty'])) {
                        continue;
                    }
                    $row_key = sprintf('%d_%d', $payment_id, $vals['id']);

                    if (empty($current_payments[$row_key])) {
                        $current_payments[$row_key] = array(
                            'gt2_id'          => $key,
                            'gt2_row'         => $row_num,
                            'payment_id'      => $schedule_payments[$payment_id]['id'],
                            'payment_num'     => $schedule_payments[$payment_id]['num'],
                            'distribute_date' => $schedule_payments[$payment_id]['status_modified'],
                            'payment_date'    => $schedule_payments[$payment_id]['issue_date'],
                            'interest'        => 0,
                            'warranty'        => 0,
                            'principal'       => 0,
                            'penalty'         => 0
                        );
                    }
                    foreach ($order_completion as $cover => $cover_var) {
                        if (!$left_paid_sums[$key][$cover]) {
                            continue;
                        }
                        if ($left_paid_sums[$key][$cover] > 0 && $schedule_payments[$payment_id]['paid_amount']) {
                            if ($left_paid_sums[$key][$cover] > $schedule_payments[$payment_id]['paid_amount']) {
                                $current_payments[$row_key][$cover] += $schedule_payments[$payment_id]['paid_amount'];
                                $left_paid_sums[$key][$cover] = $left_paid_sums[$key][$cover] - $schedule_payments[$payment_id]['paid_amount'];
                                $schedule_payments[$payment_id]['paid_amount'] = 0;
                            } else {
                                $current_payments[$row_key][$cover] += $left_paid_sums[$key][$cover];
                                $schedule_payments[$payment_id]['paid_amount'] = $schedule_payments[$payment_id]['paid_amount'] - $left_paid_sums[$key][$cover];
                                $left_paid_sums[$key][$cover] = 0;
                            }
                        }
                    }
                }
            }
        }

        if (!empty($current_payments)) {
            $row_num = 1;
            $insert = array();
            foreach ($current_payments as $cp) {
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_GT2_ID]['id'], $row_num, $cp['gt2_id'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_GT2_ROW]['id'], $row_num, $cp['gt2_row'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PAYMENT_ID]['id'], $row_num, $cp['payment_id'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PAYMENT_NUM]['id'], $row_num, $cp['payment_num'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_DISTRIBUTE_DATE]['id'], $row_num, $cp['distribute_date'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PAYMENT_DATE]['id'], $row_num, $cp['payment_date'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PRINCIPAL]['id'], $row_num, sprintf('%.2f', $cp['principal']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_WARRANTY]['id'], $row_num, sprintf('%.2f', $cp['warranty']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_INTEREST]['id'], $row_num, sprintf('%.2f', $cp['interest']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PENALTY]['id'], $row_num, sprintf('%.2f', $cp['penalty']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $row_num++;
            }

            // perform the insert queries
            $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . implode(",\n", $insert) . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($sql);
        }
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /*
     * Function to load the panel with sms list
     */
    public function _loadSMSPanel() {
        $this->prepareReportSettings();

        $customer_id = $this->registry['request']->get('customer_id');
        if ($this->registry['request']->get('page')) {
            $page = $this->registry['request']->get('page');
            $include_outer_container = false;
        } else {
            $page = 1;
            $include_outer_container = true;
        }

        // get the data which have to refreshed
        require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

        // prepare the table which have to be reloaded
        list($sms_list, $pagination) = Creditins_Customer_File::getSMSList($this->registry, $customer_id, false, $page);

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_sms_panel.html';
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->data['sms_list'] = $sms_list;
        $viewer->data['include_outer_container'] = $include_outer_container;
        $viewer->data['pagination'] = $pagination;
        $viewer->data['report_type'] = $this->report;
        $viewer->data['customer_id'] = $customer_id;
        $template = $viewer->fetch();

        if ($include_outer_container) {
            $operation_result['content'] = $template;
            $operation_result['title'] = $this->i18n('reports_sent_sms');
            print json_encode($operation_result);
        } else {
            print $template;
        }

        exit;
    }
}

?>
