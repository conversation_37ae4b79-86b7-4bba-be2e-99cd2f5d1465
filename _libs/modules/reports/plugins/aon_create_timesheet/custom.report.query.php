<?php
    Class Aon_Create_Timesheet Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            if (!empty($filters['insurer'])) {
                $payments_sum = 0;

                //round all subtotal amounts according to 'gt2_rows' precision setting
                $precision = $registry['config']->getParam('precision', 'gt2_rows');
                $skip_debit_notes = array();
                $skip_contracts_rows = array();

                $contract_types = array();
                if (!empty($filters['contract_type'])) {
                    $contract_types = array_merge($contract_types, explode('_', $filters['contract_type']));
                } else {
                    $sql = 'SELECT dt.id' . "\n" .
                           'FROM ' . DB_TABLE_CONTRACTS_TYPES . ' AS dt' . "\n" .
                           'WHERE dt.active=1 AND dt.deleted_by=0 AND dt.id NOT IN (' . EXCLUDE_CONTRACT_TYPE . ')' . "\n";
                    $contract_types = $registry['db']->GetCol($sql);
                }

                //sql to take the ids of the needed additional vars
                $additional_vars = array(CONTRACTS_INSURER_ID, CONTRACTS_ALTERNATVE_INSURER_ID, CONTRACTS_NUMBER_PAYMENTS);
                $sql_for_contract_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Contract" AND (fm.name LIKE "' . CONTRACTS_INSURANCE_TYPE_PREFIX . '%" OR fm.name IN ("' . implode('","', $additional_vars) . '")) AND fm.model_type IN (' . implode(',', $contract_types) . ')';
                $contracts_add_vars = $registry['db']->GetAll($sql_for_contract_add_vars);

                $debit_notes_statuses = array(DEBIT_NOTE_SUBSTATUS_PAID, DEBIT_NOTE_SUBSTATUS_PARTIAL);

                $insurer_ids = array();
                $alternative_insurer_ids = array();
                $insurance_type_ids = array();
                $insurerance_number_payments_ids = array();

                foreach ($contracts_add_vars as $contr_var) {
                    if (preg_match('#^' . CONTRACTS_INSURANCE_TYPE_PREFIX . '\d+$#', $contr_var['name'])) {
                        $insurance_type_ids[] =  $contr_var['id'];
                    } elseif ($contr_var['name'] == CONTRACTS_INSURER_ID) {
                        $insurer_ids[] = $contr_var['id'];
                    } elseif ($contr_var['name'] == CONTRACTS_ALTERNATVE_INSURER_ID) {
                        $alternative_insurer_ids[] =  $contr_var['id'];
                    } elseif ($contr_var['name'] == CONTRACTS_NUMBER_PAYMENTS) {
                        $insurerance_number_payments_ids[] =  $contr_var['id'];
                    }
                }

                //sql to take the ids of the needed additional vars
                $sql_for_add_vars = 'SELECT fm.id, fm.model, fm.model_type, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE (fm.model="Customer" AND fm.model_type=' . CUSTOMER_TYPE_INSURER . ' AND fm.name="' . CUSTOMER_INSURER_NAME_IN_BGN . '") ORDER BY fm.position';
                $customer_name_in_bg = $registry['db']->GetOne($sql_for_add_vars);

                // get the paid sum to the debit notes and the related payments
                $dnp_add_vars = array(PAYMENT_DEBIT_NOTE_ROW, PAYMENT_DEBIT_NOTE_VALUE);
                $sql_for_contract_add_vars = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Document" AND name IN ("' . implode('","', $dnp_add_vars) . '") AND model_type="' . PAYMENT_DEBIT_NOTE_TYPE_ID . '"';
                $dnp_add_vars = $registry['db']->GetAssoc($sql_for_contract_add_vars);

                // get the paid sum to the debit notes and the related payments
                $dn_add_vars = array(DEBIT_NOTE_VALUE, DEBIT_NOTE_PAID_VALUE, DEBIT_NOTE_PAID_TO);
                $sql_for_dn_add_vars = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Document" AND name IN ("' . implode('","', $dn_add_vars) . '") AND model_type="' . DEBIT_NOTE_TYPE_ID  . '"';
                $dn_add_vars = $registry['db']->GetAssoc($sql_for_dn_add_vars);

                // IF THE REPORT IS CALLED FROM THE REPORTS MODULE THE document_id FILTER SHOULD BE EMPTY
                if (!empty($filters['document_id'])) {
                    // Get data for the document
                    // the rows from the document should be taken
                    $sql_doc_row['select'] = 'SELECT gt2.article_id as policy_id, con.type as contract_type, con.custom_num as policy_num, "1" as checked, gt2.article_volume as gt2_id, ' . "\n" .
                                             '  gt2.article_deliverer as customer, CONCAT(ci18n_insured_name.name, " ", ci18n_insured_name.lastname) as customer_name, gt2.price, ' . "\n" .
                                             '  gt2.quantity, gt2.free_field3 as insurance_type, nomi18n.name as insurance_type_name, DATE_FORMAT(gt2_i18n.free_text1, "%Y-%m-%d") as deadline, ' . "\n" .
                                             '  gt2.last_delivery_price, gt2.article_barcode as payment_num, ROUND(gt2.subtotal, ' . $precision . ') as subtotal, gt2.id as gt2_timesheet_id, ' . "\n" .
                                             '  ROUND(gt2.average_weighted_delivery_price, ' . $precision . ') as average_weighted_delivery_price, gt2.article_delivery_code as debit_note_id, ' . "\n" .
                                             '  gt2_i18n.free_text3 as debit_note_num, ROUND(gt2.article_height, ' . $precision . ') as article_deliverer_name,  ' . "\n" .
                                             '  gt2.free_field1 as article_description, 0 as fee, gt2.article_code, gt2.article_width, gt2.article_weight, ' . "\n" .
                                             '  gt2_i18n.free_text2 as article_name, gt2_i18n.free_text4, gt2_dn.id as debit_note_row_id, gt2_i18n.free_text5 as date_sign, gt2.discount_percentage, gt2.discount_value, ' . "\n" .
                                             '  gt2.article_volume, gt2.article_second_code, gt2.free_field5, gt2.free_field4' . "\n";

                    $sql_doc_row['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS doc' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                             '  ON (gt2.model="Document" AND doc.id=gt2.model_id)' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                                             '  ON (con.id=gt2.article_id)' . "\n" .
                                             'INNER JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2_i18n' . "\n" .
                                             '  ON (gt2_i18n.parent_id=gt2.id AND gt2_i18n.lang="' . $model_lang . '")' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_insured_name' . "\n" .
                                             '  ON (ci18n_insured_name.parent_id=gt2.article_deliverer AND ci18n_insured_name.lang="' . $model_lang . '")' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                             '  ON (gt2.free_field3=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2_dn' . "\n" .
                                             '  ON (gt2_dn.model="Document" AND gt2_dn.model_id=gt2.article_delivery_code AND gt2_dn.free_field2=ROUND(gt2.article_volume))' . "\n";

                    // construct where
                    $where = array();
                    $where[] = 'doc.id="' . $filters['document_id'] . '"';
                    $where[] = 'doc.deleted_by=0';
                    $where[] = 'doc.active=1';
                    $where[] = 'doc.type="' . DOCUMENT_TIMESHEET_TYPE_ID . '"';

                    $sql_doc_row['where'] = 'WHERE ' . implode(' AND ', $where);
                    $sql_doc_row['order'] = 'ORDER BY gt2.id ASC';

                    $query_data = implode("\n", $sql_doc_row);
                    $timesheet_data = $registry['db']->GetAll($query_data);

                    $included_contract_rows = array();
                    foreach ($timesheet_data as $td) {
                        if (!empty($td['debit_note_id'])) {
                            $debit_note_id = $td['debit_note_id'];
                        } else {
                            $debit_note_id = uniqid();
                        }
                        if (!isset($final_results[$debit_note_id])) {
                            $final_results[$debit_note_id] = array(
                                'checked'      => 1,
                                'id'           => $td['debit_note_id'],
                                'num'          => $td['debit_note_num'],
                                'payment_date' => $td['article_description'],
                                'contracts'    => array(),
                                'encoded_data' => '',
                                'rowspan'      => 0

                            );
                        }

                        if ($td['debit_note_row_id']) {
                            $debit_note_row = $td['debit_note_row_id'];
                        } else {
                            $debit_note_row = uniqid();
                        }
                        if (!isset($final_results[$debit_note_id]['contracts'][$debit_note_row])) {
                            $final_results[$debit_note_id]['contracts'][$debit_note_row] = $td;
                            $final_results[$debit_note_id]['contracts'][$debit_note_row]['fee'] = floatval($td['article_code']) + floatval($td['article_width']) + floatval($td['article_weight']);
                            $final_results[$debit_note_id]['contracts'][$debit_note_row]['payments'] = array();
                            $final_results[$debit_note_id]['rowspan']++;
                        }

                        if (!empty($td['free_field5'])) {
                            if (!isset($final_results[$debit_note_id]['contracts'][$debit_note_row]['payments'][$td['free_field5']])) {
                                $final_results[$debit_note_id]['contracts'][$debit_note_row]['payments'][$td['free_field5']] = array(
                                    'id'      => $td['free_field5'],
                                    'num'     => $td['article_second_code'],
                                    'gt2_row' => $td['gt2_timesheet_id'],
                                    'value' => 0
                                );
                            }
                            $final_results[$debit_note_id]['contracts'][$debit_note_row]['payments'][$td['free_field5']]['value'] += floatval($td['free_field4']);
                            $skip_debit_notes[] = $debit_note_id;
                        } else {
                            $final_results[$debit_note_id]['contracts'][$debit_note_row]['payments'][] = array(
                                'id'      => $td['free_field5'],
                                'num'     => $td['article_second_code'],
                                'gt2_row' => $td['gt2_timesheet_id'],
                                'value' => 0
                            );
                            $skip_contracts_rows[] = round($td['article_volume']);
                        }

                        if (!in_array(intval($td['gt2_id']), $included_contract_rows)) {
                            $included_contract_rows[] = intval($td['gt2_id']);
                            $payments_sum += $td['average_weighted_delivery_price'];
                        }
                    }
                    unset($timesheet_data);
                    $skip_debit_notes = array_unique($skip_debit_notes);
                }

                $sql_data = array();

                // construct where
                $where = array();
                $where[] = 'con.annulled_by=0';
                $where[] = 'con.deleted_by=0';
                $where[] = 'con.active=1';
                $where[] = 'con.subtype="contract"';
                $where[] = '(gt2.article_trademark="" OR gt2.article_trademark IS NULL OR gt2.article_trademark="0")';
                if (!empty($filters['client'])) {
                    $where[] = 'con.customer="' . $filters['client'] . '"';
                }
                if (!empty($filters['contract_filt'])) {
                    $where[] = 'con.id="' . $filters['contract_filt'] . '"';
                }
                if (!empty($contract_types)) {
                    $where[] = 'con.type IN ("' . implode('","', $contract_types) . '")';
                }

                // Get the gt2 rows for the final results
                $sql_data['select'] = 'SELECT con.id as policy_id, con.type as contract_type, con.custom_num as policy_num, 0 as checked, gt2.id as gt2_id, con.customer, CONCAT(ci18n_client.name, " ", ci18n_client.lastname) as customer_name,' . "\n" .
                                      '  gt2.price, gt2.quantity, con_cstm_insurance.value as insurance_type, nomi18n.name as insurance_type_name, DATE_FORMAT(gt2_i18n.free_text1, "%Y-%m-%d") as deadline, ' . "\n" .
                                      '  gt2.last_delivery_price, gt2.free_field5 as row_num, ROUND(gt2.subtotal, ' . $precision . ') as subtotal, con_cstm_number_payments.value as number_payments, ' . "\n" .
                                      '  ROUND(gt2.average_weighted_delivery_price, ' . $precision . ') as average_weighted_delivery_price, gt2_i18n.free_text5 as debit_note_id, ' . "\n" .
                                      '  gt2_i18n.article_deliverer_name as article_deliverer_name, gt2_i18n.article_description, 0 as fee, gt2.article_code, gt2.article_width, ' . "\n" .
                                      '  gt2.article_weight, gt2_i18n.article_name as article_name, gt2_i18n.free_text4, con.date_sign as date_sign, gt2.discount_percentage, gt2.discount_value' . "\n";

                $sql_data['from']   = 'FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                      '  ON (gt2.model="Contract" AND con.id=gt2.model_id AND ' . implode(' AND ', $where) . ' AND gt2.free_field4!=2 AND gt2.free_field4!=4)' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2_i18n' . "\n" .
                                      '  ON (gt2_i18n.parent_id=gt2.id AND gt2_i18n.lang="' . $model_lang . '")' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_cstm_insurance' . "\n" .
                                      '  ON (con_cstm_insurance.model_id=con.id AND con_cstm_insurance.var_id IN (' . implode(',', $insurance_type_ids) . '))' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_cstm_number_payments' . "\n" .
                                      '  ON (con_cstm_number_payments.model_id=con.id AND con_cstm_number_payments.var_id IN (' . implode(',', $insurerance_number_payments_ids) . '))' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_insurer' . "\n" .
                                      '  ON (con_insurer.model_id=con.id AND con_insurer.var_id IN (' . implode(',', $insurer_ids) . '))' . "\n" .
                                      'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_alternative_insurer' . "\n" .
                                      '  ON (con_alternative_insurer.model_id=con.id AND con_alternative_insurer.var_id IN (\'' . implode('\',\'', $alternative_insurer_ids) . '\'))' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_client' . "\n" .
                                      '  ON (ci18n_client.parent_id=con.customer AND ci18n_client.lang="' . $model_lang . '")' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                      '  ON (con_cstm_insurance.value=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                      'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_insurer_name' . "\n" .
                                      '  ON (ci18n_insurer_name.parent_id= con_insurer.value AND ci18n_insurer_name.lang="' . $model_lang . '")' . "\n";

                $where[] = '(con_insurer.value="' . $filters['insurer'] . '" OR (con.type="' . CONTRACT_REINSURER_POLICY . '" AND con_alternative_insurer.value="' . $filters['insurer'] . '"))';
                if (!empty($filters['from_date'])) {
                    $where[] = 'DATE_FORMAT(gt2_i18n.free_text1, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
                }
                if (!empty($filters['to_date'])) {
                    $where[] = 'DATE_FORMAT(gt2_i18n.free_text1, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
                }
                if (!empty($filters['customer_branch'])) {
                    $where[] = 'gt2.article_height="' . $filters['customer_branch'] . '"';
                }
                if (!empty($skip_contracts_rows)) {
                    $where[] = 'gt2.id NOT IN ("' . implode('","', $skip_contracts_rows) . '")';
                }

                $sql_data['where'] = 'WHERE ' . implode(' AND ', $where);
                $query_data = implode("\n", $sql_data);
                $contracts_rows = $registry['db']->GetAll($query_data);

                $related_debit_notes = array();
                foreach ($contracts_rows as $cr) {
                    if (!empty($cr['debit_note_id'])) {
                        $related_debit_notes[] = $cr['debit_note_id'];
                    }
                }
                $related_debit_notes = array_unique($related_debit_notes);

                // get the related debit notes
                $debit_note_rows = array();
                if (!empty($related_debit_notes)) {
                    $sql = 'SELECT CONCAT(d.id, "_", gt2_dn.free_field2) as idx, d.id as debit_note_id, d.full_num as debit_note_num, gt2_dn.id as debit_note_row_id' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dn_value' . "\n" .
                           '  ON (dn_value.model_id=d.id AND dn_value.var_id="' . $dn_add_vars[DEBIT_NOTE_VALUE] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dn_paid_value' . "\n" .
                           '  ON (dn_paid_value.model_id=d.id AND dn_paid_value.var_id="' . $dn_add_vars[DEBIT_NOTE_PAID_VALUE] . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm1' . "\n" .
                           '  ON (tm1.model_id=d.id AND tm1.model="Document" AND tm1.tag_id="' . DEBIT_NOTE_TAG_SYSTEM_APPROVED . '")' . "\n" .
                           'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2_dn' . "\n" .
                           '  ON (gt2_dn.model_id=d.id AND gt2_dn.model="Document" AND gt2_dn.free_field2 IS NOT NULL AND gt2_dn.free_field2!="" AND gt2_dn.free_field2!="0")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dn_paid_to' . "\n" .
                           ' ON (dn_paid_to.var_id="' . $dn_add_vars[DEBIT_NOTE_PAID_TO] . '" AND dn_paid_to.model_id=d.id AND dn_paid_to.value="' . DEBIT_NOTE_PAID_TO_INSURER . '")' . "\n";
                    $where = array();
                    $where[] = 'd.active=1';
                    $where[] = 'd.deleted_by=0';
                    $where[] = 'd.id IN ("' . implode('","', $related_debit_notes) . '")';
                    $where[] = 'd.type="' . DEBIT_NOTE_TYPE_ID . '"';
                    $where[] = '(tm1.tag_id IS NOT NULL OR (dn_paid_to.value IS NOT NULL OR (d.substatus IN ("' . implode('","', $debit_notes_statuses) . '") AND dn_value.value-dn_paid_value.value<=' . DEBIT_NOTE_ACCEPTABLE_BALANCE_DIFFERENCE . ')))';
                    //$where[] = '(dn_paid_to.value IS NOT NULL OR (d.substatus IN ("' . implode('","', $debit_notes_statuses) . '") AND (tm1.tag_id IS NOT NULL OR dn_value.value-dn_paid_value.value<=' . DEBIT_NOTE_ACCEPTABLE_BALANCE_DIFFERENCE . ')))';
                    if (!empty($skip_debit_notes)) {
                        $where[] = 'd.id NOT IN ("' . implode('","', $skip_debit_notes) . '")';
                    }

                    $sql .= 'WHERE ' . implode(' AND ', $where);
                    $debit_note_rows = $registry['db']->GetAssoc($sql);
                }

                $contracts_dates = array();
                $debit_notes_list_ids = array();
                foreach ($contracts_rows as $cr_key => $cr) {
                    // build the key
                    if ($cr['debit_note_id']) {
                        $related_key = sprintf('%d_%d', $cr['debit_note_id'], $cr['gt2_id']);
                        if (isset($debit_note_rows[$related_key])) {
                            $contracts_rows[$cr_key]['debit_note_num'] = $debit_note_rows[$related_key]['debit_note_num'];
                            $contracts_rows[$cr_key]['debit_note_row_id'] = $debit_note_rows[$related_key]['debit_note_row_id'];
                            $debit_notes_list_ids[$debit_note_rows[$related_key]['debit_note_row_id']] = $cr['debit_note_id'];
                        } else {
                            unset($contracts_rows[$cr_key]);
                            continue;
                        }
                    }

                    if (!isset($contracts_dates[$cr['policy_id']])) {
                        $contracts_dates[$cr['policy_id']] = $cr['deadline'];
                    } else {
                        if ($cr['deadline']<$contracts_dates[$cr['policy_id']]) {
                            $contracts_dates[$cr['policy_id']] = $cr['deadline'];
                        }
                    }

                    $row_num = $cr['row_num'];
                    $additional_row_num = '';
                    if (preg_match('#([0-9]+)_([0-9]+)#', $cr['row_num'], $matches)) {
                        $row_num = $matches[1];
                        $additional_row_num = '_' . $matches[2];
                    }

                    $contracts_rows[$cr_key]['fee'] = floatval($cr['article_code']) + floatval($cr['article_width']) + floatval($cr['article_weight']);
                    $contracts_rows[$cr_key]['payment_num'] = sprintf('%d/%d%s', $row_num, $cr['number_payments'], $additional_row_num);
                    $contracts_rows[$cr_key]['article_deliverer_name'] = sprintf('%.2f', floatval($contracts_rows[$cr_key]['article_deliverer_name']));
                    unset($contracts_rows[$cr_key]['row_num']);
                    unset($contracts_rows[$cr_key]['number_payments']);
                }

                asort($contracts_dates);
                $contracts_filtered_by_dates = array_fill_keys(array_keys($contracts_dates), array());
                foreach ($contracts_rows as $cr) {
                    $contracts_filtered_by_dates[$cr['policy_id']][$cr['gt2_id']] = $cr;
                }

                $contracts_rows = array();
                foreach ($contracts_filtered_by_dates as $con_dat) {
                    asort($con_dat);
                    $contracts_rows = array_merge($contracts_rows, array_values($con_dat));
                }

                $debit_notes_list_ids = array_filter($debit_notes_list_ids);
                $dnp_rows = array();
                if (!empty($debit_notes_list_ids)) {
                    $sql = 'SELECT dnp.id as payment_id, dnp.full_num as payment_num, dn_row.value as debit_note_row, dn_payment_value.value ' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' AS dnp' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dn_row' . "\n" .
                           ' ON (dn_row.var_id="' . $dnp_add_vars[PAYMENT_DEBIT_NOTE_ROW] . '" AND dn_row.model_id=dnp.id AND dn_row.value IN ("' . implode('","', array_keys($debit_notes_list_ids)) . '"))' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dn_payment_value' . "\n" .
                           ' ON (dn_payment_value.var_id="' . $dnp_add_vars[PAYMENT_DEBIT_NOTE_VALUE] . '" AND dn_payment_value.model_id=dnp.id AND dn_row.num=dn_payment_value.num AND dn_payment_value.value!="")' . "\n" .
                           'WHERE dnp.type="' . PAYMENT_DEBIT_NOTE_TYPE_ID . '" AND dnp.active=1 AND dnp.deleted_by=0' . "\n";
                    $dnp_rows = $registry['db']->GetAll($sql);
                }

                // prepare the results for the final view
                $debit_notes_list = array();
                foreach ($contracts_rows as $cr) {
                    if (!empty($cr['debit_note_id'])) {
                        $res_key = $cr['debit_note_id'];
                        $debit_note_id = $cr['debit_note_id'];
                        $debit_note_num = $cr['debit_note_num'];
                    } else {
                        $res_key = uniqid();
                        $debit_note_id = '';
                        $debit_note_num = '';
                    }
                    if (!isset($debit_notes_list[$res_key])) {
                        $debit_notes_list[$res_key] = array(
                            'checked'      => 0,
                            'id'           => $debit_note_id,
                            'num'          => $debit_note_num,
                            'payment_date' => $cr['article_description'],
                            'encoded_data' => '',
                            'contracts'    => array(),
                            'rowspan'      => 0
                        );
                    }

                    if ($debit_note_id) {
                        $debit_notes_list[$res_key]['contracts'][$cr['debit_note_row_id']] = $cr;
                        $debit_notes_list[$res_key]['contracts'][$cr['debit_note_row_id']]['payments'] = array(
                            0 => array(
                                'id'      => 0,
                                'num'     => '',
                                'gt2_row' => '',
                                'value'   => 0
                            )
                        );
                    } else {
                        $debit_notes_list[$res_key]['contracts'][] = array_merge($cr, array('payments' => array(
                            0 => array(
                                'id'      => 0,
                                'num'     => '',
                                'gt2_row' => '',
                                'value'   => 0
                            )
                        )));
                    }
                    $debit_notes_list[$res_key]['rowspan']++;
                }

                foreach ($dnp_rows as $dnp_r) {
                    if (isset($debit_notes_list[$debit_notes_list_ids[$dnp_r['debit_note_row']]]['contracts'][$dnp_r['debit_note_row']])) {
                        if (!isset($debit_notes_list[$debit_notes_list_ids[$dnp_r['debit_note_row']]]['contracts'][$dnp_r['debit_note_row']]['payments'][$dnp_r['payment_id']])) {
                            $debit_notes_list[$debit_notes_list_ids[$dnp_r['debit_note_row']]]['contracts'][$dnp_r['debit_note_row']]['payments'][$dnp_r['payment_id']] = array(
                                'id'      => $dnp_r['payment_id'],
                                'num'     => $dnp_r['payment_num'],
                                'gt2_row' => '',
                                'value'   => 0
                            );
                        }
                        $debit_notes_list[$debit_notes_list_ids[$dnp_r['debit_note_row']]]['contracts'][$dnp_r['debit_note_row']]['payments'][$dnp_r['payment_id']]['value'] += $dnp_r['value'];

                        if (isset($debit_notes_list[$debit_notes_list_ids[$dnp_r['debit_note_row']]]['contracts'][$dnp_r['debit_note_row']]['payments'][0])) {
                            unset($debit_notes_list[$debit_notes_list_ids[$dnp_r['debit_note_row']]]['contracts'][$dnp_r['debit_note_row']]['payments'][0]);
                        }
                    }
                }
                unset($contracts_rows);
                $final_results = array_merge($final_results, $debit_notes_list);

                foreach ($final_results as $fr_key => $fr) {
                    $final_results[$fr_key]['encoded_data'] = base64_encode(json_encode($fr));
                }

                //sort by checked first
                uasort($final_results, function($a, $b) {
                    return ($a['checked'] > $b['checked']) ? -1 : 1;
                });

                $final_results['additional_options']['document_id'] = (!empty($filters['document_id']) ? $filters['document_id'] : 0);
                $final_results['additional_options']['precision'] = $precision;
                $final_results['additional_options']['payments_sum'] = $payments_sum;
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>
