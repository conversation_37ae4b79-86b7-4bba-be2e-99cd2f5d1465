<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            $report_name = '';
            if ($this->reportName) {
                $report_name = $this->reportName;
            } else {
                $report_name = $registry['report_type']['name'];
            }

            // $filters - array containing description of all filters
            $filters = array();

            // DEFINE INSURER FILTER
            $filter = array (
                'custom_id'       => 'insurer',
                'name'            => 'insurer',
                'type'            => 'autocompleter',
                'required'        => true,
                'width'           => 222,
                'label'           => $this->i18n('reports_insurer'),
                'help'            => $this->i18n('reports_insurer'),
                'autocomplete'    => array (
                    'search'       => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'insurer',
                    'execute_after'=> 'loadCustomersBranches',
                    'fill_options' => array('$insurer => <id>',
                        '$insurer_autocomplete => <name> <lastname>'),
                    'filters'      => array(
                        '<type>' => (string)CUSTOMER_TYPE_INSURER
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['insurer'] = $filter;


            //DEFINE BRANCH FILTER
            //prepare filters
            $filters['customer_branch'] = array (
                'custom_id' => 'customer_branch',
                'name'      => 'customer_branch',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_branch'),
                'help'      => $this->i18n('reports_branch'),
                'options'   => array()
            );


            // DEFINE CLIENT FILTER
            $filter = array (
                'custom_id'       => 'client',
                'name'            => 'client',
                'type'            => 'autocompleter',
                'width'           => 222,
                'label'           => $this->i18n('reports_client'),
                'help'            => $this->i18n('reports_client'),
                'autocomplete'    => array (
                    'search'       => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'client',
                    'fill_options' => array('$client => <id>',
                                            '$client_autocomplete => <name> <lastname>'),
                    'filters'      => array(
                        '<type>' => (string)CUSTOMER_TYPE_CLIENT
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );
            $filters['client'] = $filter;


            // DEFINE CONTRACT FILTER
            $sql = 'SELECT dt.id as option_value, dti18n.name as label ' . "\n" .
                   'FROM ' . DB_TABLE_CONTRACTS_TYPES . ' AS dt' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS dti18n' . "\n" .
                   '  ON (dt.id=dti18n.parent_id AND dti18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'WHERE dt.active=1 AND dt.deleted_by=0 AND dt.id NOT IN (' . EXCLUDE_CONTRACT_TYPE . ')' . "\n" .
                   'ORDER BY dti18n.name';
            $contracts_types = $registry['db']->GetAssoc($sql);

            $filter = array (
                'custom_id'       => 'contract_filt',
                'name'            => 'contract_filt',
                'type'            => 'autocompleter',
                'width'           => 222,
                'label'           => $this->i18n('reports_contract'),
                'help'            => $this->i18n('reports_contract'),
                'autocomplete'    => array (
                    'search'       => array('<custom_num>'),
                    'sort'         => array('<custom_num>'),
                    'type'         => 'contracts',
                    'clear'        => 1,
                    'suggestions'  => '<custom_num>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'contract_filt',
                    'fill_options' => array('$contract_filt => <id>',
                                            '$contract_filt_autocomplete => <custom_num>'),
                    'filters'      => array(
                        '<type>' => implode(',', array_keys($contracts_types))
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'contracts', 'contracts', 'ajax_select')
                )
            );
            $filters['contract_filt'] = $filter;


            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_date',
                'name'              => 'from_date',
                'type'              => 'date',
                'label'             => $this->i18n('reports_from_date'),
                'help'              => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;


            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_date',
                'name'            => 'to_date',
                'type'            => 'date',
                'label'           => $this->i18n('reports_to_date'),
                'help'            => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;


            //DEFINE CONTRACTS TYPES FILTER
            $options_documents_types = array();

            $casco_set = false;
            foreach($contracts_types as $contract_id => $contract_type) {
                if (($contract_id == CASCO_POLICIES_INDIVIDUAL || $contract_id == CASCO_POLICIES_GROUP)) {
                    if (!$casco_set) {
                        $casco_set = true;
                        $options_documents_types[] = array(
                            'label'         => $this->i18n('reports_contract_type_casco'),
                            'option_value'  => sprintf('%d_%d', CASCO_POLICIES_INDIVIDUAL, CASCO_POLICIES_GROUP)
                        );
                    }
                } else {
                    $options_documents_types[] = array(
                        'label'         => $contract_type,
                        'option_value'  => strval($contract_id)
                    );
                }
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'contract_type',
                'name'      => 'contract_type',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_contract_type'),
                'help'      => $this->i18n('reports_contract_type'),
                'options'   => $options_documents_types,
            );
            $filters['contract_type'] = $filter;

            // DEFINE DOCUMENT ID HIDDEN FILTER
            $filter = array (
                'custom_id' => 'document_id',
                'name'      => 'document_id',
                'type'      => 'hidden',
                'hidden'    => 1
            );
            $filters['document_id'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            if ($registry['request']->get('skip_session_filters')) {
                // set show type to 'check_bso' which has to be the default setting
                $registry->set('generated_report', 0, true);
                $filters['document_id']['value'] = '';
            }

            if (!empty($filters['document_id']['value'])) {
                $filters['insurer']['readonly'] = true;
                $filters['client']['readonly'] = true;
            }

            if ($registry['request']->get('call_from_insurance_report')) {
                $filters['from_date']['value'] = date('Y-m-d');
            }

            if (!empty($filters['insurer']['value'])) {
                require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
                $filters_branch  = array('sanitize' => true,
                                  'model_lang' => $registry['lang'],
                                  'where' => array ('c.parent_customer = ' . $filters['insurer']['value'],
                                      'c.subtype = \'branch\'',
                                      'c.active = 1'),
                                  'sort' => array('c.is_main DESC', 'ci18n.name ASC', 'c.id DESC'));
                $branches = Customers_Branches::search($registry, $filters_branch);
                $branches_options = array();
                foreach ($branches as $branch) {
                    $branches_options[] = array(
                        'label'        => $branch->get('name'),
                        'option_value' => $branch->get('id')
                    );
                }

                $filters['customer_branch']['options'] = $branches_options;
            }

            if ((!empty($filters['client']['value']) && empty($filters['client']['value_autocomplete'])) || (!empty($filters['insurer']['value']) && empty($filters['insurer']['value_autocomplete']))) {
                $customers_list = array();
                $customers_list[] = $filters['client']['value'];
                if (!empty($filters['insurer']['value'])) {
                    $customers_list[] = $filters['insurer']['value'];
                }

                // if the report has been triggered from document the name of the insurer has to be completed
                $sql_customer_name = 'SELECT `parent_id`, CONCAT(`name`, " ", `lastname`) FROM ' . DB_TABLE_CUSTOMERS_I18N . ' WHERE parent_id IN ("' . implode('","', $customers_list) . '") AND lang="' . $registry['lang'] . '"';
                $customers_list = $registry['db']->GetAssoc($sql_customer_name);

                foreach ($customers_list as $customer_id => $customer_name) {
                    if ($customer_id == $filters['client']['value']) {
                        $filters['client']['value_autocomplete'] = trim($customer_name);
                    }
                    if ($customer_id == $filters['insurer']['value']) {
                        $filters['insurer']['value_autocomplete'] = trim($customer_name);
                    }
                }
            }

            return $filters;
        }
    }
?>