<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
<body>
  <table border="1">
    <tr>
      <th>{#reports_th_material#|escape}</th>
      <th>{#reports_th_batch#|escape}</th>
      <th>{#reports_th_expiration_date#|escape}</th>
      <th>{#reports_th_quantity#|escape}</th>
      <th>{#reports_th_warehouse#|escape}</th>
    </tr>
    {foreach from=$reports_results.noms item='nom'}
      {foreach from=$nom.warehouses item='warehouse' name='warehouses'}
        {foreach from=$warehouse.batches item='batch' name='branches'}
          <tr>
            {if $smarty.foreach.warehouses.first && $smarty.foreach.branches.first}
              <td rowspan="{$nom.rowspan}" style="text-align: left; vertical-align: middle;">{$nom.name|escape|default:"&nbsp;"}</td>
            {/if}
            <td style="background-color: {$batch.color}; text-align: left;">{$batch.batch_code|escape|default:"&nbsp;"}</td>
            <td style="background-color: {$batch.color}; text-align: center; mso-number-format: 'dd.mm.yyyy'">{$batch.expire|date_format:#date_short#|escape|default:"&nbsp;"}</td>
            <td style="background-color: {$batch.color}; text-align: right; mso-number-format: '\#\ \#\#0\.0000';">{$batch.quantity|escape|default:"&nbsp;"}</td>
            {if $smarty.foreach.branches.first}
              <td rowspan="{$warehouse.rowspan}" style="text-align: left; vertical-align: middle;">{$warehouse.name|escape|default:"&nbsp;"}</td>
            {/if}
          </tr>
        {/foreach}
      {/foreach}
    {/foreach}
  </table>
</body>
</html>