<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table">
        <tr class="reports_title_row reports_title_centered_middle">
          <td width="200" class="t_border"><div style="width: 200px;">{#reports_th_material#|escape}</div></td>
          <td width="100" class="t_border"><div style="width: 100px;">{#reports_th_batch#|escape}</div></td>
          <td width="100" class="t_border"><div style="width: 100px;">{#reports_th_expiration_date#|escape}</div></td>
          <td width="100" class="t_border"><div style="width: 100px;">{#reports_th_quantity#|escape}</div></td>
          <td width="150"                 ><div style="width: 150px;">{#reports_th_warehouse#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.noms item='nom'}
          {foreach from=$nom.warehouses item='warehouse' name='warehouses'}
            {foreach from=$warehouse.batches item='batch' name='branches'}
              <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
                {if $smarty.foreach.warehouses.first && $smarty.foreach.branches.first}
                  <td class="t_v_border hleft t_border" rowspan="{$nom.rowspan}">{$nom.name|escape|default:"&nbsp;"}</td>
                {/if}
                <td class="t_v_border hleft t_border" style="background-color: {$batch.color};">{$batch.batch_code|escape|default:"&nbsp;"}</td>
                <td class="t_v_border hcenter t_border" style="background-color: {$batch.color};">{$batch.expire|date_format:#date_short#|escape|default:"&nbsp;"}</td>
                <td class="t_v_border hright t_border" style="background-color: {$batch.color};">{$batch.quantity|escape|default:"&nbsp;"}</td>
                {if $smarty.foreach.branches.first}
                  <td class="t_v_border hleft" rowspan="{$warehouse.rowspan}">{$warehouse.name|escape|default:"&nbsp;"}</td>
                {/if}
              </tr>
            {/foreach}
          {/foreach}
        {foreachelse}
          <tr class="t_odd">
            <td class="error" colspan="5">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="5"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>
      <table border="0">
        <tr>
          <td style="text-align: right;">{#reports_legend_shelf_life#|escape}</td>
          <td width="20" style="background-color: {$reports_results.settings.batch_color_shelf_life}; border: 1px solid gray;"></td>
        </tr>
        <tr>
          <td style="text-align: right;">{#reports_legend_expires_soon#|escape}</td>
          <td width="20" style="background-color: {$reports_results.settings.batch_color_expires_soon}; border: 1px solid gray;"></td>
        </tr>
        <tr>
          <td style="text-align: right;">{#reports_legend_expired#|escape}</td>
          <td width="20" style="background-color: {$reports_results.settings.batch_color_expired}; border: 1px solid gray;"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
</table>