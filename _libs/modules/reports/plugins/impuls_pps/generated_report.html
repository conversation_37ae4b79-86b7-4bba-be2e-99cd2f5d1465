<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="4"><div style="width: 189px;">{#reports_table_header_pps#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="4"><div style="width: 139px;">{#reports_table_header_assigned#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;" colspan="4">{#reports_table_header_documents#|escape}</td>
          <td style="text-align: center; vertical-align: middle;" colspan="5">{#reports_table_header_status#|escape}</td>
        </tr>
        <tr class="reports_title_row">
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="3"><div style="width: 139px;">{#reports_table_header_documents_type#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="3"><div style="width: 69px;">{#reports_table_header_documents_valid_from#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="3"><div style="width: 69px;">{#reports_table_header_documents_valid_to#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="3"><div style="width: 69px;">{#reports_table_header_documents_deadline#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 9px;">1</div></td>
          <td class="t_border" style="text-align: left; vertical-align: middle;"><div style="width: 139px;">{#reports_table_header_status_property#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="3"><div style="width: 139px;">{#reports_table_header_status_contract#|escape}</div></td>
          <td class="t_border" style="text-align: center; vertical-align: middle;" rowspan="3"><div style="width: 69px;">{#reports_table_header_status_contract_validity_term#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" rowspan="3"><div style="width: 290px;">{#reports_table_header_status_desc#|escape}</div></td>
        </tr>
        <tr class="reports_title_row">
          <td class="t_border" style="text-align: center; vertical-align: middle;" width="9">2</td>
          <td class="t_border" style="text-align: left; vertical-align: middle;">{#reports_table_header_status_rent#|escape}</td>
        </tr>
        <tr class="reports_title_row">
          <td class="t_border" style="text-align: center; vertical-align: middle;" width="9">3</td>
          <td class="t_border" style="text-align: left; vertical-align: middle;">{#reports_table_header_status_leased#|escape}</td>
        </tr>
        {foreach from=$reports_results.pps item=pps key=pps_id}
          {counter name='statuses_count' assign='statuses_count' start=1}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          {capture assign="pps_doc_count"}{if is_array($pps.documents)}{$pps.documents|@count}{else}0{/if}{/capture}
          {capture assign="pps_stat_count"}{if is_array($pps.statuses)}{$pps.statuses|@count}{else}0{/if}{/capture}
          {if $pps_doc_count > 3}
            {assign var="max_rows" value=$pps_doc_count}
            {capture assign="statuses_base_rowspan"}{math equation="x / 3" x=$max_rows format="%d"}{/capture}
            {capture assign="statuses_balance_rowspan"}{math equation="x % 3" x=$max_rows}{/capture}
            {capture assign="statuses_first_rowspan"}{if $statuses_balance_rowspan gt 0}{math equation="x + 1" x=$statuses_base_rowspan}{else}{$statuses_base_rowspan}{/if}{/capture}
            {capture assign="statuses_second_rowspan"}{if $statuses_balance_rowspan eq 2}{math equation="x + 1" x=$statuses_base_rowspan}{else}{$statuses_base_rowspan}{/if}{/capture}
            {capture assign="statuses_third_rowspan"}{if $statuses_balance_rowspan gt 0}{math equation="x - 1" x=$statuses_first_rowspan}{else}{$statuses_base_rowspan}{/if}{/capture}
          {else}
            {assign var="max_rows" value=$pps_stat_count}
            {assign var="statuses_first_rowspan" value=1}
            {assign var="statuses_second_rowspan" value=1}
            {assign var="statuses_third_rowspan" value=1}
          {/if}
          <tr class="{$current_row_class}">
            <td class="t_border t_v_border" rowspan="{$max_rows}" width="189"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$pps_id}" target="_blank">{$pps.pps_name|escape|default:"&nbsp;"}</a></td>
            <td class="t_border t_v_border" rowspan="{$max_rows}" width="139">{$pps.assigned_to|escape|default:"&nbsp;"}</td>
            <td class="t_border t_v_border" width="139">{$pps.documents.0.type_policy|escape|default:"&nbsp;"}</td>
            <td class="t_border t_v_border" width="69">{$pps.documents.0.type_policy_from|escape|default:"&nbsp;"}</td>
            <td class="t_border t_v_border" width="69">{$pps.documents.0.type_policy_to|escape|default:"&nbsp;"}</td>
            <td class="t_border t_v_border" width="69">{$pps.documents.0.policy_date|escape|default:"&nbsp;"}</td>
            <td class="t_border t_v_border" width="9" rowspan="{$statuses_first_rowspan}" style="text-align: center;">1</td>
            <td class="t_border t_v_border" width="139" rowspan="{$statuses_first_rowspan}">{$pps.statuses.property.customer_name|escape|default:"&nbsp;"}</td>
            <td class="t_border t_v_border" width="139" rowspan="{$statuses_first_rowspan}">{if $pps.statuses.property.contract_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$pps.statuses.property.contract_id}" target="_blank">{/if}{$pps.statuses.property.contract_num|escape|default:"&nbsp;"}{if $pps.statuses.property.contract_id}</a>{/if}</td>
            <td class="t_border t_v_border" width="69" rowspan="{$statuses_first_rowspan}">{$pps.statuses.property.contract_validity_term|escape|default:"&nbsp;"}</td>
            <td class="t_v_border" width="290" rowspan="{$statuses_first_rowspan}">{$pps.statuses.property.contract_description|escape|default:"&nbsp;"}</td>
          </tr>
          {if $pps_doc_count > $pps_stat_count}
            {foreach name=doc from=$pps.documents item=document}
              {if $smarty.foreach.doc.iteration gt 1}
                <tr class="{$current_row_class}">
                  <td class="t_border t_v_border" width="139">{$document.type_policy|escape|default:"&nbsp;"}</td>
                  <td class="t_border t_v_border" width="69">{$document.type_policy_from|escape|default:"&nbsp;"}</td>
                  <td class="t_border t_v_border" width="69">{$document.type_policy_to|escape|default:"&nbsp;"}</td>
                  <td class="t_border t_v_border" width="69">{$document.policy_date|escape|default:"&nbsp;"}</td>
                  {if $statuses_count eq 1}
                    {capture assign="new_status_row"}{math equation="x + 1" x=$statuses_first_rowspan}{/capture}
                  {else}
                    {capture assign="new_status_row"}{math equation="x + y + 1" x=$statuses_first_rowspan y=$statuses_second_rowspan}{/capture}
                  {/if}
                  {if $smarty.foreach.doc.iteration eq $new_status_row}
                    {counter name='statuses_count' assign='statuses_count'}
                    {if $statuses_count eq 2}
                      {assign var="rowspan" value=$statuses_second_rowspan}
                    {else}
                      {assign var="rowspan" value=$statuses_third_rowspan}
                    {/if}
                    <td class="t_border t_v_border" width="9" rowspan="{$rowspan}" style="text-align: center;">{$statuses_count}</td>
                    <td class="t_border t_v_border" width="139" rowspan="{$rowspan}">{if $statuses_count eq 2}{$pps.statuses.rent.customer_name|escape|default:"&nbsp;"}{else}{$pps.statuses.leased.customer_name|escape|default:"&nbsp;"}{/if}</td>
                    {capture assign="contract_num"}{if $statuses_count eq 2}{$pps.statuses.rent.contract_num|escape|default:"&nbsp;"}{else}{$pps.statuses.leased.contract_num|escape|default:"&nbsp;"}{/if}{/capture}
                    {capture assign="contract_id"}{if $statuses_count eq 2}{$pps.statuses.rent.contract_id}{else}{$pps.statuses.leased.contract_id}{/if}{/capture}
                    <td class="t_border t_v_border" width="139" rowspan="{$rowspan}">{if $contract_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$contract_id}" target="_blank">{/if}{$contract_num}{if $contract_id}</a>{/if}</td>
                    <td class="t_border t_v_border" width="69" rowspan="{$rowspan}">{if $statuses_count eq 2}{$pps.statuses.rent.contract_validity_term|escape|default:"&nbsp;"}{else}{$pps.statuses.leased.contract_validity_term|escape|default:"&nbsp;"}{/if}</td>
                    <td class="t_v_border" width="290" rowspan="{$rowspan}">{if $statuses_count eq 2}{$pps.statuses.rent.contract_description|escape|default:"&nbsp;"}{else}{$pps.statuses.leased.contract_description|escape|default:"&nbsp;"}{/if}</td>
                  {/if}
                </tr>
              {/if}
            {/foreach}
          {else}
            <tr class="{$current_row_class}">
              <td class="t_border t_v_border" width="139">{$pps.documents.1.type_policy|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="69">{$pps.documents.1.type_policy_from|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="69">{$pps.documents.1.type_policy_to|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="69">{$pps.documents.1.policy_date|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="9" style="text-align: center;">2</td>
              <td class="t_border t_v_border" width="139">{$pps.statuses.rent.customer_name|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="139">{if $pps.statuses.rent.contract_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$pps.statuses.rent.contract_id}" target="_blank">{/if}{$pps.statuses.rent.contract_num|escape|default:"&nbsp;"}{if $pps.statuses.rent.contract_id}</a>{/if}</td>
              <td class="t_border t_v_border" width="69">{$pps.statuses.rent.contract_validity_term|escape|default:"&nbsp;"}</td>
              <td class="t_v_border" width="290">{$pps.statuses.rent.contract_description|escape|default:"&nbsp;"}</td>
            </tr>
            <tr class="{$current_row_class}">
              <td class="t_border t_v_border" width="139">{$pps.documents.2.type_policy|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="69">{$pps.documents.2.type_policy_from|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="69">{$pps.documents.2.type_policy_to|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="69">{$pps.documents.2.policy_date|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="9" style="text-align: center;">3</td>
              <td class="t_border t_v_border" width="139">{$pps.statuses.leased.customer_name|escape|default:"&nbsp;"}</td>
              <td class="t_border t_v_border" width="139">{if $pps.statuses.leased.contract_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$pps.statuses.leased.contract_id}" target="_blank">{/if}{$pps.statuses.leased.contract_num|escape|default:"&nbsp;"}{if $pps.statuses.leased.contract_id}</a>{/if}</td>
              <td class="t_border t_v_border" width="69">{$pps.statuses.leased.contract_validity_term|escape|default:"&nbsp;"}</td>
              <td class="t_v_border" width="290">{$pps.statuses.leased.contract_description|escape|default:"&nbsp;"}</td>
            </tr>
          {/if}
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="11">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="11"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>