<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    {literal}
      <style type="text/css">
        br { mso-data-placement: same-cell; }
      </style>
    {/literal}
  </head>
  <body>
    {assign var=total_colspan value=19}
    {if $reports_additional_options.include_discount}
      {math equation='2+x' x=$total_colspan assign='total_colspan'}
    {/if}
    {if $reports_additional_options.show_notes}
      {math equation='1+x' x=$total_colspan assign='total_colspan'}
    {/if}
    {if $reports_additional_options.show_car_name}
      {math equation='1+x' x=$total_colspan assign='total_colspan'}
    {/if}
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="vertical-align: middle;"><strong>{#reports_manager#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_department#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_client#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_commission_for#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_insurance_group#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_insurance_type#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_policy_num#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_policy_status#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_deadline_date#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_payment_num#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_advance#|escape}</strong></td>
        {if $reports_additional_options.include_discount}
          <td style="vertical-align: middle;"><strong>{#reports_advance_discount#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_advance_with_discount#|escape}</strong></td>
        {/if}
        <td style="vertical-align: middle;"><strong>{#reports_tax#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_fee#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_premium#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_balance#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_debit_note_num#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_invoice_num#|escape}</strong></td>
        {if $reports_additional_options.show_car_name}
          <td style="vertical-align: middle;"><strong>{#reports_car_name#|escape}</strong></td>
        {/if}
        <td style="vertical-align: middle;"><strong>{#reports_car_code#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_car_rama#|escape}</strong></td>
        {if $reports_additional_options.show_notes}
          <td style="vertical-align: middle;"><strong>{#reports_notes#|escape}</strong></td>
        {/if}
      </tr>
      {foreach from=$reports_results item=result name=results}
        <tr>
          <td>
            {$result.contact_person_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.contact_person_department|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.customer_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.commission_for_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.insurance_group_name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.insurance_type_name|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format: \@;">
            {if $result.policy_num}{$result.policy_num|escape|default:"&nbsp;"}{else}<i>[{#reports_contract_no_number#}]</i>{/if}
          </td>
          <td style="mso-number-format: \@;">
            {if $result.canceled_policy}{#reports_status_cancelled#|escape}{else}{#reports_status_active#|escape}{/if}
          </td>
          <td style="mso-number-format:'dd\.mm\.yyyy';">
            {$result.deadline|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td align="right" style="mso-number-format: \@;">
            {$result.payment_num|escape|default:"0"} / {$result.total_payments|string_format:"%d"|escape|default:"0"}
          </td>
          <td align="right" style="mso-number-format: '0\.00';">
            {$result.subtotal|string_format:"%.2f"|default:"0.00"}
          </td>
          {if $reports_additional_options.include_discount}
            <td align="right" style="mso-number-format: '0\.00';" width="92">
              {$result.zp_discount|string_format:"%.2f"|default:"0.00"}
            </td>
            <td align="right" style="mso-number-format: '0\.00';" width="92">
              {$result.zp_with_discount|string_format:"%.2f"|default:"0.00"}
            </td>
          {/if}
          <td align="right" style="mso-number-format: '0\.00';">
            {$result.tax|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" style="mso-number-format: '0\.00';">
            {$result.fee|default:"&nbsp;"}
          </td>
          <td align="right" style="mso-number-format: '0\.00';">
            {$result.bruto_premium|string_format:"%.2f"|default:"0.00"}
          </td>
          <td align="right" style="mso-number-format: '0\.00';">
            {$result.balance|default:"&nbsp;"}
          </td>
          <td style="mso-number-format: \@;">
            {$result.debit_note_num|default:"&nbsp;"}
          </td>
          <td style="mso-number-format: \@;">
            {$result.invoice_num|default:"&nbsp;"}
          </td>
          {if $reports_additional_options.show_car_name}
            <td style="mso-number-format: \@;">
              {if $result.car_id}{$result.car_name|escape|default:"&nbsp;"}{else}-{/if}
            </td>
          {/if}
          <td style="mso-number-format: \@;">
            {if $result.car_id}{$result.car_code|escape|default:"&nbsp;"}{else}-{/if}
          </td>
          <td style="mso-number-format: \@;">
            {if $result.car_id}{$result.car_rama|escape|default:"&nbsp;"}{else}-{/if}
          </td>
          {if $reports_additional_options.show_notes}
            <td style="mso-number-format: \@;">
              {$result.notes|escape|nl2br|default:"&nbsp;"}
            </td>
          {/if}
        </tr>
      {foreachelse}
        <tr>
          <td colspan="{$total_colspan}}"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>
