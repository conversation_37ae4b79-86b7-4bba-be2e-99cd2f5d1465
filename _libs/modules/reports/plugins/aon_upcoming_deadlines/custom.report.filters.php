<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_deadline_date',
                'name'              => 'from_deadline_date',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/date_from_to_filter.html',
                'type'              => 'custom_filter',
                'width'             => 65,
                'additional_filter' => 'to_deadline_date',
                'first_filter_label'=> $this->i18n('from'),
                'label'             => $this->i18n('reports_date_deadline'),
                'help'              => $this->i18n('reports_date_deadline')
            );
            $filters['from_deadline_date'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_deadline_date',
                'name'            => 'to_deadline_date',
                'type'            => 'date',
                'width'           => 65,
                'label'           => mb_strtolower($this->i18n('to'), mb_detect_encoding($this->i18n('to'))),
                'help'            => mb_strtolower($this->i18n('to'), mb_detect_encoding($this->i18n('to')))
            );
            $filters['to_deadline_date'] = $filter;

            // DEFINE INSURANCE TYPE
            $filter = array (
                'custom_id'     => 'insurance_type',
                'name'          => 'insurance_type',
                'type'          => 'custom_filter',
                'actual_type'   => 'autocompleter',
                'custom_template'=> PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'width'         => 222,
                'label'         => $this->i18n('reports_insurance_type'),
                'help'          => $this->i18n('reports_insurance_type'),
                'autocomplete'  => array (
                                      'search'       => array('<code>', '<name>'),
                                      'sort'         => array('<name>', '<code>'),
                                      'type'         => 'nomenclatures',
                                      'clear'        => 1,
                                      'suggestions'  => '<name>',
                                      'buttons_hide' => 'search',
                                      'id_var'       => 'insurance_type',
                                      'fill_options' => array('$insurance_type => <id>',
                                                              '$insurance_type_autocomplete => <name>'),
                                      'filters'      => array('<type>' => (string)TYPE_INSURANCE_TYPES),
                                      'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
                                    )
            );
            $filters['insurance_type'] = $filter;

            //DEFINE EMPLOYEE FILTER
            $sql_managers_list = 'SELECT c.id as idx, c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label, c.active' . "\n" .
                                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 ' ON (c.id=ci18n.parent_id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'WHERE c.deleted_by=0 AND c.subtype="normal" AND c.type="' . PH_CUSTOMER_EMPLOYEE . '"' . (INCLUDE_INACTIVE_EMPLOYEES ? '' : ' AND c.active=1') . "\n" .
                                 'ORDER BY c.active DESC, CONCAT(ci18n.name, " ", ci18n.lastname) ASC' . "\n";
            $managers_list = $registry['db']->getAssoc($sql_managers_list);

            if (INCLUDE_INACTIVE_EMPLOYEES) {
                array_walk(
                    $managers_list,
                    function ($item, $key) use (&$managers_list) {
                        if (!$item['active']) {
                            $managers_list[$key]['class_name'] = 'inactive_option';
                            $managers_list[$key]['label'] = '* ' . $item['label'];
                        }
                    }
                );
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'manager',
                'name'      => 'manager',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_manager'),
                'help'      => $this->i18n('reports_manager'),
                'options'   => $managers_list,
            );
            $filters['manager'] = $filter;

            //DEFINE PAYMENT STATUS
            $sql_managers_list = 'SELECT c.id as idx, c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label' . "\n" .
                'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                ' ON (c.id=ci18n.parent_id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                'WHERE c.active=1 AND c.deleted_by=0 AND c.subtype="normal" AND c.type="' . PH_CUSTOMER_EMPLOYEE . '"' . "\n" .
                'ORDER BY CONCAT(ci18n.name, " ", ci18n.lastname) ASC' . "\n";
            $managers_list = $registry['db']->getAssoc($sql_managers_list);

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }
    }
?>