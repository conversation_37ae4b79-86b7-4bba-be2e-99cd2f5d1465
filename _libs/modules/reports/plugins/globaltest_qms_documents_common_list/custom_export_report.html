{if !$prepare_placeholder}
  <html>
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    </head>
    <body>
{/if}
  {if !$prepare_placeholder || $prepare_placeholder eq 'qms_documents_common_list_table'}
    <table border="1" cellpadding="2" cellspacing="0">
      <tr>
        <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="600"><div style="width: 600px">{#reports_code_and_name#|escape}</div></td>
        <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="80"><div style="width: 80px">{#reports_version#|escape}</div></td>
        <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="80"><div style="width: 80px">{#reports_date#|escape}</div></td>
        <td style="text-align: center; font-weight: bold; vertical-align: middle; background-color: #DFDFDF;" width="220"><div style="width: 220px">{#reports_subscribers#|escape}</div></td>
      </tr>
      <tr>
        <td colspan="4" style="text-align: center; font-weight: bold;">{#reports_nk#}</td>
      </tr>
      {foreach from=$reports_results.nk item=nk key=nomenclature_id}
        <tr>
          <td style="text-align: left">{$nk.code|escape}&nbsp;{$nk.name|escape}</td>
          <td style="text-align: left">{$nk.version|escape|default:"&nbsp;"}</td>
          <td style="text-align: center">{$nk.date|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$nk.subscribers|default:"&nbsp;"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="4" style="text-align: center;">-</td>
        </tr>
      {/foreach}
      <tr>
        <td colspan="4" style="text-align: center; font-weight: bold;">{#reports_opk#}</td>
      </tr>
      {foreach from=$reports_results.opk item=opk key=nomenclature_id}
        <tr>
          <td style="text-align: left">{$opk.code|escape}&nbsp;{$opk.name|escape}</td>
          <td style="text-align: left">{$opk.version|escape|default:"&nbsp;"}</td>
          <td style="text-align: center">{$opk.date|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$opk.subscribers|default:"&nbsp;"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="4" style="text-align: center;">-</td>
        </tr>
      {/foreach}
      <tr>
        <td colspan="4" style="text-align: center; font-weight: bold;">{#reports_fk#}</td>
      </tr>
      {foreach from=$reports_results.fk item=fk key=nomenclature_id}
        <tr>
          <td style="text-align: left">{$fk.code|escape}&nbsp;{$fk.name|escape}</td>
          <td style="text-align: left">{$fk.version|escape|default:"&nbsp;"}</td>
          <td style="text-align: center">{$fk.date|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$fk.subscribers|default:"&nbsp;"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="4" style="text-align: center;">-</td>
        </tr>
      {/foreach}
      <tr>
        <td colspan="4" style="text-align: center; font-weight: bold;">{#reports_spk#}</td>
      </tr>
      {foreach from=$reports_results.spk item=spk key=nomenclature_id}
        <tr>
          <td style="text-align: left">{$spk.code|escape}&nbsp;{$spk.name|escape}</td>
          <td style="text-align: left">{$spk.version|escape|default:"&nbsp;"}</td>
          <td style="text-align: center">{$spk.date|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$spk.subscribers|default:"&nbsp;"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="4" style="text-align: center;">-</td>
        </tr>
      {/foreach}
      <tr>
        <td colspan="4" style="text-align: center; font-weight: bold;">{#reports_rpk#}</td>
      </tr>
      {foreach from=$reports_results.rpk item=rpk key=nomenclature_id}
        <tr>
          <td style="text-align: left">{$rpk.code|escape}&nbsp;{$rpk.name|escape}</td>
          <td style="text-align: left">{$rpk.version|escape|default:"&nbsp;"}</td>
          <td style="text-align: center">{$rpk.date|escape|default:"&nbsp;"}</td>
          <td style="text-align: left">{$rpk.subscribers|default:"&nbsp;"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="4" style="text-align: center;">-</td>
        </tr>
      {/foreach}
    </table>
  {/if}
{if !$prepare_placeholder}
    </body>
  </html>
{/if}
