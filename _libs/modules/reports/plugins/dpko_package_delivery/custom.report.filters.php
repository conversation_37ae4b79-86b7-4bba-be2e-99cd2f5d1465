<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {

            define('DOCUMENT_TYPE_ID', 58);
            define('COURIER_PRIORITY', 'kurier_prioritet');

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
            'custom_id'             => 'customer',
                'name'              => 'customer',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_customer_name'),
                'help'              => $this->i18n('reports_customer_name'),
                'value'             => ''
            );
            $filters['customer'] = $filter;

            //DEFINE STATUSES FILTER
            //prepare substatuses
            require_once (PH_MODULES_DIR . 'documents/models/documents.dropdown.php');
            $params = array(0 => $registry,
                            'model_types' => array(DOCUMENT_TYPE_ID));
            $options_statuses = Documents_Dropdown::getStatuses($params);

            array_unshift($options_statuses, array('option_value' => 'all', 'label' => $this->i18n('all')));

            $filter = array(
                'custom_id'     => 'status',
                'name'          => 'status',
                'type'          => 'dropdown',
                'label'         => $this->i18n('reports_status'),
                'help'          => $this->i18n('reports_status'),
                'options'       => $options_statuses
            );
            $filters['status'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE PRIORITY FILTER
            $query = 'SELECT fo.option_value, fo.label FROM ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo' . "\n" .
                     'WHERE fo.parent_name="' . COURIER_PRIORITY . '" AND fo.lang="' . $registry['lang'] . '"' . "\n" .
                     'ORDER BY fo.position ASC';
            $records_priority = $registry['db']->GetAll($query);

            $options_priority = array();
            foreach ($records_priority as $record) {
                $options_priority[] = array(
                    'label'         => $record['label'],
                    'option_value'  => $record['option_value']
                );
            }

            $filter = array(
                'custom_id' => 'priority',
                'name'      => 'priority',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_priority'),
                'help'      => $this->i18n('reports_priority'),
                'options'   => $options_priority
            );
            $filters['priority'] = $filter;

            return $filters;
        }
    }
?>