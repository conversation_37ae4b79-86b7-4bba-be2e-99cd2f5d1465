<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border"><div style="width: 30px;">{#num#|escape}</div></td>
          <td class="t_border"><div style="width: 300px;">{#reports_material#|escape}</div></td>
          <td class="t_border"><div style="width: 30px;">{#reports_measure#|escape}</div></td>
          <td class="t_border"><div style="width: 100px;">{#reports_necessary_quantity#|escape}</div></td>
          <td class="t_border"><div style="width: 100px;">{#reports_available_quantity#|escape}</div></td>
          <td class="t_border"><div style="width: 100px;">{#reports_minimal_quantity#|escape}</div></td>
          <td class="t_border"><div style="width: 100px;">{#reports_shortage_quantity#|escape}</div></td>
          <td class="t_border"><div style="width: 100px;">{#reports_order_quantity#|escape}</div></td>
          <td><div style="width: 100px;">{#reports_delivery_term#|escape}</div></td>
        </tr>

        {foreach from=$reports_results key='rk' name='ri' item='nomenclature'}
          {capture assign='current_row_class'}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{if $nomenclature.shortage_quantity gt 0} row_pink{elseif $nomenclature.order_quantity gt 0} row_yellow{/if}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_border hright">{$smarty.foreach.ri.iteration}</td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$rk}">{$nomenclature.name|escape|default:"&nbsp;"}</a>
            </td>
            <td class="t_border">
              {$nomenclature.measure|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$nomenclature.quantity|escape|default:"0.00"}
            </td>
            <td class="t_border hright">
              {$nomenclature.available_quantity|escape|default:"0.00"}
            </td>
            <td class="t_border hright">
              {$nomenclature.minimal_quantity|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$nomenclature.shortage_quantity|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$nomenclature.order_quantity|escape|default:"&nbsp;"}
            </td>
            <td class="hright">
              {$nomenclature.delivery_term|escape|default:"&nbsp;"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="9">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="9"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>