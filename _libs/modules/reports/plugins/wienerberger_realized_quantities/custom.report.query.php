<?php
    Class Wienerberger_Realized_Quantities Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            // Prepare model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                // Default model language is the interface language
                $model_lang = $registry['lang'];
            }

            // Prepare an array for the final results
            $final_results = array();

            $vars = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model = "Project" AND model_type = 1';
            $vars = $registry['db']->GetAssoc($vars);

            // get projects in function of the filters
            $query = 'SELECT p.id, pi.name, (SELECT ti.name FROM tags_models tm JOIN tags t ON tm.tag_id=t.id AND tm.model="Project" AND t.section=4 JOIN ' . DB_TABLE_TAGS_I18N . ' ti ON ti.parent_id = t.id AND ti.lang = "' . $model_lang . '" WHERE p.id=tm.model_id ORDER BY t.id DESC LIMIT 1) as tag' . "\n" .
                     'FROM ' . DB_TABLE_PROJECTS . ' p' . "\n" .
                     'JOIN ' . DB_TABLE_PROJECTS_I18N . ' pi' . "\n" .
                     '  ON p.id = pi.parent_id AND pi.lang = "' . $model_lang . '"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' tm' . "\n" .
                     '  ON tm.model_id = p.id AND tm.model = "Project"' . "\n";

            // filter by product
            $filters['nomenclature'] = array_filter($filters['nomenclature']);
            if (!empty($filters['nomenclature'])) {
                $query .= 'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc1' . "\n" .
                          '  ON pc1.model_id = p.id AND pc1.var_id = ' . $vars['product_object_id'] . ' AND pc1.value IN (' . implode(', ', $filters['nomenclature']) . ')' . "\n";
            }
            // filter by customer type
            if (!empty($filters['customer_type']) && empty($filters['customer'])) {
                $query .= 'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                          '  ON c.id = p.customer AND c.type = ' . $filters['customer_type'] . "\n";
            }
            // filter by phase
            if (!empty($filters['phase'])) {
                $query .= 'JOIN ' . DB_TABLE_TAGS_MODELS . ' tm1' . "\n" .
                          '  ON tm1.model_id = p.id AND tm1.model = "Project" AND tm1.tag_id IN (' . implode(', ', $filters['phase']) . ')' . "\n";
            }
            // filter by roof
            if (!empty($filters['roof'])) {
                $query .= 'JOIN ' . DB_TABLE_TAGS_MODELS . ' tm2' . "\n" .
                    '  ON tm2.model_id = p.id AND tm2.model = "Project" AND tm2.tag_id IN (' . implode(', ', $filters['roof']) . ')' . "\n";
            }
            $query .= 'WHERE 1' . "\n";
            // filter by project ID (if provided)
            if (!empty($filters['project'])) {
                $query .='  AND p.id = ' . $filters['project'] . "\n";
            }
            // filter by dates
            if (!empty($filters['from_date'])) {
                $query .='  AND p.date_start >= "' . $filters['from_date'] . ' 00:00:00"' . "\n";
            }
            if (!empty($filters['to_date'])) {
                $query .='  AND p.date_end <= "' . $filters['to_date'] . ' 23:59:59"' . "\n";
            }
            // filter by customer
            if (!empty($filters['customer'])) {
                $query .='  AND p.customer = ' . $filters['customer'] . "\n";
            }
            // filter by manager
            if (!empty($filters['manager'])) {
                $query .='  AND p.manager = ' . $filters['manager'] . "\n";
            }

            $projects = $registry['db']->GetAssoc($query);
            // get variables for document type 4
            $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Document" AND model_type = 4';
            $dVars = $registry['db']->GetAssoc($query);
            $articles = array();
            foreach ($projects as $id => $p) {
                //get pojects articles
                $query = 'SELECT pc1.value as idx, pc1.value as id, pc2.value as name' . "\n";
                if (!empty($filters['for']) && in_array('data', $filters['for'])) {
                    $query .= ', pc3.value as distributor, pc4.value as measure, ROUND(pc5.value,2) as quantity,' . "\n" .
                              'pc6.value as month, ROUND(pc7.value, 2) as qual, pc8.value as validity' . "\n";
                }
                $query .= 'FROM ' . DB_TABLE_PROJECTS_CSTM . ' pc1' . "\n" .
                          'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc2' . "\n" .
                          '  ON pc1.model_id = pc2.model_id AND pc1.num = pc2.num AND pc2.var_id = ' . $vars['product_object'] . "\n";
                if (!empty($filters['for']) && in_array('data', $filters['for'])) {
                    $query .= 'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc3' . "\n" .
                              '  ON pc1.model_id = pc3.model_id AND pc1.num = pc3.num AND pc3.var_id = ' . $vars['distributor_object'] . "\n" .
                              'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc4' . "\n" .
                              '  ON pc1.model_id = pc4.model_id AND pc1.num = pc4.num AND pc4.var_id = ' . $vars['measure_product'] . "\n" .
                              'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc5' . "\n" .
                              '  ON pc1.model_id = pc5.model_id AND pc1.num = pc5.num AND pc5.var_id = ' . $vars['amount_name'] . "\n" .
                              'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc6' . "\n" .
                              '  ON pc1.model_id = pc6.model_id AND pc1.num = pc6.num AND pc6.var_id = ' . $vars['use_month_name'] . "\n" .
                              'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc7' . "\n" .
                              '  ON pc1.model_id = pc7.model_id AND pc1.num = pc7.num AND pc7.var_id = ' . $vars['use_qual_name'] . "\n" .
                              'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc8' . "\n" .
                              '  ON pc1.model_id = pc8.model_id AND pc1.num = pc8.num AND pc8.var_id = ' . $vars['date_validation'] . "\n";
                }
                $query .= 'WHERE pc1.model_id = ' . $id . ' AND pc1.var_id = ' . $vars['product_object_id'] . "\n";
                if (!empty($filters['nomenclature'])) {
                    // filter by nomenclature
                    $query .= ' AND pc1.value IN (' . implode(', ', $filters['nomenclature']) . ')';
                }
                $projects[$id]['articles'] = $registry['db']->GetAssoc($query);
                foreach($projects[$id]['articles'] as $a => $article) {
                    if (empty($a)) {
                        unset($projects[$id]['articles'][$a]);
                        continue;
                    }
                    $projects[$id]['articles'][$a] = array('data' => array($article), 'rowspan' => 1);
                    // prepare as much as we can for the seccond table
                    $articles[$a] = array('name' => $article['name']);
                    if (!empty($filters['for']) && in_array('data', $filters['for'])) {
                        if (empty($article[$a]['quantity'])) {
                            $articles[$a]['quantity'] = 0;
                        }
                        $articles[$a]['quantity'] += $article['quantity'];
                    }
                }
                if (count($projects[$id]['articles'])) {
                    $projects[$id]['rowspan'] = count($projects[$id]['articles']);
                } else {
                    unset($projects[$id]);
                    continue;
                }

                if (!empty($filters['for'])) {
                    if (in_array('offer', $filters['for'])) {
                        //get offers for the project even if we will not display their data
                        // we will need info for the second table
                        $query = 'SELECT d.id, g.article_id, d.full_num as num, nci.name as category, nm.name as measure, ROUND(g.price, 2) as price' . "\n" .
                            'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n";
                        // filter by customer type
                        if (!empty($filters['customer_type']) && empty($filters['customer'])) {
                            $query .= 'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                                '  ON c.id = d.customer AND c.type = ' . $filters['customer_type'] . "\n";
                        }
                        // filter by manager
                        if (!empty($filters['manager'])) {
                            $query .= 'JOIN ' . DB_TABLE_USERS . ' u' . "\n" .
                                '  ON u.employee = d.employee AND u.id = ' . $filters['manager'] . "\n";
                        }
                        $query .= 'JOIN ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                            '  ON g.model = "Document" AND g.model_id = d.id AND g.article_id IN (' . implode(', ', array_keys($projects[$id]['articles'])) . ')' . "\n" .
                            'JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' gi' . "\n" .
                            '  ON gi.parent_id = g.id AND gi.lang = "' . $model_lang . '"' . "\n" .
                            'JOIN ' . DB_TABLE_CATEGORIES_I18N . ' nci' . "\n" .
                            '  ON nci.parent_id = g.free_field2 AND nci.lang = "' . $model_lang . '"' . "\n" .
                            'JOIN ' . DB_TABLE_MEASURES . ' nm' . "\n" .
                            '  ON nm.id = gi.article_measure_name AND nm.lang = "' . $model_lang . '"' . "\n" .
                            'WHERE d.type = 1 AND d.project = ' . $id . "\n";
                        // filter by dates
                        if (!empty($filters['from_date'])) {
                            $query .='  AND d.date >= "' . $filters['from_date'] . '"' . "\n";
                        }
                        if (!empty($filters['to_date'])) {
                            $query .='  AND d.date <= "' . $filters['to_date'] . '"' . "\n";
                        }
                        // filter by customer
                        if (!empty($filters['customer'])) {
                            $query .='  AND d.customer = ' . $filters['customer'] . "\n";
                        }
                        $records = $registry['db']->GetAll($query);
                        $aPrices = array();
                        foreach ($records as $r => $record) {
                            foreach ($projects[$id]['articles'] as $a => $article) {
                                if ($article['data'][0]['id'] != $record['article_id']) {
                                    continue;
                                }
                                if (empty($projects[$id]['articles'][$a]['offer'])) {
                                    $projects[$id]['articles'][$a]['offer'] = array($record);
                                    $projects[$id]['articles'][$a]['rowspan'] = 1;
                                } else {
                                    //more than one offer for an article
                                    // add +1 for the rowspans
                                    $projects[$id]['articles'][$a]['offer'][] = $record;
                                    $projects[$id]['articles'][$a]['rowspan']++;
                                    $projects[$id]['rowspan']++;
                                }
                            }
                            // prepare data for the second table
                            if (empty($aPrices[$record['article_id']])) {
                                $aPrices[$record['article_id']] = array();
                            }
                            $aPrices[$record['article_id']][] = $record['price'];
                        }
                        foreach ($aPrices as $a => $prices) {
                            $articles[$a]['average'] = array_sum($prices) / count($prices);
                        }
                    }
                    if (in_array('statement', $filters['for'])) {
                        //get statements for the project
                        $query = 'SELECT g.article_id, nci.name as category, ROUND(g.quantity, 2) as quantity, ROUND(g.price, 2) as price,' . "\n" .
                                 'gi.article_deliverer_name as deliverer, ROUND(g.article_height, 2) as percent1, ROUND(g.free_field1, 4) as exw,' . "\n" .
                                 'ROUND(gi.free_text3, 3) as srt1, gi.article_alternative_deliverer_name as warehouse, ROUND(g.article_width, 3) as srt2,' . "\n" .
                                 'ROUND(g.article_weight, 2) as percent2, g.free_field4 as builder, ROUND(g.article_second_code, 3) as srt3, ROUND(g.free_field5, 2) as percent3' . "\n" .
                            'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n";
                        // filter by customer type
                        if (!empty($filters['customer_type']) && empty($filters['customer'])) {
                            $query .= 'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                                '  ON c.id = d.customer AND c.type = ' . $filters['customer_type'] . "\n";
                        }
                        // filter by manager
                        if (!empty($filters['manager'])) {
                            $query .= 'JOIN ' . DB_TABLE_USERS . ' u' . "\n" .
                                '  ON u.employee = d.employee AND u.id = ' . $filters['manager'] . "\n";
                        }
                        $query .= 'JOIN ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                            '  ON g.model = "Document" AND g.model_id = d.id AND g.article_id IN (' . implode(', ', array_keys($projects[$id]['articles'])) . ')' . "\n" .
                            'JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' gi' . "\n" .
                            '  ON gi.parent_id = g.id AND gi.lang = "' . $model_lang . '"' . "\n" .
                            'JOIN ' . DB_TABLE_CATEGORIES_I18N . ' nci' . "\n" .
                            '  ON nci.parent_id = g.free_field2 AND nci.lang = "' . $model_lang . '"' . "\n" .
                            'WHERE d.type = 5 AND d.project = ' . $id . "\n";
                        // filter by dates
                        if (!empty($filters['from_date'])) {
                            $query .='  AND d.date >= "' . $filters['from_date'] . '"' . "\n";
                        }
                        if (!empty($filters['to_date'])) {
                            $query .='  AND d.date <= "' . $filters['to_date'] . '"' . "\n";
                        }
                        // filter by customer
                        if (!empty($filters['customer'])) {
                            $query .='  AND d.customer = ' . $filters['customer'] . "\n";
                        }
                        $records = $registry['db']->GetAll($query);
                        foreach ($projects[$id]['articles'] as $a => $article) {
                            foreach ($records as $r => $record) {
                                if ($article['data'][0]['id'] != $record['article_id']) {
                                    continue;
                                }
                                if (empty($projects[$id]['articles'][$a]['statement'])) {
                                    $projects[$id]['articles'][$a]['statement'] = array($record);
                                } else {
                                    //more than one offer for an article
                                    // add +1 for the rowspans
                                    $projects[$id]['articles'][$a]['statement'][] = $record;
                                }
                            }
                        }
                        foreach ($projects[$id]['articles'] as $a => $article) {
                            if (!empty($projects[$id]['articles'][$a]['statement']) && $article['rowspan'] < count($projects[$id]['articles'][$a]['statement'])) {
                                $diff = count($projects[$id]['articles'][$a]['statement']) - $article['rowspan'];
                                $projects[$id]['articles'][$a]['rowspan'] += $diff;
                                $projects[$id]['rowspan'] += $diff;
                            }
                        }
                    }
                    if (in_array('schedule', $filters['for'])) {
                        //get schedules for the project
                        $query = 'SELECT dc1.value as article_id, dc2.value as period, dc3.value as measure,' . "\n" .
                                 '  ROUND(dc4.value) as quantity, dc5.value as distributor, dc6.value as validity' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n";
                        // filter by customer type
                        if (!empty($filters['customer_type']) && empty($filters['customer'])) {
                            $query .= 'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                                      '  ON c.id = d.customer AND c.type = ' . $filters['customer_type'] . "\n";
                        }
                        // filter by manager
                        if (!empty($filters['manager'])) {
                            $query .= 'JOIN ' . DB_TABLE_USERS . ' u' . "\n" .
                                      '  ON u.employee = d.employee AND u.id = ' . $filters['manager'] . "\n";
                        }
                        $query .= 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc1' . "\n" .
                                  '  ON dc1.model_id = d.id AND dc1.var_id = ' . $dVars['product_field_id'] . ' AND dc1.value IN (' . implode(', ', array_keys($projects[$id]['articles'])) . ')' . "\n" .
                                  'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc2' . "\n" .
                                  '  ON dc2.model_id = d.id AND dc2.num = dc1.num AND dc2.var_id = ' . $dVars['period_field'] . "\n" .
                                  'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc3' . "\n" .
                                  '  ON dc3.model_id = d.id AND dc3.num = dc1.num AND dc3.var_id = ' . $dVars['measure_field'] . "\n" .
                                  'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc4' . "\n" .
                                  '  ON dc4.model_id = d.id AND dc4.num = dc1.num AND dc4.var_id = ' . $dVars['amount_field'] . "\n" .
                                  'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc5' . "\n" .
                                  '  ON dc5.model_id = d.id AND dc5.num = dc1.num AND dc5.var_id = ' . $dVars['distributor_field'] . "\n" .
                                  'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc6' . "\n" .
                                  '  ON dc6.model_id = d.id AND dc6.num = dc1.num AND dc6.var_id = ' . $dVars['validity_to'] . "\n" .
                                  'WHERE d.type = 4 AND d.project = ' . $id . "\n";
                        // filter by dates
                        if (!empty($filters['from_date'])) {
                            $query .='  AND d.date >= "' . $filters['from_date'] . '"' . "\n";
                        }
                        if (!empty($filters['to_date'])) {
                            $query .='  AND d.date <= "' . $filters['to_date'] . '"' . "\n";
                        }
                        // filter by customer
                        if (!empty($filters['customer'])) {
                            $query .='  AND d.customer = ' . $filters['customer'] . "\n";
                        }
                        $records = $registry['db']->GetAll($query);
                        foreach ($projects[$id]['articles'] as $a => $article) {
                            foreach ($records as $r => $record) {
                                if ($article['data'][0]['id'] != $record['article_id']) {
                                    continue;
                                }
                                if (empty($projects[$id]['articles'][$a]['schedule'])) {
                                    $projects[$id]['articles'][$a]['schedule'] = array($record);
                                } else {
                                    //more than one offer for an article
                                    // add +1 for the rowspans
                                    $projects[$id]['articles'][$a]['schedule'][] = $record;
                                }
                            }
                        }
                        if (!empty($projects[$id]['articles'][$a]['schedule'])) {
                            foreach ($projects[$id]['articles'] as $a => $article) {
                                if ($article['rowspan'] < count($projects[$id]['articles'][$a]['schedule'])) {
                                    $diff = count($projects[$id]['articles'][$a]['schedule']) - $article['rowspan'];
                                    $projects[$id]['articles'][$a]['rowspan'] += $diff;
                                    $projects[$id]['rowspan'] += $diff;
                                }
                            }
                        }
                    }
                    if (in_array('sap', $filters['for'])) {
                        //get schedules for the project
                        $query = 'SELECT dc1.value as article_id, dc2.value as invoice_type, dc3.value as incoterms,' . "\n" .
                            '  dc4.value as distributor, dc5.value as warehouse, dc6.value as builder,' . "\n" .
                            '  dc7.value as region, dc8.value as region_ps, ROUND(dc9.value, 2) as quantity,' . "\n" .
                            '  dc10.value as date, ROUND(dc11.value, 2) as price' . "\n" .
                            'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n";
                        // filter by customer type
                        if (!empty($filters['customer_type']) && empty($filters['customer'])) {
                            $query .= 'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                                '  ON c.id = d.customer AND c.type = ' . $filters['customer_type'] . "\n";
                        }
                        // filter by manager
                        if (!empty($filters['manager'])) {
                            $query .= 'JOIN ' . DB_TABLE_USERS . ' u' . "\n" .
                                '  ON u.employee = d.employee AND u.id = ' . $filters['manager'] . "\n";
                        }
                        $query .= 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc1' . "\n" .
                            '  ON dc1.model_id = d.id AND dc1.var_id = ' . $dVars['product_id'] . ' AND dc1.value IN (' . implode(', ', array_keys($projects[$id]['articles'])) . ')' . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc2' . "\n" .
                            '  ON dc2.model_id = d.id AND dc2.num = dc1.num AND dc2.var_id = ' . $dVars['invoice_type'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc3' . "\n" .
                            '  ON dc3.model_id = d.id AND dc3.num = dc1.num AND dc3.var_id = ' . $dVars['incoterms'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc4' . "\n" .
                            '  ON dc4.model_id = d.id AND dc4.num = dc1.num AND dc4.var_id = ' . $dVars['deliverer'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc5' . "\n" .
                            '  ON dc5.model_id = d.id AND dc5.num = dc1.num AND dc5.var_id = ' . $dVars['warehouse_name'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc6' . "\n" .
                            '  ON dc6.model_id = d.id AND dc6.num = dc1.num AND dc6.var_id = ' . $dVars['builder_name'] . "\n" .

                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc7' . "\n" .
                            '  ON dc7.model_id = d.id AND dc7.num = dc1.num AND dc7.var_id = ' . $dVars['region_name'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc8' . "\n" .
                            '  ON dc8.model_id = d.id AND dc8.num = dc1.num AND dc8.var_id = ' . $dVars['region_ps_name'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc9' . "\n" .
                            '  ON dc9.model_id = d.id AND dc9.num = dc1.num AND dc9.var_id = ' . $dVars['invoice_quantity'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc10' . "\n" .
                            '  ON dc10.model_id = d.id AND dc10.num = dc1.num AND dc10.var_id = ' . $dVars['invoice_date'] . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc11' . "\n" .
                            '  ON dc11.model_id = d.id AND dc11.num = dc1.num AND dc11.var_id = ' . $dVars['invoice_price'] . "\n" .
                            'WHERE d.type = 4 AND d.project = ' . $id . "\n";
                        // filter by dates
                        if (!empty($filters['from_date'])) {
                            $query .='  AND d.date >= "' . $filters['from_date'] . '"' . "\n";
                        }
                        if (!empty($filters['to_date'])) {
                            $query .='  AND d.date <= "' . $filters['to_date'] . '"' . "\n";
                        }
                        // filter by customer
                        if (!empty($filters['customer'])) {
                            $query .='  AND d.customer = ' . $filters['customer'] . "\n";
                        }
                        $records = $registry['db']->GetAll($query);
                        foreach ($records as $r => $record) {
                            foreach ($projects[$id]['articles'] as $a => $article) {
                                if ($article['data'][0]['id'] != $record['article_id']) {
                                    continue;
                                }
                                if (empty($projects[$id]['articles'][$a]['sap'])) {
                                    $projects[$id]['articles'][$a]['sap'] = array($record);
                                } else {
                                    //more than one offer for an article
                                    // add +1 for the rowspans
                                    $projects[$id]['articles'][$a]['sap'][] = $record;
                                }
                            }
                            // prepare data for the secind table
                            if (isset($articles[$record['article_id']])) {
                                if (empty($articles[$record['article_id']]['invoice_quantity'])) {
                                    $articles[$record['article_id']]['invoice_quantity'] = 0;
                                }
                                $articles[$record['article_id']]['invoice_quantity'] += $record['quantity'];
                            }
                        }
                        if (!empty($projects[$id]['articles'][$a]['sap'])) {
                            foreach ($projects[$id]['articles'] as $a => $article) {
                                if ($article['rowspan'] < count($projects[$id]['articles'][$a]['sap'])) {
                                    $diff = count($projects[$id]['articles'][$a]['sap']) - $article['rowspan'];
                                    $projects[$id]['articles'][$a]['rowspan'] += $diff;
                                    $projects[$id]['rowspan'] += $diff;
                                }
                            }
                        }
                    }
                }
                $projects[$id]['articles'] = array_values($projects[$id]['articles']);
            }
            if (!empty($projects) && !empty($articles)) {
                // prepare summary/average table
                if (empty($filters['for']) || !in_array('data', $filters['for'])) {
                    //get pojects articles
                    $query = 'SELECT pc1.value as id, ROUND(pc5.value, 2) as quantity' . "\n" .
                             'FROM ' . DB_TABLE_PROJECTS_CSTM . ' pc1' . "\n" .
                             'JOIN ' . DB_TABLE_PROJECTS_CSTM . ' pc5' . "\n" .
                             '  ON pc1.model_id = pc5.model_id AND pc1.num = pc5.num AND pc5.var_id = ' . $vars['amount_name'] . "\n" .
                             'WHERE pc1.model_id IN (' . implode(', ', array_keys($projects)) . ') AND pc1.var_id = ' . $vars['product_object_id'];
                    $records = $registry['db']->GetAssoc($query);
                    foreach($records as $k => $v) {
                        if (isset($articles[$k])) {
                            $articles[$k]['quantity'] = $v;
                        }
                    }
                }
                if (empty($filters['for']) || !in_array('offer', $filters['for'])) {
                    $query = 'SELECT g.article_id, g.price' . "\n" .
                             'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n";
                    // filter by customer type
                    if (!empty($filters['customer_type']) && empty($filters['customer'])) {
                        $query .= 'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                            '  ON c.id = d.customer AND c.type = ' . $filters['customer_type'] . "\n";
                    }
                    // filter by manager
                    if (!empty($filters['manager'])) {
                        $query .= 'JOIN ' . DB_TABLE_USERS . ' u' . "\n" .
                            '  ON u.employee = d.employee AND u.id = ' . $filters['manager'] . "\n";
                    }
                    $query .= 'JOIN ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                              '  ON g.model = "Document" AND g.model_id = d.id AND g.article_id IN (' . implode(', ', array_keys($articles)) . ')' . "\n" .
                              'WHERE d.type = 1 AND d.project IN (' . implode(',', array_keys($projects)) . ")\n";
                    // filter by dates
                    if (!empty($filters['from_date'])) {
                        $query .='  AND d.date >= "' . $filters['from_date'] . '"' . "\n";
                    }
                    if (!empty($filters['to_date'])) {
                        $query .='  AND d.date <= "' . $filters['to_date'] . '"' . "\n";
                    }
                    // filter by customer
                    if (!empty($filters['customer'])) {
                        $query .='  AND d.customer = ' . $filters['customer'] . "\n";
                    }
                    $records = $registry['db']->GetAll($query);
                    $aPrices = array();
                    foreach ($records as $k => $v) {
                        // prepare data for the second table
                        if (empty($aPrices[$k])) {
                            $aPrices[$k] = array();
                        }
                        $aPrices[$k][] = $v;
                    }
                    foreach ($aPrices as $k => $v) {
                        if (isset($articles[$k])) {
                            $articles[$k]['average'] = round(array_sum($v) / count($v), 2);
                        }
                    }
                }
                if (empty($filters['for']) || !in_array('sap', $filters['for'])) {
                    //get schedules for the project
                    $query = 'SELECT dc1.value as article_id, ROUND(dc9.value, 2) as quantity' . "\n" .
                             'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n";
                    // filter by customer type
                    if (!empty($filters['customer_type']) && empty($filters['customer'])) {
                        $query .= 'JOIN ' . DB_TABLE_CUSTOMERS . ' c' . "\n" .
                            '  ON c.id = d.customer AND c.type = ' . $filters['customer_type'] . "\n";
                    }
                    // filter by manager
                    if (!empty($filters['manager'])) {
                        $query .= 'JOIN ' . DB_TABLE_USERS . ' u' . "\n" .
                            '  ON u.employee = d.employee AND u.id = ' . $filters['manager'] . "\n";
                    }
                    $query .= 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc1' . "\n" .
                        '  ON dc1.model_id = d.id AND dc1.var_id = ' . $dVars['product_id'] . ' AND dc1.value IN (' . implode(', ', array_keys($articles)) . ')' . "\n" .
                        'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dc9' . "\n" .
                        '  ON dc9.model_id = d.id AND dc9.num = dc1.num AND dc9.var_id = ' . $dVars['invoice_quantity'] . "\n" .
                        'WHERE d.type = 4 AND d.project IN (' . implode(array_keys($projects)) . ")\n";
                    // filter by dates
                    if (!empty($filters['from_date'])) {
                        $query .='  AND d.date >= "' . $filters['from_date'] . '"' . "\n";
                    }
                    if (!empty($filters['to_date'])) {
                        $query .='  AND d.date <= "' . $filters['to_date'] . '"' . "\n";
                    }
                    // filter by customer
                    if (!empty($filters['customer'])) {
                        $query .='  AND d.customer = ' . $filters['customer'] . "\n";
                    }
                    $records = $registry['db']->GetAssoc($query);
                    foreach ($records as $k => $v) {
                        // prepare data for the secind table
                        if (isset($articles[$k])) {
                            if (empty($articles[$k]['invoice_quantity'])) {
                                $articles[$k]['invoice_quantity'] = 0;
                            }
                            $articles[$k]['invoice_quantity'] += $v;
                        }
                    }
                }
            }

            $final = array('projects' => $projects, 'filters' => $filters, 'summary' => $articles);
            if (!empty($filters['paginate'])) {
                $results = array($final, 0);
            } else {
                $results = $final;
            }

            return $results;
        }
    }
?>