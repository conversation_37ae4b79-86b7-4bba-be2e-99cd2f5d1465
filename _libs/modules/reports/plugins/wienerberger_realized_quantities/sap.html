{if $header}
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" colspan="10">{#reports_for_sap#|escape}</td>
{elseif $subheader}
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_invoice_type#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_incoterms#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_distributor#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_warehouse#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_builder#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_region#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_region_ps#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_quantity#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_date#|escape}</td>
  <td {if !$export}class="t_border"{else}align="center"{/if} nowrap="nowrap" >{#reports_price#|escape}</td>
{elseif $item}
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.invoice_type|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.incoterms|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.distributor|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.warehouse|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.builder|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.region|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.region_ps|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border hright"{else}align="right"{/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.quantity|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border" {/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.date|date_format:'%d.%m.%Y'|escape|default:"&nbsp;"}
  </td>
  <td {if !$export}class="t_border t_v_border hright"{else}align="right"{/if} nowrap="nowrap" style="vertical-align: middle;">
    {$item.price|escape|default:"&nbsp;"}
  </td>
{else}
  <td {if !$export}class="t_border{if $is_last} t_v_border{/if}" {/if} colspan="10">&nbsp;</td>
{/if}