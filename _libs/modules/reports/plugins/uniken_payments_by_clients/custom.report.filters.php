<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_date',
                'name'              => 'from_date',
                'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
                'type'              => 'custom_filter',
                'width'             => 65,
                'required'          => 1,
                'additional_filter' => 'to_date',
                'first_filter_label'=> $this->i18n('reports_from_date'),
                'label'             => $this->i18n('reports_date_filter'),
                'help'              => $this->i18n('reports_date_filter')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_date',
                'name'            => 'to_date',
                'type'            => 'date',
                'width'           => 65,
                'label'           => $this->i18n('reports_to_date'),
                'help'            => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            // DEFINE SUBCONTRACTOR FILTER
            $filter = array (
                'custom_id'    => 'subcontractor',
                'name'         => 'subcontractor',
                'type'         => 'custom_filter',
                'actual_type'  => 'autocompleter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'width'        => 222,
                'label'        => $this->i18n('reports_filter_subcontractor'),
                'help'         => $this->i18n('reports_filter_subcontractor'),
                'autocomplete'      => array(
                    'search' => array('<name>'),
                    'sort'         => array('<name>'),
                    'type'         => 'customers',
                    'clear'        => 1,
                    'suggestions'  => '<name> <lastname>',
                    'buttons_hide' => 'search',
                    'id_var'       => 'subcontractor',
                    'fill_options' => array('$subcontractor => <id>',
                                            '$subcontractor_autocomplete => <name> <lastname>',
                    ),
                    'filters' => array(
                        '<type>'    => CUSTOMER_SUBCONTRACTOR_TYPE_ID
                    ),
                    'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
                )
            );

            $filters['subcontractor'] = $filter;
/*
            //DEFINE CONTRACT STATUS FILTER
            //prepare filters
            $filter = array (
                'custom_id' => 'contract_statuses',
                'name'      => 'contract_statuses',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_contract_statuses'),
                'help'      => $this->i18n('reports_contract_statuses'),
                'options'   => array(
                    array(
                        'label'        => $this->i18n('reports_contract_statuses_active'),
                        'option_value' => CONTRACT_SUBSTATUS_SIGNED
                    ),
                    array(
                        'label'        => $this->i18n('reports_contract_statuses_inactive'),
                        'option_value' => CONTRACT_SUBSTATUS_FINISHED
                    )
                )
            );
            $filters['contract_statuses'] = $filter;
*/
            //DEFINE SHOW FILTER
            //prepare filters
            $filter = array (
                'custom_id'          => 'contract_show',
                'name'               => 'contract_show',
                'type'               => 'dropdown',
                'label'              => $this->i18n('reports_contract_show'),
                'help'               => $this->i18n('reports_contract_show'),
                'first_option_label' => $this->i18n('all'),
                'options'            => array(
                    array(
                        'label'        => $this->i18n('reports_contract_show_paid'),
                        'option_value' => 'paid'
                    ),
                    array(
                        'label'        => $this->i18n('reports_contract_show_unpaid'),
                        'option_value' => 'unpaid'
                    )
                )
            );
            $filters['contract_show'] = $filter;

            $magic_articles = array_filter(preg_split('/\s*,\s*/', MAGIC_ARTICLE_ID));

            if (count($magic_articles) > 1) {
                $sql = 'SELECT `name` as label, `parent_id` as option_value FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' WHERE `parent_id` IN ("' . implode('","', $magic_articles) . '") AND `lang`="' . $registry['lang'] . '"';

                //DEFINE SERVICE FILTER
                //prepare filters
                $filter = array (
                    'custom_id'          => 'service',
                    'name'               => 'service',
                    'type'               => 'dropdown',
                    'label'              => $this->i18n('reports_service'),
                    'help'               => $this->i18n('reports_service'),
                    'first_option_label' => $this->i18n('all'),
                    'options'            => $registry['db']->GetAll($sql)
                );
                $filters['service'] = $filter;
            }

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }
            return $filters;
        }
    }
?>
