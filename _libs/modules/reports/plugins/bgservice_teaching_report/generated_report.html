<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_teacher#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_protocol_accepted_by#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_topic#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_people_count#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_start#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_finish#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_total_time#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_rests#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_duration#|escape}</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}{if $result.deleted_by} t_deleted{/if}">
            <td class="t_border hright" nowrap="nowrap" width="25" rowspan="{$result.doc_rows}">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border" nowrap="nowrap">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.teacher_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {if !empty($result.contact_person_name)}
                {$result.contact_person_name|escape|default:"&nbsp;"}
              {else}
                {$result.participants_string|escape|default:"&nbsp;"}
              {/if}
            </td>
            <td class="t_border">
              {$result.document_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$result.participants_count|escape|default:"0"}
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.instruction_start|date_format:#date_mid#|default:"&nbsp;"}
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.instruction_finish|date_format:#date_mid#|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$result.instruction_duration|escape|default:"0:00"}
            </td>
            <td class="t_border hright">
              {$result.time_rest|escape|default:"0:00"}
            </td>
            <td class="hright">
              {$result.total_time|escape|default:"0:00"}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td class="error" colspan="11">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr class="{cycle values='t_odd,t_even'} row_blue t_border">
          <td class="hright t_border" colspan="8"><strong>{#reports_total#|escape}:</strong></td>
          <td class="hright t_border"><strong>{$reports_additional_options.total_duration_time_formatted|escape|default:"0:00"}</strong></td>
          <td class="hright t_border"><strong>{$reports_additional_options.total_rest_time_formatted|escape|default:"0:00"}</strong></td>
          <td class="hright"><strong>{$reports_additional_options.total_time_formatted|escape|default:"0:00"}</strong></td>
        </tr>
        <tr>
          <td class="t_footer" colspan="11"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
