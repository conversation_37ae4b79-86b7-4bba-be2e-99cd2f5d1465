<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'         => 'customer',
                'name'              => 'customer',
                'type'              => 'autocompleter',
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_customer'),
                'help'              => $this->i18n('reports_customer'),
                'value'             => ''
            );
            $filters['customer'] = $filter;

            //DEFINE PROJECTS' FILTER
            $filter = array (
                'custom_id'             => 'project',
                'name'                  => 'project',
                'type'                  => 'autocompleter',
                'autocomplete_type'     => 'projects',
                'autocomplete_buttons'  => 'clear',
                'label'                 => $this->i18n('reports_project'),
                'help'                  => $this->i18n('reports_project_help'),
                'value'                 => ''
            );
            $filters['project'] = $filter;

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;


            //DEFINE EMPLOYEE FILTER
            //get employees' options
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $filters_employees = array('model_lang'    => $registry['lang'],
                                       'sanitize'      => true,
                                       'sort'          => array('ui18n.firstname', 'ui18n.lastname'),
                                       'where'         => array('u.hidden=0',
                                                                'u.active=1'));
            $employees = Users::search($registry, $filters_employees);

            //prepare documents' types groups
            $options_employees = array();

            foreach($employees as $employee) {
                $options_employees[] = array(
                    'label'         => $employee->get('firstname') . ' ' . $employee->get('lastname'),
                    'option_value'  => $employee->get('id')
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'employee',
                'name'      => 'employee',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_employee'),
                'help'      => $this->i18n('reports_employee'),
                'options'   => $options_employees,
            );
            $filters['employee'] = $filter;

            //DEFINE PARTICIPANT FILTER
            $filter = array (
                'custom_id' => 'participant',
                'name' => 'participant',
                'type' => 'text',
                'label' => $this->i18n('reports_participant'),
                'help' => $this->i18n('reports_participant_help')
            );
            $filters['participant'] = $filter;

            return $filters;
        }
    }
?>