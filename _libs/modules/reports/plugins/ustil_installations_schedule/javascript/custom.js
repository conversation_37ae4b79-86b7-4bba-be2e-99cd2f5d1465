var reportUstilInstallationsSchedule = {
    msg_no_selected_records: 'No selected records?',
    msg_more_than_one_consumable: 'There are teams with more than one consumable?',
    msg_simultaneous_installations: 'There are employees with simultaneous installations?',
    msg_confirm_date_time_change: 'Are you sure?',
    msg_select_team_for_all_requests: 'Please, select team for all requests!',

//    _init: function () {
//        // TODO: Here we can make a functionality to detect changes for each row and check the checkbox for it.
//        // Another thing related to this is to make a function, which will execute on click on the checkbox and
//        // will mark it as not auto checkable. In other words - the checkboxes can have class "detect_changes"
//        // or something like that and when the user checks it - the class will be removed. The idea is - after
//        // the user touches the checkbox, he become responsible of its status (checked/unchecked) - i.e. the
//        // changes detection is turned off.
//
//        // This code can be used to find the parent of a changed element
//        $$('.reports_table [name]').each(
//            function (e) {
//                while (e.tagName != 'TR') {
//                    e = e.parentNode;
//                }
//            }
//        );
//    },

    changeDateTime: function (e, type, client_distributor_id) {
        var regexp = new RegExp(type + 'box')
        if (e.className.match(regexp)) {
            var current_request_id = e.id.replace(/^.+_(\d+)$/, '$1');
            var current_checkbox = $('request_' + current_request_id);
            if (current_checkbox.checked) {
                var other_checkboxes = $$('input[type="checkbox"][id^="request_"]:checked:not([id^="' + current_checkbox.id + '"]).' + client_distributor_id);
                if (other_checkboxes.length) {
                    var field_name = e.id.replace(/^(.+)_\d+$/, '$1');
                    var value = e.value;
                    var value_formatted = $(field_name + '_formatted_' + current_request_id).value;
                    for (var i = 0; i < other_checkboxes.length; i++) {
                        var request_id = other_checkboxes[i].value;
                        $(field_name + '_' + request_id).value = value;
                        $(field_name + '_formatted_' + request_id).value = value_formatted;
                        var field_readonly = $(field_name + '_readonly_' + request_id);
                        var field_readonly_formatted = $(field_name + '_readonly_formatted_' + request_id);
                        if (field_readonly && typeof field_readonly != 'undefined') {
                            field_readonly.value = value;
                        }
                        if (field_readonly_formatted && typeof field_readonly_formatted != 'undefined') {
                            field_readonly_formatted.value = value_formatted;
                        }
                    }
                }
            }
        }
    },

    toggleRelatedOrders: function (request_id) {
        var related_orders_container = $('related_orders_' + request_id);
        if (related_orders_container.style.display == 'none') {
            related_orders_container.style.display = '';
            var pos = getMousePosition();
            related_orders_container.style.top = (pos[1] - related_orders_container.offsetHeight + 10) + 'px';
            related_orders_container.style.left = (pos[0] + 15) + 'px';
        } else {
            related_orders_container.style.display = 'none';
        }
    },

    _getRequestTeam: function (request_id) {
        return $$('input[type="hidden"][id^="inastallation_man_id_' + request_id + '_"][value!=""]:not(.input_inactive)');
    },

    save: function (e) {
        var requests = $$('input[type="checkbox"][id^="request_"]:checked');
        if (requests.length) {
            for (var i = 0; i < requests.length; i++) {
                var tms = $$('input[type="hidden"][id^="request_team_' + requests[i].value + '_"]');
                var teams = [];
                for (var j = 0; j < tms.length; j++) {
                    var row_index = tms[j].id.replace(/^.+_(\d+)$/, '$1');
                    if ($('inst_consumable_' + requests[i].value + '_1_' + row_index).checked) {
                        if (typeof teams[tms[j].value] == 'undefined') {
                            teams[tms[j].value] = 0;
                        }
                        teams[tms[j].value]++;
                        if (teams[tms[j].value] > 1) {
                            alert(this.msg_more_than_one_consumable);
                            return false;
                        }
                    }
                }
//                if ($$('input[type="radio"][id^="inst_consumable_' + requests[i].value + '_1_"]:checked').length > 1) {
//                    alert(this.msg_more_than_one_consumable);
//                    return false;
//                }
            }
            var all_requests = $$('input[type="checkbox"][id^="request_"]');
            var mans_installations = [];
            for (var i = 0; i < all_requests.length; i++) {
                var index = all_requests[i].value;
                var client = $('client_distributor_id_' + index).value;
                var date = $('installation_date_' + index).value;
                var time = $('installation_time_' + index).value;
                var mans = this._getRequestTeam(index);
                if (mans.length) {
                    for (var j = 0; j < mans.length; j++) {
                        var key = mans[j].value + '|' + date + '|' + time;
                        if (typeof mans_installations[key] == 'undefined' || mans_installations[key] == client) {
                            mans_installations[key] = client;
                        } else {
                            alert(this.msg_simultaneous_installations);
                            return false;
                        }
                    }
                }
            }
            e.form.action = env.base_url + '?' + env.module_param + '=reports&reports=save&report_type=' + $('report_type').value;
            e.form.submit();
        } else {
            alert(this.msg_no_selected_records);
        }
    },

    print: function (url, date_css, date_formatted) {
        var requests = $$('table.installations_schedule_' + date_css + ' input[type="checkbox"][id^="request_"]');
        for (var i = 0; i < requests.length; i++) {
            var request_team = this._getRequestTeam(requests[i].value);
            if (!request_team.length) {
                alert(this.msg_select_team_for_all_requests + ' ' + date_formatted + '!');
                return false;
            }
        }
        window.open(url);
        setTimeout(function () { location.reload(true); }, 5000);
//        e.form.action = env.base_url + '?' + env.module_param + '=reports&reports=print&date=' + date;
//        e.disabled = true;
        return true;
    },

    confirmDateTimeEdit: function (index) {
        if ($('installation_date_formatted_' + index).className.match(/hidden/)) {
            var date_readonly = $('installation_date_readonly_formatted_' + index);
            var time_readonly = $('installation_time_readonly_formatted_' + index);
            var date = $('installation_date_formatted_' + index);
            var time = $('installation_time_formatted_' + index);
            if ((event.target.id == date_readonly.id || event.target.id == time_readonly.id) && confirm(this.msg_confirm_date_time_change)) {
                addClass(date_readonly, 'hidden');
                addClass(time_readonly, 'hidden');
                removeClass(date, 'hidden');
                removeClass(time, 'hidden');
//                if (event.target.id == date_readonly.id) {
//                    date.onclick();
//                } else {
//                    time.onclick();
//                }
            }
        }
    },

    addTeam: function (autocomplete, data) {
        Effect.Appear('loading');
        Effect.Center('loading');
        var opt = {
            method: 'get',
            var_request_id: autocomplete.row ? autocomplete.field.replace(/inastallation_man_(\d+)$/, '$1') : autocomplete.field.replace(/inastallation_man_(\d+)_\d+/, '$1'),
            var_row_index: autocomplete.row ? autocomplete.row : autocomplete.field.replace(/inastallation_man_\d+_(\d+)/, '$1'),
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                var team = eval('(' + t.responseText + ')');
                var request_id = t.request.options.var_request_id;
                if (team.length) {
                    var request_team = reportUstilInstallationsSchedule._getRequestTeam(request_id);
                    var mans_rows = [];
                    var plus_button = $('installation_mans_' + request_id + '_plusButton');
                    for (var i = 0; i < request_team.length; i++) {
                        mans_rows[request_team[i].value] = request_team[i].id.replace(/inastallation_man_id_\d+_(\d+)/, '$1');
                    }
                    for (var i = 0; i < team.length; i++) {
                        var man = team[i];
                        if (typeof mans_rows[man.id] == 'undefined') {
                            plus_button.onclick();
                            var row_index = reportUstilInstallationsSchedule._getRequestLastTeamRowIndex(request_id);
                        } else {
                            var row_index = mans_rows[man.id];
                        }
                        var man_ac = $('inastallation_man_'    + request_id + '_' + row_index);
                        man_ac.value = man.name;
                        // this maybe is not needed
                        window['params_' + man_ac.readAttribute('uniqid')].ac.oldElementValue = man.name;
                        $('inastallation_man_'    + request_id + '_oldvalue_' + row_index).value = man.name;
                        $('inastallation_man_id_' + request_id + '_' + row_index).value = man.id;
                        $('rol_man_'              + request_id + '_' + row_index).value = man.role;
                        $('rol_man_'              + request_id + '_' + row_index).onchange();
                        $('inst_consumable_'      + request_id + '_' + man.consumable + '_' + row_index).click();
                        $('request_team_'         + request_id + '_' + row_index).value = man.team_id;

                        // Set man team color
                        reportUstilInstallationsSchedule._setManTeamColor(request_id, row_index, man.team_color);
                    }
                } else {
                    // Clear man team color
                    reportUstilInstallationsSchedule._setManTeamColor(request_id, t.request.options.var_row_index, '');
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
                Effect.Fade('loading');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
                Effect.Fade('loading');
            }
        };

        var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_get_team&man_id=' + data.id + '&report_type=' + $('report_type').value;
        new Ajax.Request(url, opt);
    },

    _getRequestLastTeamRowIndex: function (request_id) {
        var new_rows = $$('input[type="hidden"][id^="inastallation_man_id_' + request_id + '_"][value=""]:not(.input_inactive):last');
        var row_index = '';
        for (var j = 0; j < new_rows.length; j++) {
            row_index = new_rows[j];
            row_index = row_index.id.replace(/inastallation_man_id_\d+_(\d+)/, '$1');
        }

        return row_index;
    },

    _setManTeamColor: function (request_id, row_index, color) {
        $$('tr#installation_mans_' + request_id + '_' + row_index + ' td').each(function (e) {e.style.backgroundColor = color;});
    },

    prepareNewManRow: function (request_id) {
        // Get last team row index for the given request
        var row_index = reportUstilInstallationsSchedule._getRequestLastTeamRowIndex(request_id);

        // Clear man team color
        reportUstilInstallationsSchedule._setManTeamColor(request_id, row_index, '');
    }
};