<?php

class Custom_Report_Filters extends Report_Filters {

    /** @var Registry $registry */
    private static $registry = array();

    /**
     * Defining filters for the certain type report
     *
     * @param Registry $registry - the main registry
     * @return array - filter definitions of report
     */
    function defineFilters(&$registry) {
        // Prepare an array for the filters
        $filters = array();

        $field = self::getAdditionalField($registry, 'Document', 20, 'kind_sht');
        $field['name'] = 'production_of';
        $field['readonly'] = false;
        $filters['production_of'] = $field;

        $filters['order_num'] = array(
            'name' => 'order_num',
            'type' => 'text',
            'label' => $this->i18n('reports_filter_order_num')
        );

        $filters['order_date_from'] = array (
            'name'              => 'order_date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'order_date_to',
            'label'             => $this->i18n('reports_filter_order_date')
        );
        $filters['order_date_to'] = array (
            'name' => 'order_date_to'
        );

        $filters['for_date_from'] = array (
            'name'              => 'for_date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'for_date_to',
            'label'             => $this->i18n('reports_filter_for_date')
        );
        $filters['for_date_to'] = array (
            'name' => 'for_date_to'
        );

        $this->loadDefaultFilter($registry, $filters, 'autocompleter', array('autocomplete_type' => 'customers'));
        $filters['customer']['label'] = $this->i18n('reports_filter_customer');
//             $filters['customer']['autocomplete']['clear_fields'] = array(
//                 '$trademark',
//                 '$trademark_autocomplete',
//                 '$trademark_oldvalue'
//             );
        $filters['customer']['autocomplete']['fill_options'][] = '$trademark => ';
        $filters['customer']['autocomplete']['fill_options'][] = '$trademark_autocomplete => ';
        $filters['customer']['autocomplete']['fill_options'][] = '$trademark_oldvalue => ';

        $filters['trademark'] = array (
            'custom_id'       => 'trademark',
            'name'            => 'trademark',
            'type'            => 'autocompleter',
            'width'           => 222,
            'label'           => $this->i18n('reports_filter_trademark'),
            'autocomplete'    => array(
                'search' => array('<name>', '<customer_name>'),
                'sort'         => array('<name>', '<customer_name>'),
                'type'         => 'nomenclatures',
                'clear'        => 1,
                'suggestions'  => '<name> [<customer_name>]',
                'id_var'       => 'trademark',
                'fill_options' => array(
                    '$trademark => <trademark>',
                    '$trademark_autocomplete => <name> [<customer_name>]',
                    '$trademark_oldvalue => <name> [<customer_name>]',
                ),
                'filters'      => array(
                    '<type_keyword>' => 'trademark',
                    '<customer_trademark>' => '1',
                    '<customer>' => '$customer'
                ),
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select'),
                'combobox'     => 1
            )
        );

        $this->loadDefaultFilter($registry, $filters, 'autocompleter', array('autocomplete_type' => 'nomenclatures', 'filter_name' => 'village'));
        $filters['village']['filters'] = array('<type>' => '25');
        $filters['village']['label'] = $this->i18n('reports_filter_village');
        $filters['village']['autocomplete']['suggestions'] = '(<code>) <name>';
        $filters['village']['autocomplete']['fill_options']['$village_autocomplete'] = '(<code>) <name>';
        $filters['village']['autocomplete']['fill_options']['$village_oldvalue'] = '(<code>) <name>';

        $query = "
            SELECT oi.name AS label,
                o.id AS option_value
              FROM " . DB_TABLE_OFFICES . " AS o
              JOIN " . DB_TABLE_OFFICES_I18N . " AS oi
                ON (oi.parent_id = o.id
                  AND oi.lang = '{$registry['lang']}')
              WHERE o.deleted_by = 0
                AND o.active = 1
              ORDER BY o.id DESC";
        $filters['offices'] = array(
            'name' => 'offices',
            'type' => 'checkbox_group',
            'options' => $registry['db']->GetAll($query),
            'label' => $this->i18n('reports_filter_offices')
        );

        $filters['employee'] = array(
            'name'            => 'employee',
            'type'            => 'custom_filter',
            'actual_type'     => 'autocompleter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'width'           => 244,
            'label'           => $this->i18n('reports_filter_team'),
            'autocomplete'    => array(
                'type'         => 'customers',
                'suggestions'  => '<name> <lastname>',
                'search'       => array('<name>'),
                'sort'         => array('<name>'),
                'clear'        => 1,
                'id_var'       => 'employee',
                'fill_options' => array(
                    '$employee => <id>',
                    '$employee_autocomplete => <name> <lastname>',
                ),
                'filters'      => array(
                    '<type>' => PH_CUSTOMER_EMPLOYEE . ',7'
                ),
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers', 'ajax_select')
            )
        );

        $filters['undistributed_installations'] = array(
            'name'    => 'undistributed_installations',
            'type'    => 'checkbox_group',
            'label'   => $this->i18n('reports_filter_undistributed_installations'),
            'options' => array(
                array(
                    'option_value' => 1
                )
            )
        );

        $filters['unconfirmed'] = array(
            'name'    => 'unconfirmed',
            'type'    => 'checkbox_group',
            'label'   => $this->i18n('reports_filter_unconfirmed'),
            'options' => array(
                array(
                    'option_value' => 1
                )
            )
        );

        $filters['request_status'] = array(
            'name'    => 'request_status',
            'type'    => 'radio',
            'label'   => $this->i18n('reports_filter_request_status'),
            'options' => array(
                array(
                    'option_value' => 'all',
                    'label' => $this->i18n('reports_filter_request_status_all')
                ),
                array(
                    'option_value' => 'non_finished',
                    'label' => $this->i18n('reports_filter_request_status_non_finished')
                ),
                array(
                    'option_value' => 'finished',
                    'label' => $this->i18n('reports_filter_request_status_finished')
                )
            )
        );

        return $filters;
    }

    /**
     * Process some filters that depend on the request or on each other
     *
     * @param array $filters - the report filters
     * @return array - report filters after processing
     */
    function processDependentFilters(&$filters) {
        $unset_filters = array();
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }
        }

        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        // Default value for filter: request_status
        if (empty($filters['request_status']['value'])) {
            $filters['request_status']['value'] = 'non_finished';
        }

        return $filters;
    }
}

?>
