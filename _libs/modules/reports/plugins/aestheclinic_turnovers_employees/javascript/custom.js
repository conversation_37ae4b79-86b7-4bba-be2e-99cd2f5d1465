var reportAestheclinicTurnoversEmployees = {
    changeClinic: function (clinic) {
        // Clear employee dropdowns which are not for the currently selected clinic
        $$('select[id*="employees_"]').each(
            function (e) {
                var index = e.id.replace('employees_', '');
                if (index == '1') {
                    e.value = '';
                    e.onchange();
                    for (var i = 0; i < e.options.length; i++) {
                        addClass(e.options[i], 'hidden');
                    }
                } else {
                    $('table_employees').deleteRow(index);
                }
            }
        );

        // Show employee dropdowns which are for the currently selected clinic
        $$('.reportaestheclinicturnovers_employee_clinic_' + clinic.value).each(function (e) {removeClass(e, 'hidden');});
    }
};