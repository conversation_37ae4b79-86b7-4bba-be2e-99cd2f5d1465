{if !$add_errors}
<form style="color: #666666; font-family: Verdana,Arial,Helvetica,sans-serif; font-size: 11px;">
  {$help_message|escape}
  <br />
  <br />
  {foreach from=$document_types_to_add item='document_type' name='document_type'}
    <input type="radio" name="document_type" id="document_type_{$smarty.foreach.document_type.iteration}" value="{$document_type.option_value}"{if $smarty.foreach.document_type.first} checked="checked"{/if} />
    <label for="document_type_{$smarty.foreach.document_type.iteration}"> {$document_type.label}</label>
    <br />
  {/foreach}
  <br />
  <table>
    <tr>
      <td><label for="{$agreement_with.name}">{$agreement_with.label}</label><span class="required">{#required#}</span></td>
      <td>
        {include file="input_dropdown.html"
          standalone=true
          width=$agreement_with.width
          name=$agreement_with.name
          custom_id=$agreement_with.custom_id
          options=$agreement_with.options
          label=$agreement_with.first_filter_label
          help=$agreement_with.first_filter_label
          value=$agreement_with.value}
      </td>
    </tr>
    <tr>
      <td><label for="{$zmat_zveno.name}">{$zmat_zveno.label}</label><span class="required">{#required#}</span></td>
      <td>
        {include file="input_dropdown.html"
          standalone=true
          width=$zmat_zveno.width
          name=$zmat_zveno.name
          custom_id=$zmat_zveno.custom_id
          options=$zmat_zveno.options
          label=$zmat_zveno.first_filter_label
          help=$zmat_zveno.first_filter_label
          value=$zmat_zveno.value}
    </tr>
  </table>
  <br />
  {strip}
  <input type="hidden" name="table_session_param" value="{$special_session_param}" />
  <input type="button" class="button" onclick="impulsUniteExpensesInvoicesAddDocument(this);" value="{#add#|escape}" />
  <input type="button" class="button" onclick="lb.deactivate();" value="{#cancel#|escape}" />
  {/strip}
</form>
{else}
  <table border="0" cellpadding="5" cellspacing="0">
    {foreach from=$add_errors item='add_error'}
      <tr>
        <td class="error">
          {$add_error}
        </td>
      </tr>
    {/foreach}
  </table>
  <input type="button" class="button" onclick="lb.deactivate();" value="{#cancel#|escape}" style="position: absolute; bottom: 13px;" />
{/if}