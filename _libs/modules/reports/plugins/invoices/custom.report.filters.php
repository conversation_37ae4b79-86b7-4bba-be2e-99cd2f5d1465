<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry;

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {

            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            // DEFINE FILTER: Date from
            $filters['date_from'] = array(
                'name' => 'date_from',
                'type' => 'custom_filter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
                'additional_filter' => 'date_to',
                'width' => '65',
                'required' => true
            );
            $filters['date_to'] = array(
                'name' => 'date_to',
                'type' => 'date',
                'width' => '65',
                'required' => true
            );

            // DEFINE FILTER: Num from
            $filters['num_from'] = array(
                'name' => 'num_from',
                'type' => 'custom_filter',
                'label'         => $this->i18n('reports_num'),
                'help'         => '',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_from_to.html',
                'additional_filter' => 'num_to',
                'width' => '85',
                'restrict' => 'insertOnlyDigits'
            );
            $filters['num_to'] = array(
                'name' => 'num_to',
                'label'         => $this->i18n('reports_num_to'),
                'width' => '84',
                'restrict' => 'insertOnlyDigits'
            );

            // DEFINE CUSTOMER
            $filter = array(
                'custom_id'     => 'customer',
                'name'          => 'customer',
                'type'          => 'autocompleter',
                'label'         => $this->i18n('reports_customer'),
                'help'          => $this->i18n('reports_customer'),
                'autocomplete'  => array(
                    'type' => 'customers',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'clear'         => 1,
                    'buttons_hide'  => 'search',
                    'suggestions'   => '[<code>] <name> <lastname>',
                )
            );
            $filters['customer'] = $filter;

            //get companies
            $query = 'SELECT c.id as option_value, ci18n.name as label' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_COMPANIES . ' c' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' ci18n' . "\n" .
                     '  ON c.id = ci18n.parent_id AND ci18n.lang = "' . $registry['lang'] . '"' . "\n" .
                     'ORDER BY c.position';
            $companies = $registry['db']->GetAll($query);

            // DEFINE companies
            $filter = array(
                'custom_id'     => 'company',
                'name'          => 'company',
                'type'          => 'dropdown',
                'label'         => $this->i18n('reports_company'),
                'help'          => $this->i18n('reports_company'),
                'required'      => false,
                'options'       => $companies,
                'hidden'        => (count($companies) <= 1) ? true : false,
                'onchange'      => 'selectCompany(this)',
            );
            $filters['company'] = $filter;

            // DEFINE office
            $filter = array(
                'custom_id'     => 'office',
                'name'          => 'office',
                'type'          => 'dropdown',
                'label'         => $this->i18n('reports_office'),
                'help'          => $this->i18n('reports_office'),
                'required'      => false,
                'options'       => array(),
                'hidden'        => (count($companies) == 0) ? true : false,
            );
            $filters['office'] = $filter;

            // DEFINE office
            $filter = array(
                'custom_id'     => 'type',
                'name'          => 'type',
                'type'          => 'radio',
                'label'         => '',
                'help'          => '',
                'required'      => true,
                'options_align' => 'horizontal',
                'label'         => $this->i18n('reports_document_type'),
                'options'       => array(
                    array(
                        'label' => $this->i18n('reports_incomes'),
                        'option_value' => 'incomes',
                    ),
                    array(
                        'label' => $this->i18n('reports_expenses'),
                        'option_value' => 'expenses',
                    ),
                ),
                'on_change'      => 'selectDocumentType(this)',
            );
            $filters['type'] = $filter;

            //DEFINE CURRENCY FILTER
            $filter = array(
                'custom_id' => 'currency',
                'name'      => 'currency',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_currency'),
                'help'      => $this->i18n('reports_currency'),
                'options'   => Dropdown::getCurrencies(array($registry)),
                'required'  => 1
            );
            $filters['currency'] = $filter;

            $months = array();
            for ($i = 1; $i <= 12; $i ++) {
                $months[] = array('label' => $i, 'option_value' => strval($i < 10 ? '0' . $i : $i));
            }
            $i = date('Y');
            $years = array();
            for ($j = $i+1; $j > $i-3; $j--) {
                $years[] = array('label' => $j, 'option_value' => strval($j));
            }
            // DEFINE FILTER: month
            $filter = array(
                'custom_id'            => 'period_month',
                'name'                 => 'period_month',
                'custom_template'      => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/period_filter.html',
                'additional_filter'    => 'period_year',
                'type'                 => 'custom_filter',
                'required'             => 0,
                'width'                => 65,
                'label'   => $this->i18n('reports_acc_period'),
                'options'              => $months,
                'hidden'               => true,
            );
            $filters['period_month'] = $filter;
            // DEFINE FILTER: year
            $filter = array(
                'custom_id'            => 'period_year',
                'name'                 => 'period_year',
                'type'                 => 'dropdown',
                'width'                => 65,
                'options'              => $years,
            );
            $filters['period_year'] = $filter;

            // DEFINE issue by
            $filter = array(
                'custom_id'     => 'issue_by',
                'name'          => 'issue_by',
                'type'          => 'autocompleter',
                'label'         => $this->i18n('reports_issue_by'),
                'help'          => $this->i18n('reports_issue_by'),
                'autocomplete'  => array(
                    'type'          => 'users',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'users', 'users'),
                    'clear'         => 1,
                    'buttons_hide'  => 'search',
                    'filters'       => array(
                        '<is_portal>' => '0',
                        '<active>' => '1',
                        '<id>' => 'IN (' . implode(', ', $registry['db']->GetCol('SELECT DISTINCT(issue_by) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS)) . ')',
                    ),
                    'suggestions'   => '<firstname> <lastname>',
                    'fill_options'  => array(
                        '$issue_by => <id>',
                        '$issue_by_autocomplete => <firstname> <lastname>',
                    ),
                )
            );
            $filters['issue_by'] = $filter;

            // DEFINE annulled
            $filter = array(
                'custom_id'     => 'annulled',
                'name'          => 'annulled',
                'type'          => 'checkbox',
                'label'         => $this->i18n('reports_annulled'),
                'option_value' => 'annulled',
            );
            $filters['annulled'] = $filter;

            return $filters;
        }

        /**
         * Process some filters that depend on the request or on each other
         *
         * @param array $filters - the report filters
         * @return array - report filters after processing
         */
        function processDependentFilters(&$filters) {

            $registry = &self::$registry;

            // process custom filters
            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }
            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            if (!empty($filters['type']['value']) && $filters['type']['value'] == 'expenses') {
                $filters['period_month']['hidden'] = false;
                $filters['issue_by']['hidden'] = true;
            }

            if (count($filters['company']['options']) == 1) {
                $filters['company']['value'] = $filters['company']['options'][0]['option_value'];
            }

            if (!empty($filters['company']['value'])) {
                $filters['office']['options'] = Finance_Dropdown::getCompanyOffices(array(0 => $registry, 'company_id' => $filters['company']['value']));
            } else {
                $filters['office']['hidden'] = 1;
            }

            if (!$registry->get('generated_report')) {
                if (!$filters['period_month']['value']) {
                    $filters['period_month']['value'] = date('m');
                }
                if (!$filters['period_month']['additional_filter']['value']) {
                    $filters['period_month']['additional_filter']['value'] = date('Y');
                }
                if (empty($filters['currency']['value'])) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                    $filters['currency']['value'] = Finance_Currencies::getMain($registry);
                }
            }

            return $filters;
        }
    }

?>
