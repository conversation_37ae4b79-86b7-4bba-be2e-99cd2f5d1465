/**
 * Company filter selection triggers update of other filters (offices)
 *
 * @param {Object} element - the element which calls the function
 */
function selectCompany(element) {
    // prepare ajax options
    Effect.Center('loading');
    Effect.Appear('loading');

    var company = element.value;

    if (company) {
        showHideRow('office', true);

        var opt = {
            method: 'get',
            onSuccess: function (t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                eval('var offices = ' + t.responseText + ';');

                var office = $('office');
                office.options.length = 0;

                if (offices.length) {
                    removeClass(office, 'missing_records');
                    office.options[0] = new Option(i18n['labels']['please_select'].toLowerCase(), '', false, true);
                    addClass(office.options[0], 'undefined');

                    for (var j = 0; j < offices.length; j++) {
                        office.options[j+1] = new Option(offices[j]['label'], offices[j]['option_value'], false, false);
                    }
                    toggleUndefined(office);
                } else {
                    office.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                    addClass(office, 'missing_records');
                }

                Effect.Fade('loading');
            },
            on404: function (t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function (t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_select_company_offices&report_type=' + $('report_type').value + '&company_id=' + company;
        new Ajax.Request(url, opt);
    } else {
        showHideRow('office', false);
        Effect.Fade('loading');
    }
}

/**
 * Type filter selection triggers toggle of visibility of other filters
 *
 * @param {Object} element - the element which calls the function
 */
function selectDocumentType(element) {
    showHideRow('period_month', element.value == 'expenses');
    showHideRow('issue_by', element.value == 'incomes');
}

/**
 * Toggles visibility of child credit/debit rows
 *
 * @param {Object} element - invoice row toggle
 */
function toggleSubItems(element) {
    var items = $$('.cd_' + element.id.replace(/switch_/, ''));
    if (element.className.match(/switch_expand/)) {
        removeClass(element, 'switch_expand');
        addClass(element, 'switch_collapse');
        for (var i = 0; i < items.length; i++) {
            items[i].style.display = '';
        }
    } else {
        removeClass(element, 'switch_collapse');
        addClass(element, 'switch_expand');
        for (var i = 0; i < items.length; i++) {
            items[i].style.display = 'none';
        }
    }
}
