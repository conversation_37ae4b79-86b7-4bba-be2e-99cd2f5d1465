<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      {if !empty($reports_results)}
        {capture assign='general_colspan'}{if $reports_additional_options.type_result_table eq 'month'}17{else}15{/if}{/capture}
        {capture assign='colspan_first_columns'}{if $reports_additional_options.type_result_table eq 'month'}5{else}3{/if}{/capture}
        {capture assign='amount_precision'}%.{$reports_additional_options.prec_price|escape}f{/capture}
        <h1>{$reports_additional_options.table_title|escape}</h1>
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row hcenter">
            {if $reports_additional_options.type_result_table eq 'month'}
              <td class="t_border" style="vertical-align: middle;"><div style="width: 30px;">{#reports_date#|escape}</div></td>
              <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_day_of_week#|escape}</div></td>
              <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_routes#|escape}</div></td>
            {else}
              <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_month#|escape}</div></td>
            {/if}
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_starting_km#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_ending_km#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_run#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_personal_km#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_fuel#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_fuel_with_card#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_tickets#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_taxes#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_parking#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_travel_money#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_hotel#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_food#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_others#|escape}</div></td>
            <td style="vertical-align: middle;"><div style="width: 100px;">{#reports_total#|escape}</div></td>
          </tr>
          {foreach from=$reports_results item=employee_data}
            <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
              {if $reports_additional_options.type_result_table eq 'month'}
                <td class="t_border hright">
                  {$employee_data.day_number|escape|default:"&nbsp;"}
                </td>
              {/if}
              <td class="t_border">
                {$employee_data.label|escape|default:"&nbsp;"}
              </td>
              {if $reports_additional_options.type_result_table eq 'month'}
                <td class="t_border">
                  {foreach from=$employee_data.routes item=route name=rt}
                    {$route|escape|default:"&nbsp;"}{if !$smarty.foreach.rt.last}<br />{/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                </td>
              {/if}
              {foreach from=$reports_additional_options.int_number_data item=dat_key}
                <td class="t_border hright">
                  {if $employee_data.$dat_key}{$employee_data.$dat_key|escape|default:"0"}{else}&nbsp;{/if}
                </td>
              {/foreach}
              {foreach from=$reports_additional_options.sum_data item=sum_key name=sk}
                <td class="{if !$smarty.foreach.sk.last}t_border {/if}hright">
                  {if $employee_data.$sum_key}{$employee_data.$sum_key|default:"0"|escape|string_format:$amount_precision}{else}&nbsp;{/if}
                </td>
              {/foreach}
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="{$general_colspan}">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          {capture assign='total_row_class'}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$total_row_class}">
            <td colspan="{$colspan_first_columns}" class="t_border">&nbsp;</td>
            {foreach from=$reports_additional_options.total_int_number_data item=tot_key}
              <td class="t_border hright">
                <strong>{$reports_additional_options.totals.$tot_key|escape|default:"0"}</strong>
              </td>
            {/foreach}
            {foreach from=$reports_additional_options.sum_data item=sum_key name=sd}
              <td class="{if !$smarty.foreach.sd.last}t_border {/if}hright">
                <strong>{$reports_additional_options.totals.$sum_key|default:"0"|escape|string_format:$amount_precision}</strong>
              </td>
            {/foreach}
          </tr>
          <tr class="{$total_row_class}">
            <td colspan="{$colspan_first_columns}" class="t_border">&nbsp;</td>
            <td class="t_border hright" colspan="2">
              <strong>{#reports_personal_km_amortization#|escape}</strong>
            </td>
            <td class="t_border hright">
              <strong>{$reports_additional_options.personal_km_amortization|default:"0"|escape|string_format:$amount_precision}</strong>
            </td>
            <td colspan="5" class="t_border">&nbsp;</td>
            <td class="t_border hright" colspan="3">
              <strong>{#reports_total_expenses#|escape}</strong>
            </td>
            <td class="hright">
              <strong>{$reports_additional_options.totals.total|default:"0"|escape|string_format:$amount_precision}</strong>
            </td>
          </tr>
          <tr class="{$total_row_class}">
            <td colspan="{$colspan_first_columns}" class="t_border">&nbsp;</td>
            <td class="t_border hright" colspan="2">
              <strong>{#reports_personal_km_fuel#|escape}</strong>
            </td>
            <td class="t_border hright" style="background-color: #FFFDC0;">
              <strong>{$reports_additional_options.personal_km_fuel|default:"0"|escape|string_format:$amount_precision}</strong>
            </td>
            <td colspan="5" class="t_border">&nbsp;</td>
            <td class="t_border hright" colspan="3">
              <strong>{#reports_received_advance#|escape}</strong>
            </td>
            <td class="hright">
              <strong>{$reports_additional_options.advance_amount|default:"0"|escape|string_format:$amount_precision}</strong>
            </td>
          </tr>
          <tr class="{$total_row_class}">
            <td colspan="{$colspan_first_columns}" class="t_border">&nbsp;</td>
            <td class="t_border hright" colspan="2" style="background-color: #FFFDC0;">
              <strong>{#reports_personal_km_total#|escape}</strong>
            </td>
            <td class="t_border hright" style="background-color: #FFFDC0;">
              <strong>{$reports_additional_options.personal_km_total_lv|default:"0"|escape|string_format:$amount_precision}</strong>
            </td>
            <td colspan="5" class="t_border">&nbsp;</td>
            <td class="t_border hright" colspan="3">
              <strong>{#reports_remainging_amount#|escape}</strong>
            </td>
            <td class="hright">
              <strong>{$reports_additional_options.left_amount|default:"0"|escape|string_format:$amount_precision}</strong>
            </td>
          </tr>
          <tr>
            <td class="t_footer" colspan="{$general_colspan}"></td>
          </tr>
        </table>
      {else}
        <h1 style="color: red;">{#reports_no_data#}</h1>
      {/if}
    </td>
  </tr>
</table>