<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    {if !empty($reports_results)}
      {capture assign='weight_precision'}0\.{section name='wp' start=0 loop=$reports_additional_options.weight_prec step=1}0{/section}{/capture}
      {capture assign='general_colspan'}{if $reports_additional_options.type_result_table eq 'month'}17{else}15{/if}{/capture}
      {capture assign='colspan_first_columns'}{if $reports_additional_options.type_result_table eq 'month'}5{else}3{/if}{/capture}
      {capture assign='amount_precision'}%.{$reports_additional_options.prec_price|escape}f{/capture}
      {capture assign='amount_precision_xls'}0\.{section name='qp' start=0 loop=$reports_additional_options.prec_price step=1}0{/section}{/capture}
      <h1>{$reports_additional_options.table_title|escape}</h1>
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          {if $reports_additional_options.type_result_table eq 'month'}
            <td style="vertical-align: middle;"><div style=""><strong>{#reports_date#|escape}</strong></div></td>
            <td style="vertical-align: middle;"><div style=""><strong>{#reports_day_of_week#|escape}</strong></div></td>
            <td style="vertical-align: middle;"><div style=""><strong>{#reports_routes#|escape}</strong></div></td>
          {else}
            <td style="vertical-align: middle;"><div style=""><strong>{#reports_month#|escape}</strong></div></td>
          {/if}
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_starting_km#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_ending_km#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_run#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_personal_km#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_fuel#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_fuel_with_card#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_tickets#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_taxes#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_parking#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_travel_money#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_hotel#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_food#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_others#|escape}</strong></div></td>
          <td style="vertical-align: middle;"><div style=""><strong>{#reports_total#|escape}</strong></div></td>
        </tr>
        {foreach from=$reports_results item=employee_data}
          <tr>
            {if $reports_additional_options.type_result_table eq 'month'}
              <td align="right">
                {$employee_data.day_number|escape|default:"&nbsp;"}
              </td>
            {/if}
            <td>
              {$employee_data.label|escape|default:"&nbsp;"}
            </td>
            {if $reports_additional_options.type_result_table eq 'month'}
              <td class="t_border">
                {foreach from=$employee_data.routes item=route name=rt}
                  {$route|escape|default:"&nbsp;"}{if !$smarty.foreach.rt.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
            {/if}
            {foreach from=$reports_additional_options.int_number_data item=dat_key}
              <td align="right">
                {if $employee_data.$dat_key}{$employee_data.$dat_key|escape|default:"0"}{else}&nbsp;{/if}
              </td>
            {/foreach}
            {foreach from=$reports_additional_options.sum_data item=sum_key name=sk}
              <td{if $employee_data.$sum_key} align="right" style="mso-number-format:{$amount_precision_xls};"{/if}>
                {if $employee_data.$sum_key}{$employee_data.$sum_key|default:"0"|escape|string_format:$amount_precision}{else}&nbsp;{/if}
              </td>
            {/foreach}
          </tr>
        {foreachelse}
          <tr>
            <td colspan="{$general_colspan}"><span style="color: red;">{#no_items_found#|escape}</span></td>
          </tr>
        {/foreach}
        <tr>
          <td colspan="{$colspan_first_columns}">&nbsp;</td>
          {foreach from=$reports_additional_options.total_int_number_data item=tot_key}
            <td align="right">
              <strong>{$reports_additional_options.totals.$tot_key|escape|default:"0"}</strong>
            </td>
          {/foreach}
          {foreach from=$reports_additional_options.sum_data item=sum_key name=sd}
            <td align="right" style="mso-number-format:{$amount_precision_xls};">
              <strong>{$reports_additional_options.totals.$sum_key|default:"0"|escape|string_format:$amount_precision}</strong>
            </td>
          {/foreach}
        </tr>
        <tr>
          <td colspan="{$colspan_first_columns}">&nbsp;</td>
          <td align="right" colspan="2">
            <strong>{#reports_personal_km_amortization#|escape}</strong>
          </td>
          <td align="right" style="mso-number-format:{$amount_precision_xls};">
            <strong>{$reports_additional_options.personal_km_amortization|default:"0"|escape|string_format:$amount_precision}</strong>
          </td>
          <td colspan="5">&nbsp;</td>
          <td align="right" colspan="3">
            <strong>{#reports_total_expenses#|escape}</strong>
          </td>
          <td align="right" style="mso-number-format:{$amount_precision_xls};">
            <strong>{$reports_additional_options.totals.total|default:"0"|escape|string_format:$amount_precision}</strong>
          </td>
        </tr>
        <tr>
          <td colspan="{$colspan_first_columns}">&nbsp;</td>
          <td align="right" colspan="2">
            <strong>{#reports_personal_km_fuel#|escape}</strong>
          </td>
          <td align="right" style="background-color: #FFFDC0; mso-number-format:{$amount_precision_xls};">
            <strong>{$reports_additional_options.personal_km_fuel|default:"0"|escape|string_format:$amount_precision}</strong>
          </td>
          <td colspan="5">&nbsp;</td>
          <td align="right" colspan="3">
            <strong>{#reports_received_advance#|escape}</strong>
          </td>
          <td align="right" style="mso-number-format:{$amount_precision_xls};">
            <strong>{$reports_additional_options.advance_amount|default:"0"|escape|string_format:$amount_precision}</strong>
          </td>
        </tr>
        <tr>
          <td colspan="{$colspan_first_columns}">&nbsp;</td>
          <td align="right" colspan="2" style="background-color: #FFFDC0;">
            <strong>{#reports_personal_km_total#|escape}</strong>
          </td>
          <td align="right" style="background-color: #FFFDC0; mso-number-format:{$amount_precision_xls};">
            <strong>{$reports_additional_options.personal_km_total_lv|default:"0"|escape|string_format:$amount_precision}</strong>
          </td>
          <td colspan="5">&nbsp;</td>
          <td align="right" colspan="3">
            <strong>{#reports_remainging_amount#|escape}</strong>
          </td>
          <td align="right" style="mso-number-format:{$amount_precision_xls};">
            <strong>{$reports_additional_options.left_amount|default:"0"|escape|string_format:$amount_precision}</strong>
          </td>
        </tr>
      </table>
    {else}
      <h1 style="color: red;">{#reports_no_data#}</h1>
    {/if}
  </body>
</html>