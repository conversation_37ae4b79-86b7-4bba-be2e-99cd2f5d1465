<?php

    class Custom_Report_Filters extends Report_Filters {

        private static $reg;
        private static $filters;

        /**
         * Defining filters for the certain type report
         */
        public function defineFilters(&$registry) {
            $filters = array();
            self::$reg = &$registry;

            // Load report i18n params to JavaScript
            $this->prepareI18nAsJsFilter($registry, $filters);

            // Load EJ2
            $this->prepareEj2($registry);

            // -- DEFINE FILTERS -- //
            // BUILDING filter
            $filters['building'] = array(
                'custom_id'            => 'building',
                'name'                 => 'building',
                'type'                 => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width'                => '222',
                'autocomplete'         => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'  => '<name>',
                    'fill_options' => array(
                        '$building              => <id>',
                        '$building_autocomplete => <name>',
                        '$building_oldvalue     => <name>'
                    ),
                    'filters' => array(
                        '<type>' => strval(NOM_TYPE_BUILDINGS),
                        '<deleted_by>' => '0',
                        '<active>'     => '1'
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_building'),
                'help'  => $this->i18n('reports_building')
            );

            // PROPERTY filter
            $filters['property'] = array(
                'custom_id'            => 'property',
                'name'                 => 'property',
                'type'                 => 'autocompleter',
                'autocomplete_buttons' => 'clear',
                'width'                => '222',
                'autocomplete'         => array(
                    'type'         => 'nomenclatures',
                    'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'suggestions'  => '<name>',
                    'fill_options' => array(
                        '$property              => <id>',
                        '$property_autocomplete => <name>',
                        '$property_oldvalue     => <name>'
                    ),
                    'filters' => array(
                        '<type>' => ' IN (' . NOM_TYPE_PROPERTIES . ')',
                        '<deleted_by>' => '0',
                        '<active>'     => '1'
                    ),
                    'clear' => 1,
                    'buttons_hide' => 'search'
                ),
                'label' => $this->i18n('reports_property'),
                'help'  => $this->i18n('reports_property')
            );

            //DEFINE FLOOR FILTER
            $filter = array (
                'custom_id' => 'floor',
                'name'      => 'floor',
                'type'      => 'text',
                'label'     => $this->i18n('reports_floor'),
                'help'      => $this->i18n('reports_floor_help'),
                'value'     => ''
            );
            $filters['floor'] = $filter;

            //DEFINE PRICE FILTER
            $filters['price_from'] = array(
                'name' => 'price_from',
                'type' => 'custom_filter',
                'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_from_to.html',
                'additional_filter' => 'price_to',
                'restrict'          => 'insertOnlyDigits',
                'width' => 83,
                'label' => $this->i18n('reports_price_range')
            );
            $filters['price_to'] = array(
                'restrict' => 'insertOnlyDigits',
                'name' => 'price_to',
                'width' => 84,
            );


            //DEFINE OCCUPANCY FILTER
            //prepare options
            $occupancy_options = array();
            $sql_tags = 'SELECT t.id as option_value, ti18n.name as `label` FROM ' . DB_TABLE_TAGS . ' AS t' . "\n" .
                        'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n' . "\n" .
                        '  ON (t.id=ti18n.parent_id AND ti18n.lang="' . $registry['lang'] . '")' . "\n" .
                        'WHERE t.id IN (' . TAGS_OCCUPANCY . ') AND t.model="nomenclatures"' . "\n" .
                        'ORDER BY t.place ASC';
            $occupancy_options = $registry['db']->GetAll($sql_tags);

            //prepare filters
            $filter = array (
                'custom_id' => 'occupancy',
                'name'      => 'occupancy',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_occupancy'),
                'help'      => $this->i18n('reports_occupancy'),
                'options'   => $occupancy_options
            );
            $filters['occupancy'] = $filter;


            // DEFINE TRANSFERRED LAND FILTER
            $query = 'SELECT `option_value`, `label` FROM ' . DB_TABLE_FIELDS_OPTIONS . "\n" .
                     'WHERE `parent_name`="' . FILTER_TRANSFERRED_LAND_OPTIONS_SOURCE . '" AND `lang`="' . $registry['lang'] . '"';
            $filter = array(
                'custom_id'       => 'transferred_land',
                'name'            => 'transferred_land',
                'type'            => 'dropdown',
                'options'         => $registry['db']->GetAll($query),
                'label'           => $this->i18n('reports_transferred_land'),
                'help'            => $this->i18n('reports_transferred_land')
            );
            $filters['transferred_land'] = $filter;

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'custom_scripts',
                'name'              => 'custom_scripts',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/custom_scripts.html',
                'type'              => 'custom_filter',
            );
            $filters['custom_scripts'] = $filter;

            self::$filters = &$filters;
            return $filters;
        }

        public function processDependentFilters($filters) {
            $unset_filters = array();

            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter'])) {
                    if (isset($filters[$filter['additional_filter']])) {
                        $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                        $unset_filters[] = $filter['additional_filter'];
                    }
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            return $filters;
        }

    }

?>
