<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_date',
                'name'              => 'from_date',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/date_from_to_filter.html',
                'type'              => 'custom_filter',
                'required'          => 1,
                'width'             => 65,
                'additional_filter' => 'to_date',
                'first_filter_label'=> $this->i18n('reports_from_date'),
                'label'             => $this->i18n('reports_date_filter'),
                'help'              => $this->i18n('reports_date_filter')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_date',
                'name'            => 'to_date',
                'type'            => 'date',
                'width'           => 65,
                'label'           => $this->i18n('reports_to_date'),
                'help'            => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'            => 'customer',
                'name'                 => 'customer',
                'type'                 => 'autocompleter',
                'autocomplete_type'    => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'                => $this->i18n('reports_customer'),
                'help'                 => $this->i18n('reports_customer'),
                'value'                => ''
            );
            $filters['customer'] = $filter;

            //DEFINE ANNULLED FILTER
            $filter = array (
                'custom_id' => 'show_annulled',
                'name'      => 'show_annulled',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_show_annulled'),
                'help'      => $this->i18n('reports_show_annulled_help'),
                'options'   => array(
                    array(
                        'label'         => '',
                        'option_value'  => '1'
                    )
                )
            );
            $filters['show_annulled'] = $filter;

            //DEFINE FEE FILTER
            $filter = array (
                'custom_id' => 'hide_fee',
                'name'      => 'hide_fee',
                'type'      => 'checkbox_group',
                'label'     => $this->i18n('reports_hide_fee'),
                'help'      => $this->i18n('reports_hide_fee_help'),
                'options'   => array(
                    array(
                        'label'         => '',
                        'option_value'  => '1'
                    )
                )
            );
            $filters['hide_fee'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            }

            if (empty($filter['search_by']['value'])) {
                $filter['search_by']['value'] = 1;
            }
            return $filters;
        }
    }
?>
