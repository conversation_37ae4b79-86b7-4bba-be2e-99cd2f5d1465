<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = [];
        private $objectCache = [];

        private $filtersFormDocuments = [
            [
                'model_type' => 2,
                'field_name' => 'full_num',
            ],
            [
                'model_type' => 2,
                'field_name' => 'award_date',
            ],
            [
                'model_type' => 2,
                'field_name' => 'loan_applicant_id',
            ],
            [
                'model_type' => 2,
                'field_name' => 'loan_applicant_name',
            ],
            [
                'model_type' => 2,
                'field_name' => 'bank_id',
            ],
            [
                'model_type' => 2,
                'field_name' => 'bank_name',
            ],
            [
                'model_type' => 2,
                'field_name' => 'assignor_id',
            ],
            [
                'model_type' => 2,
                'field_name' => 'assignor_name',
            ],
            [
                'model_type' => 13,
                'field_name' => 'object_name_type_id',
            ],
            [
                'model_type' => 13,
                'field_name' => 'object_name_type',
            ],
            [
                'model_type' => 13,
                'field_name' => 'object_subtype_id',
            ],
            [
                'model_type' => 13,
                'field_name' => 'object_subtype_name',
            ],
            [
                'model_type' => 13,
                'field_name' => 'full_adress',
            ],
            /*[
                'model_type' => 13,
                'field_name' => 'full_adress_id',
            ],*/

            [
                'model_type' => 15,
                'field_name' => 'identity_type',
            ],
            [
                'model_type' => 24,
                'field_name' => 'object_identifier',
            ],
            [
                'model_type' => 18,
                'field_name' => 'object_identifier',
            ],
            [
                'model_type' => 25,
                'field_name' => 'object_id',
            ],
            [
                'model_type' => 13,
                'field_name' => 'populated_place_id',
            ],
            [
                'model_type' => 13,
                'field_name' => 'populated_place',
            ],
            [
                'model_type' => 13,
                'field_name' => 'analog_quarter_id',
            ],
            [
                'model_type' => 13,
                'field_name' => 'analog_quarter_name',
            ],
            [
                'model_type' => 13,
                'field_name' => 'street_id',
            ],
            [
                'model_type' => 13,
                'field_name' => 'street_name',
            ],
            [
                'model_type' => 13,
                'field_name' => 'street_number',
            ],
        ];

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;



            // $filters - array containing description of all filters
            $filters = array();
            $filters['full_num'] = [
                'name' => 'full_num',
                'type' => 'text',
                'label' => 'Номер на доклада',
                'required' => '',
                'readonly' => '',
            ];

            foreach ($this->filtersFormDocuments as $vars) {
                $docModel = $this->getEmptyDocumentOfType($vars['model_type']);
                foreach ($docModel->get('vars') as $var) {
                    if ($var['name'] === $vars['field_name']) {
                        if ($vars['field_name'] === 'award_date') {
                            // From - to dates
                            $fromDateKey = $var['name'].'_from';
                            $filters[$fromDateKey] = $var;
                            $filters[$fromDateKey]['name'] = $fromDateKey;
                            $filters[$fromDateKey]['label'] = 'От ' . $filters[$fromDateKey]['label'];
                            $filters[$fromDateKey]['required'] = '';
                            $filters[$fromDateKey]['readonly'] = '';

                            $toDateKey = $var['name'].'_to';
                            $filters[$toDateKey] = $var;
                            $filters[$toDateKey]['name'] = $toDateKey;
                            $filters[$toDateKey]['label'] = 'До ' . $filters[$toDateKey]['label'];
                            $filters[$toDateKey]['required'] = '';
                            $filters[$toDateKey]['readonly'] = '';
                        } else {
                            $filters[$var['name']] = $var;
                            $filters[$var['name']]['required'] = '';
                            $filters[$var['name']]['readonly'] = '';
                            if (array_key_exists('autocomplete', $filters[$var['name']])) {
                                //$filters[$var['name']]['autocomplete']['fill_options'] = null;
                                $filters[$var['name']]['autocomplete']['clear'] = 1;
                                //$filters[$var['name']]['autocomplete']['fill_options'] = null;
                            }
                        }
                        break;
                    }
                }
            }

            $filters['full_adress']['type'] = 'autocompleter';
            $filters['full_adress']['autocomplete'] = [
                'url' => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'documents', 'documents', 'ajax_select'),
                'min_chars' => 3,
                'suggestions' => '<a__full_adress>',
                'search' => ['<a__full_adress>'],
                'type' => 'documents',
                'fill_options' => [
                    '$full_adress => <a__full_adress>',
                    '$full_adress_autocomplete => <a__full_adress>',
                    '$full_adress_oldvalue => <a__full_adress>',
                ],
                'filters' => ['<type>' => '13'],
                'clear' => 1,
            ];

            $filters['include_archived'] = [
                'custom_id' => 'include_archived',
                'name' => 'include_archived',
                'type' => 'checkbox',
                'label' => $this->i18n('reports_include_archived'),
                'help' => $this->i18n('reports_include_archived_help'),
                'required' => '',
                'option_value' => 1,
            ];

            return $filters;
        }

        function processDependentFilters(&$filters) {
            /** @var Request $request */
            $request = self::$registry['request'];
            $includeArchivedForced = defined('FORCE_INCLUDE_ARCHIVED') && FORCE_INCLUDE_ARCHIVED;
            $includeArchivedValue = $request->get('include_archived') || $includeArchivedForced;

            $filters['include_archived']['value'] = $includeArchivedValue;
            $filters['include_archived']['disabled'] = $includeArchivedForced;

            return $filters;
        }



        /**
         * @param $registry
         * @return Document
         */
        private function getEmptyDocumentOfType($type): Document
        {
            $cacheKey = "doc_{$type}";
            if (array_key_exists($cacheKey, $this->objectCache)) {
                return $this->objectCache[$cacheKey];
            }
            $registry = self::$registry;
            $docModel = new Document($registry, ['type' => $type]);
            $docModel->getVars();
            return $this->objectCache[$cacheKey] = $docModel;
        }
    }
?>
