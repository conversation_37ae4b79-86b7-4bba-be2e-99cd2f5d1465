<?php

class ParentEntity
{
    private Document $model;
    private string $varName;
    private ?int $index;

    public function __construct(Document $model, string $varName, ?int $index = null)
    {
        $this->model = $model;
        $this->varName = $varName;
        $this->index = $index;
    }

    /**
     * @return Document
     */
    public function getModel(): Document
    {
        return $this->model;
    }

    /**
     * @return string
     */
    public function getVarName(): string
    {
        return $this->varName;
    }

    /**
     * @return int|null
     */
    public function getIndex(): ?int
    {
        return $this->index;
    }
}
