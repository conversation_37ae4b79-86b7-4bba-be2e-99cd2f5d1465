{if !empty($vehicle_details) && $vehicle_details.id}
{assign var='model' value=$vehicle_details.model}
{assign var='vars' value=$model->get('assoc_vars')}

<h1>{#reports_file_of#|escape} {$model->get('name')|escape|default:'&nbsp;'}</h1>
<br />

<h2>{help label_content=#reports_general_info_title#}</h2>
<table border="1">
  <tr>
    <th colspan="2">{$vars[$smarty.const.VEHICLE_MAKE_VAR].label|escape}</th>
    <th colspan="2">{$vars[$smarty.const.VEHICLE_MODEL_VAR].label|escape}</th>
    <th>{$vars[$smarty.const.VEHICLE_REG_NUM_VAR].label|escape}</th>
    <th colspan="2">{#reports_chassis_num#|escape}</th>
    <th>{#reports_first_reg_date#|escape}</th>
    <th>{#reports_acquisition_date#|escape}</th>
    <th>{$vars[$smarty.const.VEHICLE_PRICE_NET_VAR].label|escape}</th>
    <th>{$vars[$smarty.const.VEHICLE_PRICE_GROSS_VAR].label|escape}</th>
    <th>{#reports_active_contract#|escape}</th>
  </tr>
  <tr>
    <td colspan="2">{$vars[$smarty.const.VEHICLE_MAKE_VAR].value|escape|default:'&nbsp;'}</td>
    <td colspan="2">{$vars[$smarty.const.VEHICLE_MODEL_VAR].value|escape|default:'&nbsp;'}</td>
    <td class="text">{$vars[$smarty.const.VEHICLE_REG_NUM_VAR].value|escape|default:'&nbsp;'}</td>
    <td colspan="2" class="text">{$model->get('code')|escape|default:'&nbsp;'}</td>
    <td>{$vars[$smarty.const.VEHICLE_REG_DATE_VAR].value|date_format:#date_short#|escape|default:'&nbsp;'}</td>
    <td>{$vars[$smarty.const.VEHICLE_ACQUISITION_DATE_VAR].value|date_format:#date_short#|escape|default:'&nbsp;'}</td>
    <td class="hright-float">{if $vars[$smarty.const.VEHICLE_PRICE_NET_VAR].value}{$vars[$smarty.const.VEHICLE_PRICE_NET_VAR].value|number_format:2:".":" "}{else}&nbsp;{/if}</td>
    <td class="hright-float">{if $vars[$smarty.const.VEHICLE_PRICE_GROSS_VAR].value}{$vars[$smarty.const.VEHICLE_PRICE_GROSS_VAR].value|number_format:2:".":" "}{else}&nbsp;{/if}</td>
    <td class="text">
      {assign var='active_contract' value=''}
      {if is_array($vehicle_details.contracts) && $vehicle_details.contracts|@count}
        {foreach from=$vehicle_details.contracts item='contract' name='ci' key='ck'}
          {if $contract.num || $contract.status == 'closed' && $contract.substatus == $smarty.const.CONTRACT_SUBSTATUS_SIGNED}
            {assign var='active_contract' value=$contract}
          {/if}
        {/foreach}
      {/if}
      {if $active_contract}{$active_contract.num|default:#no_number#|escape}{else}&nbsp;{/if}
    </td>
  </tr>
</table>
<br />

<h2>{help label_content=#reports_contract_history_title#}</h2>
<table border="1">
  <tr>
    <th>{#num#|escape}</th>
    <th>{#reports_assignor#|escape}</th>
    <th colspan="2">{#reports_contact_assignor#|escape}</th>
    <th>{#reports_user2#|escape}</th>
    <th colspan="2">{#reports_contact_user#|escape}</th>
    <th>{#reports_contract_assignor#|escape}</th>
    <th>{#reports_rent_monthly#|escape} ({#reports_no_vat#|escape})</th>
    <th>{#reports_rent_remaining_num#|escape}</th>
    <th>{#reports_contract_date_sign#|escape}</th>
    <th>{#reports_contract_date_validity#|escape}</th>
  </tr>
  {foreach from=$vehicle_details.contracts item='contract' name='ci' key='ck'}
  <tr class="{cycle name='contracts' values='t_odd1,t_even1'}">
    <td class="hright-int">{$smarty.foreach.ci.iteration}</td>
    <td>{$contract.assignor|escape|default:'&nbsp;'}</td>
    <td colspan="2">{$contract.assignor_contact|regex_replace:'#\|[^\n]*(\n|$)#':'$1'|nl2br}</td>
    <td>{if $vars[$smarty.const.VEHICLE_USER_ID_VAR].value}{$vars[$smarty.const.VEHICLE_USER_VAR].value|escape|default:'&nbsp;'}{else}&nbsp;{/if}</td>
    <td colspan="2">{$contract.user_contact|regex_replace:'#\|[^\n]*(\n|$)#':'$1'|nl2br}</td>
    <td class="text">{$contract.num|default:#no_number#|escape}</td>
    <td class="hright-float">{$contract.rent_monthly|number_format:2:".":" "|escape|default:'&nbsp;'}</td>
    <td class="hright-text">{$contract.remaining_periods} / {$contract.total_periods}</td>
    <td>{$contract.date_sign|date_format:#date_short#|escape|default:'&nbsp;'}</td>
    <td>{$contract.date_validity|date_format:#date_short#|escape|default:'&nbsp;'}</td>
  </tr>
  {foreachelse}
  <tr>
    <td class="legend" colspan="12">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
</table>
<br />

<h2>{help label_content=#reports_payments_terms_title#}</h2>
<table border="1">
  <tr>
    <th>{#reports_civil_liability_to#|escape}</th>
    <th>{#reports_casco_to#|escape}</th>
    <th>{#reports_tax_to#|escape}</th>
    <th>{#reports_technical_inspection_to#|escape}</th>
    <th>{#reports_warranty_to#|escape}</th>
    <th>{#reports_proxy_to#|escape}</th>
  </tr>
  <tr>
    <td>{$vars[$smarty.const.VEHICLE_CIVIL_LIABILITY_TO_VAR].value|date_format:#date_short#|escape|default:'&nbsp;'}</td>
    <td>{$vars[$smarty.const.VEHICLE_CASCO_TO_VAR].value|date_format:#date_short#|escape|default:'&nbsp;'}</td>
    <td>{$vars[$smarty.const.VEHICLE_TAX_TO_VAR].value|date_format:#date_short#|escape|default:'&nbsp;'}</td>
    <td>{$vars[$smarty.const.VEHICLE_TECHNICAL_INSPECTION_TO_VAR].value|date_format:#date_short#|escape|default:'&nbsp;'}</td>
    <td>{if $vars[$smarty.const.VEHICLE_WARRANTY_EXTENDED_TO_VAR].value}{$vars[$smarty.const.VEHICLE_WARRANTY_EXTENDED_TO_VAR].value|date_format:#date_short#|escape}{else}{$vars[$smarty.const.VEHICLE_WARRANTY_TO_VAR].value|date_format:#date_short#|escape|default:'&nbsp;'}{/if}</td>
    <td>{if $vehicle_details.proxy}{$vehicle_details.proxy.date_to|date_format:#date_short#|escape|default:'&nbsp;'}{if $vehicle_details.proxy.person} ({$vehicle_details.proxy.person|escape}){/if}{else}&nbsp;{/if}</td>
  </tr>
</table>
<br />

<h2>{help label_content=#reports_revenue_expense_title#}</h2>
<table border="1">
  <tr>
    <th>{#num#|escape}</th>
    <th>{#num_inwords#|escape}</th>
    <th>{#date#|escape}</th>
    <th>{#date_of_payment#|escape}</th>
    <th>{#gt2_total#|escape} ({#reports_no_vat#|escape})</th>
    <th>{#reports_currency#|escape}</th>
    <th>{#reports_paid#|escape} ({#reports_no_vat#|escape})</th>
    <th>{#reports_unpaid#|escape} ({#reports_no_vat#|escape})</th>
    <th colspan="2">{#reports_services#|escape}</th>
  </tr>
  {foreach from=$vehicle_details.incomes item='incomes_reason' key='rk' name='ri'}
    {capture assign='rowspan'}{if is_array($incomes_reason->get('values'))}{$incomes_reason->get('values')|@count}{else}0{/if}{/capture}
    {if !$rowspan}{assign var='rowspan' value=1}{/if}
    {foreach from=$incomes_reason->get('values') item='row' key='gk' name='gi'}
      {if $smarty.foreach.gi.first}
      <tr style="background-color: {if $incomes_reason->get('payment_status') == 'paid'}#A5DBA8{else}#EFB9B9{/if};">
        <td rowspan="{$rowspan}" class="hright-int">{$smarty.foreach.ri.iteration}</td>
        <td rowspan="{$rowspan}" class="text">{$incomes_reason->get('num')|default:#no_number#|escape}</td>
        <td rowspan="{$rowspan}">{$incomes_reason->get('issue_date')|date_format:#date_short#|escape}</td>
        {include file=`$smarty.const.PH_MODULES_DIR`/outlooks/templates/td/finance_incomes_reason_date_of_payment.html row_link=" rowspan=\"`$rowspan`\"" single=$incomes_reason}
        <td rowspan="{$rowspan}" class="hright-float">{$incomes_reason->get('vehicle_subtotal')|number_format:2:".":" "}</td>
        <td rowspan="{$rowspan}">{$incomes_reason->get('currency')|escape}</td>
        <td rowspan="{$rowspan}" class="hright ">{$incomes_reason->get('vehicle_paid_amount')|number_format:2:".":" "}</td>
        <td rowspan="{$rowspan}" class="hright-float">{$incomes_reason->get('vehicle_remaining_amount')|number_format:2:".":" "}</td>
      {else}
      <tr style="background-color: {if $incomes_reason->get('payment_status') == 'paid'}#A5DBA8{else}#EFB9B9{/if};">
      {/if}
        <td colspan="2">{$row.article_name|escape|default:'&nbsp;'}</td>
      </tr>
    {/foreach}
  {foreachelse}
  <tr>
    <td class="legend" colspan="10">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
  {if is_array($vehicle_details.incomes) && $vehicle_details.incomes|@count}
  <tr class="t_odd1" style="font-weight: bold;">
    <td class="hright" colspan="4">{#gt2_total#|escape} ({$smarty.const.DEFAULT_CURRENCY}):</td>
    <td class="hright-float">{$vehicle_details.totals.incomes.vehicle_subtotal|default:0|number_format:2:".":" "}</td>
    <td>&nbsp;</td>
    <td class="hright-float"{if $vehicle_details.totals.incomes.vehicle_paid_amount gt 0} style="color: #298923;"{/if}>{$vehicle_details.totals.incomes.vehicle_paid_amount|default:0|number_format:2:".":" "}</td>
    <td class="hright-float"{if $vehicle_details.totals.incomes.vehicle_remaining_amount gt 0} style="color: #FF0000;"{/if}>{$vehicle_details.totals.incomes.vehicle_remaining_amount|default:0|number_format:2:".":" "}</td>
    <td colspan="2">&nbsp;</td>
  </tr>
  {/if}
</table>
<br />

<table border="1">
  <tr>
    <th>{#num#|escape}</th>
    <th>{#num_inwords#|escape}</th>
    <th>{#date#|escape}</th>
    <th>{#date_of_payment#|escape}</th>
    <th>{#gt2_total#|escape} ({#reports_no_vat#|escape})</th>
    <th>{#reports_currency#|escape}</th>
    <th>{#reports_paid#|escape} ({#reports_no_vat#|escape})</th>
    <th>{#reports_unpaid#|escape} ({#reports_no_vat#|escape})</th>
    <th colspan="2">{#reports_services#|escape}</th>
  </tr>
  {foreach from=$vehicle_details.expenses item='expenses_reason' key='rk' name='ri'}
    {capture assign='rowspan'}{if is_array($expenses_reason->get('values'))}{$expenses_reason->get('values')|@count}{else}0{/if}{/capture}
    {if !$rowspan}{assign var='rowspan' value=1}{/if}
    {foreach from=$expenses_reason->get('values') item='row' key='gk' name='gi'}
      {if $smarty.foreach.gi.first}
      <tr style="background-color: {if $expenses_reason->get('payment_status') == 'paid'}#A5DBA8{else}#EFB9B9{/if};">
        <td rowspan="{$rowspan}" class="hright-int">{$smarty.foreach.ri.iteration}</td>
        <td rowspan="{$rowspan}" class="text">{$expenses_reason->get('invoice_num')|default:#no_number#|escape}</td>
        <td rowspan="{$rowspan}">{$expenses_reason->get('issue_date')|date_format:#date_short#|escape}</td>
        <td rowspan="{$rowspan}">{$expenses_reason->get('date_of_payment')|date_format:#date_short#|escape}</td>
        <td rowspan="{$rowspan}" class="hright-float">{$expenses_reason->get('vehicle_subtotal')|number_format:2:".":" "}</td>
        <td rowspan="{$rowspan}">{$expenses_reason->get('currency')|escape}</td>
        <td rowspan="{$rowspan}" class="hright-float">{$expenses_reason->get('vehicle_paid_amount')|number_format:2:".":" "}</td>
        <td rowspan="{$rowspan}" class="hright-float">{$expenses_reason->get('vehicle_remaining_amount')|number_format:2:".":" "}</td>
      {else}
      <tr style="background-color: {if $expenses_reason->get('payment_status') == 'paid'}#A5DBA8{else}#EFB9B9{/if};">
      {/if}
        <td colspan="2">{$row.article_name|escape|default:'&nbsp;'}</td>
      </tr>
    {/foreach}
  {foreachelse}
  <tr>
    <td class="legend" colspan="10">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
  {if is_array($vehicle_details.expenses) && $vehicle_details.expenses|@count}
  <tr class="t_odd1" style="font-weight: bold;">
    <td class="hright" colspan="4">{#gt2_total#|escape} ({$smarty.const.DEFAULT_CURRENCY}):</td>
    <td class="hright-float">{$vehicle_details.totals.expenses.vehicle_subtotal|default:0|number_format:2:".":" "}</td>
    <td>&nbsp;</td>
    <td class="hright-float"{if $vehicle_details.totals.expenses.vehicle_paid_amount gt 0} style="color: #298923;"{/if}>{$vehicle_details.totals.expenses.vehicle_paid_amount|default:0|number_format:2:".":" "}</td>
    <td class="hright-float"{if $vehicle_details.totals.expenses.vehicle_remaining_amount gt 0} style="color: #FF0000;"{/if}>{$vehicle_details.totals.expenses.vehicle_remaining_amount|default:0|number_format:2:".":" "}</td>
    <td colspan="2">&nbsp;</td>
  </tr>
  {/if}
</table>
<br />

<h2>{help label_content=#reports_contracts_obligations_title#}</h2>
<table border="1">
  <tr>
    <th>{#num#|escape}</th>
    <th colspan="3">{#reports_contract_num#|escape}</th>
    <th>{#reports_total#|escape}</th>
    <th>{#reports_overdue#|escape}</th>
    <th>{#reports_remaining#|escape}</th>
  </tr>
  {foreach from=$vehicle_details.contracts_obligations item='contract' name='ci' key='ck'}
  <tr class="{cycle name='contracts_obligations' values='t_odd1,t_even1'}">
    <td class="hright-int">{$smarty.foreach.ci.iteration}</td>
    <td class="text" colspan="3">{$contract.num|default:#no_number#|escape}</td>
    <td class="hright">{$contract.total|number_format:2:".":" "} {$smarty.const.DEFAULT_CURRENCY}</td>
    <td class="hright">{$contract.overdue|number_format:2:".":" "} {$smarty.const.DEFAULT_CURRENCY}</td>
    <td class="hright">{$contract.remaining|number_format:2:".":" "} {$smarty.const.DEFAULT_CURRENCY}</td>
  </tr>
  {if is_array($contract.invoices) && $contract.invoices|@count}
  <tr>
    <td colspan="7">
      <table border="1">
        <tr class="reports_title_row">
          <th>{#num#|escape}</th>
          <th>{#num_inwords#|escape}</th>
          <th>{#date#|escape}</th>
          <th>{#date_of_payment#|escape}</th>
          <th>{#gt2_total#|escape}</th>
          <th>{#reports_paid#|escape}</th>
          <th>{#reports_unpaid#|escape}</th>
        </tr>
        {assign var='mode' value='invoice'}
        {foreach name=$mode from=$contract.invoices item='record'}
          {assign var='background_style' value=''}
          {assign var='background_properties' value=$record->getBackgroundColor()}
          {if !empty($background_properties)}
            {array_push array='background_colors' new_item=$background_properties.background_color key=$background_properties.definition}
            {capture assign='background_style'} style="background-color: #{$background_properties.background_color}; color: #{$background_properties.text_color};"{/capture}
          {/if}
          <tr {$background_style} class="{if !$background_style}{cycle name='cycle_invoice' values='t_odd1,t_even1'}{/if}">
            <td class="hright-text">{$smarty.foreach.$mode.iteration}</td>
            <td class="text">{$record->get('num')|escape}</td>
            <td>{$record->get('issue_date')|date_format:#date_short#|escape}</td>
            <td>{$record->get('date_of_payment')|date_format:#date_short#|escape}</td>
            <td class="hright">{$record->get('total_with_vat')|escape|default:"&nbsp;"} {$record->get('currency')|escape|default:"&nbsp;"}</td>
            <td class="hright">{$record->getFullPaidAmount()|default:0|string_format:"%.2f"} {$record->get('currency')|escape|default:"&nbsp;"}</td>
            <td class="hright">{math equation=x-y x=$record->get('total_with_vat') y=$record->getFullPaidAmount()|default:0 format="%.2f"} {$record->get('currency')|escape|default:"&nbsp;"}</td>
          </tr>
          {if $record->get('credit_debit')}
            {assign var='parent_iteration' value=$smarty.foreach.$mode.iteration}
            {assign var='mode' value='credit_debit'}
            {foreach name=$mode from=$record->get('credit_debit') item='record'}
              {assign var='background_style' value=''}
              {assign var='background_properties' value=$record->getBackgroundColor()}
              {if !empty($background_properties)}
                {array_push array='background_colors' new_item=$background_properties.background_color key=$background_properties.definition}
                {capture assign='background_style'} style="background-color: #{$background_properties.background_color}; color: #{$background_properties.text_color};"{/capture}
              {/if}
              <tr {$background_style} class="{if !$background_style}{cycle name=cycle_`$mode`_`$parent_iteration` values='t_odd1,t_even1'}{/if}">
                <td class="hright-text">{$parent_iteration}.{$smarty.foreach.$mode.iteration}</td>
                <td class="text">{$record->get('num')|escape}</td>
                <td>{$record->get('issue_date')|date_format:#date_short#|escape}</td>
                <td>{$record->get('date_of_payment')|date_format:#date_short#|escape}</td>
                <td class="hright">{$record->get('total_with_vat')|escape|default:"&nbsp;"} {$record->get('currency')|escape|default:"&nbsp;"}</td>
                <td class="hright">{$record->getFullPaidAmount()|default:0|string_format:"%.2f"} {$record->get('currency')|escape|default:"&nbsp;"}</td>
                <td class="hright">{math equation=x-y x=$record->get('total_with_vat') y=$record->getFullPaidAmount()|default:0 format="%.2f"} {$record->get('currency')|escape|default:"&nbsp;"}</td>
              </tr>
            {/foreach}
          {/if}
        {/foreach}
      </table>
    </td>
  </tr>
  {/if}
  {foreachelse}
  <tr>
    <td class="legend" colspan="7">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
</table>
<br />

<table border="1">
  <tr>
    <th>{#reports_deposit_amount#|escape}</th>
    <th>{#reports_date_of_payment#|escape}</th>
    <th>{#reports_used_amount#|escape}</th>
  </tr>
  <tr>
    <td class="hright">{if $vehicle_details.deposit.amount}{$vehicle_details.deposit.amount|number_format:2:".":" "} {$smarty.const.DEFAULT_CURRENCY}{else}-{/if}</td>
    <td>{if $vehicle_details.deposit.date_of_payment}{$vehicle_details.deposit.date_of_payment|date_format:#date_short#|escape}{else}-{/if}</td>
    <td class="hright">{if $vehicle_details.deposit.used_amount}{$vehicle_details.deposit.used_amount|number_format:2:".":" "} {$smarty.const.DEFAULT_CURRENCY}{else}-{/if}</td>
  </tr>
</table>
<br />

<h2>{help label_content=#reports_other_documents#}</h2>
<table border="1">
  <tr>
    <th colspan="2">{#file#|escape}</th>
    <th colspan="2">{#history_event_text#|escape}</th>
  </tr>
  {assign var='has_files' value=false}
  {foreach from=$vars[$smarty.const.VEHICLE_FILE_VAR].value item='file_info' key='fk' name='fi'}
    {if !empty($file_info) && is_object($file_info) && !$file_info->get('deleted_by')}
      {assign var='has_files' value=true}
      <tr class="blue">
        <td colspan="2">
          {$file_info->get('filename')|escape}
        </td>
        <td colspan="2">
          {if !empty($vars[$smarty.const.VEHICLE_FILE_TYPE_VAR].value.$fk)}
            {foreach from=$vars[$smarty.const.VEHICLE_FILE_TYPE_VAR].options item='opt'}
              {if $opt.option_value === $vars[$smarty.const.VEHICLE_FILE_TYPE_VAR].value.$fk}
                {$opt.label|escape|default:'&nbsp;'}
              {/if}
            {/foreach}
          {else}
            &nbsp;
          {/if}
        </td>
      </tr>
    {/if}
  {/foreach}
  {foreach from=$vehicle_details.files item='file_info' key='fk' name='fi'}
    {if !empty($file_info) && is_object($file_info) && !$file_info->get('deleted_by')}
      {assign var='has_files' value=true}
      <tr class="blue">
        <td colspan="2">
          {$file_info->get('filename')|escape}
        </td>
        <td colspan="2">
          {$file_info->get('description')|escape|default:'&nbsp;'}
        </td>
      </tr>
    {/if}
  {/foreach}
  {if !$has_files}
  <tr>
    <td class="legend" colspan="4">{#no_items_found#|escape}</td>
  </tr>
  {/if}
</table>
{else}
<div class="error">{#no_items_found#|escape}</div>
{/if}
