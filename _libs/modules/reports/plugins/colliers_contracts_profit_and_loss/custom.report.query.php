<?php
    Class Colliers_Contracts_Profit_And_Loss Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            ini_set("memory_limit", '2048M');

            if (!defined('CONTRACT_RENT_ID')) {
                define('CONTRACT_RENT_ID', 1);
            }
            if (!defined('BASE_RENT_PRICE')) {
                define('BASE_RENT_PRICE', 1034);
            }
            if (!defined('BASE_RENT_PRICE_OP')) {
                define('BASE_RENT_PRICE_OP', 1038);
            }
            if (!defined('TAX_SERVICE')) {
                define('TAX_SERVICE', 1035);
            }

            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array(
                'rents'             => array(),
                'services'          => array(),
                'rents_totals'      => array(),
                'services_totals'   => array(),
                'full_totals'       => array(),
            );
            if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                // prepare months options
                $months_options = array();
                $months_labels = array();

                $date_from_sec = strtotime($filters['from_date']);
                $date_to_sec = strtotime($filters['to_date']);
                $month_date_from = General::strftime('%m', $date_from_sec);
                $year_date_from = General::strftime('%Y', $date_from_sec);
                $start_period_date = mktime(0, 0, 0, $month_date_from, 1, $year_date_from);

                $month_date_to = General::strftime('%m', $date_to_sec);
                $year_date_to = General::strftime('%Y', $date_to_sec);
                $first_month_date_to_sec = mktime(0, 0, 0, $month_date_to, 1, $year_date_to);
                $end_period_date = strtotime('+1 month', $first_month_date_to_sec);

                for ($j=$start_period_date; $j<$end_period_date; $j=strtotime('+1 month', $j)) {
                    $month_key = General::strftime('%Y-%m', $j);
                    $months_options[$month_key] = 0;
                    $months_labels[$month_key] = General::strftime('%B %Y', $j, true);
                }

                $final_results['rents_totals'] = $months_options;
                $final_results['rents_totals']['total'] = 0;
                $final_results['services_totals'] = $months_options;
                $final_results['services_totals']['total'] = 0;
                $final_results['full_totals'] = $months_options;
                $final_results['full_totals']['total'] = 0;

                if (!empty($filters['show'])) {
                    if (!in_array('rent', $filters['show'])) {
                        unset($final_results['rents_totals']);
                        unset($final_results['rents']);
                    }
                    if (!in_array('tax_service', $filters['show'])) {
                        unset($final_results['services_totals']);
                        unset($final_results['services']);
                    }
                }

                require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                $contract_params = array(
                    'date_from'         => General::strftime('%Y-%m-%d', $start_period_date),
                    'date_to'           => General::strftime('%Y-%m-%d', strtotime('-1 day', $end_period_date)),
                    'recurrent_only'    => true
                );

                // prepare tenants filter
                if (!empty($filters['renters'])) {
                    $searched_tm = array();
                    foreach ($filters['renters'] as $renter) {
                        if ($renter && !in_array($renter, $searched_tm)) {
                            $searched_tm[] = $renter;
                        }
                    }
                    if (!empty($searched_tm)) {
                        $contract_params['trademarks_ids'] = $searched_tm;
                    }
                }
                // get all the invoices that has to issued in this period
                $contracts_info = Contracts::getAmountDue($registry, $contract_params);

                // take customers and nomenclatures info
                $searched_contracts_ids = array_keys($contracts_info);
                $commercial_areas_ids = array();

                // take the contracts and commercial areas which we need information for
                foreach ($contracts_info as $key_con => $con_info) {
                    foreach ($con_info['timeline'] as $period => $active_contract) {
                        if ($active_contract && !in_array($active_contract, $searched_contracts_ids)) {
                            $searched_contracts_ids[] = $active_contract;
                        }
                    }
                    if (!empty($con_info['rows'])) {
                        foreach ($con_info['rows'] as $gt2_rows) {
                            if (!in_array($gt2_rows['free_field1'], $commercial_areas_ids)) {
                                $commercial_areas_ids[] = $gt2_rows['free_field1'];
                            }
                        }
                    }
                }

                // take the customers, trademarks and rental prices for the required contracts
                $contracts_customer_tm_information = array();
                if (!empty($searched_contracts_ids)) {
                    $query_contracts = 'SELECT con.id as id, con.customer as customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, con.trademark as trademark, ni18n.name as trademark_name ' . "\n" .
                                       'FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                       '  ON (con.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                       '  ON (con.trademark=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                                       'WHERE con.active=1 AND con.deleted_by=0 AND con.id IN (' . implode(',', $searched_contracts_ids) . ')';

                    $records_contracts_customers = $registry['db']->GetAll($query_contracts);

                    foreach ($records_contracts_customers as $rcc) {
                        if (!isset($contracts_customer_tm_information[$rcc['id']])) {
                            $contracts_customer_tm_information[$rcc['id']] = array(
                                'id'            => $rcc['id'],
                                'customer'      => $rcc['customer'],
                                'customer_name' => $rcc['customer_name'],
                                'trademark'     => $rcc['trademark'],
                                'trademark_name'=> $rcc['trademark_name']
                            );
                        }
                    }
                }

                // get the names of the required commercial areas
                $commercial_areas_info = array();
                if (!empty($commercial_areas_ids)) {
                    $query_nomenclatures = 'SELECT nom.id as id_idx, ni18n.name as name' . "\n" .
                                           'FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                           '  ON (nom.id=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                                           'WHERE nom.id IN (' . implode(',', $commercial_areas_ids) . ')';

                    $commercial_areas_info = $registry['db']->GetAssoc($query_nomenclatures);
                }

                // calculate final results
                foreach ($contracts_info as $key_con => $con_info) {
                    $main_contract_info = $contracts_customer_tm_information[$key_con];
                    $current_customer_id = $main_contract_info['customer'];
                    $current_trademark_id = $main_contract_info['trademark'];

                    if (!empty($con_info['rows'])) {

                        foreach ($con_info['rows'] as $gt2_rows) {
                            if ($gt2_rows['template_currency'] != $filters['currency']) {
                                $currency_multiplier = Finance_Currencies::getRate($registry, $gt2_rows['template_currency'], $filters['currency']);
                            } else {
                                $currency_multiplier = 1;
                            }

                            $included_in_table = '';
                            $included_in_table_total = '';
                            $all_total_var = '';
                            if ($gt2_rows['article_id'] == BASE_RENT_PRICE || $gt2_rows['article_id'] == BASE_RENT_PRICE_OP) {
                                $included_in_table = 'rents';
                                $included_in_total_table = 'rents_totals';
                            } else if ($gt2_rows['article_id'] == TAX_SERVICE) {
                                $included_in_table = 'services';
                                $included_in_total_table = 'services_totals';
                            }

                            if ($included_in_table && $included_in_total_table && isset($final_results[$included_in_table])) {
                                if ($gt2_rows['recurrence_period'] == 'month' || $gt2_rows['recurrence_period'] == 'month') {
                                    $area_id = $gt2_rows['free_field1'];

                                    $array_key = $current_customer_id . '_' . $current_trademark_id . '_' . $area_id;
                                    if (!isset($final_results[$included_in_table][$array_key])) {
                                        $final_results[$included_in_table][$array_key] = array(
                                            'customer_id'   => $main_contract_info['customer'],
                                            'customer_name' => $main_contract_info['customer_name'],
                                            'trademark_id'  => $main_contract_info['trademark'],
                                            'trademark_name'=> $main_contract_info['trademark_name'],
                                            'area_name'     => $commercial_areas_info[$area_id],
                                            'periods'       => array(),
                                            'months'        => $months_options,
                                            'total'         => 0
                                        );
                                        asort($final_results[$included_in_table][$array_key]);
                                    }

                                    // period key
                                    $period_key = $gt2_rows['recurrence_count'] . '_' . $gt2_rows['recurrence_period'];
                                    if (!isset($final_results[$included_in_table][$array_key]['periods'][$period_key])) {
                                        $lang_var = 'reports_recurrence_period_' . $gt2_rows['recurrence_period'];
                                        $final_results[$included_in_table][$array_key]['periods'][$period_key] = $gt2_rows['recurrence_count'] . ' ' . $registry['translater']->translate($lang_var);
                                    }

                                    $row_date_sec_from = strtotime($gt2_rows['date_from']);
                                    $row_date_sec_to = strtotime($gt2_rows['date_to']);

                                    // defines which period this invoice will be included in
                                    if (General::strftime('%Y-%m', $row_date_sec_from) == General::strftime('%Y-%m', $row_date_sec_to)) {
                                        $month_key = General::strftime('%Y-%m', $gt2_rows['date_from']);
                                        $calculated_sum = sprintf("%01.2f", ((($gt2_rows['price']-$gt2_rows['discount_value']) * $gt2_rows['quantity']) * $currency_multiplier));
                                        $final_results[$included_in_table][$array_key]['months'][$month_key] = sprintf("%01.2f", ($final_results[$included_in_table][$array_key]['months'][$month_key] + $calculated_sum));
                                        $final_results[$included_in_table][$array_key]['total'] = sprintf("%01.2f", ($final_results[$included_in_table][$array_key]['total'] + $calculated_sum));
                                        $final_results[$included_in_total_table][$month_key] = sprintf("%01.2f", ($final_results[$included_in_total_table][$month_key] + $calculated_sum));
                                        $final_results[$included_in_total_table]['total'] = sprintf("%01.2f", ($final_results[$included_in_total_table]['total'] + $calculated_sum));
                                        $final_results['full_totals'][$month_key] = sprintf("%01.2f", ($final_results['full_totals'][$month_key] + $calculated_sum));
                                        $final_results['full_totals']['total'] = sprintf("%01.2f", ($final_results['full_totals']['total'] + $calculated_sum));
                                    } else {
                                        $calculated_sum_all_period = sprintf("%01.2f", ((($gt2_rows['price']-$gt2_rows['discount_value']) * $gt2_rows['quantity']) * $currency_multiplier));
                                        $days_period = round(($row_date_sec_to - $row_date_sec_from) / 86400, 0) + 1;
                                        $sum_per_day = sprintf("%01.2f", ($calculated_sum_all_period / $days_period));

                                        $current_calc_date = $row_date_sec_from;
                                        $i = 0;
                                        while ($current_calc_date<$row_date_sec_to && $i<100) {
                                            $i++;
                                            // retrieve last month date
                                            $current_month = General::strftime('%m', $current_calc_date);
                                            $current_year = General::strftime('%Y', $current_calc_date);
                                            $first_month_date = mktime(0, 0, 0, $current_month, 1, $current_year);
                                            $next_month_date = strtotime('+1 month', $first_month_date);
                                            if ($next_month_date>$row_date_sec_to) {
                                                $calculated_period_days = round(($row_date_sec_to - $current_calc_date) / 86400, 0) + 1;
                                                $calculated_sum = sprintf("%01.2f", ($calculated_period_days * $sum_per_day));
                                            } else {
                                                $calculated_period_days = round(($next_month_date - $current_calc_date) / 86400, 0);
                                                $calculated_sum = sprintf("%01.2f", ($calculated_period_days * $sum_per_day));
                                            }

                                            $month_key = $current_year . '-' . $current_month;

                                            $final_results[$included_in_table][$array_key]['months'][$month_key] = sprintf("%01.2f", ($final_results[$included_in_table][$array_key]['months'][$month_key] + $calculated_sum));
                                            $final_results[$included_in_table][$array_key]['total'] = sprintf("%01.2f", ($final_results[$included_in_table][$array_key]['total'] + $calculated_sum));
                                            $final_results[$included_in_total_table][$month_key] = sprintf("%01.2f", ($final_results[$included_in_total_table][$month_key] + $calculated_sum));
                                            $final_results[$included_in_total_table]['total'] = sprintf("%01.2f", ($final_results[$included_in_total_table]['total'] + $calculated_sum));
                                            $final_results['full_totals'][$month_key] = sprintf("%01.2f", ($final_results['full_totals'][$month_key] + $calculated_sum));
                                            $final_results['full_totals']['total'] = sprintf("%01.2f", ($final_results['full_totals']['total'] + $calculated_sum));

                                            $calculated_sum_all_period = sprintf("%01.2f", ($gt2_rows['subtotal_with_discount'] * $currency_multiplier));

                                            $current_calc_date = $next_month_date;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $final_results['months_labels'] = $months_labels;
                    $final_results['total_columns'] = 5 + count($months_labels);
                }

            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>
