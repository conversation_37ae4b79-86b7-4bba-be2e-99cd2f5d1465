/**
 * custom javascript messages
 */
if (env.current_lang == 'bg') {
    i18n['messages']['error_penalty_overpaid'] = 'Сумата на полето "Наказ. лихва по вноска" не може да е по-малко от вече платената сума по наказателната лихва ("Пл. нак. лихва по вноска")!';
    i18n['messages']['error_amount_left_less_than_zero'] = 'Общият остатък от вноската не може да е по-малък от 0!';
    i18n['messages']['error_negative_amount_less_than_allowed'] = 'От съображения за сигурност, негативната стойност на полето "Наказ. лихва по вноска" не може да е по-малка от -3!';
    i18n['messages']['error_complete_required_fields'] = 'Моля, попълнете задължителните полета!';
    i18n['messages']['error_sum_not_enough_to_cover_obligations_by_target_date'] = 'Въведената сума е по-малка от задълженията към посочената дата!';
    i18n['messages']['error_complete_sums_not_correct'] = 'Въведените суми в част от полетата не са коректни! Моля, коригирайте сумите иопитайте отново!';
    i18n['messages']['error_complete_sums_principal_not_correct'] = 'Въведената сума за главница не е коректна! Моля, коригирайте сумите и опитайте отново!';
    i18n['messages']['error_complete_sums_more_than_owed'] = 'Въведената сума по перата надвишава дължимата към посочената дата! Моля, коригирайте сумите иопитайте отново!';
} else {
    i18n['messages']['error_penalty_overpaid'] = 'The sum in the field "Penalty" cannot be less than the penalty sum already paid ("Paid penalty")!';
    i18n['messages']['error_amount_left_less_than_zero'] = 'The amount left for tha payment can not be less than 0!';
    i18n['messages']['error_negative_amount_less_than_allowed'] = 'For security reasons, the negative value of the field field "Penalty" can not be less than -3!';
    i18n['messages']['error_complete_required_fields'] = 'Please, complete required fields!';
    i18n['messages']['error_sum_not_enough_to_cover_obligations_by_target_date'] = 'Entered sum is less than the obligation by the entered date!';
    i18n['messages']['error_complete_sums_not_correct'] = 'Completed sums in some of the fields are not correct! Please, correct the sums and try again!';
    i18n['messages']['error_complete_sums_principal_not_correct'] = 'The completed sum for principal is not correct! Please, correct the sums and try again!';
    i18n['messages']['error_complete_sums_more_than_owed'] = 'The entered sums is more that the owed until the enetered date! Please, correct the sums and try again!';
}

/**
 * Function to prepare the redirect to list for proceedings
 */
function credilinkRedirectToCustomerProceedings(customer_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_redirect_to_customer_proceedings&report_type=' + $('report_type').value + '&customer_id=' + customer_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            if (result) {
                window.open(result, '_blank');
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);

    return true;
}

/**
 * Function to save the customer note with AJAX
 */
function credilinkSaveCustomerNote(customer_id, customer_note_field) {
    Effect.Center('loading');
    Effect.Appear('loading');

    customer_note = $(customer_note_field);

    var opt = {
        method: 'post',
        parameters: Form.serialize(customer_note.form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            alert(result);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_save_customer_note&report_type=' + $('report_type').value + '&customer_id=' + customer_id;
    new Ajax.Request(url, opt);
}

/**
 * Function to activate the form for advance cover of a loan
 */
function credilinkAdvanceLoanCover(contract_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_advance_loan_cover&report_type=' + $('report_type').value + '&contract_id=' + contract_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            // Activate the lightbox
            lb = new lightbox({
                content: result['template']['content'],
                title:   result['template']['title'],
                width:   '470px'});

            Effect.Fade('loading');
            lb.activate();
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to activate the form for advance principal cover
 */
function credilinkAdvancePrincipalCover(contract_id, cover_date = '', cover_sum = '') {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_advance_principal_cover&report_type=' + $('report_type').value + '&contract_id=' + contract_id;
    if (cover_date) {
        url += '&cover_date=' + cover_date;
    }
    if (cover_date) {
        url += '&cover_sum=' + cover_sum;
    }

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            // Activate the lightbox
            lb = new lightbox({
                content: result['template']['content'],
                title:   result['template']['title'],
                width:   '600px'});

            Effect.Fade('loading');
            lb.activate();
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to activate the form for advance cover of a loan
 */
function credilinkCalculateRepaymentPlansAmounts(element) {
    if (!$('advance_pay_date').value) {
        alert(i18n['messages']['alert_empty_field']);
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_advance_loan_cover_calculate&report_type=' + $('report_type').value;

    var opt = {
        asynchronous: false,
        method: 'post',
        parameters: Form.serialize(element.form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');

            if (lb && lb.active) {
                // lightbox is opened, update it without reloading
                lb.update({
                    content: result['content'],
                    title:   result['title']
                });
            } else {
                // Activate the lightbox
                lb = new lightbox({
                    content: result['content'],
                    title:   result['title'],
                    width:   '470px'});
                lb.activate();
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to process the selected edit method
 */
function credilinkProcessRepaymentPlanEditMethod(element, action, contract_id) {
    var reports_method = '';
    var lb_width = '';
    var action_annul = false;
    if (action == 'extend') {
        reports_method = 'ajax_activate_extend_contract_with_one_period';
        lb_width = '420px';
    } else {
        reports_method = 'ajax_activate_edit_contract';
        lb_width = '1800px';
        if (action == 'annul') {
            action_annul = true;
        }
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=' + reports_method + '&report_type=' + $('report_type').value + '&contract_id=' + contract_id;
    if (action_annul) {
        url += '&annul=1';
    }

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');

            if (result.result) {
                // Activate the lightbox
                lb = new lightbox({
                    content: result['content'],
                    title:   result['title'],
                    width:   lb_width,
                    onActivate: function() {
                        credilinkColorSums();
                    }
                });
                lb.activate();
            } else {
                alert (result.message);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to load owed dues to date in the panel for advance principal cover
 */
function loadOwedDuesToDate(date = '') {
    if (!date) {
        date = $('advance_pay_date').value;
    }

    let owed_dues = getOwedDuesInfo(date);
    var pow = Math.pow(10, env.precision.gt2_rows);
    for (i in owed_dues) {
        if (!i.match(/^owed_/)) {
            continue;
        }
        related_element = i.replace(/^owed_/, '');
        visible_field = $('repayment_plan_owed_' + related_element + '_visible');
        hidden_field = $('repayment_plan_owed_' + related_element);
        visible_field.innerHTML = owed_dues[i].toFixed(2);
        hidden_field.value = owed_dues[i].toFixed(2);
    }
    changeAdvancedPaySum();
}

/**
 * Function to get the owed dues info based on the date provided
 * @param date
 */
function getOwedDuesInfo(date) {
    const selected_date = new Date(date);
    let owed_dues = [];
    var pow = Math.pow(10, env.precision.gt2_rows);
    for (i in owed_dues_to_date) {
        let current_check_date = new Date(i);
        if (current_check_date>=selected_date) {
            var item_amount = Math.round(parseFloat(owed_dues_to_date[i].owed_total) * pow) / pow;
            owed_dues = owed_dues_to_date[i];
            break;
        }
    }
    return owed_dues;
}

/**
 * Function to process the selected edit method
 */
function credilinkProcessAdvancePrincipalCover(element) {
    // check the values
    let advance_pay_sum = parseFloat($('advance_pay_sum').value);
    let advance_pay_date = $('advance_pay_date').value;

    if (!advance_pay_date || isNaN(advance_pay_sum)) {
        alert(i18n['messages']['error_complete_required_fields']);
        return false;
    }

    owed_dues = getOwedDuesInfo($('advance_pay_date').value);
    var pow = Math.pow(10, env.precision.gt2_rows);
    if (advance_pay_sum <= Math.round(parseFloat(owed_dues.owed_total) * pow) / pow) {
        alert(i18n['messages']['error_sum_not_enough_to_cover_obligations_by_target_date']);
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_principal_cover_change_plan&report_type=' + $('report_type').value +
                                                              '&contract_id=' + $('repayment_plan_id').value +
                                                              '&principal_repayment_date=' + $('advance_pay_date').value +
                                                              '&principal_repayment_sum=' + $('advance_pay_sum').value;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            alert(result.message);
            if (result.result) {
                reloadReportPanels();
                lb.deactivate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to recalculate the last table row based on the entered value
 */
function credilinkRecalculateLockedTableRecords(element) {
    var row_id = element.id.replace(/^[a-zA-Z_]*_(\d+)$/, '$1');

    var principal_amount = isNaN(parseFloat($('price_' + row_id).value)) ? 0 : Math.round(parseFloat($('price_' + row_id).value)*100)/100;
    var interest_amount = isNaN(parseFloat($('quantity_' + row_id).value)) ? 0 : Math.round(parseFloat($('quantity_' + row_id).value)*100)/100;
    var warranty_amount = isNaN(parseFloat($('article_trademark_' + row_id).value)) ? 0 : Math.round(parseFloat($('article_trademark_' + row_id).value)*100)/100;
    interest_warranty = Math.round((interest_amount+warranty_amount)*100)/100;
    var pentalty_amount = isNaN(parseFloat($('article_delivery_code_' + row_id).value)) ? 0 : Math.round(parseFloat($('article_delivery_code_' + row_id).value)*100)/100;
    total = Math.round((principal_amount+pentalty_amount+interest_warranty)*100)/100;

    // paid amounts
    var paid_principal_amount = isNaN(parseFloat($('article_deliverer_name_' + row_id).value)) ? 0 : Math.round(parseFloat($('article_deliverer_name_' + row_id).value)*100)/100;
    var paid_interest_amount = isNaN(parseFloat($('article_barcode_' + row_id).value)) ? 0 : Math.round(parseFloat($('article_barcode_' + row_id).value)*100)/100;
    var paid_warranty_amount = isNaN(parseFloat($('article_height_' + row_id).value)) ? 0 : Math.round(parseFloat($('article_height_' + row_id).value)*100)/100;
    var paid_pentalty_amount = isNaN(parseFloat($('article_weight_' + row_id).value)) ? 0 : Math.round(parseFloat($('article_weight_' + row_id).value)*100)/100;
    paid_interest_warranty = Math.round((paid_interest_amount+paid_warranty_amount)*100)/100;
    paid_total = Math.round((paid_principal_amount+paid_interest_warranty+paid_pentalty_amount)*100)/100;

    $('last_delivery_price_' + row_id).value = interest_warranty.toFixed(2);
    $('article_second_code_' + row_id).value = total.toFixed(2);

    $('average_weighted_delivery_price_' + row_id).value = (Math.round((principal_amount-paid_principal_amount)*100)/100).toFixed(2);
    $('free_field1_' + row_id).value = (Math.round((interest_amount-paid_interest_amount)*100)/100).toFixed(2);
    $('free_field2_' + row_id).value = (Math.round((warranty_amount-paid_warranty_amount)*100)/100).toFixed(2);
    $('free_field3_' + row_id).value = (Math.round((interest_warranty-paid_interest_warranty)*100)/100).toFixed(2);
    $('free_field4_' + row_id).value = (Math.round((pentalty_amount-paid_pentalty_amount)*100)/100).toFixed(2);
    $('free_field5_' + row_id).value = (Math.round((total-paid_total)*100)/100).toFixed(2);

    return true;
}

/**
 * Function to set the edited value in proper format
 */
function credilinkRoundEditedValues(element) {
    if (element.value != '') {
        element_value = element.value;
    } else {
        element_value = 0;
    }
    element.value = (Math.round(parseFloat(element_value)*100)/100).toFixed(2);
    return true;
}

/**
 * Function to extend the contract with one period
 */
function credilinkExtendContract(element) {
    if (!$('extended_payment') || !$('extended_payment').value) {
        alert(i18n['messages']['alert_empty_field']);
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_extend_contract_with_one_period&report_type=' + $('report_type').value;

    var opt = {
        asynchronous: false,
        method: 'post',
        parameters: Form.serialize(element.form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            alert(result.message);

            if (!result.error) {
                reloadReportPanels();
                lb.deactivate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to activate the edit function
 */
function credilinkEditContract(element, contract_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_validate_edit_contract&report_type=' + $('report_type').value + '&contract_id=' + contract_id;

    var opt = {
        asynchronous: false,
        method: 'post',
        parameters: Form.serialize(element.form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var valid_result = eval('(' + t.responseText + ')');
            Effect.Fade('loading');

            if (valid_result.error) {
                var error_text = '';
                for (var p = 0; p < valid_result.message.length; p++) {
                    error_text += valid_result.message[p] + "\n";
                }

                alert(error_text);
                return false;
            } else {
                Effect.Center('loading');
                Effect.Appear('loading');

                // prepare the ajax url
                var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_edit_contract&report_type=' + $('report_type').value + '&contract_id=' + contract_id;

                var opt = {
                    asynchronous: false,
                    method: 'post',
                    parameters: Form.serialize(element.form),
                    onSuccess: function(t) {
                        if (!checkAjaxResponse(t.responseText)) {
                            return;
                        }

                        var result = eval('(' + t.responseText + ')');
                        alert(result.message);

                        if (!result.error) {
                            reloadReportPanels();
                            lb.deactivate();
                        }
                        Effect.Fade('loading');
                    },
                    on404: function(t) {
                        alert('Error 404: location "' + t.statusText + '" was not found.');
                    },
                    onFailure: function(t) {
                        alert('Error ' + t.status + ' -- ' + t.statusText);
                    }
                };

                new Ajax.Request(url, opt);
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to annul the contract
 */
function credilinkAnnulContract(element, contract_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_annul_contract&report_type=' + $('report_type').value + '&contract_id=' + contract_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            alert(result.message);

            if (!result.error) {
                reloadReportPanels();
                lb.deactivate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function activate a lightbox with info for the gt2 table of certain contract
 */
function showContractRepaymentPlanInfo(contract_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_show_paid_repayment_plan_gt2&report_type=' + $('report_type').value + '&contract_id=' + contract_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');

            // Activate the lightbox
            lb = new lightbox({
                content: result['content'],
                title:   result['title'],
                width:   '1305px',
                onActivate: function() {
                    credilinkColorSums();
                }
            });
            lb.activate();

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function activate a lightbox with option to add noi or ckr data
 */
function activateCkrNoiAddPanel(type_report, customer_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_show_ckr_noi_add_panel&report_type=' + $('report_type').value + '&customer_id=' + customer_id + '&type_report_to_add=' + type_report;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');

            // Activate the lightbox
            lb = new lightbox({
                content: result['content'],
                title:   result['title'],
                width:   '420px'});

            lb.activate();
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to save the new data for CKR/NOI
 */
function credilinkSaveCkrNoiData(element) {
    if ($('ckr_noi_add_file').value && $('ckr_noi_add_date').value) {
        var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_save_ckr_noi_data&report_type=' + $('report_type').value;

        Effect.Center('loading');
        Effect.Appear('loading');

        // serialize form
        var get_params = Form.serialize(element.form);

        var opt = {
            asynchronous: false,
            method: 'post',
            parameters: get_params,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var operation_result = eval('(' + t.responseText + ')');
                if (operation_result.result) {
                    var iframe_target = $('upload_target');
                    var form = element.form;
                    form_id = form.id;
                    form.id = 'files_form';

                    iframe_target.onload = function () {processFileUploadResult(this, $('report_ckr_noi_customer').value, operation_result.table_num_row);};

                    var current_form_target = form.target;
                    var current_form_action = form.action;
                    form.action = env.base_url + '?' + env.module_param + '=reports&reports=ajax_submit_ckr_noi_file&report_type=' + $('report_type').value + '&model_id=' + $('report_ckr_noi_customer').value + '&table_num_row=' + operation_result.table_num_row;
                    form.target = iframe_target.id;
                    form.submit();
                    form.target = current_form_target;
                    form.action = current_form_action;
                    form.id = form_id;
                } else {
                    alert(operation_result['message']);
                }

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        new Ajax.Request(url, opt);
    } else {
        alert(i18n['messages']['alert_empty_field']);
        return false;
    }
}

/*
 * Function to process the result of the ajax upload of the file
 */
function processFileUploadResult(element) {
    var scripts = element.contentDocument.getElementsByTagName('SCRIPT');
    eval(scripts[0].text);

    element.onload = function () {return false;};
    element.contentDocument.innerHTML = '';

    if (operation_result.result) {
        lb.deactivate();
        $('ckr_noi_container').innerHTML = operation_result.template;
        Effect.Fade('loading');
    } else {
        alert(operation_result.message);
    }

    return false;
}

/*
 * Function to fully repay a loan contract for a certain date
 */
function credilinkАdvancedLoanCover(repayment_plan, repayment_date) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_fully_cover_loan_contract&report_type=' + $('report_type').value + '&contract_id=' + repayment_plan + '&repayment_date=' + repayment_date;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            alert(result.message);

            if (result.result) {
                reloadReportPanels();
                lb.deactivate();
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to color certain sums in red after repayment plan is loaded
 */
function credilinkColorSums() {
    var fields_to_color = ["article_delivery_code", "article_weight"];
    for (var i=0; i<fields_to_color.length; i++) {
        fields_list = $$('.' + fields_to_color[i]);
        for (var j=0; j<fields_list.length; j++) {
            fields_list[j].style.color = '#FF0000';
        }
    }
}

/*
 * Function to load the panel with smses
 */
function loadSMSINfoPanel(customer_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_load_sms_panel&report_type=' + $('report_type').value + '&customer_id=' + customer_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            // Activate the lightbox
            lb = new lightbox({
                content: result['content'],
                title:   result['title'],
                width:   '750px'});
            lb.activate();

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to expand and fold the tables with the repayment plan
 */
function showHideRepaymentPlan(element) {
    // get the current action
    var action = element.id.replace(/^(show|hide)_(.*)$/, '$1');
    var related_table = element.id.replace(/^(show|hide)_(.*)$/, '$2');
    var payment_sums_table = 'payment_sums_' + related_table;
    if (action == 'show' && $(related_table)) {
        $(related_table).style.display = '';
        $(payment_sums_table).style.display = '';
        $('hide_' + related_table).style.display = '';
        $('show_' + related_table).style.display = 'none';
    } else if (action == 'hide' && $(related_table)) {
        $(related_table).style.display = 'none';
        $(payment_sums_table).style.display = 'none';
        $('hide_' + related_table).style.display = 'none';
        $('show_' + related_table).style.display = '';
    }
}

/*
 * Function to calculate the completed sums in the middle screen
 */
function changeAdvancedPaySum() {
    const calc_elements = ['overdue_principal', 'interest', 'insurance', 'tax1', 'tax2', 'lpg', 'penalty', 'total', 'principal', 'total_principal'];
    const pow = Math.pow(10, env.precision.gt2_rows);
    let initial_sum = Math.round(parseFloat($('advance_pay_sum').value) * pow) / pow;
    let advance_pay_amount = initial_sum;
    for (i=0; i<calc_elements.length; i++) {
        visible_field = $('repayment_plan_owed_' + calc_elements[i] + '_visible');
        hidden_field = $('repayment_plan_owed_' + calc_elements[i]);
        if (calc_elements[i] == 'total_principal') {
            complete_field = $('advance_pay_principal');
        } else {
            complete_field = $('advance_pay_' + calc_elements[i]);
            complete_field.removeClassName('error');
        }
        if (!initial_sum) {
            complete_field.value = '';
            if (calc_elements[i] == 'principal') {
                hidden_field.value = '0.00';
                visible_field.innerHTML = '0.00';
            }
            continue;
        }
        var current_element_pay_sum = Math.round(parseFloat(hidden_field.value) * pow) / pow;
        if (calc_elements[i] == 'principal') {
            hidden_field.value = advance_pay_amount.toFixed(2);
            complete_field.value = advance_pay_amount.toFixed(2);
            visible_field.innerHTML = advance_pay_amount.toFixed(2);
            checkAdvancePaySumsError(complete_field, advance_pay_amount, 0.01);
        } else if (calc_elements[i] == 'total') {
            complete_field.value = (initial_sum - advance_pay_amount).toFixed(2);
        } else if (calc_elements[i] == 'total_principal') {
            let principal_total_left = Math.round(parseFloat($('repayment_plan_owed_total_principal').value) * pow) / pow;
            if (!complete_field.hasClassName('error')) {
                checkAdvancePaySumsError(complete_field, principal_total_left - advance_pay_amount);
            }
        } else {
            if (advance_pay_amount<current_element_pay_sum) {
                complete_field.value = advance_pay_amount.toFixed(2);
                advance_pay_amount = 0;
            } else {
                complete_field.value = current_element_pay_sum.toFixed(2);
                advance_pay_amount = advance_pay_amount - current_element_pay_sum;
            }
        }
    }
}

function checkAdvancePaySumsError(element, compare_value, match_value = 0) {
    let error = false;
    if (compare_value < match_value) {
        error = true;
    }
    if (error) {
        element.addClassName('error');
    } else {
        element.removeClassName('error');
    }
    return error;
}

/*
 * Function to calculate the distribution of the prepaid sums
 */
function calculatePrepaidSumDistribution(element) {
    const calc_elements = ['overdue_principal', 'interest', 'insurance', 'tax1', 'tax2', 'lpg', 'penalty'];
    const pow = Math.pow(10, env.precision.gt2_rows);
    let advance_pay_amount = Math.round(parseFloat($('advance_pay_sum').value) * pow) / pow;
    let total_dues = 0;
    for (i=0; i<calc_elements.length; i++) {
        visible_field = $('repayment_plan_owed_' + calc_elements[i] + '_visible');
        hidden_field = $('repayment_plan_owed_' + calc_elements[i]);
        complete_field = $('advance_pay_' + calc_elements[i]);
        let current_element_pay_sum = Math.round(parseFloat(complete_field.value) * pow) / pow;

        hidden_field_value = Math.round(parseFloat(hidden_field.value) * pow) / pow;
        checkAdvancePaySumsError(complete_field, hidden_field_value - current_element_pay_sum);
        advance_pay_amount = advance_pay_amount - current_element_pay_sum;
        total_dues += current_element_pay_sum;
    }
    total_field = $('advance_pay_total');
    total_field.value = total_dues.toFixed(2);
    total_hidden_field = $('repayment_plan_owed_total');
    total_hidden_field_value = Math.round(parseFloat(total_hidden_field.value) * pow) / pow;
    checkAdvancePaySumsError(total_field, total_hidden_field_value - total_dues);

    principal_field = $('advance_pay_principal');
    principal_field.value = advance_pay_amount.toFixed(2);
    checkAdvancePaySumsError(principal_field, advance_pay_amount, 0.01);
}

/**
 * Function to create payment plan version
 */
function credilinkCreatePaymentPlanVersion(element) {
    // check the values
    let advance_pay_sum = parseFloat($('advance_pay_sum').value);
    let advance_pay_date = $('advance_pay_date').value;
    const calc_elements = ['overdue_principal', 'interest', 'insurance', 'tax1', 'tax2', 'lpg', 'penalty', 'total', 'principal'];
    let error_messages = [];

    if (!advance_pay_date || isNaN(advance_pay_sum)) {
        error_messages.push(i18n['messages']['error_complete_required_fields']);
    }

    for (i=0; i<calc_elements.length; i++) {
        complete_field = $('advance_pay_' + calc_elements[i]);
        if (complete_field.hasClassName('error')) {
            if (calc_elements[i] == 'principal') {
                error_messages.push(i18n['messages']['error_complete_sums_principal_not_correct']);
            } else if (calc_elements[i] == 'total') {
                error_messages.push(i18n['messages']['error_complete_sums_more_than_owed']);
            } else {
                error_messages.push(i18n['messages']['error_complete_sums_not_correct']);
            }
            break;
        }
    }

    if (error_messages.length) {
        alert(error_messages.join('\n'));
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'ajax_create_payment_plan_version');
    url.searchParams.set('report_type', $('report_type').value);

    fetch(url, { method: 'POST', body: new FormData(element.form)})
        .then(response=> response.json())
        .then(data => {
            alert(data.messages.join('\n'));
            if (data.result) {
                lb.deactivate();
            }
            Effect.Fade('loading');
        })
        .catch(error => {
            alert('Error: location "' + t.statusText + '" was not found.');
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}


/**
 * Function to open the list with versions
 */
function credilinkUseЕxistingPaymentPlanVersion() {
    Effect.Center('loading');
    Effect.Appear('loading');

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'ajax_get_payment_plan_versions');
    url.searchParams.set('report_type', $('report_type').value);
    url.searchParams.set('payment_plan', $('repayment_plan_id').value);

    fetch(url)
        .then(response=> response.json())
        .then(data => {
            lb = new lightbox({
                content: data['content'],
                title:   data['title'],
                width:   '450px'});
            lb.activate();
        })
        .catch(error => {
            alert('Error: location "' + t.statusText + '" was not found.');
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}

/**
 * Function to apply the selected repayment plan version to the current repayment plan
 */
function applyRepaymentPlanVersion(repayment_plan, version_pp) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'ajax_apply_repayment_plan_version');
    url.searchParams.set('report_type', $('report_type').value);
    url.searchParams.set('payment_plan', repayment_plan);
    url.searchParams.set('version', version_pp);

    fetch(url)
        .then(response=> response.json())
        .then(data => {
            alert(data.messages.join('\n'));
            if (data.result) {
                reloadReportPanels();
                lb.deactivate();
                lb.deactivate();
            }
        })
        .catch(error => {
            alert('Error: location "' + t.statusText + '" was not found.');
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}

function reloadReportPanels() {
    // prepare the ajax url
    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'ajax_reload_report_panels');
    url.searchParams.set('report_type', $('report_type').value);
    url.searchParams.set('customer', $('client').value);

    fetch(url)
        .then(response=> response.json())
        .then(data => {
            $('repayment_plans_container').innerHTML = data.template_repayment_plans;
            $('credits_container').innerHTML = data.template_credits_list;
            $('taxes_container').innerHTML = data.template_taxes_list;
            $('customers_contracts_container').innerHTML = data.template_all_credits_list;
        })
        .catch(error => {
            alert('Error: location "' + t.statusText + '" was not found.');
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}
