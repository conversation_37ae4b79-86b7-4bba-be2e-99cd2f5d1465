<?php
    Class Aon_Create_Fee Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            $contract_types = explode(',', INCLUDED_CONTRACT_TYPES);

            //round all subtotal amounts according to 'gt2_rows' precision setting
            $precision = $registry['config']->getParam('precision', 'gt2_rows');
            $skip_contracts = array();

            // IF THE REPORT IS CALLED FROM THE REPORTS MODULE THE CONTRACT_ID FILTER SHOULD BE EMPTY
            if (!empty($filters['contract_id'])) {
                // Get data for the contract
                $sql_contract_rows['select'] = 'SELECT con.id as model_id, gt2.id as gt2_id, gt2.article_alternative_deliverer as insured, CONCAT(ci18n_insured_name.name, " ", ci18n_insured_name.lastname) as insured_name, ' . "\n" .
                                               '  gt2.article_id as policy_id, gt2_i18n.article_name as policy_num, gt2.free_field2 as contract_row, gt2.free_field3 as insurance_type, nomi18n.name as insurance_type_name, ' . "\n" .
                                               '  DATE_FORMAT(gt2_i18n.free_text1, "%Y-%m-%d") as date_validity, DATE_FORMAT(gt2_i18n.free_text4, "%Y-%m-%d") as date_start, 1 as checked, ' . "\n" .
                                               '  gt2.article_deliverer as manager, CONCAT(ci18n_manager_name.name, " ", ci18n_manager_name.lastname) as manager_name' . "\n";

                $sql_contract_rows['from']   = 'FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                                               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                               '  ON (gt2.model="Contract" AND con.id=gt2.model_id AND con.id="' . $filters['contract_id'] . '")' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2_i18n' . "\n" .
                                               '  ON (gt2_i18n.parent_id=gt2.id AND gt2_i18n.lang="' . $model_lang . '")' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_insured_name' . "\n" .
                                               '  ON (ci18n_insured_name.parent_id=gt2.article_alternative_deliverer AND ci18n_insured_name.lang="' . $model_lang . '")' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                               '  ON (gt2.free_field3=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_manager_name' . "\n" .
                                               '  ON (ci18n_manager_name.parent_id=gt2.article_deliverer AND ci18n_manager_name.lang="' . $model_lang . '")' . "\n";

                // construct where
                $where = array();
                $where[] = 'con.id="' . $filters['contract_id'] . '"';
                $where[] = 'con.annulled_by=0';
                $where[] = 'con.deleted_by=0';
                $where[] = 'con.active=1';
                $where[] = 'con.type="' . CONTRACT_FEE_ID . '"';

                $sql_contract_rows['where'] = 'WHERE ' . implode(' AND ', $where);

                $query_data = implode("\n", $sql_contract_rows);
                $final_results = $registry['db']->GetAll($query_data);

                foreach ($final_results as $fr_key => $fr) {
                    $final_results[$fr_key]['encoded_data'] = base64_encode(json_encode($final_results[$fr_key]));
                    if (!empty($fr['contract_row'])) {
                        $skip_contracts[] = $fr['contract_row'];
                    }
                }
            }


            //sql to take the ids of the needed additional vars
            $sql_for_contract_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Contract" AND (fm.name LIKE "' . CONTRACTS_INSURANCE_TYPE_PREFIX . '%" OR fm.name="' . CONTRACTS_FEE_VAR . '" OR fm.name="' . CONTRACT_MAIN_OFFICE . '") AND fm.model_type IN (' . implode(',', $contract_types) . ')';
            $contracts_add_vars = $registry['db']->GetAll($sql_for_contract_add_vars);

            $insurerance_type_var_ids = array();
            $insurerance_fee_var_ids = array();
            $insurerance_contract_main_office = array();

            foreach ($contracts_add_vars as $contr_var) {
                if (preg_match('#^' . CONTRACTS_INSURANCE_TYPE_PREFIX . '\d+$#', $contr_var['name'])) {
                    $insurerance_type_var_ids[] = $contr_var['id'];
                } elseif ($contr_var['name'] == CONTRACTS_FEE_VAR) {
                    $insurerance_fee_var_ids[] =  $contr_var['id'];
                } elseif ($contr_var['name'] == CONTRACT_MAIN_OFFICE) {
                    $insurerance_contract_main_office[] =  $contr_var['id'];
                }
            }

            // Get the gt2 rows for the final results
            $sql_data['select'] = 'SELECT con.id as model_id, 0 as gt2_id, con.customer as insured, CONCAT(ci18n_insured_name.name, " ", ci18n_insured_name.lastname) as insured_name, ' . "\n" .
                                  '  con.id as policy_id, con.custom_num as policy_num, con.id as contract_row, con_cstm_insurance.value as insurance_type, nomi18n.name as insurance_type_name, ' . "\n" .
                                  '  DATE_FORMAT(con.date_validity, "%Y-%m-%d") as date_validity, DATE_FORMAT(con.date_start, "%Y-%m-%d") as date_start, 0 as checked, ' . "\n" .
                                  '  con.self_administrative as manager, CONCAT(ci18n_manager_name.name, " ", ci18n_manager_name.lastname) as manager_name' . "\n";

            $sql_data['from']   = 'FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_insured_name' . "\n" .
                                  '  ON (ci18n_insured_name.parent_id=con.customer AND ci18n_insured_name.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_cstm_insurance' . "\n" .
                                  '  ON (con_cstm_insurance.model_id=con.id AND con_cstm_insurance.var_id IN (' . implode(',', $insurerance_type_var_ids) . '))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                  '  ON (con_cstm_insurance.value=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_manager_name' . "\n" .
                                  '  ON (ci18n_manager_name.parent_id=con.self_administrative AND ci18n_manager_name.lang="' . $model_lang . '")' . "\n" .
                                  'INNER JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_cstm_fee_value' . "\n" .
                                  '  ON (con_cstm_fee_value.model_id=con.id AND con_cstm_fee_value.var_id IN (' . implode(',', $insurerance_fee_var_ids) . ') AND con_cstm_fee_value.value="' . CONTRACTS_FEE_VALUE . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS con_cstm_main_office' . "\n" .
                                  '  ON (con_cstm_main_office.model_id=con.id AND con_cstm_main_office.var_id IN (' . implode(',', $insurerance_contract_main_office) . '))' . "\n";

            // construct where
            $where = array();
            if (!empty($skip_contracts)) {
                $where[] = 'con.id NOT IN (' . implode(',', $skip_contracts) . ')';
            }
            $where[] = 'con.annulled_by=0';
            $where[] = 'con.deleted_by=0';
            $where[] = 'con.active=1';
            $where[] = 'con.type IN (' . implode(',', $contract_types) . ')';
            if (!empty($filters['client'])) {
                $where[] = 'con.customer="' . $filters['client'] . '"';
            }
            if (!empty($filters['policy_num'])) {
                $where[] = 'con.custom_num LIKE "%' . $filters['policy_num'] . '%"';
            }
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(con.date_sign, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(con.date_sign, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }
            if (!empty($filters['main_office'])) {
                $where[] = '(con.type="' . CONTRACT_TYPE_WITH_OFFICE_ID . '" AND con_cstm_main_office.value="' . $filters['main_office'] . '")';
            }

            $sql_data['where'] = 'WHERE ' . implode(' AND ', $where);

            $query_data = implode("\n", $sql_data);
            $contracts_rows = $registry['db']->GetAll($query_data);

            foreach ($contracts_rows as $cr_key => $cr) {
                $contracts_rows[$cr_key]['encoded_data'] = base64_encode(json_encode($contracts_rows[$cr_key]));
                $final_results[] = $contracts_rows[$cr_key];
            }

            unset($contracts_rows);

            $final_results['additional_options']['contract_id'] = (!empty($filters['contract_id']) ? $filters['contract_id'] : 0);

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>