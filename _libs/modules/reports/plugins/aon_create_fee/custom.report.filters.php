<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CLIENT FILTER
            $filter = array (
                'custom_id'            => 'client',
                'name'                 => 'client',
                'type'                 => 'autocompleter',
                'autocomplete_type'    => 'customers',
                'autocomplete'          => array(
                    'type'          => 'customers',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions'   => '<name> <lastname>',
                    'fill_options'  => array(
                        '$client_autocomplete  => <name> <lastname>',
                        '$client_oldvalue      => <name> <lastname>',
                        '$client               => <id>'
                    ),
                    'filters'       => array(
                        '<type>' => strval(CUSTOMER_CLIENT_TYPE)
                    ),
                    'clear'         => 1,
                    'buttons_hide'  => 'search'
                ),
                'autocomplete_buttons' => 'clear',
                'label'                => $this->i18n('reports_client'),
                'help'                 => $this->i18n('reports_client'),
            );
            $filters['client'] = $filter;

            //DEFINE MAIN OFFICE FILTER
            //prepare options
            $sql_office_options = 'SELECT c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label' . "\n" .
                                  'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                  ' ON (c.id=ci18n.parent_id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                                  'WHERE c.active=1 AND c.deleted_by=0 AND c.subtype="normal" AND c.type="' . CUSTOMER_OFFICE_TYPE . '"' . "\n" .
                                  'ORDER BY CONCAT(ci18n.name, " ", ci18n.lastname) ASC' . "\n";
            $office_options = $registry['db']->getAll($sql_office_options);

            //prepare filters
            $filter = array (
                'custom_id' => 'main_office',
                'name'      => 'main_office',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_main_office'),
                'help'      => $this->i18n('reports_main_office'),
                'options'   => $office_options,
            );
            $filters['main_office'] = $filter;

            //DEFINE POLICY NUM IS FILTER
            $filter = array (
                'custom_id'         => 'policy_num',
                'name'              => 'policy_num',
                'type'              => 'text',
                'label'             => $this->i18n('reports_policy_num'),
                'help'              => $this->i18n('reports_policy_num'),
                'value'             => ''
            );
            $filters['policy_num'] = $filter;

            //DEFINE DATE FROM FILTER
            $filter = array (
                'custom_id'         => 'from_date',
                'name'              => 'from_date',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/date_from_to_filter.html',
                'type'              => 'custom_filter',
                'width'             => 65,
                'additional_filter' => 'to_date',
                'first_filter_label'=> $this->i18n('reports_from_date'),
                'label'             => $this->i18n('reports_date_filter'),
                'help'              => $this->i18n('reports_date_filter')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'       => 'to_date',
                'name'            => 'to_date',
                'type'            => 'date',
                'width'           => 65,
                'label'           => $this->i18n('reports_to_date'),
                'help'            => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            // DEFINE CONTRACT ID HIDDEN FILTER
            $filter = array (
                'custom_id' => 'contract_id',
                'name'      => 'contract_id',
                'type'      => 'hidden',
                'hidden'    => 1
            );
            $filters['contract_id'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            if ($registry['request']->get('skip_session_filters')) {
                // set show type to 'check_bso' which has to be the default setting
                $registry->set('generated_report', 0, true);
                $filters['contract_id']['value'] = '';
            } elseif (empty($filters['contract_id']['value']) && $registry['request']->get('contract_id')) {
                $filters['contract_id']['value'] = $registry['request']->get('contract_id');
            }

           $unset_filters = array();
            foreach ($filters as $name => $filter) {
                if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                    $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                    $unset_filters[] = $filter['additional_filter'];
                }
            }

            foreach ($unset_filters as $unset_fltr) {
                unset($filters[$unset_fltr]);
            } 

            return $filters;
        }
    }
?>