reports_fir_types = Report by documents
reports_fir_types_invoices = Invoices
reports_fir_types_reasons = Revenue reasons
reports_company = Company
reports_office = Office
reports_employee = Employee
reports_department = Department
reports_report_by = Report by:
reports_article = Article
reports_article_category = Article category
reports_customer = Contractor
reports_trademark = Contractor group
reports_tag = Tag
reports_currency = Currency

reports_th_customer = Contractor
reports_th_article_name = Article
reports_th_gt2_article_name = Previous names
reports_th_subtotal_with_discount = Total without VAT
reports_th_subtotal_with_vat_with_discount = Total with VAT
reports_th_total = TOTAL:

error_reports_required_filters = Please, fill the required filters!
error_reports_invalid_period = Start of period should be before its end!
error_reports_equal_periods = Please, specify periods with equal length!
