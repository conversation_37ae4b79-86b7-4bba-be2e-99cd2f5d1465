{if !$reports_additional_options.failed}
  <table border="0" cellpadding="0" cellspacing="0">
    <tr>
      <td>
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="table-layout: fixed; width: 975px; word-break: break-all; word-wrap: break-word;">
          <tr class="reports_title_row" style="word-break: normal; word-wrap: normal;">
            <td width="25"  class="hcenter vmiddle t_border"><div style="width:  25px;">{#num#|escape}</div></td>
            <td width="150" class="hcenter vmiddle t_border"><div style="width: 150px;">{#reports_th_object#|escape}</div></td>
            <td width="100" class="hcenter vmiddle t_border"><div style="width: 100px;">{#reports_th_role_impuls#|escape}</div></td>
            <td width="130" class="hcenter vmiddle t_border"><div style="width: 130px;">{#reports_th_period#|escape}</div></td>
            <td width="150" class="hcenter vmiddle t_border"><div style="width: 150px;">{#reports_th_contractor#|escape}</div></td>
            <td width="100" class="hcenter vmiddle t_border"><div style="width: 100px;">{#reports_th_contract_num#|escape}</div></td>
            <td width="80"  class="hcenter vmiddle t_border"><div style="width:  80px;">{#reports_th_contract_date#|escape}</div></td>
            <td width="100" class="hcenter vmiddle t_border"><div style="width: 100px;">{#reports_th_company_won#|escape}</div></td>
            <td width="80"  class="hcenter vmiddle t_border"><div style="width:  80px;">{#reports_th_contract_value#|escape}</div></td>
            <td width="100" class="hcenter vmiddle t_border"><div style="width: 100px;">{#reports_th_what_is_executing#|escape}</div></td>
            <td width="50"  class="hcenter vmiddle t_border"><div style="width:  50px;">{#reports_th_type_kv#|escape}</div></td>
            <td width="50"  class="hcenter vmiddle t_border"><div style="width:  50px;">{#reports_th_layout_length#|escape}</div></td>
            <td width="50"  class="hcenter vmiddle t_border"><div style="width:  50px;">{#reports_th_number_guyed#|escape}</div></td>
            <td width="70"  class="hcenter vmiddle t_border"><div style="width:  70px;">{#reports_th_conductor#|escape}</div></td>
            <td width="50"  class="hcenter vmiddle t_border"><div style="width:  50px;">{#reports_th_opgw#|escape}</div></td>
            <td width="50"  class="hcenter vmiddle t_border"><div style="width:  50px;">{#reports_th_mzv#|escape}</div></td>
            <td width="70"  class="hcenter vmiddle t_border"><div style="width:  70px;">{#reports_th_term_by_contract#|escape}</div></td>
            <td width="70"  class="hcenter vmiddle t_border"><div style="width:  70px;">{#reports_th_term_real#|escape}</div></td>
            <td width="70"  class="hcenter vmiddle"         ><div style="width:  70px;">{#reports_th_report_num#|escape}</div></td>
          </tr>
          {counter start=$pagination.start name='item_counter' print=false}
          {foreach from=$reports_results key='report_id' item='record'}
            {capture assign="current_row_class"}{cycle values='t_odd,t_even'}{/capture}
            <tr class="{$current_row_class}">
              <td class="t_v_border vmiddle hright t_border" nowrap="nowrap" width="25">
                {counter name='item_counter' print=true}
              </td>
              <td class="t_v_border vmiddle hleft t_border">
                {if $record.project_id}
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$record.project_id}" target="_blank">[{$record.project_code|escape}] {$record.project_name|escape}</a>
                {else}
                  &nbsp;
                {/if}
              </td>
              <td class="t_v_border vmiddle hleft t_border">{$record.role_impuls|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">
                {if !empty($record.contract_date) && $record.contract_date eq $record.contract_date|date_format:#date_iso_short# || !empty($record.date_finish_contract)}
                  {if !empty($record.contract_date) && $record.contract_date eq $record.contract_date|date_format:#date_iso_short#}
                    {$record.contract_date|date_format:#date_short#|escape}
                  {/if}
                  /
                  {$record.date_finish_contract|date_format:#date_short#|escape}
                {else}
                  &nbsp;
                {/if}
              </td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_vazlojitel_name|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.contract_num|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">
                {if !empty($record.contract_date) && $record.contract_date eq $record.contract_date|date_format:#date_iso_short#}
                  {$record.contract_date|date_format:#date_short#|escape|default:"&nbsp;"}
                {else}
                  &nbsp;
                {/if}
              </td>
              <td class="t_v_border vmiddle hleft t_border">{$record.contract_customer_name|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.contract_value|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_kakvo_izpalnqva|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.type_kv|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_danni_length|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_number_guyed|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_conductor|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_opgw|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_mzv|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.uv_srok_izpalnenie|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft t_border">{$record.contract_days|escape|default:"&nbsp;"}</td>
              <td class="t_v_border vmiddle hleft">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$report_id}" target="_blank">{$record.report_num|escape|default:"&nbsp;"}</a>
              </td>
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="19">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr class="reports_title_row">
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border vmiddle hleft t_border">{#reports_th_all#|escape}</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border vmiddle hleft t_border">{$reports_additional_options.totals.contract_value|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border vmiddle hleft t_border">{$reports_additional_options.totals.uv_danni_length|escape|default:"&nbsp;"}</td>
            <td class="t_v_border vmiddle hleft t_border">{$reports_additional_options.totals.uv_number_guyed|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border t_border">&nbsp;</td>
            <td class="t_v_border vmiddle hleft t_border">{$reports_additional_options.totals.uv_srok_izpalnenie|escape|default:"&nbsp;"}</td>
            <td class="t_v_border vmiddle hleft t_border">{$reports_additional_options.totals.contract_days|escape|default:"&nbsp;"}</td>
            <td class="t_v_border">&nbsp;</td>
          </tr>
          <tr>
            <td class="t_footer" colspan="19"></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td class="pagemenu" bgcolor="#FFFFFF" colspan="9">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
  </table>
{else}
  <span class="error">{$reports_additional_options.error}</span>
{/if}