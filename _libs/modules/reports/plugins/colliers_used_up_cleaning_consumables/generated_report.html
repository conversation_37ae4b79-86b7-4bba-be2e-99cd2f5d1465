{if isset($reports_results.detailed)}
<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_list t_table">
        <tr class="hcenter reports_title_row">
          <td class="t_border" nowrap="nowrap" style="width: 120px">{#reports_doc_num#|escape}</td>
          <td class="t_border" nowrap="nowrap" style="width: 100px">{#reports_location#|escape}</td>
          <td class="t_border" nowrap="nowrap" style="width: 120px">{#reports_consumable#|escape}</td>
          <td class="t_border" nowrap="nowrap" style="width: 80px">{#reports_quantity#|escape}</td>
          <td class="t_border" nowrap="nowrap" style="width: 80px">{#reports_price#|escape}</td>
          <td class="t_border" nowrap="nowrap" style="width: 80px">{#reports_subtotal#|escape}</td>
          <td class="" nowrap="nowrap" style="width: 80px">{#reports_total#|escape}</td>
        </tr>
        {foreach from=$reports_results.detailed item='doc' name='di' key='dk'}
        {foreach from=$doc.locations item='loc' name='li' key='lk'}
        {foreach from=$loc.consumables item='cons' name='ci' key='ck'}
        <tr class="{if !($smarty.foreach.di.last and $smarty.foreach.li.last and $smarty.foreach.ci.last) or $report_filters.direction.value}t_bottom_border{/if}">
          {if $smarty.foreach.li.first and $smarty.foreach.ci.first}
          <td class="t_border" rowspan="{$doc.num_consumables}"{if $smarty.foreach.di.last and !$report_filters.direction.value} style="border-bottom: 0px none!important;"{/if}>
            <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$dk}">{$doc.full_num|numerate:$doc.direction}</a>
          </td>
          {/if}
          {if $smarty.foreach.ci.first}
          <td class="t_border" rowspan="{$loc.consumables|@count}"{if $smarty.foreach.di.last and $smarty.foreach.li.last and !$report_filters.direction.value} style="border-bottom: 0px none!important;"{/if}>{$loc.name|escape}</td>
          {/if}
          <td class="t_border">{$cons.article_name|escape}</td>
          <td class="t_border hright">{$cons.quantity|default:"0.00"} {$cons.article_measure_name|escape}</td>
          <td class="t_border hright">{$cons.price|default:"0.00"}</td>
          <td class="t_border hright">{$cons.subtotal|default:"0.00"}</td>
          {if $smarty.foreach.li.first and $smarty.foreach.ci.first}
          <td class="hright" rowspan="{$doc.num_consumables}"{if $smarty.foreach.di.last and !$report_filters.direction.value} style="border-bottom: 0px none!important;"{/if}>{$doc.total|default:"0.00"}</td>
          {/if}
        </tr>
        {/foreach}
        {/foreach}
        {foreachelse}
        <tr{if $report_filters.direction.value} class="t_bottom_border"{/if}>
          <td colspan="7" class="error">{#no_items_found#|escape}</td>
        </tr>
        {/foreach}
        {if $report_filters.direction.value}
        <tr>
          <td colspan="6" class="t_border">&nbsp;</td>
          <td class="hright strong" style="background-color: #98BCFF;">{$reports_results.detailed_total|default:"0.00"}</td>
        </tr>
        {/if}
      </table>
    </td>
  </tr>
  {if $report_filters.direction.value}
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_list t_table">
        <tr class="reports_title_row">
          {capture assign='reports_for_period'}reports_for_period_{$report_filters.direction.value}{/capture}
          <td class="" nowrap="nowrap" colspan="3" style="width: 280px">{$smarty.config.$reports_for_period|escape}</td>
        </tr>
        {foreach from=$reports_results.summary item='s' name='si' key='sk'}
        <tr class="{if !$smarty.foreach.si.last}t_bottom_border{/if}">
          <td class="t_border" style="width: 120px">{$s.article_name}</td>
          <td class="t_border hright" style="width: 80px">{$s.quantity|default:"0.00"} {$s.article_measure_name|escape}</td>
          <td class="hright" style="width: 80px">{$s.subtotal|default:"0.00"}</td>
        </tr>
        {foreachelse}
        <tr class="">
          <td colspan="3" class="error">{#no_items_found#|escape}</td>
        </tr>
        {/foreach}
      </table>
    </td>
  </tr>
  {/if}
  <tr>
    <td>&nbsp;</td>
  </tr>
</table>
{/if}