reports_representative = Representative
reports_period = Period
reports_date_from = from
reports_date_to = to
reports_ray = Ray
reports_client = Client
reports_customer_type = Type
reports_city = City
reports_type_results = Results
reports_speciality = Speciality

reports_type_results_options_client = by client
reports_type_results_options_agent = by representative

reports_type_client = Client type
reports_pharmacies = Pharmacy
reports_doctors = Doctors
reports_dentists = Dentists
reports_activities = Activities
reports_potential_loyality = Potential/Loyalty

reports_lbl_date = Date
reports_lbl_representative = Representative
reports_lbl_visits = Visits
reports_lbl_visits_target = Visits target
reports_lbl_client = Client
reports_lbl_potential = P/L
reports_lbl_type = Type
reports_lbl_specialty = Specialty
reports_lbl_region = Region
reports_lbl_city = City
reports_lbl_address = Address
reports_lbl_phone = Phone
reports_lbl_responsible = Responsible
reports_lbl_ray = Ray
reports_lbl_comment = Comment
reports_lbl_goal_visit = Visit goal
reports_lbl_promotion = Promotion
reports_lbl_collaboration_with = Collaboration with
reports_lbl_physical = Visit type
reports_lbl_materials = Materials
reports_lbl_material = Material
reports_lbl_quantity = Quantity
reports_lbl_visit_at = Visited at
reports_lbl_visit_by = Visited by

reports_total_lbl = Total
reports_total = TOTAL

reports_specialty_dentist = Dental medicine
reports_specialty_pharmacy = Pharmacy

reports_report_by_agent = Report by agent
reports_report_by_client = Report by client
reports_summary_table = Summary table

error_reports_no_results_to_show = no results to be displayed
error_reports_complete_required_filters = Please, specify values for required filters!
