<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
      <table border="1" cellpadding="0" cellspacing="0" width="50%">
        {if $reports_additional_options.total_sum}
          <tr>
            <td colspan="3">{#reports_total_sum#|escape}:</td>
            <td align="right"><strong>{$reports_additional_options.total_sum}</strong></td>
          </tr>
        {/if}
        <tr>
          <td nowrap="nowrap">{#num#|escape}</td>
          <td nowrap="nowrap">{#reports_full_num#|escape}</td>
          <td nowrap="nowrap">{#reports_customer#|escape}</td>
          <td nowrap="nowrap">{#reports_sum#|escape}</td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr>
            <td nowrap="nowrap" width="25">
              {counter name='item_counter' print=true}
            </td>
            <td>
              {$result.full_num|numerate:$result.direction|default:"&nbsp;"}
            </td>
            <td>
              {$result.customer_name|default:"&nbsp;"}
            </td>
            <td align="right">
              {$result.offer_sum|default:"0.00"}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="4">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
      </table>
  </body>
</html>