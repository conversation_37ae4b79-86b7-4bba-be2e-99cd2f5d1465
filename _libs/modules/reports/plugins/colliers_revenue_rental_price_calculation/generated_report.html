<script type="text/javascript">
    function prepareURL(link) {ldelim}
        if (!$('attached_files').value) {ldelim}
          //do not allow preview without a pattern
          alert('{$smarty.config.reports_alert_no_pattern}');
          $('attached_files').focus();
          return false;
        {rdelim}
        link.href = link.href.replace(/\&pattern\=[^\&]*(\&|$)/, '$1');
        link.href = link.href.replace(/\&email_subject\=[^\&]*(\&|$)/, '$1');
        link.href += '&pattern=' + $('attached_files').value + '&email_subject=' + $('email_subject').value;
    {rdelim}
    function toggleReportResult(num, action) {ldelim}
        if (isSubelementClicked()) {ldelim}
            return;
        {rdelim}    
        if (action == 'expand') {ldelim}
            var row = $$('.extended_report_info');
            for (var i = 0; i < row.length; i++) {ldelim}
                row[i].style.display = '';
            {rdelim}
            var row = $$('.switch_expand');
            for (var i = 0; i < row.length; i++) {ldelim}
                row[i].className = 'switch_collapse';
            {rdelim}
            return true;
        {rdelim} else if (action == 'collapse') {ldelim}
            var row = $$('.extended_report_info');
            for (var i = 0; i < row.length; i++) {ldelim}
                row[i].style.display = 'none';
            {rdelim}
            var row = $$('.switch_collapse');
            for (var i = 0; i < row.length; i++) {ldelim}
                row[i].className = 'switch_expand';
            {rdelim}
            return true;
        {rdelim}
        var row = $('report_' + num);
        if (row.style.display == 'none') {ldelim}
            row.style.display = '';
            $('switch_report_' + num).className = 'switch_collapse';
        {rdelim} else {ldelim}
            row.style.display = 'none';
            $('switch_report_' + num).className = 'switch_expand';
        {rdelim}
    {rdelim}
</script>
<input type="hidden" name="skip_session_ids" value="1" />
<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td>
  <span class="pointer" onclick="toggleReportResult('', 'expand');">{#reports_expand_all#}</span> |
  <span class="pointer" onclick="toggleReportResult('', 'collapse');">{#reports_collapse_all#}</span>
  <br /><br />
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
    <tr>
      <td class="t_caption t_border t_checkall">
        <div style="width: 30px; text-align:center;">
        {include file="`$theme->templatesDir`_select_items.html"
          pages=$pagination.pages
          total=$pagination.total
          session_param=$session_param|default:$pagination.session_param
        }
        </div>
      </td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_contract_num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_trademark#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_trade_area#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_base_price#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_turovers_price#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_invoiced#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_difference#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_currency#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_financial#|escape}</div></td>
      <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_preview#|escape}</div></td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach from=$reports_results item=result key=kk}
    {cycle assign='row_bckg' name='first_cycle' values='t_odd,t_even'}
      <tr class="{$row_bckg} t_bottom_border{if !$result.turnover_confirmed} row_pink{/if}" onclick="toggleReportResult({$kk}, '');" style="cursor: pointer;"{if !$result.turnover_confirmed} {help label=not_all_incomes popup_only=1}{/if}>
        <td class="t_border">
          <div id="switch_report_{$kk}" class="switch_expand"></div>
          {if !$result.turnover_confirmed}
            &nbsp;{help label=not_all_incomes}
          {else}
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: 'reports',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$result.idx}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($result.idx, $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($result.idx, $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          {/if}
        </td>
        <td class="t_border hright" nowrap="nowrap" width="25">
          {counter name='item_counter' print=true}
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$result.contract_id}" target="_blank">
            {$result.contract_num|default:"&nbsp;"}
          </a>
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.customer}" target="_blank">
            {$result.customer_name|default:"&nbsp;"}
          </a>
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=trademarks&amp;trademarks={$result.customer}" target="_blank">
            {$result.trademark_name|default:"&nbsp;"}
          </a>
        </td>        
        <td class="t_border">
          {$result.object_name}
        </td>  
        <td class="t_border" style="text-align: right">
          {$result.base_price|default:0}
        </td>
        <td class="t_border" style="text-align: right">
          {$result.turnovers_price|default:0}
        </td>
        <td class="t_border" style="text-align: right">
          {$result.invoiced}
        </td>  
        <td class="t_border" style="text-align: right">
          {$result.difference}
        </td>        
        <td class="t_border">
          {$result.currency}
        </td>
        <td class="t_border">
        {foreach from=$result.financial item=contact}
          {$contact.name}<br/>
        {/foreach}
        </td>
        <td class="hcenter">
          {if !$result.turnover_confirmed}
            <img src="{$theme->imagesUrl}pdf.png" alt="{#reports_preview#|escape}" title="{#reports_preview#|escape}" border="0" class="dimmed" />
          {else}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports&amp;report_type={$report_type}&amp;reports=generate_preview&amp;generate_preview={$result.idx}"
             onmousedown="prepareURL(this);"
             target="_blank">
            <img src="{$theme->imagesUrl}pdf.png" alt="{#reports_preview#|escape}" title="{#reports_preview#|escape}" border="0" />
          </a>
          {/if}
        </td>
      </tr>
      <tr style="display: none;" class="{$row_bckg}1 extended_report_info" id="report_{$kk}">
        <td colspan="13" style="padding: 10px;">
          <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" align="left" width="50%" style="border: 1px solid #CCCCCC;">
            <tr>
              <td class="t_caption t_border" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_date_from#|escape}</div></td>
              <td class="t_caption t_border" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_date_to#|escape}</div></td>
              <td class="t_caption t_border" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_percent#|escape}</div></td>
              <td class="t_caption t_border" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_base_price#|escape}</div></td>
              <td class="t_caption t_border" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_turovers_price#|escape}</div></td>
              <td class="t_caption t_border" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_invoiced#|escape}</div></td>
              <td class="t_caption t_border" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_difference#|escape}</div></td>
              <td class="t_caption" nowrap="nowrap" style="border-top: none;"><div class="t_caption_title">{#reports_currency#|escape}</div></td>
            </tr>
            {foreach from=$result.extended item=ext}
              <tr class="{cycle name='second_cycle' values='t_odd1,t_even1'}{if !$ext.turnover_confirmed} red{/if}">
                <td class="t_border" nowrap="nowrap">
                  {$ext.date_from|date_format:#date_short#}
                </td>
                <td class="t_border">
                  {$ext.date_to|date_format:#date_short#}
                </td>
                <td class="t_border">
                  {$ext.percent}
                </td>
                <td class="t_border" style="text-align: right">
                  {$ext.base_price|default:0}
                </td>
                <td class="t_border" style="text-align: right">
                  {$ext.turnovers_price|default:0}
                </td>
                <td class="t_border" style="text-align: right">
                  {$ext.invoiced}
                </td>  
                <td class="t_border" style="text-align: right">
                  {$ext.difference}
                </td>        
                <td>
                  {$result.currency}
                </td>
              </tr>
            {/foreach}
          </table>
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="14">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="14"></td>
    </tr>
  </table>
</td>
</tr>
</table>