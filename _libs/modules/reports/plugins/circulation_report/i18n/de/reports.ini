reports_from_date = Von
reports_to_date = Bis
reports_payment_type = Zahlungsart
reports_document_type = Typ des Vorgangs
reports_office_name = Objekt
reports_employees = Mitarbeiter
reports_customers = Kunde
reports_currency = Währung
reports_type_income = Typ der Einnahme
reports_type_expense = Typ der Ausgabe
reports_money_given = Verbindlichkeiten
reports_project = Projekt
reports_payment_type_cash_paying = bar
reports_payment_type_bank_paying = Bank
reports_document_type_income = Einnahme
reports_document_type_expense = Ausgabe
reports_money_given_yes = bezahlt
reports_money_given_no = nicht bezahlt
reports_legend = Legende:
reports_expense = Aufwand
reports_office = Objekt
reports_employee = Mitarbeiter
reports_document_num = №
reports_customer = Kunde
reports_type = Typ
reports_paying_reason = Grund
reports_type_paying = Zahlungsweise
reports_paying_value = Betrag
reports_paying_currency = Währung
reports_take_money = erhaltener Betrag
reports_money_date = Zah;ungsdatum
reports_incomings = Zugang (bar)
reports_bank_incomings = Zugang (Bank)
reports_expenses = Abgänge (bar)
reports_bank_expenses = Abgänge (Bank)

