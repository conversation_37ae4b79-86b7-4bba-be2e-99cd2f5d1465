<?php

class Grifidhotels_Salaries extends Reports
{
    public static function buildQuery(&$registry, $reportFilters)
    {
        $total_results_count = 0;
        $year = $reportFilters['year'] ?? date('Y');
        $dayFirst = "{$year}-{$reportFilters['month']}-01";
        $settings = Reports::getReportSettings($registry);
        $db = $registry['db'];
        $documentSql = "SELECT d.id, docVars1.`value` AS unit, ni.`name` AS company, ci18n.`name` AS hotel
                        FROM documents d
                        JOIN customers_i18n AS ci18n ON (ci18n.parent_id = d.customer)
                        JOIN documents_cstm AS docVars ON (docVars.model_id = d.id)
                        JOIN _fields_meta AS fm_docs ON (fm_docs.id = docVars.var_id)
                        JOIN documents_cstm AS docVars1 ON (docVars1.model_id = d.id)
                        JOIN _fields_meta AS fm_docs1 ON (fm_docs1.id = docVars1.var_id)
                        JOIN documents_cstm AS docVars2 ON (docVars2.model_id = d.id)
                        JOIN _fields_meta AS fm_docs2 ON (fm_docs2.id = docVars2.var_id)
                        JOIN documents_cstm AS docVars3 ON (docVars3.model_id = d.id)
                        JOIN _fields_meta AS fm_docs3 ON (fm_docs3.id = docVars3.var_id)
                        LEFT JOIN customers_trademarks AS ct ON (ct.parent_id = d.customer)
                        LEFT JOIN nom_i18n AS ni ON (ni.parent_id = ct.trademark_id)
                        WHERE fm_docs.name = 'for_month'
                        AND fm_docs1.name = 'for_link'
                        AND fm_docs2.name = 'for_year'
                        AND fm_docs3.name = 'for_link_id'
                        AND docVars.`value` = '{$reportFilters['month']}'
                        AND docVars2.`value` = '{$reportFilters['year']}'
                        AND docVars3.`value` = '{$reportFilters['unit']}'
                        AND d.customer = {$reportFilters['hotel']}";

        $documentVars = $db->getAll($documentSql)[0];
        if (!in_array("all", $reportFilters['employee'])) {
            $ids = implode(",", $reportFilters['employee']);
        } else {
            $gt2GroupedSql = "SELECT GROUP_CONCAT(DISTINCT gd.article_deliverer ORDER BY gd.article_deliverer ASC SEPARATOR ', ') AS ids
                        FROM gt2_details gd
                        WHERE gd.model_id = '{$documentVars['id']}'";
            $gt2GroupedRes = $db->getAll($gt2GroupedSql)[0];
            $ids = $gt2GroupedRes['ids'];
        }

        if (empty($ids)) {
            return [[], 0];
        }

        $contractQuerySelect = "SELECT
        c.customer,
        c.customer AS employee_id,
        CONCAT(ci.`name`, ' ', ci.`lastname`) AS employee,
        cc2.`value` AS hotel,
        ni.`name` AS company,
        ROUND(cc1.`value`,2) AS con_hourly,
        cc3.`value` AS unit,
        0 AS workDays,
        0 AS hourly,
        0 AS hours,
        0 AS salary,
        cc4.`value` AS netWage,
        cc6.`value` AS job
        FROM contracts AS c
        JOIN customers_i18n AS ci ON c.customer = ci.parent_id
        LEFT JOIN _fields_meta fm0 ON fm0.`model` = 'Contract' AND fm0.`model_type` = {$settings['contractType']} AND fm0.`name` = 'hotel_id'
        LEFT JOIN contracts_cstm cc0 ON cc0.model_id = c.id AND cc0.var_id = fm0.id AND cc0.lang = ''
        LEFT JOIN _fields_meta fm1 ON fm1.`model` = 'Contract' AND fm1.`model_type` = {$settings['contractType']} AND fm1.`name` = 'hourly_rate'
        LEFT JOIN contracts_cstm cc1 ON cc1.model_id = c.id AND cc1.var_id = fm1.id AND cc1.lang = ''
        LEFT JOIN _fields_meta fm2 ON fm2.`model` = 'Contract' AND fm2.`model_type` = {$settings['contractType']} AND fm2.`name` = 'hotel_name'
        LEFT JOIN contracts_cstm cc2 ON cc2.model_id = c.id AND cc2.var_id = fm2.id AND cc2.lang = ''
        LEFT JOIN _fields_meta fm3 ON fm3.`model` = 'Contract' AND fm3.`model_type` = {$settings['contractType']} AND fm3.`name` = 'position_department'
        LEFT JOIN contracts_cstm cc3 ON cc3.model_id = c.id AND cc3.var_id = fm3.id AND cc3.lang = ''
        LEFT JOIN _fields_meta fm4 ON fm4.`model` = 'Contract' AND fm4.`model_type` = {$settings['contractType']} AND fm4.`name` = 'net_wage'
        LEFT JOIN contracts_cstm cc4 ON cc4.model_id = c.id AND cc4.var_id = fm4.id AND cc4.lang = ''
        LEFT JOIN _fields_meta fm5 ON fm5.`model` = 'Contract' AND fm5.`model_type` = {$settings['contractType']} AND fm5.`name` = 'position_department_id'
        LEFT JOIN contracts_cstm cc5 ON cc5.model_id = c.id AND cc5.var_id = fm5.id AND cc5.lang = ''
        LEFT JOIN _fields_meta fm6 ON fm6.`model` = 'Contract' AND fm6.`model_type` = {$settings['contractType']} AND fm6.`name` = 'position_name'
        LEFT JOIN contracts_cstm cc6 ON cc6.model_id = c.id AND cc6.var_id = fm6.id AND cc6.lang = ''
        LEFT JOIN customers_trademarks AS ct ON (ct.parent_id = cc0.`value`)
        LEFT JOIN nom_i18n AS ni ON (ni.parent_id = ct.trademark_id)";

        $contractQueryWhere = [
            "c.active = 1",
            "c.type = {$settings['contractType']}",
            "c.deleted_by = 0",
            "c.subtype = 'contract'",
            "(c.date_validity IS NULL OR c.date_validity > '{$dayFirst}')",
        ];

        $contractQueryWhere[] = "c.customer IN ({$ids})";
        $conQuery = $contractQuerySelect . "WHERE " . implode(' AND ', $contractQueryWhere) . "GROUP BY c.id, c.customer, cc1.`value`";
        $contracts = $db->getAssoc($conQuery);
        if (empty($contracts)) {
            return [
                $contracts, 0
            ];
        }
        $model = new Calendars_Month($year, $reportFilters['month']);
        $nonWorkingDays = $model->getNonWorkingDays($registry);
        $lastOfMonth = date('t', strtotime($dayFirst));
        $lastDay = "$year-{$reportFilters['month']}-$lastOfMonth";
        $workDays = $lastOfMonth - sizeof($nonWorkingDays);
        $workHours = $workDays * 8;
        foreach ($contracts as $contract) {
            if (empty((int)$contract['con_hourly'])) {
                $contracts[$contract['employee_id']]['hourly'] = $contract['netWage'] / $workHours;
            } else {
                $contracts[$contract['employee_id']]['hourly'] = $contract['con_hourly'];
            }
            $contracts[$contract['employee_id']]['firstDay'] = $dayFirst;
            $contracts[$contract['employee_id']]['lastDay'] = $lastDay;
            $contracts[$contract['employee_id']]['netWage'] = (int)$contracts[$contract['employee_id']]['netWage'];
        }
        $newSelect = "SELECT
		TRIM(gt2di18n.article_deliverer_name) AS employee,
		'{$documentVars['company']}' AS company,
		TRIM(gt2di18n.article_name) AS job,
		gt2d0.article_id AS job_id,
		'{$documentVars['hotel']}' AS hotel,
		'{$documentVars['unit']}' AS unit,
		0 AS con_hourly,
		gt2d0.article_deliverer AS employee_id,
		COUNT(DISTINCT (CASE WHEN gt2d0.quantity > 0.1 AND gt2d0.article_volume = 0 THEN gt2d0.article_code END)) AS workDays,
		gt2d0.price AS hourly,
		ROUND(SUM(IF(gt2d0.quantity > 0.1 AND gt2d0.article_volume = 0, gt2d0.quantity, 0)),2) AS hours,
		ROUND(SUM(IF(gt2d0.quantity > 0.1 AND gt2d0.article_volume = 0, gt2d0.subtotal, 0)),2) AS salary,
		MIN(gt2d0.article_code) AS firstDay,
		MAX(gt2d0.article_code) AS lastDay,
		CAST(gt2d0.free_field3 AS DECIMAL(10,2)) AS netWage\n";

        $newFrom = "FROM gt2_details AS gt2d0
        JOIN gt2_details_i18n AS gt2di18n ON (gt2d0.id = gt2di18n.parent_id)\n";

        $conEmployees = implode(',', array_keys($contracts));
        $where = [
            "gt2d0.article_deliverer IN ($conEmployees)",
            "gt2d0.model_id = {$documentVars['id']}",
            "gt2d0.article_code != ''"
        ];

        if (!in_array("all", $reportFilters['employee'])) {
            $ids = implode(",", $reportFilters['employee']);
            $where[] = "gt2d0.article_deliverer IN ({$ids})";
        }

        if (isset($reportFilters['sort']) && isset($reportFilters['order'])) {
            $orderBy = "ORDER BY {$reportFilters['sort'][0]} {$reportFilters['order'][0]}";
        } else {
            $orderBy = "ORDER BY employee, netWage";
        }

        $query = $newSelect . $newFrom . "WHERE " . implode(" AND ", $where) . "\nGROUP BY employee, gt2d0.price,gt2d0.article_deliverer, netWage, job_id" . "\n{$orderBy}";
        $results = $db->GetAll($query);
        $allEmployees = array_column($results, 'employee_id');
        $total_results_count = intval($registry['db']->GetOne('SELECT FOUND_ROWS()'));
        if (isset($reportFilters['limit'])) {
            $query .= "\nLIMIT {$reportFilters['limit']}";
        }

        $results = $db->GetAll($query);
        if (empty($results)) {
            $results = $contracts;
            $allEmployees = array_column($contracts, 'employee_id');
        }
        $currPageEmployees = array_column($results, "employee_id");
        $employeesStr = implode(',', $currPageEmployees);
        $dateStr = "{$year}-{$reportFilters['month']}";

        $bonusQuery = self::_getExtrasQuery($employeesStr, $dateStr, 2, $reportFilters['unit']);
        $bonuses = $db->GetAssoc($bonusQuery);
        $deductionQuery = self::_getExtrasQuery($employeesStr, $dateStr, 3, $reportFilters['unit']);
        $deductions = $db->GetAssoc($deductionQuery);

        foreach ($results as $idx => $result) {
            $result['bonus'] = floatval(($bonuses[$result['employee_id']] ?? 0));
            $result['deduction'] = floatval(($deductions[$result['employee_id']] ?? 0));
            $result['con_hourly'] = $contracts[$result['employee_id']]['con_hourly'];
            $results[$idx] = $result;
        }

        $leaveSql = <<<SQL
        SELECT d.id, d.customer AS employee, dc1.`value` AS startDate, dc2.`value` AS endDate, dc5.`value` AS leaveDays
        FROM documents d
        JOIN _fields_meta fm1 ON fm1.`model` = 'Document' AND fm1.`model_type` = 5 AND fm1.`name` = 'plr_leave_start_date'
        JOIN documents_cstm dc1 ON dc1.model_id = d.id AND dc1.var_id = fm1.id AND dc1.lang = ''
        JOIN _fields_meta fm2 ON fm2.`model` = 'Document' AND fm2.`model_type` = 5 AND fm2.`name` = 'plr_leave_finish_date'
        LEFT JOIN documents_cstm dc2 ON dc2.model_id = d.id AND dc2.var_id = fm2.id AND dc2.lang = ''
        JOIN _fields_meta fm3 ON fm3.`model` = 'Document' AND fm3.`model_type` = 5 AND fm3.`name` = 'plr_leave_type'
        JOIN documents_cstm dc3 ON dc3.model_id = d.id AND dc3.var_id = fm3.id AND dc3.lang = ''
        JOIN _fields_meta fm4 ON fm4.`model` = 'Document' AND fm4.`model_type` = 5 AND fm4.`name` = 'position_department_id'
        JOIN documents_cstm dc4 ON dc4.model_id = d.id AND dc4.var_id = fm4.id
        JOIN _fields_meta fm5 ON fm5.`model` = 'Document' AND fm5.`model_type` = 5 AND fm5.`name` = 'plr_leave_days'
        JOIN documents_cstm dc5 ON dc5.model_id = d.id AND dc5.var_id = fm5.id AND dc5.lang = ''
        WHERE (DATE_FORMAT(dc1.`value`, '%m') = {$reportFilters['month']} OR DATE_FORMAT(dc2.`value`, '%m') = {$reportFilters['month']})
        AND (DATE_FORMAT(dc1.`value`, '%Y') = {$year} OR DATE_FORMAT(dc2.`value`, '%Y') = {$year})
        AND d.active = 1
        AND d.deleted_by = 0
        AND d.`type` = 5
        AND d.substatus = 1
        AND d.`status` = 'closed'
        AND dc3.`value` = 'paid'
        AND dc4.`value` = '{$reportFilters['unit']}'
        AND d.`customer` IN ({$conEmployees})
        SQL;
        $daysOff = $db->getAll($leaveSql);
        $offEmployees = array_column($daysOff, 'employee');
        $counts = array_count_values($allEmployees);
        $diff = array_diff(array_keys($contracts), array_keys($counts));
        if (!empty($diff)) {
            foreach ($diff as $d) {
                if (in_array($d, $offEmployees)) {
                    $contracts[$d]['bonus'] = 0;
                    $contracts[$d]['deduction'] = 0;
                    $results[] = $contracts[$d];
                    $total_results_count++;
                }
            }
        }
        foreach ($results as $idx => $result) {
            $result['daysOffPay'] = 0;
            $result['leaveHours'] = 0;
            $result['hourly'] = floatval($result['hourly']);
            $result['hours'] = floatval($result['hours']);
            $emptyRate = empty($result['con_hourly']);

            foreach ($daysOff as $ind => $day) {
                if ($day['employee'] !== $result['employee_id']) {
                    continue;
                }
                $dateStart = new DateTime($day['startDate']);
                if ($dateStart->format('m') !== $reportFilters['month']) {
                    $dateStart = new DateTime("{$dateStr}-01");
                    $daysOff[$ind]['startDate'] = $dateStart->format('Y-m-d');
                }
                try {
                    $dateEnd = new DateTime($day['endDate']);
                    $leaveDays = $dateStart->diff($dateEnd)->days + 1;
                } catch (\Exception $e) {
                    $leaveDays = ((int)$day['leaveDays']) ?? 0;
                    $dateEnd = clone $dateStart;
                    $dateEnd->add(DateInterval::createFromDateString("{$leaveDays} Days"));

                }
                $hasOneRow = $counts[$result['employee_id']] === 1;
                if (!$hasOneRow) {
                    $firstCheck = (int)(strtotime($result['firstDay']) <= $dateStart->getTimestamp() && $dateStart->getTimestamp() <= strtotime($result['lastDay']));
                    $secondCheck = (int)((strtotime($result['firstDay']) <= $dateEnd->getTimestamp() && $dateEnd->getTimestamp() <= strtotime($result['lastDay'])));
                    $period = $firstCheck . $secondCheck;
                    if ($period === "00") {
                        continue;
                    } elseif ($period === "01" && $counts[$result['employee_id']] > 1) {
                        $dateStart->setTimestamp(strtotime($result['firstDay']));
                    }
                }
                $added = 0;
                for ($i = 0; $i < $leaveDays; $i++) {
                    if ($dateStart->format("m") !== $reportFilters['month'] || ($dateStart->getTimestamp() > strtotime($result['lastDay']) && !$hasOneRow)) {
                        break;
                    }
                    $addPay = (!$emptyRate && $dateStart->format("N") !== "7") || ($emptyRate && !in_array($dateStart->format("Y-m-d"), $nonWorkingDays));
                    if ($addPay) {
                        $result['daysOffPay'] += 8 * $result['hourly'];
                        $result['leaveHours'] += 8;
                    }
                    $dateStart->add(DateInterval::createFromDateString("1 Day"));
                    $added++;
                }
                $leaveDays -= $added;
                if ($leaveDays === 0) {
                    unset($daysOff[$ind]);
                    continue;
                }
                $daysOff[$ind]['leaveDays'] = $leaveDays;
                $daysOff[$ind]['startDate'] = $dateStart->format("Y-m-d");
            }
            $result['salary'] = floatval($result['salary']);
            $result['daysOffPay'] = round($result['daysOffPay'], 2);
            $results[$idx] = $result;
        }
        return [
            array_values($results), $total_results_count
        ];
    }

    private static function _getExtrasQuery($employeesStr, $dateStr, $type, $department)
    {
        return "SELECT d.customer AS customer, COALESCE(ROUND(SUM(dc1.`value`),2),0) AS value
        FROM documents AS d
        LEFT JOIN _fields_meta fm0 ON fm0.`model` = 'Document' AND fm0.`model_type` = {$type} AND fm0.`name` = 'position_department_id'
        LEFT JOIN documents_cstm dc0 ON dc0.model_id = d.id AND dc0.var_id = fm0.id AND dc0.lang = ''
        LEFT JOIN _fields_meta fm1 ON fm1.`model` = 'Document' AND fm1.`model_type` = {$type} AND fm1.`name` = 'total_sum'
        LEFT JOIN documents_cstm dc1 ON dc1.model_id = d.id AND dc1.var_id = fm1.id AND dc1.lang = ''
        WHERE DATE_FORMAT(d.`date`,'%Y-%m') = '{$dateStr}'
          AND d.deleted_by = 0
          AND d.active = 1
          AND d.`status` = 'closed'
          AND dc0.`value` = '{$department}'
          AND d.customer IN ({$employeesStr})
        GROUP BY d.customer";

    }
}
