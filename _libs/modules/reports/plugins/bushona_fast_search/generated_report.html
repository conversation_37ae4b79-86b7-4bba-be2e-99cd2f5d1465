<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="100"><div style="width: 110px">{#reports_order_num#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="120"><div style="width: 120px">{#reports_date_acceptance#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="180"><div style="width: 180px">{#reports_client#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="180"><div style="width: 180px">{#reports_device#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="120"><div style="width: 120px">{#reports_brand#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="120"><div style="width: 120px">{#reports_model#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="120"><div style="width: 120px">{#reports_serial_num#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="120"><div style="width: 120px">{#reports_serial_num_new#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;"                  width="250"><div style="width: 250px">{#reports_status#|escape}</div></td>
        </tr>
        {foreach from=$reports_results item=document}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_v_border t_border" style="text-align: left; max-width: 100px;">{if $document->get('id')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$document->get('id')}" target="_blank">{/if}{$document->get('full_num')|escape|default:"&nbsp;"}{if $document->get('id')}</a>{/if}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 120px;">{$document->getVarValue('date_acceptance')|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 180px;">{if $document->get('customer')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$document->get('customer')}" target="_blank">{/if}{$document->get('customer_name')|escape|default:"&nbsp;"}{if $document->get('customer')}</a>{/if}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 180px;">{$document->getVarValue('kind_tech_name')|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 120px;">{$document->getVarValue('tm_name')|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 120px;">{$document->getVarValue('product_name')|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 120px;">{$document->getVarValue('serial_num')|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 120px;">{$document->getVarValue('serial_num_new')|escape|default:"&nbsp;"}</td>
            <td class="t_v_border"          style="text-align: left; max-width: 250px;">
              {capture assign='lang_var_status'}documents_status_{$document->get('status')}{/capture}
              <img src="{$theme->imagesUrl}documents_{$document->get('status')}.png" border="0" alt="{$smarty.config.$lang_var_status|escape|default:"&nbsp;"}" title="{$smarty.config.$lang_var_status|escape|default:"&nbsp;"}" />{if $document->get('substatus')} {$document->get('substatus_name')|escape}{/if}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td class="error" colspan="9">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="9"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td class="pagemenu" bgcolor="#FFFFFF">
      {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        link=$link
        hide_selection_stats=true
      }
    </td>
  </tr>
</table>
