var reportUstilProductionSchedule = {

    markRequestAsChanged: function (element) {
      var order_id = element.name.replace(/^.+\[(\d+)\]$/, '$1');
      if (order_id) {
        $('request_' + order_id).checked = true;
        this.multiChangeDates(element);
      } else {
          alert('Error: Failed to mark record as changed!');
      }
    },

    saveProduced: function (element) {
      element.form.action = env.base_url + '?' + env.module_param + '=reports&reports=save_produced&report_type=' + $('report_type').value;
      return true;
    },

    saveSchedule: function (element) {
        element.form.action = env.base_url + '?' + env.module_param + '=reports&reports=save_schedule&report_type=' + $('report_type').value;
        return true;
    },

    worked: function () {
        $('report_show').value = 'worked';
        return true;
    },

    standard: function () {
        $('report_show').value = 'standard';
        return true;
    },

    multiChangeDates: function (e) {
        if (e.className.match(/datebox/)) {
            var current_checkbox = $('selected_request[' + e.id.replace(/^.+\[(\d+)\]$/, '$1') + ']');
            if (current_checkbox.checked) {
                var other_checkboxes = $$('input[type="checkbox"][id^="selected_request["]:checked:not([id^="' + current_checkbox.id + '"])');
                if (other_checkboxes.length) {
                    var date_field_name = e.id.replace(/^(.+)\[\d+\]$/, '$1');
                    var date = e.value;
                    var date_formatted = $(e.id + '_formatted').value;
                    for (var i = 0; i < other_checkboxes.length; i++) {
                        var order_id = other_checkboxes[i].id.replace(/^.+\[(\d+)\]$/, '$1');
                        $(date_field_name + '[' + order_id + ']').value = date;
                        $(date_field_name + '[' + order_id + ']_formatted').value = date_formatted;
                        $('request_' + order_id).checked = true;
                    }
                }
            }
        }
    }
};