<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry = array();

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            self::$registry = &$registry;

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE TYPE INVOICE FILTER
            //prepare options
            $sql_type_invoice_options = 'SELECT label, option_value FROM ' . DB_TABLE_FIELDS_OPTIONS . ' WHERE parent_name="' . DOCUMENT_INVOICE_TYPE . '" AND lang="' . $registry['lang'] . '" ORDER BY position ASC';
            $type_invoice_options = $registry['db']->getAll($sql_type_invoice_options);

            //prepare filters
            $filter = array (
                'custom_id'       => 'type_invoice',
                'name'            => 'type_invoice',
                'type'            => 'custom_filter',
                'custom_template' => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/type_insurance_filter.html',
                'required'        => 1,
                'label'           => $this->i18n('reports_type_invoice'),
                'help'            => $this->i18n('reports_type_invoice'),
                'options'         => $type_invoice_options
            );
            $filters['type_invoice'] = $filter;

            //DEFINE INSURER FILTER IS FILTER
            $filter = array (
                'custom_id'            => 'insurer',
                'name'                 => 'insurer',
                'required'             => 1,
                'custom_template'      => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/customer_branch_filter.html',
                'type'                 => 'custom_filter',
                'autocomplete_type'    => 'customers',
                'additional_filter'    => 'branch',
                'autocomplete'         => array(
                    'type'          => 'customers',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions'   => '<name> <lastname>',
                    'fill_options'  => array(
                        '$insurer_autocomplete  => <name> <lastname>',
                        '$insurer_oldvalue      => <name> <lastname>',
                        '$insurer               => <id>'
                    ),
                    'execute_after'        => 'completeCustomersBranches',
                    'filters'       => array(
                        '<type>' => strval(CUSTOMER_INSURER_TYPE)
                    ),
                    'buttons_hide'  => 'search clear'
                ),
                'hide_plus_minus_buttons'   => false,
                'width'                     => 200,
                'label'                     => $this->i18n('reports_insurer'),
                'help'                      => $this->i18n('reports_insurer'),
            );
            $filters['insurer'] = $filter;

            //DEFINE DATE TO FILTER
            $filter = array (
                'custom_id'                 => 'customer_branch',
                'name'                      => 'customer_branch',
                'type'                      => 'dropdown',
                'width'                     => 150,
                'options'                   => array(),
                'first_option_label'        => $this->i18n('all'),
                'no_select_records_label'   => $this->i18n('reports_customer_empty_branch'),
                'label'                     => $this->i18n('reports_customer_branch'),
                'help'                      => $this->i18n('reports_customer_branch')
            );
            $filters['customer_branch'] = $filter;

            //DEFINE POLICY NUM IS FILTER
            $filter = array (
                'custom_id'         => 'policy_num',
                'name'              => 'policy_num',
                'type'              => 'text',
                'label'             => $this->i18n('reports_policy_num'),
                'help'              => $this->i18n('reports_policy_num'),
                'value'             => ''
            );
            $filters['policy_num'] = $filter;

            //DEFINE DEADLINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_deadline_date',
                'name'      => 'from_deadline_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_from_deadline_date'),
                'help'      => $this->i18n('reports_from_deadline_date')
            );
            $filters['from_deadline_date'] = $filter;

            //DEFINE DEADLINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_deadline_date',
                'name'      => 'to_deadline_date',
                'type'      => 'date',
                'label'     => $this->i18n('reports_to_deadline_date'),
                'help'      => $this->i18n('reports_to_deadline_date')
            );
            $filters['to_deadline_date'] = $filter;

            //DEFINE POLICY TYPE FILTER
            $sql_policy_types = 'SELECT ct.id as idx, ct.id as option_value, cti18n.name as label' . "\n" .
                                'FROM ' . DB_TABLE_CONTRACTS_TYPES . ' AS ct' . "\n" .
                                'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS cti18n' . "\n" .
                                '  ON (ct.id=cti18n.parent_id AND cti18n.lang="' . $registry['lang'] . '")' . "\n" .
                                'WHERE ct.active=1 AND ct.deleted_by=0 AND ct.id IN (' . INCLUDED_CONTRACT_TYPES . ')' . "\n" .
                                'ORDER BY cti18n.name ASC' . "\n";
            $policy_types_options = $registry['db']->getAssoc($sql_policy_types);

            if (isset($policy_types_options[CONTRACTS_KASKO_POLICY_2]) && isset($policy_types_options[CONTRACTS_KASKO_POLICY_1])) {
                unset($policy_types_options[CONTRACTS_KASKO_POLICY_2]);

                $policy_types_options[CONTRACTS_KASKO_POLICY_1]['label'] = preg_replace('# \(.*\)$#', '', $policy_types_options[CONTRACTS_KASKO_POLICY_1]['label']);
                $policy_types_options[CONTRACTS_KASKO_POLICY_1]['option_value'] = sprintf('%d,%d', CONTRACTS_KASKO_POLICY_1, CONTRACTS_KASKO_POLICY_2);
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'policy_type',
                'name'      => 'policy_type',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_policy_type'),
                'help'      => $this->i18n('reports_policy_type'),
                'options'   => $policy_types_options,
            );
            $filters['policy_type'] = $filter;

            //DEFINE EMPLOYEE FILTER
            $sql_managers_list = 'SELECT c.id as idx, c.id as option_value, CONCAT(ci18n.name, " ", ci18n.lastname) as label' . "\n" .
                                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'WHERE c.active=1 AND c.deleted_by=0 AND c.subtype="normal" AND c.type="' . PH_CUSTOMER_EMPLOYEE . '"' . "\n" .
                                 'ORDER BY CONCAT(ci18n.name, " ", ci18n.lastname) ASC' . "\n";
            $managers_list = $registry['db']->getAssoc($sql_managers_list);

            //prepare filters
            $filter = array (
                'custom_id' => 'manager',
                'name'      => 'manager',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_manager'),
                'help'      => $this->i18n('reports_manager'),
                'options'   => $managers_list,
            );
            $filters['manager'] = $filter;

            //DEFINE CLIENT FILTER
            $filter = array (
                'custom_id'            => 'client',
                'name'                 => 'client',
                'type'                 => 'autocompleter',
                'autocomplete_type'    => 'customers',
                'autocomplete'         => array(
                    'type'          => 'customers',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                    'suggestions'   => '<name> <lastname>',
                    'fill_options'  => array(
                        '$client_autocomplete  => <name> <lastname>',
                        '$client_oldvalue      => <name> <lastname>',
                        '$client               => <id>'
                    ),
                    'filters'       => array(
                        '<type>' => strval(CUSTOMER_CLIENT_TYPE)
                    ),
                    'clear'         => 1,
                    'buttons_hide'  => 'search'
                ),
                'autocomplete_buttons' => 'clear',
                'label'                => $this->i18n('reports_client'),
                'help'                 => $this->i18n('reports_client'),
            );
            $filters['client'] = $filter;

            // DEFINE DEBIT NOTE FILTER
            $filter = array (
                'custom_id'            => 'debit_note',
                'name'                 => 'debit_note',
                'type'                 => 'custom_filter',
                'actual_type'          => 'autocompleter',
                'width'                => '222',
                'custom_template'      => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
                'autocomplete_type'    => 'documents',
                'autocomplete_buttons' => 'clear',
                'autocomplete'         => array('type'         => 'documents',
                                                'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'documents', 'documents'),
                                                'suggestions'  => '<full_num>',
                                                'fill_options' => array('$debit_note_autocomplete => <full_num>',
                                                                        '$debit_note_oldvalue     => <full_num>',
                                                                        '$debit_note              => <id>'),
                                                'filters'      => array('<type>' => 'IN (11,16)'),
                                                'buttons_hide' => 'search',
                                                'clear'        => 1),
                'label'                => $this->i18n('reports_debit_note'),
                'help'                 => $this->i18n('reports_debit_note_help'),
            );
            $filters['debit_note'] = $filter;


            // DEFINE DOCUMENT ID HIDDEN FILTER
            $filter = array (
                'custom_id' => 'document_id',
                'name'      => 'document_id',
                'type'      => 'hidden',
                'hidden'    => 1
            );
            $filters['document_id'] = $filter;

            // DEFINE DOCUMENT VALUE HIDDEN FILTER
            $filter = array (
                'custom_id' => 'invoice_value',
                'name'      => 'invoice_value',
                'type'      => 'hidden',
                'hidden'    => 1
            );
            $filters['invoice_value'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &self::$registry;

            if ($registry['request']->get('skip_session_filters')) {
                // set show type to 'check_bso' which has to be the default setting
                $registry->set('generated_report', 0, true);
                $filters['document_id']['value'] = '';
            }

            if (!empty($filters['document_id']['value'])) {
                $filters['type_invoice']['readonly'] = true;
                $filters['insurer']['readonly'] = true;
                $filters['insurer']['hide_plus_minus_buttons'] = true;

                if ($filters['type_invoice']['value'] == CONTRACT_TYPE_INVOICE_FEE) {
                    $filters['policy_num']['readonly'] = true;
                    $filters['policy_num']['disabled'] = true;
                    $filters['from_deadline_date']['readonly'] = true;
                    $filters['from_deadline_date']['disabled'] = true;
                    $filters['to_deadline_date']['readonly'] = true;
                    $filters['to_deadline_date']['disabled'] = true;
                    $filters['policy_type']['readonly'] = true;
                    $filters['policy_type']['disabled'] = true;
                    $filters['client']['readonly'] = true;
                    $filters['client']['disabled'] = true;
                }
            } else {
                // disable the fee option if the report has been triggered from the reports module
                foreach ($filters['type_invoice']['options'] as $key => $opt) {
                    if ($opt['option_value'] == CONTRACT_TYPE_INVOICE_FEE) {
                        unset($filters['type_invoice']['options'][$key]);
                    }
                }
            }

            if (!empty($filters['insurer']['value']) && empty($filters['insurer']['value_autocomplete'])) {
                // if the report has been triggered from document the name of the insurer has to be completed
                $sql_customer_name = 'SELECT parent_id as id, CONCAT(`name`, " ", `lastname`) as insurer_name FROM ' . DB_TABLE_CUSTOMERS_I18N . ' WHERE parent_id IN ("' . implode('","', $filters['insurer']['value']) . '") AND lang="' . $registry['lang'] . '"';
                $insurer_names = $registry['db']->GetAll($sql_customer_name);
                foreach ($insurer_names as $insurer_name) {
                    $key = array_search($insurer_name['id'], $filters['insurer']['value']);
                    $filters['insurer']['value_autocomplete'][$key] = trim($insurer_name['insurer_name']);
                }
            }

            // complete the option for branches
            if (!empty($filters['insurer']['value'])) {
                $insurer_ids = array();
                foreach ($filters['insurer']['value'] as $insurer_id) {
                    if (!empty($insurer_id)) {
                        $insurer_ids[] = $insurer_id;
                    }
                }
                if (!empty($insurer_ids)) {
                    $sql_branches = 'SELECT c.parent_customer, c.id as option_value, CONCAT(ci18n.`name`, " ", ci18n.`lastname`) as label' . "\n" .
                                    'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                    '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                                    'WHERE c.parent_customer IN (' . implode(',', $insurer_ids) . ') AND c.subtype = \'branch\' AND c.active=1 AND c.deleted_by=0' . "\n" .
                                    'ORDER BY c.is_main DESC';
                    $options_branches = $registry['db']->GetAll($sql_branches);

                    $option_by_customer = array();
                    foreach ($options_branches as $opt_branch) {
                        $option_by_customer[$opt_branch['parent_customer']][] = array(
                            'option_value' => $opt_branch['option_value'],
                            'label'        => $opt_branch['label']
                        );
                    }

                    foreach ($filters['insurer']['value'] as $key => $insurer_id) {
                        if (isset($option_by_customer[$insurer_id])) {
                            $filters['customer_branch']['options'][$key] = $option_by_customer[$insurer_id];
                        }
                    }
                }
            }

            $filters['insurer']['additional_filter'] = $filters['customer_branch'];
            unset($filters['customer_branch']);

            return $filters;
        }
    }
?>
