reports_company = Company
reports_employee = Employee
reports_type_table = Table type
reports_count_records = Results number

reports_option_calls = Calls
reports_option_customer_meetings = Meetings with companies
reports_option_contracts = Contracts
reports_option_projects = Related products with the company
reports_option_communications = Comments / Emails to company

reports_customer_name = Company name
reports_customer_type = Type company
reports_customer_address = Address for correspondence
reports_customer_phone = Contact phone
reports_customer_contact_person = Main contact
reports_customer_contact_person_phone = Phone of contact person

reports_call_document_date = No. / Date
reports_call_description = Description call
reports_call_next_step_status = Status
reports_call_next_step_description = Next step (description)
reports_call_employee = Employee

reports_meetings_document_date = No. / Date
reports_meetings_type = Type
reports_meetings_note_next_step = Notes / Next step
reports_meetings_employee = Employee

reports_contracts_document_date = No. / Date
reports_contracts_document_type = Document type
reports_contracts_status = Status
reports_contracts_employee = Employee
reports_contracts_documents_status_opened = Opened
reports_contracts_documents_status_locked = Locked
reports_contracts_documents_status_closed = Closed

reports_projects_code_date = No. / Date
reports_projects_type = Type project

reports_communications_date = Date
reports_communications_type_record = Type / kind record
reports_communications_description = Description
reports_communications_added_by = Employee

reports_type_record_document = Document
reports_type_record_event = Event
reports_type_record_project = Project

reports_communication_type_comment = comment
reports_communication_type_email = e-mail

error_reports_select_required_filters = Please, complete the required filters!
