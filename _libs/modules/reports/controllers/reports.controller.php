<?php

class Reports_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Report';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Reports';

    /**
     * Action name one of the add, edit, delete, list, etc.
     */
    public $defaultAction = 'index';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit'
    );

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'translate'
    );

    /**
     * A list of background actions
     */
    public $backgroundActions = array(
        'send_as_mail',
        'create_model',
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_email_content':
                $this->_email_content();
                break;
            case 'ajax_svg_img':
                $this->_svgImg();
                break;
            case 'generate_report':
            case 'ajax_generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    /**
     * Reports' homepage
     */
    protected function _index() {
        $request = &$this->registry['request'];
        $session = &$this->registry['session'];
        $filters_values = array();
        $filters = array();
        $report_type = array();

        $report_type = $this->getReportType();

        if (!empty($report_type['name'])) {
            // Check if the user have rights to this report
            $this->checkReportRights($report_type['name']);

            //check if we can run this report(FOR BACKGROUND WORK ONLY)
            if (method_exists($this, 'checkReportAccess')) {
                $this->checkReportAccess($report_type['name']);
            }

            $session_reports_param = 'reports_' . $report_type['name'] . '_report';
            $this->registry->set('session_reports_param', $session_reports_param);

            if ($this->checkGeneratedReport($session_reports_param)) {
                $this->registry->set('generated_report', 1, true);
            } else {
                $this->registry->set('generated_report', 0, true);
            };

            require_once PH_MODULES_DIR . "reports/models/report.filters.php";
            if (file_exists(PH_MODULES_DIR . "reports/plugins/" . $report_type['name'] . "/custom.report.filters.php")) {
                // take the settings of the report from DB
                // TODO: the process of getting the report settings can be optimized by making the report name accessible everywhere in the code where the getReportSettings() method is called
                // TODO: another optimization is to make the settings array accessible as array (not only as constants) everywhere in the reports module, because whenever we use a report we may need the report settings
                // TODO: the whole report model should be accessible everywhere in the reports module
                Reports::getReportSettings($this->registry, $report_type['name']);
                require_once PH_MODULES_DIR . "reports/plugins/" . $report_type['name'] . "/custom.report.filters.php";
                $defineCustomFilters = new Custom_Report_Filters($this->registry, $report_type['name']);
                $filters = $defineCustomFilters->defineFilters($this->registry);
                if ($request->get('prepare_data_for_lightbox')) {
                    // Special filter to contain the scripts for this module
                    if (!isset($filters['scripts'])) {
                        $filters['scripts'] = array (
                            'custom_id'       => 'scripts',
                            'name'            => 'scripts',
                            'type'            => 'custom_filter',
                            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_scripts.html'
                        );
                    }
                }
                $filters_values = $defineCustomFilters->getFilterValues($this->registry, $report_type);

                // Prepare a flag to check if the report filters should be disabled
                $disable_report_filters = $this->registry->get('disable_report_filters');

                foreach ($filters as $key => $value) {
                    // If the report filters should be disabled
                    if ($disable_report_filters) {
                        // Make all report filters: disabled
                        $filters[$key]['disabled'] = true;
                        // Make all report filters: readonly
                        $filters[$key]['readonly'] = true;
                    }

                    if (isset ($filters_values[$key])) {
                        $filters[$key]['value'] = $filters_values[$key];
                        if (!empty($filters_values[$key . '_autocomplete'])) {
                            $filters[$key]['value_autocomplete'] = $filters_values[$key . '_autocomplete'];
                        }
                    } else {
                        if (isset($value['setting']) && $value['setting']) {
                            if ($this->registry->get('generated_report')) {
                                $filters[$key]['value'] = '';
                            }
                        } else {
                            $filters[$key]['value'] = '';
                        }
                    }
                }
                if ($this->registry->get('generated_report')) {
                    $filters_values['generated'] = 1;
                }
            } else {
                //show error 'no such record'
                $this->registry['messages']->setError($this->i18n('error_no_such_report'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'index', array('report_type' => ''));
            }
        }
        if (!empty($defineCustomFilters) && method_exists($defineCustomFilters, 'processDependentFilters')) {
            $filters = $defineCustomFilters->processDependentFilters($filters);
        }

        // TODO: Make a function to process dependent additional filters

        if (!empty($defineCustomFilters) && property_exists($defineCustomFilters, 'custom_filters_template')) {
            $this->registry->set('custom_filters_template', $defineCustomFilters->custom_filters_template);
        }
        $this->registry->set('filters_values', $filters_values);
        $this->registry->set('report_filters', $filters);

        if ($this->action == 'export') {
            $this->_export();
        }

        if ($request->get('prepare_data_for_lightbox')) {
            $this->viewer                                = $this->getViewer();
            $this->viewer->data['hide_report_selection'] = true;
            $this->viewer->setFrameset('frameset_blank.html');
            $this->viewer->prepare();
            print $this->viewer->fetch();
            exit;
        } elseif ($request->get('prepare_data_for_ajax')) {
            $this->viewer = $this->getViewer();
            $this->viewer->data['hide_report_selection'] = true;
            $this->viewer->data['hide_filters'] = true;
            $this->viewer->data['dashlet_mode'] = true;
            $this->viewer->setFrameset('frameset_blank.html');
            $this->viewer->prepare();
            $errors = '';
            if ($this->registry['messages']->getErrors()) {
                $errors = new Viewer($this->registry);
                $errors->setFrameset('message.html');
                $errors->data['display'] = 'error';
                $errors->data['items'] = $this->registry['messages']->getErrors();
                $errors = $errors->fetch();
            }
            echo json_encode(array('errors' => $errors, 'data' => $this->viewer->fetch()));
            exit;
        } else {
            return true;
        }
    }

    /**
     * Generating of a report after submit
     */
    protected function _generate_report() {
        if ($this->registry['request']->isPost()) {
            $redirect_parameters = array();
            foreach ($this->registry['request']->getAll('post') as $post_var => $post_value) {
                if (is_array($post_value)) {
                    foreach ($post_value as $key => $val) {
                        $redirect_parameters[] = array(
                            'param' => sprintf('%s[%s]', $post_var, $key),
                            'value' => $val
                        );
                    }
                } else {
                    $redirect_parameters[] = array(
                        'param' => $post_var,
                        'value' => $post_value
                    );
                }
            }

            $redirect_url = sprintf('%s://%s%sindex.php?d=%s',
                                    (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                    $_SERVER["HTTP_HOST"], PH_BASE_URL,
                                    General::encodeUrlData($redirect_parameters));

            header('Location: ' . $redirect_url);
            exit;
        }

        ini_set("memory_limit","1050M");
        $this->registry->set('generated_report', 1, true);
        return true;
    }

    /**
     * Export report results
     */
    protected function _export() {
        // check for pattern set in the request
        $export_pattern = $this->registry['request']->get('pattern');
        $this->registry->set('export_pattern', $export_pattern, true);

        require_once $this->viewersDir . 'reports.export.viewer.php';
        $this->viewer = new Reports_Export_Viewer($this->registry);
    }

    /**
     * Load report in a dashlet
     */
    public function _dashlet() {
        //get the requested model ID
        $id = $this->registry['request']->get('dashlet');

        require_once PH_MODULES_DIR . "dashlets/models/dashlets.factory.php";
        $filters = array('where'    => array('d.id = ' . $id, 'd.active = 1'),
                         'sanitize' => true);
        $dashlet = Dashlets::searchOne($this->registry, $filters);
        if ($dashlet) {
            $filters = $dashlet->get('filters');

            $filters['report_type'] = $dashlet->get('controller');
            $report_type['name'] = $dashlet->get('controller');
            $this->registry->set('filters', $filters, true);
            $session_param = 'reports_' . $report_type['name']. '_report';

            //load needed lang files
            $i18n_files = array();
            $i18n_files[] = sprintf('%s%s%s%s%s%s',
                PH_MODULES_DIR,
                'reports/plugins/',
                $report_type['name'],
                '/i18n/',
                $this->registry['lang'],
                '/reports.ini');
            $this->loadI18NFiles($i18n_files);

            // take the settings of the report from DB
            Reports::getReportSettings($this->registry, $report_type['name']);

            $this->registry['session']->set($session_param, $filters, '', true);
            $this->registry['session']->set('generated', 1, $session_param, true);
            $this->registry->set('report_type', $report_type, true);
            $this->registry->set('generated_report', 1, true);
        } else {
            return false;
        }

    }

    /**
     * Getting the report name and source
     *
     * @return array - report name and origin where it was defined from, or an empty array if not found
     */
    public function getReportType() {
        $request = &$this->registry['request'];
        $session = &$this->registry['session'];

        if ($request->isRequested('report_type')) {
            $report_type = array(
                'name' => $request->get('report_type'),
                'source' => 'request'
            );
        } else if ($this->registry->get('report_type')) {
            $report_type = $this->registry->get('report_type');
        } else {
            $report_type = array();
        }
        $this->registry->set('report_type', $report_type, true);

        return $report_type;
    }

    /**
     * Get model of current report
     *
     * @return Report - report model
     */
    public function getReportModel() {
        $report_type = $this->getReportType();
        if (empty($report_type)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report_type = $report_type['name'];

        $report = Reports::getReports($this->registry, array('name' => $report_type));
        $report = $report ? $report[0] : null;

        return $report;
    }

    /**
     * Get model name of current report
     *
     * @return string - Report model name
     */
    public function getReportModelName() {
        $reportModel = $this->getReportModel();
        if (!$reportModel) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $reportType = $reportModel->get('type');

        return str_replace(' ', '_', ucwords(str_replace('_', ' ', $reportType)));
    }

    /**
     * Checking if a report from the certain type is already generated
     *
     * @param string $report_name - report name
     * @return bool - result of the operation
     */
    private function checkGeneratedReport($report_name) {
        if ($this->registry->get('generated_report')) {
            return true;
        } else if ($this->registry['session']->isRequested($report_name)) {
            if ($this->registry['session']->isRequested('generated', $report_name) && !$this->registry['request']->get('skip_session_filters')) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * Fetch content (body) of selected e-mail template
     */
    public function _email_content() {

        $request = $this->registry['request'];
        $mail = $request->get('ajax_email_content');
        if ($mail) {
            require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
            $email = Emails::searchOne($this->registry, array('where' => array('e.id = ' . $mail)));
            if (!empty($email)) {
                echo $email->get('body');
            }
        }
        exit;
    }

    /**
     * Send reports results as e-mail
     */
    public function _sendAsMail() {

        $registry = &$this->registry;
        $session = &$registry['session'];
        /** @var $request Request */
        $request = &$registry['request'];
        $db = $registry['db'];

        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        if ($request->get('original_user') && $request->get('background_mode')) {
            $filters = array('where' => array('u.id = ' . PH_AUTOMATION_USER,
                                              'u.hidden IS NOT NULL'),
                             'sanitize' => true);
            $user = Users::searchOne($registry, $filters);
            $user->unsanitize();
            $user->getGroups();
            $user->sanitize();
            $registry->set('currentUser', $user, true);
        }

        // get current report
        $report = $this->getReportModel();

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $registry['translater']->loadFile($i18n_file);

        /** @var Reports $factory - get report factory class name */
        $factory = Reports::getPluginFactory($report->get('type'));

        if (!$request->get('background_mode')) {

            //get selected records for the report from the session
            $selected = $session->get('reports_' . $report->get('type') . '_report', 'selected_items');

            if (empty($selected['ids']) && empty($selected['select_all'])) {
                //nothing is selected
                $registry['messages']->setError($this->i18n('reports_no_selected_records'));
                $registry['messages']->insertInSession($registry);
                $this->redirect($this->module, '', array('report_type' => $report->get('type')));
                die;
            }

            // !!! IMPORTANT !!! here we will pass additional validation for the selection
            if (method_exists($this, 'validateSelection')) {
                $this->validateSelection($selected);
            }

            //create selection in the temporary table
            $factory::setTemporarySelection($registry, $selected);

            $attachments = $request->get('attached_files');
            if ($attachments) {
                if (!is_array($attachments)) {
                    $attachments = array($attachments);
                }
                foreach ($attachments as $key => $attachment) {
                    $attachments[$key] = 'attached_files[]=' . $attachment;
                }
                $attachments = '&' . implode('&', $attachments);
            }

            // IMPORTANT set to true only during testing in browser
            $test_in_browser = false;

            $location = sprintf('%s/index.php?%s=%s&%s=%s&%s=%s&%s=%s&%s=%s&%s=%s&%s=%s',
                                $registry['config']->getParam('crontab', 'base_host'),
                                $registry->get('module_param'), 'reports', 'reports', 'send_as_mail',
                                'report_type', $report->get('type'),
                                'original_user', $registry['currentUser']->get('id'),
                                'background_mode', '1',
                                'email_subject', urlencode($request->get('email_subject')),
                                'email_template', $request->get('email_template'));
            if ($request->get('sms_template')) {
                $location .= sprintf('&%s=%s', 'sms_template', $request->get('sms_template'));
            }

            // START HERE THE BACKGROUND WORK
            $location .= $attachments;
            //get report filters and pass them through the request
            $filters = $registry['session']->get('reports_' . $report->get('type') . '_report');
            $filters_get_array = array();
            foreach ($filters as $key => $value) {
                if (is_array($value)) {
                    foreach ($value as $vk => $vv) {
                        $filters_get_array[] = 'filters[' . $key . '][' . $vk . ']=' . urlencode($vv);
                    }
                } else {
                    $filters_get_array[] = 'filters[' . $key . ']=' . urlencode($value);
                }
            }

            //create folder for wget logs
            FilesLib::createDir(PH_LOGGER_DIR . 'wget', 0777, false);

            if (!$test_in_browser) {
                //post filters as they might be too long causing Error 414 (Submitted URI too large!)
                $post_data = $filters_get_array ?? [];
                $serverProcessesHelper = new ServerProcessesHelper($this->registry['config']->getParam('crontab', 'base_host'));
                $serverProcessesHelper->backgroundExecute($location, ['post_data' => $post_data]);

            } else {
                // test in browser
                //IMPORTANT: Error 414 (Submitted URI too large!) might occur if too many filters have been prepared
                $location .= '&' . implode('&', $filters_get_array);
                header('Location: ' . $location);
                die($location);
            }

            $session->remove('report_type');
            $session->remove('reports_' . $report->get('type') . '_report');
            $session->remove('reports_' . $report->get('type') . '_report', 'selected_items');

            $registry['messages']->setMessage($this->i18n('reports_background_work_start'));
            if (method_exists($this, 'prepareAnnouncementContent')) {
                $registry['messages']->setMessage($this->i18n('reports_background_announcement'));
            }
            $registry['messages']->insertInSession($registry);
            $this->redirect($this->module, '', array('report_type' => ''));
            die;
        }

        // !!! HERE STARTS THE BACKGROUND WORK

        //IMPORTANT: if the background is executed with POST
        //make sure that all the data is derived from the DB, and not from POST
        if ($request->isPost()) {
            $this->registry->set('get_old_vars', true, true);
        }

        //try to guess the name of the table where data is stored
        //until the send operation finishes
        $table = Reports::getTemporaryTableName($registry);

        //prepare report settings and define them as constants
        $settings = $factory::getReportSettings($registry, $report->get('type'));
        $report->set('settings', $settings, true);

        require_once PH_MODULES_DIR . 'users/models/users.factory.php';

        $user = $registry['currentUser'];
        $filters = array('where' => array('u.id = ' . $request->get('original_user'),
                                          'u.hidden >= 0'),
                         'sanitize' => true);
        $original_user = Users::searchOne($registry, $filters);
        $original_user->getRights();
        $original_user->getPermissions();
        $original_user->getGroups();
        $original_user->getDepartments();

        if (empty($original_user)) {
            $this->result['additional_info'][] = $this->i18n('error_reports_wrong_original_user');
        } else {
            $registry->set('currentUser', $original_user, true);

            // !!! IMPORTANT !!! Call custom function for each plugin to do the mail sending etc.
            // All the arguments are passed by reference
            //we need back only the "result" argument
            $this->customEmailSend($settings);

            //delete/clean-up temporary table
            $factory::dropTemporaryTable($registry);

            $this->sendResultsEmail($report);

            if (method_exists($this, 'prepareAnnouncementContent')) {

                $registry->set('currentUser', $user, true);

                require_once PH_MODULES_DIR . 'announcements/models/announcements.factory.php';
                $announcement = new Announcement($registry);
                $announcement->set('subject', $this->i18n('reports_background_work_finished'), true);
                $announcement->set('type', -1, true);
                $announcement->set('category', -1, true);
                $announcement->set('priority', 'normal', true);
                $announcement->set('validity_term', General::strftime('%Y-%m-%d', strtotime('+ 1 week')), true);
                $announcement->set('assignments_type', 'Users', true);
                $announcement->set('users_assignments', array($original_user->get('id')), true);

                $content = $this->prepareAnnouncementContent();

                $announcement->set('content', $content, true);
                $announcement->set('id', '', true);

                $announcement->add();
            }
        }

        die('Background Work Finished!');
    }

    /**
     * Creates a new model (document etc.)
     */
    public function _createModel() {

        $registry = &$this->registry;

        if (empty($registry['currentUser'])) {
            //set system current user
            require_once PH_MODULES_DIR .  'users/models/users.factory.php';
            $filters = array('where' => array('u.id = ' . PH_AUTOMATION_USER,
                                              'u.hidden IS NOT NULL'));
            $user = Users::searchOne($registry, $filters);
            $user->unsanitize();
            $user->getGroups();
            $user->sanitize();
            $registry->set('currentUser', $user, true);
        }

        // get current report model
        $report = $this->getReportModel();

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $registry['translater']->loadFile($i18n_file);

        //prepare report settings
        $settings = General::parseSettings($report->get('settings'));

        //call report-specific function for creation
        if (method_exists($this, 'customCreateModel')) {
            $this->customCreateModel($settings);
        } else {
            $registry['messages']->setError($this->i18n('reports_not_records_issued'));
            $registry['messages']->insertInSession($registry);
            $this->redirect($this->module, '', array('report_type' => $report->get('type')));
            exit;
        }
    }

    public function createDocument($record, $settings) {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $db = $registry['db'];

        $properties = array();
        foreach ($settings as $key => $value) {
            if (preg_match('#transform_(a|b)_(.*)#', $key, $matches)) {
                if ($matches[1] == 'b') {
                    //basic var requested
                    $properties[$matches[2]] = $record[$value];
                } else {
                    $request->set($matches[2], $record[$value], 'post', true);
                }
            } elseif ($key == 'create_type') {
                $properties['type'] = $value;
            } elseif ($key == 'create_contains_additional_vars') {
                //set all additional vars to request
                foreach ($record as $var_name => $var_value) {
                    if ($var_name == 'gt2') {
                        continue;
                    }
                    $request->set($var_name, $var_value, 'post', true);
                }
            }
        }

        if (empty($properties['type'])) {
            return false;
        }

        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';

        $filters = array('where' => array('dt.id = ' . $properties['type']),
                         'sanitize' => true);
        $model_type = Documents_Types::searchOne($this->registry, $filters);

        if ($model_type) {
            foreach ($model_type->getAll() as $key => $value) {
                if (preg_match('#^default_(.*)#', $key, $matches)) {
                    if (empty($properties[$matches[1]])) {
                        $properties[$matches[1]] = $value;
                    }
                }
            }
            $properties['direction'] = $model_type->get('direction');
            if (empty($properties['name'])) {
                $properties['name'] = $model_type->get('name') ?: $this->i18n('document');
            }
        }
        $error = false;
        $model = new Document($this->registry, $properties);
        $this->registry->set('edit_all', true, true);
        $model->getVars();

        $vars = $model->get('vars');
        $gt2 = array();
        $gt2_idx = false;
        foreach ($vars as $key => $var) {
            if ($var['type'] == 'gt2') {
                $gt2 = $var;
                $gt2_idx = $key;
                break;
            }
        }

        if (!empty($record['gt2']) && !empty($gt2)) {
            $gt2['values'] = $record['gt2']['values'];
            if (!empty($record['gt2']['plain_values'])) {
                $gt2['plain_values'] = $record['gt2']['plain_values'];
                foreach ($gt2['plain_values'] as $key => $value) {
                    $model->set($key, $value, true);
                }
            }
            $model->set('grouping_table_2', $gt2, true);
            $model->calculateGT2();
            $gt2 = $model->get('grouping_table_2');
            $model->set('table_values_are_set', true, true);
            //this param is used to allow the save of GT2 using the model's replaceVars method
            $this->registry['request']->set('gt2_requested', true, '', true);
            $vars[$gt2_idx] = $gt2;
            $model->set('vars', $vars, true);
        }

        if (!empty($settings['simulation'])) {
            return $model;
        }

        //load documents i18n file
        $registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');

        //validate + add (save basic and additional vars)
        if ($model->save()) {
            unset($model->counter);
        } else {
            return false;
        }

        return $model;
    }

    public function createFinanceIncomesReason($record, $settings) {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $db = $registry['db'];

        //load finance i18n files
        $i18n_path = PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/';
        $this->registry['translater']->loadFile(FilesLib::readDir($i18n_path, false, '', '', true));

        $properties = array();
        foreach ($settings as $key => $value) {
            if (preg_match('#transform_(b)_(.*)#', $key, $matches)) {
                if ($matches[1] == 'b' && !empty($record[$value])) {
                    //basic var requested
                    $properties[$matches[2]] = $record[$value];
                }
            } elseif ($key == 'create_type') {
                $properties['type'] = $value;
            }
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';

        $filters = array('where' => array('fdt.id = ' . $properties['type']),
                         'sanitize' => true);
        $model_type = Finance_Documents_Types::searchOne($this->registry, $filters);

        foreach ($model_type->getAll() as $key => $value) {
            if (preg_match('#^default_(.*)#', $key, $matches)) {
                if (empty($properties[$matches[1]])) {
                    $properties[$matches[1]] = $value;
                }
            }
        }
        $error = false;

        $model = new Finance_Incomes_Reason($this->registry, $properties);
        if (!empty($record['gt2'])) {
            $model->getGT2Vars();
            $table = $model->get('grouping_table_2');
            $table['values'] = $record['gt2']['values'];
            $table['plain_values'] = $record['gt2']['plain_values'];
            foreach ($table['plain_values'] as $key => $value) {
                $model->set($key, $value, true);
            }
            $model->set('grouping_table_2', $table, true);
            $model->set('table_values_are_set', true, true);
            $model->calculateGT2();
        }

        //fix the dates
        if ($model->get('fiscal_event_date_count') != '') {
            if ($model->get('periods_' . $model->get('fiscal_event_date_point'))) {
                $date_start = ($model->get('fiscal_event_date_point') == 'start') ? $date_from : $date_to;
            } else {
                $date_start = General::strftime('%Y-%m-%d');
            }
            if ($model->get('fiscal_event_date_period_type') == 'working') {
                require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                    $fiscal_event_date = Calendars_Calendar::calcDateOnWorkingDays($model->registry, $date_start,
                        $model->get('fiscal_event_date_count'), $model->get('fiscal_event_date_direction'));
            } elseif ($model->get('fiscal_event_date_direction') == 'before') {
                $fiscal_event_date = General::strftime('%Y-%m-%d',
                    strtotime('-' . $model->get('fiscal_event_date_count') . ' day',strtotime($date_start)));
            } else {
                $fiscal_event_date = General::strftime('%Y-%m-%d',
                    strtotime('+' . $model->get('fiscal_event_date_count') . ' day', strtotime($date_start)));
            }
            $model->set('fiscal_event_date', $fiscal_event_date, true);
        } else {
            $model->set('fiscal_event_date', General::strftime('%Y-%m-%d'), true);
        }
        if ($model->get('date_of_payment_count') != '') {
            if ($model->get('date_of_payment_point') == 'issue') {
                $date_start = General::strftime('%Y-%m-%d');
                if ($model->get('date_of_payment_period_type') == 'working') {
                    require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                    $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($model->registry, $date_start,
                        $model->get('date_of_payment_count'), 'after');
                } else {
                    $date_of_payment = General::strftime('%Y-%m-%d',
                        strtotime('+' . $model->get('date_of_payment_count') . ' day', strtotime($date_start)));
                }
             } else {
                 $date_of_payment = array('count := ' . $model->get('date_of_payment_count'),
                                          'period_type := ' . $model->get('date_of_payment_period_type'),
                                          'period := day',
                                          'direction := after',
                                          'point := receive');
                 $date_of_payment = implode('\n', $date_of_payment);
             }
            $model->set('date_of_payment', $date_of_payment, true);
        } else {
            $model->set('date_of_payment', General::strftime('%Y-%m-%d'), true);
        }
        if (!$model->get('issue_date')) {
            $model->set('issue_date', General::strftime('%Y-%m-%d'), true);
        }

        if (!empty($settings['simulation'])) {
            //set a fake number of the invoice
            $model->set('num', '&lt;number&gt;', true);

            //fix customer name, address, eik, vat_num
            $query = 'SELECT IF(c.is_company, c.eik, c.ucn) as eik, c.in_dds as vat_num,' . "\n" .
                     'TRIM(CONCAT(ci.name, " ", ci.lastname)) as name, ' . "\n" .
                     'IF(c.is_company, ci.registration_address, ci.address_by_personal_id) as address, ' . "\n" .
                     'IF(c.is_company, ci.mol, TRIM(CONCAT(ci.name, " ", ci.lastname))) as received_by ' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci ON (c.id=ci.parent_id AND ci.lang="' . $model->get('model_lang') .'")' . "\n" .
                     ' WHERE c.id=' .  $model->get('customer');
            $customer_info = $this->registry['db']->GetRow($query);
            if ($customer_info) {
                $model->set('customer_name', $customer_info['name'], true);
                $model->set('customer_address', $customer_info['address'], true);
                $model->set('eik', $customer_info['eik'], true);
                $model->set('vat_num', $customer_info['vat_num'], true);
                $model->set('received_by', $customer_info['received_by'], true);
            }

            //this data is used for preview of the invoice only
            $model->set('added_by', $model->get('issue_by'), true);
            $model->set('modified_by', $model->get('issue_by'), true);
            $model->set('status_modified_by', $model->get('issue_by'), true);

            //get the name of the issue by and set it to the invoice
            $query = 'SELECT CONCAT(firstname, " ", lastname) FROM ' . DB_TABLE_USERS_I18N . ' WHERE parent_id="' .  $model->get('issue_by') . '" AND lang="' . $model->get('model_lang') . '"';
            $issue_by_name = $this->registry['db']->GetOne($query);
            if ($issue_by_name) {
                $model->set('added_by_name', $issue_by_name, true);
                $model->set('modified_by_name', $issue_by_name, true);
                $model->set('status_modified_by_name', $issue_by_name, true);
            }

            return $model;
        }

        if ($model->get('status') && $model->get('status')=='finished') {
            $model->getCounter();
        }

        if (!$model->add()) {
            return false;
        }

        $filters = array('where' => array('fir.id = ' . $model->get('id')));
        $model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $model->getGT2Vars();

        return $model;
    }

    /**
     * Convert and save images for all graphics for report before export to pdf
     */
    public function _svgImg() {

        $registry = &$this->registry;
        $request = &$registry['request'];

        $params = $request->isPost() ? $request->getAll('post') : array();

        echo intval(Chart::convertSVGImages($params));
        exit;
    }

    /**
     * IMPORTANT!!!
     * This method checks if the background work is running
     * for this report and if YES, denies access to the report
     *
     * @param string $report - report plugin name
     * @return bool - true, if access is allowed
     */
    public function checkReportAccess($report) {

        $request = &$this->registry['request'];

        //exclude the process in order not to kill itself
        if ($request->get('background_mode')) {
            return true;
        }

        if (preg_match('#WIN#i', PHP_OS)) {
            //get active processes in WINDOWS with their command line parameters
            exec('wmic PROCESS get Commandline', $output);
        } else {
            exec('ps -e -F|grep -i wget', $output);
        }

        $regex1 = '#reports=(' . implode('|', $this->backgroundActions) . ')#';
        //do it for this installation only (in case there is more than one installation on the server)
        //IMPORTANT: sometimes the installations are accessed from address different from that in settings > crontab > base_host
        $location1 = sprintf('%s://%s%s?%s=reports',
                        (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                        $_SERVER["HTTP_HOST"], $_SERVER['PHP_SELF'],
                        Router::MODULE_PARAM);
        $regex2 = '#' . preg_quote($location1) . '#';
        $location2 = sprintf('%s/index.php?%s=reports',
                        $this->registry['config']->getParam('crontab', 'base_host'),
                        Router::MODULE_PARAM);
        $regex3 = '#' . preg_quote($location2) . '#';
        foreach ($output as $row) {
            if (preg_match($regex1, $row) && (preg_match($regex2, $row) || preg_match($regex3, $row)) &&
                //preg_match('#original_user=' . $this->registry['currentUser']->get('id') . '#', $row) &&
                preg_match('#background_mode=\d+#', $row) && preg_match('#report_type=' . $report . '#', $row)) {

                $this->registry['messages']->setError($this->i18n('reports_background_work_running'));
                $this->registry['messages']->insertInSession($this->registry);
                // redirect to index page of reports module with no report selected
                $this->redirect($this->module, '', array(), $this->module);
            }
        }

        return true;
    }

    /**
     * Check if the current user has rights to a report
     *
     * @param string $report_type_name - the type name of the report
     * @return mixed                   - returns true if there is such report and the user have rights for it or redirects the user if no such report or no rights to it
     */
    public function checkReportRights($report_type_name) {
        // If there is a report type name
        if (!empty($report_type_name)) {
            // Try to get the report
            $filters = array(
                'name' => $report_type_name,
                'sanitize' => true
            );
            $report = Reports::getReports($this->registry, $filters);
            $report = array_shift($report);

            // If there is such report and the user have rights to it
            if (!empty($report) && is_object($report) && $report->checkRights()) {
                // Return that the user have rights to this report
                return true;
            }
        }

        // Set error: no rights
        $this->registry['messages']->setError($this->i18n('reports_no_rights'));
        $this->registry['messages']->insertInSession($this->registry);
        // Redirect to module Reports
        $this->redirect($this->module, '', array('report_type' => ''));
    }

    /**
     * Sends feedback information to the person who generates the report
     *
     * @param Report $report - report model
     * @return bool  result of the operation
     */
    public function sendResultsEmail(Report $report) {

        $settings = $report->get('settings');

        if (!empty($settings['feedback_email'])) {
            require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
            $filters = array('where' => array('e.id = ' . $settings['feedback_email']), 'sanitize' => true);
            $template = Emails::searchOne($this->registry, $filters);

            if (empty($template)) {
                return false;
            }
        } elseif (!empty($settings['template'])) {
            $template = $settings['template'];
        } else {
            return true;
        }

        if (!empty($settings['user_id'])) {
            $user = Users::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        sprintf('u.id = %d', $settings['user_id']),
                        'u.active = 1',
                        'u.hidden IS NOT NULL',
                        'u.email != \'\'',
                    ),
                    'sanitize' => true,
                    'model_lang' => $this->registry['lang'],
                )
            );
        } else {
            $user = $this->registry['currentUser'];
        }
        if (!$user) {
            return false;
        }

        $mail = new Mailer($this->registry);

        $config = $this->registry['config'];

        $mail->template['sender'] = $config->getParam('emails', 'from_email');
        $mail->template['from_name'] = $config->getParam('emails', 'from_name');

        $mail->template['replyto_email'] = $config->getParam('emails', 'replyto_email') ?: $mail->template['from_email'];
        $mail->template['replyto_name'] = $config->getParam('emails', 'replyto_name') ?: $mail->template['from_name'];

        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');

        $details = new Viewer($this->registry);
        $details->setFrameset('frameset_blank.html');
        $details->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $report->get('type') . '/';
        $details->template = 'email_details.html';
        $details->data['reports_results'] = $this->result;
        $base_host = $this->registry['config']->getParam('crontab', 'base_host');
        // short base url should be just the host, it is used for images and such
        $details->data['base_short'] = str_replace(parse_url($base_host, PHP_URL_PATH), '', $base_host);
        $details->data['base_url'] = $base_host . '/index.php?';
        $details->loadCustomI18NFiles($i18n_file);
        $details = $details->fetch();

        $mail->placeholder->add('sent_at', General::strftime('%d.%m.%Y %H:%M'));
        $mail->placeholder->add('details', $details);
        $mail->placeholder->add('user_name', $user->get('firstname'));
        $mail->template['recipient'] = $user->get('email');
        $recipient_name = $user->get('firstname') . ' ' . $user->get('lastname');
        $mail->template['names_to'] = array($recipient_name);
        //recipient data of current user
        $mail->placeholder->add('recipient_name', $recipient_name);
        $mail->placeholder->add('recipient_firstname', $user->get('firstname'));

        $mail->templateName = 'custom_template';
        $mail->template['subject'] = $template->get('subject');
        $mail->template['body'] = $template->get('body');

        $mail->send();

        return true;
    }

    /**
     * Prepare some report defaults
     *
     * @param mixed $registry - the registry
     * @return string         - the type name of the current report plugin
     */
    public function prepareReportDefaults(&$registry) {
        // Get the report name
        $report_type = $this->getReportType();
        $report_type = $report_type['name'];

        // Load the report plugin i18n file
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report_type,
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $registry['translater']->loadFile($i18n_file);

        //TODO: use the function loadReportsI18NFromDb to add the DB translations also

        // Load the report settings
        Reports::getReportSettings($registry, $report_type);

        // Return the report type name
        return $report_type;
    }

    /**
     * Function to load the translation for the current report
     * from the DB (`i18n` table translations)
     *
     * @return void
     */
    protected function loadReportsI18NFromDb() : void
    {
        $reportType = $this->getReportType();
        $i18nService = \Nzoom\I18n\I18nService::getInstance($this->registry);
        $i18nService->loadInTranslater(
            'reports',
            [$reportType['name']]
        );
    }
}

?>
