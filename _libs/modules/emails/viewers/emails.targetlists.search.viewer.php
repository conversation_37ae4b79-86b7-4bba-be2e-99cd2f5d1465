<?php

class Emails_Targetlists_Search_Viewer extends Viewer {
    public $template = 'targetlists_search.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'emails.targetlists.factory.php';

        $filters = Emails_Targetlists::saveSearchParams($this->registry, array(), 'search_');
        list($emails_targetlists, $pagination) = Emails_Targetlists::pagedSearch($this->registry, $filters);

        $this->data['emails_targetlists'] = $emails_targetlists;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('emails_targetlists');
        $this->data['title'] = $title;
    }
}

?>
