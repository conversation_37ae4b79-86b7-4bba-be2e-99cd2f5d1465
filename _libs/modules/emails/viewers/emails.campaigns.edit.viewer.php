<?php

class Emails_Campaigns_Edit_Viewer extends Viewer {
    public $template = 'campaigns_edit.html';

    public function prepare() {
        $this->model = $this->registry['emails_campaign'];

        $this->model->set('sender_readonly', $this->registry['config']->getParam('emails', 'sender_custom_user_emails'), true);

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $configurations = array();
        if ($this->registry['request']->isPost()) {
            $configurations['targetlists'] = $this->model->get('targetlists');
            $configurations['templates'] = $this->model->get('templates');
            $configurations['sel_files'] = $this->model->get('sel_files');
        } else {
            $configurations = $this->model->getConfigurations();
        }
        $this->data['configurations'] = $configurations;

        //get targetlists
        require_once PH_MODULES_DIR . 'emails/models/emails.targetlists.factory.php';
        $filters = array('where' => array('et.active = 1'),
                         'sort' => array('eti18n.name ASC'));
        $emails_targetlists = Emails_Targetlists::search($this->registry, $filters);
        $options_targetlists = array();
        foreach($emails_targetlists as $et) {
            $options_targetlists[] = array(
                'active_option' => $et->get('active'),
                'label' => $et->get('name'),
                'option_value' => $et->get('id'));
        }
        $this->data['options_targetlists'] = $options_targetlists;

        //get email templates
        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $filters = array('sanitize' => true,
                         'where' => array('e.model = "Emails_Campaign"',
                                          'e.active = 1'),
                         'sort' => array('e.added DESC')
                        );
        $templates = Emails::search($this->registry, $filters);
        $options_templates = array();
        foreach($templates as $p) {
            $options_templates[] = array(
                'active_option' => $p->get('active'),
                'label' => mb_substr($p->get('subject'), 0, 50, mb_detect_encoding($p->get('subject'))) . ((mb_strlen($p->get('subject'), mb_detect_encoding($p->get('subject'))) > 50) ? '...' : ''),
                'option_value' => $p->get('id')
            );
        }
        $this->data['options_templates'] = $options_templates;

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('emails_campaigns_edit');
        $this->data['title'] = $title;
    }
}

?>
