<?php

class Emails_List_Viewer extends Viewer {
    public $template = 'list.html';
    public $filters = array();

    public function prepare() {

        $filters = Emails::saveSearchParams($this->registry);
        list($emails, $pagination) = Emails::pagedSearch($this->registry, $filters);

        $this->data['emails'] = $emails;
        $this->data['pagination'] = $pagination;

        // prepare types names
        $this->data['model_types_names'] = Emails::getModelTypesNames($this->registry);

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('emails_templates');
        $this->data['title'] = $title;
    }
}

?>
