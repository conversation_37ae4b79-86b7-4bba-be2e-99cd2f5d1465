<?php

class Emails_Targetlists_Edit_Viewer extends Viewer {
    public $template = 'targetlists_edit.html';

    public function prepare() {
        $this->model = $this->registry['emails_targetlist'];
        $this->data['emails_targetlist'] = $this->model;

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->data['recipients'] = $this->model->getRecipients();

        $this->data['recipients']['erred_emails'] = $this->model->get('erred_emails') ? $this->model->get('erred_emails') : array();

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

    }

    public function prepareTitleBar() {
        $title = $this->i18n('emails_targetlists_edit');
        $this->data['title'] = $title;
    }
}

?>
