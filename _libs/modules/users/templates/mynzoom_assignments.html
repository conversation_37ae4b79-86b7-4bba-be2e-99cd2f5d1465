<tr>
  <td class="t_caption2">
    <script type="text/javascript" src="{$smarty.const.PH_MODULES_URL}assignments/javascript/assignments.js?{$system_options.build}"></script>
    <div class="t_caption2_title">
      {#users_mynzoom_default_assignments#|escape}
    </div>
  </td>
</tr>
<tr>
  <td>
    <script type="text/javascript">
      var type_assignments = {json encode=$type_assignments};
    </script>
    {include file=`$smarty.const.PH_MODULES_DIR`/assignments/templates/_assignments_configurator_panel.html config_templates=$config_templates}
    <table cellspacing="0" cellpadding="3" border="0" id="default_assignments">
      <tr>
        <td width="20">&nbsp;</td>
        <td class="strong" nowrap="nowrap" width="200">{#users_mynzoom_assignments_type#|escape}</td>
        <td class="strong" nowrap="nowrap" width="200"{if $events} style="display:none"{/if}>{#users_mynzoom_assignments_department#|escape}</td>
        {foreach from=$assignments_types item=at}
          {capture assign=alabel}users_mynzoom_assignments_{$at}{/capture}
          <td class="strong" nowrap="nowrap">{$smarty.config.$alabel|escape}
          {include file=`$smarty.const.PH_MODULES_DIR`/assignments/templates/_assignments_group.html type=$at users=null}
          </td>
        {/foreach}
        <td>
          <img src="{$theme->imagesUrl}plus.png" border="0" class="pointer" alt="{#users_mynzoom_add_new_default_assignment#|escape}" onclick="insertNewDefaultAssignmentRow(this, '{$layout}')" title="{#users_mynzoom_add_new_default_assignment#|escape}" />
        </td>
      </tr>
      {counter name=row_c start=0 print=false}
      {foreach from=$assignments item=departs key=selected_type}
        {foreach from=$departs item=users key=selected_department}
          {counter name=row_c assign=row}
          <tr class="vtop{if $users.colored} row_{$users.colored}{/if}" id="default_assignments_{$row}">
            <td>
              <input type="checkbox" name="included[]" value="{$row}" id="included_{$row}" class="included_row" title="{#check_to_include#|escape}" checked="checked" />
            </td>
            <td>
              <select class="selbox" name="model_type_{$row}" id="model_type_{$row}" onchange="changeMyNZoomAssignmentsOptions(this)" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#users_mynzoom_assignments_type#|escape}">
                {foreach from=$model_types item='model_type'}
                  <option value="{$model_type->get('id')}"{if $model_type->get('id') eq $selected_type} selected="selected"{/if}>{$model_type->get('name')|escape}</option>
                {/foreach}
              </select>
            </td>
            <td{if $events} style="display:none"{/if}>
              <select class="selbox" name="department_{$row}" id="department_{$row}" onchange="changeMyNZoomAssignmentsOptions(this)" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="dropdownTypingSearch(this, event);" title="{#users_mynzoom_assignments_department#|escape}">
                <option value="0"{if $selected_department eq '0'} selected="selected"{/if}>[{#users_mynzoom_assignments_no_department#}]</option>
                <option value="-1"{if $selected_department eq '-1'} selected="selected"{/if}>[{#users_mynzoom_assignments_any_department#}]</option>
                {foreach from=$departments item='department'}
                  <option value="{$department->get('id')}"{if $department->get('id') eq $selected_department} selected="selected"{/if}>{$department->get('name')|escape|default:"&nbsp;"|indent:$department->get('level'):"-"}</option>
                {/foreach}
              </select>
            </td>
            {foreach from=$assignments_types item=at}
              {array assign=data users=$users.$at ac_settings=$assignments_settings.$at}
              {assign var='inactive_at' value=true}
              {foreach from=$type_assignments.$selected_type item=at_active}
                {if $at_active eq $at}
                  {assign var='inactive_at' value=false}
                {/if}
              {/foreach}
              {include file=`$smarty.const.PH_MODULES_DIR`/assignments/templates/_assign.html
                       type=$at
                       data=$data
                       borderless=true
                       ac_index=$row
                       inactive_at=$inactive_at
              }
            {/foreach}
            <td nowrap="nowrap">
              &nbsp;
              <script type="text/javascript">
                  fixACState($('model_type_{$row}'));
              </script>
            </td>
          </tr>
        {/foreach}
      {/foreach}
    </table>
  </td>
</tr>