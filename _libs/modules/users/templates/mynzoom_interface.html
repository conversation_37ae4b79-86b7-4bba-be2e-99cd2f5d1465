<script type="text/javascript">
  timezones = {$timezones_obj};
</script>
    <input type="hidden" name="layout" id="layout" value="{$layout}" />
    <input type="hidden" name="timezones" id="timezones" value="{$timezones_obj|escape}" />
    <table cellspacing="0" cellpadding="0" border="0" class="t_table">
      <tr>
        <td class="t_caption3">
          <div class="t_caption2_title">
            <img src="{$theme->imagesUrl}small/info.png" alt="{#system_info#|escape}" {popup text=$layouts.$layout.description|default:'&nbsp;' caption=#system_info#} />
            {$layouts.$layout.name|escape}
          </div>
        </td>
      </tr>

      <tr>
        <td style="padding: 5px;">
          <input type="checkbox" name="action_labels" id="action_labels" value="1"{if $settings.interface.action_labels} checked="checked"{/if} /><label for="action_labels">{#users_mynzoom_settings_action_labels#|escape}</label><br />
        </td>
      </tr>
      {if $allow_alternative_lang_switch}
        <tr>
          <td class="t_caption2">
            <div class="t_caption2_title">{#users_mynzoom_settings_alternative_lang_switch#|escape}</div>
          </td>
        </tr>
        <tr>
          <td style="padding: 5px;">
            <input type="checkbox" name="alternative_lang_switch" id="alternative_lang_switch" value="1"{if $settings.interface.alternative_lang_switch} checked="checked"{/if} onclick="$('alternative_keyboard_inputs_container').style.display=(this.checked)?'':'none'" /><label for="alternative_lang_switch">{#users_mynzoom_settings_allow_alternative_lang_switch#|escape}</label><br />

            <div id="alternative_keyboard_inputs_container" {if !$settings.interface.alternative_lang_switch} style="display: none"{/if}>
              <ol id="alternative_keyboard_inputs" class="alternative_keyboard_inputs">
              {foreach from=$alternative_keyboard_inputs item='kinput'}
                <li class="sortable pointer">
                  <input type="checkbox" name="alternative_keyboard_inputs[]" id="alternative_keyboard_input_{$kinput.abbr}" value="{$kinput.abbr}"{if $kinput.selected} checked="checked"{/if} /><label for="alternative_keyboard_input_{$kinput.abbr}"><span class="langLinkSample {$kinput.abbr}">{$kinput.abbr}</span>{$kinput.description}</label>
                </li>
              {/foreach}
              </ol>
              {#users_mynzoom_settings_alternative_lang_switch_disclaimer#}
            </div>
            <script type="text/javascript">
              Position.includeScrollOffsets = true;
              Sortable.create('alternative_keyboard_inputs', {ldelim}tag: 'LI',
                                                        containment: 'alternative_keyboard_inputs',
                                                        constraint: 'vertical',
                                                        only: 'sortable',
                                                        scroll: 'alternative_keyboard_inputs_container'
                                                       {rdelim});
            </script>
          </td>
        </tr>
      {/if}

      {if $userCanSelectTheme}
      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title">{#users_mynzoom_theme#|escape}</div>
        </td>
      </tr>
      <tr>
        <td style="padding: 5px;">
          {include file="`$theme->templatesDir`input_dropdown.html"
            name='theme'
            label=#users_mynzoom_theme#|escape
            value=$settings.interface.theme|default:''
            sequences=''
            required=''
            disabled=''
            standalone=1
            options=$themes
            index=1
            width=200
          }
        </td>
      </tr>
      {/if}

      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title">{#users_timezone#|escape}</div>
        </td>
      </tr>
        <tr>
          <td nowrap="nowrap" id="timers_1_1" style="padding: 5px;">
            <select name="timezone_area" id="areas_1" onchange="setTimezones(this);" class="selbox">
            {foreach from=$timezones_all item='zones' key='zones_area'}
            <option value="{$zones_area}"{if $zones_area eq $timezone_area} selected="selected"{assign var="timezones" value=$zones}{/if}>{$zones_area}</option>
            {/foreach}
            </select>
            <span id="timers_1_2">
            {include file=`$theme->templatesDir`input_dropdown.html
              name='timezones'
              label=#timezones#
              value=$timezone
              sequences=''
              required=1
              disabled=0
              standalone=1
              options=$timezones
              index=1
              width=200
            }
            </span>
          </td>
        </tr>
      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title">{#users_mynzoom_settings_rpp#|escape}</div>
        </td>
      </tr>
      {array assign='rpp_vals' eval='array(5,10,25,50,75,100)'}
      {assign var=cols value=3}
      {math assign=dcols equation='a*2' a=$cols}
      {math assign=cwidth equation='80/a' a=$cols format='%d'}
      <tr>
        <td>
          <table class="t_table" cellspacing="0" cellpadding="0" border="0" id="interface_rpp">
            {foreach from=$modules item='mod' name='mi' key='mk'}
            {assign var='idx' value=$smarty.foreach.mi.iteration}
            {assign var='var_name' value=$mod.var_name}
            {if !($mk % $cols)}<tr class="{cycle values='t_odd1, t_even1'}">{/if}
            <td style="width: {$cwidth}%;">{$mod.module_name}</td>
            <td>
              <select class="selbox small" name="{$var_name}" id="{$var_name}" {if $smarty.foreach.mi.first}title="{#users_mynzoom_settings_rpp#|escape}" onchange="return confirmAction('all_default_rpp', function(el) {ldelim} setAllDefaultRPP(el); {rdelim}, this);"{/if}>
                {foreach from=$rpp_vals item='rpp'}
                <option value="{$rpp}"{if $settings.interface.$var_name eq $rpp || $rpp eq 10 && !$settings.interface.$var_name} selected="selected"{/if}>{$rpp}</option>
                {/foreach}
              </select>
            </td>
            {if $smarty.foreach.mi.last && $mk % $cols != $cols - 1}<td colspan="{math equation='2*(b-(a+1)%b)' a=$mk b=$cols}">&nbsp;</td>{/if}
            {if ($mk % $cols) == $cols - 1 || $smarty.foreach.mi.last}</tr>{/if}
            {/foreach}
            <tr>
              <td colspan="{$dcols}" class="t_caption2">
                <div class="t_caption2_title">{#users_mynzoom_settings_rpp_communications#|escape}</div>
              </td>
            </tr>
            <tr class="{cycle values='t_odd1, t_even1'}">
              <td>{#communications#|escape}</td>
              <td colspan="{$dcols}-1">
                <select class="selbox small" name="list_communications" id="list_communications">
                  {foreach from=$rpp_vals item='rpp'}
                  <option value="{$rpp}"{if $settings.interface.list_communications eq $rpp || $rpp eq 10 && !$settings.interface.list_communications} selected="selected"{/if}>{$rpp}</option>
                  {/foreach}
                </select>
              </td>
            </tr>
            <tr>
              <td colspan="{$dcols}" class="t_caption2">
                <div class="t_caption2_title">{#users_mynzoom_settings_rpp_history#|escape}</div>
              </td>
            </tr>
            <tr class="{cycle values='t_odd1, t_even1'}">
              <td>{#history#|escape}</td>
              <td colspan="{$dcols}-1">
                <select class="selbox small" name="list_history" id="list_history">
                  {foreach from=$rpp_vals item='rpp'}
                  <option value="{$rpp}"{if $settings.interface.list_history eq $rpp || $rpp eq 10 && !$settings.interface.list_history} selected="selected"{/if}>{$rpp}</option>
                  {/foreach}
                </select>
              </td>
            </tr>
            <tr>
              <td colspan="{$dcols}" class="t_caption2">
                <div class="t_caption2_title">{#users_mynzoom_settings_rpp_timesheets#|escape}</div>
              </td>
            </tr>
            <tr class="{cycle values='t_odd1, t_even1'}">
              <td>{#timesheets#|escape}</td>
              <td colspan="{$dcols}-1">
                <select class="selbox small" name="list_timesheets" id="list_timesheets">
                  {foreach from=$rpp_vals item='rpp'}
                  <option value="{$rpp}"{if $settings.interface.list_timesheets eq $rpp || $rpp eq 10 && !$settings.interface.list_timesheets} selected="selected"{/if}>{$rpp}</option>
                  {/foreach}
                </select>
              </td>
            </tr>
            <tr>
              <td colspan="{$dcols}" class="t_caption2">
                <div class="t_caption2_title">{#users_mynzoom_settings_rpp_reminders#|escape}</div>
              </td>
            </tr>
            <tr class="{cycle values='t_odd1, t_even1'}">
              <td>{#remind#|escape}</td>
              <td colspan="{$dcols}-1">
                <select class="selbox small" name="list_reminders" id="list_reminders">
                  {foreach from=$rpp_vals item='rpp'}
                  <option value="{$rpp}"{if $settings.interface.list_reminders eq $rpp || $rpp eq 10 && !$settings.interface.list_reminders} selected="selected"{/if}>{$rpp}</option>
                  {/foreach}
                </select>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title floatl">{#users_mynzoom_settings_confirm_required#|escape}</div>
          <div class="floatr">
            <span onclick="toggleCheckboxes(this, 'confirm', true, 'settings_confirm')" class="pointer">{#check_all#|escape}</span> |
            <span onclick="toggleCheckboxes(this, 'confirm', false, 'settings_confirm')" class="pointer">{#check_none#|escape}</span>
          </div>
        </td>
      </tr>
      <tr id="settings_confirm">
        <td>
          {assign var='num_cols' value=$confirm_actions|@count}
          {if $num_cols gt $cols}{assign var='num_cols' value=$cols}{/if}
          {foreach from=$confirm_actions item='confirm_actions_part' name='ci'}
          <div class="vtop floatl" style="min-height: 180px; width: {math equation='100/a' a=$num_cols format='%.2f'}%;{if $smarty.foreach.ci.iteration % $num_cols == 1} clear: both;{/if}">
            <div class="t_caption3 t_caption3_title">{$confirm_actions_part.label|escape|default:"&nbsp;"}</div>
            {foreach from=$confirm_actions_part.actions item='lbl' key='act'}
            <input type="checkbox"{if !in_array($act, $settings.interface.skip_confirm)} checked="checked"{/if} name="confirm[]" id="confirm_{$act}" value="{$act}" title="{$lbl|escape}" /> <label for="confirm_{$act}">{$lbl|escape}</label><br />
            {/foreach}
          </div>
          {if $smarty.foreach.ci.last && $smarty.foreach.ci.iteration % $num_cols}
          <div class="vtop floatl" style="min-height: 180px; width: {math equation='(a-b%a)*100/a' a=$num_cols b=$smarty.foreach.ci.iteration format='%.2f'}%;">
            <div class="t_caption3 t_caption3_title">&nbsp;</div>
          </div>
          {/if}
          {/foreach}
        </td>
      </tr>
    </table>
