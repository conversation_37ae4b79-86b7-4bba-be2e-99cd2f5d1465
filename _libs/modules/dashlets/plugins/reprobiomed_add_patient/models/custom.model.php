<?php

/**
 * Reprobiomed ADD PATIENT plugin custom model class
 */
Class Custom_Model extends Model {
    public $modelName = 'Dashlet';
    public $plugin_name = 'reprobiomed_add_patient';


    public function __construct($dashlet_settings = '') {
        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        return true;
    }

    public function prepareCustomData(&$model) {
        //prepare custom filters
        $filters = array();
        if ($model->get('title_template')) {
            $filters['title_template'] = $model->get('title_template');
            $model->unsetProperty('title_template', true);
        }

        $model->set('filters', $filters, true);

        return $model;
    }

    /**
     * Parse settings for current plugin and set them as properties to model
     *
     * @param string $dashlet_settings - settings field of dashlet
     */
    public function parseDashletPluginSettings($dashlet_settings) {

        $settings_rows = preg_split('/(\n|\r|\r\n)/', $dashlet_settings);

        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0]=='#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $this->$key = trim($value);
        }
    }

    /**
     * Prepare all necessary custom options for dashlet
     *
     * @return array - array with arrays of options
     */
    public function prepareVars() {

        $options = array();

        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $patient = new Customer($this->registry, array('type' => $this->customer_type_patient));
        $vars = $patient->getAssocVars(true);
        unset($patient);
        foreach($vars['contact_point']['autocomplete']['fill_options'] as $idx => $fill) {
            if (preg_match('#\$contact_point *=>#', $fill)) {
                $vars['contact_point']['autocomplete']['fill_options'][$idx] = str_replace('$contact_point', '$contact_point_autocomplete', $fill);
            } elseif (preg_match('#\$contact_point_id *=>#', $fill)) {
                $vars['contact_point']['autocomplete']['fill_options'][$idx] = str_replace('$contact_point_id', '$contact_point', $fill);
            }
        }

        return $vars;
    }

    /**
     * Create customer of type "Patient" or "Donor"
     */
    public function createCustomer() {
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.types.factory.php';

        $request = $this->registry['request'];
        $db = &$this->registry['db'];
        $lang = $this->registry['lang'];

        $db->StartTrans();

        if ($request->get('donor')) {
            $customer_type_id = $this->customer_type_donor;
        } else {
            $customer_type_id = $this->customer_type_patient;
        }
        $filters_type = array('where' => array('ct.id = ' . $customer_type_id,
                                               'ct.active = 1'),
                              'sanitize'   => true,
                              'model_lang' => $request->get('model_lang'));
        $customer_type = Customers_Types::searchOne($this->registry, $filters_type);

        $currentUser = $this->registry['currentUser'];
        $currentUserId = $this->registry['currentUser']->get('id');

        $customer = Customers::buildModel($this->registry);
        $customer->set('type', $customer_type_id, true);
        $customer->set('is_company', 0, true);
        $customer->set('group', $customer_type->getDefaultGroup(), true);

        // call plugin automation
        $method = 'setPatientCode';
        $automations_controller = Automations_Controller::getPluginController($this->registry, array('method' => $method));
        if ($automations_controller) {
            $automations_controller->action = 'add';
            $request->set('code', '', 'post', true);
            $automations_controller->$method(array(
                'model' => $customer,
                'start_model_type' => $customer_type_id
            ));
            $customer->set('code', $request->get('code'), true);
            $request->remove('code');
        }
        unset($automations_controller);

        $substitute_ucn = '**********';
        $ucn = $request->get('ucn');

        if ($request->get('surname')) {
            $customer->set('lastname', trim($request->get('surname') . ' ' . $request->get('lastname')), true);
        }

        // get additional required fields
        $required_fields = $this->registry['config']->getParamAsArray('customers', 'validate_' . $customer_type_id);

        // select default department if no department is set
        if (in_array('department', $required_fields) && $this->registry['currentUser']->get('default_department')) {
            $customer->set('department', $this->registry['currentUser']->get('default_department'), true);
        }

        // load i18n files for customers
        $lang_file_customer = sprintf('%s%s%s%s', PH_MODULES_DIR, 'customers/i18n/', $lang, '/customers.ini');
        $this->loadI18NFiles($lang_file_customer);

        if ($customer->validate('add')) {
            // escape slashes before adding
            $customer->slashesEscape();

            if ($customer->add()) {
                $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                 'model_lang' => $customer->get('model_lang'));
                $new_customer = Customers::searchOne($this->registry, $filters);
                $this->old_model = new Customer($this->registry);
                $this->old_model->sanitize();
                $audit_parent = Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'add', 'new_model' => $new_customer, 'old_model' => $this->old_model));
            } else {
                // strip slashes if adding was unsuccessful
                $customer->slashesStrip();

                //set type name to model
                if ($customer_type) {
                    $customer->set('type_name', $customer_type->get('name'), true);
                }
                $db->FailTrans();
            }

            //add additional vars
            if (!$db->HasFailedTrans()) {
                if ($customer_type_id == $this->customer_type_patient) {
                    //save partner, contact and info_health_status only for patient (not for donor)
                    $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Customer" AND model_type=' . $customer_type_id;
                    $vars = $db->GetAssoc($query);
                    $system_data = ", NOW(), $currentUserId, NOW(), $currentUserId, ''";
                    $insert_sql =
                        'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . '(`model_id`,`var_id`,`num`,`value`,`added`,`added_by`,`modified`,`modified_by`,`lang`) VALUES'."\n".
                        //partner
                        '(' . $customer->get('id') . ', '.$vars['partner_name'] . ', 1, "'.General::slashesEscape($request->get('partner_name')).'"'.$system_data.'),'."\n".
                        '(' . $customer->get('id') . ', '.$vars['partner_id'] . ', 1, "'.General::slashesEscape($request->get('partner_id')).'"'.$system_data.'),'."\n".
                        //info_health_status checkbox
                        '(' . $customer->get('id') . ', '.$vars['info_health_status'] . ', 1, '.@intval($request->get('info_health_status')).$system_data.')';

                    $insert_cstm = array();
                    if ($request->get('partner_id')) {
                        $insert_cstm[] = sprintf('(\'Customer\', %d, \'Customer\', %d, %d, 1, "")',
                                                 $customer->get('id'), $request->get('partner_id'), $vars['partner_id']);
                    }

                    if ($request->get('contact_point')) {
                        $cnt = 0;
                        $inserts = array();
                        $contact_point    = $request->get('contact_point_autocomplete');
                        $contact_point_id = $request->get('contact_point');
                        foreach($contact_point as $idx => $cp) {
                            if (!empty($cp)) {
                                $cnt++;
                                $inserts[] = '(' . $customer->get('id') . ', '.$vars['contact_point']    .', '.$cnt.', "'.General::slashesEscape($cp).'"'.$system_data.')';
                                $inserts[] = '(' . $customer->get('id') . ', '.$vars['contact_point_id'] .', '.$cnt.', "'.$contact_point_id[$idx]    .'"'.$system_data.')';

                                $insert_cstm[] = sprintf('(\'Customer\', %d, \'Customer\', %d, %d, %d, "")',
                                                 $customer->get('id'), $contact_point_id[$idx], $vars['contact_point_id'], $cnt);
                            }
                        }
                        if (!empty($inserts)) {
                            $insert_sql .= ",\n" . implode(",\n", $inserts);
                        }
                    }

                    // use flag for getting all additional data below
                    $this->registry->set('get_old_vars', false, true);

                    // use new model from db
                    $customer = $new_customer;

                    $old_model = clone $customer;
                    $old_model->getVars();
                    $old_model->sanitize();

                    if ($db->Execute($insert_sql)) {
                        // insert model-to-cstm_model relations
                        if ($insert_cstm) {
                            $customer->saveCstmRelatives(array(), $insert_cstm);
                        }

                        $filters = array('where'      => array('c.id = ' . $customer->get('id')),
                                         'model_lang' => $customer->get('model_lang'));
                        $new_customer = Customers::searchOne($this->registry, $filters);
                        $new_customer->getVars();
                        $new_customer->sanitize();

                        Customers_History::saveData($this->registry,
                                                    array('model' => $new_customer,
                                                          'action_type' => 'edit',
                                                          'new_model' => $new_customer,
                                                          'old_model' => $old_model));
                    } else {
                        $errors_occurred = true;
                    }

                    if (empty($errors_occurred) && !$db->HasFailedTrans() && $request->get('partner_id')) {
                        //set the current patient as partner of this one's partner
                        $filters = array('where' => array('c.id = ' . $request->get('partner_id')));
                        $old_partner = Customers::searchOne($this->registry, $filters);
                        $old_partner->getVars();
                        $old_partner->sanitize();

                        $query = 'DELETE FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' WHERE model_id="'.$request->get('partner_id').'" AND var_id IN ('.$vars['partner_name'].', '.$vars['partner_id'].')';
                        $db->Execute($query);
                        $insert_sql =
                        'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . '(`model_id`,`var_id`,`num`,`value`,`added`,`added_by`,`modified`,`modified_by`,`lang`) VALUES'."\n".
                        //partner
                        '(' . $request->get('partner_id') . ', '.$vars['partner_name'] . ', 1, "'.General::slashesEscape(trim($customer->get('name').' ' .$customer->get('lastname'))).'"'.$system_data.'),'."\n".
                        '(' . $request->get('partner_id') . ', '.$vars['partner_id'] . ', 1, "'.General::slashesEscape($customer->get('id')).'"'.$system_data.')';
                        $db->Execute($insert_sql);

                        // update model-to-cstm_model relations for partner
                        $old_partner->saveCstmRelatives(
                            array($vars['partner_id'] => 0),
                            array(sprintf('(\'Customer\', %d, \'Customer\', %d, %d, 1, "")',
                                          $old_partner->get('id'), $customer->get('id'), $vars['partner_id'])));

                        $filters = array('where' => array('c.id = ' . $request->get('partner_id')));
                        $new_partner = Customers::searchOne($this->registry, $filters);
                        $new_partner->getVars();
                        $new_partner->sanitize();

                        Customers_History::saveData($this->registry,
                                                    array('model' => $new_partner,
                                                          'action_type' => 'edit',
                                                          'new_model' => $new_partner,
                                                          'old_model' => $old_partner));
                    }

                    $this->registry->set('get_old_vars', true, true);
                }


                if (empty($errors_occurred) && !$db->HasFailedTrans()) {
                    //tag the customer
                    $tags = array();
                    if ($ucn && $ucn != $substitute_ucn) {
                        $gender = $ucn[8]%2;
                        if ($gender) {
                            //woman
                            $tags[] = 2;
                        } else {
                            //man
                            $tags[] = 3;
                        }
                    }
                    if (!empty($tags)) {
                        $customer->unsanitize();
                        $old_customer = clone $customer;
                        $old_customer->getModelTagsForAudit();
                        $old_customer->sanitize();

                        // call addTags method directly (skip validation), no old tags to delete
                        if ($customer->addTags($tags)) {
                            $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                             'skip_permissions_check' => true);
                            $new_customer = Customers::searchOne($this->registry, $filters);
                            $new_customer->getModelTagsForAudit();
                            $new_customer->sanitize();

                            $audit_parent = Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'tag', 'new_model' => $new_customer, 'old_model' => $old_customer));
                        }
                    }
                }
            }
        } else {
            $errors_occurred = true;
        }

        // if any errors occurred, fail transaction
        if (!empty($errors_occurred)) {
            $db->FailTrans();
        }

        // the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        $result = array('errors' => array(), 'messages' => array());
        if ($dbTransError) {
            $error_txt = ($customer_type_id == $this->customer_type_patient) ? 'error_plugin_reprobiomed_add_patient_customer_not_added' : 'error_plugin_reprobiomed_add_donor_customer_not_added';
            $this->registry['messages']->setError($this->i18n($error_txt), '', -1);
            $errors = $this->registry['messages']->getErrors();
            foreach($errors as $err) {
                $result['errors'][] = $err;
            }
        } else {
            $message_txt = ($customer_type_id == $this->customer_type_patient) ? 'message_plugin_reprobiomed_add_patient_customer_added_successfully' : 'message_plugin_reprobiomed_add_donor_customer_added_successfully';
            $url = sprintf('%s?%s=customers&amp;customers=view&amp;view=%s',
                           $_SERVER['PHP_SELF'], $this->registry['module_param'], $customer->get('id'));
            $this->registry['messages']->setMessage($this->i18n($message_txt, array($url)), '', -1);
            $messages = $this->registry['messages']->getMessages();
            foreach($messages as $msg) {
                $result['messages'][] = $msg;
            }
        }

        return json_encode($result);
    }
}

?>
