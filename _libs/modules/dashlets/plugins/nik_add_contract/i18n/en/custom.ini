#LABELS FOR ALL PLUGINS
dashlets_dashlet_for = Dashlet for
plugin_name = Name
plugin_description = Description
dashlets_default = Add by default
plugin_full_width = Full screen width
help_dashlets_default = Specifies whether the dashlet to be added by default

help_dashlets_dashlet_for = Module or report dashlet is for
help_plugin_full_width = If selected, dashlet will be displayed in full screen width

#PLUGIN SPECIFIC LABELS
plugin_nik_add_contract = Quick adding of contract
plugin_nik_add_contract_no_specific_fields = No specific fields for this dashlet

plugin_nik_add_contract_customer = Customer
plugin_nik_add_contract_service = Service
plugin_nik_add_contract_period = Period
plugin_nik_add_contract_machine_type = Machine type
plugin_nik_add_contract_num = Number
plugin_nik_add_contract_price = Price
plugin_nik_add_contract_duration = Duration
plugin_nik_add_contract_electronic_invoice = Electronic invoice
plugin_nik_add_contract_email = Send to email
plugin_nik_add_contract_invoice_firstdate = Invoice first date

plugin_nik_add_contract_preview = Preview
plugin_nik_add_contract_add = Create contract
plugin_nik_add_contract_financial_contact = financial contact

error_plugin_nik_add_contract_preview_missing_data = Please, fill in all necessary fields to preview contract!
error_plugin_nik_add_contract_negative_price_required = Please, fill negative value for the price of the discount rows!
error_plugin_nik_add_contract_negative_total = Error! Negative total amount for the contract!