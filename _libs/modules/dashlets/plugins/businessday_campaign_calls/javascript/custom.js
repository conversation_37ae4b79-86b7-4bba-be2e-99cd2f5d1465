campaignCalls = function() {
    /**
     * Reloads dashlet and restores the title
     */
    this.back = function() {
        $('title_text_dashlet_' + this.dashlet_id).innerHTML = this.oldTitle;
        dashletsLoad('content_dashlet_' + this.dashlet_id, 'plugin', this.plugin, this.dashlet_id);
    };

    /**
     * Shows and hides dashlet fields in function of the step chosen
     */
    this.manageStepFields = function(step) {

        if (!step) {
            // step has been cleared
            // hide all fields
            $('campCallsEmployee').parentNode.parentNode.style.display = 'none';
            $('campCallsDate').parentNode.parentNode.style.display = 'none';
            return true;
        }
        // get fields for the step
        var fields = this.stepsOptions[step].fields;
        // process date and time fields
        if (fields.list_call_date || fields.list_call_hour) {
            $('campCallsDate').parentNode.parentNode.style.display = '';
            if (!fields.list_call_date) {
                $('campCallsDate').parentNode.parentNode.cells[0].style.visibility = 'hidden';
                $('campCallsDate').parentNode.parentNode.cells[2].style.visibility = 'hidden';
                $('campCallsDate').disabled = true;
            } else {
                $('campCallsDate').parentNode.parentNode.cells[0].style.visibility = '';
                $('campCallsDate').parentNode.parentNode.cells[2].style.visibility = '';
                $('campCallsDate').disabled = false;
            }
            if (!fields.list_call_hour) {
                $('campCallsTime').parentNode.parentNode.cells[4].style.visibility = 'hidden';
                $('campCallsTime').parentNode.parentNode.cells[6].style.visibility = 'hidden';
                $('campCallsTime').disabled = true;
            } else {
                $('campCallsTime').parentNode.parentNode.cells[4].style.visibility = '';
                $('campCallsTime').parentNode.parentNode.cells[6].style.visibility = '';
                $('campCallsTime').disabled = false;
            }
        } else {
            $('campCallsDate').parentNode.parentNode.style.display = 'none';
        }
        // process employee and location fields
        if (fields.list_call_location || fields.list_call_employee) {
            $('campCallsEmployee').parentNode.parentNode.style.display = '';
            if (!fields.list_call_employee) {
                $('campCallsEmployee').parentNode.parentNode.cells[0].style.visibility = 'hidden';
                $('campCallsEmployee').parentNode.parentNode.cells[2].style.visibility = 'hidden';
                $('campCallsEmployee').disabled = true;
            } else {
                $('campCallsEmployee').parentNode.parentNode.cells[0].style.visibility = '';
                $('campCallsEmployee').parentNode.parentNode.cells[2].style.visibility = '';
                $('campCallsEmployee').disabled = false;
            }
            if (!fields.list_call_location) {
                $('campCallsPlace').parentNode.parentNode.cells[4].style.visibility = 'hidden';
                $('campCallsPlace').parentNode.parentNode.cells[6].style.visibility = 'hidden';
                $('campCallsPlace').disabled = true;
            } else {
                $('campCallsPlace').parentNode.parentNode.cells[4].style.visibility = '';
                $('campCallsPlace').parentNode.parentNode.cells[6].style.visibility = '';
                $('campCallsPlace').disabled = false;
            }
        } else {
            $('campCallsEmployee').parentNode.parentNode.style.display = 'none';
        }
    };

    /**
     * loads the second step of the dashlet where the user can select a customer
     */
    this.listCalls = function(form) {
        // prepare ajax options
        Effect.Center('loading');
        Effect.Appear('loading');
        var dashlet_id = this.dashlet_id;
        var container = $(this.plugin);

        var opt = {
            method: 'post',
            parameters: Form.serialize(form),
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                eval(t.responseText);
                if (result.errors) {
                    // error occurred - show them
                    var mc = $('dashlet_messages_' + dashlet_id);
                    mc.innerHTML = result.errors;
                    Effect.Appear(mc);
                    setTimeout(function() {Effect.Fade(mc);}, 5000);
                } else {
                    // custom title has been provided
                    if (result.change_title) {
                        campaignCalls.oldTitle = $('title_text_dashlet_' + dashlet_id).innerHTML;
                        $('title_text_dashlet_' + dashlet_id).innerHTML = result.change_title;
                    }
                    // show success messages if document has been added
                    if (result.messages) {
                        var mc = $('dashlet_messages_' + dashlet_id);
                        mc.innerHTML = result.messages;
                        Effect.Appear(mc);
                        setTimeout(function() {Effect.Fade(mc);}, 5000);
                    }
                    // show second step content
                    if (result.data) {
                        container.innerHTML = result.data;
                    }
                    scripts = $$('#' + container.id + ' script');
                    for (var i in scripts) {
                        ajaxLoadJS(scripts[i]);
                    }
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action';
        url += '&custom_plugin_action=listCalls&plugin=' + this.plugin;
        url += '&dashlet=' + this.dashlet_id + '&use_ajax=1';
        new Ajax.Request(url, opt);
    };

    /**
     * Loads customer data, customer contacts and calls list
     * IMPORTANT!!!
     * Contact and calls are reloaded only when we come from AC.
     * If we come from customer edit lightbox, only reload customer data.
     */
    this.loadCustomerData = function(autocomplete, data) {
        // prepare ajax options
        Effect.Center('loading');
        Effect.Appear('loading');
        var dashlet_id = this.dashlet_id;
        var cContainer = $(this.plugin + '_customer');
        var lContainer = $(this.plugin + '_full_list');
        var lContainerH = $(this.plugin + '_full_list_header');

        var params = 'customer=' + data.id;
        // only main data of customer OR full reload
        if (autocomplete.action == 'edit') {
            params += '&cstm_only=1';
        } else {
            getCustomerBranches(autocomplete, data);
        }

        var opt = {
            method: 'post',
            parameters: params,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                eval(t.responseText);
                if (result.errors) {
                    // show errors if any
                    var mc = $('dashlet_messages_' + dashlet_id);
                    mc.innerHTML = result.errors;
                    Effect.Appear(mc);
                    setTimeout(function() {Effect.Fade(mc);}, 5000);
                } else {
                    if (result.cData) {
                        if (!(autocomplete.action == 'edit')) {
                            // show customer data when the function is invoked
                            // from AC
                            cContainer.innerHTML = result.cData;
                            cContainer.style.display = '';
                            scripts = $$('#' + cContainer.id + ' script');
                            for (i in scripts) {
                                ajaxLoadJS(scripts[i]);
                            }
                        } else {
                            // refresh customer data when customer has been
                            // updated from lightbox
                            pContainer = cContainer;
                            cContainer = $$('#' + pContainer.id + ' #customer_data')[0];
                            pContainer.removeChild(cContainer);
                            pContainer.innerHTML = result.cData + pContainer.innerHTML;
                        }
                    }
                    if (result.lData) {
                        // show customer calls list if we have calls
                        lContainer.innerHTML = result.lData;
                        lContainer.parentNode.style.display = '';
                        lContainerH.style.display = '';
                        scripts = $$('#' + lContainer.id + ' script');
                        for (i in scripts) {
                            ajaxLoadJS(scripts[i]);
                        }
                    } else if (!(autocomplete.action == 'edit')) {
                        // no calls - hide the panel
                        lContainer.parentNode.style.display = 'none';
                        lContainerH.style.display = 'none';
                    }
                    // display messages after edit
                    if (autocomplete.action == 'edit' && data.messages) {
                        if (data.messages.length) {
                            data.messages = '<ul><li class="message">' + data.messages.join('</li><li class="message">') + '</li></ul>';
                        }
                        if (typeof data.messages == 'string') {
                            var mc = $('dashlet_messages_' + dashlet_id);
                            mc.innerHTML = data.messages;
                            Effect.Appear(mc);
                            setTimeout(function() { Effect.Fade(mc); }, 5000);
                        }
                    }
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action';
        url += '&custom_plugin_action=getCstmData&plugin=' + this.plugin;
        url += '&dashlet=' + this.dashlet_id + '&use_ajax=1';
        new Ajax.Request(url, opt);
    };

    /**
     * Loads customer contact at specified offset
     */
    this.getContact = function(pos) {
        // prepare ajax options
        if (pos == 0) {
            // not valid value
            return true;
        }

        var dashlet_id = this.dashlet_id;
        var cContainer = $$('#' + this.plugin + '_customer #contacts_data')[0];
        var total = $$('#' + this.plugin + '_customer #contacts_data #total')[0];
        if (typeof total == 'undefined') {
            // not a company, no contacts
            return true;
        }
        if (pos != 'last' && pos > parseInt(total.innerHTML)) {
            // not valid value
            return true;
        }
        Effect.Center('loading');
        Effect.Appear('loading');
        // prepare AJAX parameters
        var params = 'branch=' + $('campCallsBranch').value + '&customer=' +
            $('campCallsCustomer').value + '&pos=' + pos;
        var opt = {
            method: 'post',
            parameters: params,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                eval(t.responseText);
                if (result.errors) {
                    // errors occurred - show them
                    var mc = $('dashlet_messages_' + dashlet_id);
                    mc.innerHTML = result.errors;
                    Effect.Appear(mc);
                    setTimeout(function() {Effect.Fade(mc);}, 5000);
                } else if (result.data) {
                    // show the contact requested
                    var pContainer = cContainer.parentNode;
                    pContainer.removeChild(cContainer);
                    pContainer.innerHTML += result.data;
                    if (pos == 'last' && result.branch) {
                        // we are here when we add new contact
                        // selected branch has to be changed
                        var branches = $('campCallsBranch');
                        for (var i = 0; i < branches.options.length; i++) {
                            if (branches.options[i].value == result.branch) {
                                branches.selectedIndex = i;
                                break;
                            }
                        }
                    }
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action';
        url += '&custom_plugin_action=getContacts&plugin=' + this.plugin;
        url += '&dashlet=' + this.dashlet_id + '&use_ajax=1';
        new Ajax.Request(url, opt);
    };

    /**
     * Adds new call to the document requested
     */
    this.addCall = function(form) {
        // prepare ajax options
        Effect.Center('loading');
        Effect.Appear('loading');
        var dashlet_id = this.dashlet_id;
        var container = $(this.plugin);
        var lContainer = $(this.plugin + '_full_list');
        var lContainerH = $(this.plugin + '_full_list_header');

        var opt = {
            method: 'post',
            parameters: Form.serialize(form),
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                eval(t.responseText);
                if (result.errors) {
                    // show errors - if any
                    var mc = $('dashlet_messages_' + dashlet_id);
                    mc.innerHTML = result.errors;
                    Effect.Appear(mc);
                    setTimeout(function() {Effect.Fade(mc);}, 5000);
                } else {
                    if (result.messages) {
                        // show messages - if any
                        var mc = $('dashlet_messages_' + dashlet_id);
                        mc.innerHTML = result.messages;
                        Effect.Appear(mc);
                        setTimeout(function() {Effect.Fade(mc);}, 5000);
                    }
                    if (result.data) {
                        // reload step 2 content
                        container.innerHTML = result.data;
                    }
                    scripts = $$('#' + container.id + ' script');
                    for (var i in scripts) {
                        ajaxLoadJS(scripts[i]);
                    }
                    if (result.lData) {
                        // reload calls list
                        lContainer.innerHTML = result.lData;
                        lContainer.parentNode.style.display = '';
                        lContainerH.style.display = '';
                        scripts = $$('#' + lContainer.id + ' script');
                        for (i in scripts) {
                            ajaxLoadJS(scripts[i]);
                        }
                    } else {
                        lContainer.parentNode.style.display = 'none';
                        lContainerH.style.display = 'none';
                    }
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action';
        url += '&custom_plugin_action=addCall&plugin=' + this.plugin;
        url += '&dashlet=' + this.dashlet_id + '&use_ajax=1';
        new Ajax.Request(url, opt);
    };
};
