<?php

/**
 * Brainstorm_Tickets plugin custom model class
 *
 * @category Brainstorm_Tickets
 * @package Brainstorm_Tickets
 */
class Brainstorm_Tickets_Custom_Model extends Model {
    /**
     * Name of model
     * @var string
     */
    public $modelName = 'Dashlet';

    /**
     * Name of plugin - same as plugin folder
     * @var string
     */
    public $plugin_name;

    /**
     * Minimal number of characters for starting search from customer
     * autocompleter in dashlet.
     * @var string
     */
    CONST AUTOCOMPLETER_MIN_CHARS = 2;

    /**
     * Reference to Messages object in registry
     * @var Messages
     */
    protected $messages;

    /**
     * Reference to Request object in registry
     * @var Request
     */
    protected $request;

    /**
     * Id of entry autocompleter field, related field names are based on it
     * @var string
     */
    private $ticket_customer_custom_id = 'ticket_customer';

    /**
     * Dashlet plugin constructor
     *
     * @param string $dashlet_settings - dashlets plugin settings
     * @return boolean - result of the operation
     */
    public function __construct($dashlet_settings = '') {

        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        $this->messages = &$this->registry['messages'];
        $this->request = &$this->registry['request'];

        parent::__construct($this->registry);

        // plugin name matches plugin folder name
        $pn = explode(DIRECTORY_SEPARATOR, realpath(dirname(__FILE__)));
        $this->plugin_name = $pn[count($pn)-2];

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        $dashlet = $this->registry['dashlet'] ?:
            // if requested url does not contain dashlet id, get dashlet model
            // (there should be no more than one in the installation)
            Brainstorm_Tickets_Custom_Factory::getDashlet($this->registry);

        // set data from dashlet model into plugin model
        if ($dashlet && !$dashlet->get('deleted_by')) {
            $this->set('dashlet_id', $dashlet->get('id'), true);
        }

        // load documents module files because we need them in most actions
        $lang_files = array(
            PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini',
        );
        $this->loadI18NFiles($lang_files);

        return true;
    }

    /**
     * Parse settings for current plugin and set them as properties to model
     *
     * @param string $dashlet_settings - settings field of dashlet
     */
    public function parseDashletPluginSettings($dashlet_settings) {

        $settings_rows = preg_split('/(\r\n|\n|\r)/', $dashlet_settings);

        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0] == '#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);

            // set them as properties of model in the usual way
            $this->set($key, $value, true);
        }
    }

    /**
     * Prepare data for display in interface (index page) and set it into model
     *
     * @param Dashlet $dashlet - dashlet model that is assigned to viewer
     */
    public function prepareDashletData(Dashlet $dashlet) {

        /** @var User $user */
        $user = &$this->registry['currentUser'];

        $registry = &$this->registry;

        // prepare entry screen autocompleter options
        $search_fields = array('phone', 'gsm');
        $search_fields_i18n = array('name', 'lastname');
        $idx = 0;
        $where = array();
        while ($idx < 3) {
            $alias = 'c' . ($idx ?: '');
            foreach ($search_fields as $f) {
                $where[] = "{$alias}.{$f}";
            }
            $alias = 'ci' . ($idx ?: '');
            foreach ($search_fields_i18n as $f) {
                $where[] = "{$alias}.{$f}";
            }
            $idx++;
        }
        $where = implode(', ', $where);

        $customer_options = array(
            'type' => 'autocompleter',
            'custom_id' => $this->ticket_customer_custom_id,
            'name' => $this->ticket_customer_custom_id,
            'width' => 400,
            'label' => $this->i18n('plugin_client'),
            'autocomplete' => array(
                'type' => 'autocompleters',
                'url' => sprintf(
                    '%s?%s=%s&%s=%s',
                    $_SERVER['PHP_SELF'],
                    $this->registry['module_param'],
                    'autocompleters',
                    'autocompleters',
                    'ajax_select'
                ),
                /** @see Autocompleters::customQuery */
                'plugin_search' => 'customQuery',
                'plugin_params' => array(
                    'sql' => preg_replace('#\s{1,}#', ' ', "
                        SELECT
                          CONCAT(`c`.`id`, '_', IF (`c1`.`id` IS NULL, '', `c1`.`id`), '_', IF (`c2`.`id` IS NULL, '', `c2`.`id`)) AS `id`,
                          `c`.`id` AS `customer_id`,
                          TRIM(CONCAT(`ci`.`name`, ' ', `ci`.`lastname`)) AS `customer_name`,
                          `c1`.`id` AS `branch_id`,
                          IF(`ci1`.`name` != '' OR `ci1`.`lastname` != '', TRIM(CONCAT(`ci1`.`name`, ' ', `ci1`.`lastname`)), '') AS `branch_name`,
                          `c2`.`id` AS `contact_id`,
                          TRIM(CONCAT(`ci2`.`name`, ' ', `ci2`.`lastname`)) AS `contact_name`,
                          /* priority #1 - contact person */
                          IF(0, '', REPLACE(c2.gsm, '|', ' ')) AS gsm2,
                          IF(c2.phone = c2.gsm, '', REPLACE(c2.phone, '|', ' ')) AS phone2,
                          /* priority #2 - normal customer */
                          IF(c.gsm IN (c2.phone, c2.gsm), '', REPLACE(c.gsm, '|', ' ')) AS gsm,
                          IF(c.phone IN (c.gsm, c2.phone, c2.gsm), '', REPLACE(c.phone, '|', ' ')) AS phone,
                          /* priority #3 - non-main branch (only) */
                          IF(c1.is_main OR c1.gsm IN (c.phone, c.gsm, c2.gsm, c2.phone), '', REPLACE(c1.gsm, '|', ' ')) AS gsm1,
                          IF(c1.is_main OR c1.phone IN (c1.gsm, c.phone, c.gsm, c2.gsm, c2.phone), '', REPLACE(c1.phone, '|', ' ')) AS phone1
                        FROM `" . DB_TABLE_CUSTOMERS . "` AS `c`
                        LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci`
                          ON `ci`.`parent_id` = `c`.`id` AND `ci`.`lang` = '{$registry['lang']}'
                        LEFT JOIN `" . DB_TABLE_CUSTOMERS . "` AS `c1`
                          ON `c1`.`subtype` = 'branch' AND `c1`.`parent_customer` = `c`.`id`
                        LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci1`
                          ON `ci1`.`parent_id` = `c1`.`id` AND `ci1`.`lang` = '{$registry['lang']}'
                        LEFT JOIN `" . DB_TABLE_CUSTOMERS . "` AS `c2`
                          ON `c2`.`subtype` = 'contact' AND `c2`.`parent_customer` = `c1`.`id`
                        LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci2`
                          ON `ci2`.`parent_id` = `c2`.`id` AND `ci2`.`lang` = '{$registry['lang']}'
                        WHERE `c`.`active` = '1' AND `c`.`deleted_by` = '0' AND `c`.`subtype` = 'normal'
                        /* exclude inactive data unless related to active data */
                          AND (c1.`id` IS NULL OR `c1`.`active` = '1' AND `c1`.`deleted_by` = '0' OR c2.`id` IS NOT NULL)
                          AND (c2.`id` IS NULL OR `c2`.`active` = '1' AND `c2`.`deleted_by` = '0')
                        " .
                        ($this->get('cust_types_search') ?
                        "  AND `c`.`type` IN ('" . implode("', '", preg_split('#\s*,\s*#', $this->get('cust_types_search'))) . "') " :
                        '') .
                        "  AND (<search_string_parts>)
                        GROUP BY `c`.`id`, `c1`.`id`, `c2`.`id`
                        ORDER BY <TRIM(CONCAT(`customer_name`, ' ', `branch_name`, ' ', `contact_name`))>, `customer_name`
                        LIMIT 10
                    "),
                    'search' => $where,
                ),
                'suggestions' => '<customer_name> <gsm> <phone> <branch_name> <gsm1> <phone1> <contact_name> <gsm2> <phone2>',
                'fill_options' => array(
                    "\${$this->ticket_customer_custom_id} => <id>",
                    "\${$this->ticket_customer_custom_id}_autocomplete => <customer_name>",
                    "\${$this->ticket_customer_custom_id}_oldvalue => <customer_name>",
                ),
                'id_var' => $this->ticket_customer_custom_id,
                'min_chars' => $this->get('autocompleter_min_chars') ?: self::AUTOCOMPLETER_MIN_CHARS,
                //'combobox' => 1,
                'buttons_hide' => 'search',
                'execute_after' => 'Brainstorm_Tickets.setACData',
            ),
            'autocomplete_var_type' => 'basic',
            'required' => 1,
        );
        $dashlet->set('customer_options', $customer_options, true);

        $dashlet->set('data', $this->prepareDashletParams(), true);
    }

    /**
     * Prepares data that should be passed to javascript dashlet object
     *
     * @return array - prepared data
     */
    private function prepareDashletParams() {
        $dashlets_reload = array_values(array_intersect(
            array_map(
                function($a) { return preg_replace('#^dashlet_#', '', $a); },
                array_keys($this->registry['currentUser']->getPersonalSettings('dashlets') ?: array())
            ),
            preg_split('#\s*,\s*#', $this->get('dashlets_reload') ?: '')
        ));
        if ($dashlets_reload) {
            $dashlets = Dashlets::search(
                $this->registry,
                array(
                    'where' => array("d.id IN ('" . implode("', '", $dashlets_reload) . "')"),
                    'sanitize' => true,
                )
            );
            $dashlets_reload = array();
            foreach ($dashlets as $dashlet) {
                $dashlets_reload[] = array(
                    "content_dashlet_{$dashlet->get('id')}",
                    $dashlet->get('module'),
                    $dashlet->get('controller'),
                    $dashlet->get('id'),
                );
            }
        }

        return array(
            'dashlets_reload' => $dashlets_reload,
        );
    }

    /**
     * Loads lightbox screen for ticket management in add/edit mode
     *
     * @return string - json-encoded fetched HTML content and other data
     */
    public function load() {

        // data to assign to viewer
        $data = array(
            'settings' => $this->getAll(),
            'dashlet' => $this->registry['dashlet'] ?: Brainstorm_Tickets_Custom_Factory::getDashlet($this->registry),
        );
        // no dashlet created - set a blank model
        if (!$data['dashlet']) {
            $data['dashlet'] = new Dashlet($this->registry, array('module' => 'plugin', 'controller' => $this->plugin_name));
            $data['dashlet']->sanitize();
        }

        if ($this->request['id']) {
            /** @var Document $document */
            $document = Documents::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        "d.id = '{$this->request['id']}'",
                        "d.type = '{$this->get('doc_type_ticket')}'",
                        "d.active = '1'",
                    ),
                    'model_lang' => $this->request['model_lang'] ?: $this->registry['lang'],
                )
            );
            if ($document) {
                $document->getAttachments();

                if ($document->get('branch')) {
                    $document->set('customer_is_company', true, true);
                }
            }
        } else {
            // set all specified data from request into document
            $document = Documents::buildModel($this->registry);
            $document->set('type', $this->get('doc_type_ticket'), true);

            // parameter holds json-encoded data
            if ($this->request->isRequested("{$this->ticket_customer_custom_id}_data")) {
                $customer_data = json_decode($this->request["{$this->ticket_customer_custom_id}_data"], true) ?: array();
                foreach ($customer_data as $k => $v) {
                    if (strpos($k, '$') === 0) {
                        $this->request->set(substr($k, 1), $v, 'all', true);
                    } elseif ($k == 'search_string' && trim($v) !== '') {
                        // search string holds entered value in autocompleter
                        // field before option selection
                        $search_string = preg_split('#\s+#u', trim($v));
                        $pattern = '#^\d{' . ($this->get('autocompleter_min_chars') ?: self::AUTOCOMPLETER_MIN_CHARS) . ',}$#';
                        foreach ($search_string as $idx => $substr) {
                            if (!preg_match($pattern, $substr)) {
                                unset($search_string[$idx]);
                            }
                        }
                    }
                }
            }

            // customer is selected, prepare related data and set it into model
            if ($this->request[$this->ticket_customer_custom_id]) {
                $customer_ids = explode('_', $this->request[$this->ticket_customer_custom_id]);
                $document->set('customer', $customer_ids[0], true);
                $document->set('customer_name', $this->request["{$this->ticket_customer_custom_id}_autocomplete"], true);
                $document->set('customer_is_company', !empty($customer_ids[1]), true);
                $document->set('branch', !empty($customer_ids[1]) ? $customer_ids[1] : '', true);
                $document->set('contact_person', !empty($customer_ids[2]) ? $customer_ids[2] : '', true);
            }
            if ($document->get('event_id')) {
                $event = $this->getCall($document->get('event_id'));
                if ($event) {
                    // update event call with data from request, if necessary
                    if ($this->updateCall($event) && $event->get('update_call')) {
                        $update_call = true;
                    }
                    /** @var User $user */
                    $user = Users::searchOne(
                        $this->registry,
                        array(
                            'where' => array(
                                "u.id = '{$event->get('added_by')}'",
                                "u.hidden IS NOT NULL",
                            ),
                            'model_lang' => $event->get('model_lang'),
                            'sanitize' => true,
                        )
                    );
                    if ($user) {
                        $document->set('department', $user->get('default_department') ?: strval(PH_DEPARTMENT_FIRST), true);
                        $document->set('assigned_to', $user->get('id'), true);
                        $document->set('assigned_to_name', $user->get('firstname') . ' ' . $user->get('lastname'), true);
                        $this->request->set($this->get('ticket_phone_var'), $event->get('location'), 'all', true);
                    }
                } else {
                    $this->messages->setError('error_plugin_no_event');
                }
            }
            if (!$document->isDefined('employee')) {
                $document->set('employee', $this->registry['currentUser']->get('employee'), true);
                $document->set('employee_name', $this->registry['currentUser']->get('employee_name'), true);
            }
            if (!$document->isDefined('office')) {
                $document->set('office', $this->registry['currentUser']->get('office'), true);
            }
            if (!$document->isDefined('date')) {
                $document->set('date', General::strftime('%Y-%m-%d'), true);
            }
        }

        // search for customer of document
        $customer = false;
        if ($document && $document->get('customer')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array(
                'sanitize' => false,
                'where' => array(
                    'c.id = \'' . $document->get('customer') . '\'',
                    'c.deleted_by IS NOT NULL',
                ),
                'model_lang' => $document->get('model_lang'),
            );
            /** @var Customer $customer */
            $customer = Customers::searchOne($this->registry, $filters);
        }

        // customer not specified or not found, display error message
        if (!$customer) {
            if (!$document) {
                $this->messages->setError($this->i18n('error_plugin_no_document'), '', -1);
            } else {
                $this->messages->setError($this->i18n('error_plugin_no_customer'), '', -1);
            }
        } elseif (!$document->get('id')) {
            // if customer data is missing (for example, when method is called
            // from another functionality), set it from customer model
            if (!$document->isDefined('customer_name')) {
                $document->set('customer_name', $customer->get('full_name'), true);
            }
            if ($customer->get('is_company')) {
                if (!$document->get('branch')) {
                    $document->set('branch', $customer->get('main_branch_id'), true);
                }
            } else {
                $document->set('branch', '', true);
            }
            $document->set('customer_is_company', $customer->get('is_company'), true);

            $reaction_time = $customer->getValueByName($this->get('reaction_time'));
            $working_time = $customer->getValueByName($this->get('working_time'));
        }

        // validate type and check action permissions
        if ($document && $document->get('type') && $document->get('type') == $this->get('doc_type_ticket')) {
            $type = $document->getModelType();
            $action = $document->get('id') ? 'edit' : 'add';
            if (empty($type) || !$type->get('id') || !$document->get('id') && (!$type->get('active') || $type->get('inheritance'))) {
                $this->messages->setError($this->i18n("error_documents_{$action}_failed", array($document->getModelTypeName())), '', -1);
                $this->messages->setError($this->i18n('error_invalid_type'));
            } elseif (!$document->checkPermissions($action)) {
                $this->messages->setError($this->i18n($action == 'add' ? 'error_add_notallowed' : 'error_no_access_to_model'));
            }
        }

        if ($this->messages->getErrors()) {
            return $this->prepareErrorResponse(
                $this->messages->getErrors(),
                true
            );
        }

        // in add mode
        if (!$document->get('id')) {
            // search for default trademark
            foreach ($customer->getTrademarks() as $t) {
                if (!empty($t['is_default'])) {
                    $document->set('trademark', $t['id'], true);
                    break;
                }
            }

            // set default property values from type
            $document->set('direction', $type->get('direction'), true);
            if (!$document->get('name') && $type->get('default_name')) {
                $document->set('name', $type->get('default_name'), true);
            }
            if (!$document->get('department')) {
                $document->set('department', $type->getDefaultDepartment(), true);
            }
            $document->set('group', $type->getDefaultGroup(), true);

            // search for phone match in autocompleter-entered text
            if (!empty($search_string)) {
                // collect all phone numbers to search into
                $haystack_array = array_merge($customer->get('phone') ?: array(), $customer->get('gsm') ?: array());
                if ($document->get('branch')) {
                    $branch = Customers_Branches::searchOne(
                        $this->registry,
                        array(
                            'where' => array(
                                'c.id = \'' . $document->get('branch') . '\'',
                            )
                        )
                    );
                    if ($branch && !$branch->get('is_main')) {
                        $haystack_array = array_merge($haystack_array, $branch->get('phone') ?: array(), $branch->get('gsm') ?: array());
                    }
                    if ($document->get('contact_person')) {
                        $contact_person = Customers_Contactpersons::searchOne(
                            $this->registry,
                            array(
                                'where' => array(
                                    'c.id = \'' . $document->get('contact_person') . '\'',
                                )
                            )
                        );
                        if ($contact_person) {
                            $haystack_array = array_merge($haystack_array, $contact_person->get('phone') ?: array(), $contact_person->get('gsm') ?: array());
                        }
                    }
                }
                $haystack_array = array_filter(array_unique($haystack_array));

                $matches = array();
                foreach ($haystack_array as $haystack) {
                    foreach ($search_string as $needle) {
                        if (strpos($haystack, $needle) !== false) {
                            // in case of first met full match, ignore partial matches and do not search further
                            if ($haystack === $needle || $haystack === $customer_data['search_string']) {
                                $matches = array($haystack);
                                break 2;
                            }
                            $matches[] = $haystack;
                            continue 2;
                        }
                    }
                }
                if (!empty($matches)) {
                    $this->request->set(
                        $this->get('ticket_phone_var'),
                        implode(', ', $matches),
                        'all',
                        true
                    );
                }
            }
        } else {
            // in edit mode

            // prepare assigned user as owner
            $assignments_owner = $document->isDefined('assignments_owner') ? $document->get('assignments_owner') : $document->getAssignments('owner');
            $assigned_to = '';
            if ($assignments_owner) {
                foreach ($assignments_owner as $user_id => $assigned_info) {
                    if (!$assigned_info['is_portal'] && (!$assigned_to || $assignments_owner[$assigned_to]['assigned'] < $assigned_info['assigned'])) {
                        $assigned_to = $user_id;
                    }
                }
            }
            $document->set('assigned_to', $assigned_to, true);
            $document->set('assigned_to_name', $assigned_to ? $assignments_owner[$assigned_to]['assigned_to_name'] : '', true);

            // prepare menu for actions that can be done with model from lightbox
            $action_defs = array(/* 'relatives',  */'communications', 'comments', 'emails', 'addtimesheet', );

            $routing_params = array(
                'module' => 'documents',
                'controller' => 'documents',
                'action' => 'edit',
                'action_param' => 'documents',
            );
            $original_routing_params = array();
            foreach (array_keys($routing_params) as $key) {
                $original_routing_params[$key] = $this->registry[$key];
                $this->registry->set($key, $routing_params[$key], true);
            }

            $controller = new Documents_Controller($this->registry);
            $controller->model = $document;
            $controller->getActions($action_defs);

            foreach ($original_routing_params as $key => $value) {
                $this->registry->set($key, $value, true);
            }

            $data['available_actions_right'] = $this->registry['available_actions_right'];
            unset($this->registry['available_actions_right']);

            foreach ($data['available_actions_right'] as $action_name => $action) {
                if ($action_name == 'communications') {
                    $this->loadI18NFiles(array(PH_MODULES_DIR . 'communications/i18n/' . $this->registry['lang'] . '/communications.ini'));
                    unset($data['available_actions_right'][$action_name]);
                    if (!empty($action['options'])) {
                        foreach ($action['options'] as $option_action_name => $option_action) {
                            $type_record = General::plural2singular($option_action_name);
                            $url = sprintf(
                                '%s?%s=%s&%s=%s&%s=%s&%s=%s&%s=%s&%s=%s&%s=%s&inline_add=1',
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'],
                                'communications',
                                'communications',
                                'ajax_load_communication_add_panel',
                                'type_record',
                                $type_record,
                                'model_id',
                                $controller->model->get('id'),
                                'communication_type',
                                $option_action_name,
                                'module',
                                'documents',
                                'action',
                                'add'
                            );
                            $option_action_name .= '_add';
                            if ($controller->model->checkPermissions($option_action_name)) {
                                $option_action_title = $this->i18n($option_action_name == 'emails_add' ? 'communications_add_email' : 'add_comment');
                                $data['available_actions_right'][$option_action_name] = array(
                                    'name' => $option_action_name,
                                    'options' => null,
                                    'label' => $this->i18n('communications_' . $option_action_name),
                                    'onclick' => "Brainstorm_Tickets.loadInlineForm({event: event, url: '{$url}', title: '{$option_action_title}'});",
                                ) + $option_action + $action;
                            }
                        }
                    }
                } elseif ($action_name == 'addtimesheet') {
                    $url = sprintf(
                        '%s?%s=%s&%s=%s&%s=%s&%s=%d&inline_add=1',
                        $_SERVER['PHP_SELF'],
                        $this->registry['module_param'],
                        'tasks',
                        $this->registry['controller_param'],
                        'timesheets',
                        'timesheets',
                        'ajax_add',
                        'task_id',
                        Documents::getSystemTask($this->registry, $controller->model->get('id'))
                    );
                    $option_action_title = $this->i18n('documents_add_timesheet');
                    $data['available_actions_right'][$action_name] = array(
                        'label' => $option_action_title,
                        'img' => 'timesheets',
                        'onclick' => "Brainstorm_Tickets.loadInlineForm({event: event, url: '{$url}', title: '{$option_action_title}'});",
                    ) + $action;
                }
            }
            // end prepare actions

            // parameters for transformation buttons
            if ($document->checkPermissions('transform')) {
                $document->set('transform_optgroups', $document->getTransformOptgroups('button'), true);
            }
        }

        $data['customer'] = $customer->sanitize();

        // prepare data for contacts
        $data = array_merge($data, $this->getContacts($document->get('customer'), $document->get('branch'), $document->get('contact_person')));

        // prepare customer tags groups, if specified
        if ($this->get('cust_tags_groups')) {
            $data['cust_tags_groups'] = preg_split('#\s*,\s*#', $this->get('cust_tags_groups'));
        }

        $open_tickets_filters = array(
            'where' => array(
                "d.type = '{$this->get('doc_type_ticket')}' AND",
                "d.customer = '{$customer->get('id')}' AND",
                "d.active = '1' AND",
                "d.status = 'opened' AND",
            ),
            'sort' => array(
                "d.id DESC",
            ),
        );
        $data['num_open_tickets'] = count(Documents::getIds($this->registry, $open_tickets_filters));

        $sessionPrefix = 'search_open_tickets_';
        Documents::saveSearchParams($this->registry, $open_tickets_filters, $sessionPrefix);
        $sessionParam = strtolower($sessionPrefix . Documents::$modelName);
        $session_filters = $this->registry['session'][$sessionParam];
        if (!empty($session_filters['search_fields'])) {
            foreach ($session_filters['search_fields'] as $idx => $value) {
                if ($value == 'd.customer') {
                    $session_filters['values_autocomplete'] = array(
                        $idx => sprintf(
                            "[%s] %s",
                            $customer->get('code'),
                            trim($customer->get('name') . ' ' . $customer->get('lastname'))
                        ),
                    );
                    break;
                }
            }
        }
        $this->registry['session']->remove($sessionParam);
        $data['search_open_tickets_url'] = sprintf(
            '%s?%s=documents&documents=search&search_document=1&search_module=documents&search_controller=&%s',
            $_SERVER['PHP_SELF'],
            $this->registry['module_param'],
            http_build_query($session_filters)
        );

        /**
         * @var string $custom_id
         */
        $custom_id = 'assigned_to';
        /**
         * autocomplete parameters for modified assignment for owner of one user only
         */
        $data['assigned_autocomplete'] = array(
            'type' => 'autocompleters',
            'url' => sprintf(
                '%s?%s=%s&%s=%s',
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'],
                'autocompleters',
                'autocompleters',
                'ajax_select'
            ),
            /** @see Autocompleters::customQuery */
            'plugin_search' => 'customQuery',
            'plugin_params' => array(
                'sql' => preg_replace('#\s{1,}#', ' ', "
                    SELECT
                      CONCAT(IF(ui18n.firstname IS NOT NULL, ui18n.firstname, ''), ' ', IF(ui18n.lastname IS NOT NULL, ui18n.lastname, '')) AS name,
                      u.id, u.active, u.is_portal
                    FROM " . DB_TABLE_USERS . " AS u
                    LEFT JOIN " . DB_TABLE_USERS_I18N . " AS ui18n
                      ON u.id = ui18n.parent_id AND ui18n.lang = '{$this->registry['lang']}'
                    JOIN " . DB_TABLE_USERS_DEPARTMENTS . " AS uud
                      ON uud.parent_id = u.id AND uud.department_id IN (<selected_department>)
                    WHERE u.active=1 AND u.deleted_by=0 AND u.hidden=0 AND u.is_portal=0
                      AND (<search_string_parts>)
                    GROUP BY u.id
                    ORDER BY name
                "),
                'search' => 'ui18n.firstname, ui18n.lastname',
                'selected_department' => '$department',
            ),
            'suggestions' => '<name>',
            'fill_options' => array(
                "\${$custom_id} => <id>",
                "\${$custom_id}_autocomplete => <name>",
                "\${$custom_id}_oldvalue => <name>",
            ),
            'var_id' => $custom_id,
            'combobox' => 1,
            'combobox_mode' => 'empty',
            'buttons_hide' => 'search',
            'buttons' => 'clear',
            'execute_after' => '',
            'cstm_model' => 'User',
        );


        if ($document->get('customer_is_company')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branches = array(
                'sanitize' => true,
                'model_lang' => $document->get('model_lang'),
                'where' => array(
                    'c.parent_customer = \'' . $document->get('customer') . '\'',
                    'c.subtype = \'branch\'',
                    'c.active = 1'
                ),
                'sort' => array(
                    'c.is_main DESC',
                    'ci18n.name ASC',
                    'c.id DESC'
                )
            );
            $data['customer_branches'] = Customers_Branches::search($this->registry, $filters_branches);
            array_walk($data['customer_branches'], array($this, 'modelsToOptions'));

            if ($document->get('branch')) {
                require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                $filters_contacts = array(
                    'sanitize' => true,
                    'model_lang' => $document->get('model_lang'),
                    'where' => array(
                        'c.parent_customer = \'' . $document->get('branch') . '\'',
                        'c.subtype = \'contact\'',
                        'c.active = 1'
                    ),
                    'sort' => array(
                        'c.is_main DESC',
                        'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC',
                        'c.id DESC'
                    )
                );
                $data['contact_persons'] = Customers_Contactpersons::search($this->registry, $filters_contacts);
                array_walk($data['contact_persons'], array($this, 'modelsToOptions'));
            }
        }

        // get additional required fields
        $fields = $this->registry['config']->getParamAsArray($document->getModule(), 'validate_' . $document->get('type'));
        $data['required_fields'] = array_filter($fields, function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; });

        $data['groups'] = Groups::getTree($this->registry);

        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $data['departments'] = Departments::getTree($this->registry);
        array_walk($data['departments'], function(&$a) {
            $a->set('name', preg_replace('!^!m',str_repeat('-', $a->get('level')), $a->get('name')), true);
        });
        array_walk($data['departments'], array($this, 'modelsToOptions'));

        $additional_vars_add_only = array(
            $this->get('ticket_email_var'),
            $this->get('ticket_phone_var'),
            $this->get('ticket_information_security_var'),
        );
        //TODO do we need real time update of datetimes?
        $js_methods = array(
            $this->get('ticket_reaction_time_var') => array('onchange' => ""),
            $this->get('ticket_solution_time_var') => array('onchange' => ""),
            //$this->get('ticket_priority_var') => array('onchange' => ""),
        );

        $routing_params = array(
            'module' => 'documents',
            'controller' => 'documents',
            'action' => $document->get('id') ? 'edit' : 'add',
            'action_param' => 'documents',
        );
        $original_routing_params = array();
        foreach (array_keys($routing_params) as $key) {
            $original_routing_params[$key] = $this->registry[$key];
            $this->registry->set($key, $routing_params[$key], true);
        }

        $document->getVars();

        foreach ($original_routing_params as $key => $value) {
            $this->registry->set($key, $value, true);
        }
        // process additional vars
        foreach (array('vars', 'plain_vars', 'config_vars', 'table_vars', 'group_vars',) as $vars_key) {
            if ($document->get($vars_key)) {
                $vars_array = $document->get($vars_key) ?: array();
                foreach ($vars_array as $idx => $var) {
                    if (in_array($var['type'], array('group', 'table', 'config', ))) {
                        // change styles
                        $vars_array[$idx]['source'] = (!empty($var['source']) ? $var['source'] . "\n" : '') . "custom_class := t_layout_table t_borderless labelbox\nborderless := 1";
                        // unset width
                        $vars_array[$idx]['width'] = '';
                    } elseif ($var['type'] == 'text' && !$var['width'] && !($var['grouping'] || $var['table'] || $var['configurator'])) {
                        // set width
                        $vars_array[$idx]['width'] = '200';
                    } elseif ((!empty($var['grouping']) || !empty($var['table'])) && $var['type'] != 'autocompleter') {
                        // unset width
                        $vars_array[$idx]['width'] = '';
                    }
                    if ($document->get('id') && in_array($var['name'], $additional_vars_add_only)) {
                        $vars_array[$idx]['readonly'] = 1;
                        if (in_array($var['name'], array($this->get('ticket_email_var'), $this->get('ticket_phone_var'),))) {
                            $vars_array[$idx]['hidden'] = 1;
                        }
                    }
                    if (array_key_exists($var['name'], $js_methods)) {
                        if (!array_key_exists('js_methods', $var)) {
                            $var['js_methods'] = array();
                        }
                        foreach ($js_methods[$var['name']] as $attr => $method) {
                            if (!array_key_exists($attr, $var['js_methods'])) {
                                $var['js_methods'][$attr] = '';
                            }
                            $var['js_methods'][$attr] .= $method;
                        }
                        $vars_array[$idx] = $var;
                    }
                }
                $document->set($vars_key, $vars_array, true);
            }
        }
        if (!$document->get('id')) {
            // get the times from the customer file
            if (!empty($reaction_time) || !empty($working_time)) {
                // set reaction and solution times
                $vars = $document->get('vars');
                foreach ($vars as $idx => $var) {
                    if ($var['name'] == $this->get('ticket_reaction_time_var')) {
                        $vars[$idx]['value'] = $reaction_time;
                    } elseif ($var['name'] == $this->get('ticket_solution_time_var')) {
                        $vars[$idx]['value'] = $working_time;
                    }
                }
                $document->set('vars', $vars, true);
            }

            $this->setTicketTerms($document);
        }
        $document->getAssocVars();
        $document->getLayoutVars();
        $document->getLayoutsDetails();
        $vars_layouts = $document->isDefined('vars') ? $document->get('vars') : array();
        $additional_layouts_add_only = array();
        // get custom layouts
        foreach ($vars_layouts as $layout_id => $layout_vars) {
            $document->getLayoutsDetails($layout_id);

            $all_readonly = true;
            foreach ($layout_vars as $lv) {
                if (!is_array($lv['readonly']) && !$lv['readonly'] || is_array($lv['readonly']) && $lv['readonly'] !== array_filter($lv['readonly'])) {
                    $all_readonly = false;
                    break;
                }
            }
            if ($all_readonly) {
                $additional_layouts_add_only[] = $layout_id;
            }
        }

        // visible basic layouts
        $layouts_details = $document->get('layouts_details');
        $layouts_details = array_merge(
            array('status' => $document->getLayoutsDetails('status')),
            $layouts_details,
            array('attachments' => array_merge($document->getLayoutsDetails('attachments'), array('name' => $this->i18n('attachments'))))
        );
        $basic_layouts_editable = array('customer', 'department', 'name', 'description', 'full_num', );

        foreach ($layouts_details as $layout_name => &$layout) {
            if ($this->registry['currentUser']->get('is_portal') && $layout_name == 'department') {
                $layout['view'] = $layout['edit'] = 0;
                continue;
            }
            if ($layout['system'] && in_array($layout_name, $basic_layouts_editable)) {
                $layout['view'] = $layout['edit'] = 1;
            } else {
                if ($layout['edit'] && $document->get('id') && in_array($layout['id'], $additional_layouts_add_only)) {
                    $layout['edit'] = 0;
                }
            }
        }
        unset($layout);
        $document->set('layouts_details', $layouts_details, true);
        $document->sanitize();

        $data['document'] = $document;

        $data['dashlet']->set('data', $this->prepareDashletParams(), true);

        return json_encode(array(
            'messages' => $this->prepareMessages($this->messages->getMessages(), 'message', true),
            'warnings' => $this->prepareMessages($this->messages->getWarnings(), 'warning', true),
            'content' => $this->fetch('_screen.html', $data),
            'title' => $this->i18n($document->get('id') ? 'plugin_manage_ticket_title' : 'plugin_add_ticket_title'),
            'reload_origin_container' => !empty($update_call) ? 1 : '',
        ));
    }

    /**
     * Changes status of ticket
     *
     * @return string - JSON-encoded result of operation (messages, errors, other)
     */
    public function setStatus() {

        $request = $this->request;

        $request->set('id', $request->get('setstatus'), 'all', true);
        $document = Documents::buildModel($this->registry);
        $filters = array(
            'where' => array('d.id = ' . $document->get('id')),
            'model_lang' => $request->get('model_lang'),
        );
        $old_document = Documents::searchOne($this->registry, $filters);

        if ($old_document) {
            $automations_controller = new Automations_Controller($this->registry);
            $document->set('type', $old_document->get('type'), true);
            $automations_controller->executeActionAutomations($old_document, $document, 'setstatus', true);
        }

        if (!$old_document) {
            // show error no such record
            $this->messages->setError($this->i18n('error_no_such_document'));
        } elseif (!$this->messages->getErrors() && $document->setStatus()) {
            //show message 'message_documents_status_success'
            $this->messages->setMessage($this->i18n('message_documents_status_success', array($old_document->getModelTypeName())), '', -2);

            $comment = '';
            if ($request->get('comment')) {
                require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                $comment = Comments::buildModel($this->registry);

                $comment->set('content', $request->get('comment'), true);
                $comment->set('subject', $this->i18n('documents_status_change_comment'), true);
                $comment->set('model', 'Document', true);
                $comment->set('model_id', $request->get('id'), true);
                $comment->set('is_portal', ($request->get('is_portal') ? '1' : '0'), true);
                $comment->set('skip_send_email', true, true);
                $comment->unsetProperty('id', true);

                if ($comment->save()) {
                    $comment->slashesStrip();
                    //show corresponding message
                    $this->messages->setMessage($this->i18n('message_documents_comments_add_success'), '', -1);
                } else {
                    //some error occurred
                    //show corresponding error(s)
                    $this->messages->setError($this->i18n('error_comments_add_failed'), '', -1);
                }
            }

            //if the status of the document is 'closed', the system task for it is set to 'finished'
            if ($document->get('status') == 'closed') {
                $system_task_id = Documents::getSystemTask($this->registry, $document->get('id'));
                if ($system_task_id) {
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
                    $task = Tasks::searchOne(
                        $this->registry,
                        array(
                            'where' => array(
                                't.id = ' . $system_task_id,
                                't.type = ' . PH_TASK_SYSTEM_TYPE
                            )
                        )
                    );

                    if ($task) {
                        $task->set('status', 'finished', true);
                        $task->setStatus();
                    }
                }
            }

            $new_document = Documents::searchOne($this->registry, $filters);

            $audit_parent = Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $document,
                    'action_type' => 'status',
                    'new_model' => $new_document,
                    'old_model' => $old_document
                ));

            if ($comment && $comment->get('id')) {
                $comment->saveHistory($new_document);
            }

            //send notification
            $controller = new Documents_Controller($this->registry);
            $new_document->set('comment', $comment, true);
            $controller->sendStatusNotification($new_document, $audit_parent);

            // perform automations maybe?

            // prepare status cell
            $viewer = new Viewer($this->registry);
            $viewer->setFrameset(PH_MODULES_DIR . 'outlooks/templates/td/document_status.html');
            $data = array(
                'single' => $new_document,
                'document_status' => sprintf(
                    '%s%s',
                    $this->i18n("help_documents_status_{$new_document->get('status')}"),
                    ($new_document->get('substatus') ?
                    sprintf(
                        '<br />%s%s',
                        $this->i18n('help_documents_substatus'),
                        $new_document->get('substatus_name')
                    ) :
                    '')
                ),
            );
            foreach ($data as $k => $v) {
                $viewer->data[$k] = $v;
            }
            $viewer->loadCustomI18NFiles($this->registry['translater']->i18nFiles['custom']);
            $status_cell_content = $viewer->fetch();

            // perform action automations
            $controller->executeActionAutomations($old_document, $new_document, 'setstatus');

        } else {
            // some error occurred
            $this->messages->setError($this->i18n('error_documents_status_failed', array($old_document->getModelTypeName())), '', -1);
        }

        $assigned_id = '';
        $assigned_name = '';
        $reaction_time = '';
        $working_time = '';
        $last_resume_date = '';
        if (!empty($new_document)) {
            $owners = $new_document->getAssignments('owner');
            if (!empty($new_document) && $new_document->getAssignments('owner')) {
                $owners = $new_document->getAssignments('owner');
                $current_owner = reset($owners);
                $assigned_id = $current_owner['assigned_to'];
                $assigned_name = $current_owner['assigned_to_name'];
            }

            $reaction_time = $new_document->getValueByName($this->get('ticket_reaction_time_calculated'));
            $working_time = $new_document->getValueByName($this->get('ticket_working_time_calculated'));
            $last_resume_date = $new_document->getValueByName($this->get('ticket_last_resume_date'));
        }

        $result = array(
            'messages' => $this->prepareMessages($this->messages->getMessages(), 'message', true),
            'errors' => $this->prepareMessages($this->messages->getErrors(), 'error', true),
            'status_cell_content' => !empty($status_cell_content) ? $status_cell_content : '',
            'employee_id' => $assigned_id,
            'employee_name' => $assigned_name,
            'reaction_time' => $reaction_time,
            'working_time' => $working_time,
            'last_resume_date' => $last_resume_date
        );

        if ($request->get('history_total') && empty($result['errors']) && !empty($new_document)) {
            list(, $result['history_total']) = History::getData(
                $this->registry,
                array(
                    'model' => $new_document,
                    'history_activity' => 1,
                    'limit' => 1,
                )
            );
        }

        // make sure to clear messages/errors from session
        $this->messages->removeFromSession($this->registry);

        return json_encode($result);
    }

    /**
     * Saves ticket
     *
     * @return string - json-encoded response or javascript to be executed
     */
    public function save() {

        /** @var Document $document */
        $document = Documents::buildModel($this->registry);
        $action = $document->get('id') ? 'edit' : 'add';

        // custom validation + preparation before calling save method
        if ($document->get('type') && $document->get('type') == $this->get('doc_type_ticket')) {
            // search for type model
            $type = $document->getModelType();
            if ($type && $type->get('name')) {
                $document->set('type_name', $type->get('name'), true);
            }
        }

        // invalid type
        if (empty($type) || !$type->get('id') || !$document->get('id') && (!$type->get('active') || $type->get('inheritance'))) {
            $this->messages->setError($this->i18n("error_documents_{$action}_failed", array($document->getModelTypeName())), '', -1);
            $this->messages->setError($this->i18n('error_invalid_type'));
        } elseif ($action == 'edit') {
            // search for old model
            $old_document = Documents::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        "d.id = '{$document->get('id')}'",
                    ),
                    'model_lang' => $document->get('model_lang'),
                )
            );
            if (!$old_document) {
                $this->messages->setError($this->i18n('error_no_such_document'));
            } else {
                // custom preparations
                $old_props = array(
                    'status', 'substatus', 'substatus_name',
                    'generate_system_task', 'type_name', 'direction',
                    'added', 'added_by', 'user_permissions', 'requires_completed_minitasks',
                );
                foreach ($old_props as $prop) {
                    $document->set($prop, $old_document->get($prop), true);
                }
                if (!$document->checkPermissions($action)) {
                    $this->messages->setError($this->i18n('error_no_access_to_model'));
                }
            }
        } else {
            if (!$document->checkPermissions($action)) {
                $this->messages->setError($this->i18n('error_add_notallowed'));
            } else {
                // prepare old model
                // custom preparations
                $document->set('generate_system_task', $type->get('generate_system_task'), true);
                $document->set('direction', $type->get('direction'), true);

                // build a blank model, just set the type to get the additional variables
                $old_document = new Document($this->registry, array('type' => $document->get('type')));
            }
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST' && empty($_POST) && empty($_FILES)) {
            // $_FILES and $_POST are empty because file size exceeds max allowed size for application/server
            $this->messages->setError($this->i18n(
                'error_file_greater_filesize',
                array(General::convertFileSize(General::convertBytes(ini_get('upload_max_filesize'))))
            ));
        }

        // run the before action automations
        $automations_controller = new Automations_Controller($this->registry);
        if (!isset($old_document)) {
            $old_document = new Document($this->registry, array('type' => $document->get('type')));
        }

        $automations_controller->executeActionAutomations($old_document, $document, $action, true);

        if (!$this->messages->getErrors()) {
            // get additional vars and values:
            // get old vars before post
            $this->registry->set('get_old_vars', true, true);
            $old_document->getVars();
            $old_document->sanitize();

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $document->unsetVars();
            $document->getVars();
            // calculate terms
            $this->setTicketTerms($document, $document->get($this->get('ticket_priority_var')));
        }
        // end custom validation + preparation

        if (!$this->messages->getErrors() && $document->save()) {
            // history
            $new_document = Documents::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        "d.id = '{$document->get('id')}'",
                    ),
                    'model_lang' => $document->get('model_lang'),
                )
            );
            $this->registry->set('get_old_vars', true, true);
            $new_document->getVars();

            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $document,
                    'action_type' => $action,
                    'new_model' => $new_document,
                    'old_model' => $old_document,
                ));

            $new_document->unsetVars();

            // load the lang files for assignments
            $this->registry['translater']->loadFile(
                FilesLib::readDir(
                    PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/',
                    false, 'files_only', 'ini', true
                ),
                'module'
            );

            // assignments + history
            $new_assignments = array_filter(array($document->get('assigned_to')));
            if ($action == 'add') {
                if ($new_assignments) {
                    $new_document->set('assignments_owner', $new_assignments, true);
                }
                $new_document->defaultAssign();
            } else {
                $assignments_owner = $new_document->getAssignments('owner');
                $old_assignments = array_keys($assignments_owner);

                // check if reassignment is needed
                if (array_diff($old_assignments, $new_assignments) || array_diff($new_assignments, $old_assignments)) {
                    $old_document = clone $new_document;
                    $this->request->set('a_type', 'owner', 'all', true);
                    $new_document->set('assignments_owner', $new_assignments, true);

                    if ($new_document->assign()) {
                        //$this->messages->setMessage($this->i18n('message_model_assign_success'));

                        $new_document->unsetProperty('assignments_owner', true);
                        $new_document->getAssignments('owner');
                        Documents_History::saveData(
                            $this->registry,
                            array(
                                'model' => $new_document,
                                'action_type' => 'assign',
                                'new_model' => $new_document,
                                'old_model' => $old_document,
                            ));
                    } else {
                        // non-fatal error, do not fail action
                        $this->messages->setWarning($this->i18n('error_model_not_assigned'));
                    }
                    $this->request->remove('a_type');
                }
            }

            // attachments + history
            if (!empty($_FILES) && !empty($_FILES['attachments'])) {

                $old_document = clone $new_document;
                $old_document->getAttachments();

                require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                require_once 'transliterate.class.php';

                // added files
                $added_files = $success_added_files = $erred_added_files = array();
                $additional_files = $_FILES['attachments'];

                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'      => $additional_files['name'][$idx],
                            'type'      => $additional_files['type'][$idx],
                            'tmp_name'  => $additional_files['tmp_name'][$idx],
                            'error'     => $additional_files['error'][$idx],
                            'size'      => $additional_files['size'][$idx]
                        );
                    } else {
                        $file = array();
                    }
                    $params = array(
                        'name'          => $additional_files['name'][$idx],
                        'description'   => '',
                        'revision'      => '',
                        'permission'    => 'all'
                    );

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $new_document)) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setWarning($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type)/* , 'add_attachment_' . ($idx+1) */);

                            //explain the failed upload with more details
                            foreach (FilesLib::$_errors as $err) {
                                $this->registry['messages']->setWarning($err);
                            }
                            FilesLib::$_errors = array();
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }

                    $added_files[$idx] = $params;
                }

                if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {

                    $new_document->getAttachments();

                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $new_document,
                            'action_type' => 'add_attachments',
                            'new_model' => $new_document,
                            'old_model' => $old_document,
                        ));

                    $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                } elseif ($added_files && !empty($erred_added_files)) {
                    $this->registry['messages']->setWarning($this->i18n('error_attachments_all'), '', -1);
                }
            }

            // removed files + history
            if ($this->request['deleteid_attachments'] && array_filter($this->request['deleteid_attachments'])) {

                $old_document = clone $new_document;
                $old_document->getAttachments();

                require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                $remove_ids = array_filter($this->request['deleteid_attachments']);

                if (Files::delete($this->registry, $remove_ids)) {

                    $new_document->getAttachments();

                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $new_document,
                            'action_type' => 'del_attachments',
                            'new_model' => $new_document,
                            'old_model' => $old_document
                        ));

                    $this->registry['messages']->setMessage($this->i18n('message_documents_file_deleted_success'));
                } else {
                    $this->registry['messages']->setWarning($this->i18n('error_documents_file_deleted_failed'));
                }
            }

            if ($action == 'add') {
                $this->request->set('id', $document->get('id'), 'all', true);
            }
            $controller = new Documents_Controller($this->registry);
            $controller->executeActionAutomations($old_document, $new_document, $action);

            // show corresponding message
            $this->messages->setMessage($this->i18n("message_documents_{$action}_success", array($document->getModelTypeName())), '', -1);

        } else {
            $this->messages->setError($this->i18n("error_documents_{$action}_failed", array($document->getModelTypeName())), '', -1);
        }

        $response = !$this->messages->getErrors() ?
            // on success reload lightbox in edit mode or close lightbox
            (
                $this->request['close'] ?
                json_encode(array(
                    'messages' => $this->prepareMessages($this->messages->getMessages(), 'message', true),
                    'warnings' => $this->prepareMessages($this->messages->getWarnings(), 'warning', true),
                    'close' => 1,
                )):
                $this->load()
            ) :
            // on failure return error messages
            $this->prepareErrorResponse(
                $this->messages->getErrors(),
                true
            );
        header('Content-Type: application/json');

        return $response;
    }

    /**
     * Get customer contacts
     *
     * @param int $customer - customer id (when method is called internally)
     * @param int $branch - customer branch (when method is called internally)
     * @param int $contact_person - customer contact person (when method is called internally)
     * @return mixed
     */
    public function getContacts($customer = null, $branch = null, $contact_person = null) {
        $contact = false;
        $count_contacts = 0;
        $pos = $this->request['pos'] ?: 1;

        require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

        // get contact persons count and the first contact person ordered by name
        if ($pos == 'last') {
            // last added/modified contact has been requested - just get it
            // we are here after contact add/edit from lightbox
            $filters = array(
                'where' => array(
                    "pc.id = '{$this->request['customer']}'",
                ),
                'sort' => array(
                    'c.modified DESC',
                ),
                'sanitize' => true,
                'limit' => '0, 1',
            );
            $contact = Customers_Contactpersons::search($this->registry, $filters);

            if (!empty($contact)) {
                // search for contacts only within branch of contact
                $filters = array(
                    'where' => array('c.parent_customer = ' . $contact[0]->get('parent_customer')),
                    'sort' => array('c.is_main DESC', 'ci18n.name', 'ci18n.lastname'),
                    'sanitize' => true,
                );
                //get all contacts IDs
                $count_contacts = Customers_Contactpersons::getIds($this->registry, $filters);
                foreach ($count_contacts as $pos => $c) {
                    // redefine contact position
                    if ($c == $contact[0]->get('id')) {
                        $pos++;
                        break;
                    }
                }
                //get contacts count
                $count_contacts = count($count_contacts);
            }
        } else {
            // define filters to get contacts by customer and optionally by branch as well
            $filters = array(
                'where' => array(),
                'sort' => array(
                    'c.is_main DESC',
                    'ci18n.name',
                    'ci18n.lastname',
                ),
                'sanitize' => true,
            );
            if ($customer) {
                $filters['where'][] = "pc.id = '{$customer}'";
                $filters['where'][] = "pc.is_company = '1'";
                if ($branch) {
                    $filters['where'][] = "bn.id = '{$branch}'";
                }
            } elseif ($this->request->get('customer')) {
                $filters['where'][] = "pc.id = '{$this->request->get('customer')}'";
                $filters['where'][] = "pc.is_company = '1'";
                if ($this->request->get('branch')) {
                    $filters['where'][] = "bn.id = '{$this->request->get('branch')}'";
                }
            } elseif ($this->request->get('branch')) {
                $filters['where'][] = "bn.id = '{$this->request->get('branch')}'";
            } else {
                $filters['where'][] = '0';
            }

            // get contacts count
            $count_contacts = Customers_Contactpersons::getIds($this->registry, $filters);

            // if we don't have the position but we know which contact person to select
            if ($contact_person && array_search($contact_person, $count_contacts) !== false) {
                $pos = array_search($contact_person, $count_contacts) + 1;
            }
            // positions in interface are 1-based so reduce offset by one
            $filters['limit'] = ($pos-1) . ', 1';

            // get requested contact offset
            $contact = Customers_Contactpersons::search($this->registry, $filters);

            $count_contacts = count($count_contacts);
        }

        if (!$count_contacts) {
            $pos = 0;
        }
        $contact = $contact ? array_shift($contact) : false;
        $branch = $contact ? $contact->get('parent_customer') : false;

        $data = array(
            'contact' => $contact,
            'count_contacts' => $count_contacts,
            'current' => $pos,
            'branch' => $branch,
        );

        // when method is called internally, the prepared data is returned and
        // no fetching of content is performed
        if ($customer) {
            return $data;
        }

        $data = array_merge(
            $data,
            array(
                'customer' => $this->request['customer'],
                'customer_is_company' => intval($contact || $this->request->isRequested('branch')),
                'branch' => $branch ?: $this->request['branch'],
            ));

        return json_encode(array(
            'content' => $this->fetch(
                '_contacts_data.html',
                $data
            ),
            'branch' => $data['branch'],
        ));
    }

    /**
     * Displays screen with actions to perform with call event
     *
     * @return string - json-encoded fetched HTML content and other data
     */
    public function manageCall() {

        // data to assign to viewer
        $data = array(
            'settings' => $this->getAll(),
            'dashlet' => $this->registry['dashlet'] ?: Brainstorm_Tickets_Custom_Factory::getDashlet($this->registry),
        );
        // no dashlet created - set a blank model
        if (!$data['dashlet']) {
            $data['dashlet'] = new Dashlet($this->registry, array('module' => 'plugin', 'controller' => $this->plugin_name));
            $data['dashlet']->sanitize();
        }

        $event = $this->getCall($this->request['event_id']);

        if (empty($event)) {
            $this->messages->setError($this->i18n('error_plugin_no_event'), '', -1);
        } else {
            // search for customer of event
            $customer = false;
            if ($event->get('customer')) {
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $filters = array(
                    'where' => array(
                        'c.id = \'' . $event->get('customer') . '\'',
                        'c.deleted_by IS NOT NULL',
                    ),
                    'model_lang' => $event->get('model_lang'),
                    'sanitize' => false,
                );
                /** @var Customer $customer */
                $customer = Customers::searchOne($this->registry, $filters);

                if ($customer) {
                    $event->set('customer_is_company', $customer->get('is_company'), true);

                    // prepare dropdown options for branch and contact
                    if ($event->get('customer_is_company')) {
                        require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
                        $filters_branches = array(
                            'sanitize' => true,
                            'model_lang' => $event->get('model_lang'),
                            'where' => array(
                                'c.parent_customer = \'' . $event->get('customer') . '\'',
                                'c.subtype = \'branch\'',
                                'c.active = 1'
                            ),
                            'sort' => array(
                                'c.is_main DESC',
                                'ci18n.name ASC',
                                'c.id DESC'
                            )
                        );
                        $data['customer_branches'] = Customers_Branches::search($this->registry, $filters_branches);
                        array_walk($data['customer_branches'], array($this, 'modelsToOptions'));

                        if (!$event->get('branch') && !empty($data['customer_branches'])) {
                            $event->set('branch', $data['customer_branches'][0]['option_value'], true);
                        }

                        if ($event->get('branch')) {
                            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                            $filters_contacts = array(
                                'sanitize' => true,
                                'model_lang' => $event->get('model_lang'),
                                'where' => array(
                                    'c.parent_customer = \'' . $event->get('branch') . '\'',
                                    'c.subtype = \'contact\'',
                                    'c.active = 1'
                                ),
                                'sort' => array(
                                    'c.is_main DESC',
                                    'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC',
                                    'c.id DESC'
                                )
                            );
                            $data['contact_persons'] = Customers_Contactpersons::search($this->registry, $filters_contacts);
                            array_walk($data['contact_persons'], array($this, 'modelsToOptions'));
                        }
                    }
                }
            }
        }

        if ($this->messages->getErrors()) {
            return $this->prepareErrorResponse(
                $this->messages->getErrors(),
                true
            );
        }

        $data['request_type_name'] = $this->i18n('plugin_customer_request');
        $data['inquiry_type_name'] = $this->i18n('plugin_inquiry');

        // if customer not specified or is set to unknown - allow changing it from screen
        $data['customer_readonly'] = $event->get('customer') && $event->get('customer') != $this->get('cust_id_unknown');

        $open_tickets_filters = array(
            'where' => array(
                "d.type = '{$this->get('doc_type_ticket')}' AND",
                "d.customer = '{$event->get('customer')}' AND",
                "d.active = '1' AND",
                "d.status = 'opened' AND",
            ),
            'sort' => array(
                "d.id DESC",
            ),
        );
        $data['num_open_tickets'] = count(Documents::getIds($this->registry, $open_tickets_filters));

        // other types of records to create (redirect to Add form)
        /** @var User $user */
        $user = &$this->registry['currentUser'];

        $create_regexp = '#^create_(.*)_types$#';
        $create_settings = $this->getAll();
        $create_settings = array_intersect_key(
            $create_settings,
            array_flip(array_filter(array_keys($create_settings), function($a) use ($create_regexp) { return preg_match($create_regexp, $a); }))
        );
        $create_params = array();
        foreach ($create_settings as $create_key => $create_types) {
            if ($create_types) {
                $create_types = array_filter(array_unique(array_map('intval', preg_split('#\s*,\s*#', $create_types))));
            }
            if (!empty($create_types)) {
                $create_model = preg_replace($create_regexp, '$1', $create_key);
                if (strpos($create_model, '_') !== false) {
                    // will not process module+controller settings for now
                    continue;
                }

                $create_module = General::singular2plural($create_model);
                if (!$user->checkPermissions('add', $create_module)) {
                    // add permission is not available for module
                    continue;
                }

                /** @var Model_Factory|Documents_Types $create_type_factory */
                $create_type_factory = ucfirst($create_module) . '_Types';
                if (!class_exists($create_type_factory)) {
                    continue;
                }

                $alias = $create_type_factory::getAlias($create_type_factory, '');
                /** @var Model[] $types */
                $types = $create_type_factory::search(
                    $this->registry,
                    array(
                        'where' => array(
                            "{$alias}.id IN ('" . implode("', '", $create_types) . "')",
                            "{$alias}.active = 1",
                        ),
                        'sanitize' => true,
                    )
                );

                foreach ($types as $type) {
                    if ($user->checkPermissions('add', "{$create_module}{$type->get('id')}") && !$type->get('inheritance')) {
                        if (!array_key_exists($create_module, $create_params)) {
                            $create_params[$create_module] = array();
                        }
                        $create_params[$create_module][$type->get('id')] = $type->get('name');
                    }
                }
            }
        }
        $data['create_params'] = $create_params;

        $event->getLayoutsDetails();
        $event->sanitize();

        $data['event'] = $event;

        $data['dashlet']->set('data', $this->prepareDashletParams(), true);

        return json_encode(array(
            'messages' => $this->prepareMessages($this->messages->getMessages(), 'message', true),
            'warnings' => $this->prepareMessages($this->messages->getWarnings(), 'warning', true),
            'content' => $this->fetch(
                '_manage_call.html',
                $data,
                array(
                    PH_MODULES_DIR . 'events/i18n/' . $this->registry['lang'] . '/events.ini',
                )
            ),
            'title' => $this->i18n('plugin_manage_call_title'),
        ));
    }

    /**
     * Updates call event with current data from form
     *
     * @return string - JSON-encoded result of operation (messages, errors, other)
     */
    public function saveCall() {

        $event = $this->getCall($this->request['event_id']);

        if (empty($event)) {
            $this->messages->setError($this->i18n('error_plugin_no_event'), '', -1);
        } else {
            // update event call with data from request, if necessary
            if ($this->updateCall($event) && $event->get('update_call')) {
                $update_call = true;
            }
        }
        return json_encode(array(
            'messages' => $this->prepareMessages($this->messages->getMessages(), 'message', true),
            'errors' => $this->prepareMessages($this->messages->getErrors(), 'error', true),
            'reload_origin_container' => !empty($update_call) ? 1 : '',
        ));
    }

    /**
     * Searches for and displays a list of tickets that call event can be
     * connected to
     *
     * @return string - json-encoded fetched HTML content and other data
     */
    public function searchTickets() {

        $data = array();

        // call event is required
        $event = $this->getCall($this->request['event_id']);

        if (empty($event)) {
            $this->messages->setError($this->i18n('error_plugin_no_event'), '', -1);
        } else {
            $customer_id = $this->request['customer'] ?: $event->get('customer');

            if ($customer_id) {
                // update event call with data from request, if necessary
                if ($this->updateCall($event) && $event->get('update_call')) {
                    $update_call = true;
                }

                $open_tickets_filters = array(
                    'where' => array(
                        "d.type = '{$this->get('doc_type_ticket')}' AND",
                        "d.customer = '{$customer_id}' AND",
                        "d.active = '1' AND",
                        "d.status = 'opened' AND",
                    ),
                    'sort' => array(
                        "d.id DESC",
                    ),
                    'get_fields' => array(
                        'timesheet_time',
                    ),
                    'sanitize' => true,
                );
                $data['tickets'] = Documents::search($this->registry, $open_tickets_filters);
                foreach ($data['tickets'] as $t) {
                    $t->unsanitize();
                    $t->getAssignments('owner');
                    $t->sanitize();
                }

                if (!$data['tickets']) {
                    // no open tickets found
                    $this->messages->setError($this->i18n('error_plugin_customer_no_open_tickets'));
                }

                $data['event'] = $event->sanitize();
            } else {
                // no customer specified
                $this->messages->setError($this->i18n('error_plugin_no_customer'));
            }
        }

        // display error messages and exit
        if ($this->messages->getErrors()) {
            return $this->prepareErrorResponse(
                $this->messages->getErrors(),
                true
            );
        }

        return json_encode(array(
            'content' => $this->fetch(
                '_search_tickets.html',
                $data
            ),
            'reload_origin_container' => !empty($update_call) ? 1 : '',
        ));
    }

    /****************************** internal methods *************************/

    /**
     * Searches unprocessed call event by id
     *
     * @param number $event_id - id of event
     * @return Event - event model or false if not found
     */
    private function getCall($event_id = 0) {
        return
            $event_id ?
            Events::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        "e.id = '{$event_id}'",
                        "e.type = '{$this->get('event_type_call')}'",
                        "e.active = '1' AND",
                        "e.status = 'finished' OR",
                        "e.status = 'unstarted' AND",
                        "erl1.link_to = '' AND",
                        "erl2.link_to = '' AND",
                    ),
                    'model_lang' => $this->request['model_lang'] ?: $this->registry['lang'],
                    // set module param because of event search specifics
                    'check_module_permissions' => 'events',
                )
            ) : false;
    }

    /**
     * Updates call event with set values from Request, if different
     *
     * @param Event $event - call event model
     * @return bool - result of the operation
     */
    private function updateCall(Event $event) {
        $result = true;
        $old_event = clone $event;

        // event properties that can be updated from interface of plugin
        foreach (array('customer', 'branch', 'contact_person', 'active',) as $k) {
            if ($this->request->isRequested($k) && $this->request[$k] != $event->get($k) && ($this->request[$k] || $event->get($k))) {
                $event->set($k, $this->request[$k], true);
                if ($k == 'customer') {
                    $event->unsetProperty('customer_name', true);
                    $event->getCustomerName();
                }
                // flag that event should be updated
                $event->set('update_call', true);
            }
        }
        $this->loadI18NFiles(array(
            PH_MODULES_DIR . 'events/i18n/' . $this->registry['lang'] . '/events.ini',
        ));

        if ($event->get('update_call')) {
            if ($event->save()) {
                $event->slashesStrip();
                Events_History::saveData(
                    $this->registry,
                    array(
                        'model' => $event,
                        'action_type' => 'edit',
                        'new_model' => $event,
                        'old_model' => $old_event,
                    )
                );
                $this->messages->setMessage($this->i18n('message_events_edit_success', array($event->getModelTypeName())));

            } else {
                $this->messages->setError($this->i18n('error_events_edit_failed', array($event->getModelTypeName())));
                $result = false;
            }
        }

        // if current user is not assigned to event, set as observer
        if (!$event->get('access')) {
            $event->getAssignments();
            $old_event = clone $event;
            $u_id = $this->registry['currentUser']->get('id');
            $event->set('new_users', $event->get('users_assignments') + array($u_id => array('participant_id' => $u_id, 'ownership' => 'other', 'access' => 'edit')), true);
            if ($event->assign(false, true)) {
                $event->getAssignments();
                Events_History::saveData(
                    $this->registry,
                    array(
                        'model' => $event,
                        'action_type' => 'assign',
                        'new_model' => $event,
                        'old_model' => $old_event,
                    )
                );
            }
        }

        // make sure to clear messages/errors from session
        $this->messages->removeFromSession($this->registry);

        return $result;
    }

    /**
     * Defines ticket reaction and solution terms and sets them into model
     *
     * @param Document $document - ticket model
     * @param string $priority - value of selected priority option, normal by default
     */
    private function setTicketTerms(Document $document, $priority = '') {
        require_once PH_MODULES_DIR . 'automations/plugins/brainstorm/controllers/brainstorm.automations.controller.php';
        $automation_controller = new Brainstorm_Automations_Controller($this->registry);
        $automation_controller->settings = array(
            'ticket_priority_var'      => $this->get('ticket_priority_var'),
            'ticket_reaction_time_var' => $this->get('ticket_reaction_time_var'),
            'ticket_solution_time_var' => $this->get('ticket_solution_time_var'),
            'default_priority'         => $this->get('default_priority'),
            'working_day_start'        => $this->get('working_day_start'),
            'working_day_end'          => $this->get('working_day_end')
        );
        $automation_controller->_setTicketTerms($document, $priority);
    }

    /**
     * Calculate term from current datetime according to working hours
     *
     * @param int $term_duration - duration in seconds
     * @param string $start_datetime - optional start datetime in ISO-format
     * @return string - calculated datetime in ISO-format
     */
    private function calculateTerm($term_duration, $start_datetime = '') {
        $registry = &$this->registry;
        $ticket_data = array();

        // calculate according to current datetime, if not specified otherwise
        if (!$start_datetime) {
            $start_datetime = 'now';
        }

        $start_datetime = new DateTime($start_datetime);
        $start_date = $start_datetime->format('Y-m-d');
        list($hour, $minute) = explode(':', $this->get('working_day_start'));
        $start_datetime_start = (clone $start_datetime)->setTime($hour, $minute);
        list($hour, $minute) = explode(':', $this->get('working_day_end'));
        $start_datetime_end = (clone $start_datetime)->setTime($hour, $minute);
        $ticket_data['start_datetime'] = $start_datetime;
        $ticket_data['start_datetime_start'] = $start_datetime_start;
        $ticket_data['start_datetime_end'] = $start_datetime_end;
        $ticket_data['start_date_working_day'] = Calendars_Calendar::getWorkingDays($registry, $start_date, $start_date);
        // specify if start date is inside or outside of working hours
        // (if it is a working day and the time is in the working hours)
        if ($ticket_data['start_date_working_day'] && $start_datetime >= $start_datetime_start && $start_datetime <= $start_datetime_end) {
            $ticket_data['start_date_working_hours'] = 1;
        } else {
            $ticket_data['start_date_working_hours'] = 0;
        }

        // calculate working day duration and duration before working day (in seconds)
        $int = $start_datetime_end->diff($start_datetime_start);
        $ticket_data['working_day_duration'] = $int->h * 3600 + $int->i * 60 + $int->s;
        $int = $start_datetime_start->diff((clone $start_datetime_start)->setTime(0, 0));
        $ticket_data['before_working_day_duration'] = $int->h * 3600 + $int->i * 60 + $int->s;

        /** @var DateTime $start_datetime */
        //$start_datetime = $ticket_data['start_datetime'];

        // number of whole working days
        $num_days = floor($term_duration / $ticket_data['working_day_duration']);
        // remaining term duration (in seconds)
        $remaining_duration = $term_duration % $ticket_data['working_day_duration'];

        // deadline should be end of the working day, not start of the next one
        if ($num_days && !$remaining_duration) {
            $num_days--;
            $remaining_duration = $ticket_data['working_day_duration'];
        }

        if ($ticket_data['start_date_working_hours']) {
            // start date is within working hours, calculate from start date on

            // if the remaining duration goes after end of working day
            if ($start_datetime->getTimestamp() + $remaining_duration > $ticket_data['start_datetime_end']->getTimestamp()) {
                // current datetime minus end of working day (difference will be negative)
                $remaining_duration += $start_datetime->getTimestamp() - $ticket_data['start_datetime_end']->getTimestamp();
                // plus 1 day
                $num_days++;
            } else {
                // current datetime minus start of working day
                $remaining_duration += $start_datetime->getTimestamp() - $ticket_data['start_datetime_start']->getTimestamp();
            }

            // add the time from start of the day until start of the working day
            if ($remaining_duration) {
                $remaining_duration += $ticket_data['before_working_day_duration'];
            }
        } else {
            // start date is outside working hours, calculate from start of next working day on

            // add the time from start of the day until start of the working day
            $remaining_duration += $ticket_data['before_working_day_duration'];

            // add 1 day to find the next working day
            // (unless start date and time is on a working day but before its start)
            if (!($ticket_data['start_date_working_day'] && $start_datetime < $ticket_data['start_datetime_start'])) {
                $num_days++;
            }
        }

        $new_date = (new DateTime(
            Calendars_Calendar::calcDateOnWorkingDays($registry, $start_datetime->format('Y-m-d'), $num_days)
        ))->add(
            new DateInterval(sprintf('PT%dS', $remaining_duration))
        )->format('Y-m-d H:i:s');

        return $new_date;
    }

    /**
     * Callback function for processing of arrays of models.
     * Prepares model data as dropdown options.
     *
     * @param Model $a - current array element
     */
    private function modelsToOptions(Model &$a) {
        $a = array(
            'option_value' => $a->get('id'),
            'label' => trim($a->get('name') . ' ' . ($a->get('lastname') ?: '')),
            'active_option' => $a->get('active'),
        );
    }

    /**
     * Prepares JSON-encoded formatted response containing error messages.
     *
     * @param array $messages - error messages
     * @param bool $urls - if true, keeps relative links (anchors) of messages
     * @return string - a JSON-encoded string
     */
    private function prepareErrorResponse($messages = array(), $urls = false) {
        return json_encode(array(
            'errors' => $this->prepareMessages($messages, 'error', $urls)
        ));
    }

    /**
     * Fetches html content for success, warning or error messages to be
     * displayed.
     *
     * @param array $messages - array of messages
     * @param string $mode - message (default), warning or error
     * @param bool $urls - if true, keeps relative links (anchors) of messages
     * @return string - fetched content
     */
    private function prepareMessages($messages = array(), $mode = 'message', $urls = false) {
        if (!empty($messages)) {
            $v = new Viewer($this->registry);
            $v->setFrameset('message.html');
            $v->data['display'] = $mode;
            // don't display relative URLs unless specified
            $v->data['items'] = $urls ? $messages : array_values($messages);
            $messages = $v->fetch();
            unset($v);
        } else {
            $messages = '';
        }
        return $messages;
    }

    /**
     * Fetches template for display
     *
     * @param string $template - name of a template file from current plugin
     * @param array $data - data to assign to viewer
     * @param array $lang_files - additional language files to be loaded
     * @return string - fetched content
     */
    private function fetch($template, $data = array(), $lang_files = array()) {
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        $viewer->template = $template;
        foreach ($data as $k => $v) {
            $viewer->data[$k] = $v;
        }
        $viewer->data['scripts'] = array(
            // load after the other js files
            PH_MODULES_URL . 'dashlets/plugins/' . $this->plugin_name . '/javascript/custom.js'
        );
        $lang_files = array_merge(
            $lang_files,
            $this->registry['translater']->i18nFiles['custom'],
            array(PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/i18n/' . $this->registry['lang'] . '/custom.ini')
        );
        $viewer->loadCustomI18NFiles($lang_files);
        return $viewer->fetch();
    }
}
