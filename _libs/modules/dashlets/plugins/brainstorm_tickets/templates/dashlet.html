{strip}
<table border="0" cellpadding="0" cellspacing="0" style="width: 100%;" class="t_table">
  <tr>
    <td style="padding: 10px;">
      <table class="t_layout_table" style="width: auto; margin: auto;">
        <tr>
          <td colspan="3">
            {assign var='var' value=$dashlet->get('customer_options')}
            <label for="{$var.custom_id|default:$var.name}_autocomplete" class="labelbox hright" style="width: auto!important;">
              {help label_content=$var.label text_content=$var.description}
              <span class="required">{#required#}</span>
            </label>
            {include file="input_autocompleter.html"
                    standalone=true
                    name=$var.name
                    custom_id=$var.custom_id
                    label=$var.label
                    help=$var.help
                    back_label=$var.back_label
                    back_label_style=$var.back_label_style
                    value=$var.value
                    value_id=$var.value_id
                    options=$var.options
                    optgroups=$var.optgroups
                    option_value=$var.option_value
                    first_option_label=$var.first_option_label
                    onclick=$var.onclick
                    on_change=$var.on_change
                    sequences=$var.sequences
                    check=$var.check
                    scrollable=$var.scrollable
                    calculate=$var.calculate
                    readonly=$var.readonly
                    source=$var.source
                    onchange=$var.onchange
                    map_params=$var.map_params
                    width=$var.width
                    hidden=$var.hidden
                    really_required=$var.required
                    required=$var.required
                    disabled=$var.disabled
                    options_align=$var.options_align
                    autocomplete=$var.autocomplete
                    autocomplete_var_type=$var.autocomplete_var_type
                    js_methods=$var.js_methods
                    restrict=$var.js_filter
                    deleteid=$var.deleteid
                    show_placeholder=$var.show_placeholder
                    text_align=$var.text_align
                    custom_class=$var.custom_class
            }
            {include file="input_hidden.html"
                    standalone=true
                    name="`$var.custom_id`_data"
                    value=''
            }
          </td>
        </tr>
        <tr>
          <td colspan="3">
            {capture assign='button_source'}Brainstorm_Tickets.loadScreen(this, Form.serialize($(this).up('table')));return false;{/capture}
            {array assign='button_source' onclick=$button_source}
            {include file="input_button.html"
                     standalone=true
                     name='ticket_button'
                     source=$button_source
                     label=#plugin_select#
                     custom_class='floatr reports_export_button short'
            }
          </td>
        </tr>
      </table>
      {foreach from=$scripts item='script'}
      <script defer="defer" type="text/javascript" src="{$script}?{$system_options.build}"></script>
      {/foreach}
      <script defer="defer" type="text/javascript">Brainstorm_Tickets = new Brainstorm_Tickets('{$dashlet->get('id')}', '{$dashlet->get('controller')}', {json encode=$dashlet->get('data')|default:''});</script>
    </td>
  </tr>
</table>
{/strip}