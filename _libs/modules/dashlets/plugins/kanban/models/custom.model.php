<?php
/**
 * KANBAN plugin custom model class
 */
Class Custom_Model extends Model {
    public $modelName = 'Dashlet';
    public $plugin_name = 'kanban';

    public function __construct($dashlet_settings = '') {
        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        return true;
    }

    /**
     * Parse settings for current plugin and set them as properties to model
     *
     * @param string $dashlet_settings - settings field of dashlet
     */
    public function parseDashletPluginSettings($dashlet_settings) {

        $settings_rows = preg_split('/(\n|\r|\r\n)/', $dashlet_settings);

        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0]=='#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);

            if($this->get($key)) {
                $old = $this->get($key);
                if(is_array($old)) {
                    $old[] = $value;
                    $value = $old;
                } else {
                    $value = [$old, $value];
                }

            }

            // set settings as properties of model in the usual way
            $this->set($key, $value, true);
        }
    }

    /**
     * Prepare the main panel, depending on the action
     */
    public function processMainAction() {
        $db = &$this->registry['db'];
        $lang = $this->registry['lang'];
        $request = $this->registry['request'];

        $this->registry->set('module', 'documents', true);
        $this->registry->set('controller', 'documents', true);
        $this->registry->set('action_param', 'documents', true);

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = [$this->registry->get('theme')->getTemplatesDir,  PH_MODULES_DIR.'dashlets/plugins/' . $this->plugin_name . '/templates/'];
        $lang_files = array(PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/i18n/' . $lang . '/custom.ini');
        $viewer->loadCustomI18NFiles($lang_files);

        $template = 'dashlet.html';

        $viewer->template = $template;

        return $viewer->fetch();
    }

    public function prepareCustomData(&$model)
    {
        $registry = &$this->registry;

        // Get the type
        $docTypeId = $this->get('document_type');

        // Generate empty document of this type
        $doc = $this->_generatePrototypeDocument($registry, $docTypeId);

        // Prepare $var_names array
        $lang = $registry->get('lang');
        $var_names = $this->_prepDocVarNames($doc, $lang);
        $availableVars = array_keys($var_names);
        $availableVars[] = '_owner';
        $availableVars[] = '_responsible';
        $availableVars[] = '_observer';
        $availableVars[] = '_decision';
        $availableVars[] = '_tags';


        // Prep Tags
        $tags = $doc->getAvailableTags();
        $tagNames = [];
        foreach ($tags as $k=>$v) {
            $tagNames[$v->get('id')] = [
                'color' => $v->get('color'),
                'keyword' => $v->get('keyword'),
                'name' => $v->get('name'),
                'description' => $v->get('description'),
            ];
        }
        $role = $this->registry['currentUser']->get('role');

        $cardFields = $this->get('card_fields_'.$role);
        if(!$cardFields) {
            $cardFields = $this->get('card_fields');
        }
        $cardFields = $cardFields ? preg_split('/,\s*/', $cardFields) : ['notes', 'deadline'];

        // Filter out the vars that the user don't have rights to see
        $cardFields = array_values(array_intersect($cardFields, $availableVars));

        $cardTooltip = $this->get('card_tooltip');
        $cardTooltip = $cardTooltip ? preg_split('/,\s*/', $cardTooltip) : null;

        if($cardTooltip) {
            // Filter out the vars that the user don't have rights to see
            $cardTooltip = array_values(array_intersect($cardTooltip, $availableVars));
        }

        $searchFields = $this->get('search_fields');
        $searchFields = $searchFields ? preg_split('/,\s*/', $searchFields) : ['name', 'num', 'custom_num', 'customer_name', 'description', 'notes', 'deadline', 'deadline_formatted', 'date', 'date_formatted'];

        // Filter out the vars that the user don't have rights to see
        $searchFields = array_values(array_intersect($searchFields, $availableVars));

        $filterLabels = [
            'owner' => 'Изпълнител',
            'responsible' => 'Отговорник',
            'observer' => 'Наблюдаващ',
            'decision' => 'Вземащ решения',
        ];

        $filtersSettings = $this->get('filters');
        $filtersSettings = $filtersSettings ? preg_split('/,\s*/', $filtersSettings) : [];
        $filters = [];
        foreach ($filtersSettings as $v) {
            if(strpos($v, '-')){
                $parts = explode('-',$v);
                if(!array_key_exists($parts[0], $filters)) {
                    $filters[$parts[0]] = [];
                }
                $filters[$parts[0]][$parts[1]] = $filterLabels[$parts[1]];
            } else {
                $filters[$v] = true;
            }
        }

        $custom_css = $this->get('custom_css');
        $custom_css = $custom_css ? (is_array($custom_css) ? $custom_css : [$custom_css]) : [];

        $options = [
            'addCard' => (bool) $doc->checkPermissions('add'),
            'fullscreen' => (bool) $this->get('option_fullscreen'),
        ];

        $session_name = session_name();
        $model->set('kb', $session_name, true);
        $model->set('card_tooltip', $cardTooltip);
        $model->set('fields', $var_names);
        $model->set('card_fields', $cardFields);
        $model->set('filters_view', $filters);
        $model->set('options_view', $options);
        $model->set('css_overrides', $custom_css);
        $model->set('js_params', [
            'document_type' => $docTypeId,
            'websocket' => $this->get('websocket'),
            'columns' => $this->_getStatuses($registry, $docTypeId, $this->get('nomenclature_type')),
            'debug' => $this->get('debug'),
            'card_title' => $this->get('card_title'),
            'card_fields' => $cardFields,
            'card_tooltip' => $cardTooltip,
            'fields' => $var_names,
            'time_urgency' => $this->get('time_urgency'),
            'time_cri' => $this->get('time_cri'),
            'time_high' => $this->get('time_high'),
            'time_med' => $this->get('time_med'),
            'deadline_format' => $this->get('deadline_format'),
            'time_left_format' => $this->get('time_left_format'),
            'search_fields' => $searchFields,
            'filters' => $filters,
            'tags' => $tagNames,
            'options_view' => $options,
            'sort_by' => $this->get('sort_by'),
            'currentUser' => ['id' => $registry['currentUser']->get('id'), 'name'=>$registry['currentUser']->get('firstname').' '. $registry['currentUser']->get('lastname')],
        ], true);

        $model->set('filters', [
            'custom_css' => [
                PH_JAVASCRIPT_URL . 'ej2/material.css',
            ],
            'custom_js' => [
                PH_JAVASCRIPT_URL . 'ej2/ej2.min.js',
            ],
        ], true);

        return $model;
    }

    /**
     * Generates an empty prototype of a document model of givven type
     *
     * @param $registry
     * @param $docTypeId
     * @return Document
     */
    private function _generatePrototypeDocument ($registry, $docTypeId) : Document
    {
        $doc_type = Documents_Types::searchOne($registry, [
            'where'      => ["dt.id = {$docTypeId}"],
            'model_lang' => 'bg',
        ]);

        $lang = $registry->get('lang');

        // Generate empty document
        $doc = new Document($registry, [
            'type'       => $doc_type->get('id'),
            'name'       => $doc_type->get('default_name'),
            'active'     => 1,
            'model_lang' => $lang,
            'department' => $doc_type->getDefaultDepartment(),
            'group'      => $doc_type->getDefaultGroup(),
            'date'       => General::strftime('%Y-%m-%d'),
            'deadline'   => General::strftime('%Y-%m-%d'),
            'customer'   => 1,
            'employee'   => $registry['currentUser']->get('employee')
        ]);
        // Get the default layout names for the basic vars
        $get_old_vars = $registry->get('get_old_vars');
        $registry->set('get_old_vars', true, true);
        $doc->getAllVars();
        $doc->getLayoutVars();
        $registry->set('get_old_vars', $get_old_vars, true);

        return $doc;
    }

    /**
     * Takes a Document type model and a language, then returns an array that maps var_name => var_label.
     * It includes the labels of basic vars as well as labels of additional vars.
     * Also includes labels for assignments
     *
     * @param Document $doc
     * @param $lang
     * @return array
     */
    private function _prepDocVarNames(Document $doc, $lang) : array
    {
        $var_names = [];
        // Prep basic vars
        foreach ($doc->getLayoutsDetails() as $k=>$v) {
            $var_names[$v['keyword']] = $v['name'];
        }

        // Prep additional vars
        foreach ($doc->get('plain_vars') as $k=>$v) {
            $var_names[$v['name']] = $v['label'];
        }

        // Custom var variants labels
        $var_names['customer_name'] = $var_names['customer'] ? $var_names['customer'] : '';
        $var_names['date_formatted'] = $var_names['date'] ? $var_names['date'] : '';
        $var_names['deadline_formatted'] = $var_names['deadline'] ? $var_names['deadline'] : '';

        // Steal terms from document module
        $terms = parse_ini_file(PH_MODULES_DIR."documents/i18n/$lang/documents.ini", false, INI_SCANNER_RAW);

        $var_names['assignments_owner'] = $terms['documents_owner'];
        $var_names['assignments_responsible'] = $terms['documents_responsible'];
        $var_names['assignments_observer'] = $terms['documents_observer'];
        $var_names['assignments_decision'] = $terms['documents_decision'];

        return $var_names;
    }

    /**
     * Fetches data and process it to return an array representing the columns/statuses in the Kanban
     *
     * @param $registry
     * @param $docTypeId
     * @param $nomType
     * @return array
     */
    private function _getStatuses($registry, $docTypeId, $nomType) : array
    {
        $statusCollection = [];

        $get_old_vars = $registry->get('get_old_vars');
        $registry->set('get_old_vars', true, true);

        // Fetch the nomenclatures
        $nomStatuses = Nomenclatures::search($registry, [
            'where'=>[
                "n.type={$nomType}",
                "n.active=1",
            ],
            'sort' => [
                'a__sort_num ASC'
            ],
            'sanitize' => false,
        ]);

        if($nomStatuses) {
            // This is a nomenclature representing the document statuses and adding additional properties to them
            $docStatuses = [];
            $docStatusIds = [];
            // Process the nomenclatures sto simplify stuff
            foreach ($nomStatuses as $k => $v) {
                $v->getAllVars();

                $docStatuses[$v->get('id')] = $v->getAll();
                foreach ($v->get('plain_vars') as $k2 => $v2) {
                    $docStatuses[$v->get('id')][$v2['name']] = $v2;

                    if (is_array($v2) && $v2['name'] == 'status' && !empty($v2['value'])) {
                        $docStatusIds[] = $v2['value'];
                    }
                }
            }
        }

        if(!empty($docStatusIds)) {
            $docStatusIds_str = implode(',', $docStatusIds);

            // Fetch the document statuses for the doc type
            $statuses = Documents_Statuses::search($registry, [
                'where' => [
                    "ds.doc_type = {$docTypeId}",
                    "ds.id IN ({$docStatusIds_str})"
                ]
            ]);
        }
        $registry->set('get_old_vars', $get_old_vars, true);

        if(!empty($statuses)) {
            $statusesById = [];
            // Prep doc statuses
            foreach ($statuses as $k=>$v) {
                $statusesById[$v->get('id')] = $v;
            }

            // Prep the final array
            foreach ($docStatuses as $k=>$v) {
                $statusCollection[] = [
                    'id' => $v['id'],
                    'sort' => $v['sort_num']['value'],
                    'name' => $v['name'],
                    'icon' => $statusesById[$v['status']['value']]->get('icon_name'),
                    'status' => $v['status']['value'],
                ];
            }
        }
        return $statusCollection;
    }
}
    
?>
