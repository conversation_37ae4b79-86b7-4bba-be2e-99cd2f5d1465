/**
 * All the functionality for dashlet management goes here
 * @param {int} dashlet_id - dashlet id
 * @param {string} plugin - plugin name
 * @param {Object} params - any other parameters (settings, data etc.)
 * @return
 */
window.dashKanban = typeof dashKanban == 'object' ? dashKanban : function(dashlet_id, plugin, params) {
    Effect.Center('loading');
    Effect.Appear('loading');
    ej.base.enableRipple(true);

    this.DEBUG_TAG = 'DASH_KANBAN';
    /**
     * Function to show debug messages if debug mode is enabled
     */
    this._log = (function () {
        if (this.debug) {
            let e = new Error();
            if (!e.stack)
                try {
                    // IE requires the Error to actually be thrown or else the
                    // Error's 'stack' property is undefined.
                    throw e;
                } catch (e) {
                    if (!e.stack) {
                        return;
                    }
                }
            let stack = e.stack.toString().split(/\r\n|\n/);
            let a = Array.from(arguments);
            a.unshift(this.DEBUG_TAG);
            a.push('          [' + stack[2] + ']');
            console.log.apply(null, a);
        }
    }).bind(this);

    this.assignmentType = ['owner', 'decision', 'responsible', 'observer'];
    this.assignmentsAll = [];

    this.dashlet_id = dashlet_id;
    this.plugin = plugin;
    this.data = params || {};
    this.debug = !!Number(this.data.debug);
    this._log(this.dashlet_id, this.plugin, this.data);

    this.envName = env.code.toLowerCase();
    this.socket;
    this.messageBox = document.getElementById('dashlet_messages_' + this.dashlet_id);
    this.dataSource = [];
    this.kbStatuses;
    this.ck = document.getElementById('kanban').dataset.ck;
    this._sess = document.getElementById('kanban').dataset.ck;
    this.kanbanObj;
    this.statusIconPath = document.getElementById('kanban').dataset.statuspath;
    this.websocketCallbacks = {};

    this.shouldConnect = true;
    this.isNavigating = false;
    this.maxNumberOfRetries = 10;
    this.numberOfRetries = 0;

    let that = this;

    this.closestParent = function (element, parentSelector, context) {
        let closest = null;
        while (element.parentNode) {
            if (element.matches(parentSelector)) {
                closest = element;
                break;
            }
            element = element.parentNode;
        }

        return closest;
    }

    this.calcFilter = (function () {
        if (!this.kanbanObj) {
            return;
        }
        let searchQuery = new ej.data.Query().where('active', 'equal', '1').where('deleted_by', 'equal', '0');
        // Text serach
        let searchField = document.getElementById('search_text');
        if (searchField) {
            let searchValue = searchField.value;
            if (searchValue !== '') {
                dashKanban.emptyValue = false;
                searchQuery.search(searchValue, this.data.search_fields, 'contains', true);
                this.closestParent(searchField, '.kb-toolbar-sidetools-item').classList.add('used-filter');
            } else {
                dashKanban.emptyValue = true;
                this.closestParent(searchField, '.kb-toolbar-sidetools-item').classList.remove('used-filter');
            }
        }

        let usedFilters = document.querySelectorAll(`.used-filter`);
        for (let j = 0; j < usedFilters.length; j++) {
            if (usedFilters[j].querySelectorAll(':checked, #search_text').length == 0) {
                usedFilters[j].classList.remove('used-filter');
            }
        }

        let temp
        let assignments;
        let assignmentsPred;
        for (let i in this.assignmentType) {
            if (isNaN(parseInt(i))) {
                continue;
            }

            assignmentsPred = null;
            assignments = document.querySelectorAll(`.assignments-filter-${this.assignmentType[i]}-option:checked`);
            if (assignments.length) {
                temp = this.closestParent(assignments[0], '.kb-toolbar-sidetools-item');
                if (temp && !temp.matches('.used-filter')) {
                    temp.classList.add('used-filter');
                }

                for (let j = 0; j < assignments.length; j++) {
                    if (assignmentsPred) {
                        assignmentsPred = assignmentsPred.and(`_${this.assignmentType[i]}_id`, 'contains', `,${assignments[j].value},`);
                    } else {
                        assignmentsPred = new ej.data.Predicate(`_${this.assignmentType[i]}_id`, 'contains', `,${assignments[j].value},`);
                    }
                }

                if (assignmentsPred) {
                    searchQuery.where(assignmentsPred);
                }
            }
        }

        let tagsPred;
        let tags = document.querySelectorAll(`.tags-filter-option:checked`);
        if (tags.length) {
            temp = this.closestParent(tags[0], '.kb-toolbar-sidetools-item');
            if (temp && !temp.matches('.used-filter')) {
                temp.classList.add('used-filter');
            }
            for (let j = 0; j < tags.length; j++) {
                if (tagsPred) {
                    tagsPred = tagsPred.and(`_tags_id`, 'contains', `,${tags[j].value},`);
                } else {
                    tagsPred = new ej.data.Predicate(`_tags_id`, 'contains', `,${tags[j].value},`);
                }
            }
            if (tagsPred) {
                searchQuery.where(tagsPred);
            }
        }

        this.kanbanObj.query = searchQuery;
        setTimeout(this.tick, 20);
    }).bind(this);

    // Clear assignment filters
    this.clearAssignmentFilter = function (assignmentType, calcFilter) {
        let checks = document.querySelectorAll(`.filter-assignments .assignments-filter-${assignmentType}-option:checked`);
        for (let i = 0; i < checks.length; i++) {
            checks[i].checked = false;
        }

        if (typeof calcFilter == 'undefined' || calcFilter) {
            dashKanban.calcFilter();
        }
    };

    //
    this.clearTagFilter = function (calcFilre) {
        let checks = document.querySelectorAll(`.filter-tags .tags-filter-option:checked`);
        for (let i = 0; i < checks.length; i++) {
            checks[i].checked = false;
        }

        if (typeof calcFilre == 'undefined' || calcFilrer) {
            dashKanban.calcFilter();
        }
    };

    this.clearSearchFilter = function (calcFilre) {
        let search = document.getElementById(`search_text`);
        search.value = '';

        if (typeof calcFilre == 'undefined' || calcFilrer) {
            dashKanban.calcFilter();
        }
    };

    this.setSize = (function (e) {
        let dash = document.getElementById('dashlet_' + this.dashlet_id)
        let box = dash.getBoundingClientRect();
        let size = {};

        if (dash.querySelector('.dashlet-kanban.fullscreen')) {
            size.width = window.innerWidth - 10;
            size.height = window.innerHeight - 52;
        } else {
            size.width = window.innerWidth - box.left - 27;
            size.height = window.innerHeight - box.top - 190;
        }

        if (!this.kanbanObj) {
            return size;
        }

        this.kanbanObj.width = size.width;
        this.kanbanObj.height = size.height;
        this.kanbanObj.scrollSettings.width = size.width;
        this.kanbanObj.scrollSettings.height = size.height;

        return size;

    }).bind(this);


    (function () {
        // This makes sure to respect other onbeforeunload handlers.
        const existingHandler = window.onbeforeunload;
        window.onbeforeunload = function (event) {
            if (existingHandler) existingHandler(event);
            dashKanban.shouldConnect = false;
        }

        let _windowResizeTimer;

        let resizeHandler = function (e) {
            _windowResizeTimer = null;
            dashKanban.setSize(e);
        }

        window.addEventListener('resize', function (e) {
            if (_windowResizeTimer) {
                clearTimeout(_windowResizeTimer);
            }
            _windowResizeTimer = setTimeout(resizeHandler, 400);
        });

        // Handle click on main filter button
        document.getElementsByClassName('kb-toolbar')[0].addEventListener('click', e => {
            let element = that.closestParent(e.target, '.filter-button');
            if (!element) {
                return;
            }

            element = that.closestParent(element, '.kb-toolbar-sidetools-item');
            if (!element) {
                return;
            }

            if (element.matches('.opened')) {
                element.classList.remove('opened');
            } else {
                let others = document.querySelectorAll('.kb-toolbar-sidetools-item.opened');
                if (others) {
                    for (let i = 0; i < others.length; i++) {
                        others[i].classList.remove('opened');
                    }
                }
                element.classList.add('opened');
            }

        });

        // Handle the clicks on filter options
        document.getElementsByClassName('kb-toolbar')[0].addEventListener('click', e => {
            let assignmentType = 'owner';
            let element = that.closestParent(e.target, `.assignments-filter-${assignmentType}-option, .tags-filter-option`);
            if (!element) {
                return;
            }

            that.calcFilter();
        });

        // Handle clicks on buttons to clear filters
        document.getElementsByClassName('kb-toolbar')[0].addEventListener('click', e => {
            let element = that.closestParent(e.target, '.filter-pan-cancel, .filter-clear');
            if (!element) {
                return;
            }

            let filterItem = that.closestParent(element, '.kb-toolbar-sidetools-item');
            if (filterItem) {
                if (filterItem.matches('.filter-assignments')) {
                    that.clearAssignmentFilter(filterItem.dataset.type);
                    return;
                }

                if (filterItem.matches('.filter-tags')) {
                    that.clearTagFilter();
                    return;
                }

                if (filterItem.matches('.filter-search')) {
                    that.clearSearchFilter();
                    return;
                }
            }
        });

        // Handle click on the OK button in filters
        document.getElementsByClassName('kb-toolbar')[0].addEventListener('click', e => {
            let element = that.closestParent(e.target, '.filter-pan-ok');
            if (!element) {
                return;
            }

            element = that.closestParent(e.target, '.kb-toolbar-sidetools-item');
            if (!element) {
                return;
            }

            if (element.matches('.opened')) {
                element.classList.remove('opened');
            }
        });

        // Full screen toggle
        document.getElementsByClassName('kb-toolbar')[0].addEventListener('click', e => {
            let element = that.closestParent(e.target, '.option-fullscreen');
            if (!element) {
                return;
            }
            let kb = that.closestParent(e.target, '.dashlet-kanban');
            if (kb.matches('.fullscreen')) {
                kb.classList.remove('fullscreen');
                document.body.classList.remove('fullscreen_body_fix');
            } else {
                kb.classList.add('fullscreen');
                document.body.classList.add('fullscreen_body_fix');
            }
            resizeHandler();
        });

    })();

    /**
     * Handler for websocket messages with 'kanban/<environment>/update/<id>' action
     * @param body the response data object
     * @param id the ID from the action string
     */
    const action = this.envName + '/kanban/update';
    this.websocketCallbacks[action] = (body, id) => {
        let isNewCard_flag = true;
        for (const i in this.dataSource) {
            if (this.dataSource[i].id == id) {
                for (const j in body.board) {
                    if (body.board[j].id == id) {
                        body.board[j] = this.proccessCardAssignments(body.board[j]);
                        body.board[j] = this.proccessCardTags(body.board[j]);

                        for (const k in body.board[j]) {
                            if (body.board[j].hasOwnProperty(k)) {
                                this.dataSource[i][k] = body.board[j][k];
                            }
                        }
                        this.dataSource[i]['_deadline'] = Date.parse(body.board[j].deadline);
                        this.dataSource[i]['deadline_formatted'] = this.getDeadline(body.board[j].deadline);
                        for (const i1 in this.assignmentType) {
                            this.dataSource[i]['_' + i1] = '';
                            if (body.board[j]['assignments_' + this.assignmentType[i1]]) {
                                for (const j1 in body.board[j]['assignments_' + this.assignmentType[i1]]) {
                                    this.dataSource[i]['_' + i1] += body.board[j]['assignments_' + this.assignmentType[i1]][j1].assigned_to_name + ' ';
                                }
                            }
                        }

                        let element = document.getElementsByClassName('e-card-id-' + this.dataSource[i].id);

                        if (element.length !== 0) {
                            dashKanban.decorateCard(element[0].parent, this.dataSource[i]);
                        }
                    }
                }
                isNewCard_flag = false;
            }
        }
        if (isNewCard_flag) {
            for (const j in body.board) {
                if (body.board[j].id == id) {
                    let newCard = {};
                    for (const k in body.board[j]) {
                        newCard[k] = body.board[j][k];
                    }
                    newCard['_deadline'] = Date.parse(body.board[j].deadline);
                    newCard['deadline_formatted'] = this.getDeadline(body.board[j].deadline);

                    newCard = this.proccessCardAssignments(newCard);
                    newCard = this.proccessCardTags(newCard);

                    this.dataSource.push(newCard);
                }
            }
        }
        this.refreshFilterAssignments();
        this.refreshFilterTags();
        //check if there is any card dragged while refreshing board
        if (!document.querySelector('#kanban .e-kanban-dragged-card')) {
            this.kanbanObj.refresh();
        }
    }

    /**
     * Clicking anywhere in the pop-up closes it, unless a link is clicked
     */
    Event.observe($('overDiv'), 'click', function () {
        if (!isSubelementClicked()) {
            cClick();
        }
    });

    /**
     * To create a new card, just create a new document, and make sure the AK is active.
     */
    let createCardBtn = document.getElementById('createCardBtn')
    if (createCardBtn) {
        createCardBtn.addEventListener('click', function (e) {
            e.preventDefault();

            dashKanban.hideOverlib();
            ajaxForm('ajax_add', 'documents', dashKanban.data['document_type'], 'dashlet_messages_' + dashKanban.dashlet_id);
        });
    }

    /**
     * Handle clicking on tag icon of the cards
     */
    document.getElementById('kanban').addEventListener('click',function(e){
        let element = e.target;
        let temp;
        while(element.parentNode) {
            if((' ' + element.className + ' ').indexOf(' e-card-tool-tags ') !== -1) {
                temp = element;
                break;
            }
            element = element.parentNode;
        }

        if(temp) {
            element = temp;
            while(element.parentNode) {
                if((' ' + element.className + ' ').indexOf(' card-template ') !== -1) {
                    temp = element;
                    break;
                }
                element = element.parentNode;
            }
            if(temp) {
                dashKanban.changeTags('documents', temp.dataset.id, 'dashlet_messages_'+dashKanban.dashlet_id);
            }
        }
    });

    /**
     * Search box functionality
     */
    this.emptyValue = true;
    if(document.getElementById('search_text')) {
        document.getElementById('search_text').onkeyup = function (e) {
            if (e.code === 'Tab' || e.code === 'Escape' || e.code === 'ShiftLeft' || (e.code === 'Backspace' && dashKanban.emptyValue)) {
                return;
            }
            dashKanban.calcFilter();
        };
    }


    /**
     * Open an edit dialog in light box
     * @param ej
     */
    this.openCard = function(ej) {
        dashKanban._log(ej);
        // Stop the built-in dialog from opening
        ej.cancel=true;

        if(!ej.data.canEdit) {
            dashKanban.messageBox.innerHTML = '<div class="message_container"><ul>';
            dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + '<li class="error">Нямате право да редактирате този запис!</li>'
            dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + '</ul></div>';
            Effect.Appear(dashKanban.messageBox);
            new Effect.ScrollTo(dashKanban.messageBox);
            if(dashKanban._noEditRightsTimer){
                clearTimeout(dashKanban._noEditRightsTimer);
            }
            dashKanban._noEditRightsTimer = setTimeout(function () {
                Effect.Fade(dashKanban.messageBox);
                dashKanban._noEditRightsTimer = false;
            }, 10000);
            return false;
        }

        // Close any opened dialog.
        dashKanban.hideOverlib();
        // Send ajax request, and open the tags overlay
        ajaxForm('ajax_edit', 'documents', ej.data.id, 'dashlet_messages_'+dashKanban.dashlet_id);
    }


    /** ========= */

    /**
     * Hide sticky popups - use overlib functionality
     */
    this.hideSticky = function() {
        if ($('overDiv') && $('overDiv').style.visibility == 'visible') {
            // Initiate a timer for timeout
            //if (o3_timeout > 0) {
            if (o3_timerid > 0) clearTimeout(o3_timerid);
            o3_timerid = setTimeout("cClick()", o3_timeout);
            //}
        }
    };

    /**
     * Hide sticky popup when necessary for another action
     */
    this.hideOverlib = function() {
        if ($('overDiv') && $('overDiv').style.visibility == 'visible') {
            cClick();
        }
    };

    /**
     * Start the Kanban plugin
     *
     * @param columns - array of objects
     */
    this.start = function (columns) {
        const dash = this;
        // Default sorting
        let sortField = 'name';
        let sortDirection = 'Ascending';

        // Prep Sorting from settings if any
        if(typeof dash.data.sort_by == 'string' && dash.data.sort_by != '') {
            let parts = dash.data.sort_by.split(/[\s:]/);
            if(parts && parts.length){
                sortField = parts[0];
                if(parts.length >= 2) {
                    sortDirection = (parts[1].match(/^ascending$/i) || parts[1].match(/^asc$/i)) ? 'Ascending' : 'Descending';
                }
            }
        }

        try {
            const size = this.setSize();
            this.kanbanObj = new window.ej.kanban.Kanban({
                dataSource: this.dataSource,
                keyField: 'substatus',
                columns: columns,
                enableTooltip: ((typeof dash.data.card_tooltip == undefined) || !dash.data.card_tooltip) ? false : true,
                tooltipTemplate: '#tooltipTemp',
                cardSettings: {
                    headerField: 'id',
                    contentField: 'notes',
                    template: '#cardTemplate',
                },
                sortSettings: {
                    sortBy: 'Custom',
                    field: sortField,
                    direction: sortDirection
                },
                kanbanDialog: false,
                cardDoubleClick: this.openCard.bind(this),

                width: size.width,
                height:size.height,
                allowScrolling: true,
                scrollSettings: {
                    width: size.width,
                    height:size.height,
                },

                cardRendered: function (e) {
                    //ej.base.addClass([args.element], args.data.Priority);
                    dash.decorateCard(e.element, e.data);
                },

                actionBegin: function (e) {
                    dashKanban._log('actionBegin', e)
                },
                actionComplete: function (e) {
                    dashKanban._log('actionComplete', e)
                },
                actionFailure: function (e) {
                    console.error('actionFailure', e.error.stack.split(/\r\n|\n/));
                },

                dragStop: function (ej) {
                    dashKanban._log('dragStop', ej);
                    dash.updateStatus.apply(dash, [ej.data]);
                },
            });
        } catch (e) {
            console.error(e);
        }
        // Attach the Kanban object to the document and render it
        this.kanbanObj.appendTo('#kanban');
        this.calcFilter()
    }

    /**
     * Send the status change to websocket server.
     *
     * @param data - array of card models
     */
    this.updateStatus = function (data) {
        dashKanban._log('updateStatus', data);
        for (const card of data) {
            this.socket.send(JSON.stringify({
                action: this.envName+'/kanban/update/' + card.id,
                session: this._sess,
                data: {
                    status: card.status,
                    substatus: card.substatus,
                }
            }));
        }
    }

    this.assesment_list = {};
    /**
     * Process a cards data and populate assignments properties for filters
     * @param card
     */
    this.proccessCardAssignments = function(card){
        for(let i1 = 0; i1 < this.assignmentType.length; i1++) {
            assaignment = [];
            assaignment_id = [];

            if(typeof this.assesment_list[this.assignmentType[i1]] == 'undefined') {
                this.assesment_list[this.assignmentType[i1]] = {};
            }
            if (typeof card['assignments_' + this.assignmentType[i1]] == 'object') {
                for (const j1 in card['assignments_' + this.assignmentType[i1]]) {
                    if(isNaN(parseInt(j1)) || typeof card['assignments_' + this.assignmentType[i1]][j1].assigned_to_name == 'undefined'){
                        continue;
                    }
                    name = card['assignments_' + this.assignmentType[i1]][j1].assigned_to_name;
                    if(typeof this.assesment_list[this.assignmentType[i1]][j1] == 'undefined'){
                        this.assesment_list[this.assignmentType[i1]][j1] = name;
                    }
                    assaignment.push(name);
                    assaignment_id.push(j1);
                }
            }

            card['_' + this.assignmentType[i1]] = assaignment.join(' ')
            card['_' + this.assignmentType[i1] + '_id'] = `,${assaignment_id.join(',')},`;
        }
        return card;
    }

    this.tags_list = {};
    /**
     * Process a cards data and populate tags properties for filters
     * @param card
     */
    this.proccessCardTags = function(card){
        let tags = [];
        let tags_id = [];
        let name, tagId;

        if(card['tags']) {
            for (let k1 = 0; k1 < card['tags'].length; k1++) {
                if (typeof card['tags'][k1] == 'undefined' ) {
                    continue;
                }
                name = '';
                tagId = parseInt(card['tags'][k1]);
                if(typeof card.model_tags != 'undefined' && typeof card.model_tags[tagId] != 'undefined') {
                    name = card.model_tags[tagId].properties.name;
                    if(typeof this.data.tags[tagId] == 'undefined' || this.data.tags[tagId].name !== name) {
                        this.data.tags[tagId] = {
                            color: card.model_tags[tagId].properties.color,
                            description: card.model_tags[tagId].properties.description,
                            keyword: card.model_tags[tagId].properties.keyword,
                            name: card.model_tags[tagId].properties.name
                        };
                    }
                } else if(typeof this.data.tags[tagId] != 'undefined' && typeof this.data.tags[tagId].name != 'undefined') {
                    name = this.data.tags[tagId].name;
                }

                if (typeof this.tags_list[tagId] == 'undefined') {
                    this.tags_list[tagId] = name;
                }
                tags.push(name);
                tags_id.push(tagId);
            }
        }

        card['_tags'] = tags.join(' ');
        card['_tags_id'] = `,${tags_id.join(',')},`;

        return card;
    }

    /**
     * Refresh the Assignments filter options
     */
    this.refreshFilterAssignments = function() {
        let checked, assesment_ul;
        for(let i1 = 0; i1 < this.assignmentType.length; i1++) {
            assesment_li = [];
            if (typeof this.assesment_list[this.assignmentType[i1]] == 'object') {
                for(let j1 in this.assesment_list[this.assignmentType[i1]]) {
                    if(isNaN(parseInt(j1))){
                        continue;
                    }
                    checked = document.querySelectorAll(`.assignments-filter-${this.assignmentType[i1]}-option:checked[value="${j1}"]`).length ? ' checked="checked"' : '';
                    assesment_li.push(`<li><label><input type="checkbox" class="assignments-filter-${this.assignmentType[i1]}-option" value="${j1}"${checked} /> ${this.assesment_list[this.assignmentType[i1]][j1]}</label></li>`);
                }
            }
            assesment_ul = document.getElementById(`assignments-filter-${this.assignmentType[i1]}-list`);

            if(assesment_ul && assesment_li.length) {
                assesment_ul.innerHTML = assesment_li.join('');
            }
        }
    }

    /**
     * Refresh the tags filter options
     */
    this.refreshFilterTags = function() {
        let tags_li = [], cls;
        for(let k1 in this.tags_list) {
            if(isNaN(parseInt(k1))){
                continue;
            }
            checked = document.querySelectorAll(`.tags-filter-option:checked[value="${k1}"]`).length ? ' checked="checked"' : '';
            cls = this.data.tags[k1].color+'_pushpin';
            tags_li.push(`<li><label class="${cls}"><input type="checkbox" class="tags-filter-option" value="${k1}"${checked} /> ${this.tags_list[k1]}</label></li>`);
        }
        const tags_ul = document.getElementById(`tags-filter-list`);
        if(tags_ul && tags_li.length) {
            tags_ul.innerHTML = tags_li.join('');
        }
    }

    /**
     * Send a request to get the current state of the board from the socket server.
     * With the included handler of the response, populates the dataSource and the kbStatuses, then starts up the Kanban
     */
    this.populateBoard = function  () {
        dashKanban._log('populateBoard');
        Effect.Center('loading');
        Effect.Appear('loading');

        const action = this.envName+'/kanban/getAll';
        const dash = this;
        /**
         * Handle the response of 'kanban/getAll' action
         * @param body - data object returned from the server
         */
        this.websocketCallbacks[action] = body => {
            // Remove all elements of the array, without changing tha array itself.
            dash.dataSource.splice(0 ,dash.dataSource.length);
            // Loop over the cards returned by the server and push them to the dataSource
            for(const id in body.board.cards){
                if(!body.board.cards.hasOwnProperty(id)) {
                    continue;
                }
                try {
                    body.board.cards[id]['_deadline'] = Date.parse(body.board.cards[id].deadline);
                    body.board.cards[id]['deadline_formatted'] = this.getDeadline(body.board.cards[id].deadline);

                    body.board.cards[id] = this.proccessCardAssignments(body.board.cards[id])
                    body.board.cards[id] = this.proccessCardTags(body.board.cards[id])

                    dash.dataSource.push(body.board.cards[id]);
                } catch(e) {
                    console.error(e);
                }
            }

            this.refreshFilterAssignments();
            this.refreshFilterTags();

            // Prep the statuses
            dash.kbStatuses = dash.data.columns;
            let columns = [];
            let col;
            for(const i in dash.kbStatuses){
                if(!dash.kbStatuses[i].status) {
                    continue;
                }
                columns.push({
                    headerText: dash.kbStatuses[i].name,
                    keyField: dash.kbStatuses[i].status,
                    icon: dash.kbStatuses[i].icon,
                    template: '#headerTemplate',
                    allowToggle: false
                });
            }
            // Start up the Kanban plugin
            dash.start(columns);
            Effect.Fade('loading');
        }

        // Send request to websocket server
        dash.socket.send(JSON.stringify({
            action: action,
            session: dash._sess,
        }));
    }

    /**
     * Connect to the WebSocket server
     */
    this.connectToWs = function () {
        this._log('WS:','Try to connect');
        this.numberOfRetries++;
        this.socket = new WebSocket(this.data.websocket);
        const dash = this;

        document.cookie.split(';').forEach(element => {
            const parts = element.trim().split('=');
            if(parts[0] ==  this.ck){
                this._sess = element.trim();
            }
        });
        this.socket.addEventListener('open', function (event) {
            dashKanban._log('WS:','Connection established');
            dash.numberOfRetries = 0;
            dash.sendAuthToWs.apply(dash,[]);
        });

        this.socket.addEventListener('close', function (event) {
            const shouldRetry = dashKanban.shouldConnect || dashKanban.maxNumberOfRetries > dashKanban.numberOfRetries;
            dashKanban._log('WS:','Connection closed ' + (shouldRetry?'(continue trying)':'(stop trying)'));

            if(!dashKanban.shouldConnect){
                return;
            }

            if(dashKanban.maxNumberOfRetries <= dashKanban.numberOfRetries) {
                dashKanban.messageBox.innerHTML = '<div class="message_container"><ul>';
                dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + '<li class="error">Възникна неочакван проблем с връзката към сървъра (сървъра не отговаря). Моля уверете се, че мрежата в която се намирате, няма ограничения или защитна стена и презаредете страницата.</li>'
                dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + '</ul></div>';
                Effect.Appear(dashKanban.messageBox);
                new Effect.ScrollTo(dashKanban.messageBox);
                return;
            }

            dashKanban.messageBox.innerHTML = '<div class="message_container"><ul>';
            dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + `<li class="error">Не успяхме да се свържем със сървъра! Опитваме отново! (${dashKanban.numberOfRetries}/${dashKanban.maxNumberOfRetries})</li>`
            dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + '</ul></div>';
            Effect.Appear(dashKanban.messageBox);
            new Effect.ScrollTo(dashKanban.messageBox);

            setTimeout(function(){
                dashKanban.connectToWs();
            }, 2000);
        });

        this.socket.addEventListener('error', function (event) {
            console.error('WS:','Error: ', event)
        });

        this.socket.addEventListener('message', function (event) {
            const body = JSON.parse(event.data);
            dashKanban._log('WS:','Message ', body);

            if(body.code == 401) {
                dashKanban.shouldConnect = false;
                dashKanban.messageBox.innerHTML = '<div class="message_container"><ul>';
                dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + '<li class="error">Възникна неочакван проблем с удостоверяването на сесията! Ако виждате това за първи път моля презаредете страницата. Ако продължавате да получавате това съобщение се свържете с поддръжката на nZoom.</li>'
                dashKanban.messageBox.innerHTML = dashKanban.messageBox.innerHTML + '</ul></div>';
                if (dashKanban.messageBox.className.match(/collapsible/)) {
                    //class that tells us that we have to hide the container after time
                    Effect.Appear(dashKanban.messageBox);
                    new Effect.ScrollTo(dashKanban.messageBox);
                } else {
                    dashKanban.messageBox.style.display = '';
                }
            }

            // Something is wrong. We should always get an action attribute in a valid message
            if(!body.action) {
                return;
            }

            let actionPath = body.action;
            let actionArgument = body.action.replace(/(.*)\/([^\/]*)/, '$2');
            do {
                if( typeof dash.websocketCallbacks[actionPath] == 'function') {
                    dash.websocketCallbacks[actionPath].apply(this, [body, actionArgument]);
                    break;
                } else {
                    actionArgument = actionPath.replace(/(.*)\/([^\/]*)/, '$2');
                    actionPath = actionPath.replace(/(.*)(\/[^\/]*)/, '$1');
                }
            } while (actionPath.match(/\//));


        });
    }

    /**
     * Send authentication request. Should be called after connecting to WebSocket
     *
     */
    this.sendAuthToWs = function () {
        dashKanban._log('WS:','Sending auth ('+this._sess+') ');

        const action = this.envName+'/auth/authorize';
        this.websocketCallbacks[action] = body => {
            Effect.Fade(dashKanban.messageBox);
            this.populateBoard();
        }
        this.socket.send(JSON.stringify({
            action: action,
            session: this._sess,
        }));
    }

    /**
     * Get a value string from a card variable. Helper function for the card template,
     * that returns the value as a string, no mather if the variable is an additional ot basic
     *
     * @param data - the variable source value
     * @returns {string|*}
     */
    this.getValue = function (data) {
        switch(typeof(data)){
            case 'object':
                if(data === null) {
                    return '';
                }
                if(data.name) {
                    return data.value;
                }
                if(data.assigned_to_name) {
                    return data.assigned_to_name
                }
                let temp = [], v;
                for (let i in data) {
                    v = this.getValue(data[i]);
                    if(v) {
                        temp.push(v);
                    }
                }
                return temp.join(', ');
                break
            case 'string':
            case 'number':
            case 'bigint':
                return data;
        }
        return '';
    }

    /**
     * Return a string MarkUp value for cards.
     * @param data
     * @returns string
     */
    this.getCardMu = function (data) {
        if(typeof data == 'string') {
            return `<div class="e-text">${data}</a></div>`;
        }

        if(typeof data == 'object') {
            if (data.type == 'file_upload' || data.modelName == 'File') {
                let props = null;
                if (typeof data.value === 'object' && typeof data.value.properties === 'object') {
                    props = data.value.properties;
                } else if (typeof data.properties === 'object') {
                    props = data.properties;
                }

                if (!props) {
                    return '';
                }
                if (typeof props.not_exist !== 'undefined') {
                    return `<div class="e-file dimmed"><img src="${props.icon_url}" alt="${props.icon_name}" />&nbsp;${props.name}</a></div>`;
                }

                return `<div class="e-file"><a href="index.php?launch=documents&amp;documents=getfile&amp;getfile=${props.model_id}&amp;file=${props.id}"><img src="${props.icon_url}" alt="${props.icon_name}" />&nbsp;${props.name}</a></div>`;
            }
        }
        return '';
    }

    /**
     * Returns the formatted string for the deadline date in accordance to data.deadline_format.
     * the data.deadline_format should be passed to the dashlet params on initialisation
     *
     * @param data - the deadline value
     * @returns {*|string}
     */
    this.getDeadline = function (data) {
        if(!data || !dashKanban.data.deadline_format){
            return data ? data : '';
        }
        let date = parseISODate(data);
        if (date) {
            return date.format(dashKanban.data.deadline_format);
        } else if (data.match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4}$/)) {
            //the date has been formatted already dd.mm.yyyy
            date = parseFormattedDate(data);
            return date.format('d.m.Y');
        }
    }

    /**
     * Returns an image url/src to the status icon from a given status id
     *
     * @param $statusId
     * @returns {string|string}
     */
    this.statusId2IconPath = function ($statusId) {
        if(this.kbStatuses){
            for(const s in this.kbStatuses) {
                if(this.kbStatuses[s].status == $statusId) {
                    return this.kbStatuses[s].icon ? this.statusIconPath + this.kbStatuses[s].icon : '';
                }
            }
        }
    }

    /**
     * Returns a tags markup from a given tag ids. This is a helper for the card template
     *
     * @param tagIds
     * @returns {string}
     */
    this.getTags = function (tagIds) {
        let tagDiv = '', color;
        for (let i in tagIds) {
            if(dashKanban.data.tags[tagIds[i]]) {
                color = '';
                if(dashKanban.data.tags[tagIds[i]].color) {
                    color = `e-card-tag-color e-card-tag-color-${dashKanban.data.tags[tagIds[i]].color} ${dashKanban.data.tags[tagIds[i]].color}_pushpin`;
                }
                tagDiv += `<div class="e-card-tag-field e-tooltip-text ${color}">${dashKanban.data.tags[tagIds[i]].name}</div>`;
            }
        }
        if(tagDiv) {
            tagDiv = `<div class="e-card-tags">${tagDiv}</div>`;
        }
        return tagDiv;
    }

    /**
     * Returns asignments markyp from given asignees array. This is a helper for the card template
     *
     * @param assignees
     * @returns {string}
     */
    this.getAssignment = function (assignees) {
        let tagDiv = '', currentUserClass;
        for (let i in assignees) {
            let name = assignees[i].assigned_to_name;
            if(name) {
                let s = name.split(' ');
                s.forEach((e,i,a) => {
                    a[i] = e.substring(0,1);
                });
                s.splice(2,s.length-2);
                currentUserClass = dashKanban.data.currentUser.id === assignees[i].assigned_to ? ' current-user' : '';
                tagDiv += `<div class="e-card-avatar${currentUserClass}" data-id="${assignees[i].assigned_to}"><div class="e-card-tag-field e-tooltip-text" title="${name}">` + s.join('').toUpperCase() + '</div></div>';
            }
        }
        return tagDiv;
    };

    /**
     * Decorates a card with colorful stuff in accordance with the passed card data.
     *
     * @param element -  document.getElementsByClassName('e-card-id-' + card.id )[0].parent
     * @param card - the entire card object as retreaved from WebSocket server
     * @param now_ms [optional] - number of seconds until unix epoch. This is used for optimisation when using this function in loops
     */
    this.decorateCard = (function(element, card, now_ms) {
        if(!element || element.length === 0) {
            return;
        }
        if(!now_ms) {
            now_ms = (new Date()).getTime();
        };

        if(!isNaN(card._deadline)) {
            card._timeLeft = Math.round((card._deadline - now_ms) / 60 / 1000);
            //dashKanban._log(card.id, card._timeLeft, card._deadline, this.data.time_cri, this.data.time_high, this.data.time_med);
            if (card._timeLeft < this.data.time_cri
                && (card_urg = element.querySelectorAll('.e-card-urgency:not(.e-card-urgency-cri)')).length) {
                //dashKanban._log('time_cri', this.data.time_cri, card_urg);
                card_urg[0].removeClassName('e-card-urgency-high');
                card_urg[0].removeClassName('e-card-urgency-med');
                card_urg[0].removeClassName('e-card-urgency-low');
                card_urg[0].addClassName('e-card-urgency-cri');
            }
            if (card._timeLeft < this.data.time_high && card._timeLeft > this.data.time_cri
                && (card_urg = element.querySelectorAll('.e-card-urgency:not(.e-card-urgency-high)')).length) {
                //dashKanban._log('time_high', this.data.time_high, card_urg);
                card_urg[0].removeClassName('e-card-urgency-cri');
                card_urg[0].removeClassName('e-card-urgency-med');
                card_urg[0].removeClassName('e-card-urgency-low');
                card_urg[0].addClassName('e-card-urgency-high');
            }
            if (card._timeLeft < this.data.time_med && card._timeLeft > this.data.time_high
                && (card_urg = element.querySelectorAll('.e-card-urgency:not(.e-card-urgency-med)')).length) {
                //dashKanban._log('time_med', this.data.time_med, card_urg);
                card_urg[0].removeClassName('e-card-urgency-cri');
                card_urg[0].removeClassName('e-card-urgency-high');
                card_urg[0].removeClassName('e-card-urgency-low');
                card_urg[0].addClassName('e-card-urgency-med');
            }
            //dashKanban._log(card._timeLeft > this.data.time_med, (card_urg = document.querySelectorAll('.e-card[data-key="' + card.id + '"] .e-card-urgency:not(.e-card-urgency-low)')).length);
            if(card._timeLeft > this.data.time_med
                && (card_urg = element.querySelectorAll('.e-card-urgency:not(.e-card-urgency-low)')).length) {
                //dashKanban._log('time_low', this.data.time_med, card_urg);
                card_urg[0].removeClassName('e-card-urgency-cri');
                card_urg[0].removeClassName('e-card-urgency-high');
                card_urg[0].removeClassName('e-card-urgency-med');
                card_urg[0].addClassName('e-card-urgency-low');
            }
        }
    });

    /**
     * A time tick function that should run in regular intervals, or can be used to refresh the decorations of all cards
     */
    this.tick = (function() {
        const now_ms = (new Date()).getTime();
        let element;
        for(const card of this.dataSource) {
            element = document.getElementsByClassName('e-card-id-' + card.id );
            if(element.length !== 0) {
                this.decorateCard(element[0].parent, card, now_ms);
            }
        }
    }).bind(this);

    // Tick every 10 sec
    if (this.data.time_urgency) {
        setInterval(this.tick, 30000);
    }


    /**
     * Open a light box with a form for changing tags
     *
     * @param module
     * @param form
     * @param messages_container
     * @param func
     */
    this.changeTags = (function (module, form, messages_container, func) {
        Effect.Center('loading');
        Effect.Appear('loading');

        let ft = typeof(form), method, params = {}, url, id;

        if (!ft.match(/object/)) {
            //we will just display form
            method = 'get';
            id = parseInt(form);
            url = env.base_url + '?' + env.module_param + '='+module+'&'+module+'=ajax_tag&id=' + parseInt(form);
        } else {
            //we have to save the data
            method = 'post';
            url = env.base_url + '?' + env.module_param + '='+module+'&'+module+'=tag&tag=' + parseInt(form);
            params = Form.serialize(form);
        }
        let opt = {
            method: method,
            parameters: params,
            onSuccess: function(t) {
                var result = t.responseText;

                Effect.Fade('loading');

                if (method === 'get' && result) {
                    //show form in a lightbox
                    lb = new lightbox({
                        content: result,
                        title: i18n['labels']['tags_change'],
                        icon: 'tag.png',
                        width: 600
                    });
                    lb.params.saveFunction = function(){
                        dashKanban.changeTags('documents', this.form);
                    };
                    lb.params.messageContainer = messages_container;
                    if (func) {
                        //save callback in the lightbox object
                        lb.params.successFunc = typeof func == 'function' ? func : function () { func(); };
                        lb.params.id = id;
                    }

                    lb.activate();
                    // enable some common toggling functionality
                    initSystemSettingsBox();
                    initHelpBox();
                } else if (method === 'post' && result) {
                    if (lb.params.successFunc) {
                        //we have callback registered - invoke it
                        try {
                            lb.params.successFunc(lb.params.id);
                        } catch(e) {
                            alert(e);
                        }
                    }
                    lb.deactivate();
                }
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        }

        new Ajax.Request(url, opt);
    }).bind(this);

    // Release the stack by decoupling the execution.
    // Start connecting after 1ms delay
    setTimeout(this.connectToWs.bind(this),1);
}



