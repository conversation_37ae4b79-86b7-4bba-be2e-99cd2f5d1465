<?php

class Custom_View_Viewer extends Viewer {

    public function __construct(&$registry, $is_main) {
        $this->template = 'view.html';
        $this->pluginName = 'kanban';

        parent::__construct($registry, $is_main);
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir        = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl        = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir     = $this->pluginDir . 'templates/';

        $this->modelsDir        = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir       = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir   = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir          = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir       = $this->pluginDir . 'javascript/';
        $this->scriptsUrl       = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {

        $this->prepareTitleBar();

        $registry = &$this->registry;
        $this->model = $this->registry->get('dashlet');

        //prepare the data for the template

        require_once $this->modelsDir . 'dashlets.factory.php';
        require_once $this->modelsDir . 'dashlets.dropdown.php';

        //prepare group
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $group = Groups::searchOne($this->registry, array('where' => array('g.id = ' . $this->model->get('group')),
                                                          'sanitize' => true));
        if ($group) {
            $this->data['group'] = $group->get('name');
        } else {
            $this->data['group'] = 0;
        }

        //prepare actions
        $params = array('get_one' => $this->pluginName);
        $plugin = Dashlets::getPlugins($registry, $params);

        //get the model
        $this->model = $this->registry->get('dashlet');

        // dashlet plugin filters
        $display_plugin_filters = array();

        $available_display_values = array(5,10,25,50,75,100);
        foreach ($available_display_values as $avb_val) {
            $display_plugin_filters[] = array(
                'label'        => $avb_val,
                'option_value' => strval($avb_val),
            );
        }

        //assign data for template
        $this->data['dashlet'] = $this->model;
        $this->data['display_plugin_filters'] = $display_plugin_filters;
        $this->data['module_name_i18n'] = $plugin[$this->pluginName]['label'];
    }

    public function prepareTitleBar() {
        $title = $this->i18n('dashlets_view');
        $this->data['title'] = $title;
    }
}

?>
