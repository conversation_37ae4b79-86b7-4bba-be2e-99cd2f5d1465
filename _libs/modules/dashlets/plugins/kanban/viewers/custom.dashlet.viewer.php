<?php

class Custom_Dashlet_Viewer extends Viewer {

    public function __construct(&$registry, $is_main = false) {
        $this->template = 'dashlet.html';
        $this->pluginName = 'kanban';

        parent::__construct($registry, $is_main);
        $this->setFrameset('frameset_blank.html');
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir        = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl        = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir     = $this->pluginDir . 'templates/';

        $this->modelsDir        = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir       = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir   = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir          = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir       = $this->pluginDir . 'javascript/';
        $this->scriptsUrl       = $this->pluginUrl . 'javascript/';

        $this->cssDir       = $this->pluginDir . 'css/';
        $this->cssUrl       = $this->pluginUrl . 'css/';

        return true;
    }

    public function prepare() {

        $registry = &$this->registry;

        //prepare the data for the template
        $dashlet = $registry->get('dashlet');
        $dashlet_settings = '';

        // get the current dashlet properties to get settings
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $filter_dashlet_plugin = array('get_one' => $this->pluginName);
        $dashlet_plugin = Dashlets::getPlugins($registry, $filter_dashlet_plugin);
        $dashlet_settings = $dashlet_plugin[$this->pluginName]['settings'];


        require_once $this->pluginDir . 'models/custom.model.php';
        $custom_dashlet_model = new Custom_Model($dashlet_settings);
        $custom_dashlet_model->prepareCustomData($dashlet);

        $this->data['dashlet'] = $dashlet;
        $this->data['pluginDir'] = $this->pluginDir;
        $this->data['scripts_url'] = array(
            $this->scriptsUrl . 'custom.js'
        );
        $this->data['css_files'] = [
            $this->cssDir . 'custom.css'
        ];
        $include_gt2 = $custom_dashlet_model->get('include_gt2');
        if($include_gt2 && ($include_gt2 === '1' || $include_gt2 === 'true')) {
            $this->data['scripts_url'][] =  PH_JAVASCRIPT_URL . '_gt2.js';
        }

        $this->data['scriptsUrl'] = $this->scriptsUrl;

    }
}

?>
