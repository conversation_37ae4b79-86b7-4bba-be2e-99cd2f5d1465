{* Look the link bellow to know why DEFER is used *}
{* http://msdn.microsoft.com/en-us/library/ms533897.aspx *}
<form method="post"
      id="work_leaving_form_search"
      onsubmit="return false;"
      style="background: none repeat scroll 0 0 #EDEDED;"
      name="work_leaving_form_search"
      action="{$smarty.server.PHP_SELF}?{$module_param}=dashlets&amp;dashlets=custom_action&amp;custom_plugin_action=preparePaymentData&amp;plugin={$dashlet->get('controller')}&amp;dashlet={$dashlet->get('id')}">
  <input id="work_leaving_form_dashlet_plugin" name="work_leaving_form_dashlet_plugin" value="{$dashlet_plugin}" type="hidden" />
  <input id="work_leaving_form_dashlet_plugin_id" name="work_leaving_form_dashlet_plugin_id" value="{$dashlet_id}" type="hidden" />
  {foreach from=$scripts_url item='script_url'}
    <script defer="defer" src="{$script_url}?{$system_options.build}"></script>
  {/foreach}
  <table cellspacing="0" cellpadding="0" border="0">
    <tr>
      <td>
        <table>
          <tr>
            <td class="labelbox" style="width: 250px!important; padding-bottom: 0px; padding-left: 5px;" colspan="2">{#plugin_dermavita_work_leaving_schedule_patient#|escape}</td>
            <td class="labelbox" style="padding-bottom: 0px; padding-left: 10px;">{#plugin_dermavita_work_leaving_schedule_notes#|escape}</td>
          </tr>
          <tr>
            <td style="border: 0!important; width: 250px; padding-top:0; padding-left: 5px;" colspan="2">
              {include file="input_autocompleter.html"
                standalone=true
                width=$filters.patient.width
                name=$filters.patient.name
                custom_id=$filters.patient.custom_id
                options=$filters.patient.options
                autocomplete=$filters.patient.autocomplete
                value_autocomplete=$filters.patient.value_autocomplete
                autocomplete_var_type='basic'
                autocomplete_type=$filters.patient.autocomplete_type
                autocomplete_var_type='basic'
                autocomplete_buttons = $filters.patient.autocomplete_buttons
                label=$filters.patient.label
                help=$filters.patient.help}
            </td>
            <td style="border: 0!important; padding-top:0; padding-left: 10px;" rowspan="5">
              {include file="input_textarea.html"
                standalone=true
                name=$filters.notes.name
                custom_id=$filters.notes.custom_id
                width=$filters.notes.width
                height=$filters.notes.height
                readonly=$filters.notes.readonly
                disabled=$filters.notes.disabled
                label=$filters.notes.label
                help=$filters.notes.help}
            </td>
          </tr>
          <tr>
            <td class="labelbox" style="width: 120px!important; padding-bottom: 0px; padding-left: 5px;">{#plugin_dermavita_work_leaving_schedule_discount#|escape}</td>
            <td class="labelbox" style="width: 120px!important; padding-bottom: 0px; padding-left: 5px;">{#plugin_dermavita_work_leaving_schedule_balance#|escape}</td>
          </tr>
          <tr>
            <td style="border: 0!important; padding-top:0; padding-left: 5px;">
              {include file="input_text.html"
                standalone=true
                name=$filters.discount.name
                custom_id=$filters.discount.custom_id
                width=$filters.discount.width
                readonly=$filters.discount.readonly
                disabled=$filters.discount.disabled
                label=$filters.discount.label
                help=$filters.discount.help}
            </td>
            <td style="border: 0!important; padding-top:0; padding-left: 5px;">
              {include file="input_text.html"
                standalone=true
                name=$filters.balance.name
                custom_id=$filters.balance.custom_id
                width=$filters.balance.width
                readonly=$filters.balance.readonly
                disabled=$filters.balance.disabled
                label=$filters.balance.label
                help=$filters.balance.help}
            </td>
          <tr>
            <td class="labelbox" style="width: 250px!important; padding-bottom: 0px; padding-left: 5px;" colspan="2">{#plugin_dermavita_work_leaving_schedule_visits#|escape}</td>
          </tr>
          <tr>
            <td style="border: 0!important; padding-left: 5px; padding-top:0;" colspan="2">
              {include file="input_dropdown.html"
                standalone=true
                name=$filters.visits.name
                custom_id=$filters.visits.custom_id
                options=$filters.visits.options
                onchange='loadVisitInformation(this)'
                width=$filters.visits.width
                readonly=$filters.visits.readonly
                disabled=$filters.visits.disabled
                label=$filters.visits.label
                help=$filters.visits.help}
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr style="display: none;">
      <td id="container_work_leaving_form_visit_information"></td>
    </tr>
    <tr>
      <td style="padding: 3px 0 3px 5px;">
        <input type="button" class="button" id="wlf_add_button" onclick="editWorkLeavingFormGT2(this);" style="display: none;" value="{#edit#|escape}" />
        <input type="button" class="button" id="wlf_pay_button" onclick="showPaymentForm(this);" style="display: none; float: right;" value="{#plugin_dermavita_work_leaving_schedule_payment#|escape}" />
      </td>
    </tr>
  </table>
</form>
