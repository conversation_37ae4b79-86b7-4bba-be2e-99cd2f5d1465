/*
 * Fuction to manage dropdown dependences between the action and the module
 * @param element - the element which call the function
*/
function setDependences(element) {
    
    var element = $(element);
    var index = element.id.replace(/[^\d].*_(\d+)/, '$1');
    
    //change dependants
    currency = element.value;
    $('currency_units_' + index).value = currencies[currency].units + ' ' + currency;
    $('rate_' + index).value = currencies[currency].rate + ' BGN';

}
