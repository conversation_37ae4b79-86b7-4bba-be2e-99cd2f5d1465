if (env.current_lang == 'bg') {
    i18n['messages']['error_empty_required_fields'] = 'Моля попълнете всички задължителни полета!';
    i18n['messages']['error_invalid_dates'] = 'Не може Край да е преди Начало! Моля, попълнете коректни стойности за Начало и Край!';
    i18n['messages']['error_incorrect_step'] = 'Не може да се разпознае стъпката!';
} else {
    i18n['messages']['error_empty_required_fields'] = 'Please, fill in all the mandatory fields!';
    i18n['messages']['error_invalid_dates'] = 'The start date is after the end date! Please, correct the date sequence!';
    i18n['messages']['error_incorrect_step'] = 'Incorrect step!';
}

/**
 * Object for the current dashlet
 */
var dashletWienerbergerMeetingProtocol = {
    /**
     * Set some defaults for the dashlet
     *
     * @param dashlet_id - the ID of the current dashlet
     * @param plugin_name - the name of the current dashlet plugin
     * @param custom_style_url - URL for custom dashlet css file
     * @return boolean true|false
     */
    setDefaults: function (dashlet_id, plugin_name, custom_style_url) {
        // Set the dashlet ID
        this.id = dashlet_id;

        this.custom_actions_url = env.base_url + '?' + env.module_param + '=dashlets' +
            '&dashlets=custom_action' +
            '&plugin=' + plugin_name +
            '&dashlet=' + this.id +
            '&custom_plugin_action=';

        this.container = $('dashlet_' + dashlet_id + '_container');

        var link = document.createElement('link');
        link.type = 'text/css';
        link.rel = 'stylesheet';
        link.href = custom_style_url;
        $$('head')[0].appendChild(link);

        this.document_id = '';

//        $('title_text_dashlet_' + this.id).onclick = function () {
//            dashletWienerbergerMeetingProtocol.toggleFullScreen();
//        };

        return true;
    },

    load: function (url_extend, container) {
        // Show loading effect
        Effect.Center('loading');
        Effect.Appear('loading');

        var url = this.custom_actions_url + url_extend;
        var opt = {
            cstm_container: container,
            method:    'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var container = t.request.options.cstm_container;
                container.innerHTML = t.responseText;

                var scripts = container.getElementsByTagName('script');
//                scripts.each(function (scr) {ajaxLoadJS(scr)});
                for (var i = 0; i < scripts.length; i++) {
                    ajaxLoadJS(scripts[i]);
                }

                // Fade the loading effect
                Effect.Fade('loading');
            },
            on404: function(t) {
                Effect.Fade('loading');
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                Effect.Fade('loading');
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        // Execute the AJAX
        new Ajax.Request(url, opt);
    },

    exec: function (url_extend, element) {
        if (!element.form) {
            return false;
        }

        // Prevent from multi click
        element.disable();

        var parameters = Form.serialize(element.form);

        // Show loading effect
        Effect.Center('loading');
        Effect.Appear('loading');

        var url = this.custom_actions_url + url_extend;
        var opt = {
            cstm_container: element.form.parentNode,
            method: 'post',
            parameters: parameters,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                eval('var result = ' + t.responseText + ';');
                var container = t.request.options.cstm_container;
                container.innerHTML = result.html;

                var scripts = container.getElementsByTagName('script');
                for (var i = 0; i < scripts.length; i++) {
                    ajaxLoadJS(scripts[i]);
                }

                // Fade the loading effect
                Effect.Fade('loading');

                if (result.messages.error) {
                    alert(result.messages.error);
                } else if (result.messages.message) {
                    alert(result.messages.message);
                }
            },
            on404: function(t) {
                Effect.Fade('loading');
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                Effect.Fade('loading');
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        // Execute the AJAX
        new Ajax.Request(url, opt);
    },

    showHome: function () {
        this.load('showHome', this.container);
        this.document_id = '';
    },

    showAddMeetingProtocol: function (type) {
        this.load('addMeetingProtocol&type=' + type, this.container);
    },

    _checkMeetingProtocol: function(element) {
        if (element.form.meeting_date.value.trim() == ''
                || element.form.meeting_place.value.trim() == ''
                || element.form.customer.value.trim() == ''
                || element.form.topic_meeting.value.trim() == '') {
            alert(i18n['messages']['error_empty_required_fields']);
            return false;
// TODO: this check should not be in JS because there is such check in PHP
//       remove this check and make the PHP check to correctly show the error message
        } if (element.form.meeting_finish.value < element.form.meeting_start.value) {
            alert(i18n['messages']['error_invalid_dates']);
            return false;
        }

        return true;
    },

    addMeetingProtocol: function (element) {
        if (this._checkMeetingProtocol(element)) {
            this.exec('addMeetingProtocol', element);
//            this.showHome();
        } else {
            return false;
        }
    },

    cancelMeetingProtocol: function () {
        this.showHome();
    },

    selectTab: function (element) {
        element.parentNode.childElements().each(function (el) {
            removeClass(el, 'selected');
            if (el.getAttribute('data-content-id')) {
                $(el.getAttribute('data-content-id')).style.display = 'none';
            }
        });
        addClass(element, 'selected');
        $(element.getAttribute('data-content-id')).style.display = '';
    },

    getNextActionHome: function () {
        this.load('getNextActionHome&document_id=' + this.document_id, $('dashlet_' + this.id + '_meetingnextaction_content'));
    },

    openMeetingProtocol: function (document_id) {
        this.document_id = document_id;
        this.load('editMeetingProtocol&document_id=' + document_id, this.container);
    },

    editMeetingProtocol: function (element) {
        if (this._checkMeetingProtocol(element)) {
            this.exec('editMeetingProtocol', element);
//            this.showHome();
        } else {
            return false;
        }
    },

    selectNextStep: function (step) {
        var url_extend = 'selectNextStep&document_id=' + this.document_id + '&step=' + step;
        var container  = $('dashlet_' + this.id + '_meetingnextaction_content');
        this.load(url_extend, container);
    },

    selectExistingStep: function (step, step_id) {
        var url_extend = 'selectNextStep&document_id=' + this.document_id + '&step=' + step + '&step_id=' + step_id;
        var container  = $('dashlet_' + this.id + '_meetingnextaction_content');
        this.load(url_extend, container);
    },

    _checkNextStep: function (element) {
        var result = true;
        var step = element.form.step.value;
        if (step) {
// TODO: message should not be hardcoded
            var msg = i18n['messages']['error_empty_required_fields'];
            switch (step) {
                case 'nointerest':
                    if (element.form.content.value.trim() == '') {
                        result = false;
                    }
                    break;
                case 'call':
                    if (element.form.event_date.value.trim() == ''
                        || element.form.event_time.value.trim() == ''
                            || element.form.location.value.trim() == ''
                                || element.form.description.value.trim() == ''
                                    || element.form.select('input[name^="event_employee["]').filter(function(n) {return n.value.trim() != '';}).length == 0) {
                        result = false;
                    }
                    break;
                case 'meeting':
                    if (element.form.event_date.value.trim() == ''
                        || element.form.event_time.value.trim() == ''
                            || element.form.location.value.trim() == ''
                                || element.form.description.value.trim() == ''
                                    || element.form.select('input[name^="event_employee["]').filter(function(n) {return n.value.trim() != '';}).length == 0
                                    || element.form.event_duration.value.trim() == '') {
                        result = false;
                    }
                    break;
                case 'info':
                    if (element.form.reminder_user_id.value.trim() == ''
                        || element.form.reminder_date.value.trim() == ''
                            || element.form.custom_message.value.trim() == '') {
                        result = false;
                    }
                    break;
                case 'minitask':
                    if (element.form.assigned_to.value.trim() == ''
                            || element.form.dashlet_wb_meeting_protocol_deadline.value.trim() == ''
                            || element.form.severity.value.trim() == ''
                            || element.form.description.value.trim() == '') {
                        result = false;
                    }
                    break;
            }
            if (!result) {
                alert(msg);
            }
        } else {
            result = false;
            alert(i18n['messages']['error_incorrect_step']);
        }

        return result;
    },

    addNextStep: function (element) {
        if (this._checkNextStep(element)) {
            this.exec('addNextStep', element);
        }
    },

    cancelNextStep: function () {
        this.getNextActionHome();
    },

//    toggleFullScreen: function () {
//        var dashlet = $('dashlet_' + this.id);
//        if (dashlet.webkitIsFullScreen) {
//            dashlet.webkitCancelFullScreen();
//        } else {
//            dashlet.webkitRequestFullScreen();
//        }
//    }
};