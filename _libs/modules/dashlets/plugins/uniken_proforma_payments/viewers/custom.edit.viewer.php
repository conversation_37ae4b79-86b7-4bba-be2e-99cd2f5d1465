<?php

class Custom_Edit_Viewer extends Viewer {

    public function __construct(&$registry, $is_main) {
        $this->template = 'edit.html';
        $this->pluginName = 'uniken_proforma_payments';

        $this->data['model_langs'] = $registry['config']->getParamAsArray('i18n', 'model_langs');

        parent::__construct($registry, $is_main);
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir = $this->pluginDir . 'templates/';

        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir      = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir = $this->pluginDir . 'javascript/';
        $this->scriptsUrl = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {

        $this->prepareTitleBar();

        $registry = &$this->registry;

        //prepare the data for the template
        require_once $this->modelsDir . 'dashlets.factory.php';
        $params = array('get_one' => $this->pluginName);
        $plugin = Dashlets::getPlugins($registry, $params);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($registry);

        //get the model
        $this->model = $this->registry->get('dashlet');
        if ($this->model->get('filters')) {
            foreach ($this->model->get('filters') as $k => $v) {
                $this->model->set($k, $v, true);
            }
        }
        //assign data for template
        $this->data['dashlet'] = $this->model;
        $this->data['module_name_i18n'] = $plugin[$this->pluginName]['label'];
    }

    public function prepareTitleBar() {
        $title = $this->i18n('dashlets_edit');
        $this->data['title'] = $title;
    }
}

?>
