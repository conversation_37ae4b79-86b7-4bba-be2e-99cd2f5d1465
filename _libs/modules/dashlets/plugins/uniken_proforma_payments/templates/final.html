<div class="t_table" style="padding: 10px;">
{include file=message.html items=$messages->getErrors() display='error'}
{include file=message.html items=$messages->getWarnings() display='warning'}
{include file=message.html items=$messages->getMessages() display='message'}

<br/>
{if !$messages->getErrors() && $invoice_id}
<a href="{$url_base}view&amp;view={$invoice_id}" target="_blank" style="font-size: 12px;">{#plugin_show_invoice#}</a><br/>
{if $pattern}
<a href="{$url_base}print&amp;print={$invoice_id}&amp;pattern={$pattern}" target="_blank" style="font-size: 12px;">{#plugin_print_invoice#}</a><br/>
{/if}
{/if}
<br/>
<button class="button" onclick="dashletsLoad('content_dashlet_{$dashlet}', 'plugin', '{$plugin}', '{$dashlet}');">{#plugin_new_invoice#}</button>
<br/><br/>
</div>