/**
 * Declare plugin functionality as a function
 */
Bgs_Tickets = typeof Bgs_Tickets !== 'undefined' ? Bgs_Tickets : function(dashlet_id, plugin, data) {

    /* ***********   private functionality start   ********** */

    var dashlet_id = dashlet_id,
        plugin = plugin;

    /**
     * @type {String} URL to use for AJAX requests to plugin. Append name of
     * plugin method to be executed, then other parameters can be specified as
     * further key-value pairs in query string. (internal variable)
     */
    var base_url = env.base_url + '?' +
        env.module_param + '=dashlets&dashlets=custom_action' +
        '&dashlet=' + dashlet_id +
        '&plugin=' + plugin +
        // force flag is used in order to execute method even when dashlet_id is not set
        '&force=1' +
        '&custom_plugin_action=';

    /**
     * Outermost container identifying scope of plugin - dashlet container.
     */
    var dashlet_container = $('content_dashlet_' + dashlet_id);

    /**
     * Outermost container identifying scope of plugin - lightbox layer container.
     */
    var container = null;
    if (lb && lb.active && lb.params) {
        container = $(lb.params.uniqid);
    }

    // inject custom CSS into head of document
    var link = document.createElement("link");
    link.type = 'text/css';
    link.rel = 'stylesheet';
    (dashlet_container || container).select('script[src]').each(function(s) {
        if (s.src.match(/javascript\/custom\.js/)) {
            link.href = s.src.replace('javascript/custom.js', 'styles/custom.css');
        }
    });
    $$('head')[0].appendChild(link);

    //toggle autocompleters
    toggleAutocompleter('ticket_customer');


    /**
     * Internal function to define container element to work within
     * (in lightbox it will be the div of the active lightbox)
     *
     * @param {Object} event - window event
     */
    function defineContainer(event) {
        if (!event) {
            event = window.event;
        }
        if (event) {
            var el = event.target || event.srcElement || event.originalTarget;
            if (el && typeof $(el).up !== 'undefined' && $(el).up('.lb_content')) {
                container = $(el).up('.lb_content');
            }
        }

        return container;
    }

    /**
     * Defines optimal width for lightbox container (the div of the active
     * lightbox) according to screen width
     *
     * @return {number} - width in pixels
     */
    function defineContainerWidth() {
        var container_width = Math.max(Math.min(Math.min(window.screen.availWidth, window.document.body.clientWidth) - 60, 1350), 768);
        if (container_width > 768) {
            container_width += 'px';
        } else {
            container_width = 'auto';
        }
        return container_width;
    }

    /**
     * Internal function to define container element to load additional forms into
     *
     * @param {Object} event - window event
     * @return {Object} - container element
     */
    function defineFormContainer(event) {
        var target = defineContainer(event);
        if (target) {
            target = $(target).down('.details_container.absolute');
            if (target) {
                // all forms will load in the same container, replacing each other,
                // so we don't end up with multiple elements with the same id
                // (especially the CKEditor-related fields)
                var class_name = 'form_container';

                if (!target.down(' > div.' + class_name)) {
                    target.insert({'bottom': Builder.node('div', {'class': class_name})});
                }
                target = target.down('> div.' + class_name);

                target = $(target);
            }
        }

        return target;
    }

    /**
     * Format and display notifications as fixed notification (panel at the top
     * of page)
     *
     * @param {string|Array} content - content to display (HTML-formatted text
     *          or array of plain texts)
     * @param {string} message_type - error, warning or message
     */
    function displayResultNotification(content, message_type) {
        if (typeof content !== 'string') {
            content = displayNotificationFixed.formatContent(content, message_type || 'error');
        }
        displayNotificationFixed(content);
    }

    /**
     * General function for performing AJAX requests from plugin (calling custom
     * methods of plugin).
     * Simple mode uses ajaxUpdater to load response into target container,
     * complex mode creates an AJAX request and has own processing of response.
     *
     * @param {Object} params - action, optional target, additional data etc.
     * @return {boolean} - always return false
     */
    function load(params) {
        // action is required
        if (!(params.action || params.url)) {
             return;
        }

        if (!params.skip_toggle_buttons) {
            // disable all buttons
            toggleButtonsActive(container, false);
        }

        // prepare default values
        var target = container,
            link = params.url ? params.url : base_url + params.action;
        // get container to load result into if not the default one
        if (params.target) {
            if (Object.isString(params.target)) {
                // specified as an id
                target = container.select('#' + params.target);
                if (target.length) {
                    target = target[0];
                } else {
                    return;
                }
            } else {
                // specified as a DOM element
                target = params.target;
            }
        }
        if (!params.execute_after && !params.skip_toggle_buttons) {
            // enable buttons when response has been received
            params.execute_after = function() {
                toggleButtonsActive(container, true);
            }.bind(this);
        }

        // prepare parameters if there are any
        var parameters = '';
        // add form data if available
        if (params.form) {
            parameters = Form.serialize(params.form);
        }
        // add data object if available
        if (params.data) {
            if (parameters) {
                parameters += '&';
            }
            parameters += Object.toQueryString(params.data);
        }

        if (params.complex) {
            // create our own AJAX request
            Effect.Center('loading');
            Effect.Appear('loading');

            var opt = {
                parameters: parameters,
                onSuccess: function(t) {
                    if (!checkAjaxResponse(t.responseText)) {
                        toggleButtonsActive(container, true);
                        return false;
                    }
                    // response might be JSON-encoded or plain text
                    // prepare it as an object
                    var result = t.responseText;
                    if (String.prototype.isJSON.call(result)) {
                        result = String.prototype.evalJSON.call(result);
                        if (typeof result.content === 'undefined' && typeof result.data === 'string') {
                            result.content = result.data;
                        }
                    }
                    if (typeof result === 'string') {
                        result = {
                            content: result
                        };
                    }

                    if (result) {
                        if (typeof result.content !== 'undefined') {
                            target.innerHTML = result.content;
                            target.select('script').each(ajaxLoadJS);
                        }
                        // display success and/or error messages
                        if (result.messages) {
                            displayResultNotification(result.messages, 'message');
                        }
                        if (result.errors) {
                            displayResultNotification(result.errors, 'error');
                        }
                        if (result.scripts) {
                            result.scripts.each(eval);
                        }
                        // activate buttons
                        if (typeof params.execute_after !== 'undefined' && Object.isFunction(params.execute_after)) {
                            params.execute_after(params, result);
                        }
                    }

                    lb.setModal(true);

                    Effect.Fade('loading');
                }.bind(this),
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            };

            new Ajax.Request(link, opt);
        } else {
            // use the existing functionality for loading content
            ajaxUpdater({
                link: link,
                target: target,
                parameters: parameters,
                execute_after: params.execute_after || null
            });
        }

        return false;
    }

    /**
     * Enables/disables all buttons in dashlet
     *
     * @param {Object} selector_container - container to process buttons in (dashlet or lightbox)
     * @param {boolean} enabled - true to enable, false to disable
     * @param {string} selector - optional selector when only specific button should be toggled
     * @param {boolean} force - force activation and deactivation
     */
    function toggleButtonsActive(selector_container, enabled, selector, force) {
        selector_container = selector_container || container;
        if (!selector_container) {
            return;
        }

        selector_container.select('button' + (selector || '')).each(function(btn) {
            if (enabled) {
                if (!btn.className.match(/force_inactive/) || force) {
                    btn.removeAttribute('disabled');
                    removeClass(btn, 'inactive');
                    if (force) {
                        removeClass(btn, 'force_inactive');
                    }
                }
            } else {
                btn.setAttribute('disabled', 'disabled');
                addClass(btn, 'inactive');
                if (force) {
                    addClass(btn, 'force_inactive');
                }
            }
        });
    }

    /* **********   private functionality end   ********** */


    /* ***********   public functionality start   ********** */

    /**
     * Execute after method of entry screen autocompleter.
     * All data of selected option is set into a hidden field in order to be
     * passed to form.
     *
     * @param {Object} autocomplete - autocompleter settings
     * @param {Object} data - data of selected autocomplete option
     */
    function setACData(autocomplete, data) {
        // text entered in autocompleter before selection of a suggestion
        data.search_string = autocomplete.ac.oldElementValue || '';
        var data_field = $$((autocomplete.scope || '') + ' #' + autocomplete.field.replace(/_autocomplete$/, '_data'))[0];
        if (data_field) {
            data_field.value = Object.toJSON(data);
        }
    }

    /**
     * Sets internal container variable to specified DOM element.
     * Used when opening lightbox for add/edit of document.
     *
     * @param {Object} element - DOM element or null
     */
    function setContainer(element) {
        container = element;
    }

    /**
     * Toggle autocmplete at entry screen autocompleter.
     *
     * @param {String} autocompleteId - id of the autocompleter
     */
    function toggleAutocompleter(autocompleteId) {
        if ($$('input[name="' + autocompleteId + '_filter"]').length == 0) {
            return;
        }
        $$('input[name="' + autocompleteId + '_filter"]').each(function(type) {
            if (type.value != "" && $(autocompleteId + '_' + type.value + '_autocomplete')) {
                $(autocompleteId + '_' + type.value + '_autocomplete').style.display = 'none';
            }
        });
        let selectedValue = $$('input:checked[name="' + autocompleteId + '_filter"]')[0].value;
        $(autocompleteId + '_' + selectedValue + '_autocomplete').style.display='';
    }

    /**
     * Toggle autocmplete at entry screen autocompleter.
     *
     * @param {Object} autocomplete - autocomlete settings
     * @param {Object} data - data after autocomplete search
     */
    function changeCustomerInfo(autocomplete, data) {

        load({
            complex: true,
            url: base_url + 'getCustomerInfo&use_ajax=1',
            target: container.down('.customers_info_container'),
            data: {
                customer: container.down('#customer').value,
            },
            execute_after: function(load_params, result) {
                container.down('.customer_additional_container').innerHTML = result.content_additional;
                this.getContact(1, container.down('#branch'));
            }.bind(this)
        });
    }

    /**
     * Linkifies the relatives (documents to be loaded in the same lightbox).
     *
     * @param {Object} autocomplete - autocomlete settings
     * @param {Object} data - data after autocomplete search
     */
    function linkifyRelatives() {
        container.select('.relatives .relative').each(
            function(relative) {
                relative.down('span').style.cursor = 'pointer';
                relative.observe(
                    'click',
                    function(event) {
                        //ToDo: confirm whether to replace content

                        var load_params = {
                            'id': relative.dataset.id,
                            'replace_content': true
                        }
                        this.loadScreen(relative, load_params);
                    }.bind(this)
                )
            }.bind(this)
        );

        var btn = container.down('.button.merge');
        if (btn) {
            btn.observe(
                'click',
                function(event) {
                    this.merge($(btn.dataset.mergeToField).value);
                }.bind(this)
            );
        }
    }

    /**
     * Loads lightbox screen for add/edit
     *
     * @param {mixed} element - trigger element or its id
     * @param {Object} params - parameters and data for lightbox
     */
    function loadScreen(element, params) {
        // validate data, customer selection is required
        if (typeof params === 'string') {
            params = String.prototype.toQueryParams.call(params);
        }
        // display error message and exit
        if (typeof params !== 'object') {
            displayResultNotification([i18n['messages']['alert_empty_field']], 'error');
            return;
        }

        // disable buttons in dashlet
        toggleButtonsActive(dashlet_container, false);

        new Ajax.Request(
            base_url + 'load',
            {
                method: 'post',
                parameters: Object.toQueryString(params),
                onSuccess: function(t) {
                    if (!checkAjaxResponse(t.responseText)) {
                        return;
                    }

                    // enable buttons in dashlet
                    toggleButtonsActive(dashlet_container, true);

                    // response might be JSON-encoded or plain text
                    // prepare it as an object
                    var result = t.responseText;
                    if (String.prototype.isJSON.call(result)) {
                        result = String.prototype.evalJSON.call(result);
                    } else {
                        result = {
                            content: result
                        };
                    }

                    if (result) {
                        if (typeof result.content !== 'undefined') {
                            if (lb && lb.active && lb.params.origin &&
                                (lb.params.origin === element || params.replace_content)) {
                                // update content in the same lightbox layer
                                lb.update(result);
                            } else {
                                // display lightbox
                                var default_width = defineContainerWidth();
                                lb = new lightbox({
                                    title: result.title || '',
                                    content: result.content,
                                    width: params.width || default_width,
                                    backgroundColor: '#f1f1f1',
                                    origin: $(element)
                                });
                                lb.activate();
                                lb.setModal(true);
                            }
                        }
                        // set flag that model has been updated
                        if (result.reload_origin_container) {
                            this.reload_origin_container = true;
                        }
                        // display success and/or error messages
                        if (result.messages) {
                            displayResultNotification(result.messages, 'message');
                        }
                        if (result.errors) {
                            displayResultNotification(result.errors, 'error');
                        }
                    }

                    Effect.Fade('loading');
                }.bind(this),
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            }
        );
        Effect.Center('loading');
        Effect.Appear('loading');
    }

    /**
     * Loads history activity container
     *
     * @param {number} id - id of current document
     */
    function loadHistoryActivity(id) {
        if (!id && container && container.down('input#id')) {
            id = container.down('input#id').value;
        }
        var target = container.down('.history_activity_container');
        if (!id || !target) {
            return;
        }
        load({
            complex: true,
            url: env.base_url + '?' + env.module_param + '=documents&documents=history&history=' + id + '&source=ajax&history_activity=1',
            container: container,
            target: target,
            execute_after: function(load_params, result) {
                //check if the communication controls are present
                if (container.down('.action_tabs .emails_add') === undefined) {
                    //the email add button is absent, do not add the Reply buttons
                    return;
                }

                //add buttons for reply and replyAll but only for email events
                load_params.target.select('.activity_data .content_container').each(function(contentContainer) {
                    var action = contentContainer.up('tr').down('td').className;
                    if (!action.match(/email/)) {
                        return;
                    }

                    //decode the audit to get the mail code
                    var audit = contentContainer.dataset.audit.evalJSON();

                    //create action container for the two buttons
                    var actions_container = new Element('div', {'class': 'action_tabs floatr'})
                    contentContainer.appendChild(actions_container);

                    //reply
                    var replyBtn = new Element(
                        'a', {
                            'title': i18n['labels']['reply'],
                            'class': 'reply',
                            'data-mail-code': audit.mail_code
                        });
                    replyBtn.observe(
                        'click',
                        function(event) {
                            this.replyEmail(event, load_params);
                        }.bind(this)
                    );

                    //replyAll
                    var replyAllBtn = new Element(
                        'a', {
                            'title': i18n['labels']['reply_all'],
                            'class': 'replyAll',
                            'data-mail-code': audit.mail_code
                        });
                    replyAllBtn.observe(
                        'click',
                        function(event) {
                            this.replyEmail(event, load_params);
                        }.bind(this)
                    );
                    actions_container.appendChild(replyBtn);
                    actions_container.appendChild(replyAllBtn);
                }.bind(this));

                /*
                // Remove the click to history
                load_params.target.observe(
                    'click',
                    function(event) {
                        historyActivityGoToAudit(event, load_params);
                    }
                );
                */
            }.bind(this),
            skip_toggle_buttons: true
        });
    }

    /**
     * Loads additional form for some email
     *
     * @param {Object} params - url to load and other parameters
     */
    function composeEmail(params) {
        //replace the "name" in the subject pattern
        let subject = params.subject.replace(/\[name\]/, container.down('#name').value)
        params.url += '&subject=' + encodeURIComponent(subject);
        this.loadInlineForm(params);
    }

    /**
     * Loads additional form for reply to email
     *
     * @param {Object} params - url to load and other parameters
     */
    function replyEmail(event, params) {
        event.stop();

        var model_id = params.container.down('#id').getValue();
        var btn = event.findElement();
        var mailCode = btn.dataset.mailCode;
        var reply = btn.hasClassName('replyAll') ? 'replyAll' : 'reply';
        var params = {
            'event': event,
            url: env.base_url + '?' + env.module_param + '=communications&communications=ajax_load_communication_add_panel'+
                '&type_record=email&model_id='+model_id+'&communication_type=emails'+
                '&module=documents'+
                '&action=add&inline_add=1' +
                '&'+reply+'=' + mailCode
        };
        Bgs_Tickets.loadInlineForm(params);
    }

    /**
     * Loads additional form for some action
     *
     * @param {Object} params - url to load and other parameters
     */
    function loadInlineForm(params) {
        var target = defineFormContainer(params.event);
        if (!params.url && params.action) {
            // construct URL using plugin base URL
            params.url = base_url + params.action +
                (params.data ? '&' + Object.toQueryString(params.data) : '') +
                (params.form ? '&' + Form.serialize(params.form) : '');
        }
        if (!target || !params.url) {
            return;
        }

        // load form
        load({
            complex: true,
            url: params.url,
            target: target,
            execute_after: function(load_params, result) {
                // enable all buttons
                toggleButtonsActive(container, true);

                // there is an error, exit
                if (typeof result === 'object' && typeof result.result !== 'undefined' && !result.result || typeof result.content === 'undefined') {
                    return;
                }

                // set flag that model has been updated
                if (result.reload_origin_container) {
                    this.reload_origin_container = true;
                }

                // display form container
                toggleFormContainer('show', target);

                new Effect.ScrollTo(target.parentNode);

                // hide some elements
                var caption_div = target.up().down('> div.form_header .form_caption');
                caption_div.innerHTML = '';
                target.select('td.t_caption').invoke('hide').map(function(el) { caption_div.innerHTML = el.innerText; });
                if (params.title) {
                    caption_div.innerHTML = params.title;
                }
                // set focus
                target.up().down('.focus-dummy').focus();

                var success_func;
                if (typeof params.success_func !== 'undefined') {
                    // pass a different success_func or pass null to avoid custom success function execution
                    success_func = params.success_func;
                } else {
                    // function to close additional layer of lightbox and reload history activity panel
                    success_func = function() {
                        Bgs_Tickets.closeFormContainer(this);
                        Bgs_Tickets.loadHistoryActivity();
                    };
                }
                if (typeof success_func === 'function') {
                    success_func = success_func.toString() + '.bind(this)';
                }

                // modify onsubmit handler
                var form = target.down('form');
                if (form != null) {
                    var form_onsubmit = form.getAttribute('onSubmit');
                    if (form_onsubmit) {
                        // available onsubmit functionality of form is modified and used
                        form_onsubmit = form_onsubmit.replace(/(\);\s*return\s+false)/, ', ' + success_func + '$1');
                    } else if (typeof params.onsubmit === 'string') {
                        // onsubmit functionality is specified in params
                        form_onsubmit = params.onsubmit;
                    } else {
                        // no functionality, cancel submit of page
                        form_onsubmit = 'return false;';
                    }
                    form.setAttribute('onSubmit', form_onsubmit);
                    form.onsubmit = new Function('event', form.getAttribute('onsubmit')).bind(form);
                }

                // set cancel button handler
                if (target.down('button[name="cancel"]') != null) {
                    target.down('button[name="cancel"]').onclick = function() {
                        Bgs_Tickets.closeFormContainer(this);
                    };
                }

                if (typeof CKEDITOR !== 'undefined') {
                    // make CKEditor dialogs visible in lightbox
                    CKEDITOR.config.baseFloatZIndex = $('lightbox').getStyles().zIndex || CKEDITOR.config.baseFloatZIndex;
                }
            }.bind(this)
        });
    }

    /**
     * Show/hide form container
     *
     * @param {string} action - show or hide
     * @param {Object} target - target element, if not specified - defined dynamically
     */
    function toggleFormContainer(action, target) {
        // try to identify form container element, if not specified
        if (!target && container) {
            target = $(container).down('.form_container');
        }
        if (target) {
            target = $(target);
            if (action == 'show' || action == 'hide') {
                target.up()[action]();
                if (action == 'hide') {
                    target.innerHTML = '';
                    document.stopObserving('keydown', formContainerKeyboardAction);
                    lb.enableKeyboardNav();
                } else {
                    //make the windows modal stopping the overlay to be closed
                    //document.observe('keydown', formContainerKeyboardAction);
                    lb.disableKeyboardNav();
                }
            }
        }
    }

    /**
     * Function to be called externally for closing form layer
     *
     * @param {Object} element - any DOM element within the layer
     */
    function closeFormContainer(element) {
        // try to identify form container element
        var target = null;
        if (element && typeof element.up === 'function' && element.up('.container')) {
            target = element.up('.container').down('.form_container');
        }
        var in_lightbox = target.up('#lightbox');
        this.toggleFormContainer('hide', target);
        // set focus
        if (in_lightbox) {
            in_lightbox.down('.focus-dummy').focus();
        }
        // enable all buttons
        toggleButtonsActive(container, true);
    }

    /**
     * Handles escape button when form layer is active - closes form container but
     * not lightbox
     *
     * @param {Object} event - keydown event
     */
    function formContainerKeyboardAction(event) {
        var keycode = event.keyCode;

        var escapeKey;
        if (event.DOM_VK_ESCAPE) {  // mozilla
            escapeKey = event.DOM_VK_ESCAPE;
        } else { // ie
            escapeKey = 27;
        }

        var key = String.fromCharCode(keycode).toLowerCase();

        if (keycode == escapeKey) {
            // close form layer
            var el = event.target || event.srcElement || event.originalTarget;
            // do not pass event further
            Bgs_Tickets.closeFormContainer(el);
            Event.stop(event);
        }
    }

    /**
     * Reloads container where lightbox was opened from, if possible, or reloads page
     *
     * @param {Object} element - DOM element that triggered opening of lightbox
     */
    function reloadOriginContainer(element) {
        var target,
            link,
            page,
            reload_cancel = false,
            query_params,
            mc = '',
            action = '';

        if (this.reload_origin_container === true) {
            delete this.reload_origin_container;
            if (element) {
                element = $(element);

                query_params = {};
                query_params[env.module_param] = '';
                query_params[env.controller_param] = '';

                if (element.tagName == 'BUTTON') {
                    if (env.module_name == 'index') {
                        // button in plugin dashlet
                        // TODO if necessary, add functionality for add mode - detect which dashlet(s) to reload...
                        reload_cancel = true;
                    } else if ($$('.subpanel_container').length && $('id') && $('rel_type') && $('rel_type').value) {
                        // additional variable button, check for loaded subpanel
                        target = $(env.modelName.toLowerCase() + '' + $('id').value + '_ajax_' + $('rel_type').value.replace(/s$/, ''));
                        if (target) {
                            mc = $('rel_type').value;
                            action = 'subpanel';
                            query_params.session_param = target.id;
                        }
                    } else {
                        // reload not necessary
                        reload_cancel = true;
                    }
                } else if (element.up('div.dashlet_content')) {
                    // dashlet
                    var check_element = element;
                    if (check_element.tagName != 'TR') {
                        check_element = check_element.up('tr');
                    }
                    // check that dashlet is a standard one with custom
                    // row_link_action functionality applied to it
                    if (check_element && check_element.hasClassName('row_link_action')) {
                        target = element.up('div.dashlet_content');
                        var dashlet_id = target.id.replace(/^.*_(\d+)$/, '$1');
                        // identify module+controller from class name
                        mc = $(check_element).classNames().toArray().
                            filter(function(n) { return /.*_\d+$/.test(n); }).
                            map(function(n) { return n.replace(/_\d+$/, 's'); }).
                            pop() || '';
                        if (mc) {
                            action = 'dashlet';
                            query_params.dashlet = dashlet_id;
                            query_params.session_param = 'dashlets_' + dashlet_id + '_' + mc.replace(/s$/, '');
                        }
                    }
                } else if (element.up('div.rel_tab.loaded') && $('rel_type') && $('rel_type').value) {
                    // subpanel
                    target = element.up('div.rel_tab.loaded');
                    mc = $('rel_type').value;
                    action = 'subpanel';
                    query_params.session_param = target.id;
                }


                if (target && mc && action) {
                    if (mc.indexOf('_') > -1) {
                        query_params[env.module_param] = mc.substr(0, mc.indexOf('_'));
                        query_params[env.controller_param] = mc.substr(mc.indexOf('_') + 1);
                        query_params[query_params[env.controller_param]] = action;
                    } else {
                        query_params[env.module_param] = mc;
                        delete query_params[env.controller_param];
                        query_params[query_params[env.module_param]] = action;
                    }
                    query_params.page = target.down('span.page_menu_current_page') ? target.down('span.page_menu_current_page').innerText.strip() : '1';
                    query_params.source = 'ajax';

                    link = env.base_url + '?' + Object.toQueryString(query_params);
                }
            }

            if (target && link) {
                ajaxUpdater({
                    link: link,
                    target: target
                });
            } else if (!reload_cancel) {
                window.location.reload();
            }

            // reload other dashlets
            if (env.module_name == 'index' && this.data.dashlets_reload && this.data.dashlets_reload.length) {
                for (var i = 0; i < this.data.dashlets_reload.length; i++) {
                    var content_container = $(this.data.dashlets_reload[i][0]);
                    if (content_container && content_container != target) {
                        dashletsLoad.apply(null, this.data.dashlets_reload[i]);
                    }
                }
            }
        }
    }

    /**
     * Save (add/edit) ticket - perform AJAX request that uploads files as well,
     * using FormData, or submit form to an iframe if not supported.
     *
     * @param {Object} form - form to submit
     */
    function save(form) {

        // disable all buttons
        toggleButtonsActive(container, false);

        var link = base_url + 'save';
        // close lightbox after save
        if (Event.element(event).name == 'saveButton2') {
            link += '&close=1';
        }

        // iframe fallback
        if (typeof FormData === 'undefined') {
            return uploadViaAjax(
                form,
                {
                    action: link,
                    onLoad: function(result) {
                        if (typeof result !== 'object' || result === null) {
                            result = {};
                        }
                        var context = window.parent !== window ? window.parent : window;
                        if (typeof context.Bgs_Tickets === 'object') {
                            context.Bgs_Tickets.onSave({responseText: context.Object.toJSON(result)});
                        } else {
                            ['errors', 'warnings', 'messages'].forEach(function(key) {
                                if (typeof result[key] !== 'undefined') {
                                    context.displayNotificationFixed(result[key]);
                                }
                            });
                        }
                        if (window.parent !== window) {
                            window.parent.document.body.select('iframe[name="' + window.name + '"]').invoke('remove');
                        }
                    }
                }
            );
        }

        /**
         * first two parameters specify that form is submitted via
         * XMLHttpRequest.send() with "multipart/form-data" contentType header
         * @see https://developer.mozilla.org/en-US/docs/Web/API/FormData
         * @see https://github.com/sstephenson/prototype/commit/f63d7f7bb59bfa11adb21a79c3322a0f4d7ed7d3
         * */
        var opt = {
            postBody: (new FormData(form)),
            contentType: null,
            onSuccess: this.onSave.bind(this),
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        new Ajax.Request(link, opt);
    }

    /**
     * Handler for processing response data after save action (via AJAX request
     * or submit into an iframe)
     *
     * @param {Object} t - response object
     */
    function onSave(t) {
        if (!checkAjaxResponse(t.responseText)) {
            return false;
        }
        var target = container;
        // response might be JSON-encoded or plain text
        // prepare it as an object
        var result = t.responseText;
        if (String.prototype.isJSON.call(result)) {
            result = String.prototype.evalJSON.call(result);
            if (typeof result.content === 'undefined' && typeof result.data === 'string') {
                result.content = result.data;
            }
        }
        if (typeof result === 'string') {
            result = {
                content: result
            };
        }

        if (result) {
            if (typeof result.content !== 'undefined') {
                target.innerHTML = result.content;
                target.select('script').each(ajaxLoadJS);
                // set focus
                target.up('#lightbox').down('.focus-dummy').focus();

                // set flag that model has been updated
                this.reload_origin_container = true;
            }
            if (result.title) {
                lb.params.title = result.title;
                lb.updateCaption();
            }
            // display success, warning and/or error messages
            ['message', 'warning', 'error'].each(function(message_type) {
                if (result[message_type + 's']) {
                    displayResultNotification(result[message_type + 's'], message_type);
                }
            });
            if (result.scripts) {
                result.scripts.each(eval);
            }
            if (result.close) {
                // set flag that model has been updated
                this.reload_origin_container = true;
                lb.deactivate();
            }
        }
        // enable all buttons
        toggleButtonsActive(container, true);

        Effect.Fade('loading');
    }

    /**
     * Sets status of ticket
     *
     * @param {Object} form - status form to submit
     */
    function setStatus(form) {
        var model = form.down('input[name="' + env.module_param + '"]') ? form.down('input[name="' + env.module_param + '"]').value.replace(/s$/, '') : '';
        var model_id = form.down('input[name="setstatus"]') ? form.down('input[name="setstatus"]').value : '0';

        // remove form elements that should not be submitted
        form.select('input[name="' + env.module_param + '"]').invoke('remove');

        var data = {};
        if (model && model_id && $$('.history_activity_' + model + '_' + model_id).length) {
            data.history_total = 1;
        }

        // submit to plugin, not to standard action of module, because it has redirect
        load({
            complex: true,
            action: 'setStatus',
            form: form,
            data: data,
            target: container,
            // function to close additional layer of lightbox and reload history activity panel

            execute_after: function(load_params, result) {

                Bgs_Tickets.closeFormContainer(this);
                Bgs_Tickets.loadHistoryActivity();

            }.bind(form)

        });
    }

    /**
     * Merge to another ticket
     *
     * @param int ticketId - ticket id to merge to
     */
    function merge(ticketId) {
        var model_id = container.down('input[name="id"]') ? container.down('input[name="id"]').value : '0';


        var data = {id: model_id, merge_to: ticketId};

        // submit to plugin, not to standard action of module, because it has redirect
        load({
            complex: true,
            action: 'merge',
            data: data,
            execute_after: function(load_params, result) {
                //reset the merge number/id upon error
                if (result.errors) {
                    container.select('input[name^="merge_document"]').each(function(el) {
                       el.setValue('');
                    });
                }

                // enable buttons in dashlet
                toggleButtonsActive(container, true);

                Bgs_Tickets.loadHistoryActivity();

            }

        });
    }

    /**
     * Marks file for deletion
     *
     * @param {Object} element - trigger element
     * @param {number} id - file id
     * @return {boolean} - always return false
     */
    function deleteFile(element, id) {
        if (element && id) {
            element = $(element);
            element.up().down('input[name="deleteid_attachments[]"]').value = id;
            element.up('tr').addClassName('strike');
            element.remove();
        }
        return false;
    }

    /**
     * Updates call event with current data from form
     *
     * @param {Object} element - trigger button element
     */
    function saveCall(element) {
        var form = element.form;
        var data = {};
        if (element.name && element.name.match(/deactivate/)) {
            data.active = 0;
        }

        load({
            complex: true,
            action: 'saveCall',
            form: form,
            data: data,
            // we specify target because it is requred but we are not really returning content for it
            target: form.up('.form_container'),
            execute_after: function(load_params, result) {
                if (typeof load_params.data.active != 'undefined' && !load_params.data.active) {
                    // close lightbox on deactivate
                    Bgs_Tickets.reload_origin_container = true;
                    lb.deactivate();
                } else {
                    if (result.reload_origin_container) {
                        Bgs_Tickets.reload_origin_container = true;
                    }
                    // enable all buttons
                    toggleButtonsActive(container, true);
                }
            }
        });
    }

    /**
     * Delete call event
     *
     * @param {Object} element - trigger button element
     */
    function deleteCall(element) {
        var form = element.form;
        var data = {};

        load({
            complex: true,
            action: 'deleteCall',
            form: form,
            data: data,
            // we specify target because it is requred but we are not really returning content for it
            target: form.up('.form_container'),
            execute_after: function(load_params, result) {
                // close lightbox
                Bgs_Tickets.reload_origin_container = true;
                lb.deactivate();
            }
        });
    }

    /* **********   contact persons functionality start   ********** */

    /**
     * DROPDOWNs relations for contact persons change
     *
     * Overwrite the general function internally because extra functionality
     * for selected index is needed.
     *
     * @param {Object} element - branch dropdown
     * @param {string} dropdown_id - id of dropdown for contact persons
     * @param {number} selected_index - index of option to be set as selected
     */
    function changeContactPersonsOptions(element, dropdown_id, selected_index) {
        Effect.Center('loading');
        Effect.Appear('loading');

        var select_obj = $(dropdown_id);
        var branch_id = element.value;
        var opt = {
            asynchronous: true,
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var result = eval('(' + t.responseText + ')');
                select_obj.options.length = 0;
                if (result.length > 0) {
                    select_obj.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                    addClass(select_obj.options[0], 'undefined');
                    removeClass(select_obj, 'missing_records');
                } else {
                    select_obj.options[0] = new Option(i18n['labels']['empty_contact_person'], '', false, false);
                    addClass(select_obj, 'missing_records');
                }
                for (var j = 0; j < result.length; j++) {
                    select_obj.options[j+1] = new Option(result[j]['label'], result[j]['option_value'], false, false);
                }

                // set selection
                select_obj.selectedIndex = selected_index < contact_person.options.length ? selected_index : 0;

                toggleUndefined(select_obj);

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=customers&customers=ajax_fill_contact_persons_options&branch_id=' + branch_id;
        if ($('model_lang') != null) {
            url += '&model_lang=' + $('model_lang').value;
        }
        new Ajax.Request(url, opt);
    }

    /**
     * Loads customer contact at specified offset
     *
     * @param {string|number} pos - offset index or text 'last'
     */
    function getContact(pos) {
        // prepare ajax options
        if (pos == 0) {
            // not valid value
            return true;
        }

        load({
            complex: true,
            url: base_url + 'getContacts&use_ajax=1',
            target: container.down('.contacts_data_container'),
            data: {
                customer: container.down('#customer').value,
                branch: container.down('#branch').value,
                pos: pos
            },
            execute_after: function(load_params, result) {
                // get selected index from contacts panel
                var selected_index = container.down('.contacts_data_caption span.current_pos');
                selected_index = selected_index && !isNaN(parseInt(selected_index.innerHTML)) ? parseInt(selected_index.innerHTML) : 0;

                // reload branch
                if (pos == 'last' && container.down('#branch')) {
                    branch_field = container.down('#branch');
                    branch_field.value = result.branch;

                    // reload contact person options and set specified index as
                    // selected, if present as parameter and then in dropdown
                    changeContactPersonsOptions(branch_field, 'contact_person', selected_index);
                } else {
                    // adjust contact person dropdown selection to current contact person
                    var contact_person = container.down('select#contact_person');
                    if (contact_person != null) {
                        if (contact_person.options.length == 1 && selected_index == 1) {
                            contact_person.selectedIndex = 0;
                        } else if (contact_person.options.length > 1) {
                            // 0-index option is "please, select", indexes of dropdown and list match
                            contact_person.selectedIndex = selected_index < contact_person.options.length ? selected_index : 0;
                        }
                        toggleUndefined(contact_person);
                    }
                }
                // enable all buttons
                toggleButtonsActive(container, true);
            }.bind(this)
        });
    }

    /**
     * We use functionality of customers module for add/edit of contact persons
     * with AJAX, this is why we have a dedicated function to comply with it.
     *
     * @param {Object} event - click event
     * @param {number} id - id of contact
     * @param {number} customer - id of customer
     * @param {number} branch - id of branch
     */
    function manageContact(event, id, customer, branch) {
        Effect.Center('loading');
        Effect.Appear('loading');

        var el = event.target || event.srcElement || event.originalTarget;
        var form = el.form || '';
        var method = el.form ? 'post' : 'get';
        var target = defineFormContainer(event);
        id = parseInt(id);

        // disable all buttons
        toggleButtonsActive(container, false);

        new Ajax.Request(
            env.base_url + '?' + env.module_param + '=customers&controller=contactpersons&contactpersons=' +
                (id ? 'ajax_edit&ajax_edit=' + id: 'ajax_add') +
                '&parent_customer_id=' + customer + '&parent_branch=' + branch,
            {
                method: method,
                parameters: el.form ? Form.serialize(el.form) : '',
                onSuccess: function(t) {
                    if (!checkAjaxResponse(t.responseText)) {
                        toggleButtonsActive(container, true);
                        return false;
                    };
                    eval(t.responseText);
                    if (result.errors) {
                        displayResultNotification(result.errors, 'error');
                        // enable all buttons
                        toggleButtonsActive(container, true);
                    } else if (method == 'get' && result.data) {
                        target.innerHTML = result.data;
                        target.select('script').each(ajaxLoadJS);

                        // display form container
                        toggleFormContainer('show', target);

                        new Effect.ScrollTo(target.parentNode);

                        // hide some elements
                        target.select('td.t_caption').invoke('hide').map(function(el) {
                            target.up().down('> div.form_header .form_caption').innerHTML = el.innerText;
                        });
                        // set focus
                        target.up().down('.focus-dummy').focus();

                        target.down('button[name="saveButton1"]').removeAttribute('onclick');
                        target.down('button[name="saveButton1"]').onclick = function(event) {
                            this.manageContact(event, id, customer, branch);
                            return false;
                        }.bind(this);
                        target.down('button[name="cancel"]').onclick = function() {
                            Bgs_Tickets.closeFormContainer(this);
                        };
                        // enable all buttons
                        toggleButtonsActive(container, true);
                    } else if (method == 'post' && result.messages) {
                        // data has been saved successfully - show some messages
                        displayResultNotification(result.messages, 'message');

                        // hide form container
                        toggleFormContainer('hide', target);

                        // reload contact panel, reload contact dropdown as well
                        this.getContact('last');
                    }

                    Effect.Fade('loading');
                }.bind(this),
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            }
        );
    }

    /* **********   contact persons functionality end   ********** */


    /* **********   public functionality end   ********** */


    /**
     * public functionality export
     */
    return {
        data: data || {},
        setACData: setACData,
        setContainer: setContainer,
        defineContainerWidth: defineContainerWidth,
        loadScreen: loadScreen,
        loadHistoryActivity: loadHistoryActivity,
        loadInlineForm: loadInlineForm,
        composeEmail: composeEmail,
        replyEmail: replyEmail,
        toggleFormContainer: toggleFormContainer,
        closeFormContainer: closeFormContainer,
        reloadOriginContainer: reloadOriginContainer,
        setStatus: setStatus,
        deleteFile: deleteFile,
        save: save,
        onSave: onSave,
        getContact: getContact,
        manageContact: manageContact,
        saveCall: saveCall,
        deleteCall: deleteCall,
        toggleAutocompleter: toggleAutocompleter,
        changeCustomerInfo: changeCustomerInfo,
        linkifyRelatives: linkifyRelatives,
        merge: merge
    }
};

/**
 * Initialize as an object
 */
Bgs_Tickets.init = function() {
    var obj = this;
    if (typeof obj === 'function') {
        // initialize as an object with passed arguments
        var args = [null];
        for (var i = 0; i< arguments.length; i++) {
            args.push(arguments[i]);
        }
        // create constructor function with bound arguments
        var f = Bgs_Tickets.bind.apply(Bgs_Tickets, args);
        obj = new f();
        obj.init = Bgs_Tickets.init;
        // set global variable to newly created object
        Bgs_Tickets = obj;
    }

    // functonality is executed if lightbox is opened
    if (lb && lb.active && lb.params) {
        obj.setContainer($(lb.params.uniqid));
        lb.setWidth(obj.defineContainerWidth());
        lb.params.closeHandler = function() {
            Bgs_Tickets.reloadOriginContainer(lb.params.origin || null);
            // clear internal container variable
            Bgs_Tickets.setContainer(null);
        };
    }

    return obj;
};
