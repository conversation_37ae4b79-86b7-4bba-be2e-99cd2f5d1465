<?php
/**
 * Planned Time Allocation plugin custom model class
 */
class Custom_Model extends Model {
    public $modelName = 'Dashlet';
    public $plugin_name;

    /**
     * Width (in pixels) of first column for non-fixed and all-day events
     * @var int
     */
    const W0 = 106;

    /**
     * Width (in pixels) of columns for fixed events
     * @var int
     */
    const W1 = 56;

    /**
     * Height (in pixels) of calendar row
     * @var int
     */
    const CAL_ROW_HEIGHT = 28;

    /**
     * Max. height (in pixels) of calendar container
     * @var int
     */
    const CAL_MAX_HEIGHT = 800;

    /**
     * Max. number of events to display without overlapping in first, second and last column
     * @var int
     */
    const MAX_NO_OL = 3;

    public function __construct($dashlet_settings = '') {
        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        // plugin name matches plugin folder name
        $pn = explode(DIRECTORY_SEPARATOR, realpath(dirname(__FILE__)));
        $this->plugin_name = $pn[count($pn)-2];

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        // set data from dashlet model into plugin model
        if ($this->registry['dashlet']) {
            if ($this->registry['dashlet']->get('filters')) {
                foreach ($this->registry['dashlet']->get('filters') as $k => $v) {
                    $this->set($k, $v, true);
                }
            }
            $this->set('dashlet_id', $this->registry['dashlet']->get('id'), true);
        }

        return true;
    }

    /**
     * Process dashlet-specific data before save and set it into 'filters'
     * property of dashlet
     *
     * @param Dashlet $model - dashlet model for current plugin
     */
    public function prepareCustomData(&$model) {
        // prepare custom filters
        $filters = array();

        if ($model->get('projects_filter')) {
            $filters['projects_filter'] = 1;

            if ($model->get('projects_types')) {
                $filters['projects_types'] = $model->get('projects_types');
            }
        } else {
            $filters['projects_filter'] = 0;
        }

        $model->set('filters', $filters, true);
    }

    /**
     * Validate dashlet-specific data before save
     *
     * @param Dashlet $model - dashlet model for current plugin
     */
    public function validateCustomData(&$model) {
        if ($model->get('projects_filter') && !$model->get('projects_types')) {
            $model->raiseError('error_empty_field', 'projects_types', 0,
                array('var_label' => $this->i18n('plugin_projects_types')));
        }
    }

    /**
     * Parse settings for current plugin and set them as properties to model
     *
     * @param string $dashlet_settings - settings field of dashlet
     */
    public function parseDashletPluginSettings($dashlet_settings) {

        $settings_rows = preg_split('/(\r\n|\n|\r)/', $dashlet_settings);

        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0] == '#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);

            // set them as properties of model in the usual way
            $this->set($key, $value, true);
        }
    }

    /**
     * Prepare filter definitions for dashlet
     *
     * @return array - array with arrays of options
     */
    public function prepareFilters() {

        $filters = array();

        // period filter
        $week_options = array();
        $lbl_week = $this->i18n('plugin_week');
        $prepareWeekOption = function($start, $end) use ($lbl_week) {
            return array(
                'label' => sprintf('%s %d (%s - %s)',
                                   $lbl_week,
                                   General::strftime('%V', strtotime($start)),
                                   General::strftime('%d' . (substr($start, 5, 2) != substr($end, 5, 2) ?
                                                     ' %b' . (substr($start, 0, 4) != substr($end, 0, 4) ? ' %Y' : '') : ''),
                                                     $start),
                                   General::strftime('%d %b %Y', $end)),
                'option_value' => $start
            );
        };

        $today_ts = strtotime(General::strftime('%Y-%m-%d'));
        $current_week_start = General::strftime('%Y-%m-%d', strtotime('-' . ((General::strftime('%w', $today_ts) ?: 7) - 1) . ' day', $today_ts));

        $weeks_before = -1 * intval($this->get('period_weeks_before'));
        $weeks_after = intval($this->get('period_weeks_after'));

        $request_period = $this->registry['request']->get('period') ?: '';
        $period = $current_week_start;

        for ($i = $weeks_before; $i <= $weeks_after; $i++) {

            $selected_week_start = General::strftime('%Y-%m-%d', strtotime(($i < 0 ? '' : '+') . $i . ' week', strtotime($current_week_start)));
            $selected_week_end = General::strftime('%Y-%m-%d', strtotime('+1 week -1 day', strtotime($selected_week_start)));

            $key = General::strftime('%Y_%V',
                strtotime(substr($selected_week_start, 0, 4) != substr($selected_week_end, 0, 4) && General::strftime('%V', strtotime($selected_week_end)) != 1 ?
                    $selected_week_start : $selected_week_end));
            $week_options[$key] =
                $prepareWeekOption($selected_week_start, $selected_week_end) + (!$i ? array('class_name' => 'strong') : array());

            if ($request_period == $selected_week_start) {
                $period = $selected_week_start;
            }
        }

        $filters['period'] = array(
            'type'      => 'dropdown',
            'custom_id' => 'pta_period_' . $this->get('dashlet_id'),
            'name'      => 'period',
            'width'     => 200,
            'label'     => $this->i18n('plugin_period'),
            'options'   => $week_options + array('skip_please_select' => 1),
            'value'     => $period,
            //'onchange'  => 'plannedTime.reloadContent($(\'pta_filtersGo\'))',
            'required'  => 1
        );

        // users/departments filter
        $custom_id = 'pta_participant_' . $this->get('dashlet_id');
        $filters['participant'] = array(
            'type'          => 'autocompleter',
            'custom_id'     => $custom_id,
            'name'          => 'participant',
            'width'         => 200,
            'label'         => $this->i18n('plugin_department_user'),
            'autocomplete'  => array(
                'type'          => 'autocompleters',
                'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'autocompleters', 'autocompleters', 'ajax_select'),
                'plugin_search' => 'searchDepartmentsAndUsers',
                'suggestions'   => '<name>',
                'fill_options'  => array(
                    '$' . $custom_id . ' => <id>',
                    '$' . $custom_id . '_autocomplete => <name>',
                    '$' . $custom_id . '_oldvalue => <name>',
                    '$user_ids => <user_ids>'
                ),
                'buttons_hide'  => 'search',
                'execute_after' => 'plannedTime.addPlannedTimeFilter',
            ),
            'autocomplete_var_type' => 'basic',
            'required'              => 1
        );

        // projects filter
        if ($this->get('projects_filter')) {
            $custom_id = 'pta_project_' . $this->get('dashlet_id');
            $filters['project'] = array(
                'type'          => 'autocompleter',
                'custom_id'     => $custom_id,
                'name'          => 'project',
                'width'         => 200,
                'label'         => $this->i18n('plugin_project'),
                'autocomplete'  => array(
                    'type'          => 'projects',
                    'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'projects', 'projects', 'ajax_select'),
                    'filters'       => array(
                        '<type>'    => ($this->get('projects_types') ? implode(',', $this->get('projects_types')) : '0'),
                        '<status>'  => '!= finished'
                    ),
                    'fill_options'  => array(
                        '$' . $custom_id . ' => <id>',
                        '$' . $custom_id . '_autocomplete => <name>',
                        '$' . $custom_id . '_oldvalue => <name>',
                    ),
                    'buttons_hide'  => 'search',
                    'execute_after' => 'plannedTime.addPlannedTimeFilter',
                ),
                'autocomplete_var_type' => 'basic',
                'required'              => 0
            );
        }

        return $filters;
    }

    /******************** specific functionality ********************/

    /**
     * Filter and prepare displayed events
     *
     * @param bool $print - 'content' (or any other truthy value) to print the whole dashlet content,
     *  'calendar' - to print only the calendar part, false to return results
     * @return string|array - fetched content or array of results to pass to viewer for dispaly
     */
    public function filterPlannedTime($print = 'content') {

        $result = array();
        // clear any previous messages
        $this->registry['messages']->flush();

        // get values of search filters from request or from personal settings
        $dashlet_filters = $this->_getFiltersValues();

        // if method is called from initial load if dashlet, set period for current week in filters
        if (!$print && !$this->registry['request']->isRequested('period')) {
            $today_ts = strtotime(General::strftime('%Y-%m-%d'));
            $dashlet_filters['period'] = General::strftime('%Y-%m-%d', strtotime('-' . ((General::strftime('%w', $today_ts) ?: 7) - 1) . ' day', $today_ts));
            $this->set('period', $dashlet_filters['period'], true);
        }

        // prepare filter values for display in interface
        if ($print !== 'calendar') {
            $this->_prepareFiltersValues($result);
        }

        // prepare participants
        $user_ids = $this->_prepareUsers($result);

        // prepare calendar settings
        $result['settings'] = $this->_prepareCalendarSettings();

        // if no users specified or found OR too many users found
        if (empty($user_ids)) {
            $this->registry['messages']->setError($this->i18n('error_plugin_no_participants'));
        } elseif (count($user_ids) > $this->get('display_max_users')) {
            $this->registry['messages']->setError($this->i18n('error_plugin_too_many_participants',
                array(count($user_ids), $this->get('display_max_users'))));
        }
        $errors = $this->registry['messages']->getErrors();

        // prepare settings, data etc. that will be passed to the plannedTime javascript object
        $result['dashlet_data'] = array(
            'workload'      => $this->_prepareWorkload(),
            'period'        => $dashlet_filters['period'],    // first date of displayed period
            'check_interval'=> intval($this->get('check_interval')),    // interval for checking for modifications
            'timestamp'     => isset($dashlet_filters['timestamp']) ? $dashlet_filters['timestamp'] : time(),
            'w0'            => self::W0,
            'w1'            => self::W1,
            'cal_row_height'=> self::CAL_ROW_HEIGHT,
            'cal_max_height'=> self::CAL_MAX_HEIGHT,
            'max_no_ol'     => self::MAX_NO_OL,
            'day_start'     => 60 * $result['settings']['week_start_hour'],
            'day_end'       => 60 * ($result['settings']['week_end_hour'] + 1),
            'default_task_type' => 0,
        );

        if ($errors) {
            $result['errors'] = $errors;
            $result['days'] = array();
        } else {
            // prepare tasks
            $this->_prepareTasks($result);

            if ($print != 'calendar') {
                $cu = $this->registry['currentUser'];
                // prepare permissions and default type for adding of tasks
                if ($cu->checkRights('tasks', 'add')) {
                    $default_type = (int)$cu->getPersonalSettings('tasks', 'default_task_type');
                    $default_config = (int)$cu->getPersonalSettings('tasks', 'default_task_type_config');

                    $types_filters = array(
                        'model_lang' => $this->registry['lang'],
                        'sanitize' => true,
                        'where' => array('tt.active = 1'),
                        'sort' => array('tti18n.name ASC')
                    );
                    $tasks_types = Tasks_Types::getIds($this->registry, $types_filters);

                    if (!$default_type || !in_array($default_type, $tasks_types) || !$cu->checkRights('tasks' . $default_type, 'add')) {
                        $default_type = $default_config = 0;
                        foreach ($tasks_types as $type_id) {
                            if ($cu->checkRights('tasks' . $type_id, 'add')) {
                                $default_type = $type_id;
                                break;
                            }
                        }
                    }

                    $result['dashlet_data']['default_task_type'] = $default_type . ($default_config ? '|' . $default_config : '');
                }
            }

            // prepare days
            $this->_prepareDays($result);

            // prepare events
            $this->_prepareEvents($result);

            // we need to remember which events were prepared for display
            // in dashlet because they might have been modified when we next check
            $dashlet_filters['events'] = array_keys($result['events']);
            // save current timestamp in filters
            $dashlet_filters['timestamp'] = time();
            // save filters in personal settings
            if ($this->_saveFilters($dashlet_filters)) {
                // set current timestamp into dashlet data
                $result['dashlet_data']['timestamp'] = $dashlet_filters['timestamp'];
            }

            // data for users that will be passed to the plannedTime javascript object
            $params = array(
                $this->registry,
                'table' => 'DB_TABLE_USERS',
                'table_i18n' => 'DB_TABLE_USERS_I18N',
                'order_by' => 't.active DESC, t.is_portal ASC',
                'label' => 'firstname,lastname',
                'value' => 'id,is_portal',
                'where' => 't.active = 1',
                'assoc' => true
            );
            $result['dashlet_data']['users'] = Dropdown::getCustomDropdown($params);

            foreach ($result['dashlet_data']['users'] as $k => $v) {
                unset($result['dashlet_data']['users'][$k]);
                $v['is_portal'] = intval(strrpos($k, '1') === strlen($k) - 1);
                $v['option_value'] = substr($k, 0, strlen($k) - 1);
                $result['dashlet_data']['users'][$v['option_value']] = $v;
            }
            foreach ($result['users'] as $k => $v) {
                if (isset($result['dashlet_data']['users'][$k])) {
                    $result['dashlet_data']['users'][$k]['working_time'] = $v['working_time'];
                }
            }
        }

        // initial loading of dashlet or after submit of filters
        if (!$print) {
            return $result;
        }

        // calling from AJAX - fetch calendar or full content and return it
        require_once PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/viewers/custom.dashlet.viewer.php';
        $viewer = new Custom_Dashlet_Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        if ($print === 'calendar') {
            $viewer->template = '_calendar.html';
            $viewer->data['vscroll'] =
                intval(self::CAL_ROW_HEIGHT *
                    ($this->registry['currentUser']->getPersonalSettings('calendars', 'week_days_number') ?: 7) *
                    count($result['users']) > self::CAL_MAX_HEIGHT);
            $viewer->data['cal_width'] =
                166 + self::W0 +
                ($result['settings']['week_end_hour'] -
                $result['settings']['week_start_hour'] + 1 +
                ($result['settings']['week_start_hour'] > 0) +
                ($result['settings']['week_end_hour'] < 23)) * self::W1 + 86 + 5 +
                14 * $viewer->data['vscroll'];
        } else {
            $viewer->template = 'dashlet.html';
            $this->registry['request']->set('period', $dashlet_filters['period'], 'post', true);
            $viewer->data['filters'] = $this->prepareFilters();
        }
        $viewer->data['dashlet'] = $this->registry['dashlet'];
        $viewer->data = array_merge($viewer->data, $result);

        $lang_files = array(
            PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/i18n/' . $this->registry['lang'] . '/custom.ini',
            PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini'
        );
        $viewer->loadCustomI18NFiles($lang_files);

        return $viewer->fetch();
    }

    /**
     * Check for any modified data among displayed events. If found, dashlet content is reloaded.
     */
    public function reloadContent() {

        $dashlet_filters = $this->_getFiltersValues();

        $result = array();
        if ($this->_checkForModifications($result)) {
            // full reload of content
            $result = $this->filterPlannedTime('content');
        } else {
            // new timestamp to compare against
            $dashlet_filters['timestamp'] = time();

            // save filters in personal settings
            $this->_saveFilters($dashlet_filters);

            $result = json_encode(array('timestamp' => $dashlet_filters['timestamp']));
        }

        return $result;
    }

    /**
     * Check if task legend should be reloaded after add/edit of task. Prepare and return
     * legend content and if task has visible event, prepare and return calendar as well
     */
    public function reloadTasks() {

        $registry = &$this->registry;

        $result = array();

        // prepare filters and set them to current model
        $dashlet_filters = $this->_getFiltersValues();

        // prepare participant ids and set them to current model
        $this->_prepareUserIds();

        // prepare tasks
        $this->_prepareTasks($result);

        // data to be returned as result
        $data = array();

        // if task should be added to or removed from legend
        if (array_key_exists($this->registry['request']->get('task_id'), $result['tasks']) ||
        $this->registry['request']->get('task_action') == 'edit') {

            // fetch tasks legend
            $viewer = new Viewer($registry);
            $viewer->loadCustomI18NFiles(sprintf('%s%s%s%s%s%s', PH_MODULES_DIR, 'dashlets/plugins/', $this->plugin_name, '/i18n/', $registry['lang'], '/custom.ini'));
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
            $viewer->template = '_legend.html';
            $viewer->data = $viewer->data + $result;
            $viewer->data['dashlet'] = $registry['dashlet'];
            $viewer->data['filters'] = array('project' => $this->get('projects_filter'));

            // data for users (assigned to tasks)
            $params = array(
                $this->registry,
                'table' => 'DB_TABLE_USERS',
                'table_i18n' => 'DB_TABLE_USERS_I18N',
                'order_by' => 't.active DESC, t.is_portal ASC',
                'label' => 'firstname,lastname',
                'value' => 'id,is_portal',
                'where' => 't.active = 1',
                'assoc' => true
            );
            $viewer->data['dashlet_data']['users'] = Dropdown::getCustomDropdown($params);

            foreach ($viewer->data['dashlet_data']['users'] as $k => $v) {
                unset($viewer->data['dashlet_data']['users'][$k]);
                $v['is_portal'] = intval(strrpos($k, '1') === strlen($k) - 1);
                $v['option_value'] = substr($k, 0, strlen($k) - 1);
                $viewer->data['dashlet_data']['users'][$v['option_value']] = $v;
            }

            $lang_files = array(
                PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/i18n/' . $this->registry['lang'] . '/custom.ini',
                PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini'
            );
            $viewer->loadCustomI18NFiles($lang_files);

            // task legend should be updated
            $data = array('tasks' => $viewer->fetch());

            $query = 'SELECT e.id' . "\n" .
                     'FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                     'JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' AS er' . "\n" .
                     '  ON e.id=er.parent_id' . "\n" .
                     '    AND er.link_to=\'' . $this->registry['request']->get('task_id') . '\'' . "\n" .
                     '    AND er.origin=\'task\'' . "\n" .
                     '    AND er.link_type=\'parent\'' . "\n" .
                     'JOIN ' . DB_TABLE_EVENTS_TYPES . ' AS et' . "\n" .
                     '  ON e.type=et.id AND et.active = 1 AND et.deleted = 0 AND et.keyword = \'plannedtime\'';
            $task_events = $this->registry['db']->getCol($query);

            // if task has events within displayed week (check the events from the
            // filters), calendar should be updated as well
            if ($task_events && array_intersect($task_events, $dashlet_filters['events'])) {
                $data['calendar'] = $this->filterPlannedTime('calendar');
            }
        }

        return json_encode($data);
    }

    /**
     * Delete planned time event
     * @return string - result of the operation
     */
    public function deletePlannedTime() {
        $registry = $this->registry;
        $request = &$this->registry['request'];
        $currentUserId = $this->registry['currentUser']->get('id');
        $errors = array();

        $i18n_files = array(
            PH_MODULES_DIR . 'events/i18n/' . $this->registry['lang'] . '/events.ini',
            PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini',
        );
        $this->registry['translater']->loadFile($i18n_files);

        // get event type
        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
        $filters = array('where' => array('et.deleted IS NOT NULL',
                                          'et.keyword = \'plannedtime\''),
                         'sanitize' => true);
        $type = Events_Types::searchOne($this->registry, $filters);
        if (!$type) {
            $errors[] = $this->i18n('error_invalid_type');
            return json_encode(array('errors' => $this->_prepareMessages($errors, 'error')));
        }

        require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.factory.php';

        $task = null;
        $event_id = $request->get('event');
        if ($event_id) {
            // delete event - get from db
            $filters = array('where' => array('e.id=\'' . intval($event_id) . '\'',
                                              'e.type=' . $type->get('id'),
                                              'e.status=\'planning\'',
                                              'e.active=1'),
                             'sanitize' => true);
            $this->registry->set('getAssignments', true, true);
            $this->registry->set('module', 'custom_module', true);
            $old_event = Events::searchOne($registry, $filters);
            $this->registry->set('module', 'dashlets', true);
            $this->registry->remove('getAssignments');

            if ($old_event) {
                // if user is an assignee with edit access, edit is allowed
                if (array_filter($old_event->get('users_assignments'),
                    function($a) use ($currentUserId) {
                        return $a['participant_id'] == $currentUserId && $a['access'] == 'edit'; })) {
                    $old_event->set('edit_allocate', 1, true);
                }

                $task = $old_event->getPlannedTimeTask();
                if ($task) {
                    // if user is allowed to edit planned time allocation of task, edit is allowed
                    if (!$old_event->get('edit_allocate') && $task->checkPermissions('allocate') && $task->checkPermissions('edit_allocate')) {
                        $old_event->set('edit_allocate', 1, true);
                    }

                    // if edit is not allowed, display error
                    if (!$old_event->get('edit_allocate')) {
                        $old_event = null;
                    }
                } else {
                    // if task is deleted
                    $old_event = null;
                }
            }
        }

        if (empty($old_event)) {
            // action is not allowed
            $errors[] = $this->i18n('error_delete_notallowed');
            return json_encode(array('errors' => $this->_prepareMessages($errors, 'error')));
        }

        // before performing action - check if any of displayed data has been modified since it was last reloaded
        $dashlet_filters = $this->_getFiltersValues();
        $calendar_settings = $this->_prepareCalendarSettings();
        $res = array(
            'settings' => &$calendar_settings
        );
        $reload = $this->_checkForModifications($res);

        $result = array();
        if (Events::delete($this->registry, array($event_id))) {
            // new timestamp to compare against
            $dashlet_filters['timestamp'] = time();

            $old_event->set('deleted', General::strftime($this->i18n('date_iso')), true);
            $old_event->set('deleted_by', $currentUserId, true);

            Events_History::saveData($this->registry, array('model' => $old_event, 'action_type' => 'delete'));

            $old_event = $old_event->getAll();

            // send notification
            $events = array($old_event);
            $task->unsanitize();
            $task->sendPlannedTimeNotification($events);
            $task->sanitize();

            if ($reload) {
                // full reload (content or calendar)
                $result[$reload] = $this->filterPlannedTime($reload);
            } else {
                // partial reload

                $old_event['owner'] = !empty($old_event['users_participants']) ?
                    $old_event['users_participants'][0]['participant_id'] : '';

                // here we get all events for the old day and user
                $res = array(
                    'settings' => &$calendar_settings,
                    'days' => array($old_event['event_start_date']),
                    'user_ids' => array($old_event['owner'])
                );
                $this->set('after_save', true, true);
                $this->_prepareEvents($res);
                $old_data = isset($res['event_ids'][$old_event['event_start_date']][$old_event['owner']]) ?
                    $res['event_ids'][$old_event['event_start_date']][$old_event['owner']] : array();
                unset($res);

                // get position in which sub-array of old event was located
                $position = $this->_prepareEventPosition($old_event, $calendar_settings);

                // remove old event
                $result['old_event'] = array(
                    'id' => $old_event['id'],
                    'user' => $old_event['owner'],
                    'date' => $old_event['event_start_date'],
                    'position' => $position,
                    'group' => (isset($old_data[$position]) ? $old_data[$position] : array()), // data for sub-array where old event was located
                    'duration' => (isset($old_data['duration']) ? $old_data['duration'] : 0) // absolute value of duration for old day
                );

                // update unallocated time of task
                if ($task->get('planned_time')) {
                    $result['task'] = array(
                        'id' => $task->get('id'),
                        'remaining_time' => intval($task->get('planned_time') - $task->getPlannedDuration())
                    );
                }

                if (empty($dashlet_filters['events'])) {
                    $dashlet_filters['events'] = array();
                }
                // remove event from monitored events
                if (in_array($old_event['id'], $dashlet_filters['events'])) {
                    $dashlet_filters['events'] = array_diff($dashlet_filters['events'], array($old_event['id']));
                }

                // new timestamp
                $result['timestamp'] = $dashlet_filters['timestamp'];

                // save filters in personal settings
                $this->_saveFilters($dashlet_filters);
            }

            $this->registry['messages']->setMessage($this->i18n('plugin_done'), '', -1);
        } else {
            $this->registry['messages']->setError($this->i18n('plugin_failed'), '', -1);
        }
        unset($task);

        // prepare success or error messages
        $result['errors'] = $this->_prepareMessages($this->registry['messages']->getErrors(), 'error');
        $result['messages'] = $this->_prepareMessages($this->registry['messages']->getMessages(), 'message');
        $this->registry['messages']->removeFromSession($this->registry);

        return json_encode($result);
    }

    /**
     * Save (add/edit) planned time event
     * @return string - result of the operation
     */
    public function savePlannedTime() {
        $request = &$this->registry['request'];

        $i18n_files = array(
            PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini',
            PH_MODULES_DIR . 'events/i18n/' . $this->registry['lang'] . '/events.ini'
        );
        $this->registry['translater']->loadFile($i18n_files);

        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
        $filters = array('where' => array('et.active = 1',
                                          'et.keyword = \'plannedtime\''),
                         'sanitize' => true);
        $type = Events_Types::searchOne($this->registry, $filters);
        if (!$type) {
            return json_encode(array(
                'errors' => $this->_prepareMessages(array($this->i18n('error_invalid_type')), 'error'),
                'close' => 1
            ));
        }

        require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.factory.php';

        // validate event for overlapping with any other event
        $request->set('event_end_date', $request->get('event_start_date'), 'all', true);
        $request->set('allday_event', ($request->get('event_start_time') || $request->get('event_end_time') ? '0' : '-1'), 'all', true);
        $event = Events::buildModel($this->registry);
        // set some values to pass validation
        // (we only need this model to check availability of user)
        $event->set('name', '-', true);
        $event->set('type', $type->get('id'), true);
        $event->set('type_keyword', 'plannedtime', true);
        $event->set('assigned_to_name', '-', true);
        $event->set('id', $event->get('event_id'), true);

        if ($event->get('owner') &&
            $event->get('event_start_date') >= General::strftime($this->i18n('date_iso_short')) &&
            $event->validate()) {
            // get assignments
            $this->registry->set('getAssignments', true, true);
            // let's pretend we are somewhere else, shall we?
            $this->registry->set('module', 'custom_module', true);
            list($availability, $user_intervals) = $event->checkAvailability($event->get('owner'), 'user', -1);
            $this->registry->set('module', 'dashlets', true);
            $this->registry->remove('getAssignments');
            if ($availability != 'available') {
                foreach ($user_intervals as $k => $v) {
                    if (!empty($v['conflict'])) {
                        $format = (substr($v['date_end'], 0, 10) == $event->get('event_start_date')) ? 'time_short' : 'date_mid';
                        $user_intervals[$k] =
                            ' - ' . General::strftime($this->i18n($format), strtotime($v['date'])) .
                            ' - ' . General::strftime($this->i18n($format), strtotime($v['date_end']));
                    } else {
                        unset($user_intervals[$k]);
                    }
                }
                // display error message for overlapping
                return json_encode(array('errors' => $this->_prepareMessages(
                    array($this->i18n('error_plugin_planned_time_overlap',
                                      array('<br />' . implode('<br />', $user_intervals)))), 'error')));
            }
        }

        $task = false;
        if ($event->get('task')) {
            $filters = array(
                'where' => array(
                    't.id=\'' . $event->get('task') . '\'',
                    't.status != \'finished\'',
                    't.active = 1'
                )
            );
            $task = Tasks::searchOne($this->registry, $filters);

            if ($task) {
                // check permissions for allocate + edit_allocate to task
                if (!($task->checkPermissions('allocate') && $task->checkPermissions('edit_allocate'))) {
                    if (!$event->get('id')) {
                        // add is not allowed
                        $task = false;
                    } else {
                        // check for edit access to event
                        $event->getAssignments();
                        $users_assignments = $event->get('users_assignments');
                        $currentUserId = $this->registry['currentUser']->get('id');
                        if (!(isset($users_assignments[$currentUserId]) && $users_assignments[$currentUserId]['access'] == 'edit')) {
                            // edit is not allowed
                            $task = false;
                        }
                    }
                }
            }
        }
        unset($event);

        if (!$task) {
            // display error message
            return json_encode(array(
                'errors' => $this->_prepareMessages(array($this->i18n('error_no_access_to_model')), 'error'),
                'close' => $request->get('event_id')
            ));
        }

        // before performing action - check if any of displayed data has been modified since it was last reloaded
        $dashlet_filters = $this->_getFiltersValues();
        $calendar_settings = $this->_prepareCalendarSettings();
        $res = array(
            'settings' => &$calendar_settings
        );
        $reload = $this->_checkForModifications($res);

        // set posted values as arrays because data is expected like that in Task::savePlannedTime method
        foreach ($request->getAll('post') as $k => $v) {
            $request->set($k, array($v), 'post', true);
        }

        // let's pretend we are somewhere else, shall we?
        $this->registry->set('module', 'custom_module', true);

        // set flag that method is called for just one planned time event
        // and some specific things should be done in Task::savePlannedTime method
        $task->set('save_single_planned_time', true, true);

        $result = array();
        if ($task->savePlannedTime(array('skip_delete' => true))) {
            // new timestamp to compare against
            $dashlet_filters['timestamp'] = time();

            Tasks_History::saveData($this->registry,
                                    array('model' => $task,
                                          'action_type' => 'allocate'));

            if ($reload) {
                // full reload (content or calendar)
                $result[$reload] = $this->filterPlannedTime($reload);
            } else {
                // partial reload

                // if any of the properties used for visualisation were modified,
                // event should be updated in dashlet
                if ($task->get('new_event')) {

                    // get data for event before and after save; if event was added, there is no old event
                    $old_event = $task->get('old_event');
                    if (is_array($old_event)) {
                        $old_event['owner'] = !empty($old_event['users_participants']) ?
                            $old_event['users_participants'][0]['participant_id'] : '';
                    }
                    $new_event = $task->get('new_event');
                    $new_event['event_start_date'] = $new_event['event_end_date'] =
                        substr($new_event['event_start'], 0, 10);
                    $new_event['event_start_time'] = substr($new_event['event_start'], 11, 5);
                    $new_event['event_end_time'] = substr($new_event['event_end'], 11, 5);
                    $new_event['owner'] = !empty($new_event['users_participants']) ?
                        $new_event['users_participants'][0]['participant_id'] : '';

                    $display_event = false;
                    list($first_day, $last_day) = $this->_getPeriodBoundaries();
                    // check if event date is within displayed period
                    if ($new_event['event_start_date'] >= $first_day && $new_event['event_start_date'] <= $last_day) {
                        // check if participant is among displayed users
                        if ($this->get('participant') &&
                            (in_array('user_' . $new_event['owner'], $this->get('participant')) ||
                            in_array($new_event['owner'], $this->_prepareUserIds()))) {
                            $display_event = true;
                        }
                    }

                    if ($display_event) {
                        // prepare some more data for visualisation of event
                        if (!$this->get('project') || in_array($task->get('project'), $this->get('project'))) {
                            $this->_defineColor($task);
                            $new_event['background_color'] = $task->get('background_color');
                        }
                        $new_event['view'] = $new_event['edit_allocate'] = 1;
                        $new_event['task'] = $task->get('id');
                        $new_event['task_name'] = $task->get('name');
                        $new_event['task_severity'] = $task->get('severity');

                        // here we get all events for the new day and user
                        $res = array(
                            'settings' => &$calendar_settings,
                            'days' => array($new_event['event_start_date']),
                            'user_ids' => array($new_event['owner'])
                        );
                        $this->set('after_save', true, true);
                        $this->_prepareEvents($res);
                        $new_data = isset($res['event_ids'][$new_event['event_start_date']][$new_event['owner']]) ?
                            $res['event_ids'][$new_event['event_start_date']][$new_event['owner']] : array();
                        unset($res);

                        // prepare positioning
                        $position = $this->_prepareEventPosition($new_event, $calendar_settings);

                        // w0 - width of allday events cell, w1 - width of hour cells
                        if ($position == 'allday_events') {
                            // not fixed
                            $offset = 0;
                            $width = self::W0;
                        } elseif ($position == 'events_before') {
                            // before
                            $offset = self::W0;
                            $width = self::W1;
                        } elseif ($position == 'events') {
                            // within
                            $offset = self::W0+($calendar_settings['week_start_hour']>0)*self::W1;
                            $width = self::W1;
                        } else {
                            // after
                            $offset = self::W0+($calendar_settings['week_end_hour']-$calendar_settings['week_start_hour']+1+($calendar_settings['week_start_hour']>0))*self::W1;
                            $width = self::W1;
                        }

                        // fetch the result div and reposition it
                        $viewer = new Viewer($this->registry);
                        $viewer->loadCustomI18NFiles(sprintf('%s%s%s%s%s%s', PH_MODULES_DIR, 'dashlets/plugins/', $this->plugin_name, '/i18n/', $this->registry['lang'], '/custom.ini'));
                        $viewer->setFrameset('frameset_blank.html');
                        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
                        $viewer->template = '_events.html';
                        $viewer->data['events'] = array($new_event['id'] => $new_event);
                        $chunk = isset($new_data[$position][$new_event['id']]) ?
                            $new_data[$position][$new_event['id']] :
                            array('id' => $new_event['id']);
                        $viewer->data['event_ids_chunk'] = array($new_event['id'] => $chunk);
                        if ($position == 'events') {
                            $viewer->data['day_start'] = 60*$calendar_settings['week_start_hour'];
                        } else {
                            $viewer->data['max_count'] = self::MAX_NO_OL;
                        }
                        $viewer->data['offset'] = $offset;
                        $viewer->data['width'] = $width;
                        $viewer->data['day'] = $new_event['event_start_date'];
                        $viewer->data['uid'] = $new_event['owner'];

                        // prepare content to display for event
                        $result['event'] = array(
                            'id' => $new_event['id'],
                            'user' => $new_event['owner'],
                            'date' => $new_event['event_start_date'],
                            'position' => $position,
                            'group' => (isset($new_data[$position]) ? $new_data[$position] : array()), // data for sub-array where new event was located
                            'duration' => $new_data['duration'],
                            'content' => $viewer->fetch()
                        );
                    }

                    if ($old_event) {
                        // get position in which sub-array of old event was located
                        $position = $this->_prepareEventPosition($old_event, $calendar_settings);

                        // check if old and new event should update the same location
                        if (!empty($result['event']) && $result['event']['user'] == $old_event['owner'] &&
                        $result['event']['date'] == $old_event['event_start_date'] &&
                        $result['event']['position'] == $position) {
                            // same location, do not prepare data for update
                        } else {
                            // here we get all events for the old day and user
                            $res = array(
                                'settings' => &$calendar_settings,
                                'days' => array($old_event['event_start_date']),
                                'user_ids' => array($old_event['owner'])
                            );
                            $this->set('after_save', true, true);
                            $this->_prepareEvents($res);
                            $old_data = isset($res['event_ids'][$old_event['event_start_date']][$old_event['owner']]) ?
                                $res['event_ids'][$old_event['event_start_date']][$old_event['owner']] : array();
                            unset($res);

                            // remove old event
                            $result['old_event'] = array(
                                'id' => $old_event['id'],
                                'user' => $old_event['owner'],
                                'date' => $old_event['event_start_date'],
                                'position' => $position,
                                'group' => (isset($old_data[$position]) ? $old_data[$position] : array()), // data for sub-array where old event was located
                                'duration' => (isset($old_data['duration']) ? $old_data['duration'] : 0) // absolute value of duration for old day
                            );
                        }
                    }

                    // update unallocated time of task
                    if ($task->get('planned_time') && $task->get('background_color')) {
                        $result['task'] = array(
                            'id' => $task->get('id'),
                            'remaining_time' => intval($task->get('planned_time') - $task->getPlannedDuration())
                        );
                    }

                    if (empty($dashlet_filters['events'])) {
                        $dashlet_filters['events'] = array();
                    }
                    if ($display_event) {
                        // if displaying event, add it to monitored events
                        if (!in_array($new_event['id'], $dashlet_filters['events'])) {
                            $dashlet_filters['events'][] = $new_event['id'];
                        }
                    } else {
                        // if not displaying event, remove it from monitored events
                        if ($old_event && in_array($old_event['id'], $dashlet_filters['events'])) {
                            $dashlet_filters['events'] = array_diff($dashlet_filters['events'], array($old_event['id']));
                        }
                    }

                    unset($old_event);
                    unset($new_event);
                }

                // new timestamp
                $result['timestamp'] = $dashlet_filters['timestamp'];

                // save filters in personal settings
                $this->_saveFilters($dashlet_filters);
            }

            $this->registry['messages']->setMessage($this->i18n('plugin_done'), '', -1);
        } else {
            $this->registry['messages']->setError($this->i18n('plugin_failed'), '', -1);
        }
        unset($task);

        // prepare success or error messages
        $result['errors'] = $this->_prepareMessages($this->registry['messages']->getErrors(), 'error');
        $result['messages'] = $this->_prepareMessages($this->registry['messages']->getMessages(), 'message');
        $this->registry['messages']->removeFromSession($this->registry);

        return json_encode($result);
    }

    /**
     * Prepare data for lightbox for add/edit of planned time
     * @return string|boolean
     */
    public function showForm() {
        $registry = $this->registry;
        $request = &$this->registry['request'];
        $currentUserId = $this->registry['currentUser']->get('id');
        $errors = array();

        $i18n_files = array(PH_MODULES_DIR . 'events/i18n/' . $this->registry['lang'] . '/events.ini');
        $this->registry['translater']->loadFile($i18n_files);

        // get event type
        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
        $filters = array('where' => array('et.active = 1',
                                          'et.keyword = \'plannedtime\''),
                         'sanitize' => true);
        $type = Events_Types::searchOne($this->registry, $filters);
        if (!$type) {
            $errors[] = $this->i18n('error_invalid_type');
            return json_encode(array('errors' => $this->_prepareMessages($errors, 'error')));
        }

        $action = $request->get('event') ? 'edit' : 'add';
        if ($action == 'edit') {
            // edit event - get from db
            $filters = array('where' => array('e.id=\'' . intval($request->get('event')) . '\'',
                                              'e.type=' . $type->get('id'),
                                              'e.status=\'planning\'',
                                              'e.active=1'),
                             'sanitize' => true);
            $this->registry->set('getAssignments', true, true);
            $this->registry->set('module', 'custom_module', true);
            $event = Events::searchOne($registry, $filters);
            $this->registry->set('module', 'dashlets', true);
            $this->registry->remove('getAssignments');

            if ($event) {
                // if user is an assignee with edit access, edit is allowed
                if (array_filter($event->get('users_assignments'),
                    function($a) use ($currentUserId) {
                        return $a['participant_id'] == $currentUserId && $a['access'] == 'edit'; })) {
                    $event->set('edit_allocate', 1, true);
                }

                $task = $event->getPlannedTimeTask();
                if ($task) {
                    // if user is allowed to edit planned time allocation of task, edit is allowed
                    if (!$event->get('edit_allocate') && $task->checkPermissions('allocate') && $task->checkPermissions('edit_allocate')) {
                        $event->set('edit_allocate', 1, true);
                    }

                    // copy some data from task
                    $event->set('user_permissions', $task->get('user_permissions'), true);
                    $event->set('task', $task->get('id'), true);
                    $event->set('task_name', $task->get('name'), true);
                    $event->set('task_severity', $task->get('severity'), true);

                    // date_min, date_max - for validation regarding task
                    $date_min = substr($task->get('planned_start_date'), 0, 10);
                    $today = General::strftime($this->i18n('date_iso_short'));
                    if ($date_min < $today) {
                        $date_min = $today;
                    }
                    $event->set('date_min', $date_min, true);
                    $event->set('date_max', substr($task->get('planned_finish_date'), 0, 10), true);

                    // if edit is not allowed, display error
                    if (!$event->get('edit_allocate')) {
                        $event = null;
                    }
                } else {
                    // if task is deleted
                    $event = null;
                }
                unset($task);

            }
        } elseif ($registry['currentUser']->checkRights('tasks', 'allocate')) {
            // add event - set params from request
            $end_time = intval($request->get('start'))*60 + $type->get('default_duration');
            if ($end_time > 1439) {
                $end_time = 1439;
            }
            $params = array(
                'users_participants' => array(array('participant_id' => $request->get('user'))),
                'type' => $type->get('id'),
                'type_name' => $type->get('name'),
                'type_keyword' => 'plannedtime',
                'allday_event' => ($request->isRequested('start') ? '0' : '-1'),
                'event_start_date' => $request->get('date'),
                'event_start_time' => sprintf('%02d:00', $request->get('start')),
                'event_end_date' => $request->get('date'),
                'event_end_time' => sprintf('%02d:%02d', floor($end_time/60), $end_time%60),
                'duration' => ($request->isRequested('start') ? '' : $type->get('default_duration')),
            );

            $event = new Event($registry, $params);
        }

        $result = array();
        if (empty($event)) {
            // action is not allowed
            $errors[] = $this->i18n('error_' . $action . '_notallowed');
            return json_encode(array('errors' => $this->_prepareMessages($errors, 'error')));
        }

        $viewer = new Viewer($registry);
        $viewer->loadCustomI18NFiles(sprintf('%s%s%s%s%s%s', PH_MODULES_DIR, 'dashlets/plugins/', $this->plugin_name, '/i18n/', $registry['lang'], '/custom.ini'));
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        $viewer->template = '_event_form.html';
        $viewer->data['dashlet'] = $registry['dashlet'];
        $viewer->data['event'] = $event->getAll();
        $viewer->data['priority_options'] =
            array('skip_please_select' => true) + Events_Dropdown::getPriorities(array($registry));

        if ($action == 'add') {
            $viewer->data['project_autocomplete'] = array(
                'type'          => 'projects',
                'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'projects', 'projects', 'ajax_select'),
                'filters'       => array(
                    '<type>'    => ($this->get('projects_filter') ? ($this->get('projects_types') ? implode(',', $this->get('projects_types')) : '0') : null),
                    '<status>'  => '!= finished'
                ),
                'fill_options'  => array(
                    '$pt_project => <id>',
                    '$pt_project_autocomplete => <name>',
                    '$pt_project_oldvalue => <name>',
                    '$pt_task => ',
                    '$pt_task_autocomplete => ',
                    '$pt_task_oldvalue => '
                ),
                'suggestions'   => '<name>',
                'buttons_hide'  => 'search',
                'buttons'       => 'clear',
                'execute_after' => 'plannedTime.onProjectSelect'
            );
            $query = 'SELECT id FROM ' . DB_TABLE_TASKS . ' WHERE user_permissions LIKE \'%,' . $this->registry['currentUser']->get('id') . ',%\' AND active = 1 AND deleted_by = 0 AND status != \'finished\'';
            $assigned_tasks = $this->registry['db']->GetCol($query) ?: array(0);
            $viewer->data['task_autocomplete'] = array(
                'type'          => 'tasks',
                'url'           => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'tasks', 'tasks', 'ajax_select'),
                'filters'       => array(
                    '<status>'  => '!= finished',
                    '<id>' => 'IN (' . implode(',', $assigned_tasks) . ')'
                ),
                'fill_options'  => array(
                    '$pt_project => <project>',
                    '$pt_project_autocomplete => <project_name>',
                    '$pt_project_oldvalue => <project_name>',
                    '$pt_task => <id>',
                    '$pt_task_autocomplete => <name>',
                    '$pt_task_oldvalue => <name>',
                    '$pt_planned_start_date => <planned_start_date>',
                    '$pt_planned_finish_date => <planned_finish_date>',
                    '$pt_user_permissions => <user_permissions>',
                ),
                'suggestions'   => '<customer_name> - <name>',
                'buttons_hide'  => 'search',
                'buttons'       => 'clear',
                'clear_fields'  => array(
                    '$pt_task',
                    '$pt_task_autocomplete',
                    '$pt_task_oldvalue'
                ),
                'execute_after' => 'plannedTime.onTaskSelect'
            );
            $viewer->data['users_options'] = array(
                array('option_value' => $request->get('user'), 'label' => $request->get('user_name'))
            );
        } else {
            $users = array();
            $user_permissions = strval($event->get('user_permissions'));
            if ($user_permissions) {
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';
                $filters = array('where' => array('u.id IN (' . preg_replace('#^,(.+),$#', '$1', $user_permissions) . ')'),
                                 'sort' => array('ui18n.firstname ASC', 'ui18n.lastname ASC', 'u.active DESC' , 'u.id ASC'),
                                 'sanitize' => true);
                $users = Users::search($this->registry, $filters);
                $user_options = array();
                foreach ($users as $u) {
                    $user_options[$u->get('id')] = array(
                        'option_value' => $u->get('id'),
                        'label' => $u->get('firstname') . ' ' . $u->get('lastname'),
                        'active_option' => $u->get('active')
                    );
                }
                $users = $user_options;
            }
            $viewer->data['users_options'] = $users;
        }

        $result['content'] = $viewer->fetch();
        $result['title'] = $this->i18n($request->get('event') ? 'edit' : 'add') . ' ' . $type->get('name');

        return json_encode($result);
    }

    /******************** private area ********************/

    /**
     * Save filters in personal settings
     * @param array $filters - values to save
     * @return bool - result of the operation
     */
    private function _saveFilters($filters) {
        $query = 'INSERT INTO ' . DB_TABLE_PERSONAL_SETTINGS . ' ( `user_id`, `section`, `name`, `value`) VALUES' . "\n" .
                 '(\'' . $this->registry['currentUser']->get('id') . '\', \'dashlets_plugins\', \'dashlet_' . $this->get('dashlet_id') . '\', \'' . serialize($filters) . '\')' . "\n" .
                 'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`)';
        $this->registry['db']->Execute($query);
        return !$this->registry['db']->HasFailedTrans();
    }

    /**
     * Get filter values for dashlet from request or from personal settings
     * @return array - array with filter values
     */
    private function _getFiltersValues() {

        $request = &$this->registry['request'];

        $filters = array();
        if ($this->registry['currentUser']) {
            $filters = $this->registry['currentUser']->getPersonalSettings('dashlets_plugins', 'dashlet_' . $this->get('dashlet_id'));
            if ($filters) {
                $filters = unserialize($filters);
            }
            if (!is_array($filters)) {
                $filters = array();
            }
            foreach (array('participant', 'project') as $key) {
                if (!isset($filters[$key])) {
                    $filters[$key] = array();
                }
            }
        }

        if ($request->isRequested('period')) {
            // selected period and filters
            $filters['period'] = $request->get('period');
            $filters['participant'] = $request->get('participant_filters') ?: array();
            $filters['project'] = $request->get('project_filters') ?: array();
        } else {
            // default period is current week
            if (empty($filters['period'])) {
                $today_ts = strtotime(General::strftime('%Y-%m-%d'));
                $filters['period'] = General::strftime('%Y-%m-%d', strtotime('-' . ((General::strftime('%w', $today_ts) ?: 7) - 1) . ' day', $today_ts));
            }

            // default participant is current user
            if (empty($filters['participant']) && $this->registry['currentUser']) {
                $filters['participant'] = array('user_' . $this->registry['currentUser']->get('id'));
            }
        }

        // set filters into model to be available for every method
        foreach ($filters as $k => $v) {
            $this->set($k, $v, true);
        }

        return $filters;
    }

    /**
     * Prepare filter values and labels for display in interface
     *
     * @param array $result - result values to be displayed
     */
    private function _prepareFiltersValues(&$result) {

        $result['filters_values'] = array();

        $participant_filters = $this->get('participant');
        if (!empty($participant_filters)) {

            $user_ids = $department_ids = array();
            $matches = array();
            foreach ($participant_filters as $p) {
                if (preg_match('#^(department|user)_(\d+)$#', $p, $matches)) {
                    if ($matches[1] == 'department') {
                        $department_ids[] = $matches[2];
                    } else {
                        $user_ids[] = $matches[2];
                    }
                }
            }

            $result['filters_values']['participant'] = array_fill_keys($participant_filters, false);
            if ($department_ids) {
                $f = array(
                    $this->registry,
                    'table' => 'DB_TABLE_DEPARTMENTS',
                    'table_i18n' => 'DB_TABLE_DEPARTMENTS_I18N',
                    'where' => 't.active=1 AND t.id IN (' . implode(',', $department_ids) . ')',
                    'order_by' => 'FIND_IN_SET(t.id, "' . implode(',', $department_ids) . '")',
                    'value_prefix' => 'department_',
                    'assoc' => true,
                    'skip_current_user_departments' => true
                );
                foreach (Dropdown::getCustomDropdown($f) as $k => $v) {
                    $result['filters_values']['participant'][$k] = array('id' => $k, 'name' => $v['label']);
                }
            }
            if ($user_ids) {
                $f = array(
                    $this->registry,
                    'table' => 'DB_TABLE_USERS',
                    'table_i18n' => 'DB_TABLE_USERS_I18N',
                    'where' => 't.active=1 AND t.id IN (' . implode(',', $user_ids) . ')',
                    'order_by' => 'FIND_IN_SET(t.id, "' . implode(',', $user_ids) . '")',
                    'value_prefix' => 'user_',
                    'label' => 'firstname,lastname',
                    'assoc' => true
                );
                foreach (Dropdown::getCustomDropdown($f) as $k => $v) {
                    $result['filters_values']['participant'][$k] = array('id' => $k, 'name' => $v['label']);
                }
            }
            $result['filters_values']['participant'] = array_values(array_filter($result['filters_values']['participant']));
        }

        $project_filters = $this->get('project');
        if (!empty($project_filters)) {
            $f = array(
                $this->registry,
                'table' => 'DB_TABLE_PROJECTS',
                'table_i18n' => 'DB_TABLE_PROJECTS_I18N',
                'where' => 't.active=1 AND t.status!=\'finished\'' .
                           ' AND t.type IN (' . ($this->get('projects_types') ? implode(',', $this->get('projects_types')) : '0') . ')' .
                           ' AND t.id IN (' . implode(',', $project_filters) . ')',
                'order_by' => 'FIND_IN_SET(t.id, "' . implode(',', $project_filters) . '")',
                'assoc' => true
            );
            $result['filters_values']['project'] = array();
            foreach (Dropdown::getCustomDropdown($f) as $k => $v) {
                $result['filters_values']['project'][] = array('id' => $k, 'name' => $v['label']);
            }
        }
    }

    /**
     * Get ids of users to be displayed (ORDERED) and set them to model
     * @return array - ordered user ids
     */
    private function _prepareUserIds() {

        if ($this->isDefined('user_ids')) {
            // get the ids from model
            $user_ids_ordered = $this->get('user_ids');
        } else {
            // get the ids from participant filters
            $user_ids_ordered = $participants_ordered = $dep_user_ids = array();
            $participant_filters = $this->get('participant') ?: array();

            $matches = array();
            foreach ($participant_filters as $p) {
                if (preg_match('#^(department|user)_(\d+)$#', $p, $matches)) {
                    if ($matches[1] == 'department') {
                        $participants_ordered[$p] = array();
                        $dep_users = Departments::getUsers($this->registry, array('where' => array('d.id=' . $matches[2])));
                        if ($dep_users) {
                            foreach ($dep_users as $u) {
                                // if users belong to multiple departments, they are
                                // displayed in the first department specified
                                if (in_array($u['id'], $dep_user_ids)) {
                                    continue;
                                }
                                // if users are specified individually but also belong to department filters,
                                // they are displayed in the first department specified
                                if (array_key_exists('user_' . $u['id'], $participants_ordered)) {
                                    unset($participants_ordered['user_' . $u['id']]);
                                    $user_ids_ordered = array_diff($user_ids_ordered, array($u['id']));
                                }
                                // collect all user ids for current department
                                $participants_ordered[$p][] = $u['id'];
                                // collect all user ids added as part of a department
                                $dep_user_ids[] = $u['id'];
                            }
                        }
                        unset($dep_users);
                    } else {
                        // if users are already added, they must be members of some specified department
                        if (in_array($matches[2], $user_ids_ordered)) {
                            continue;
                        } else {
                            $participants_ordered[$p] = array($matches[2]);
                        }
                    }
                    // accumulate all user ids in the order users should be displayed
                    $user_ids_ordered = array_merge($user_ids_ordered, $participants_ordered[$p]);
                }
            }
            unset($participants_ordered);
            unset($dep_user_ids);

            $this->set('user_ids', $user_ids_ordered, true);
        }

        return $user_ids_ordered;
    }

    /**
     * Prepare displayed info for users (name, department, working time)
     * @param array $result - result values to be displayed
     * @return array - ordered user ids
     */
    private function _prepareUsers(&$result) {
        $user_ids_ordered = $this->_prepareUserIds();
        $result['users'] = array();
        if ($user_ids_ordered) {
            $query =
                'SELECT u.id AS idx,' . "\n" .
                '  CONCAT(IF(ui18n.firstname IS NOT NULL, ui18n.firstname, ""), " ", IF(ui18n.lastname IS NOT NULL, ui18n.lastname, "")) AS name,' . "\n" .
                '  IF(udi18n.name IS NOT NULL, CONCAT(" (", udi18n.name, ")"), "") AS department,' . "\n" .
                '  ROUND(IF(u.working_hours > 0, u.working_hours, ' . $this->get('working_hours') . ')*60) AS working_time' . "\n" .
                'FROM ' . DB_TABLE_USERS . ' AS u ' . "\n" .
                'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n ' . "\n" .
                '  ON u.id=ui18n.parent_id AND ui18n.lang="' . $this->registry['lang'] . '" ' . "\n" .
                'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS udi18n ' . "\n" .
                '  ON (u.default_department=udi18n.parent_id AND udi18n.lang="' . $this->registry['lang'] . '") ' . "\n" .
                'WHERE u.active=1 AND u.deleted_by=0 AND u.hidden=0 AND u.id IN (' . implode(',', $user_ids_ordered) . ')' . "\n" .
                'GROUP BY u.id' . "\n" .
                'ORDER BY FIND_IN_SET(u.id, "' . implode(',', $user_ids_ordered) . '"), name ASC';
            $result['users'] = $this->registry['db']->getAssoc($query);

            // get only the ids returned from search
            $user_ids_ordered = array_keys($result['users']);
        }

        return $user_ids_ordered;
    }

    /**
     * Prepare tasks grouped by projects
     * @param array $result - result values to be displayed
     */
    private function _prepareTasks(&$result) {

        $i18n_files = array(
            PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini',
        );
        $this->registry['translater']->loadFile($i18n_files);

        // filter tasks
        $filters = array(
            'where' => array(
                't.status != \'finished\'',
                't.active = 1',
                'DATE(t.planned_start_date) <= DATE(DATE_ADD(\'' . $this->get('period') . '\', INTERVAL 6 DAY))',
                'DATE(t.planned_finish_date) >= DATE(\'' . $this->get('period') . '\')',
                't.user_permissions RLIKE \',(' . ($this->get('user_ids') ? implode('|', $this->get('user_ids')) : '0') . '),\''
            ),
            'sort' => array('t.severity DESC', 't.planned_start_date ASC', 't.id ASC'),
            'sanitize' => true,
            'get_fields' => array('name', 'project', 'status')
        );
        if ($this->get('project')) {
            $filters['where'][] = 't.project IN (\'' . implode('\', \'', $this->get('project')) . '\')';
        }
        $tasks = Tasks::search($this->registry, $filters);

        $projects = $tasks_assoc = array();
        foreach ($tasks as $task) {

            $pid = $task->get('project');
            if (!isset($projects[$pid])) {
                $projects[$pid] = array(
                    'name' => ($pid ?
                               ($task->get('project_name') ?: '(' . $this->i18n('plugin_project') . ')') :
                               $this->i18n('plugin_no_project')),
                    'tasks' => array()
                );
            }

            $tid = $task->get('id');
            $projects[$pid]['tasks'][] = $tid;

            if (!$task->get('name')) {
                $task->set('name', '(' . $this->i18n('plugin_task') . ')', true);
            }
            $this->_defineColor($task);
            // take only the necessary properties
            $tasks_assoc[$tid] = array();
            foreach (array('name', 'type_name', 'severity',
                           'status', 'status_name', 'substatus_name',
                           'planned_time', 'planned_time_formatted', 'timesheet_time_formatted',
                           'planned_start_date', 'planned_finish_date', 'background_color') as $prop) {
                $tasks_assoc[$tid][$prop] = $task->get($prop);
            }
            $tasks_assoc[$tid]['remaining_time'] = $task->get('planned_time') ? $task->get('planned_time') - $task->getPlannedDuration() : '';
            // check if current user is allowed to edit planned time allocation of task
            $tasks_assoc[$tid]['edit_allocate'] = $task->checkPermissions('allocate') && $task->checkPermissions('edit_allocate');
            $tasks_assoc[$tid]['user_permissions'] = array_filter(explode(',', $task->get('user_permissions')));
        }
        unset($tasks);

        // move tasks without project to the end of list
        $tasks_no_project = false;
        if (isset($projects[0])) {
            $tasks_no_project = array(0 => $projects[0]);
            unset($projects[0]);
        }

        uasort($projects, function ($a, $b) {
            return $a['name'] < $b['name'] ? -1 : 1;
        });
        if ($tasks_no_project) {
            $projects = $projects + $tasks_no_project;
        }

        $result['projects'] = $projects;
        $result['tasks'] = $tasks_assoc;
    }

    /**
     * Get first and last date of the displayed weekly period for a specified date
     * @return array - indexed array of dates
     */
    private function _getPeriodBoundaries() {
        $selected_date = $this->get('period') ?: General::strftime($this->i18n('date_iso_short'));
        $week_days = $this->registry['currentUser']->getPersonalSettings('calendars', 'week_days_number') ?: 7;
        $first_day = date_create($selected_date)->format('N') - 1;
        $first_day = date_sub(date_create($selected_date), new DateInterval('P' . $first_day . 'D'))->format('Y-m-d');
        $last_day = date_add(date_create($first_day), new DateInterval('P' . ($week_days-1) . 'D'))->format('Y-m-d');
        return array($first_day, $last_day);
    }

    /**
     * Prepare displayed days
     * @param array $result - result values to be displayed
     */
    private function _prepareDays(&$result) {
        list($first_day, $last_day) = $this->_getPeriodBoundaries();
        for ($i = $first_day; $i <= $last_day; $i = date_add(date_create($i), new DateInterval('P1D'))->format('Y-m-d')) {
            $result['days'][] = $i;
            $result['working_days'][] = Calendars_Calendar::isWorkingDay($this->registry, $i);
        }
    }

    /**
     * Get calendar settings from saved personal settings or use default values
     * @return array - calendar settings
     */
    private function _prepareCalendarSettings() {
        return array_merge(
            array('week_start_hour' => 7, 'week_end_hour' => 19, 'week_days_number' => 7),
            $this->registry['currentUser']->getPersonalSettings('calendars')
        );
    }

    /**
     * Prepare workload criteria (for coloring last column of calendar)
     * @return multitype:string
     */
    private function _prepareWorkload() {
        $workload = array(
            (intval($this->get('workload_veryheavy')) ?: 100)   => 'veryheavy',
            (intval($this->get('workload_heavy')) ?: 90)        => 'heavy',
            (intval($this->get('workload_intermediate')) ?: 75) => 'intermediate'
        );
        krsort($workload, SORT_NUMERIC|SORT_DESC);

        return $workload;
    }

    /**
     * Prepare displayed events - positioning, overlapping, colors, permissions etc.
     * @param array $result - result values to be displayed
     */
    private function _prepareEvents(&$result) {

        $db = &$this->registry['db'];

        $user_ids_ordered = $this->get('user_ids') ?: array();
        if (!empty($result['user_ids'])) {
            $user_ids_ordered = $result['user_ids'];
        }
        if (!$user_ids_ordered) {
            return;
        }

        //get calendar settings
        $calendar_settings = &$result['settings'];

        //define some variables
        $day_start = 60 * $calendar_settings['week_start_hour'];
        $day_end   = 60 * $calendar_settings['week_end_hour'] + 60;
        //minimum event interval
        //$min_interval = 30;

        $filters_search = array(
            'get_fields' => array('name', 'project'),
            'sanitize' => true,
            // a flag to instruct processing of the twisted filter by participant_id
            'has_calendar_filters' => 1,
            // we need to know current user's relation to event
            'participant_id' => $this->registry['currentUser']->get('id'),
            'where' => array(
                // this is the twisted filter
                'ea.participant_id IN user-(' . implode(',', $user_ids_ordered) . ')',
                'e.active=1',
                'e.availability=\'busy\'',
                'e.status IN (\'planning\', \'progress\', \'finished\')'
            ),
            'event_start' => reset($result['days']) . ' 00:00:00',
            'event_end' => end($result['days']) . ' 23:59:00',
        );
        $this->registry->set('getAssignments', true, true);
        // let's pretend we are somewhere else, shall we?
        $this->registry->set('module', 'custom_module', true);
        $events_array = Events::search($this->registry, $filters_search);
        $this->registry->set('module', 'dashlets', true);
        $this->registry->remove('getAssignments');

        $rights = $this->registry['currentUser']->getRights();
        $list_allowed = !empty($rights['events']['list']) && $rights['events']['list'] != 'none';

        $events_assoc = array();
        foreach ($events_array as $evt) {
            $ukey = $evt->get('id');
            if (isset($events_assoc[$ukey])) {
                // recurring daily events should be added for every day
                $ukey .= '|' . $evt->get('event_start_date');
            }
            // check event permissions in order to restict displayed info
            $view = 0;
            if ($list_allowed && !empty($rights['events' . $evt->get('type')]['list']) && $rights['events' . $evt->get('type')]['list'] != 'none') {
                if ($rights['events' . $evt->get('type')]['list'] == 'all' ||
                $this->registry['currentUser']->get('id') == $evt->get('added_by') ||
                $evt->get('access') ||
                $rights['events' . $evt->get('type')]['list'] == 'group' &&
                    in_array($evt->get('group'), $this->registry['currentUser']->get('groups'))) {
                    $view = 1;
                }
            }
            $evt->set('view', $view, true);
            if ($evt->get('type_keyword') != 'plannedtime') {
                $evt->set('background_color',
                    $this->get(($evt->get('allday_event') ? 'allday_' : '') . 'events_background_color'), true);
            }
            $events_assoc[$ukey] = $evt->getAll();
        }
        unset($rights);
        unset($events_array);

        // sort events by start date so the recurrent events go into place
        uasort($events_assoc, array('Events', 'sortMonthEventsByStartDate'));

        // we will assign event data just once and will group the ids by day and participant
        $event_ids_ordered = $pt_ids = array();
        foreach ($events_assoc as $eid => $evt) {
            $ukey = $eid; //unique key
            // if recurrent daily event, get event id from key
            $pipe_pos = strpos($eid, '|');
            if ($pipe_pos !== false) {
                $eid = substr($eid, $pipe_pos);
            }

            // check if planned time should be in color
            if ($evt['type_keyword'] == 'plannedtime') {
                $pt_ids[] = $eid;
            }

            $event_start_date = substr($evt['event_start'], 0, 10);
            $event_end_date = substr($evt['event_end'], 0, 10);
            $participant_ids = array_intersect($user_ids_ordered,
                array_keys(array_filter($evt['users_assignments'], function($a) {
                    return $a['ownership'] == 'mine' && $a['user_status'] != 'denied';
                })));

            for ($i = $event_start_date; $i <= $event_end_date; $i = date_add(date_create($i), new DateInterval('P1D'))->format('Y-m-d')) {
                foreach ($participant_ids as $p) {
                    if (!isset($event_ids_ordered[$i][$p]['duration'])) {
                        $event_ids_ordered[$i][$p]['duration'] = 0;
                    }
                    if ($evt['allday_event']) {
                        $event_ids_ordered[$i][$p]['allday_events'][$ukey] =
                            array(
                                'id' => $eid,
                                'pt' => ($evt['type_keyword'] == 'plannedtime'),
                                'start' => 0,
                                'end' => ($evt['allday_event'] == -1 ? $evt['duration'] : 23*60+59)
                            );
                        if ($evt['allday_event'] == -1) {
                            $event_ids_ordered[$i][$p]['duration'] += $evt['duration'];
                        }
                    } else {
                        // start and end of this day
                        $selected_day_start = $i . ' 00:00:00';
                        $selected_day_end = $i . ' 23:59:59';

                        $info = array(
                            'id' => $eid,
                            'pt' => ($evt['type_keyword'] == 'plannedtime')
                        );
                        if ($evt['event_start'] < $selected_day_start) {
                            $info['start'] = 0;
                        } else {
                            $info['start'] =
                                (int)((strtotime($evt['event_start']) - strtotime($selected_day_start))/60);
                        }
                        if ($evt['event_end'] > $selected_day_end) {
                            $info['end'] = 23*60+59;
                        } else {
                            $info['end'] =
                                (int)((strtotime($evt['event_end']) - strtotime($selected_day_start))/60);
                            // we want to display real duration of events even if very small
                            /* if ($info['end'] - $info['start'] < $min_interval) {
                                $info['end'] += $min_interval - $info['end'] + $info['start'];
                            } */
                        }

                        // store data for positioning
                        $event_ids_ordered[$i][$p]['events'][$ukey] = $info;
                    }
                }
            }
        }

        // prepare data for overlapping
        foreach ($event_ids_ordered as $date => $date_events) {
            foreach ($date_events as $p => $user_events) {
                if (!empty($user_events['events'])) {
                    $events = $user_events['events'];
                    uasort($events, array('Events', 'sortEventsByStartDate'));
                    $event_groups = array();
                    $j = -1;
                    $end = -1;

                    foreach ($events as $i => $event) {
                        if ($event['start'] < $end) {
                            array_push($event_groups[$j], $i);
                            if ($event['end'] > $end) {
                                $end = $event['end'];
                            }
                        } else {
                            $event_groups[++$j] = array();
                            array_push($event_groups[$j], $i);
                            $end = $event['end'];
                        }
                    }

                    foreach ($event_groups as $event_group) {
                        // in case of overlapping events, add the duration of
                        // the group, not of the separate events
                        $group_end = -1;
                        foreach ($event_group as $pos => $event_index) {
                            $events[$event_index]['pos'] = $pos;
                            $events[$event_index]['count'] = count($event_group);
                            if ($events[$event_index]['end'] > $group_end) {
                                $group_end = $events[$event_index]['end'];
                                // if event spans over several days, add 1 minute to complete duration
                                if ($group_end == 1439 && $events_assoc[$event_index]['event_end'] > $date . ' 23:59:00') {
                                    $group_end++;
                                }
                            }
                        }
                        $event_ids_ordered[$date][$p]['duration'] += $group_end - $events[reset($event_group)]['start'];
                    }

                    // now we will trim the events that overlap start/end and
                    // will move the rest to the before/after groups
                    foreach ($events as $i => $event) {
                        if ($event['end'] <= $day_start) {
                            $event_ids_ordered[$date][$p]['events_before'][$i] = $event;
                            unset($events[$i]);
                        } elseif ($event['start'] >= $day_end) {
                            $event_ids_ordered[$date][$p]['events_after'][$i] = $event;
                            unset($events[$i]);
                        } else {
                            if ($event['start'] < $day_start) {
                                $events[$i]['start'] = $day_start;
                            }
                            if ($event['end'] > $day_end) {
                                $events[$i]['end'] = $day_end;
                            }
                        }
                    }

                    $event_ids_ordered[$date][$p]['events'] = $events;
                }
            }
        }

        $result['event_ids'] = $event_ids_ordered;

        // if method is called for just one day and one user after save, we have all we need
        if ($this->get('after_save')) {
            unset($pt_ids);
            unset($events_assoc);
            return;
        }

        // perform additional preparations for planned times
        if ($pt_ids) {
            $query = 'SELECT er.parent_id, er.link_to AS task, ti.name AS task_name, t.*' . "\n" .
                     'FROM ' . DB_TABLE_EVENTS_RELATIVES . ' AS er' . "\n" .
                     'JOIN ' . DB_TABLE_TASKS . ' AS t' . "\n" .
                     '  ON er.link_to=t.id AND er.origin=\'task\' AND er.link_type=\'parent\'' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS ti' . "\n" .
                     '  ON t.id=ti.parent_id AND ti.lang=\'' . $this->registry['lang'] . '\'' . "\n" .
                     'WHERE er.parent_id IN (' . implode(',', $pt_ids) . ')';
            $pt_ids = $db->getAssoc($query);

            // collect permissions for tasks for non-selected projects
            $other_tasks = array();

            foreach ($pt_ids as $eid => $einfo) {
                $events_assoc[$eid]['task'] = $einfo['task'];
                $events_assoc[$eid]['task_name'] = $einfo['task_name'];
                $events_assoc[$eid]['task_severity'] = $einfo['severity'];
                // define whether current user can edit/delete planned time
                $events_assoc[$eid]['edit_allocate'] = 0;

                if (!empty($result['tasks'][$einfo['task']])) {
                    $events_assoc[$eid]['background_color'] = $result['tasks'][$einfo['task']]['background_color'];
                    // event should be in 'planning' status
                    if ($events_assoc[$eid]['status'] == 'planning') {
                        // user should either be allowed to edit allocation of task or have edit access to event
                        $events_assoc[$eid]['edit_allocate'] =
                            !empty($result['tasks'][$einfo['task']]['edit_allocate']) || ($events_assoc[$eid]['access'] == 'edit');
                    }
                } elseif ($events_assoc[$eid]['status'] == 'planning') {
                    if (($events_assoc[$eid]['access'] == 'edit')) {
                        $events_assoc[$eid]['edit_allocate'] = 1;
                    } else {
                        if (empty($other_tasks[$einfo['task']])) {
                            $other_tasks[$einfo['task']] = new Task($this->registry, $einfo);
                            $other_tasks[$einfo['task']] = array('edit_allocate' => $other_tasks[$einfo['task']]->checkPermissions('edit_allocate'));
                        }
                        $events_assoc[$eid]['edit_allocate'] = $other_tasks[$einfo['task']]['edit_allocate'];
                    }
                }
            }

            unset($other_tasks);
        }
        unset($pt_ids);

        //$result['day_start'] = $day_start;
        //$result['day_end'] = $day_end;
        //$result['day_hours'] = ($day_end - $day_start)/60;
        //$result['monday_start'] = 1;
        //$result['week_days_number'] = $calendar_settings['week_days_number'];
        $result['events'] = $events_assoc;
    }

    /**
     * Check if any of displayed data has been modified or any other data
     * should be displayed since content was last reloaded
     * @param array $result - result values to be displayed
     * @return mixed - 'content' to reload the whole dashlet content,
     *      'calendar' to reload only the calendar, false - no reload necessary
     */
    private function _checkForModifications(&$result = array()) {

        if (!isset($result['settings'])) {
            // prepare calendar settings if not passed as a parameter
            $result['settings'] = $this->_prepareCalendarSettings();
        }

        if (!$this->isDefined('events')) {
            // get saved filters from personal settings - we need old timestamp
            $this->_getFiltersValues();
        }

        // 1. check if timestamps from request and saved filters differ or timestamp is not for today
        if ($this->registry['request']->get('check') != $this->get('timestamp') ||
        General::strftime('%Y-%m-%d') != General::strftime('%Y-%m-%d', $this->get('timestamp'))) {
            // the whole content should be reloaded
            return 'content';
        }

        // datetime to check for modifications against
        $check_datetime = General::strftime($this->i18n('date_iso'), $this->get('timestamp'));

        // 2. check if any of the previously displayed events has been modified
        if ($this->get('events')) {
            $filters_search = array(
                'where' => array(
                    'e.id IN (' . implode(',', $this->get('events')) . ') AND',
                    'e.modified > \'' . $check_datetime . '\' OR',
                    'e.deleted > \'' . $check_datetime . '\' AND',
                    'e.deleted IS NOT NULL'
                ),
                'sort' => array('e.active IS NOT NULL')
            );
            // let's pretend we are somewhere else, shall we?
            $this->registry->set('module', 'custom_module', true);
            $changed = Events::getIds($this->registry, $filters_search);
            $this->registry->set('module', 'dashlets', true);

            if ($changed) {
                return 'calendar';
            }
        }

        // 3. check if any other events were added/modified and should be displayed for period
        $this->_prepareDays($result);
        $user_ids_ordered = $this->_prepareUserIds();

        if (!$user_ids_ordered) {
            return false;
        }

        $filters_search = array(
            // a flag to instruct processing of the twisted filter by participant_id
            'has_calendar_filters' => 1,
            // we need to know current user's relation to event
            'participant_id' => $this->registry['currentUser']->get('id'),
            'where' => array(
                // this is the twisted filter
                'ea.participant_id IN user-(' . implode(',', $user_ids_ordered) . ')',
                'e.active=1',
                'e.availability=\'busy\'',
                'e.status IN (\'planning\', \'progress\', \'finished\')',
                'e.modified > \'' . $check_datetime . '\'',
            ),
            'sort' => array('e.active IS NOT NULL'),
            'event_start' => reset($result['days']) . ' 00:00:00',
            'event_end' => end($result['days']) . ' 23:59:00'
        );
        // let's pretend we are somewhere else, shall we?
        $this->registry->set('module', 'custom_module', true);
        $changed = Events::getIds($this->registry, $filters_search);
        $this->registry->set('module', 'dashlets', true);

        if ($changed) {
            return 'calendar';
        }

        // check for recurrent other events separately
        $filters_search['limit'] = '0, 0';
        $filters_search['get_recurrence'] = true;
        $filters_search['return_array'] = true;
        $filters_search['get_fields'] = array('name');

        $this->registry->set('module', 'custom_module', true);
        $changed = Events::search($this->registry, $filters_search);
        $this->registry->set('module', 'dashlets', true);

        if ($changed) {
            return 'calendar';
        }

        return false;
    }

    /**
     * Define position group (allday event or before/during/after displayed
     * hours) of event to be displayed or removed
     * @param array $event - associative array with event data
     * @param array $calendar_settings - calendar settings
     * @return string - position group in calendar
     */
    private function _prepareEventPosition(&$event, &$calendar_settings) {

        $day_start = 60 * $calendar_settings['week_start_hour'];
        $day_end   = 60 * $calendar_settings['week_end_hour'] + 60;
        $data = array();

        $position = 'events';
        if ($event['allday_event']) {
            $position = 'allday_events';
        } else {
            $selected_day_start = $event['event_start_date'] . ' 00:00:00';
            $data['start'] = (int)((strtotime($event['event_start']) - strtotime($selected_day_start))/60);
            $data['end'] = (int)((strtotime($event['event_end']) - strtotime($selected_day_start))/60);

            if ($data['end'] <= $day_start) {
                $position = 'events_before';
            } elseif ($data['start'] >= $day_end) {
                $position = 'events_after';
            }
        }

        return $position;
    }

    /**
     * Fetch html content for error or success messages to be displayed
     * @param array $messages - array of messages
     * @param string $mode - message or error
     * @return string - fetched content
     */
    private function _prepareMessages($messages = array(), $mode = 'message') {
        if (!empty($messages)) {
            $v = new Viewer($this->registry);
            $v->setFrameset('message.html');
            $v->data['display'] = $mode;
            $v->data['items'] = array_values($messages); // don't display relative URLs
            $messages = $v->fetch();
            unset($v);
        } else {
            $messages = '';
        }
        return $messages;
    }

    /**
     * Algorithm for pseudo-random color generation for tasks
     * @param Task $task - task model
     */
    private function _defineColor(Task &$task) {
        $div = pow(2, 4);       // divisor
        $mul = 256 / $div;      // multiplier
        $inv_chk = round($div * 0.5);  // value to compare with; if less, invert result to get lighter colors

        $r = $task->get('customer') % $div;
        if ($r < $inv_chk) {
            $r = $div - $r;
        }
        $r = abs($r * $mul - 1);

        $g = $task->get('project') % $div;
        if ($g < $inv_chk) {
            $g = $div - $g;
        }
        $g = abs($g * $mul - 1);

        $b = $task->get('id') % $div;
        /* $b = $task->get('added');
        $b = ((substr($b, 2, 2) * 30 * 12 + substr($b, 5, 2) * 30 + substr($b, 8, 2))*60 + substr($b, 11, 2)) % $div; */
        if ($b < $inv_chk) {
            $b = $div - $b;
        }
        $b = abs($b * $mul - 1);

        $task->set('background_color', sprintf('%02x%02x%02x', $r, $g, $b), true);
    }
}

?>
