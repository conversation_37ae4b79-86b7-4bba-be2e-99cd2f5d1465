<?php

class Custom_Dashlet_Viewer extends Viewer {

    public function __construct(&$registry, $is_main = false) {
        $this->template = 'dashlet.html';
        $this->pluginName = 'atc_upcoming_prophylactics';

        parent::__construct($registry, $is_main);
        $this->setFrameset('frameset_blank.html');
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir        = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl        = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir     = $this->pluginDir . 'templates/';

        $this->modelsDir        = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir       = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir   = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir          = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir       = $this->pluginDir . 'javascript/';
        $this->scriptsUrl       = $this->pluginUrl . 'javascript/';

        $this->stylesUrl        = $this->pluginUrl . 'styles/';

        return true;
    }

    public function prepare() {

        $registry = &$this->registry;

        //prepare the data for the template
        $dashlet = $registry->get('dashlet');
        $dashlet_settings = '';

        // get the current dashlet properties to get settings
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $filter_dashlet_plugin = array('get_one' => $this->pluginName);
        $dashlet_plugin = Dashlets::getPlugins($registry, $filter_dashlet_plugin);
        $dashlet_settings = $dashlet_plugin[$this->pluginName]['settings'];

        require_once $this->pluginDir . 'models/custom.model.php';
        $custom_dashlet_model = new Custom_Model($dashlet_settings);

        // Check required settings
        $required_settings = array(
            'doc_type_warranty_card',
            'doc_warranty_card_substatus',

            'field_doc_prop_prophylaxis_one',
            'field_doc_prop_prophylaxis_one__dateplan',
            'field_doc_prop_prophylaxis_one__datereal',
            'field_doc_prop_prophylaxis_one__cust',
            'field_doc_prop_prophylaxis_two',
            'field_doc_prop_prophylaxis_two__dateplan',
            'field_doc_prop_prophylaxis_two__datereal',
            'field_doc_prop_prophylaxis_two__cust',
            'field_doc_prop_prophylaxis_three',
            'field_doc_prop_prophylaxis_three__dateplan',
            'field_doc_prop_prophylaxis_three__datereal',
            'field_doc_prop_prophylaxis_three__cust',
            'field_doc_prop_prophylaxis_four',
            'field_doc_prop_prophylaxis_four__dateplan',
            'field_doc_prop_prophylaxis_four__datereal',
            'field_doc_prop_prophylaxis_four__cust',
            'field_doc_prop_client_name',
            'field_doc_prop_client_address',
            'field_doc_prop_client_telephon',
            'field_doc_prop_mash_kind_engine',
            'field_doc_prop_mash_type_engine',
            'field_doc_prop_mash_air_model',
            'field_doc_prop_mash_air_serial_num',
            'field_doc_prop_real_montaj_date'
        );
        foreach ($required_settings as $rs) {
            if (!isset($custom_dashlet_model->settings[$rs]) || $custom_dashlet_model->settings[$rs] == '') {
                $this->data['errors'][] = $this->i18n('dashlet_atc_upcoming_prophylactics_error_missing_required_settings');
                break;
            }
        }

        // If there are no errors
        if (empty($this->data['errors'])) {
            // Prepare some viewer data
            $this->data['dashlet'] = $dashlet;
            $this->data['scripts_url'] = array(
                $this->scriptsUrl . 'custom.js'
            );
            // Prepare some date restrictions
            $current_date = General::strftime('%Y-%m-%d');
            if (isset($custom_dashlet_model->settings['date_visit_disallow_date_before_days']) && is_numeric($custom_dashlet_model->settings['date_visit_disallow_date_before_days'])) {
                $this->data['date_visit_disallow_date_before'] = General::strftime('%Y-%m-%d', strtotime("{$custom_dashlet_model->settings['date_visit_disallow_date_before_days']} days", strtotime($current_date)));
            } else {
                $this->data['date_visit_disallow_date_before'] = '';
            }
            if (isset($custom_dashlet_model->settings['date_visit_disallow_date_after_days']) && is_numeric($custom_dashlet_model->settings['date_visit_disallow_date_after_days'])) {
                $this->data['date_visit_disallow_date_after'] = General::strftime('%Y-%m-%d', strtotime("{$custom_dashlet_model->settings['date_visit_disallow_date_after_days']} days", strtotime($current_date)));
            } else {
                $this->data['date_visit_disallow_date_after'] = '';
            }
        }
    }
}

?>
