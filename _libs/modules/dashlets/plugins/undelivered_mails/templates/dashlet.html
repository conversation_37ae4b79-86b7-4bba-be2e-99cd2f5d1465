{* Look the link bellow to know why DEFER is used *}
{* http://msdn.microsoft.com/en-us/library/ms533897.aspx *}
{assign var='custom_filters' value=$dashlet->get('filters')}
  {if $custom_filters.view_mails eq 'all'}
    {assign var='colspan_total' value=6}
  {else}
    {assign var='colspan_total' value=5}
  {/if}
  <script defer="defer" src="{$scripts_url1}?{$system_options.build}"></script>
  {include file="`$theme->templatesDir`_communication_list.html"
           custom_filters=$custom_filters
           colspan_total=$colspan_total
  }

  <table style="width: 100%;">
    <tr>
      <td class="pagemenu">
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          pagination=$pagination
          session_param=$pagination.session_param
          use_ajax=1
          hide_rpp=1
          hide_stats=1
          hide_selection_stats=true
          link=$pagination.link
        }
      </td>
    </tr>
  </table>
