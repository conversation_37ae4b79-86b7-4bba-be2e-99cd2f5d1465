/*
 * Function to process selected customer
 */
changeClient = function(autocomplete, data) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_plugin = $('work_leaving_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('work_leaving_form_dashlet_plugin_id').value;
    var clinic_id = $('wlf_clinic').value;

    if (data.id) {
        reloadSelectedCustomer(data.id, clinic_id);
        Effect.Fade('loading');
    } else {
        container = $('content_dashlet_' + dashlet_plugin_id);
        addClass(container, 'dashlet_loader');
        container.innerHTML = '';

        complete_clinic = function () {
            if ($('wlf_clinic')) {
                $('wlf_clinic').value = clinic_id;
                clearTimeout(dl_timer);
            } else {
                var dl_timer = setTimeout(add_messages, 1000);
            }
        };

        var dl_timer = setTimeout(complete_clinic, 1000);
        dashletsLoad(container, 'plugin', dashlet_plugin, dashlet_plugin_id);

        Effect.Fade('loading');
    }
};

/*
 * Function to process selected clinic
 */
changeClinic = function(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_plugin = $('work_leaving_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('work_leaving_form_dashlet_plugin_id').value;
    var patient_id = $('wlf_patient').value;

    if (element.value && patient_id) {
        reloadSelectedCustomer(patient_id, element.value);
    }
    Effect.Fade('loading');
};

/*
 * Function to process selected viist
 */
loadVisitInformation = function(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_plugin = $('work_leaving_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('work_leaving_form_dashlet_plugin_id').value;
    var container = $('container_work_leaving_form_visit_information');

    if (element.value) {
        var gt2_mode = 'edit';
        var visible_edit_button = false;
        var visible_pay_button = false;
        var visible_nopay_button = false;
        if (element['options'][element.selectedIndex].className.match(/load_view_gt2/)) {
            gt2_mode = 'view';
        }
        if (element['options'][element.selectedIndex].className.match(/visible_edit_button/)) {
            visible_edit_button = true;
        }
        if (element['options'][element.selectedIndex].className.match(/visible_pay_button/)) {
            visible_pay_button = true;
        }
        if (element['options'][element.selectedIndex].className.match(/visible_nopay_button/)) {
            visible_nopay_button = true;
        }

        var opt = {
            method: 'get',
            asynchronous: false,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var ajax_result = eval('(' + t.responseText + ')');
                container.innerHTML = ajax_result.template;
                if (ajax_result.notes) {
                    $('wlf_notes').value = ajax_result.notes;
                } else {
                    $('wlf_notes').value = '';
                }
                container.parentNode.style.display = 'table-row';

                var scripts = container.getElementsByTagName('script');
                for (var j=0; j<scripts.length; j++) {
                    ajaxLoadJS(scripts[j]);
                }

                if (visible_edit_button) {
                    $('wlf_add_button').style.display = '';
                } else {
                    $('wlf_add_button').style.display = 'none';
                }
                if (visible_pay_button) {
                    $('wlf_pay_button').style.display = '';
                } else {
                    $('wlf_pay_button').style.display = 'none';
                }
                if (visible_nopay_button) {
                    $('wlf_nopay_button').style.display = '';
                } else {
                    $('wlf_nopay_button').style.display = 'none';
                }

                if (ajax_result.document_notes) {
                    $('wlf_document_notes').style.backgroundColor = '#F4878D';
                    $('wlf_document_notes').value = ajax_result.document_notes;
                    removeClass($('wlf_document_notes'), 'readonly');
                } else {
                    addClass($('wlf_document_notes'), 'readonly');
                    $('wlf_document_notes').value = '';
                    $('wlf_document_notes').style.backgroundColor = '#F5F5F5';
                }

                // show the notes field for the reason
                $('wlf_document_notes_lbl').style.display= '';
                $('wlf_document_notes').parentNode.style.display= '';

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
                return false;
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
                return false;
            }
        };

        var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_plugin_id + '&custom_plugin_action=prepareGT2Table&visit_id=' + element.value + '&gt2_mode=' + gt2_mode;

        new Ajax.Request(url, opt);
    } else {
        $('wlf_add_button').style.display = 'none';
        $('wlf_nopay_button').style.display = 'none';
        $('wlf_pay_button').style.display = '';
        $('wlf_notes').value = '';
        container.innerHTML = '';
        container.parentNode.style.display = 'none';

        $('wlf_document_notes_lbl').style.display= 'none';
        $('wlf_document_notes').parentNode.style.display= 'none';
        Effect.Fade('loading');
    }
};

/*
 * Function to save the changes made to GT2 var
 */
editWorkLeavingFormGT2 = function(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_plugin = $('work_leaving_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('work_leaving_form_dashlet_plugin_id').value;
    var get_params = Form.serialize(element.form);

    var opt = {
        method: 'post',
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');

            var alert_messages = '';
            for (var j=0; j<result['messages'].length; j++) {
                alert_messages += result['messages'][j] + '\n';
            }

            if (result['error']) {
                alert(alert_messages);
            } else {
                reloadSelectedCustomer($('wlf_patient').value, $('wlf_clinic').value);
                alert(alert_messages);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_plugin_id + '&custom_plugin_action=editWorkLeavingFormGT2';
    new Ajax.Request(url, opt);
};

/*
 * Function to mark a work leaving form as no_pay
 */
markWorkLeavingFormNoPay = function(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var get_params = Form.serialize(element.form);

    var opt = {
        method: 'post',
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');

            var alert_messages = '';
            for (var j=0; j<result['messages'].length; j++) {
                alert_messages += result['messages'][j] + '\n';
            }

            if (result['error']) {
                alert(alert_messages);
            } else {
                reloadSelectedCustomer($('wlf_patient').value, $('wlf_clinic').value);
                alert(alert_messages);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action'
              + '&plugin=' + $('work_leaving_form_dashlet_plugin').value
              + '&dashlet=' + $('work_leaving_form_dashlet_plugin_id').value
              + '&visit_id=' + $('wlf_visits').value
              + '&custom_plugin_action=markWorkLeavingFormNoPay';
    new Ajax.Request(url, opt);
};

/*
 * Function to show payment form
 */
showPaymentForm = function(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_plugin = $('work_leaving_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('work_leaving_form_dashlet_plugin_id').value;
    var get_params = Form.serialize(element.form);
    var total_with_vat_fields = $$('#container_work_leaving_form_visit_information #total_with_vat');
    var method = '';
    var extra_info = '';

    if ($('wlf_visits').value && total_with_vat_fields.length) {
        method = 'post';
    } else {
        method = 'get';
    }

    if ($('wlf_visits').value) {
        extra_info = '&visit_date=' + $('wlf_visits').options[$('wlf_visits').selectedIndex].innerHTML.replace(/([^0-9\.])+/i, '');
    }

    var opt = {
        method: method,
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');

            var alert_messages = '';
            for (var j=0; j<result['messages'].length; j++) {
                alert_messages += result['messages'][j] + '\n';
            }
            if (result['error']) {
                alert(alert_messages);
            } else {
                //lightbox have to be shown
                lb = new lightbox({content: result['template'], title: result['title'], width:result['width']});
                lb.activate();
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_plugin_id + '&custom_plugin_action=showPaymentForm' + extra_info;
    new Ajax.Request(url, opt);
};

/*
 * Function to process the convertion depending on the changesd settings ion the form
 */
processConvertionRates = function(element) {
    var recalculate_bgn = false;

    if (element.id.match(/currency/)) {
        // currency has been changed
        recalculate_bgn = true;
        container = element.id.replace(/^wlf_container_((bank|cash)_[0-9]*)_currency$/, '$1');
        currency_field = element;
        bgn_value_field = $('wlf_container_' + container + '_current_paid_bgn');
        value_field = $('wlf_container_' + container + '_current_paid');
    } else if (element.id.match(/_current_paid_bgn$/)) {
        // bgn field has been changed
        container = element.id.replace(/^wlf_container_((bank|cash)_[0-9]*)_current_paid_bgn$/, '$1');
        currency_field = $('wlf_container_' + container + '_currency');
        bgn_value_field = element;
        value_field = $('wlf_container_' + container + '_current_paid');
    } else if (element.id.match(/_current_paid$/)) {
        recalculate_bgn = true;
        // bgn field has been changed
        container = element.id.replace(/^wlf_container_((bank|cash)_[0-9]*)_current_paid$/, '$1');
        currency_field = $('wlf_container_' + container + '_currency');
        bgn_value_field = $('wlf_container_' + container + '_current_paid_bgn');
        value_field = element;
    } else {
        return true;
    }

    if (!currency_field.value) {
        if (recalculate_bgn) {
            bgn_value_field.value = value_field.value;
        } else {
            value_field.value = bgn_value_field.value;
        }
    } else {
        convertion_index = currency_field.value + '->BGN';

        if (recalculate_bgn && convertion_rates[convertion_index]) {
            if (parseFloat(value_field.value)) {
                field_value = parseFloat(value_field.value);
            } else {
                field_value = 0;
            }
            bgn_value_field.value = (field_value * parseFloat(convertion_rates[convertion_index])).toFixed(2);
        } else {
            if (parseFloat(bgn_value_field.value)) {
                field_value = parseFloat(bgn_value_field.value);
            } else {
                field_value = 0;
            }
            value_field.value = (field_value / parseFloat(convertion_rates[convertion_index])).toFixed(2);
        }
    }

    if ($('wlf_unpaid_amount')) {
        var container_amounts = $$('#containers_payment_data input[id^="wlf_container_"][type="text"]');
        var total_paid = 0;
        for (var j=0; j<container_amounts.length; j++) {
            if (container_amounts[j].id.match(/^wlf_container_((bank|cash)_[0-9]*)_current_paid_bgn$/)) {
                current_paid = 0;
                if (parseFloat(container_amounts[j].value)) {
                    current_paid = parseFloat(container_amounts[j].value);
                }

                total_paid += current_paid;
            }
        }

        $('wlf_unpaid_amount').value = (parseFloat($('wlf_unpaid_amount_basic').value) - parseFloat(total_paid)).toFixed(2);
    }
};

/*
 * Function to reload the table with the selected customer
 */
reloadSelectedCustomer = function(customer_id, clinic_id) {
    var dashlet_plugin = $('work_leaving_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('work_leaving_form_dashlet_plugin_id').value;

    removeClass($('wlf_notes'), 'readonly');
    $('wlf_notes').readOnly = false;
    $('wlf_notes').disabled = false;
    $('wlf_discount').value = '';
    $('wlf_balance').value = '';
    $('wlf_visits').options.length = 0;
    $('wlf_visits').options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
    addClass($('wlf_visits'), 'missing_records');
    $('container_work_leaving_form_visit_information').innerHTML = '';
    $('wlf_add_button').style.display = 'none';
    $('wlf_nopay_button').style.display = 'none';
    $('wlf_document_notes_lbl').style.display= 'none';
    $('wlf_document_notes').parentNode.style.display= 'none';

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var ajax_result = eval('(' + t.responseText + ')');
            $('wlf_discount').value = ajax_result.discount;
            $('wlf_balance').value = ajax_result.total_paid;
            $('wlf_pay_button').style.display = '';
            $('wlf_notes').value = '';

            // complete the visits dropdown
            if (ajax_result.visit_options.length) {
                dropdown_element = $('wlf_visits');
                dropdown_element.options[0] = new Option(i18n['labels']['please_select'].toLowerCase(), '', false, true);
                addClass(dropdown_element.options[0], 'undefined');
                addClass(dropdown_element.options[0], 'undefined');
                var iterator = 1;

                for (var i=0; i<ajax_result.visit_options.length; i++) {
                    dropdown_element.options[iterator] = new Option(ajax_result.visit_options[i]['label'], ajax_result.visit_options[i]['option_value'], false, false);
                    if (ajax_result.visit_options[i]['additional'] == 'unpaid') {
                        dropdown_element.options[iterator].style.color = 'red';
                    }
                    if (ajax_result.visit_options[i]['class_name']) {
                        addClass(dropdown_element.options[iterator], ajax_result.visit_options[i]['class_name']);
                    }
                    iterator++;
                }
                removeClass(dropdown_element, 'missing_records');
                toggleUndefined(dropdown_element);
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_plugin_id + '&custom_plugin_action=findPatientData';
    url += '&patient=' + customer_id;
    url += '&clinic=' + clinic_id;

    new Ajax.Request(url, opt);
};

/*
 * Function to add the payment
 */
addWorkLeavingFormPayment = function(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var get_params = Form.serialize(element.form);

    var opt = {
        method: 'post',
        parameters: get_params,
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var ajax_result = eval('(' + t.responseText + ')');

            var alert_messages = '';
            for (var j=0; j<ajax_result['messages'].length; j++) {
                alert_messages += ajax_result['messages'][j] + '\n';
            }
            if (ajax_result['error']) {
                alert(alert_messages);
            } else {
                alert(alert_messages);
                reloadSelectedCustomer($('wlf_patient').value, $('wlf_clinic').value);
                if ($('schedule_search')) {
                    searchSchedule($('schedule_search'), 1);
                }
                lb.deactivate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = element.form.action + '&patient=' + $('wlf_patient').value + '&visit=' + $('wlf_visits').value + '&clinic=' + $('wlf_clinic').value;
    new Ajax.Request(url, opt);
};

/*
 * Function to update the containers list depending on the payment type
 */
updateContainersList = function(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_plugin = $('work_leaving_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('work_leaving_form_dashlet_plugin_id').value;

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var ajax_result = t.responseText;
            $('containers_payment_data').innerHTML = ajax_result;
            var scripts = $('containers_payment_data').getElementsByTagName('script');
            for (var j=0; j<scripts.length; j++) {
                ajaxLoadJS(scripts[j]);
            }

            var container_amounts = $$('#containers_payment_data input[id^="wlf_container_"][type="text"]');
            for (var j=0; j<container_amounts.length; j++) {
                container_amounts[j].setAttribute('onContextMenu', 'return false;');
                container_amounts[j].setAttribute('onDrop', 'return false;');
                container_amounts[j].setAttribute('onPaste', 'return false;');
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_plugin_id + '&custom_plugin_action=updateContainersList' + '&patient=' + $('wlf_patient').value + '&clinic=' + $('wlf_clinic').value + '&payment_type=' + element.value;
    new Ajax.Request(url, opt);
}

/*
 * Recalculate the left quantity for the prepayments
 */
recalculateLeftQuantity = function(element) {
    var container_amounts = $$('#containers_payment_data input[id^="wlf_container_"][type="text"]');
    var total_paid = 0;
    for (var j=0; j<container_amounts.length; j++) {
        if (container_amounts[j].id.match(/^wlf_container_((bank|cash)_[0-9]*)_current_paid$/)) {
            current_paid = 0;
            if (parseFloat(container_amounts[j].value)) {
                current_paid = parseFloat(container_amounts[j].value);
            }

            total_paid += current_paid;
        }
    }
    $('wlf_unpaid_amount').value = (parseFloat($('wlf_unpaid_amount_basic').value) - parseFloat(total_paid)).toFixed(2);
}
