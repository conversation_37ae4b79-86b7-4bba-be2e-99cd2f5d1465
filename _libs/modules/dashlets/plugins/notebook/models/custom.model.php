<?php
/**
 * HOT LINKS plugin custom model class
 */
Class Custom_Model extends Model {
    public $modelName = 'Dashlet';

    public function __construct() {
        return true;
    }
    
    public function prepareCustomData(&$model) {
        //prepare custom filters
        $filters = array();
        if ($model->get('title_template')) {
            $filters['title_template'] = $model->get('title_template');
            $model->unsetProperty('title_template', true);
        }
        
        $model->set('filters', $filters, true);

        return $model;
    }
    
}
?>