<?php

class Custom_Add_Viewer extends Viewer {
    
    public function __construct(&$registry, $is_main) {
        $this->template = 'add.html';
        $this->pluginName = 'notebook';
        
        parent::__construct($registry, $is_main);
    }
    
    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir = $this->pluginDir . 'templates/';
        
        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir      = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir = $this->pluginDir . 'javascript/';
        $this->scriptsUrl = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {
        
        $this->prepareTitleBar();
        
        $registry = &$this->registry;
        
        //prepare the data for the template
    
        require_once $this->modelsDir . 'dashlets.factory.php';
        require_once $this->modelsDir . 'dashlets.dropdown.php';
        
        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($registry);
        
        //prepare actions
        $params = array('get_one' => $this->pluginName);
        $plugin = Dashlets::getPlugins($registry, $params);
        
        //get the model
        $this->model = $this->registry->get('dashlet');

        //assign data for template
        $this->data['dashlet'] = $this->model;
        $this->data['module_name_i18n'] = $plugin[$this->pluginName]['label'];

    }

    public function prepareTitleBar() {
        $title = $this->i18n('dashlets');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('dashlets_add');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action);

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
