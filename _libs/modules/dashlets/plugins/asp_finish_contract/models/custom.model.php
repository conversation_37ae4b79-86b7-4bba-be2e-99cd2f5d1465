<?php
class Custom_Model extends Model {
    public $modelName = 'Dashlet';
    public $plugin_name = 'asp_finish_contract';

    public function __construct($dashlet_settings = '') {
        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        //load finance i18n files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'finance/i18n/' . $this->registry->get('lang'), false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }
        //load contracts i18n files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'contracts/i18n/' . $this->registry->get('lang'), false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }

        return true;
    }

    public function prepareCustomData(&$model) {
        //prepare custom filters
        $filters = array();
        if ($model->get('autocomplete_settings')) {
            $filters['autocomplete_settings'] = $model->get('autocomplete_settings');
            $model->unsetProperty('autocomplete_settings', true);
        }
        if ($model->get('allowed_users')) {
            $filters['allowed_users'] = $model->get('allowed_users');
            $model->unsetProperty('allowed_users', true);
        }

        $model->set('filters', $filters, true);

        return $model;
    }

    /**
     * Parse settings for current plugin and set them as properties to model
     *
     * @param string $dashlet_settings - settings field of dashlet
     */
    public function parseDashletPluginSettings($dashlet_settings) {

        $settings_rows = preg_split('/(\n|\r|\r\n)/', $dashlet_settings);

        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0]=='#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);

            // other settings - set them as properties of model in the usual way
            $this->set($key, $value, true);
        }
    }

    /**
     * gets any obligations we have for the contract
     *
     * @param array $params - some useful parameters
     */
    public function getContractObligations($params = array()) {

        $request = &$this->registry['request'];

        $filters = array('where' => array('co.id = ' . $request->get('contract'),
                                          'co.subtype = "contract"',
                                          'co.active = 1',
                                          'co.annulled_by = 0'));
        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
        $contract = Contracts::searchOne($this->registry, $filters);
        if (empty($contract)) {
            exit;
        }

        //get all not paid invoices issued for the customer
        $query = 'SELECT fir.id FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                 'WHERE fir.payment_status != "paid" AND fir.payment_status != "nopay" AND fir.customer = ' . $contract->get('customer') . "\n" .
                 '  AND fir.annulled_by = 0 AND fir.type = ' . PH_FINANCE_TYPE_INVOICE;
        $invoices = $this->registry['db']->GetCol($query);

        //get all unpaid or partially paid invoices issued for the CONTRACT
        $query = 'SELECT fir.id FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' .  "\n" .
                 '  ON frr.link_to_model_name = "Contract" AND frr.link_to = ' . $contract->get('id')  .  "\n" .
                 '     AND frr.parent_model_name = "Finance_Incomes_Reason" AND fir.id = frr.parent_id'  .  "\n" .
                 'WHERE fir.payment_status != "paid" AND fir.payment_status != "nopay" AND fir.customer = ' . $contract->get('customer') . "\n" .
                 '  AND fir.annulled_by = 0 AND fir.type = ' . PH_FINANCE_TYPE_INVOICE;
        $invoices_contract = $this->registry['db']->GetCol($query);


        $settings = 'SELECT filters FROM ' . DB_TABLE_DASHLETS . ' WHERE id = ' . $this->registry['request']->get('dashlet');
        $settings = $this->registry['db']->GetOne($settings);
        $settings = unserialize(base64_decode($settings));
        $this->parseDashletPluginSettings($settings['autocomplete_settings']);

        //get all not paid incomes reasons without invoices issued for the customer
        $query = 'SELECT fir.id FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' . "\n" .
                 '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir2' . "\n" .
                 '  ON fir2.id = frr.parent_id AND fir2.type = ' . PH_FINANCE_TYPE_INVOICE . ' AND fir2.annulled_by = 0' . "\n" .
                 'WHERE fir.type IN (' . $this->get('obligations_types') . ') AND fir.customer = ' . $contract->get('customer') . ' AND fir2.id IS NULL ';
        $incomes = $this->registry['db']->GetCol($query);

        //get models
        //get unpaid amount of ALL the invoices from the cutomer
        $all_unpaid_amount = 0;
        $contract_unpaid_amount = 0;
        $incomes = array_merge($invoices, $incomes);
        if (!empty($incomes)) {
            $filters = array('where' => array('fir.id IN (' . implode(',', $incomes) . ')'));
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
            $models = Finance_Incomes_Reasons::search($this->registry, $filters);
            foreach($models as $m => $model) {
                $paid = $model->getFullPaidAmount();
                $all_unpaid_amount += $model->get('total_with_vat') - $paid;
                if (in_array($model->get('id'), $invoices_contract)) {
                    $contract_unpaid_amount += $model->get('total_with_vat') - $paid;

                }
            }
        }

        //get all not finished templates for the contract
        $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . "\n" .
                 'WHERE status != "finished" AND deleted_by = 0 AND contract_id = ' . $contract->get('id');
        $templates = $this->registry['db']->GetCol($query);

        // IMPORTANT!!! we will make something very special here
        // as all dashlet models have the same name

        // get content of the php file(the model) for the fast contract invoices plugin
        $content = General::fileGetContents(PH_MODULES_DIR . 'dashlets/plugins/fast_contract_invoice/models/custom.model.php');
        // get code for the function we need
        $content = preg_replace('#.*(public function fastTemplatesOptions.*)public function.*#ms', '$1', $content);
        // construct new class on the fly
        $content = "class Fast_Invoices_Model extends Model {\n" .
                   "public \$modelName = 'Dashlet';\n" .
                   "public \$plugin_name = 'fast_contract_invoice';\n" .
                   $content .
                   "\n}";
        // result is not needed
        EvalString::evaluate($this->registry, $content, array('eval_as_is' => true));

        // if dynamic class creation fails, display error and exit method
        if (!class_exists('Fast_Invoices_Model', false)) {
            $this->registry['messages']->setError($this->i18n('error_technical_error_please_contact_nzoom_support'));
            echo json_encode(array('errors' => $this->registry['messages']->getErrors()));
            exit;
        }

        //load plugin files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'dashlets/plugins/fast_contract_invoice/i18n/' . $this->registry->get('lang'), false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }

        // create new model for the newly created class
        $FI_model = new Fast_Invoices_Model($this->registry, $params);
        $this->registry->set('skip_discounts', true, true);
        $end_date = $this->registry['request']->get('end_date');
        $this->registry['request']->set('contract', $contract->get('id'), 'post', true);
        $this->registry['request']->set('include_date', $end_date, 'post', true);

        $prec = $this->registry['config']->getSectionParams('precision');

        $co_amount = 0;
        foreach($templates as $t => $template) {
            $params = array('return_template' => true);
            $this->registry['request']->set('template', $template, 'post', true);
            $template = $FI_model->fastTemplatesOptions($params);
            if (!is_object($template) && $template == -1) {
                // template that is locked - invoices issue in progress
                echo json_encode(array('errors' => $this->registry['messages']->getErrors()));
                exit;
            }
            if (!is_object($template)) {
                continue;
            }
            $gt2 = $template->get('grouping_table_2');
            foreach($gt2['values'] as $v => $values) {
                if ($values['date_from'] > $end_date) {
                    unset($gt2['values'][$v]);
                    continue;
                }
                if ($values['date_to'] <= $end_date) {
                    continue;
                }
                // if we are here there are rows that have to be invoiced partially
                $diff1 = date_diff(date_create($values['date_to']), date_create($values['date_from']), true);
                $diff2 = date_diff(date_create($end_date), date_create($values['date_from']), true);
                $index= ($diff2->days + 1) / ($diff1->days + 1);
                $gt2['values'][$v][$gt2['calculated_price']] = round($values[$gt2['calculated_price']] * $index, $prec['gt2_rows']);
            }
            if (empty($gt2['values'])) {
                continue;
            }
            $template->set('grouping_table_2', $gt2, true);
            $template->calculateGT2();
            $co_amount += $template->get('total_with_vat');
        }
        //all unpaid amount for the customer plus the amount of unissued invoices
        $all_unpaid_amount = sprintf('%.2F', round($all_unpaid_amount + $co_amount, $prec['gt2_total_with_vat']));
        //the amount of unissued invoices plus the amount of unpaid invoices for this contract
        $co_amount = sprintf('%.2F', round($co_amount + $contract_unpaid_amount, $prec['gt2_total_with_vat']));
        echo json_encode(array('inv_amount' => $all_unpaid_amount, 'co_amount' => $co_amount));
        exit;
    }

    /**
     * finish contract by creating an annex
     *
     * @param array $params - some useful parameters
     */
    public function createAnnexSOT($params = array()) {

        $request = &$this->registry['request'];
        $filters = array('where' => array('co.id = ' . $request->get('contract'),
                                          'co.subtype = "contract"',
                                          'co.active = 1',
                                          'co.annulled_by = 0'));
        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
        $contract = Contracts::searchOne($this->registry, $filters);
        if (empty($contract)) {
            echo json_encode(array('errors' => array($this->i18n('plugin_invalid_contract'))));
            exit;
        }
        // create the annex
        //copy from contracts tables data
        $defs = array(
            'table' => DB_TABLE_CONTRACTS,
            'method' => 'insert',
            'condition' => 'id = ' . $contract->get('id'),
            'ignore' => array('id', 'num'),
            'set' => array(
                'subtype' => '"annex"',
                'parent_record' => sprintf('"%d"', $contract->get('id')),
                'date_sign_subtype' => sprintf('"%s"', date('Y-m-d')),
                'status' => '"locked"',
                'added' => 'NOW()',
                'modified' => 'NOW()',
                'added_by' => $this->registry['currentUser']->get('id'),
                'modified_by' => $this->registry['currentUser']->get('id'),
            ),
            'copy' => 'all',
            'dependences' => array(
                array(
                    'table' => DB_TABLE_CONTRACTS_I18N,
                    'condition' => 'parent_id = ' . $contract->get('id'),
                    'method' => 'insert',
                    'set' => array(
                        'parent_id' => '<insert_id>',
                        'translated' => 'NOW()',
                    ),
                    'copy' => 'all',
                ),
                array(
                    'table' => DB_TABLE_CONTRACTS_CSTM,
                    'condition' => 'model_id = ' . $contract->get('id'),
                    'method' => 'insert',
                    'set' => array(
                        'model_id' => '<insert_id>',
                        'added' => 'NOW()',
                        'modified' => 'NOW()',
                        'added_by' => $this->registry['currentUser']->get('id'),
                        'modified_by' => $this->registry['currentUser']->get('id'),
                    ),
                    'copy' => 'all',
                ),
                // copy all cstm relatives replacing model_id
                array(
                    'table' => DB_TABLE_CSTM_RELATIVES,
                    'condition' => 'model = "Contract" AND model_id = ' . $contract->get('id'),
                    'method' => 'insert',
                    'set' => array(
                        'model_id' => '<insert_id>',
                    ),
                    'copy' => 'all',
                ),
                //copy GT2 rows one-by-one
                array(
                    'table' => DB_TABLE_GT2_DETAILS,
                    'condition' => 'model = "Contract" AND model_id = ' . $contract->get('id'),
                    'unique' => 'id',
                    'method' => 'insert-one',
                    'ignore' => array('id'),
                    'set' => array(
                        'model_id' => '<insert_id>',
                        'added' => 'NOW()',
                        'modified' => 'NOW()',
                        'added_by' => $this->registry['currentUser']->get('id'),
                        'modified_by' => $this->registry['currentUser']->get('id'),
                    ),
                    'copy' => 'all',
                    'dependences' => array(
                        array(
                            'table' => DB_TABLE_GT2_DETAILS_I18N,
                            'replace_condition' => 'parent_id',
                            'set' => array(
                                'parent_id' => '<insert_id>',
                                'translated' => 'NOW()',
                                'translated_by' => $this->registry['currentUser']->get('id'),
                            ),
                            'copy' => 'all',
                        ),
                        array(
                            'table' => DB_TABLE_GT2_INDEXES,
                            'replace_condition' => 'parent_id',
                            'set' => array(
                                'parent_id' => '<insert_id>',
                            ),
                            'copy' => 'all',
                        ),
                        // update num field of the previously copied cstm relatives for GT2 vars
                        array(
                            'table' => DB_TABLE_CSTM_RELATIVES,
                            'replace_condition' =>
                                'model = "Contract"' .
                                ' AND model_id != "' . $contract->get('id') . '"' .
                                ' AND var_id IN (SELECT id FROM ' . DB_TABLE_FIELDS_META .
                                ' WHERE model = "Contract" AND model_type = "' . $contract->get('type') . '" AND gt2 = 1 AND name RLIKE "^(article|free)_")' .
                                ' AND num',
                            'method' => 'update',
                            'set' => array(
                                'num' => '<insert_id>',
                            ),
                        ),
                    ),
                ),
            )
        );
        // we will do here some artificial intelligence because from ASP
        //are idiots and can not understand how to finish a contract

        // get the last date we have invoice for
        $query = 'SELECT MAX(`to`) FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . "\n" .
                 'WHERE contract_id = ' . $contract->get('id');
        $date_to = $this->registry['db']->GetOne($query);
        if ($date_to && $date_to >= $request->get('end_date')) {
            // we have to create annex without templates and some credit notices will be issued
            $defs['set']['date_end'] = sprintf('"%s"', $request->get('end_date'));
            if ($request->get('end_date') > date('Y-m-d')) {
                $defs['set']['date_start_subtype'] = sprintf('"%s"', date('Y-m-d'));
            } else {
                $defs['set']['date_start_subtype'] = sprintf('"%s"', $request->get('end_date'));
            }
        } else {
            // the annex must have templates till the date of contract termination
            $defs['set']['date_validity'] = sprintf('"%s"', $request->get('end_date'));
            $defs['set']['date_end'] = sprintf('"%s"', $request->get('end_date'));
            if ($date_to && $date_to != '0000-00-00') {
                $defs['set']['date_start_subtype'] = sprintf('"%s"', preg_replace('#-\d{2}$#', '-01', $date_to));
            } else {
                $defs['set']['date_start_subtype'] = sprintf('"%s"', $contract->get('date_start'));
            }
        }
        if (isset($defs['set']['date_validity']) && $defs['set']['date_validity'] < $defs['set']['date_start_subtype']) {
            //finsh contract before start - damn
            $defs['set']['date_end'] = $defs['set']['date_validity'];
            unset($defs['set']['date_validity']);
        }
        //load contracts language files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'contracts/i18n/' . $this->registry->get('lang'), false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }

        //save the annex
        $this->registry['db']->StartTrans();
        $annex = Model_Factory::copyTableData($this->registry, $defs);
        if (!$annex) {
            echo json_encode(array('errors' => array($this->i18n('plugin_error_adding_annex'))));
            exit;
        }

        //get annex as model
        $filters = array('where' => array('co.id = ' . $annex));
        $annex = Contracts::searchOne($this->registry, $filters);

        $fields_relations = array(
            'dismantling_date' => 'unmount_date',
            'termination_grounds' => 'finish_reason',
            'obligations_value' => 'obligations',
            'obligations_payable' => 'obligations_date',
            'other_condition' => 'other_conditions',
            'obligations' => '',
        );

        //fill some additional fields with data from the request
        $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE model = "Contract" AND model_type = ' . $contract->get('type') . "\n" .
                 '  AND name IN ("' . implode('", "', array_keys($fields_relations)) . '")';
        $fields = $this->registry['db']->GetAssoc($query);

        foreach ($fields as $k => $v) {
            if ($k == 'obligations') {
                if (floatVal($request->get('obligations')) > 0.00) {
                    $val = 2;
                } else {
                    $val = 1;
                }
            } else {
                $val = $request->get($fields_relations[$k]);
            }
            $query = 'INSERT INTO ' . DB_TABLE_CONTRACTS_CSTM . ' (model_id, var_id, num, value, added, added_by, modified, modified_by, lang)' . "\n" .
                     'VALUES (' . $annex->get('id') . ', ' . $v . ', 1, "' . General::slashesEscape($val) . '", NOW(), ' . $this->registry['currentUser']->get('id') . ', NOW(), ' . $this->registry['currentUser']->get('id') . ', "")' . "\n" .
                     'ON DUPLICATE KEY UPDATE ' . "\n" .
                     'value = "' . General::slashesEscape($val) . '", modified = NOW(), modified_by = ' . $this->registry['currentUser']->get('id');
            $this->registry['db']->Execute($query);
        }

        //create invoices templates using the automation
        $query = 'SELECT settings FROM ' . DB_TABLE_AUTOMATIONS . "\n" .
                 'WHERE module = "contracts" AND active=1' . "\n" .
                 '  AND start_model_type = ' . $contract->get('type') . ' AND method LIKE "%createInvoicesTemplatesSOT%"';
        $params = array('settings' => $this->registry['db']->GetOne($query));

        $params['model'] = $annex;
        //create automation controller
        require_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php';
        require_once PH_MODULES_DIR . 'automations/plugins/aso_pireli/controllers/aso_pireli.automations.controller.php';
        $controller = new Aso_Pireli_Automations_Controller($this->registry);
        $controller->action = 'automatic_invoices_templates_generation';
        $result = $controller->createInvoicesTemplatesSOT($params);
        if (!$result) {
            $this->registry['messages']->setError($this->i18n('plugin_error_adding_annex'), '', -1);
            echo json_encode(array('errors' => array_values($this->registry['messages']->getErrors())));
            exit;
        }

        //change invoices templates issue period
        //as we have to be able to issue one invoice for the whole period of the template
        $filters = array('where' => array('fit.contract_id = ' . $annex->get('id'), 'fit.recurrent > 0'));
        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
        $templates = Finance_Invoices_Templates::search($this->registry, $filters);
        foreach ($templates as $t) {
            $diff = date_diff(date_create($t->get('periods_start')), date_create($t->get('periods_end')));
            $diff = $diff->m + ($diff->d > 0 ? 1 : 0);
            if ($diff == 0) {
                $diff = 1;
            }
            $this->registry['db']->Execute($query);
        }

        $annex->set('force_all', true, true);
        $result = Contracts::calcAgreementsDifferences($contract, $annex, true);

        if (!empty($result)) {
            $viewer = new Viewer($this->registry);
            $viewer->loadCustomI18NFiles($i18n_files);
            $viewer->setFrameset(PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/_preview_new_agreement_cd.html');
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.dropdown.php';
            $viewer->data['cd_reasons'] = Nomenclatures_Dropdown::getCDReasons(array($this->registry));
            $viewer->data['agreement_subtype'] = $annex->get('subtype');
            $viewer->data['result'] = $result;
            $viewer->data['model_id'] = $annex->get('id');
            $viewer->data['hide_calculation_types'] = true;
            $viewer->data['dashlet'] = $request->get('dashlet');
            $viewer->data['plugin'] = $this->plugin_name;

            require_once PH_MODULES_DIR . 'contracts/models/contracts.dropdown.php';
            if (!empty($result['old_invoices'])) {
                //we have to get patterns for generation and emails
                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_CREDIT_NOTICE, 'company'=> $contract->get('company'));
                $viewer->data['credit_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);
                $viewer->data['credit_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_DEBIT_NOTICE, 'company'=> $contract->get('company'));
                $viewer->data['debit_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);
                $viewer->data['debit_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
            }
            if (!empty($result['new_invoices'])) {
                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_INVOICE, 'company'=> $contract->get('company'));
                $viewer->data['invoice_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);
                $viewer->data['invoice_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_PRO_INVOICE, 'company'=> $contract->get('company'));
                $viewer->data['proforma_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);
                $viewer->data['proforma_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
            }
            $response = json_encode(array('response' => $viewer->fetch()));
            //$response = $viewer->fetch();
        } elseif ($this->registry['db']->HasFailedTrans()) {
            $this->registry['messages']->setError($this->i18n('plugin_error_adding_annex'), '', -1);
            echo json_encode(array('errors' => array_values($this->registry['messages']->getErrors())));
            exit;
        } else {
            //nothing to be issued
            $response = json_encode(array('empty' => true));
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();
        if (!$result) {
            $this->registry['messages']->setError($this->i18n('plugin_error_adding_annex'), '', -1);
            echo json_encode(array('errors' => array_values($this->registry['messages']->getErrors())));
        } else {
            echo $response;
        }
        exit;
    }

    /**
     * finish contract by finishing the annex(change its status)
     *
     * @param array $params - some useful parameters
     */
    public function fastContractFinish($params = array()) {
        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
        //load contracts i18n files
        $i18n_file = sprintf('%s%s%s%s',
                PH_MODULES_DIR,
                'contracts/i18n/',
                $this->registry['lang'],
                '/contracts.ini');
        $this->registry['translater']->loadFile($i18n_file);
        $errors = Contracts::validateIntoForceData($this->registry);
        if (!empty($errors)) {
            echo json_encode(array('errors' => array_values($errors)));
            exit;
        }
        $request = $this->registry['request'];
        $settings = 'SELECT filters FROM ' . DB_TABLE_DASHLETS . ' WHERE id = ' . $this->registry['request']->get('dashlet');
        $settings = $this->registry['db']->GetOne($settings);
        $settings = unserialize(base64_decode($settings));
        $this->parseDashletPluginSettings($settings['autocomplete_settings']);

        //get the annex
        if ($request->get('model_id')) {
            $filters = array('where' => array('co.id = ' . $request->get('model_id'), 'co.subtype = "annex"',
                'co.active = 1', 'co.annulled_by = 0', 'co.status = "locked"', 'co.subtype_status = "waiting"'));
        } elseif ($request->get('contract')) {
            $filters = array('where' => array('co.parent_record = ' . $request->get('contract'), 'co.subtype = "annex"',
                'co.active = 1', 'co.annulled_by = 0', 'co.status = "locked"', 'co.subtype_status = "waiting"'),
                'sort' => array('co.id DESC'));
        }
        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
        $annex = Contracts::searchOne($this->registry, $filters);
        if (empty($annex)) {
            echo json_encode(array('errors' => array($this->i18n('plugin_invalid_annex'))));
            exit;
        }
        $this->registry['db']->StartTrans();
        //set new annex status and issue some finance documents if this option has been chosen
        $annex->set('status', 'closed', true);
        $annex->set('force_all', true, true);
        $this->registry->set('register_issued_finance', true, true);
        $this->registry->set('get_old_vars', true, true);
        if (!$annex->setStatus()) {
            $this->registry['db']->FailTrans();
        }
        //set substatus of the contract
        $query = 'UPDATE ' . DB_TABLE_CONTRACTS . ' SET substatus = ' . $this->get('substatus_terminated') . ' WHERE id = ' . $annex->get('parent_record');
        $this->registry['db']->Execute($query);
        $result = !$this->registry['db']->HasFailedTrans();

        //restore invoices templates recurrence period
        //in the templates with rejected invoices
        $filters = array('where' => array('fit.contract_id = ' . $annex->get('id'), 'fit.recurrent > 0'));
        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
        $templates = Finance_Invoices_Templates::search($this->registry, $filters);
        $issued_invoices = $this->registry['request']->get('invoice');
        foreach ($templates as $t) {
            if (empty($issued_invoices[$t->get('id') . '_0'])) {
                $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET recurrence = "count := 1\nperiod := month" WHERE id = ' . $t->get('id');
                $this->registry['db']->Execute($query);
            }
        }

        //remove the auto issue tag from the contract
        $filters = array('where' => array('co.id = ' . $annex->get('parent_record')));
        $contract = Contracts::searchOne($this->registry, $filters);
        //create invoices templates using the automation
        $query = 'SELECT settings FROM ' . DB_TABLE_AUTOMATIONS . "\n" .
                 'WHERE module = "contracts" AND active=1' . "\n" .
                 '  AND start_model_type = ' . $contract->get('type') . ' AND method LIKE "%activateInvoicesAutoIssue%"';
        $settings = $this->registry['db']->GetOne($query);
        $params = array('settings' => $settings, 'model' => $contract);

        $settings = General::parseSettings($settings);
        $contract->deleteTags(array($settings['activation_tag']));

        //create automation controller to deactivate auto issue (for the templates and their info table)
        require_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php';
        require_once PH_MODULES_DIR . 'automations/plugins/aso_pireli/controllers/aso_pireli.automations.controller.php';
        $controller = new Aso_Pireli_Automations_Controller($this->registry);
        $controller->action = 'tag';
        $result = $controller->activateInvoicesAutoIssue($params);

        $this->registry['db']->CompleteTrans();
        if ($result) {
            //get the contract
            $filters = array('where' => array('co.id = ' . $annex->get('parent_record'), 'co.subtype = "contract"'));
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $contract = Contracts::searchOne($this->registry, $filters);
            //$this->registry['messages']->setMessage($this->i18n('plugin_contract_finished'));
            $viewer = new Viewer($this->registry);
            $viewer->data['issued_finance'] = $this->registry->get('issued_finance');
            $viewer->data['inv_url_base'] = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=',
                $_SERVER['PHP_SELF'], $this->registry['module_param'], 'finance',
                $this->registry['controller_param'], 'incomes_reasons', 'incomes_reasons'
            );
            $cURL = sprintf('<a target="_blank" href="%s?%s=contracts&amp;contracts=view&view=%s">%s (%s)</a>',
                $_SERVER['PHP_SELF'], $this->registry['module_param'], $annex->get('parent_record'), $contract->get('num'), $contract->get('customer_name')
            );
            $aURL = sprintf('<a target="_blank" href="%s?%s=contracts&amp;contracts=view&view=%s">%s</a>',
                $_SERVER['PHP_SELF'], $this->registry['module_param'], $annex->get('id'), $annex->get('num')
            );
            $viewer->data['msg'] = $this->i18n('plugin_contract_finished', array($cURL, $aURL));
            //get contracts patterns
            $filters = array('where' => array(
                                 'p.model = "Contract"',
                                 'p.model_type = ' . $annex->get('type'),
                                 'p.id IN (' . $this->get('finish_patterns') . ')'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'sanitize' => true);
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $viewer->data['c_patterns'] = Patterns::search($this->registry, $filters);
            $viewer->data['patterns_url'] = sprintf('%s?%s=contracts&amp;contracts=print&print=%s&pattern=',
                    $_SERVER['PHP_SELF'], $this->registry['module_param'], $annex->get('id')
            );
            $viewer->data['dashlet'] = $this->registry['request']->get('dashlet');
            $viewer->data['plugin'] = $this->plugin_name;
            $viewer->data['messages'] = $this->registry['messages'];
            $viewer->setFrameset('frameset_blank.html');
            $viewer->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/';
            $viewer->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->plugin_name . '/';
            $viewer->templatesDir = $viewer->pluginDir . 'templates/';
            $viewer->i18nDir      = $viewer->pluginDir . 'i18n/' . $this->registry['lang'] . '/';
            $viewer->setTemplate('final.html');
            echo json_encode(array('response' => $viewer->fetch()));
        } else {
            $this->registry['messages']->setError($this->i18n('plugin_contract_finish_error'), '', -1);
            echo json_encode(array('errors' => array_values($this->registry['messages']->getErrors())));
        }
        exit;
    }
}
