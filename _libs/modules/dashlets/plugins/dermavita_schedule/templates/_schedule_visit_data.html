<table cellpadding="0" cellspacing="3" class="visit_info_panel" style="background-color: #F0F0F0; border: 1px solid #cccccc;">
  <tr>
    <td style="background-color: #C6C6C6; width: 400px;" colspan="2">
      <div style="width: 394px; overflow: hidden">
        <div style="white-space: nowrap;"><strong>{$visit_data.patient_name|escape|default:"&nbsp;"}</strong>{if $visit_data.contact_data} - <span style="cursor: pointer;" title="{$visit_data.contact_data|escape}">{$visit_data.contact_data|escape}</span>{/if}</div>
      </div>
    </td>
    <td style="width: 16px;">
      <img src="{$theme->imagesUrl}close.png" style="cursor: pointer;" border="0" alt="{#plugin_close_visit_data_panel#|escape}" title="{#plugin_close_visit_data_panel#|escape}" onclick="$('derma_vita_visit_{$visit_data.id}').style.display='none';" />
    </td>
  </tr>
  <tr>
    <td style="background-color: #C6C6C6; width: 208px;">
      {foreach from=$visit_data.procedures item=procedure name=vd_proc}
        {$procedure|escape|default:"&nbsp;"}{if !$smarty.foreach.vd_proc.last}<br />{/if}
      {/foreach}
    </td>
    <td style="width: 208px; padding: 0;" colspan="2">
      <table cellpadding="0" cellspacing="0" width="100%">
        <tr>
          <td style="width: 16px;">
            <img src="{$templatesUrl}images/office.png" width="16" height="16" border="0" />
          </td>
          <td style="background-color: #C6C6C6;">
            {if $visit_data.room}({$visit_data.room_code}) {$visit_data.room_name|escape|default:"&nbsp;"}{else}&nbsp;{/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
  {assign var='nurse_in_visit' value=0}
  {assign var='doctors_in_visit' value=0}
  {foreach from=$visit_data.medics_nurses item=med_nurs}
    {if $nurse_option eq $med_nurs.type}
      {math equation='x+1' x=$nurse_in_visit assign=nurse_in_visit}
    {else}
      {math equation='x+1' x=$doctors_in_visit assign=doctors_in_visit}
    {/if}
  {/foreach}
  <tr>
    <td style="width: 208px; padding: 0px;">
      {if is_array($visit_data.medics_nurses) && $visit_data.medics_nurses|@count}
        <table cellpadding="0" cellspacing="0" width="100%">
          <tr>
            {assign var=previous_type_med value=''}
            {foreach from=$visit_data.medics_nurses item=med_nurs}
              {if $previous_type_med && $med_nurs.type ne $previous_type_med}
                  </td>
                </tr>
                <tr>
              {/if}
              {if $med_nurs.type ne $previous_type_med}
                <td style="width: 16px;">
                  <img src="{$templatesUrl}images/{$med_nurs.type}.png" width="16" height="16" border="0" />
                </td>
                <td style="background-color: #C6C6C6;">
              {/if}
              <div>{$med_nurs.name|escape|default:"&nbsp;"}</div>
              {if $med_nurs.type ne $previous_type_med}
              {/if}
              {assign var=previous_type_med value=$med_nurs.type}
            {/foreach}
            </td>
          </tr>
        </table>
      {else}
        &nbsp;
      {/if}
    </td>
    <td style="width: 208px; padding: 0;" colspan="2">
      {if $visit_data.equipments}
        <table cellpadding="0" cellspacing="0" width="100%">
          <td style="width: 16px;">
            <img src="{$templatesUrl}images/equipment.png" width="16" height="16" border="0" />
          </td>
          <td style="background-color: #C6C6C6;">
            {foreach from=$visit_data.equipments item=equipment name=eqp}
              {$equipment|escape|default:"&nbsp;"}{if !$smarty.foreach.eqp.last}<br />{/if}
            {/foreach}
          </td>
        </table>
      {else}
        &nbsp;
      {/if}
    </td>
  </tr>
  <tr>
    <td style="width: 210px; padding: 0;">
      <button type="button" name="editButton" class="button" style="width: 210px; margin: 0;" onclick="openVisitForm(this, '{$visit_data.id}', '')">{#plugin_edit_visit_data#|escape}</button>
    </td>
    <td style="width: 210px; padding: 0;" colspan="2">
      <button type="button" name="deleteButton" class="button" style="width: 218px; margin: 0;" onclick="if (confirm(i18n['messages']['confirm_delete'])) deleteVisit(this, '{$visit_data.id}')">{#plugin_delete_visit_data#|escape}</button>
    </td>
  </tr>
</table>