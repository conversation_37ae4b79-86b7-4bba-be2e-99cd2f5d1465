<?php

class Custom_View_Viewer extends Viewer {

    public function __construct(&$registry, $is_main) {
        $this->template = 'view.html';
        $this->pluginName = 'update_reporting_period_tags';

        parent::__construct($registry, $is_main);
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir = $this->pluginDir . 'templates/';

        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir      = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir = $this->pluginDir . 'javascript/';
        $this->scriptsUrl = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {

        $this->prepareTitleBar();

        $registry = &$this->registry;
        $this->model = $this->registry->get('dashlet');

        //prepare the data for the template

        require_once $this->modelsDir . 'dashlets.factory.php';
        require_once $this->modelsDir . 'dashlets.dropdown.php';

        //prepare group
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $group = Groups::searchOne($this->registry, array('where' => array('g.id = ' . $this->model->get('group')),
                                                          'sanitize' => true));
        if ($group) {
            $this->data['group'] = $group->get('name');
        } else {
            $this->data['group'] = 0;
        }

        //prepare actions
        $params = array('get_one' => $this->pluginName);
        $plugin = Dashlets::getPlugins($registry, $params);

        //get the model
        $this->model = $this->registry->get('dashlet');

        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters_employees = array('model_lang'    => $registry['lang'],
                                   'sanitize'      => true,
                                   'sort'  => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'),
                                   'where' => array('u.active = 1', 
                                                    'u.employee>0'
                                                   )
                                   );
        $employees = Users::search($registry, $filters_employees);
        $employees_options = array();
        foreach($employees as $employee) {
            $employees_options[] = array(
                'label' => $employee->get('firstname') . ' ' . $employee->get('lastname'),
                'option_value' => $employee->get('employee')
            );
        }

        //assign data for template
        $this->data['dashlet'] = $this->model;
        $this->data['module_name_i18n'] = $plugin[$this->pluginName]['label'];
        $this->data['employees_options'] = $employees_options;
    }

    public function prepareTitleBar() {
        $title = $this->i18n('dashlets');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('dashlets_view');
        $href = sprintf('%s=%s&amp;%s=%s',
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action);

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
