<table style="border-top: 1px solid #CCCCCC; width: 100%; margin-top: 5px;" cellspacing="0" cellpadding="5" border="0">
  <tr>
    <td style="width: 200px;">
      <div class="labelbox">{#plugin_bogdev_document_name#|escape}{#required#}:</div>
        {include file="input_text.html"
                 standalone=true
                 name='document_name'
                 custom_id='ba_document_name'
                 width=200
                 label=#plugin_bogdev_document_name#}
    </td>
    <td style="width: 200px;">
      <script type="text/javascript">
        var users_by_departments = {$users_by_departments};
      </script>
      <div class="labelbox">{#plugin_bogdev_document_department#|escape}:</div>
        {include file="input_dropdown.html"
                 standalone=true
                 name='document_department'
                 custom_id='ba_document_department'
                 options=$available_departments
                 onchange='changeBogdevAssignedExecutors(this)'
                 width=200
                 label=#plugin_bogdev_document_department#}
    </td>
    <td>
      <div class="labelbox">{#plugin_bogdev_document_attachment#|escape}:</div>
      {include file='input_file_upload.html'
               name='document_attachment'
               custom_id='ba_document_attachment'
               standalone=true
               label=#plugin_bogdev_document_attachment#
      }
    </td>
  </tr>
  <tr>
    <td colspan="3">
      <div class="labelbox">{#plugin_bogdev_document_description#|escape}:</div>
      {include file="input_textarea.html"
               standalone=true
               name='document_description'
               custom_id='ba_document_description'
               width=620
               label=#plugin_bogdev_document_description#}
    </td>
  </tr>
  <tr>
    <td style="vertical-align: top;">
      <div class="labelbox" style="width: 200px!important;">{#plugin_bogdev_document_assign_owner#|escape}:
        <div class="t_buttons" style="float: right; padding-left: 5px;">
          <div id="table_assign_owner_plusButton" onclick="addField('table_assign_owner', false, false, true)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
          <div id="table_assign_owner_minusButton" class="disabled" onclick="removeField('table_assign_owner')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
        </div>
      </div>
      <table cellspacing="0" cellpadding="0" id="table_assign_owner" class="t_grouping_table" style="border: 0!important; margin: 0!important; width:200px;">
        <tr style="display: none;">
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr id="table_assign_owner_1">
          <td style="display: none;">&nbsp;</td>
          <td style="border: 0!important; width: 200px; padding: 2px 0px 2px 0px;">
            {include file="input_dropdown.html"
              standalone=true
              width=200
              name='assign_owner'
              custom_id='bs_assign_owner'
              index=1
              label=#plugin_bogdev_document_assign_owner#}
          </td>
        </tr>
      </table>
    </td>
    <td style="vertical-align: top;">
      <div class="labelbox" style="width: 200px!important;">{#plugin_bogdev_document_assign_observer#|escape}:
        <div class="t_buttons" style="float: right; padding-left: 5px;">
          <div id="table_assign_observer_plusButton" onclick="addField('table_assign_observer', false, false, true)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
          <div id="table_assign_observer_minusButton" class="disabled" onclick="removeField('table_assign_observer')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
        </div>
      </div>
      <table cellspacing="0" cellpadding="0" id="table_assign_observer" class="t_grouping_table" style="border: 0!important; margin: 0!important; width:200px;">
        <tr style="display: none;">
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr id="table_assign_observer_1">
          <td style="display: none;">&nbsp;</td>
          <td style="border: 0!important; width: 200px; padding: 2px 0px 2px 0px;">
            {include file="input_dropdown.html"
              standalone=true
              width=200
              name='assign_observer'
              custom_id='bs_assign_observer'
              options=$users_assign
              index=1
              label=#plugin_bogdev_document_assign_observer#}
          </td>
        </tr>
      </table>
    </td>
    <td style="vertical-align: top;">
      <div class="labelbox" style="width: 200px!important;">{#plugin_bogdev_document_assign_descion#|escape}:
        <div class="t_buttons" style="float: right; padding-left: 5px;">
          <div id="table_assign_descion_plusButton" onclick="addField('table_assign_descion', false, false, true)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
          <div id="table_assign_descion_minusButton" class="disabled" onclick="removeField('table_assign_descion')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
        </div>
      </div>
      <table cellspacing="0" cellpadding="0" id="table_assign_descion" class="t_grouping_table" style="border: 0!important; margin: 0!important; width:200px;">
        <tr style="display: none;">
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr id="table_assign_descion_1">
          <td style="display: none;">&nbsp;</td>
          <td style="border: 0!important; width: 200px; padding: 2px 0px 2px 0px;">
            {include file="input_dropdown.html"
              standalone=true
              width=200
              name='assign_descion'
              custom_id='bs_assign_descion'
              options=$users_assign
              index=1
              label=#plugin_bogdev_document_assign_descion#}
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td colspan="3">
      <button class="button" style="width: 120px;" name="add_btn" type="submit" onclick="processBogdevAddedDate(this);">{#add#|escape}</button>
    </td>
  </tr>
</table>
