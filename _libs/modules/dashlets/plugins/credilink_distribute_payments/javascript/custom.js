if (env.current_lang == 'bg') {
    i18n['messages']['error_empty_required_fields'] = 'Моля попълнете полето за начална дата на периода!';
    i18n['messages']['error_payment_overpaid'] = 'Разпределената сума надвишава сумата на избрано основание! Моля коригирайте сумата и опитайте отново!';
    i18n['messages']['error_total_distributed_sum_surpassed'] = 'Общата разпределена сума е по-голяма от сумата на плащането! Моля коригирайте сумите и опитайте отново!';
} else {
    i18n['messages']['error_empty_required_fields'] = 'Please, complete the field for period starting date!';
}

/*
 * Function to seach the undistributed payments
 */
searchUndistributedPayments = function(element) {
    // check the required fields
    if (!$('from_date').value) {
        alert (i18n['messages']['error_empty_required_fields']);
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');


    var dashlet_plugin = $('undistributed_payments_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('undistributed_payments_form_dashlet_plugin_id').value;
    var get_params = Form.serialize(element.form);

    var opt = {
        method: 'post',
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var ajax_result = t.responseText;
            $('container_undistributed_payments_list').parentNode.style.display = '';
            $('container_undistributed_payments_list').innerHTML = ajax_result;
            var scripts = $('container_undistributed_payments_list').getElementsByTagName('script');
            for (var j=0; j<scripts.length; j++) {
                ajaxLoadJS(scripts[j]);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_plugin_id + '&custom_plugin_action=searchUndistributedPayments';
    new Ajax.Request(url, opt);
};

/*
 * Function to load the distribution form
 */
loadDistributionForm = function(payment_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var dashlet_plugin = $('undistributed_payments_form_dashlet_plugin').value;
    var dashlet_plugin_id = $('undistributed_payments_form_dashlet_plugin_id').value;
    var get_params = Form.serialize($('adp_search').form);

    var opt = {
        method: 'post',
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');

            //lightbox have to be shown
            lb = new lightbox({content: result['template'], title: result['title'], width: '1350px'});
            lb.activate();

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_plugin_id + '&custom_plugin_action=loadDistributionForm&payment_id=' + payment_id;
    new Ajax.Request(url, opt);
}

/*
 * Function to cancel the payment distribution
 */
cancelPaymentDistribution = function() {
    lb.deactivate();
}

/*
 * Function to cancel the payment distribution
 */
validatePaymentTotal = function(element) {
    // get the current obligation
    obligation_id = element.id.replace(/^obligation_distributed_sum_([0-9]*)$/, '$1');
    element_current_value = parseFloat(element.value);
    maximum_obligation = parseFloat($('obligation_total_' + obligation_id).innerText);
    calculated_obligations = 0;

    if (element_current_value > maximum_obligation) {
        addClass(element, 'error_over_distribution');
    } else {
        removeClass(element, 'error_over_distribution');
    }

    $$('input[id^="obligation_distributed_sum_"]').each(function(field) {
        if (field.value == '') {
            field_value = 0;
        } else {
            field_value = field.value;
        }
        calculated_obligations += parseFloat(field_value);
    });

    if (calculated_obligations > parseFloat($('payment_not_distributed_amount').innerText)) {
        addClass($('title_distrib_sum'), 'error_over_distribution');
    } else {
        removeClass($('title_distrib_sum'), 'error_over_distribution');
    }
}

/*
 * Function to distribute the payments
 */
amsDistributePayment = function(element) {
    error_text = '';
    error_fields = $$('.error_over_distribution');

    error_fields.each(function(field) {
        if (field.tagName == 'INPUT') {
            error_text += i18n['messages']['error_payment_overpaid'] + '\n';
            return false;
        }
    });
    error_fields.each(function(field) {
        if (field.tagName == 'DIV') {
            error_text += i18n['messages']['error_total_distributed_sum_surpassed'] + '\n';
            return false;
        }
    });
    if (error_text != '') {
        alert(error_text);
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');
    preventResubmit(element.form, true);

    fetch(element.form.action, {method: 'POST', body: new FormData(element.form)})
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            if (!data.error) {
                searchUndistributedPayments($('adp_search'));
                cancelPaymentDistribution();
            } else {
                enableResubmit(element);
            }
        })
        .catch(error => {
            alert('Error: location "' + t.statusText + '" was not found.');
        })
        .finally(() => {
            Effect.Fade('loading');
        });






/*    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the form for submit
    var get_params = Form.serialize(element.form);
    var opt = {
        method: 'post',
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            alert(result.message);
            if (!result.error) {
                searchUndistributedPayments($('adp_search'));
                cancelPaymentDistribution();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = element.form.action;
    new Ajax.Request(url, opt);*/
}
