{if $document}
<div class="t_section_title">
  <h1 class="hcenter">{#plugin_create_production#|escape} - {$document->getPlainVarValue('product_name')|escape}</h1>
</div>
<div class="section_container nopadding">
  <form action="">
    {if $document->get('vars')}
      {assign var='layouts_vars' value=$document->get('vars')}
      {foreach from=$document->get('layouts_details') key='lkey' item='layout'}
        {if $lkey eq 'deadline'}
          <br class="clear" />
          <table class="t_layout_table" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td class="labelbox short"><a name="error_deadline"><label for="deadline_formatted"{if $messages->getErrors('deadline')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
              <td class="required">{#required#}</td>
              <td>
                {include file="input_datetime.html"
                        standalone=true
                        name='deadline'
                        label=$layout.name
                        help=$layout.description
                        value=$document->get('deadline')
                        width=200
                        show_calendar_icon=false
                        disallow_date_before=true
                }
              </td>
            </tr>
          </table>
        {elseif $layout.view && array_key_exists($layout.id, $layouts_vars)}
          {assign var='layout_id' value=$layout.id}
          {assign var='vars' value=$layouts_vars.$layout_id}
          {foreach name='j' from=$vars item='var'}
            {if $var.type}
              {if $var.type eq 'grouping'}
                {if $var.values}
                  {include file="`$smarty.const.PH_MODULES_DIR`documents/templates/input_transform_grouping.html"}
                  <br class="clear" />
        <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table hright floatr nopadding" style="width: 300px;">
                {/if}
              {elseif $var.type eq 'gt2'}
              {else}
                {include file="input_`$var.type`.html"
                          var=$var
                          standalone=false
                          var_id=$var.id
                          name=$var.name
                          custom_id=$var.custom_id
                          label=$var.label
                          help=$var.help
                          back_label=$var.back_label
                          back_label_style=$var.back_label_style
                          value=$var.value
                          value_id=$var.value_id
                          options=$var.options
                          optgroups=$var.optgroups
                          option_value=$var.option_value
                          first_option_label=$var.first_option_label
                          onclick=$var.onclick
                          on_change=$var.on_change
                          sequences=$var.sequences
                          check=$var.check
                          scrollable=$var.scrollable
                          calculate=$var.calculate
                          readonly=$var.readonly
                          source=$var.source
                          onchange=$var.onchange
                          map_params=$var.map_params
                          width=$var.width
                          hidden=$var.hidden
                          really_required=$var.required
                          required=$var.required
                          disabled=$var.disabled
                          options_align=$var.options_align
                          autocomplete=$var.autocomplete
                          js_methods=$var.js_methods
                          restrict=$var.js_filter
                          deleteid=$var.deleteid
                          show_placeholder=$var.show_placeholder
                          text_align=$var.text_align
                          custom_class=$var.custom_class
                }
              {/if}
            {/if}
          {/foreach}
        {/if}
      {/foreach}
        </table>
    {/if}
    <br class="clear" />
    <div class="hright section_container full_width">
      <button class="button" type="button" onclick="production.load({ldelim}action: 'home'{rdelim});">{#back#|mb_upper|escape}</button>
      <button class="button green" type="button" onclick="production.load({ldelim}form: this.form, action: 'create', complex: 1{rdelim});">{#create#|mb_upper|escape}</button>
      <button class="button green" type="button" onclick="production.load({ldelim}form: this.form, action: 'create', complex: 1, data: {ldelim}after_action: 'start'{rdelim}{rdelim});">{#plugin_start#|mb_upper|escape}</button>
    </div>
  </form>
</div>
<script type="text/javascript" defer="defer">production.observe('input[type="checkbox"]', 'change', 'calculateCreateQuantity');</script>
{/if}