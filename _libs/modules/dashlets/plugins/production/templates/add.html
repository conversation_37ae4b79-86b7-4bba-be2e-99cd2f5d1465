{if $dashlet}
  <h1>{$title}</h1>

  <div id="form_container">
    {include file=`$theme->templatesDir`actions_box.html}

    <form name="dashlets" action="{$submitLink}" method="post">
      <input type="hidden" name="model_lang" id="model_lang" value="{$lang}" />
      <input type="hidden" name="module_name" id="module_name" value="{$dashlet->get('module_name')}" />

      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td class="t_footer"></td>
        </tr>
        <tr>
          <td class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <!-- FIELDS FOR ALL PLUGINS -->
              {assign var='module_name' value=$dashlet->get('module_name')}
              <tr>
                <td class="labelbox">{help label='dashlet_for'}</td>
                <td class="required">{#required#}</td>
                <td>{$smarty.config.$module_name|escape}</td>
              </tr>
              {include file=`$theme->templatesDir`input_text.html
                       name='name'
                       custom_id='name'
                       label=#dashlets_name#
                       help=#help_dashlets_name#
                       readonly=0
                       value=$dashlet->get('name')|default:$smarty.config.$module_name
                       required=1
              }
              {include file=`$theme->templatesDir`input_textarea.html
                       name='description'
                       custom_id='description'
                       label=#dashlets_description#
                       help=#help_dashlets_description#
                       value=$dashlet->get('description')
                       required=0
              }
              <tr>
                <td class="labelbox">{help label='default'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {include file="input_checkbox.html"
                    standalone=true
                    name='default'
                    value=$dashlet->get('default')
                    option_value=1
                  }
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="full_width">{help label_content=#dashlets_full_width# text_content=#help_dashlets_full_width#}</label></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {include file="input_checkbox.html"
                           name='full_width'
                           required=0
                           standalone=true
                           custom_id='full_width'
                           value=$dashlet->get('full_width')
                           option_value=1
                  }
                </td>
              </tr>
              {include file=`$templatesDir`_plugin_fields.html}
              <tr>
                <td colspan="3">&nbsp;</td>
              </tr>
              <tr>
                <td colspan="3" style="padding: 10px;">
                  <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      {include file=`$theme->templatesDir`help_box.html}
      {include file=`$theme->templatesDir`system_settings_box.html object=$dashlet}
      {include file=`$theme->templatesDir`after_actions_box.html}
    </form>
  </div>
{/if}