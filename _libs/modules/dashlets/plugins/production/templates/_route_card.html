<div id="content_dashlet_{$dashlet->get('id')}" style="height: 100%; max-height: 80vh;">
  <div class="production_inner_container" style="height: 100%;">
    <table class="" style="height: inherit; width: 100%; margin: auto;">
      <tr>
        <td class="vmiddle">
          {if $cards}
          <div class="hleft" style="padding: 10px;">
            <img src="{$theme->imagesUrl}move.png" class="t_info_image bb_numbers_expand_collapse" /> <span class="legend">{#help_card_order#|escape}</span>
          </div>
          <table class="t_layout_table reports_table">
            <tbody id="cards_order">
              {foreach from=$cards item='card' key='ck' name='ci'}
              {if $smarty.foreach.ci.first}
              <tr class="reports_title_row">
                <td>{$labels.num|escape}</td>
                <td{if $multiple_field.hidden} class="hidden"{/if}>{help label_content=$labels.multiple|escape text_content=$multiple_field.label|escape label_sufix=''}</td>
                <td>{$labels.code|escape}</td>
                <td>{$labels.name|escape}</td>
                <td>{$labels.type_workplace|escape}</td>
                <td>{$labels.time_execute|escape}</td>
                <td>{$labels.scheme|escape}</td>
              </tr>
              {/if}
              <tr class="drag">
                <td>
                  <span{if in_array($card->get('id'), $selected)} class="checked"{/if}>
                    <input type="checkbox" id="items_{$card->get('id')}" name="items[{$card->get('id')}]" value="{$card->get('id')}" onclick="if (this.checked) addClass(this.parentNode, 'checked'); else removeClass(this.parentNode, 'checked');" {if in_array($card->get('id'), $selected)} checked="checked"{/if} />
                  </span>
                </td>
                <td{if $multiple_field.hidden} class="hidden"{/if}>
                  {include file='input_text.html'
                           standalone=true
                           name='multiple'
                           eq_indexes=true
                           index=$card->get('id')
                           value=$card->get('multiple')|default:1
                           width=$multiple_field.width
                           text_align=$multiple_field.text_align
                           restrict=$multiple_field.js_filter|default:'insertOnlyPositiveIntegers'
                           label=$multiple_field.label
                  }
                </td>
                <td>{$card->get('code')|escape}</td>
                <td><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$card->get('id')}" target="_blank" title="{#preview#|escape} {#of#|escape} {$card->get('type_name')|escape}">{$card->get('name')|escape}</a></td>
                <td>{$card->getVarValue('type_workplace')|escape}</td>
                <td>
                  {assign var='time_execute' value=$card->getVarValue('time_execute')}
                  {if $time_execute gt 300}
                    {math equation='round(a/60)' assign='time_execute' a=$card->getVarValue('time_execute')|default:0}
                    {php}echo General::minutes2human($GLOBALS['registry'], $this->get_template_vars('time_execute'));{/php}
                  {elseif $time_execute !== ''}
                    {$time_execute} {#seconds#|escape}
                  {else}
                    &nbsp;
                  {/if}
                </td>
                <td class="hcenter">
                  {$card->getVarValue('current_specification_file')}
                </td>
              </tr>
              {/foreach}
            </tbody>
          </table>
          <div class="hright" style="padding: 10px;">
            <button class="button short" type="button" onclick="production.manageRouteCard(card_values, '{$multiple_field.grouping}');">{#save#|escape}</button>
            <button class="button short" type="button" onclick="lb.deactivate();">{#cancel#|escape}</button>
          </div>
          <script type="text/javascript">
            Position.includeScrollOffsets = true;
            Sortable.create('cards_order', {ldelim}tag: 'TR',
                                              containment: 'cards_order',
                                              constraint: 'vertical',
                                              only: 'drag',
                                              scroll: 'cards_order'
                                           {rdelim});
          </script>
          {if !$dashlet->get('no_access')}
            {foreach from=$scripts item='script'}
            <script defer="defer" type="text/javascript" src="{$script}?{$system_options.build}"></script>
            {/foreach}
            <script type="text/javascript" defer="defer">
              var card_values = {json encode=$values};
              {* init object once when lightbox is loaded *}
              if (typeof production == 'function') production = new production('{$dashlet->get('id')}', '{$dashlet->get('controller')}', {json encode=$dashlet_data|default:''});
            </script>
          {/if}
          {else}
            {include file='message.html' display='warning' items=$warnings}
          {/if}
        </td>
      </tr>
    </table>
  </div>
</div>