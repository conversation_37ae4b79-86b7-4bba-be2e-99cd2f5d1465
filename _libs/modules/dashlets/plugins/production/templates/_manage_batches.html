<table class="t_layout_table t_table" cellpadding="0" cellspacing="0">
{foreach from=$data item='row' key='rk' name='ri'}
  {if $smarty.foreach.ri.first}
  <tr class="reports_title_row hcenter">
    <td style="width: 5%;">{#num#|escape}</td>
    <td>{#plugin_material#|escape}</td>
    <td>{#plugin_measure#|escape}</td>
    <td style="width: 15%;">{$row.vars_settings.available_quantity.label|escape}</td>
    <td style="width: 15%;">{#plugin_used_quantity#|escape}</td>
    <td style="width: 15%;">{#plugin_waste_quantity#|escape}</td>
  </tr>
  {/if}
  <tr class="t_panel_caption">
    <td class="hright">{$smarty.foreach.ri.iteration}</td>
    <td class="t_panel_caption_title">{$row.article_name|escape}</td>
    <td class="hcenter">
      {foreach from=$measure_options item='opt'}
        {if $opt.option_value eq $row.article_measure_name}{$opt.label|escape}{/if}
      {/foreach}
    </td>
    <td></td>
    <td>
      {include file="input_text.html"
               standalone=true
               custom_class="short hright viewmode strong"
               name='total_material'
               index=$rk
               eq_indexes=true
               readonly=true
               disabled=true
               label=#plugin_used_quantity#
               value=$row.material|default:0
      }
    </td>
    <td>
      {include file="input_text.html"
               standalone=true
               custom_class="short hright viewmode strong"
               name='total_waste'
               index=$rk
               eq_indexes=true
               readonly=true
               disabled=true
               label=#plugin_waste_quantity#
               value=$row.waste|default:0
      }
    </td>
  </tr>
  {if $row.article_measure_name eq $measure_num}
    {assign var='measure_class' value='number'}
    {assign var='restrict_fn' value='insertOnlyDigits'}
  {else}
    {assign var='measure_class' value=''}
    {assign var='restrict_fn' value='insertOnlyFloats'}
  {/if}
  {assign var='not_changed_material' value=false}
  {if $row.material + $row.updated_quantity_material eq 0}{assign var='not_changed_material' value=true}{/if}
  {assign var='not_changed_waste' value=false}
  {if $row.waste + $row.updated_quantity_waste eq 0}{assign var='not_changed_waste' value=true}{/if}
  {foreach from=$row.batch_data item='bd' key='bk' name='bi'}
  <tr>
    <td class="hright">{$smarty.foreach.ri.iteration}.{$smarty.foreach.bi.iteration}</td>
    <td colspan="2">
      <span class="legend">{#finance_warehouses_documents_batch#}:</span> {$bd.batch_code}
      {if $row.has_serial}, <span class="legend">{$row.vars_settings.serial.label}:</span> {$bd.serial}{/if}
      {if $row.has_expire}, <span class="legend">{$row.vars_settings.expire.label}:</span> {$bd.expire|date_format:#date_short#}{/if}
      {*if $bd.custom}, <span class="legend">{$row.vars_settings.custom.label}:</span> {$bd.custom}{/if*}
    </td>
    <td>
      {include file="input_hidden.html"
               standalone=true
               name="batch_`$rk`"
               index=$smarty.foreach.bi.iteration
               value=$bk
      }
      {include file="input_text.html"
               standalone=true
               custom_class="short hright viewmode"
               name="available_`$rk`"
               index=$smarty.foreach.bi.iteration
               readonly=true
               disabled=true
               label=$row.vars_settings.available_quantity.label
               value=$bd.quantity
      }
    </td>
    <td>
      {math assign='uq_material' equation='-1 * a' a=$bd.updated_quantity_material}
      {include file="input_text.html"
               standalone=true
               custom_class="short hright `$measure_class` total_material_`$rk`"
               name="material_`$rk`"
               index=$smarty.foreach.bi.iteration
               label=#plugin_used_quantity#
               value=$uq_material
               restrict=$restrict_fn
               readonly=$not_changed_material
               onblur="production.calculateManageQuantity(this);"
      }
    </td>
    <td>
      {math assign='uq_waste' equation='-1 * a' a=$bd.updated_quantity_waste}
      {include file="input_text.html"
               standalone=true
               custom_class="short hright `$measure_class` total_waste_`$rk`"
               name="waste_`$rk`"
               index=$smarty.foreach.bi.iteration
               label=#plugin_waste_quantity#
               value=$uq_waste|default:0
               restrict=$restrict_fn
               readonly=$not_changed_waste
               onblur="production.calculateManageQuantity(this);"
      }
    </td>
  </tr>
  {/foreach}
{foreachelse}
  <tr class="t_odd1 t_odd2">
    <td class="error">{#plugin_no_data_entered#|escape}</td>
  </tr>
{/foreach}
</table>
<br class="clear" />
<br class="clear" />
<div class="hright production_container">
  <button class="button" type="button" onclick="lb.deactivate();">{#cancel#|mb_upper|escape}</button>
  <button class="button green" type="button" onclick="production.submitManageQuantity();">{#confirm#|mb_upper|escape}</button>
</div>
