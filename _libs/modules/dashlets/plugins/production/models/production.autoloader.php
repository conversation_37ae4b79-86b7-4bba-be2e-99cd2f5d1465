<?php

Production_Autoloader::register();

/**
 * Autoloader class of production plugin
 *
 * @category Production
 * @package Production
 * @see PHPExcel_Autoloader
 */
class Production_Autoloader {
    /**
     * Register the Autoloader with SPL
     */
    public static function register() {
        if (function_exists('__autoload')) {
            // Register any existing autoloader function with SPL, so we don't get any clashes
            spl_autoload_register('__autoload');
        }
        // register load function of current class as __autoload implementation
        spl_autoload_register(array('Production_Autoloader', 'load'));
    }

    /**
     * Loads entity classes of plugin (files are located in same folder as
     * current file)
     *
     * @param string $class_name - name of class to load
     */
    public static function load($class_name) {
        if (!class_exists($class_name, false)) {
            $filename = __DIR__ . '/' . str_replace('_', '.', strtolower($class_name)) . '.php';
            if (file_exists($filename) && is_readable($filename)) {
                require_once $filename;
            }
        }
    }
}
