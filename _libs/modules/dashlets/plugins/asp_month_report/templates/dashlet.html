<form method="post" id="add_edit_protocol" onsubmit="return false" name="add_edit_protocol" action="{$smarty.server.PHP_SELF}?{$module_param}=dashlets&amp;dashlets=custom_action&amp;custom_plugin_action=saveProtocol&amp;plugin={$dashlet_plugin}&amp;dashlet={$dashlet_id}">
  <input id="month_report_dashlet_plugin" name="month_report_dashlet_plugin" value="{$dashlet_plugin}" type="hidden" />
  <input id="month_report_dashlet_id" name="month_report_dashlet_id" value="{$dashlet_id}" type="hidden" />
  {foreach from=$scripts_url item='script_url'}
    <script defer="defer" src="{$script_url}?{$system_options.build}"></script>
  {/foreach}
  <table border="0" cellpadding="5" cellspacing="3" class="t_table" width="100%">
    <tr>
      <td style="width: 200px!important;" nowrap="nowrap">
        {#plugin_month_report_client#}:<br/>
        {include file=input_autocompleter.html
          name='month_report_customer'
          autocomplete=$filters.customer_autocompleter
          autocomplete_var_type='basic'
          standalone=true
          width=200
        }
      </td>
      <td style="width: 200px!important;">
        {#plugin_month_report_contract#}:<br/>
        {include file=input_dropdown.html
          name=client_contract
          options=$filters.contract_options
          standalone=true
          onchange='getContractPeriods(this)'
          width=200
        }
      </td>
      <td style="width: 200px!important;">
        {#plugin_month_report_protocol_period#}:<br/>
        {include file=input_dropdown.html
          name=protocol_period
          options=$filters.protocol_period
          standalone=true
          onchange='loadArticlesData(this)'
          width=200
        }
      </td>
      <td>
        &nbsp;
      </td>
    </tr>
    <tr>
      <td colspan="4" style="display: none; padding: 0;" id="month_report_data"></td>
    </tr>
  </table>
</form>
