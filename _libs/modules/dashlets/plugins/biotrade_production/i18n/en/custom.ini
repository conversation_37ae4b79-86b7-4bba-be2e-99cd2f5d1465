#LABELS FOR ALL PLUGINS
dashlets_dashlet_for = Dashlet for
plugin_name = Name
plugin_description = Description
dashlets_default = Add by default
plugin_full_width = Full screen width
help_dashlets_default = Specifies whether the dashlet to be added by default

help_dashlets_dashlet_for = Module or report dashlet is for
help_plugin_full_width = If selected, dashlet will be displayed in full screen width

#PLUGIN SPECIFIC LABELS
plugin_biotrade_production = Production

plugin_biotrade_production_no_specific_fields = No specific fields for this dashlet

plugin_biotrade_production_materials_selection = Select materials
plugin_biotrade_production_start_production = Start production
plugin_biotrade_production_resume_production = Resume production
plugin_biotrade_production_warehouse_entry = Warehouse entry of production and waste / return to warehouse
plugin_biotrade_production_cancel_production = Cancel production
plugin_biotrade_production_finish_production = Finish production
plugin_biotrade_production_back_to_start = Back to start screen

plugin_biotrade_production_start_production_option = Start new production
plugin_biotrade_production_resume_production_option = Resume started production
plugin_biotrade_production_recipe = Recipe
plugin_biotrade_production_quantity = Quantity
plugin_biotrade_production_qty = Qty.
plugin_biotrade_production_quantity_in = Used
plugin_biotrade_production_quantity_loss = Wastage
plugin_biotrade_production_quantity_loss_steps = Wastage (steps)
plugin_biotrade_production_quantity_loss_total = Wastage (total)
plugin_biotrade_production_quantity_returned = Returned
plugin_biotrade_production_necessary_quantity = Necessary quantity
plugin_biotrade_production_available_quantity = Available quantity
plugin_biotrade_production_article_code = Code
plugin_biotrade_production_article_name = Article
plugin_biotrade_production_measure = MU
plugin_biotrade_production_lot = Lot
plugin_biotrade_production_selected_recipe = Selected recipe
plugin_biotrade_production_selected_quantity = Selected quantity
plugin_biotrade_production_selected_lot = Selected lot
plugin_biotrade_production_select_materials = Select materials
plugin_biotrade_production_select_production = Select production
plugin_biotrade_production_start = Start
plugin_biotrade_production_resume = Resume
plugin_biotrade_production_finish = Finish
plugin_biotrade_production_next_step = Next step
plugin_biotrade_production_back_step = Undo last action
plugin_biotrade_production_last_step = last step
plugin_biotrade_production_num_step = %d. step
plugin_biotrade_production_unstarted_production = unstarted production
plugin_biotrade_production_warehouse_entry_step = warehouse entry
plugin_biotrade_production_no_permissions = You don't have rights to start or resume production.
plugin_biotrade_production_request_num = Request No.
plugin_biotrade_production_request_added = Date
plugin_biotrade_production_request_validity_term = To date
plugin_biotrade_production_no_validity_term = Not specified
plugin_biotrade_production_request_notes = Note
plugin_biotrade_production_quantity_without_request = Quantity without request
plugin_biotrade_production_quantity_request = Requested quantity
plugin_biotrade_production_quantity_produced = Produced
plugin_biotrade_production_quantity_entered = For production
plugin_biotrade_production_quantity_enter = Quantity to be produced for this request
plugin_biotrade_production_step_started = Started
plugin_biotrade_production_step_ended = Finished
plugin_biotrade_production_record_name = Production record [date] [article_name]
plugin_biotrade_production_quantity_produced_quantity = Produced quantity
plugin_biotrade_production_quantity_waste = Wastage
plugin_biotrade_production_quantity_in_warehouse = Enter into warehouse
plugin_biotrade_production_quantity_samples = Samples
plugin_biotrade_production_used_materials = Used and returned materials
plugin_biotrade_production_expiry_date = Expiry date

message_plugin_biotrade_production_cancel_confirm = ATTENTION!\nIf "Cancel" is selected, production is terminated and materials are returned to warehouse.\nAre you sure you want to stop production?
message_plugin_biotrade_production_cancel_success = Production %s, %s was cancelled successfully!
message_plugin_biotrade_production_finish_success = Production %s, %s was finished successfully!

error_plugin_biotrade_production_access_denied = Production is not available for modifications because another user has worked on it in the past %s minutes!
error_plugin_biotrade_production_single_dashlet = There is already a production dashlet. You can work in a single production dashlet only!
error_plugin_biotrade_production_select_product = Please, select recipe!
error_plugin_biotrade_production_invalid_product = Selected recipe is invalid or does not exist!
error_plugin_biotrade_production_no_requests = There are no active requests for selected recipe!
error_plugin_biotrade_production_invalid_lot = There shoud be exactly one active lot for selected recipe! Found: %d
error_plugin_biotrade_production_invalid_measure = Selected recipe has no measure specified!
error_plugin_biotrade_production_invalid_data = Invalid or missing production data!
error_plugin_biotrade_production_invalid_articles = Articles in recipe %s and production card %s differ. Please, correct them!
error_plugin_biotrade_production_invalid_recipe = Invalid or missing data in production recipe %s!
error_plugin_biotrade_production_product_not_batch = Selected product is not a batch article!
error_plugin_biotrade_production_invalid_product_subtype = Please, modify subtype or ingredients of recipe %s to start production for it!
error_plugin_biotrade_production_invalid_batches = Please, select only one batch of %s [row %s]!
error_plugin_biotrade_production_invalid_warehouse = Invalid or missing warehouse data! Please, contact the support team!
error_plugin_biotrade_production_process_step = Error saving current step!
error_plugin_biotrade_production_warehouse_entry = Error during warehouse entry!
error_plugin_biotrade_production_warehouse_entry_unfinished = All steps should be finished before warehouse entry!
error_plugin_biotrade_production_missing_subproduct_batch = Missing batch for product which is an ingredient in current production!
error_plugin_biotrade_production_invalid_subproduct_batch_code = Batch "%s" of ingredient %s does not have the expected format (expected batch number: "%s")!
error_plugin_biotrade_production_invalid_subproduct_batch_code_expiry_date = Batch "%s" of ingredient %s does not end in date in "DD.MM.YYYY" format!
error_plugin_biotrade_production_invalid_previous_production = Missing data for previous production of product using the same batch of material! Please, contact the support team!
error_plugin_biotrade_production_invalid_time = The system could not specify batch code due to duplicate production time. Please, try finishing production later!