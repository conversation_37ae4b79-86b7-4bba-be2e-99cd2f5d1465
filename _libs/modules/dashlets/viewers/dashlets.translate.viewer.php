<?php

class Dashlets_Translate_Viewer extends Viewer {
    public $template = 'translate.html';

    public function prepare() {
        $this->model = $this->registry['dashlet'];
        $this->data['dashlet'] = $this->model;

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('d.id = ' . $this->model->get('id')),
                         'model_lang'    => $basic_model_lang,
                         'sanitize'      => true);
        $base_model = Dashlets::searchOne($this->registry, $filters);

        $module = $base_model->get('module');
        $controller = $base_model->get('controller');

        if ($module == 'reports') {
            require_once PH_MODULES_DIR . "reports/models/report.filters.php";
            require_once(PH_MODULES_DIR . 'reports/models/reports.factory.php');
            $report_filters = array('name' => $controller,
                                    'sanitize' => true);
            $report = Reports::getReports($this->registry, $report_filters);
            $this->data['module_name_i18n'] = $report[0]->get('name');
        } elseif ($module == 'plugin') {
            $this->data['module_name_i18n'] = $this->model->get('plugin_name');
        } else {
            if ($module == $controller) {
                $this->data['module_name_i18n'] = $this->registry['translater']->getParam('' , 'menu_' . $module);
            } else {
                $this->data['module_name_i18n'] = $this->registry['translater']->getParam('' , 'menu_' . $module . '_' . $controller);
            }
        }

        $this->data['module_name'] = $module . '_' . $controller;
        $this->data['base_model'] = $base_model;
    }

    public function prepareTitleBar() {
        $title = $this->i18n('dashlets_translate');
        $this->data['title'] = $title;
    }
}

?>
