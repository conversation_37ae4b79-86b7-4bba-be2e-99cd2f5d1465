<?php

class Dashlets_List_Viewer extends Viewer {
    public $template = 'list.html';
    public $filters = array();

    /**
     * Sortable columns in the list view
     */
    public $sortables = array('model', 'template_type', 'model_type_name', 'section');

    public function prepare() {
        require_once $this->modelsDir . 'dashlets.factory.php';

        $filters = Dashlets::saveSearchParams($this->registry);
        list($dashlets, $pagination) = Dashlets::pagedSearch($this->registry, $filters);

        require_once(PH_MODULES_DIR . 'reports/models/reports.factory.php');
        $reports = Reports::getReports($this->registry, array('sanitize' => true, 'visible' => 1));
        $reports_types = array();
        foreach ($reports as $report) {
            $reports_types[$report->get('type')] = $report->get('name');
        }

        $this->data['reports_types'] = $reports_types;
        $this->data['dashlets'] = $dashlets;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('dashlets');
        $this->data['title'] = $title;
    }
}

?>
