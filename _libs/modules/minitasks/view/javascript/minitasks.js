/**
 * Inserts a new minitask row
 *
 * @param element - the current object
 */
function insertNewMinitaskRow(element) {
    var dashlet_id = insertNewMinitaskRow.arguments.length > 1 ? insertNewMinitaskRow.arguments[1] : '';

    if (element.id == 'add_action') {
        element = $('img_add_minitask');
    }

    // taking current row
    var title_row = element.closest('tr');

    // parent table
    var table = title_row.closest('table');

    // defines new row index based on the current row index
    // (first two rows are title row and the hidden one)
    var hidden_row_idx = title_row.rowIndex + 1;

    var row_data = table.rows[hidden_row_idx];

    // taking row cells
    var cells = row_data.cells;

    var next_row = table.rows[hidden_row_idx + 1];
    var next_row_index = next_row.id ? parseInt(next_row.id.replace(/.*_(\d+)$/, '$1')) : '';
    var hidden_row_index = parseInt(row_data.id.replace(/.*_(\d+)$/, '$1'));

    var row_index = (next_row_index && next_row_index > hidden_row_index) ? next_row_index + 1 : hidden_row_index + 1;
    //field index for all the fields in mini task
    var field_index = row_index + 1;

    // inserts the new row and set its classes
    var new_row = table.insertRow(hidden_row_idx+1);
    addClass(new_row, 't_selected_row_for_edit');
    new_row.id = row_data.id.replace(/(.*_)\d+$/, '$1' + row_index);
    // row should have same class name as its id
    if (dashlet_id) {
        addClass(new_row, new_row.id);
    }

    // create new cell
    for (let i = 0; i < cells.length; i++) {
        let cell_elements_o = [...cells[i].childNodes];
        let cell_elements = [];
        new_cell = new_row.insertCell(i);
        new_cell.className = cells[i].className;
        if (cells[i].id) {
            new_cell.id = cells[i].id.replace(/(.*_)\d+/, '$1' + field_index);
        }
        new_cell.style.display = cells[i].style.display;

        // sets new cell's inside elements
        for (let j = 0; j < cell_elements_o.length; j++) {
            if (cell_elements_o[j].classList && cell_elements_o[j].classList.contains('nz-form-input-wrapper')) {
                cell_elements[j] = cell_elements_o[j].querySelector('input,select');
            } else {
                cell_elements[j] = cell_elements_o[j];
            }

            if (cell_elements[j].tagName === 'INPUT' || cell_elements[j].tagName === 'SELECT') {
                // defines the field name
                let field_name = cell_elements[j].name.replace(/\[[0-9]*\]/, '');

                if (field_name.match(/_(oldvalue|autocomplete)_/)) {
                    continue;
                }
                // create dropdowns and other input elements
                var field_type = '';
                var field_value = '';
                var field_style_width = '';
                var field_onchange = '';
                var field_checked = false;
                var field_disabled = false;
                var field_options = '';
                var field_title = cell_elements[j].title;

                // sets the properties depending on the field name
                switch (field_name) {
                    case 'record_model_type':
                        field_type = 'dropdown';
                        field_style_width = cell_elements[j].style.width;
                        field_onchange = cell_elements[j].attributes.onchange.value;
                        break;
                    case 'model_id':
                    case 'model':
                        field_type = 'hidden';
                        field_value = cell_elements[j].value;
                        break;
                    case 'customer':
                        if (cells[i].style.display == 'none' && !dashlet_id) {
                            field_type = 'hidden';
                            field_value = cell_elements[j].value;
                        } else {
                            field_type = 'autocompleter';
                            field_style_width = '176px';
                            var params_customer_autocomplete = window['params_' + $('customer_autocomplete_' + (hidden_row_index+1)).getAttribute('uniqid')];
                            field_options = {
                                type:           'customers',
                                url:            env.base_url + '?' + env.module_param + '=customers&customers=ajax_select',
                                suggestions:    '<name> <lastname>',
                                fill_options:   ['$customer => <id>',
                                                 '$customer_autocomplete => <name> <lastname>',
                                                 '$customer_oldvalue => <name> <lastname>'],
                                clear:          1,
                                filters:        (typeof(params_customer_autocomplete) == 'object' ? params_customer_autocomplete.filters : false),
                                execute_after:  'clearMinitaskModelIDAutocompleter'
                            };
                            field_title = $(cell_elements[j].id.replace(/(_\d+)$/, '_autocomplete$1')).title;
                        }
                        break;
                    case 'description':
                        field_type = 'text';
                        field_style_width = cell_elements[j].style.width;
                        break;
                    case 'deadline':
                        field_type = 'dropdown';
                        field_value = 'none';
                        break;
                    case 'assigned_to':
                        var autocomplete_field = $(cell_elements[j].id.replace(/(_\d+)$/, '_autocomplete$1'));
                        if (!autocomplete_field) {
                            field_type = 'hidden';
                            field_value = cell_elements[j].value;
                        } else {
                            field_type = 'autocompleter';
                            field_value = cell_elements[j].value;
                            field_style_width = '126px';
                            field_options = {
                                type:           'users',
                                url:            env.base_url + '?' + env.module_param + '=users&users=ajax_select',
                                suggestions:    '<firstname> <lastname>',
                                fill_options:   ['$assigned_to => <id>',
                                                 '$assigned_to_autocomplete => <firstname> <lastname>',
                                                 '$assigned_to_oldvalue => <firstname> <lastname>'],
                                clear:          1
                            };
                            field_title = autocomplete_field.title;
                        }
                        break;
                    default:
                        break;
                }

                if (field_type) {
                    // params for the new field
                    var params = {type:         field_type,
                                  name:         field_name,
                                  index:        field_index,
                                  value:        field_value,
                                  options:      field_options,
                                  sequences:    field_onchange,
                                  checked:      field_checked,
                                  width:        field_style_width,
                                  //eq_indexes:   true,
                                  disabled:     field_disabled,
                                  title:        field_title
                                 };

                    // creates the new element
                    var new_created_field = createField(params, new_cell);

                    if (field_type == 'dropdown') {
                        new_created_field.style.width = cell_elements[j].style.width;

                        // fills the dropdowns with the required options
                        var options_list = cell_elements[j].children;
                        if (options_list.length) {
                            new_created_field.options.length = 0;
                            removeClass(new_created_field, 'missing_records');
                            for (var p = 0; p < options_list.length; p++) {
                                var sequence_optgroup = options_list[p].cloneNode(true);
                                new_created_field.appendChild(sequence_optgroup);
                            }
                            new_created_field.options[0].selected = true;
                            toggleUndefined(new_created_field);
                        }
                    } else if (field_type == 'autocompleter') {
                        if (field_name == 'assigned_to') {
                            new_created_field.value = env.current_user_id;
                            $(new_created_field.id.replace(/(_\d+)$/, '_autocomplete$1')).value =
                            $(new_created_field.id.replace(/(_\d+)$/, '_oldvalue$1')).value =
                                env.current_user_name;
                        }
                    }
                }
            } else if (cell_elements[j].tagName === 'DIV') {
                if (cell_elements[j].id.match(/^suggestions_.*_autocomplete_\d+$/)) {
                    continue;
                }
                var new_div_element = '';
                if (cell_elements[j].id && cell_elements[j].id.match(/^model_id_div_\d+$/)) {
                    new_div_element = cell_elements[j].cloneNode(false);
                    new_div_element_id = new_div_element.id.replace(/[0-9]*$/, field_index);
                    new_div_element.id = new_div_element_id;
                    new_div_element.style.display = 'none';
                } else {
                    new_div_element = cell_elements[j].cloneNode(true);
                }
                created_div = new_cell.appendChild(new_div_element);

            } else if (cell_elements[j].tagName === 'TEXTAREA') {
                var new_textarea_element = cell_elements[j].cloneNode(true);
                new_textarea_element_id = new_textarea_element.id.replace(/[0-9]*$/, field_index);
                new_textarea_element.id = new_textarea_element_id;
                new_textarea_element.name = new_textarea_element.name.replace(/\[(\d+)\]$/, '[' + row_index + ']');
                new_textarea_element.value = '';
                created_textarea = new_cell.appendChild(new_textarea_element);
            } else if (cell_elements[j].tagName === 'A' && cell_elements[j].id.match(/^error_/)) {
                var new_anchor_element = cell_elements[j].cloneNode(true);
                new_anchor_element_id = new_anchor_element.id.replace(/[0-9]*$/, row_index);
                new_anchor_element.id = new_anchor_element_id;
                created_anchor = new_cell.appendChild(new_anchor_element);
            } else if(typeof cell_elements_o[j].tagName !== 'undefined') {
                if (cell_elements_o[j].match('.nz-icon-button')) {
                    let new_element = cell_elements_o[j].cloneNode(true);
                    new_element_id = new_element.id.replace(/[0-9]*$/, row_index);
                    new_element.id = new_element_id;
                    new_element.style.display= 'inline-block';
                    //new_img_element.name = new_img_element.name.replace(/\[\d+\]$/, '[' + row_index + ']');
                    new_cell.appendChild(new_element);
                }
            }
        }
    }

    return true;
}

/**
 * Adds an autocompleter for selected model and model type and hides dropdown.
 *
 * @param element - dropdown element with models and model types
 */
function changeMinitaskRecordType(element) {
    const containerId = element.id.replace(/record_model_type/, 'model_id_div');
    const container = element.closest('td').querySelector('#'+containerId);

    const index = parseInt(element.id.replace(/[a-zA-z_]+(\d+)$/, '$1'));
    if (element.value) {
        let autocomplete_type = element.value.toLowerCase().replace(/_\d+$/, '') + 's';
        let model_type = parseInt(element.value.replace(/[a-zA-z_]+(\d+)$/, '$1'));

        const options = Object();
        options.clear = 1;
        options.refresh = 0;
        options.type = autocomplete_type;
        options.filters = {};
        options.filters['<type>'] = model_type.toString();

        if (autocomplete_type !== 'nomenclatures') {
            options.filters['<customer>'] = '\$customer_' + index;
            if (element[element.selectedIndex].className.match(/requires_completed_minitasks/)) {
                if (autocomplete_type === 'documents') {
                    options.filters['<status>'] = '!= closed';
                } else if (autocomplete_type === 'projects' || autocomplete_type === 'tasks') {
                    options.filters['<status>'] = '!= finished';
                }
            }

            let code_field = 'code';
            if (autocomplete_type === 'documents' || autocomplete_type === 'tasks') {
                code_field = 'full_num';
            } else if (autocomplete_type === 'contracts') {
                code_field = 'num';
            }
            options.fill_options = [
                '$model_id => <id>',
                '$model_id_autocomplete => [<' + code_field + '>] <name>',
                '$model_id_oldvalue => [<' + code_field + '>] <name>',
                '$customer => <customer>',
                '$customer_autocomplete => <customer_name>',
                '$customer_oldvalue => <customer_name>'
            ];
        } else {
            options.fill_options = [];
        }
        options.id_var = '';
        options.url = env.base_url + '?' + env.module_param + '='+ autocomplete_type + '&' + autocomplete_type + '=ajax_select';

        let sequences = '';

        // create an autocomplete field
        let params = {
            type: 'autocompleter',
            name: 'model_id',
            index: index,
            sequences: sequences,
            options: options,
            disabled: false,
            width: '170px'
        };

        container.innerHTML = '';
        record_field = createField(params, container);

        const record_field_autocomplete = $(record_field.id.replace(/(_\d+)$/, '_autocomplete$1'));
        record_field_autocomplete.style.marginLeft = '0px';
        record_field_autocomplete.title = element.options[element.selectedIndex].text;
        record_field_autocomplete.placeholder = element.options[element.selectedIndex].text;

        const x = container.getElementsByTagName("script");
        for (var i = 0; i < x.length; i++) {
           eval(x[i].text);
        }

        const resetButton = document.createElement('a');
        resetButton.href = 'javascript:void(0);';
        resetButton.classList.add('nz-input-controls');
        resetButton.innerHTML = '<i class="material-icons nz-input-icon nz-md-backspace">delete</i>';
        resetButton.title = i18n['labels']['delete'];
        resetButton.addEventListener('click',(e) => {
            resetMinitaskRecordType(container.id);
        });
        container.appendChild(resetButton);

        element.style.display = 'none';
        container.style.display = '';

        $('model_' + index).value = element.value.replace(/_\d+$/, '');
    } else {
        container.innerHTML = '';
        container.style.display = 'none';
        $('model_' + index).value = '';
    }

    toggleMinitaskCustomerAutocompleter(element);
}

/**
 * Resets selection of model and model type for minitask, removes the autocompleter
 * for them and displays dropdown for selection of model and model type.
 *
 * @param container_id - id of container div for autocompleter
 */
function resetMinitaskRecordType(container_id) {
    var container = $(container_id);
    container.innerHTML = '';
    container.style.display = 'none';

    var index = parseInt(container_id.replace(/[a-zA-z_]+(\d+)$/, '$1'));
    var element = $('record_model_type_' + index);
    element.style.display = '';
    element.value = '';
    toggleUndefined(element);
    $('model_' + index).value = '';
    toggleMinitaskCustomerAutocompleter(element);
}

/**
 * Enables or disables+clears customer autocompleter depending on
 * selected model for minitask (Nomenclature has no customer).
 *
 * @param element - dropdown with models and model types
 */
function toggleMinitaskCustomerAutocompleter(element) {
    var index = parseInt(element.id.replace(/[a-zA-z_]+(\d+)$/, '$1'));
    var disabled = $('model_' + index).value == 'Nomenclature' ? true : false;

    if (0&&element.form.parentNode.id.match(/content_dashlet_\d+$/)) {
        //TODO
    } else {
        $('customer_' + index).disabled = disabled;
        if ($('customer_autocomplete_' + index)) {
            $('customer_autocomplete_' + index).disabled = disabled;
        }
        if ($('customer_oldvalue_' + index)) {
            $('customer_oldvalue_' + index).disabled = disabled;
        }
        if (disabled) {
            $('customer_' + index).value = '';
            if ($('customer_autocomplete_' + index)) {
                $('customer_autocomplete_' + index).value = '';
            }
            if ($('customer_oldvalue_' + index)) {
                $('customer_oldvalue_' + index).value = '';
            }
            addClass($('customer_autocomplete_' + index), 'readonly');
        } else {
            removeClass($('customer_autocomplete_' + index), 'readonly');
        }
    }
}

/**
 * Function to be executed after selection/clearing of customer autocompleter.
 * If autocomplter for model exists, its fields are cleared.
 *
 * @param object autocomplete settings
 * @param object data autocompleter returned data
 */
function clearMinitaskModelIDAutocompleter(autocomplete, data) {
    var index = '';
    if (data.row) {
        index = data.row;
    } else if (autocomplete.field && autocomplete.field.match(/autocomplete_\d+$/)) {
        index = autocomplete.field.replace(/.*_(\d+)$/, '$1');
    }
    if ($('model_id_' + index) != null) {
        $('model_id_' + index).value = '';
        $('model_id_autocomplete_' + index).value = '';
        $('model_id_oldvalue_' + index).value = '';
    }
}

/**
 * AJAX functionality that processes actions with minitask
 *
 * @param form - the DOM element form
 * @param real_module - current module
 * @param real_action - current action
 * @param action - action to be performed with minitask
 * @param element - element that submits form
 */
function manageMinitask(form, real_module, real_action, action, element) {
    const index = parseInt(element.id.replace(/[a-zA-z_]+(\d+)$/, '$1'));
    const minitask_id = manageMinitask.arguments.length > 5 ? manageMinitask.arguments[5] : '';
    const status = manageMinitask.arguments.length > 6 ? manageMinitask.arguments[6] : '';
    const formEl = element.closest('form');
    let dashlet_id = '';
    if (formEl.parentNode.id.match(/content_dashlet_\d+$/)) {
        dashlet_id = formEl.parentNode.id.replace(/content_dashlet_(\d+)$/, '$1');
    }

    nzShowLoading()

    var opt = {
        method: 'post',
        asynchronous: false,
        parameters: Form.serialize(formEl) + (form ? '&'+Form.serialize(form) : ''),
        onSuccess: function(t) {
            //alert(t.responseText);
            if (!checkAjaxResponse(t.responseText)) {
                nzHideLoading();
                return false;
            }
            result = t.responseText;
            eval('result = ' + result + ';');
            const isLocalMessages = real_action === 'communications' || dashlet_id;

            var messages_container = isLocalMessages ?
                                     'communication_messages_container' + dashlet_id :
                                     'messages_container';
            const mainMessages = MainMessages.getInstance();
            if (result.errors) {
                if (isLocalMessages) {
                    $(messages_container).innerHTML = result.errors;
                    new Effect.ScrollTo(messages_container);
                    setTimeout(nzHeaderHide, 1100);
                } else {
                    mainMessages.setMessagesBody(result.errors);
                }
                nzHideLoading();
                return false;
            }

            if (result.messages) {
                if (isLocalMessages) {
                    $(messages_container).innerHTML = result.messages;
                    new Effect.ScrollTo(messages_container);
                    setTimeout(nzHeaderHide, 1100);
                } else {
                    mainMessages.setMessagesBody(result.messages);
                }
            }
            nzHideLoading();

            var row_id = 'row_data_' + index;
            var row;
            if (dashlet_id) {
                row = $$('#' + formEl.parentNode.id + ' .' + row_id);
                row = row[0];
            } else {
                row = $(row_id);
            }
            if (action == 'edit') {
                addClass(row, 't_selected_row_for_edit');
            } else {
                removeClass(row, 't_selected_row_for_edit');
            }

            if (action == 'save' && !minitask_id) {
                addClass(row, 'normal');
            }

            try {
                row.innerHTML = result.minitask;
            } catch (e) {
                // Ueber hack for IE
                // TODO look for a cleaner way to execute if any
                for (var t = row.childNodes.length - 1; t >= 0; t--) {
                    row.removeChild(row.childNodes[t]);
                }

                var div = document.createElement('DIV');
                div.innerHTML = '<table><tr>' + result.minitask + '</tr></table>';

                // Get the td-s from the table in the div
                var tdElements = div.getElementsByTagName('TD');
                var t_length = tdElements.length;
                for (var t = 0; t < t_length; t++) {
                    row.appendChild(tdElements[0]);
                }

                div.parentNode.removeChild(div);
            }

            if (!minitask_id && row.parentNode.rows[row.rowIndex + 1].cells[0].className.match(/error/)) {
                row.parentNode.rows[row.rowIndex + 1].style.display = 'none';
            }

            var x = row.getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
               eval(x[i].text);
            }

            if (action == 'setstatus') {
                lb.deactivate();
            }

            nzHideLoading();
            return true;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=minitasks&minitasks=ajax_' + action + '&index=' + index + '&real_module=' + real_module + '&real_action=' + real_action;
    if (minitask_id) {
        url += '&id=' + minitask_id;
    }
    if (status) {
        url += '&status=' + status;
    }
    if (dashlet_id) {
        url += '&dashlet_id=' + dashlet_id;
    }

    new Ajax.Request(url, opt);

}

/**
 * Displays severity slider.
 *
 * @param element - image button element
 * @param real_action - current action
 * @param minitask_id - id of minitask
 */
function showSeveritySlider(element, real_action, minitask_id) {
    const img_class_name = element.className.replace(/.*(^|\s)(img_severity_\d+(_\d+)?)($|\s).*/, '$2');
    const index = parseInt(img_class_name.replace(/[a-zA-z_]+_(\d+)(_(\d+))?$/, '$1'));
    const dashlet_id = img_class_name.replace(/[a-zA-z_]+_(\d+)(_(\d+))?$/, '$3');

    const row_id = 'row_data_' + index;
    let row;
    if (dashlet_id) {
        row = document.querySelector('.' + img_class_name).closest('tr');
    } else {
        row = $(row_id);
    }

    const old_severity = getSeverityClassName(row);
    let slider_created = false;
    const track_id = img_class_name.replace(/img_severity/, 'track');
    const slider_tracks = document.querySelectorAll('div.severity_track');
    for (var i = 0; i < slider_tracks.length; i++) {
        if (slider_tracks[i].id === track_id) {
            slider_created = true;
            slider_tracks[i].style.visibility = 'visible';
            for (var c = 0; c < slider_tracks[i].children.length; c++) {
                if (slider_tracks[i].children[c].className.match(/severity_title/)) {
                    const title_div_2 = slider_tracks[i].children[c];
                    positionSeverityTitle(slider_tracks[i], title_div_2, old_severity);
                    title_div_2.innerHTML = i18n['labels']['severity_' + severity_levels[old_severity]];
                    break;
                }
            }
            element.style.visibility = 'hidden';
        } else {
            hideSeveritySlider(slider_tracks[i]);
            //slider_tracks[i].onmouseout = '';
            //slider_tracks[i].removeAttribute('onMouseOut');
        }
    }

    if (!slider_created && old_severity !== '') {
        const handle_id = track_id.replace(/track/, 'handle');
        const track_div = document.createElement('DIV');
        track_div.setAttribute('id', track_id);
        track_div.classList.add('severity_track');
        const handle_div = document.createElement('DIV');
        handle_div.setAttribute('id',handle_id);
        handle_div.classList.add('severity_handle');
        track_div.appendChild(handle_div);
        const title_div = document.createElement('DIV');
        title_div.innerHTML = i18n['labels']['severity_' + severity_levels[old_severity]];
        title_div.classList.add('severity_title');
        track_div.appendChild(title_div);
        element.closest('td').appendChild(track_div);

        positionSeverityTitle(track_div, title_div, old_severity);

        new Control.Slider(handle_id, track_id, {
                                            axis: 'vertical',
                                            sliderValue: old_severity,
                                            values: [0,1,2,3,4],
                                            range: $R(4,0),
                                            step: -1,
                                            onChange: function(value) {
                                                const slider_track = document.getElementById(track_id);
                                                hideSeveritySlider(slider_track);
                                                updateMinitaskSeverity(element, real_action, minitask_id, value);
                                            },
                                            onSlide: function(value) {
                                                setSeverityClassName(row, value);
                                                const slider_track = document.getElementById(track_id);
                                                setSeverityTitle(slider_track, value);
                                            }
                                        });

        element.style.visibility = 'hidden';
    }

    return true;
}

function positionSeverityTitle(trackElement, titleElement, value) {
    const numOfOptions = 5;
    value = parseInt(value);
    const trackSize = Math.round(trackElement.getBoundingClientRect().height);
    const pointerSize = Math.round(titleElement.getBoundingClientRect().height);
    const optionSize = trackSize / numOfOptions;
    titleElement.style.top = Math.round((numOfOptions - 1 - value) * optionSize + optionSize / 2 - pointerSize / 2) + 'px';
}

/**
 * Hides slider and displays slider image button.
 *
 * @param slider_track - track div of slider
 */
function hideSeveritySlider(slider_track) {
    if (slider_track.style.visibility != 'hidden') {
        slider_track.style.visibility = 'hidden';
        var img_severity = $$('.' + slider_track.id.replace(/track/, 'img_severity'));
        if (img_severity) {
            img_severity[0].style.visibility = 'visible';
        }
    }
}

/**
 * Severity values
 */
var severity_levels = ['verylight', 'light', 'normal', 'heavy', 'veryheavy'];

/**
 * Gets severity of mini task from table row style.
 *
 * @param element - element having severity as class
 * @return {String} - severity
 */
function getSeverityClassName(element) {
    var severity = '';
    var regex = new RegExp('(^|\\s+)(' + severity_levels.join('|') + ')($|\\s+)', 'i');
    var matches = element.className.match(regex);
    if (matches) {
        severity = matches[2];
        severity = severity_levels.indexOf(severity);
    }
    return severity.toString();
}

/**
 * Update style of mini task table row according to severity level.
 *
 * @param row - table row
 * @param value - index of severity level
 */
function setSeverityClassName(row, value) {
    var new_severity = severity_levels[value];
    var old_classes = row.className.split(' ');
    for (var i = 0; i < old_classes.length; i++) {
        if (severity_levels.indexOf(old_classes[i]) >= 0) {
            removeClass(row, old_classes[i]);
        }
    }
    addClass(row, new_severity);
}

/**
 * Update text and position of slider title.
 *
 * @param slider_track - track div of slider
 * @param value - index of severity level
 * @return {Boolean}
 */
function setSeverityTitle(slider_track, value) {
    for (var c = 0; c < slider_track.children.length; c++) {
        if (slider_track.children[c].className.match(/severity_title/)) {
            var title_div = slider_track.children[c];
            title_div.style.top = ((4 - value) * 16 - 6) + 'px';
            title_div.innerHTML = i18n['labels']['severity_' + severity_levels[value]];
            break;
        }
    }
    return true;
}

/**
 * AJAX functionality that updates severity of mini task
 *
 * @param element - element that submits form
 * @param real_action - current action
 * @param minitask_id - id of minitask
 * @param value - new severity
 */
function updateMinitaskSeverity(element, real_action, minitask_id, value) {
    var form = element.closest('form');
    var img_class_name = element.className.replace(/.*(^|\s)(img_severity_\d+(_\d+)?)($|\s).*/, '$2');
    var index = parseInt(img_class_name.replace(/[a-zA-z_]+_(\d+)(_(\d+))?$/, '$1'));
    var dashlet_id = img_class_name.replace(/[a-zA-z_]+_(\d+)(_(\d+))?$/, '$3');

    var row_id = 'row_data_' + index;
    var row;
    if (dashlet_id) {
        row = $$('.' + img_class_name)[0].parentNode.parentNode;
    } else {
        row = $(row_id);
    }

    var old_severity = severity_levels[getSeverityClassName(element)];
    var new_severity = severity_levels[value];

    if (old_severity == new_severity) {
        return;
    }

    nzShowLoading()

    var opt = {
        method: 'post',
        asynchronous: false,
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            if (!checkAjaxResponse(t.responseText)) {
                nzHideLoading();
                return false;
            }
            var result = t.responseText;
            eval('result = ' + result + ';');

            const isLocalMessages = real_action === 'communications' || dashlet_id;

            var messages_container = isLocalMessages ?
                                     'communication_messages_container' + dashlet_id :
                                     'messages_container';
            const mainMessages = MainMessages.getInstance();
            if (result.errors) {
                if (isLocalMessages) {
                    $(messages_container).innerHTML = result.errors;
                    new Effect.ScrollTo(messages_container);
                    setTimeout(nzHeaderHide, 1100);
                } else {
                    mainMessages.setMessagesBody(result.errors);
                }
                nzHideLoading();
                return false;
            }

            if (result.messages) {
                if (isLocalMessages) {
                    $(messages_container).innerHTML = result.messages;
                    new Effect.ScrollTo(messages_container);
                    setTimeout(nzHeaderHide, 1100);

                } else {
                    mainMessages.setMessagesBody(result.messages);
                }
            }
            nzHeaderHide();

            removeClass(row, old_severity);
            addClass(row, new_severity);

            removeClass(element, old_severity);
            addClass(element, new_severity);

            nzHideLoading();
            return true;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=minitasks&minitasks=ajax_severity&id=' + minitask_id + '&severity=' + new_severity;

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to show form for changing status
 */
function changeMinitaskStatus(element_class, real_module, real_action, model_id, status) {
    element_class = element_class.split(' ');
    for (var i = 0; i < element_class.length; i++) {
        if (element_class[i].match(/^img_(finished|failed)/)) {
            element_class = element_class[i];
            break;
        }
    }

    var pos = getMousePosition();

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            var result = t.responseText;
            Effect.Fade('loading');
            if (!checkAjaxResponse(result)) {
                return;
            }
            if (result) {
                //show the status layer in a lightbox
                lb = new lightbox({
                    content: result,
                    title: i18n['labels']['status_change'],
                    width: '250px',
                    xPos: pos[0] - 260,
                    yPos: pos[1] - 165,
                    onActivate: function() {
                        var confirm_button = $$('#' + this.params.uniqid + ' button')[0];
                        if (confirm_button) {
                            confirm_button.focus();
                        }
                    }
                });
                lb.activate();
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=minitasks&minitasks=ajax_status&id=' + model_id + '&status=' + status + '&element_class=' + element_class + '&real_module=' + real_module + '&real_action=' + real_action;

    new Ajax.Request(url, opt);
}
