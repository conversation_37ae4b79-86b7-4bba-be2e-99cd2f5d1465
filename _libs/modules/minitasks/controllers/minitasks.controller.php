<?php

class Minitasks_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Minitask';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Minitasks';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'myassigned', 'myrecords', 'myfinished', 'myfailed', 'add'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array();

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array();

    /**
     * Action definitions for the up right menu
     */
    public $actionDefinitionsUpRight = array();

    /**
     * Action definitions with filtering of lists
     */
    public $actionFilterDefinitions = array(
        'myassigned', 'myrecords', 'myfinished', 'myfailed'
    );

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'myassigned', 'myrecords', 'myfinished', 'myfailed'
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'multistatus':
            $this->_multiStatus();
            break;
        case 'ajax_save':
            $this->_save();
            break;
        case 'ajax_edit':
            $this->_edit();
            break;
        case 'ajax_status':
            $this->_getStatus();
            break;
        case 'ajax_setstatus':
            $this->_status();
            break;
        case 'ajax_severity':
            $this->_severity();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'search':
            $this->_search();
            break;
        case 'list':
        case 'myassigned':
        case 'myrecords':
        case 'myfinished':
        case 'myfailed':
        default:
            $list_view = $this->action;
            $list_actions_regexp = '#^(list|myassigned|myrecords|myfinished|myfailed)$#';
            // get action from personal settings only if no action or invalid action specified in request
            if (!$this->registry['request']->get($this->registry['action_param']) ||
            !preg_match($list_actions_regexp, $list_view)) {
                $settings = $this->registry['currentUser']->getPersonalSettings('minitasks');
                $list_view = (!empty($settings['list_view']) &&
                    preg_match($list_actions_regexp, $settings['list_view'])) ?
                    $settings['list_view'] : 'list';
                $this->setAction($list_view);
            }
            $method = '_' . $list_view;
            $this->$method();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * my assigned models
     */
    private function _myassigned() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * my models
     */
    private function _myrecords() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * my responsible models
     */
    private function _myfinished() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * my observer models
     */
    private function _myfailed() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * Status of model
     */
    private function _getStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('id');
        $filters = array('where' => array('m.id = ' . $id, 'm.status = "opened"'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => true);
        $minitask = Minitasks::searchOne($this->registry, $filters);

        if ($minitask) {
            $minitask->set('status', $request->get('status'), true);
            $setstatus['options'] = array('label' => $this->i18n('minitasks_status_btn'), 'form_method' => 'post');
            /*$setstatus['model_id'] = $minitask->get('id');
            $setstatus['module'] = 'minitasks';
            $setstatus['action'] = 'setstatus';
            $setstatus['module_param'] = $this->registry['module_param'];*/
            $setstatus['show_form'] = 1;
            $this->viewer = new Viewer($this->registry);
            $this->viewer->setFrameset(PH_MODULES_DIR . 'minitasks/templates/_action_status.html');
            $this->viewer->data['minitask'] = $minitask;
            $this->viewer->data['available_action'] = $setstatus;
            $this->viewer->data['element_class'] = $request->get('element_class');
            $this->viewer->data['real_module'] = $request->get('real_module');
            $this->viewer->data['real_action'] = $request->get('real_action');
            $this->viewer->display();
        }

        exit;
    }

    /**
     * Change status of model
     */
    private function _status() {
        $request = &$this->registry['request'];

        $index = $request->get('index');
        //get the requested model ID
        $id = $request->get('id');

        if ($id > 0) {
            $filters = array('where' => array('m.id = ' . $id, 'm.status = "opened"'),
                             'model_lang' => $request->get('model_lang'));
            $minitask = Minitasks::searchOne($this->registry, $filters);

            if ($minitask) {
                //check access and ownership of the model
                if (!$this->checkAccessOwnership($minitask, false, 'setstatus')) {
                    unset($minitask);
                }
            }
        }

        $this->registry['db']->StartTrans();

        $this->registry['messages']->flush();

        if (!empty($minitask)) {
            $old_minitask = clone $minitask;
            $old_minitask->sanitize();

            $minitask->set('status', $request->get('status'), true);
            $minitask->set('comment', $request->get('comment'), true);
            if ($minitask->setStatus()) {
                $filters = array('where' => array('m.id = ' . $id, 'm.status IS NOT NULL'),
                                 'model_lang' => $request->get('model_lang'),
                                 'sanitize' => true);
                $minitask = Minitasks::searchOne($this->registry, $filters);

                $model = $minitask->get('model') && $minitask->get('model_id') ? $minitask->get('model') : 'Customer';
                $model_id = $minitask->get('model') && $minitask->get('model_id') ? $minitask->get('model_id') : $minitask->get('customer');
                $id = $minitask->get('id');

                $audit_parent = '';
                if ($model && $model_id) {
                    // write history to record model
                    $record_model_factory = General::singular2plural($model);
                    $record_module = strtolower($record_model_factory);
                    require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.factory.php';
                    require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.history.php';
                    require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.audit.php';
                    $alias = $record_model_factory::getAlias($record_module, $record_module);
                    $record_model = $record_model_factory::searchOne(
                        $this->registry,
                        array('where' => array($alias . '.id=' . $model_id))
                    );

                    $old_record_model = clone $record_model;
                    $old_record_model->set('minitask', $old_minitask, true);
                    $old_record_model->sanitize();
                    $record_model->set('minitask', $minitask, true);

                    $record_model_factory .= '_History';
                    $audit_parent = $record_model_factory::saveData(
                        $this->registry,
                        array(
                            'model' => $record_model,
                            'new_model' => $record_model,
                            'action_type' => 'status_minitask',
                            'old_model' => $old_record_model
                        ));
                    unset($record_model);
                    unset($old_record_model);
                }

                // send notification for status change
                $this->sendNotification('minitask_status', $minitask, $audit_parent);

                // display minitask in view mode
                $minitaskViewer = new Viewer($this->registry);
                $minitaskViewer->setFrameset('_minitasks_view.html');
                $minitaskViewer->data['minitask'] = $minitask;
                $minitaskViewer->data['row_index'] = $index;
                // specify where user is at
                $minitaskViewer->data['real_module'] = $request->get('real_module');
                $minitaskViewer->data['real_controller'] = $request->get('real_module');
                $minitaskViewer->data['real_action'] = $request->get('real_action');

                //get columns to display in view template when in dashlet
                if ($request->get('real_action') == 'dashlet' && $request->get('dashlet_id')) {
                    $dashlets_filters = array('where' => array('d.active = 1',
                                                               'd.id = ' . $request->get('dashlet_id')),
                                              'sanitize' => true);
                    require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
                    $dashlet = Dashlets::searchOne($this->registry, $dashlets_filters);

                    if ($dashlet) {
                        $settings = $dashlet->get('settings');
                        foreach ($settings['columns'] as $key => $column) {
                            if (!isset($settings['visible'][$key])) {
                                unset($settings['columns'][$key]);
                            }
                        }
                        $minitaskViewer->data['columns'] = $settings['columns'];
                        unset($dashlet);
                    }
                }

                $this->registry['messages']->setMessage($this->i18n('message_minitasks_status_success'), '', -1);

                if (router::isJSONRequired()) {
                    $result = array('models' => $minitaskViewer->data['minitask']);
                    $result['messages'] = array_values($this->registry['messages']->getMessages());
                } else {
                    $result['minitask'] = $minitaskViewer->fetch();

                    $viewer = new Viewer($this->registry);
                    $viewer->setFrameset('message.html');
                    $viewer->data['items'] = $this->registry['messages']->getMessages();
                    $viewer->data['display'] = 'message';
                    $result['messages'] = $viewer->fetch();
                }

            } else {
                // some error occurred
                $this->registry['messages']->setError($this->i18n('error_minitasks_status_failed'), '', -1);
                $errors = $this->registry['messages']->getErrors();
                if (router::isJSONRequired()) {
                    $result['errors'] = array_values($errors);
                } else {
                    $viewer = new Viewer($this->registry);
                    $viewer->setFrameset('message.html');
                    $viewer->data['items'] = $errors;
                    $viewer->data['display'] = 'error';
                    $result['errors'] = $viewer->fetch();
                }
            }
        } else {
            // show error no such model
            $result = array();
            $this->registry['messages']->setError($this->i18n('error_no_such_minitask'));
            $errors = $this->registry['messages']->getErrors();
            if (router::isJSONRequired()) {
                $result['errors'] = array_values($errors);
            } else {
                $viewer = new Viewer($this->registry);
                $viewer->setFrameset('message.html');
                $viewer->data['items'] = $errors;
                $viewer->data['display'] = 'error';
                $result['errors'] = $viewer->fetch();
            }
        }

        $this->registry['db']->CompleteTrans();

        echo json_encode($result);
        exit;
    }

    /**
     * Switch mini task into edit mode
     */
    private function _edit() {
        $request = &$this->registry['request'];

        $index = $request->get('index');
        //get the requested model ID
        $id = $request->get('id');

        if ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('m.id = ' . $id),
                             'sanitize' => true,
                             'model_lang' => $request->get('model_lang'));
            $minitask = Minitasks::searchOne($this->registry, $filters);

            if ($minitask) {
                //check access and ownership of the model
                if (!$this->checkAccessOwnership($minitask, false, 'edit')) {
                    unset($minitask);
                }
            }
        }

        if (!empty($minitask)) {
            // display minitask in edit mode
            $minitaskViewer = new Viewer($this->registry);
            $minitaskViewer->setFrameset('_minitasks_edit.html');
            $minitaskViewer->data['minitask'] = $minitask;
            $minitaskViewer->data['row_index'] = $index;

            require_once PH_MODULES_DIR . 'minitasks/models/minitasks.dropdown.php';
            $records_model_types = Minitasks_Dropdown::getRecordsModelTypes(array($this->registry));
            $minitaskViewer->data['records_model_types'] = $records_model_types;

            $minitaskViewer->data['real_module'] = $request->get('real_module');
            $minitaskViewer->data['real_controller'] = $request->get('real_module');
            $minitaskViewer->data['real_action'] = $request->get('real_action');

            $minitaskViewer->data['user_autocomplete'] = array (
                                                            'type'         => 'users',
                                                            'clear'        => 1,
                                                            'buttons_hide' => 'search',
                                                            'suggestions'  => '<firstname> <lastname>',
                                                            'fill_options' => array('$assigned_to => <id>',
                                                                                    '$assigned_to_autocomplete => <firstname> <lastname>',
                                                                                    '$assigned_to_oldvalue => <firstname> <lastname>'),
                                                            'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'users', 'users', 'ajax_select')
                                                            );
            $minitaskViewer->data['customer_autocomplete'] = array (
                                                               'type' => 'customers',
                                                               'clear' => 1,
                                                               'buttons_hide' => 'search',
                                                               'suggestions'  => '<name> <lastname>',
                                                               'fill_options' => array('$customer => <id>',
                                                                                       '$customer_autocomplete => <name> <lastname>',
                                                                                       '$customer_oldvalue => <name> <lastname>'),
                                                               'url' => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'customers', 'customers', 'ajax_select')
                                                             );

            //prepare filters for customers autocompleter
            $cstm_types = Minitasks::getRelatedCustomersTypes($this->registry);
            $cstm_types = $cstm_types ? implode(', ', $cstm_types) : '0';
            $minitaskViewer->data['customer_autocomplete']['filters'] = array('<type>' => $cstm_types);

            //get columns to display in edit template when in dashlet
            if ($request->get('real_action') == 'dashlet' && $request->get('dashlet_id')) {
                $dashlets_filters = array('where' => array('d.active = 1',
                                                           'd.id = ' . $request->get('dashlet_id')),
                                          'sanitize' => true);
                require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
                $dashlet = Dashlets::searchOne($this->registry, $dashlets_filters);

                if ($dashlet) {
                    $settings = $dashlet->get('settings');
                    foreach ($settings['columns'] as $key => $column) {
                        if (!isset($settings['visible'][$key])) {
                            unset($settings['columns'][$key]);
                        }
                    }
                    $minitaskViewer->data['columns'] = $settings['columns'];
                    $minitaskViewer->data['dashlet_id'] = $dashlet->get('id');
                    unset($dashlet);
                }
            }

            if (router::isJSONRequired()) {
                $result = array('models' => $minitaskViewer->data['minitask']);
            } else {
                $result['minitask'] = $minitaskViewer->fetch();
            }
        } else {
            // show error no such model
            $result = array();
            $this->registry['messages']->setError($this->i18n('error_no_such_minitask'));
            $errors = $this->registry['messages']->getErrors();
            if (router::isJSONRequired()) {
                $result['errors'] = array_values($errors);
            } else {
                $viewer = new Viewer($this->registry);
                $viewer->setFrameset('message.html');
                $viewer->data['items'] = $errors;
                $viewer->data['display'] = 'error';
                $result['errors'] = $viewer->fetch();
            }
        }

        echo json_encode($result);
        exit;
    }

    /**
     * Save (add or edit) mini task
     */
    public function _save() {
        $request = &$this->registry['request'];

        $index = $request->get('index');
        $mode = $request->isRequested('id') ? 'edit' : 'add';

        $minitask = Minitasks::buildModelIndex($this->registry, $index);

        // check access and ownership of the model
        if (!$this->checkAccessOwnership($minitask, false, $mode)) {
            unset($minitask);
        }
        if (!empty($minitask)) {
            // get minitask from db to check if it still can be edited
            // (i.e. it could have been finished in the meantime)
            if ($mode == 'edit') {
                $filters = array('where' => array('m.id=' . $minitask->get('id')),
                                 'sanitize' => true,
                                 'model_lang' => $this->registry->get('lang'));
                $old_minitask = Minitasks::searchOne($this->registry, $filters);
                if (!$old_minitask) {
                    unset($minitask);
                } else {
                    $old_minitask->set('severity_name', $this->i18n('minitasks_' . $old_minitask->get('severity')), true);
                }
            }

            // if record has "status" property, check 1) if record is finished, 2) if type of record
            // requires mini tasks to be completed before finishing record.
            // if both are true, do not allow adding new mini task to record.
            if (!empty($minitask) && preg_match('#^(Document|Project|Task|Contract)$#i', $minitask->get('model')) &&
            $minitask->get('model_id') && ($mode == 'add' || !$old_minitask->get('model_id'))) {
                $model = $minitask->get('model');
                $model_id = $minitask->get('model_id');

                $record_model_factory = General::singular2plural($model);
                $record_module = strtolower($record_model_factory);
                require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.factory.php';

                $alias = $record_model_factory::getAlias($record_module, $record_module);
                $record_model = $record_model_factory::searchOne(
                    $this->registry,
                    array(
                        'where' => array($alias . '.id=' . $model_id),
                        'sanitize' => true
                    )
                );

                if ($record_model->get('requires_completed_minitasks') &&
                    preg_match('#^(finished|closed)$#', $record_model->get('status')) &&
                    ($record_model->modelName != 'Project' || $record_model->modelName == 'Project' &&
                    ($record_model->get('finished') === '0' || $record_model->get('finished') === '1'))) {
                    $this->registry['messages']->setError($this->i18n('error_minitasks_completed_record'));
                    unset($minitask);
                }
            }
        }

        $this->registry['db']->StartTrans();

        $result = array();

        if (!empty($minitask)) {
            if ($minitask->save()) {
                $id = $minitask->get('id');

                $filters = array('where' => array('m.id=' . $id),
                                 'sanitize' => true,
                                 'model_lang' => $this->registry->get('lang'));
                $minitask = Minitasks::searchOne($this->registry, $filters);
                $minitask->set('severity_name', $this->i18n('minitasks_' . $minitask->get('severity')), true);

                $model = $minitask->get('model') && $minitask->get('model_id') ? $minitask->get('model') : 'Customer';
                $model_id = $minitask->get('model') && $minitask->get('model_id') ? $minitask->get('model_id') : $minitask->get('customer');

                $action_type = $mode . '_minitask';

                $audit_parent = '';
                if ($model && $model_id) {
                    // write history to record model
                    $record_model_factory = General::singular2plural($model);
                    $record_module = strtolower($record_model_factory);
                    require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.factory.php';
                    require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.history.php';
                    require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.audit.php';

                    $alias = $record_model_factory::getAlias($record_module, $record_module);
                    $record_model = $record_model_factory::searchOne(
                        $this->registry,
                        array('where' => array($alias . '.id=' . $model_id))
                    );

                    $old_record_model = clone $record_model;
                    if ($mode == 'edit') {
                        $old_record_model->set('minitask', $old_minitask, true);
                    }
                    $old_record_model->sanitize();
                    $record_model->set('minitask', $minitask, true);

                    $record_model_factory .= '_History';
                    $audit_parent = $record_model_factory::saveData(
                        $this->registry,
                        array(
                            'model' => $record_model,
                            'new_model' => $record_model,
                            'action_type' => $action_type,
                            'old_model' => $old_record_model
                        ));
                    unset($record_model);
                    unset($old_record_model);
                }

                // notify new assignee for assignment
                if ($mode == 'add') {
                    $this->sendNotification('minitask_assign', $minitask);
                } else {
                    // send notification for modifications
                    $this->sendNotification('minitask_edit', $minitask, $audit_parent, $old_minitask);

                    if ($old_minitask->get('assigned_to') != $minitask->get('assigned_to')) {
                        // notify new assignee for assignment
                        $this->sendNotification('minitask_assign', $minitask);
                        // notify previous assignee for removal of assignment
                        $this->sendNotification('minitask_assignment_removal', $old_minitask);
                    }
                }

                // display minitask in view mode
                $minitaskViewer = new Viewer($this->registry);
                $minitaskViewer->setFrameset('_minitasks_view.html');
                $minitaskViewer->data['minitask'] = $minitask;
                $minitaskViewer->data['row_index'] = $index;

                $minitaskViewer->data['real_module'] = $request->get('real_module');
                $minitaskViewer->data['real_controller'] = $request->get('real_module');
                $minitaskViewer->data['real_action'] = $request->get('real_action');

                // get columns to display in view template when in dashlet
                if ($request->get('real_action') == 'dashlet' && $request->get('dashlet_id')) {
                    $dashlets_filters = array('where' => array('d.active = 1',
                                                               'd.id = ' . $request->get('dashlet_id')),
                                              'sanitize' => true);
                    require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
                    $dashlet = Dashlets::searchOne($this->registry, $dashlets_filters);

                    if ($dashlet) {
                        $settings = $dashlet->get('settings');
                        foreach ($settings['columns'] as $key => $column) {
                            if (!isset($settings['visible'][$key])) {
                                unset($settings['columns'][$key]);
                            }
                        }
                        $minitaskViewer->data['columns'] = $settings['columns'];
                        $minitaskViewer->data['dashlet_id'] = $dashlet->get('id');
                        unset($dashlet);
                    }
                }

                $this->registry['messages']->setMessage($this->i18n('message_minitasks_' . $mode . '_success'), '', -1);

                if (router::isJSONRequired()) {
                    $result = array('models' => $minitaskViewer->data['minitask']);
                    $result['messages'] = array_values($this->registry['messages']->getMessages());
                } else {
                    $result['minitask'] = $minitaskViewer->fetch();

                    $viewer = new Viewer($this->registry);
                    $viewer->setFrameset('message.html');
                    $viewer->data['items'] = $this->registry['messages']->getMessages();
                    $viewer->data['display'] = 'message';
                    $result['messages'] = $viewer->fetch();
                }

                $this->actionCompleted = true;
            } else {
                // show error message
                $this->registry['messages']->setError($this->i18n('error_minitasks_' . $mode . '_failed'), '', -1);
                $errors = $this->registry['messages']->getErrors();
                if (router::isJSONRequired()) {
                    $result['errors'] = array_values($errors);
                } else {
                    $viewer = new Viewer($this->registry);
                    $viewer->setFrameset('message.html');
                    $viewer->data['items'] = $errors;
                    $viewer->data['display'] = 'error';
                    $result['errors'] = $viewer->fetch();
                }
            }
        } else {
            // show error message
            $result = array();
            $this->registry['messages']->setError($this->i18n('error_minitasks_' . $mode . '_failed'), '', -1);
            $errors = $this->registry['messages']->getErrors();
            if (router::isJSONRequired()) {
                $result['errors'] = array_values($errors);
            } else {
                $viewer = new Viewer($this->registry);
                $viewer->setFrameset('message.html');
                $viewer->data['items'] = $errors;
                $viewer->data['display'] = 'error';
                $result['errors'] = $viewer->fetch();
            }
        }

        $this->registry['db']->CompleteTrans();
        // used for createModels automation
        if ($this->registry['stop_redirects']) {
            if ($this->actionCompleted) {
                $this->registry->set('minitask', $minitask, true);
                return true;
            }
            return false;
        }
        echo json_encode($result);
        exit;
    }

    /**
     * Update severity of mini task
     */
    private function _severity() {
        $request = &$this->registry['request'];

        //$index = $request->get('index');
        //get the requested model ID
        $id = $request->get('id');

        if ($id > 0) {
            $filters = array('where' => array('m.id = ' . $id, 'm.status = "opened"'),
                             'model_lang' => $request->get('model_lang'));
            $minitask = Minitasks::searchOne($this->registry, $filters);

            if ($minitask) {
                //check access and ownership of the model
                if (!$this->checkAccessOwnership($minitask, false, 'edit')) {
                    unset($minitask);
                }
            }
        }

        $this->registry['db']->StartTrans();

        $this->registry['messages']->flush();

        $result = array();
        if (!empty($minitask)) {
            $error_severity = false;
            $severity_levels = array ('verylight', 'light', 'normal', 'heavy', 'veryheavy');

            if (!$request->isRequested('severity') || array_search($request->get('severity'), $severity_levels) === false ||
            array_search($minitask->get('severity'), $severity_levels) === false) {
                // invalid severity parameter or invalid severity change
                $error_severity = true;
            } else {
                $old_minitask = clone $minitask;
                $old_minitask->sanitize();
                $old_minitask->set('severity_name', $this->i18n('minitasks_' . $old_minitask->get('severity')), true);

                $minitask->set('severity', $request->get('severity'), true);

                if ($old_minitask->get('severity') == $request->get('severity')) {
                    $result = 1;
                } elseif ($minitask->save()) {
                    $id = $minitask->get('id');

                    $filters = array('where' => array('m.id=' . $id),
                                     'sanitize' => true,
                                     'model_lang' => $this->registry->get('lang'));
                    $minitask = Minitasks::searchOne($this->registry, $filters);
                    $minitask->set('severity_name', $this->i18n('minitasks_' . $minitask->get('severity')), true);

                    $model = $minitask->get('model') && $minitask->get('model_id') ? $minitask->get('model') : 'Customer';
                    $model_id = $minitask->get('model') && $minitask->get('model_id') ? $minitask->get('model_id') : $minitask->get('customer');

                    $audit_parent = '';
                    if ($model && $model_id) {
                        // write history to record model
                        $record_model_factory = General::singular2plural($model);
                        $record_module = strtolower($record_model_factory);
                        require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.factory.php';
                        require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.history.php';
                        require_once PH_MODULES_DIR . $record_module . '/models/' . $record_module . '.audit.php';

                        $alias = $record_model_factory::getAlias($record_module, $record_module);
                        $record_model = $record_model_factory::searchOne(
                            $this->registry,
                            array('where' => array($alias . '.id=' . $model_id))
                        );

                        $old_record_model = clone $record_model;
                        $old_record_model->set('minitask', $old_minitask, true);
                        $old_record_model->sanitize();
                        $record_model->set('minitask', $minitask, true);

                        $record_model_factory .= '_History';
                        $audit_parent = $record_model_factory::saveData(
                            $this->registry,
                            array(
                                'model' => $record_model,
                                'new_model' => $record_model,
                                'action_type' => 'edit_minitask',
                                'old_model' => $old_record_model
                            ));
                        unset($record_model);
                        unset($old_record_model);
                    }

                    // send notification for modifications
                    $this->sendNotification('minitask_edit', $minitask, $audit_parent, $old_minitask);

                    $this->registry['messages']->setMessage($this->i18n('message_minitasks_severity_success'), '', -1);
                    if (router::isJSONRequired()) {
                        $result['messages'] = array_values($this->registry['messages']->getMessages());
                    } else {
                        $viewer = new Viewer($this->registry);
                        $viewer->setFrameset('message.html');
                        $viewer->data['items'] = $this->registry['messages']->getMessages();
                        $viewer->data['display'] = 'message';
                        $result['messages'] = $viewer->fetch();
                    }
                } else {
                    // severity change unsuccessful
                    $error_severity = true;
                }
            }

            if ($error_severity) {
                $this->registry['messages']->setError($this->i18n('error_minitasks_severity_failed'));
                $errors = $this->registry['messages']->getErrors();
                if (router::isJSONRequired()) {
                    $result['errors'] = array_values($errors);
                } else {
                    $viewer = new Viewer($this->registry);
                    $viewer->setFrameset('message.html');
                    $viewer->data['items'] = $errors;
                    $viewer->data['display'] = 'error';
                    $result['errors'] = $viewer->fetch();
                }
            }
        } else {
            // show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_minitask'));
            $errors = $this->registry['messages']->getErrors();
            if (router::isJSONRequired()) {
                $result['errors'] = array_values($errors);
            } else {
                $viewer = new Viewer($this->registry);
                $viewer->setFrameset('message.html');
                $viewer->data['items'] = $errors;
                $viewer->data['display'] = 'error';
                $result['errors'] = $viewer->fetch();
            }

        }

        $this->registry['db']->CompleteTrans();

        echo json_encode($result);
        exit;
    }

    /**
     * Change status of multiple models
     */
    private function _multiStatus($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids, 'setstatus');

        //check for the same type models
        $filters = array('where' => array('m.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $this->registry->get('lang'),
                         'sanitize' => false);
        $minitasks = Minitasks::search($this->registry, $filters);

        //if no minitasks or checked deleted minitasks
        if (empty($minitasks) || count($ids) != count($minitasks)) {
            $this->registry['messages']->setError($this->i18n('error_no_minitasks'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $result = Minitasks::multiStatus($this->registry, $this);
        if ($result) {
            $this->actionCompleted = true;
            if ($result > 0) {
                $this->registry['messages']->setMessage($this->i18n('message_minitasks_multistatus_success',
                    array(($result === true ? '' : $this->i18n('num_of_selected_items', array($result)) . ' ') . $this->i18n('minitasks'))), '', -1);
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_minitasks_change_status_not_all'));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_minitasks_multistatus_failed'), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        //check if the model is defined
        //in order to remove some of the actions if necessary

        $actions = parent::getActions($action_defs);

        //change some search actions
        if (isset($actions['search'])) {
            if ($this->action == 'myassigned') {
                $actions['search']['action'] = 'myassigned';
            } elseif ($this->action == 'myrecords') {
                $actions['search']['action'] = 'myrecords';
            } elseif ($this->action == 'myfinished') {
                $actions['search']['action'] = 'myfinished';
            } elseif ($this->action == 'myfailed') {
                $actions['search']['action'] = 'myfailed';
            }
        }

        // list action
        if (isset($actions['list'])) {
            // extend the link for list to clear type sections and types
            if ($this->model && $this->model->get('id')) {

            } else {
                $actions['list']['ajax_no'] = 1;
                $actions['list']['drop_menu'] = true;

                if (isset($actions['myassigned'])) {
                    $actions['list']['options']['myassigned']['img'] = 'tasks_myassigned';
                    $actions['list']['options']['myassigned']['label'] = $this->i18n('minitasks_myassigned');
                    $actions['list']['options']['myassigned']['url'] = $actions['myassigned']['url'];
                    $actions['list']['options']['myassigned']['selected'] = $actions['myassigned']['selected'];
                    unset($actions['myassigned']);
                }
                if (isset($actions['myrecords'])) {
                    $actions['list']['options']['myrecords']['img'] = 'tasks_mytasks';
                    $actions['list']['options']['myrecords']['label'] = $this->i18n('minitasks_myrecords');
                    $actions['list']['options']['myrecords']['url'] = $actions['myrecords']['url'];
                    $actions['list']['options']['myrecords']['selected'] = $actions['myrecords']['selected'];
                    unset($actions['myrecords']);
                }
                if (isset($actions['myfinished'])) {
                    $actions['list']['options']['myfinished']['img'] = 'minitasks_finished';
                    $actions['list']['options']['myfinished']['label'] = $this->i18n('minitasks_myfinished');
                    $actions['list']['options']['myfinished']['url'] = $actions['myfinished']['url'];
                    $actions['list']['options']['myfinished']['selected'] = $actions['myfinished']['selected'];
                    unset($actions['myfinished']);
                }
                if (isset($actions['myfailed'])) {
                    $actions['list']['options']['myfailed']['img'] = 'minitasks_failed';
                    $actions['list']['options']['myfailed']['label'] = $this->i18n('minitasks_myfailed');
                    $actions['list']['options']['myfailed']['url'] = $actions['myfailed']['url'];
                    $actions['list']['options']['myfailed']['selected'] = $actions['myfailed']['selected'];
                    unset($actions['myfailed']);
                }
            }
        } else {
            unset($actions['myassigned']);
            unset($actions['myrecords']);
            unset($actions['myfinished']);
            unset($actions['myfailed']);
        }

        if (isset($actions['add'])) {
            //$actions['add']['options'] = 1;
            //$actions['add']['ajax_no'] = 1;
            $actions['add']['onclick'] = 'insertNewMinitaskRow(this); return false;';
        } else {
            unset($actions['add']);
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }

    /**
     * Send notification on edit, assignment or assignment removal of a mini task
     *
     * @param string $template - template name
     * @param object $new_minitask - Minitask model
     * @param mixed $audit_parent - id of history entry (when history has been saved) or empty string
     * @param object $old_minitask - Old minitask model
     */
    public function sendNotification($template, $new_minitask, $audit_parent = '', $old_minitask = '') {

        $not_users = Users::getUsersNoSend($this->registry, $template);

        $notify_users = array();
        if ($template == 'minitask_edit') {
            $notify_users[] = $new_minitask->get('added_by');
            if ($new_minitask->get('assigned_to') == $old_minitask->get('assigned_to')) {
                $notify_users[] = $new_minitask->get('assigned_to');
            }
        } elseif ($template == 'minitask_status') {
            $notify_users[] = $new_minitask->get('added_by');
            $notify_users[] = $new_minitask->get('assigned_to');
        } else {
            $notify_users[] = $new_minitask->get('assigned_to');
        }

        if (!$new_minitask->shouldSendEmail($template)) {
            return true;
        }

        $query = 'SELECT u2.email AS assignment_email, u2.id,' . "\n" .
                 '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) AS assignment_name ' . "\n" .
                 'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                 '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="' . $new_minitask->get('model_lang') . '")' . "\n" .
                 'WHERE u2.id IN(' . implode(', ', $notify_users) . ') AND u2.active=1' . "\n" .
                 'GROUP BY u2.id';
        $records = $this->registry['db']->GetAll($query);

        // prepare composite field
        $for_record = '';
        if ($new_minitask->get('model') && $new_minitask->get('model_id')) {
            if ($new_minitask->get('record_num')) {
                $for_record .= '[' . $new_minitask->get('record_num') . '] ';
            } elseif ($new_minitask->get('model') == 'Contract') {
                $for_record .= $this->i18n('minitasks_unfinished_contract') . ' ';
            }
            $for_record .= $new_minitask->get('record_name');
        }
        $new_minitask->set('for_record', $for_record, true);

        if ($template == 'minitask_edit') {
            if ($old_minitask && !$audit_parent) {
                // prepare composite field
                $for_record = '';
                if ($old_minitask->get('model') && $old_minitask->get('model_id')) {
                    if ($old_minitask->get('record_num')) {
                        $for_record .= '[' . $old_minitask->get('record_num') . '] ';
                    } elseif ($old_minitask->get('model') == 'Contract') {
                        $for_record .= $this->i18n('minitasks_unfinished_contract') . ' ';
                    }
                    $for_record .= $old_minitask->get('record_name');
                }
                $old_minitask->set('for_record', $for_record, true);
            }

            //prepare audit data
            require_once PH_MODULES_DIR . 'minitasks/viewers/minitasks.audit.viewer.php';
            $configViewer = new Minitasks_Audit_Viewer($this->registry);
            $configViewer->templatesDir = $this->registry['theme']->templatesDir;
            $configViewer->setTemplate('_audit_email.html');

            $configViewer->data['audit'] = $audit_parent;
            if ($new_minitask->get('archive')) {
                $configViewer->data['archive'] = 1;
            }
            $configViewer->data['minitask'] = $new_minitask;
            if (!$audit_parent) {
                $configViewer->data['old_minitask'] = $old_minitask;
            }
            //prepare the title for the audit table
            $audit_title = sprintf($this->i18n('record_changed_by_at'), $new_minitask->get('modified_by_name'), date('d.m.Y, H:i', strtotime($new_minitask->get('modified'))));
            $configViewer->data['audit_title'] = $audit_title;
            $configViewer->prepare();
            $audit_data = $configViewer->fetch();
            // CHECK if there is audit data and compare the settings for each user from personal settings table
            $audit_data = preg_replace('/(\n|\r)/', '', $audit_data);
        }
        // Setting 'document_edit_send_when_audit' is used for all modules, section is 'emails'!!!
        $users_send_always = Users::getUsersSendSettings($this->registry, 'document_edit_send_when_audit', 'emails');

        $sent_to = array();
        foreach ($records as $record) {
            //check if the assignee wants to receive email
            if ($record['assignment_email']) {
                $send = false;
                if (in_array($record['id'], $not_users) || $record['id'] == $this->registry['currentUser']->get('id')) {
                    //the user does not want to receive notifications when the mini task is edited
                    continue;
                }
                if ($template != 'minitask_edit' || !empty($audit_data) || empty($audit_data) && in_array($record['id'], $users_send_always)) {
                    $send = true;
                }

                if ($send) {
                    $mailer = new Mailer($this->registry, $template, $new_minitask);

                    $mailer->placeholder->add('minitask_for_record', $new_minitask->get('for_record'));
                    $mailer->placeholder->add('minitask_assigned_to', $new_minitask->get('assigned_to_name'));
                    $mailer->placeholder->add('minitask_description', nl2br($new_minitask->get('description')));
                    $mailer->placeholder->add('minitask_deadline', ($new_minitask->get('deadline') ? General::strftime('%d.%m.%Y', $new_minitask->get('deadline')) : $this->i18n('minitasks_deadline_none')));
                    $mailer->placeholder->add('minitask_added_by', $new_minitask->get('added_by_name'));
                    $mailer->placeholder->add('customer_name', $new_minitask->get('customer_name'));

                    $model_url = ($new_minitask->get('model') && $new_minitask->get('model_id')) ?
                                 sprintf('%s/index.php?%s=%s&%s=view&view=%d%s',
                                         $this->registry['config']->getParam('crontab', 'base_host'),
                                         $this->registry['module_param'], $new_minitask->get('module'),
                                         ($new_minitask->get('controller') ? $this->registry['controller_param'] . '=' . $new_minitask->get('controller') . '&' . $new_minitask->get('controller') : $new_minitask->get('module')),
                                         $new_minitask->get('model_id'),
                                         ($new_minitask->get('archive') ? '&archive=1' : '')) :
                                 '#';
                    $mailer->placeholder->add('minitask_model_url', $model_url);

                    $customer_url = $new_minitask->get('customer') ?
                                    sprintf('%s/index.php?%s=customers&customers=view&view=%d',
                                            $this->registry['config']->getParam('crontab', 'base_host'),
                                            $this->registry['module_param'], $new_minitask->get('customer')) :
                                    '#';
                    $mailer->placeholder->add('minitask_customer_url', $customer_url);

                    $mailer->placeholder->add('to_email', $record['assignment_email']);
                    $mailer->placeholder->add('user_name', $record['assignment_name']);
                    if ($template == 'minitask_edit') {
                        $mailer->placeholder->add('last_audit', $audit_data);
                    } elseif ($template == 'minitask_status') {
                        $mailer->placeholder->add('minitask_status', $this->i18n('minitasks_status_' . $new_minitask->get('status')));
                        $mailer->placeholder->add('minitask_comment', nl2br($new_minitask->get('comment')));
                    }

                    $mailer->placeholder->add('user_firstname', $this->registry['currentUser']->get('firstname'));
                    $mailer->placeholder->add('user_lastname', $this->registry['currentUser']->get('lastname'));
                    $mailer->template['model_name'] = $new_minitask->modelName;
                    $mailer->template['model_id'] = $new_minitask->get('id');

                    //send email
                    $result = $mailer->send();
                    if (!@in_array($record['assignment_email'], $result['erred'])) {
                        $sent_to[] = $record['assignment_name'];
                    }
                }
            }
        }
        if (count($sent_to)) {
            $notify_for = $this->i18n('minitasks_' . $template . '_notify');
            if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                $this->registry['messages']->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
            } else {
                $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
            }
        }
    }
}

?>
