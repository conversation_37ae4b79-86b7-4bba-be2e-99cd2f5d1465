<?php

class Minitasks_MyFinished_Viewer extends Viewer {
    public $template = 'list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'minitasks.factory.php';

        if (isset($_SERVER['HTTP_REFERER'])) {
            $ref_url = $_SERVER['HTTP_REFERER'];
            $search_filters = $this->registry['session']->get('search_minitask');
            if (strpos($ref_url, 'minitasks=search') || $this->registry['request']->get('search_minitask')) {
                $filters = Minitasks::saveSearchParams($this->registry, array(), 'search_');
            } else {
                $filters = Minitasks::saveSearchParams($this->registry, array(), 'list_');
            }
        } else {
            $filters = Minitasks::saveSearchParams($this->registry, array(), 'list_');
        }
        $filters['where'][] = 'm.status = \'finished\'';

        list($minitasks, $pagination) = Minitasks::pagedSearch($this->registry, $filters);

        require_once PH_MODULES_DIR . 'minitasks/models/minitasks.dropdown.php';

        // prepare predefined deadlines
        $predefined_deadlines = Minitasks_Dropdown::getPredefinedDeadlines(array($this->registry));
        $this->data['predefined_deadlines'] = $predefined_deadlines;

        // prepare model types for records
        $records_model_types = Minitasks_Dropdown::getRecordsModelTypes(array($this->registry));
        $this->data['records_model_types'] = $records_model_types;

        // empty model to be used in templates in add mode
        $currentUser = $this->registry['currentUser'];
        $empty_params = array (
            'assigned_to' => $currentUser->get('id'),
            'assigned_to_code' => $currentUser->get('code'),
            'assigned_to_name' => $currentUser->get('firstname') . ' ' . $currentUser->get('lastname')
        );
        $empty_minitask = new Minitask($this->registry, $empty_params);
        $empty_minitask->sanitize();
        $this->data['empty_minitask'] = $empty_minitask;

        $this->data['minitasks'] = $minitasks;
        $this->data['pagination'] = $pagination;

        //prepare filters for customers autocompleter
        require_once PH_MODULES_DIR . 'minitasks/models/minitasks.factory.php';
        $cstm_types = Minitasks::getRelatedCustomersTypes($this->registry);
        $cstm_types = $cstm_types ? implode(', ', $cstm_types) : '0';
        $this->data['customer_autocomplete_filters'] = array('<type>' => $cstm_types);

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('minitasks') . ': ' . $this->i18n('minitasks_myfinished');
        $this->data['title'] = $title;
    }
}

?>
