<?php

class Stages_View_Viewer extends Viewer {
    public $template = 'view.html';

    public function prepare() {
        $this->model = $this->registry['stage'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s', 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        if (!$this->model->get('parent_id')) {
        //stage
            $this->data['stage'] = $this->model;
            //prepare document types
            require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'));
            $documentTypes = Documents_Types::search($this->registry, $filters);
            $this->data['stage_types'] = Documents_Types::sanitizeModels($documentTypes);

            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $groups = Groups::getTree($this->registry);
            $this->data['groups'] =  Groups::sanitizeModels($groups);

            //ToDo get only selected group users ...
            //get all users
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $users = Groups::getUsers($this->registry, array('id' => PH_GROUP_FIRST));
            $this->data['users'] = $users;

            //get substages
            require_once $this->modelsDir . 'stages.factory.php';
            $filters['model_lang'] = $this->model->get('model_lang');
            $filters['where'] = array('s.parent_id = ' . $this->model->get('id'),
                                      's.deleted IS NOT NULL');
            $substages = Stages::search($this->registry, $filters);
            $this->data['substages'] =  Stages::sanitizeModels($substages);
        } else {
        //substage
            $this->template = 'view_substage.html';
            $this->data['substage'] = $this->model;
        }

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('stages');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        if ($this->model->get('parent_id')) {
            $title = $this->i18n('stages_edit');
            $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s', 
                                $this->registry['module_param'], $this->module,
                                $this->registry['action_param'], $this->action,
                                $this->action, $this->model->get('parent_id'));

            $navbarlink[] = array('href' => $href, 'text' => $title);
            $title = $this->i18n('substages_view');
        } else {
            $title = $this->i18n('stages_view');
        }
        $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
