<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}

<form name="stages" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$stage->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$stage->get('name')|escape}" title="{#stages_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="model_type"{if $messages->getErrors('model_type')} class="error"{/if}>{help label='model_type'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="model_type" id="model_type" title="{#stages_model_type#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
{foreach from=$stage_types item='type'}
              <option value="{$type->get('id')}"{if $type->get('id') eq $stage->get('model_type')} selected="selected"{/if}>{$type->get('name')|escape}</option>
{/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_num"><label for="num"{if $messages->getErrors('num')} class="error"{/if}>{help label='position'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="num" id="num" value="{$stage->get('num')|escape}" title="{#stages_position#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="group">{help label='group'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="group" id="group" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="dropdownTypingSearch(this, event);" title="{#stages_group#|escape}">
{foreach from=$groups item='item'}
              {if (!$item->isDeleted() && $item->isActivated()) || $item->get('id') eq $stage->get('group')}
              <option value="{$item->get('id')}"{if $item->get('id') eq $stage->get('group')} selected="selected"{/if}{if $item->isDeleted() || !$item->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{$item->get('name')|indent:$item->get('level'):"-"}</option>
              {/if}
{/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_time_limit"><label for="time_limit"{if $messages->getErrors('time_limit')} class="error"{/if}>{help label='time_limit'} ({#stages_days#|escape}):</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="time_limit" id="time_limit" value="{$stage->get('time_limit')|escape}" title="{#stages_time_limit#|escape} ({#stages_days#|escape})" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="charge_person">{help label='charge_person'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox{if !$stage->get('charge_person')} undefined{/if}" name="charge_person" id="charge_person" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="{#stages_charge_person#|escape}">
              <option value="" class="undefined"{if $stage->get('charge_person') === ""} selected="selected"{/if}>[{#please_select#|escape}]</option>
{foreach from=$users item='item'}
              <option value="{$item.id}"{if $item.id eq $stage->get('charge_person')} selected="selected"{/if}>{$item.firstname|escape} {$item.lastname|escape}</option>
{/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="description">{help label='description'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#stages_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$stage->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$stage exclude='groups'}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
