<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="stages" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$stage->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$stage->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="vtop">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td colspan="3">
            {#message_translatable_items#|escape}
          </td>
          <td class="vtop t_border divider_cell" rowspan="11">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="2">&nbsp;</td>
          {capture assign='source_lang'}lang_{$stage->get('model_lang')}{/capture}
          <td><img src="{$theme->imagesUrl}flags/{$stage->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
          <td>&nbsp;</td>
          {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
          <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="name" id="name" value="{$stage->get('name')|escape}" title="{#stages_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{#stages_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="model_type"{if $messages->getErrors('model_type')} class="error"{/if}>{help label='model_type'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="model_type" id="model_type" title="{#stages_model_type#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
{foreach from=$stage_types item='type'}
              <option value="{$type->get('id')}"{if $type->get('id') eq $stage->get('model_type')} selected="selected"{/if}>{$type->get('name')|escape}</option>
{/foreach}
            </select>
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><label for="status"{if $messages->getErrors('status')} class="error"{/if}>{help label='status'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="status" id="status" title="{#stages_status#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              <option value="planning" {if $stage->get('status') eq 'planning'} selected="selected"{/if}>{#stages_status_planning#|escape}</option>
              <option value="progress" {if $stage->get('status') eq 'progress'} selected="selected"{/if}>{#stages_status_progress#|escape}</option>
              <option value="control" {if $stage->get('status') eq 'control'} selected="selected"{/if}>{#stages_status_control#|escape}</option>
              <option value="finished" {if $stage->get('status') eq 'finished'} selected="selected"{/if}>{#stages_status_finished#|escape}</option>
            </select>
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><label for="charge_person">{help label='charge_person'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox{if !$stage->get('charge_person')} undefined{/if}" name="charge_person" id="charge_person" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="{#stages_charge_person#|escape}">
              <option value="" class="undefined"{if !($stage->get('charge_person'))} selected="selected"{/if}>[{#please_select#|escape}]</option>
              {foreach from=$charge_persons item='item'}
                <option value="{$item.value}"{if $item.value eq $stage->get('charge_person')} selected="selected"{/if}>{$item.label|escape}</option>
              {/foreach}
            </select>
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_num"><label for="num"{if $messages->getErrors('num')} class="error"{/if}>{help label='position'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="num" id="num" value="{$stage->get('num')|escape}" title="{#stages_position#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_time_limit"><label for="time_limit"{if $messages->getErrors('time_limit')} class="error"{/if}>{help label='time_limit'} ({#stages_days#|escape}):</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="time_limit" id="time_limit" value="{$stage->get('time_limit')|escape}" title="{#stages_time_limit#|escape} ({#stages_days#|escape})" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td class="labelbox"><label for="description">{help label='description'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox distinctive" name="description" id="description" title="{#stages_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$stage->get('description')|escape}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_description" id="copy_description" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_description" id="bm_description" title="{#stages_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#translate#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
          <td>&nbsp;</td>
          <td colspan="2">
            <button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$stage exclude='groups'}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>

<br />
<br />
{if $substages}
{include file=`$templatesDir`_list_substages.html}
{/if}