<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {mb_truncate_overlib text=$stage->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="model_type"{if $messages->getErrors('model_type')} class="error"{/if}>{help label='model_type'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
{foreach from=$stage_types item='type'}
              {if $type->get('id') eq $stage->get('model_type')}{$type->get('name')}{/if}
{/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='position'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$stage->get('num')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="group">{help label='group'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
{foreach from=$groups item='item'}
           {if $item->get('id') eq $stage->get('group')}
             {if $item->isDeleted() || !$item->isActivated()}
               <span class="inactive_option" title="{#inactive_option#}">{$item->get('name')}</span>
             {else}
               {$item->get('name')}
             {/if}
           {/if}
{/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='time_limit'} ({#stages_days#|escape}):</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$stage->get('time_limit')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="group">{help label='charge_person'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
{foreach from=$users item='item'}
              {if $item.id eq $stage->get('charge_person')}{$item.firstname} {$item.lastname}{/if}
{/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="description">{help label='description'}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$stage->get('description')|mb_wordwrap|url2href}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$stage exclude='groups'}
</div>

<br />
<br />
{if $substages}
{include file=`$templatesDir`_list_substages.html}
{/if}