<?php

require_once PH_MODULES_DIR . 'soap/controllers/soap.controller.php';

class Custom_Soap_Controller extends Soap_Controller {

    /**
     * Array with permited test actions for the client
     */
    public $actions = array();

    /**
     * Actions which will need user's credentials and we cannot use
     * automated system of nZoom
     * @var array
     */
    public $authenticateActions = array('handle'/*, 'getdocs'*/);

    /**
    * WSDL data including login details
    */
    public static $wsdlData = array(
             'trace'          => true,
             'cache_wsdl'     => WSDL_CACHE_NONE,
             'features'       => SOAP_SINGLE_ELEMENT_ARRAYS,
             'authentication' => SOAP_AUTHENTICATION_DIGEST);
}
