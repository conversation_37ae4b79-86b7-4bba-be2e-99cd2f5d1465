<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
  xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
  xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:tns="nzTargetNamespace" targetNamespace="nzTargetNamespace">
  <wsdl:types>
    <xsi:schema xmlns:xsi="http://www.w3.org/2001/XMLSchema"
      targetNamespace="nzTargetNamespace" elementFormDefault="qualified">

      <!-- TYPE FOR DIAGNOSTIC -->
      <xsi:complexType name="tDiag">
        <xsi:sequence>
          <xsi:element name="code" type="xs:int" />
          <xsi:element name="text" type="xs:string" />
          <xsi:element name="trace" type="xs:string" />
        </xsi:sequence>
      </xsi:complexType>

      <xsi:complexType name="tPolicyData">
        <xsi:sequence>
          <xsi:element name="carID" type="xs:string" nillable="false" />
          <xsi:element name="carCode" type="xs:string" nillable="false" />
          <xsi:element name="carType" type="xs:string" nillable="true" />
          <xsi:element name="carName" type="xs:string" nillable="true" />
          <xsi:element name="carRamaNum" type="xs:string" nillable="false" />
          <xsi:element name="carCapacity" type="xs:string" nillable="true" />
          <xsi:element name="carFuelType" type="xs:string" nillable="true" />
          <xsi:element name="carSeats" type="xs:string" nillable="true" />
          <xsi:element name="carDoors" type="xs:string" nillable="true" />
          <xsi:element name="carColor" type="xs:string" nillable="true" />
          <xsi:element name="carPaint" type="xs:string" nillable="true" />
          <xsi:element name="carMadeYear" type="xs:string" nillable="true" />

          <xsi:element name="ownerID" type="xs:string" nillable="false" />
          <xsi:element name="ownerName" type="xs:string" nillable="false" />
          <xsi:element name="ownerEGNEIK" type="xs:string" nillable="true" />
          <xsi:element name="ownerCity" type="xs:string" nillable="true" />
          <xsi:element name="ownerAddress" type="xs:string" nillable="true" />

          <xsi:element name="policyID" type="xs:string" nillable="false" />
          <xsi:element name="policyNum" type="xs:string" nillable="true" />
          <xsi:element name="policyFrom" type="xs:string" nillable="true" />
          <xsi:element name="policyTo" type="xs:string" nillable="true" />
        </xsi:sequence>
      </xsi:complexType>

      <xsi:complexType name="tUploadedFile">
        <xsi:sequence>
          <xsi:element name="fileDescription" type="xs:string" nillable="true" /> <!-- This is the file description from the dropdown -->
          <xsi:element name="fileType" type="xs:string" nillable="false" /> <!-- This is the file type (image/png) from the $_FILES array -->
          <xsi:element name="fileName" type="xs:string" nillable="false" />
          <xsi:element name="fileError" type="xs:string" nillable="false" />
          <xsi:element name="fileSize" type="xs:string" nillable="false" />
          <xsi:element name="fileContent" type="xs:string" nillable="false" /> <!-- The files should be sent beas64 encoded and gzipped -->
        </xsi:sequence>
      </xsi:complexType>

      <xsi:complexType name="tMedicalExamSchedule">
        <xsi:sequence>
          <xsi:element name="date" type="xs:string" nillable="false" />
          <xsi:element name="intervals" type="tns:tInterval" nillable="false"   maxOccurs="unbounded" minOccurs="0" />
        </xsi:sequence>
      </xsi:complexType>

      <xsi:complexType name="tInterval">
        <xsi:sequence>
          <xsi:element name="hourStart" type="xs:string" nillable="false" />
          <xsi:element name="hourEnd" type="xs:string" nillable="false" />
          <!-- Status can be:
            available,
            reserved (reserved by someone else),
            mine (reserved by current user),
             -->
          <xsi:element name="status" type="xs:string" nillable="false" />
          <!-- Restrictions can be:
          empty (clickable/available),
          past (the date is past - not available),
          expired (the corrections window has expired - not available),
          exceeded (the corrections limit has been exceeded - not available)
          -->
          <xsi:element name="restrictions" type="xs:string" nillable="true" />
        </xsi:sequence>
      </xsi:complexType>


      <!-- TYPES - nZOOM REGISTER DAMAGE -->
      <xsi:element name="tRegisterDamageRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="notifierName" type="xs:string" nillable="true" />
            <xsi:element name="notifierID" type="xs:string" nillable="true" />
            <xsi:element name="notifierCity" type="xs:string" nillable="true" />
            <xsi:element name="notifierAddress" type="xs:string" nillable="true" />
            <xsi:element name="notifierAddressNum" type="xs:string" nillable="true" />
            <xsi:element name="notifierAddressBlock" type="xs:string" nillable="true" />
            <xsi:element name="notifierAddressEntry" type="xs:string" nillable="true" />
            <xsi:element name="notifierAddressApp" type="xs:string" nillable="true" />
            <xsi:element name="notifierPhone" type="xs:string" nillable="true" />
            <xsi:element name="notifierMobile" type="xs:string" nillable="true" />
            <xsi:element name="notifierEmail" type="xs:string" nillable="true" />
            <xsi:element name="notifierFunction" type="xs:string" nillable="true" />

            <xsi:element name="carID" type="xs:string" nillable="false" />
            <xsi:element name="carCode" type="xs:string" nillable="false" />
            <xsi:element name="carType" type="xs:string" nillable="true" />
            <xsi:element name="carName" type="xs:string" nillable="true" />
            <xsi:element name="carRamaNum" type="xs:string" nillable="false" />
            <xsi:element name="carCapacity" type="xs:string" nillable="true" />
            <xsi:element name="carFuelType" type="xs:string" nillable="true" />
            <xsi:element name="carSeats" type="xs:string" nillable="true" />
            <xsi:element name="carDoors" type="xs:string" nillable="true" />
            <xsi:element name="carColor" type="xs:string" nillable="true" />
            <xsi:element name="carPaint" type="xs:string" nillable="true" />
            <xsi:element name="carMadeYear" type="xs:string" nillable="true" />

            <xsi:element name="ownerID" type="xs:string" nillable="false" />
            <xsi:element name="ownerName" type="xs:string" nillable="false" />
            <xsi:element name="ownerEGNEIK" type="xs:string" nillable="true" />
            <xsi:element name="ownerCity" type="xs:string" nillable="true" />
            <xsi:element name="ownerAddress" type="xs:string" nillable="true" />

            <xsi:element name="damagesResults" type="xs:string" nillable="true" />
            <xsi:element name="insuranceEventDate" type="xs:string" nillable="true" />
            <xsi:element name="insuranceEventCircumstance" type="xs:string" nillable="true" />
            <xsi:element name="insuranceEventAutoplace" type="xs:string" nillable="true" />
            <xsi:element name="insuranceInsurerName" type="xs:string" nillable="true" />
            <xsi:element name="insuranceInsurerID" type="xs:string" nillable="true" />
            <xsi:element name="insuranceIdentifiedDisabilities" type="xs:string" nillable="true" />

            <xsi:element name="policyID" type="xs:string" nillable="false" />
            <xsi:element name="policyNum" type="xs:string" nillable="true" />
            <xsi:element name="policyFrom" type="xs:string" nillable="true" />
            <xsi:element name="policyTo" type="xs:string" nillable="true" />

            <xsi:element name="uploadedFiles" type="tns:tuploadedFile" maxOccurs="unbounded" minOccurs="0" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="tRegisterDamageResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="documentNumber" type="xs:string" nillable="true" />
            <xsi:element name="nzResult" type="xs:string" nillable="true" />
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>

      <!-- TYPES - nZOOM GET POLICY DATA -->
      <xsi:element name="tGetPolicyDataRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="carCode" type="xs:string" nillable="false" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="tGetPolicyDataResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzPolicyData" type="tns:tPolicyData" maxOccurs="unbounded" minOccurs="0" nillable="true" />
            <xsi:element name="nzResult" type="xs:string" nillable="true" />
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>

      <!-- TYPES - nZOOM LOGIN MEDICAL EXAM -->
      <xsi:element name="tLoginMedicalExamRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="UCN" type="xs:string" nillable="false" />
            <xsi:element name="HIN" type="xs:string" nillable="false" />
            <xsi:element name="Email" type="xs:string" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="tLoginMedicalExamResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzInsuredPersonName" type="xs:string" nillable="true" />
            <xsi:element name="nzHospital" type="xs:string" nillable="true" />
            <xsi:element name="nzHospitalAddress" type="xs:string" nillable="true" />
            <xsi:element name="nzContactName" type="xs:string" nillable="true" />
            <xsi:element name="nzContactPhone" type="xs:string" nillable="true" />
            <xsi:element name="nzContactEmail" type="xs:string" nillable="true" />
            <xsi:element name="nzResult" type="xs:string" nillable="true" />
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>

      <!-- TYPES - nZOOM GET MEDICAL EXAM SCHEDULE -->
      <xsi:element name="tGetMedicalExamScheduleRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="UCN" type="xs:string" nillable="false" />
            <xsi:element name="HIN" type="xs:string" nillable="false" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="tGetMedicalExamScheduleResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzReservationDate" type="xs:string" nillable="true" />
            <xsi:element name="nzReservationStartHour" type="xs:string" nillable="true" />
            <xsi:element name="nzReservationEndHour" type="xs:string" nillable="true" />
            <xsi:element name="nzReservationCorrections" type="xs:integer" nillable="true" />
            <xsi:element name="nzCorrectionsLimit" type="xs:integer" nillable="true" />
            <xsi:element name="nzCorrectionsWindowDays" type="xs:integer" nillable="true" />
            <xsi:element name="nzCorrectionsWindowDate" type="xs:string" nillable="true" />
            <xsi:element name="nzMedicalExamSchedule" type="tns:tMedicalExamSchedule" maxOccurs="unbounded" minOccurs="0" nillable="true" />
            <xsi:element name="nzResult" type="xs:string" nillable="true" />
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>

      <!-- TYPES - nZOOM RESERVE MEDICAL EXAM -->
      <xsi:element name="tReserveMedicalExamRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="UCN" type="xs:string" nillable="false" />
            <xsi:element name="HIN" type="xs:string" nillable="false" />
            <xsi:element name="Email" type="xs:string" nillable="true" />
            <xsi:element name="date" type="xs:string" nillable="false" />
            <xsi:element name="hourStart" type="xs:string" nillable="false" />
            <xsi:element name="hourEnd" type="xs:string" nillable="false" />
            <xsi:element name="remove" type="xs:boolean" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="tReserveMedicalExamResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzResult" type="xs:string" nillable="true" />
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true" />
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>

    </xsi:schema>
  </wsdl:types>

  <!-- MESSAGES - nZOOM REGISTER DAMAGE -->
  <wsdl:message name="nzRegisterDamageRequest">
    <wsdl:part name="nzRegisterDamage" element="tns:tRegisterDamageRequest" />
  </wsdl:message>
  <wsdl:message name="nzRegisterDamageResponse">
    <wsdl:part name="nzRegisterDamage" element="tns:tRegisterDamageResponse" />
  </wsdl:message>
  <!-- MESSAGES - nZOOM GET POLICY DATA -->
  <wsdl:message name="nzGetPolicyDataRequest">
    <wsdl:part name="nzGetPolicyData" element="tns:tGetPolicyDataRequest" />
  </wsdl:message>
  <wsdl:message name="nzGetPolicyDataResponse">
    <wsdl:part name="nzGetPolicyData" element="tns:tGetPolicyDataResponse" />
  </wsdl:message>
  <!-- MESSAGES - nZOOM MEDICAL EXAM LOGIN -->
  <wsdl:message name="nzLoginMedicalExamRequest">
    <wsdl:part name="nzLoginMedicalExam" element="tns:tLoginMedicalExamRequest" />
  </wsdl:message>
  <wsdl:message name="nzLoginMedicalExamResponse">
    <wsdl:part name="nzLoginMedicalExam" element="tns:tLoginMedicalExamResponse" />
  </wsdl:message>
  <!-- MESSAGES - nZOOM GET MEDICAL EXAM SCHEDULE -->
  <wsdl:message name="nzGetMedicalExamScheduleRequest">
    <wsdl:part name="nzGetMedicalExamSchedule" element="tns:tGetMedicalExamScheduleRequest" />
  </wsdl:message>
  <wsdl:message name="nzGetMedicalExamScheduleResponse">
    <wsdl:part name="nzGetMedicalExamSchedule" element="tns:tGetMedicalExamScheduleResponse" />
  </wsdl:message>
  <!-- MESSAGES - nZOOM RESERVE MEDICAL EXAM -->
  <wsdl:message name="nzReserveMedicalExamRequest">
    <wsdl:part name="nzReserveMedicalExam" element="tns:tReserveMedicalExamRequest" />
  </wsdl:message>
  <wsdl:message name="nzReserveMedicalExamResponse">
    <wsdl:part name="nzReserveMedicalExam" element="tns:tReserveMedicalExamResponse" />
  </wsdl:message>

  <wsdl:portType name="nzPortType">
    <!-- OPERATION - nZOOM REGISTER DAMAGE -->
    <wsdl:operation name="nzRegisterDamage">
      <wsdl:input message="tns:nzRegisterDamageRequest" />
      <wsdl:output message="tns:nzRegisterDamageResponse" />
    </wsdl:operation>
    <!-- OPERATION - nZOOM GET POLICY DATA -->
    <wsdl:operation name="nzGetPolicyData">
      <wsdl:input message="tns:nzGetPolicyDataRequest" />
      <wsdl:output message="tns:nzGetPolicyDataResponse" />
    </wsdl:operation>
    <!-- OPERATION - nZOOM MEDICAL EXAM LOGIN -->
    <wsdl:operation name="nzLoginMedicalExam">
      <wsdl:input message="tns:nzLoginMedicalExamRequest" />
      <wsdl:output message="tns:nzLoginMedicalExamResponse" />
    </wsdl:operation>
    <!-- OPERATION - nZOOM GET MEDICAL EXAM SCHEDULE -->
    <wsdl:operation name="nzGetMedicalExamSchedule">
      <wsdl:input message="tns:nzGetMedicalExamScheduleRequest" />
      <wsdl:output message="tns:nzGetMedicalExamScheduleResponse" />
    </wsdl:operation>
    <!-- OPERATION - nZOOM RESERVE MEDICAL EXAM -->
    <wsdl:operation name="nzReserveMedicalExam">
      <wsdl:input message="tns:nzReserveMedicalExamRequest" />
      <wsdl:output message="tns:nzReserveMedicalExamResponse" />
    </wsdl:operation>
  </wsdl:portType>

  <wsdl:binding name="nzBinding" type="tns:nzPortType">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
    <!-- OPERATION - nZOOM REGISTER DAMAGE -->
    <wsdl:operation name="nzRegisterDamage">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - nZOOM GET POLICY DATA -->
    <wsdl:operation name="nzGetPolicyData">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - nZOOM MEDICAL EXAM LOGIN -->
    <wsdl:operation name="nzLoginMedicalExam">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - nZOOM GET MEDICAL EXAM SCHEDULE -->
    <wsdl:operation name="nzGetMedicalExamSchedule">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - nZOOM RESERVE MEDICAL EXAM -->
    <wsdl:operation name="nzReserveMedicalExam">
      <soap:operation soapAction="" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>

  <wsdl:service name="nzSoapService">
    <wsdl:port name="nzPortType" binding="tns:nzBinding">
      <soap:address location="[server_base]soap/aon/" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
