<?php

require_once PH_MODULES_DIR . 'soap/models/soap.handler.php';

class Custom_Soap_Handler extends Soap_Handler {

    /**
     * Diagnostics data - array of arrays containing
     *   code, message text and trace information
     * List of codes
     * 1: Incorrect login details!
     * 2: Invalid permissions! Access denied!
     * 4: Invalid fromDate
     * 5: Invalid toDate
     * 6: Invalid limit
     * 7: Invalid page
     */
     private $diag;

    /**
     * Response sent to the SOAP client
     */
     private $response;

    /**
     * Function to set diagnostic data
     *
     * @param int $code          - codes are listed above (in the $diag property help section)
     * @param array $text_params - list of params used to replace the placeholders %s in the i18n texts
     * @param mixed $trace       - debug information
     * @return string $text      - the message text
     */
    private function _setDiagnostics($code, $text_params = array(), $trace = null) {
        $text         = vsprintf($this->i18n('message_' . $code), $text_params);
        $this->diag[] = array('code'  => $code,
                              'text'  => $text,
                              'trace' => (!empty($trace) ? var_export($trace, true) : ''));

        return $text;
    }

    /**
     * Function to get service order information
     *
     * @param object $params - function parameters
     * @return array         - the result and the diagnostic data
     */
    public function nzGetInvoices($params) {
        // Prepare some basics
        $this->diag     = array();
        $this->response = false;
        $invoices       = array();

        // Check the format of the dates
        if (!empty($params->fromDate)) {
            if (!preg_match('#^\d{4}-\d{2}-\d{2}$#', $params->fromDate)) {
                $this->_setDiagnostics(4);
            }
        }
        if (!empty($params->toDate)) {
            if (!preg_match('#^\d{4}-\d{2}-\d{2}$#', $params->toDate)) {
                $this->_setDiagnostics(5);
            }
        }
        // Check the format of the limit and page
        if (!empty($params->limit)) {
            if (!preg_match('#^\d+$#', $params->limit)) {
                $this->_setDiagnostics(6);
            }
        }
        if (!empty($params->page)) {
            if (!preg_match('#^\d+$#', $params->page)) {
                $this->_setDiagnostics(7);
            }
        }

        // If no errors
        if (empty($this->diag)) {
            // Get the order number

            // Prevent from SQL injection and set default values
            $filter_from_date = !empty($params->fromDate) ? General::slashesEscape($params->fromDate) : '';
            $filter_to_date = !empty($params->toDate) ? General::slashesEscape($params->toDate) : '';
            $filter_eik = !empty($params->eik) ? General::slashesEscape($params->eik) : '';
            $filter_number = !empty($params->number) ? General::slashesEscape($params->number) : '';
            $filter_customer = !empty($params->customer) ? General::slashesEscape($params->customer) : '';
            $limit = !empty($params->limit) && $params->limit <=1000 && $params->limit >=1 ? General::slashesEscape($params->limit) : 1000;
            $page = !empty($params->page) ? General::slashesEscape($params->page) : 1;
            $offset = $limit * ($page - 1);

			$tables = array(
				'fir' => DB_TABLE_FINANCE_INCOMES_REASONS,
				'firi18n' => DB_TABLE_FINANCE_INCOMES_REASONS_I18N,
				'frr' => DB_TABLE_FINANCE_REASONS_RELATIVES,
				'ui18n' => DB_TABLE_USERS_I18N,
			);

			$where = array();
			if ($filter_from_date) {
			    $where[] = "AND fir.issue_date >= '{$filter_from_date}'";
            }
            if ($filter_to_date) {
                $where[] = "AND fir.issue_date <= '{$filter_to_date}'";
            }
            if ($filter_eik) {
                $where[] = "AND fir.eik LIKE '%{$filter_eik}%'";
            }
            if ($filter_number) {
                $where[] = "AND fir.num LIKE '%{$filter_number}%'";
            }
            if ($filter_customer) {
                $where[] = "AND firi18n.customer_name LIKE '%{$filter_customer}%'";
            }

            // Search for invoices, debit and credit notices
            $query = " 
				SELECT SQL_CALC_FOUND_ROWS
				  fir.num as number,
				  CASE fir.type
				    WHEN 1 THEN 'invoice'
				    WHEN 3 THEN 'credit'
				    WHEN 4 THEN 'debit'
				  END as documentType,
				  fir2.num as parentNumber,
				  FORMAT(fir.total_with_vat, 2) as total,
				  fir.currency as currency,
				  fir.issue_date as issueDate,
				  fir.date_of_payment as paymentDate,
				  firi18n.customer_name as customer,
				  fir.eik as eik,
				  fir.invoice_code as code,
				  TRIM(CONCAT(ui18n.firstname, ' ', ui18n.lastname)) as issuedBy,
				  IF(fir.annulled = 0, '', fir.annulled) as annulled
				FROM {$tables['fir']} AS fir 
				LEFT JOIN {$tables['firi18n']} AS firi18n
				  ON (fir.id=firi18n.parent_id AND firi18n.lang='{$this->registry['lang']}')
				LEFT JOIN {$tables['frr']} AS frr
				  ON (fir.type IN (3,4) 
				      AND frr.parent_id=fir.id AND parent_model_name='Finance_Incomes_Reason'
				      AND frr.link_to_model_name='Finance_Incomes_Reason'
				      )
				LEFT JOIN {$tables['fir']} AS fir2 
				  ON (frr.link_to IS NOT NULL  
				      AND fir2.id=frr.link_to
				      AND fir2.type=1
				      )
				LEFT JOIN {$tables['ui18n']} AS ui18n
				  ON (ui18n.parent_id=fir.issue_by AND ui18n.lang='{$this->registry['lang']}')
				WHERE 
					fir.type IN (1, 3, 4)  
					AND fir.annulled_by IS NOT NULL
					" . implode("\n", $where) . "
				ORDER BY fir.added ASC, fir.id DESC
				LIMIT {$offset}, {$limit}";
            $invoices = $this->registry['db']->GetAll($query);
            $total = $this->registry['db']->GetOne('SELECT FOUND_ROWS()');
            $pages = ceil ($total / $limit);
        }

        $result = array(
            'page' => $page,
            'pages' => $pages,
            'total' => $total,
            'limit' => $limit,
            'invoices' => $invoices,
            'nzDiag' => $this->diag
        );

        // Return the result and the diagnostic data
        return $result;
    }
}

?>
