<?php

class Contracts_Dropdown extends Dropdown {

    /**
     * Get contract period lengths
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getSinglePeriodLengths($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        return array(
            'day' => array(
                array(
                    'option_value' => '0',
                    'label' => $registry['translater']->translate('contracts_recurrence_astro_period')
                )
            ),
            'week' => array(
                array(
                    'option_value' => '0',
                  'label' => $registry['translater']->translate('contracts_recurrence_astro_period')
                ),
                array(
                    'option_value' => '5',
                    'label' => '5 ' . $registry['translater']->translate('contracts_recurrence_days_period')
                ),
                array(
                    'option_value' => '6',
                    'label' => '6 ' . $registry['translater']->translate('contracts_recurrence_days_period')
                )
            ),
            'month' => array(
                array(
                    'option_value' => '0',
                    'label' => $registry['translater']->translate('contracts_recurrence_astro_period')
                ),
                array(
                    'option_value' => 'working',
                    'label' => $registry['translater']->translate('working_days_days')
                ),
                array(
                    'option_value' => '30',
                    'label' => '30 ' . $registry['translater']->translate('contracts_recurrence_days_period')
                )
            ),
            'trimester' => array(
                array(
                    'option_value' => '0',
                    'label' => $registry['translater']->translate('contracts_recurrence_astro_period')
                ),
                array(
                    'option_value' => 'working',
                    'label' => $registry['translater']->translate('working_days_days')
                ),
                array(
                    'option_value' => '90',
                    'label' => '90 ' . $registry['translater']->translate('contracts_recurrence_days_period')
                )
            ),
            'year' => array(
                array(
                    'option_value' => '0',
                    'label' => $registry['translater']->translate('contracts_recurrence_astro_period')
                ),
                array(
                    'option_value' => 'working',
                    'label' => $registry['translater']->translate('working_days_days')
                ),
                array(
                    'option_value' => '360',
                    'label' => '360 ' . $registry['translater']->translate('contracts_recurrence_days_period')
                )
            )
        );
    }

    /**
     * Get contract subtypes
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getSubtypes ($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        return array(array('label' => $registry['translater']->translate('contracts_all'),
                           'option_value' => 'all'),
                     array('label' => $registry['translater']->translate('contracts_contract'),
                           'option_value' => 'contract'),
                     array('label' => $registry['translater']->translate('contracts_original'),
                           'option_value' => 'original'),
                     array('label' => $registry['translater']->translate('contracts_annex'),
                           'option_value' => 'annex'),
                    );
    }

    /**
     * Get contract statuses and substatuses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getStatuses($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        $statuses = array('opened' => array('label' => $registry['translater']->translate('contracts_status_opened'),
                                            'option_value' => 'opened'),
                          'locked' => array('label' => $registry['translater']->translate('contracts_status_locked'),
                                            'option_value' => 'locked'),
                          'closed' => array('label' => $registry['translater']->translate('contracts_status_closed'),
                                            'option_value' => 'closed')
                   );

        $model_types = array();
        if (!empty($params['model_types'])) {
            $model_types = array_map('intval', $params['model_types']);
        } else {
            return $statuses;
        }

        $sql = array('select' => '', 'from' => '', 'where' => '');

        $sql['where'] = 'WHERE cs.lang = \'' . $registry['lang'] . "'\n" .
                        '  AND cs.contract_type = \'' . array_shift($model_types) . "'\n" .
                        '  AND cs.deleted_by = 0' . "\n" .
                        '  AND cs.active = 1';

        $sql['select'] = 'SELECT cs.status, CONCAT(\'substatus_\', cs.id) AS option_value, ' . "\n" .
                         '       CONCAT(\'&nbsp;&nbsp;&bull;&nbsp;\', cs.name) AS label,' . "\n" .
                         '       cs.name AS extended_value';
        $sql['from']   = 'FROM ' . DB_TABLE_CONTRACTS_STATUSES . ' AS cs'. "\n";

        foreach ($model_types as $key => $model_type) {
            $sql['from'] .= 'JOIN ' . DB_TABLE_CONTRACTS_STATUSES . ' AS cs' . $key . "\n" .
                            '  ON cs.status=cs' . $key . '.status AND cs.name=cs' . $key .'.name AND cs' . $key . '.contract_type=' . $model_type . "\n" .
                            '    AND cs' . $key . '.deleted_by=0 AND cs' . $key . '.active=1' . "\n";
        }
        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        $sub_statuses = array();
        foreach ($records as $key => $record) {
            $status = $record['status'];
            unset($record['status']);
            $sub_statuses[$status][] = $record;
        }

        if (!empty($sub_statuses)) {
            $result = array();
            foreach ($statuses as $status => $value) {
                if (isset($sub_statuses[$status])) {
                    $result = array_merge($result, array($value), $sub_statuses[$status]);
                } else {
                    $result[] = $value;
                }
            }
        } else {
            return $statuses;
        }

        return $result;
    }

    public static function getRecurrenceTypes($params){

        $registry = $params[0];

        return array(
            array('label' => $registry['translater']->translate('day'),
                  'option_value' => 'day'),
            array('label' => $registry['translater']->translate('week'),
                  'option_value' => 'week'),
            array('label' => $registry['translater']->translate('month'),
                  'option_value' => 'month'),
            array('label' => $registry['translater']->translate('trimester'),
                  'option_value' => 'trimester'),
            array('label' => $registry['translater']->translate('year'),
                  'option_value' => 'year')
        );
    }

    public static function getSinglePeriodOptions($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        return array(
            array('label' => '[' . $registry['translater']->translate('please_select') . ']',
                  'option_value' => '',
                  'class_name' => 'undefined'),
            array('label' => $registry['translater']->translate('contracts_one_row_one_quantity'),
                  'option_value' => 'one_one'),
            array('label' => $registry['translater']->translate('contracts_one_row_all_quantities'),
                  'option_value' => 'one_all'),
            array('label' => $registry['translater']->translate('contracts_all_rows_one_quantity'),
                  'option_value' => 'all_one')
        );
    }

    public static function getFirstPeriodOptions($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        return array(
            array('label' => '[' . $registry['translater']->translate('please_select') . ']',
                  'option_value' => '',
                  'class_name' => 'undefined'),
            array('label' => $registry['translater']->translate('contracts_full_period'),
                  'option_value' => 'full'),
            array('label' => $registry['translater']->translate('contracts_partial_period'),
                  'option_value' => 'partial'),
            array('label' => $registry['translater']->translate('contracts_partial_full_period'),
                  'option_value' => 'full_partial')
        );
    }

    public static function getPayAfterOptions($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        return array(
            array('label' => $registry['translater']->translate('contracts_after_issue_date'),
                  'option_value' => 'issue'),
            array('label' => $registry['translater']->translate('contracts_after_receive_date'),
                  'option_value' => 'receive')
        );
    }

    /**
     * Get user options for "observer" field in invoice templates
     * or for self financial contact person of contracts
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getResponsibles($params) {
        $registry = $params[0];
        $params['is_portal'] = 0;
        $users = parent::getUsers($params);

        if (!empty($params['model']) && strcasecmp($params['model'], 'Contract') == 0) {
            $fin_contact_option = array(
                'option_value' => '[default_financial_contact_person]',
                'label' => '[' . $registry['translater']->translate('contracts_default_financial_contact_person') . ']');
        } else {
            $fin_contact_option = array(
                'option_value' => '[financial_contact_person]',
                'label' => '[' . $registry['translater']->translate('contracts_financial_contact_person') . ']');
        }

        if (!empty($users['contain_optgroups'])) {
            $users = array($fin_contact_option['label'] => array($fin_contact_option)) + $users;
        } else {
            array_unshift($users, $fin_contact_option);
        }

        return $users;
    }

    /**
     * Get email templates for selection in invoices templates
     * @param array $params
     * @return mixed[][] - dropdown options
     */
    public static function getTemplateEmailsPatterns($params) {
        $registry = $params[0];
        if (is_array($params['model_type'])) {
            $params['model_type'] = implode(',', $params['model_type']);
        }
        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $filters = array(
            'where' => array(
                'e.model = "' . $params['model_name'] . '"',
                'e.model_type IN (' . $params['model_type'] . ')'
            ),
            'sanitize' => true);
        $records = Emails::search($registry, $filters);
        $emails = array();
        foreach ($records as $record) {
            $emails[] = array(
                'label' => $record->get('subject'),
                'option_value' => $record->get('id')
            );
        }
        return $emails;
    }

    /**
     * Get print patterns for selection in invoices templates
     * @param array $params
     * @return mixed[][] - dropdown options
     */
    public static function getTemplatePatterns($params) {
        $registry = $params[0];
        if (is_array($params['model_type'])) {
            $params['model_type'] = implode(',', $params['model_type']);
        }
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array(
            'where' => array(
                'p.model = "' . $params['model_name'] . '"',
                'p.model_type IN (' . $params['model_type'] . ')',
                'p.active = 1',
                'p.list = 0',
               /*'p.for_printform=0',*/
                '(p.company = "' . $params['company'] . '" OR p.company = "0")'
            ),
            'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
            'sanitize' => true);
        $records = Patterns::search($registry, $filters);
        $patterns = array();
        foreach ($records as $record) {
            $patterns[] = array(
                'label' => $record->get('name'),
                'option_value' => $record->get('id')
            );
        }

        return $patterns;
    }

    public static function getDiffOptions($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        return array(
            array(
                'label' => $registry['translater']->translate('contracts_diff_options_normal'),
                'option_value' => ''
            ),
            array(
                'label' => $registry['translater']->translate('contracts_diff_options_credit_all'),
                'option_value' => 'credit_all'
            ),
            array(
                'label' => $registry['translater']->translate('contracts_diff_options_credit_all_but_now'),
                'option_value' => 'credit_all_but_now'
            ),
        );
    }
}

?>
