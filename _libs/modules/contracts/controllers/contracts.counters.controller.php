<?php

require_once PH_MODULES_DIR .  'contracts/models/contracts.counters.factory.php';

class Contracts_Counters_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Contracts_Counter';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Contracts_Counters';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit', 'translate'
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'search':
            $this->_search();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_change_depending_options':
            $this->_changeDependingOptions();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {
        //check if details are submitted via POST
        if ($this->registry['request']->isPost()) {
            //build the model from the POST
            $counter = Contracts_Counters::buildModel($this->registry);

            if ($counter->save()) {
                $this->registry['messages']->setMessage($this->i18n('message_contracts_counter_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
            } else {
                $this->registry['messages']->setError($this->i18n('error_contracts_counter_add_failed'), '', -2);
            }
        } else {
            //create empty user model
            $counter = Contracts_Counters::buildModel($this->registry);
        }

        if (!empty($counter)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('contracts_counter', $counter->sanitize());
        }

        return true;
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $counter = Contracts_Counters::buildModel($this->registry);

            if ($counter->save()) {
                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_contracts_counter_edit_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_contracts_counter_edit_failed'), '', -2);
                //ToDo - set non editable properties as currency, amount ...
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('coc.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $counter = Contracts_Counters::searchOne($this->registry, $filters);
        }

        if (!empty($counter)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('contracts_counter')) {
                $this->registry->set('contracts_counter', $counter->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_contract_counter'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * View model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('coc.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $counter = Contracts_Counters::searchOne($this->registry, $filters);

        if (!empty($counter)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('contracts_counter')) {
                $this->registry->set('contracts_counter', $counter->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_contract_counter'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $counter = Contracts_Counters::buildModel($this->registry);
            $counter->set('id', $id, true);

            if ($counter->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_contracts_counter_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //register the model, with all the posted details
                $this->registry['messages']->setError($this->i18n('error_contracts_counter_translate_failed'), '', -2);
                //register the model, with all the posted details
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('coc.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $counter = Contracts_Counters::searchOne($this->registry, $filters);
        }

        if (!empty($counter)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('contracts_counter', $counter->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_contract_counter'));
        }

        return true;
    }

    /**
     * Changes options of related dropdowns
     */
    private function _changeDependingOptions() {
        $request = $this->registry['request'];
        require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
        if (preg_match('/office/', $request->get('s_id'))) {
            //get depending offices
            $offices = array();
            if ($request->get('model_id')) {
                $params = array($this->registry,
                                'lang' => $request->get('model_lang'),
                                'company_id' => $request->get('model_id'),
                                'active' => 1);
                $offices = Finance_Dropdown::getCompanyOffices($params);
                array_unshift($offices,
                              array('label' => $this->i18n('contracts_counters_office_independent'),
                                    'option_value' => '0',
                                    'active_option' => true));
            }
            print json_encode($offices);
            exit;
        }
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);
        if ($this->model && $this->model->get('id')) {

        }

        return $actions;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Contracts_Counters::changeStatus($this->registry, $ids, $status);
        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete items
        $result = Contracts_Counters::delete($this->registry, $ids);

        if ($result < 0) {
            //there was nothing to delete
            $this->registry['messages']->setError( $this->i18n('error_nothing_to_delete'));
        } elseif ($result > 0) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
            if ($result > 1) {
                //set warning
                $this->registry['messages']->setWarning( $this->i18n('warning_not_all_items_deleted'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Contracts_Counters::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Contracts_Counters::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }
}

?>
