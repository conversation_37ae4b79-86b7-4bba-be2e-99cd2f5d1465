<?php

class Contracts_Counters_Translate_Viewer extends Viewer {
    public $template = 'counters_translate.html';

    public function prepare() {
        $this->model = $this->registry['contracts_counter'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('coc.id = ' . $this->model->get('id')),
                         'model_lang' => $basic_model_lang,
                         'sanitize' => true);
        $this->data['base_model'] = Contracts_Counters::searchOne($this->registry, $filters);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('contracts_counters_translate');
        $this->data['title'] = $title;
    }
}

?>
