<?php

class Contracts_Types_Printsettings_Viewer extends Viewer {
    public $template = 'types_settings.html';

    public function prepare() {

        //prepare controls options
        $controls_options = array();
        $controls_options['alignment']      = Dropdown::getHTMLAllignments(array($this->registry));
        $controls_options['hidden']         = Dropdown::getYesNo(array($this->registry));
        $controls_options['html_measures']  = Dropdown::getHTMLMeasures();
        $controls_options['agregate']       = Dropdown::getGT2Agregates(array($this->registry));

        $this->data['controls_options']     = $controls_options;
        $this->data['fonts']                = Dropdown::getAvailableFonts();

        //set supported langs for the models
        $this->data['model_langs'] = $this->registry['config']->getParamAsArray('i18n', 'model_langs');

        $this->registry['include_colorpicker'] = true;

        if ($this->isMain) {
            //prepare patterns
            $this->data['patterns'] = $this->registry['patterns'];

            if ($this->data['patterns']) {
                $this->data['settings_types'] = Dropdown::getGT2Patterns(array($this->registry));
            }

            //set submit link
            $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'], $this->module,
                                $this->registry['controller_param'], $this->controller,
                                $this->registry['action_param'], $this->action,
                                $this->action, $this->model->get('id'));
            $this->data['submitLink'] = $this->submitLink;

            $this->prepareTranslations();

            $this->prepareTitleBar();
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('contracts_types_edit');
        $this->data['title'] = $title;
    }
}

?>
