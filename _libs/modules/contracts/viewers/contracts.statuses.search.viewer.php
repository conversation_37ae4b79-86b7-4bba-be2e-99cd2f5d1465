<?php

class Contracts_Statuses_Search_Viewer extends Viewer {
    public $template = 'statuses_search.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'contracts.statuses.factory.php';
        $filters = Contracts_Statuses::saveSearchParams($this->registry, array(), 'search_');
        list($contracts_statuses, $pagination) = Contracts_Statuses::pagedSearch($this->registry, $filters);

        $this->data['contracts_statuses'] = $contracts_statuses;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('contracts_statuses');
        $this->data['title'] = $title;
    }
}

?>
