<?php

class Contracts_Relatives_Viewer extends Viewer {
    public $template = 'relatives.html';

    public function prepare() {
        $this->model = $this->registry['contract'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare relatives tree
        $this->data['relatives_tree'] = $this->registry['relatives_tree'];

        $this->prepareTranslations();
        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('contracts_relatives'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
