<?php

class Contracts_Types_Add_Viewer extends Viewer {
    public $template = 'types_add.html';

    public function prepare() {
        $this->model = $this->registry['contracts_type'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        //prepare department tree
        require_once(PH_MODULES_DIR . 'departments/models/departments.factory.php');
        $this->data['departments'] = Departments::getTree($this->registry);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        require_once $this->modelsDir . 'contracts.dropdown.php';

        if (!$this->registry['request']->isPost()) {
            $this->model->set('include_date_sign', 1, true);
            $this->model->set('include_date_start', 1, true);
            $this->model->set('include_date_validity', 1, true);
            $this->model->set('include_date_end', 1, true);
            $this->model->set('include_section_about', 1, true);
            $this->model->set('include_section_clause', 1, true);
        }

        $params = array(0 => $this->registry,
                        'table' => 'DB_TABLE_LAYOUTS',
                        'table_i18n' => 'DB_TABLE_LAYOUTS_I18N',
                        'label' => 'name',
                        'value' => 'layout_id',
                        'where' => 't.model = "Contract" AND t.`system` = 0 AND t.model_type = "' . $this->model->get('id') . '"');
        $this->data['type_layouts'] = Dropdown::getCustomDropdown($params);
        $this->data['gt2_layout'] = $this->model->getGT2Layout();

        //set crontab settings
        $before_start_contract = $this->registry['config']->getParam('crontab', 'before_start_contract_' . $this->model->get('id'));
        if (!$before_start_contract) {
            $before_start_contract = $this->registry['config']->getParam('crontab', 'before_start_contract');
        }
        $this->data['before_start_contract'] = $before_start_contract;

        $before_end_contract = $this->registry['config']->getParam('crontab', 'before_end_contract_' . $this->model->get('id'));
        if (!$before_end_contract) {
            $before_end_contract = $this->registry['config']->getParam('crontab', 'before_end_contract');
        }
        $this->data['before_end_contract'] = $before_end_contract;

        //load the lang files for assignments
        $module_i18n_dir = PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/';
        $module_lang_files = FilesLib::readDir($module_i18n_dir, false, 'files_only', 'ini', true);
        $this->registry['translater']->loadFile($module_lang_files, 'module');

        // assignment types for installation (hard-coded)
        $assignment_types = array('owner', 'responsible', 'observer', 'decision');
        $assignment_types_options = array();
        foreach($assignment_types as $a_type) {
            $assignment_types_options[] = array(
                'label' => $this->i18n('assignments_assign_' . $a_type),
                'option_value' => $a_type,
                'active_option' => true);
        }
        $this->data['assignment_types_options'] = $assignment_types_options;
        if (!$this->model->get('assignment_types') && !$this->registry['request']->isPost()) {
            $this->model->set('assignment_types', $assignment_types, true);
        }

        //get all available additional validate fields
        $validate_fields = $this->registry['config']->getParamAsArray($this->module, 'additional_validate_fields');
        $validate_options = array();
        foreach($validate_fields as $field) {
            $validate_option = array(
                'label' => $this->i18n($this->module . '_' . $field) ?: $this->i18n($field),
                'option_value' => $field,
                'active_option' => true
            );
            if (in_array($field, $this->model->dates_to_exclude)) {
                $validate_option['onchange'] = 'toggleIncludeDate(this)';
            }
            $validate_options[] = $validate_option;
        }
        $this->data['validate_options'] = $validate_options;

        // get all available validate unique fields
        $validate_unique_fields = $this->registry['config']->getParamAsArray($this->module, 'validate_unique_fields');
        $validate_unique_options = array();
        foreach ($validate_unique_fields as $field) {
            $validate_unique_options[] = array(
                'label' => $this->i18n($this->module . '_' . $field) ?: $this->i18n($field),
                'option_value' => 'unique_' . $field,
                'active_option' => true
            );
        }
        $this->data['validate_unique_options'] = $validate_unique_options;

        //prepare customers types for related records
        require_once PH_MODULES_DIR . 'customers/models/customers.types.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'where' => array('ct.active IS NOT NULL'),
                         'sort' => array('cti18n.name'),
                         'sanitize' => true);
        $cstm_types = Customers_Types::search($this->registry, $filters);
        $customers_types = $related_customers_types = array();
        foreach($cstm_types as $cstm_type) {
            $customers_types[] = array(
                'label' => $cstm_type->get('name'),
                'option_value' => $cstm_type->get('id'),
                'active_option' => $cstm_type->get('active'));
            $related_customers_types[] = $cstm_type->get('id');
        }
        $this->data['customers_types'] = $customers_types;
        $checker = $this->model->get('related_customers_types');
        if (empty($checker)) {
            $this->model->set('related_customers_types', $related_customers_types, true);
        }

        // get companies
        require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
        $filters = array('sanitize' => true);
        $companies = Finance_Companies::search($this->registry, $filters);
        foreach ($companies as $c) {
            // get only active VAT rates
            $c->set('vats', Finance_Companies::getVatRates($this->registry, $c->get('id'), true), true);
        }
        $this->data['companies'] = $companies;

        // get only active installation default VAT rates
        $this->data['default_vat_rates'] = Finance_Companies::getVatRates($this->registry, 0, true);

        // Set default value for: invoice_issue_auto_messages
        if (!$this->model->get('invoice_issue_auto_message') && !$this->registry['request']->isPost()) {
            $this->model->set('invoice_issue_auto_message', $this->i18n('invoice_issue_auto_messages'), true);
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('contracts_types_add');
        $this->data['title'] = $title;
    }
}

?>
