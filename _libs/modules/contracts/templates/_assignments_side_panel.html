{if !$hide_side_panel}
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    {if in_array('owner', $settings_assign)}
      <td class="t_panel_caption" nowrap="nowrap" style="width: 150px;"><div class="t_panel_caption_title">{#contracts_assign_owner#|escape}</div></td>
    {/if}
    {if in_array('responsible', $settings_assign)}
      <td class="t_panel_caption" nowrap="nowrap" style="width: 150px;"><div class="t_panel_caption_title">{#contracts_assign_responsible#|escape}</div></td>
    {/if}
    {if in_array('observer', $settings_assign)}
      <td class="t_panel_caption" nowrap="nowrap" style="width: 150px;"><div class="t_panel_caption_title">{#contracts_assign_observer#|escape}</div></td>
    {/if}
    {if in_array('decision', $settings_assign)}
      <td class="t_panel_caption" nowrap="nowrap" style="width: 150px;"><div class="t_panel_caption_title">{#contracts_assign_decision#|escape}</div></td>
    {/if}
  </tr>
  {if $model->get('assignments_owner') || $model->get('assignments_responsible') || $model->get('assignments_observer') || $model->get('assignments_decision')}
  <tr class="{cycle values='t_odd,t_even'}">
    {counter name='num_columns' assign='num_columns' start=0}
    {if in_array('owner', $settings_assign)}
      {counter name='num_columns' print=false}
      <td class="{if $num_columns lt $settings_assign|@count}t_border {/if}vtop">
      {foreach name='i' from=$model->get('assignments_owner') item='assignment'}
        {if $assignment.is_portal}
        <img src="{$theme->imagesUrl}small/user_portal.png" width="12" height="12" border="0" alt="" title="{#portal_user#}" />
        {else}
        <img src="{$theme->imagesUrl}small/user.png" width="12" height="12" border="0" alt="" title="{#normal_user#}" />
        {/if}
        {$assignment.assigned_to_name|escape}<br />
      {foreachelse}
        &nbsp;
      {/foreach}
      </td>
    {/if}
    {if in_array('responsible', $settings_assign)}
      {counter name='num_columns' print=false}
      <td class="{if $num_columns lt $settings_assign|@count}t_border {/if}vtop">
      {foreach name='j' from=$model->get('assignments_responsible') item='assignment'}
        {if $assignment.is_portal}
        <img src="{$theme->imagesUrl}small/user_portal.png" width="12" height="12" border="0" alt="" title="{#portal_user#}" />
        {else}
        <img src="{$theme->imagesUrl}small/user.png" width="12" height="12" border="0" alt="" title="{#normal_user#}" />
        {/if}
        {$assignment.assigned_to_name|escape}<br />
      {foreachelse}
        &nbsp;
      {/foreach}
      </td>
    {/if}
    {if in_array('observer', $settings_assign)}
      {counter name='num_columns' print=false}
      <td class="{if $num_columns lt $settings_assign|@count}t_border {/if}vtop">
      {foreach name='k' from=$model->get('assignments_observer') item='assignment'}
        {if $assignment.is_portal}
        <img src="{$theme->imagesUrl}small/user_portal.png" width="12" height="12" border="0" alt="" title="{#portal_user#}" />
        {else}
        <img src="{$theme->imagesUrl}small/user.png" width="12" height="12" border="0" alt="" title="{#normal_user#}" />
        {/if}
        {$assignment.assigned_to_name|escape}<br />
      {foreachelse}
        &nbsp;
      {/foreach}
      </td>
    {/if}
    {if in_array('decision', $settings_assign)}
      <td class="vtop">
      {foreach name='l' from=$model->get('assignments_decision') item='assignment'}
        {if $assignment.is_portal}
        <img src="{$theme->imagesUrl}small/user_portal.png" width="12" height="12" border="0" alt="" title="{#portal_user#}" />
        {else}
        <img src="{$theme->imagesUrl}small/user.png" width="12" height="12" border="0" alt="" title="{#normal_user#}" />
        {/if}
        {$assignment.assigned_to_name|escape}<br />
      {foreachelse}
        &nbsp;
      {/foreach}
      </td>
    {/if}
   </tr>
   {else}
   <tr class="{cycle values='t_odd,t_even'}">
     <td class="error" colspan="{$settings_assign|@count}">{#no_items_found#|escape}</td>
   </tr>
   {/if}
</table>
{/if}