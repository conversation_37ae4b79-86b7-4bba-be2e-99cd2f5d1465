
<form>
  <input type="hidden" name="contract" value="{$contract}" />
  <input type="hidden" name="direction" value="{$direction}" />
  <table style="border:none!important">
    <tr>
      <td>
        <table id="setWarehouses" class="t_grouping_table">
          <tr>
            <th>{#num#}</th>
            <th>
              {#finance_warehouses#|escape}
              <div class="t_buttons">
                <div id="setWarehouses_plusButton" onclick="addField('setWarehouses', '', '', 1)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                <div id="setWarehouses_minusButton" class="disabled" onclick="removeField('setWarehouses')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
              </div>
            </th>
          </tr>
          <tr id="setWarehouses_1">
            <td>
              <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" style="visibility: hidden;" onclick="confirmAction('delete_row', function() {ldelim} hideField('setWarehouses','1'); {rdelim}, this);" />&nbsp;<a href="javascript: disableField('setWarehouses','1')">1</a>
            </td>
            <td>
              {assign var='default_warehouse_found' value='0'}
              {if $warehouses|@count eq 1}
                {assign var='default_warehouse_found' value='1'}
                {assign var='default_warehouse' value=$warehouses[0]->get('id')}
              {elseif $default_warehouse}
                {foreach from=$warehouses item=wh}
                  {if $wh->get('id') eq $default_warehouse}
                    {assign var='default_warehouse_found' value='1'}
                  {/if}
                {/foreach}
              {/if}
              <select name="warehouses[0]" id="warehouses_1" class="selbox{if !$warehouses|@count} missing_records{elseif !$default_warehouse_found} undefined{/if}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" title="{#finance_warehouses_documents_warehouse#|escape}">
                {if !$warehouses|@count}<option value="" class="missing_records">[{#no_select_records#|escape}]</option>{/if}
                {if $warehouses|@count gt 1}<option value="" class="undefined">[{#please_select#|escape}]</option>{/if}
                {foreach from=$warehouses item=wh}
                  <option value="{$wh->get('id')}"{if $wh->get('id') eq $default_warehouse} selected="selected"{/if}>{$wh->get('name')|escape}{if $wh->get('locker')} ({#finance_warehouses_inspection_lock#}){/if}</option>
                {/foreach}
              </select>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td style="padding: 5px;">
        <button type="button" name="setWarehouses" id="setWarehousesButton" value="1" onclick="getHandoversOptions(this.form)" class="button">{#ok#|escape}</button><button type="button" name="cancel" class="button" onclick="lb.deactivate();">{#cancel#|escape}</button>
      </td>
    </tr>
  </table>
</form>