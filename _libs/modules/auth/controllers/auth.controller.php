<?php

use Nzoom\Mvc\ControllerTrait\CompleteActionTrait;

class Auth_Controller extends Controller {
    use CompleteActionTrait;

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        if (in_array($this->action, array('lost_password', 'confirm_password')) &&
            $this->registry['config']->getParam('users', 'disable_lost_password')) {
            // the password retrieval procedure is disabled
            // do not allow lost_password and confirm_password actions
            $this->setAction('login');
        }
        switch($this->action) {
        case 'logout':
            $this->_logout();
            break;
        case 'lost_password':
            $this->_lostPassword();
            break;
        case 'confirm_password':
            $this->_confirmPassword();
            break;
        case 'ping':
            $this->_ping();
            break;
        case 'ajax_checkactivity':
            $this->checkActivity();
            break;
        case 'ajax_refresh_last_activity':
            $this->refreshLastActivity();
            break;
        case 'login':
        default:
            $this->setAction('login');
            $this->_login();
        }
    }

    /**
     * Login into the system
     */
    private function _login() {
        $request = &$this->registry['request'];
        $session = &$this->registry['session'];

        if ($request->isPost()) {
            $error = false;
            //the login form is submitted
            require_once PH_MODULES_DIR . 'auth/viewers/auth.login.viewer.php';

            // If too many failed login attempts, then in browser we should expect a captcha text to be submitted
            // In REST mode, we should just block for some time and set proper status.
            if (self::isMaxAttemptRateReached($this->registry)) {
                $captchaText = $session->get('nzoom_login_captcha');
                if (Auth::$is_rest) {
                    http_response_code(429);
                    $maxFailedAttemptsTimeOut =
                        floor($this->registry['config']->getParam('users', 'max_failed_attempts_timeout') / 60);
                    $this->registry['messages']->setError($this->i18n(
                        'error_auth_rest_ratelimit',
                        [$maxFailedAttemptsTimeOut]
                    ));
                    $error = true;
                } elseif (empty($captchaText) || $request->get('captcha') != $captchaText) {
                    $this->registry['messages']->setError($this->i18n('error_auth_enter_captcha2'), 'captcha');
                    $this->registry['messages']->setError($this->i18n('error_auth_enter_captcha3'), 'captcha');
                    $error = true;
                }
            }
            if (!$error) {
                //check if username and password are specified
                if (!$request->get('username') || !$request->get('password')) {
                    //always show one and the same error so that intruder do not got additional information
                    $this->registry['messages']->setError($this->i18n('error_auth_login_invalid_details'), 'username,password');
                    $error = true;
                } elseif (!Validator::validUserName($request->get('username')) || !Validator::validPassword($request->get('password'))) {
                    $this->registry['messages']->setError($this->i18n('error_auth_login_invalid_details'), 'username, password');
                    $error = true;
                }
            }

            if (!$error) {
                $is_logged_in = Auth::login($this->registry);

                if ($is_logged_in) {
                    if (Auth::$is_rest) {
                        $this->model = $this->registry['currentUser'];
                        $this->registry->set('validLogin', true, true);
                        $this->actionCompleted = true;
                    } else {
                        //redirect to initially requested link or home page
                        $redirectUrl = $request->getGet('redirect_url');
                        if ($redirectUrl
                                && \Nzoom\Navigation::isUrlFromCurrentNzoom($redirectUrl)
                                && \Nzoom\Navigation::isUrlRedirectable($redirectUrl)) {
                            \Nzoom\Navigation::redirectToUrl($redirectUrl);
                        } else {
                            //redirect to home page
                            $this->redirect('index');
                        }
                    }
                } else {
                    $error = true;
                    if ($this->registry->get('authStatus') == 4) {
                        $this->registry['messages']->setError(sprintf($this->i18n('error_auth_max_concurrent_users'), $this->registry['max_concurrent_users']));
                        if ($this->registry['logged_users']) {
                            $logged_users_names = array();
                            foreach($this->registry['logged_users'] as $logged_user) {
                                $logged_users_names[] = $logged_user['name'];
                            }
                            $this->registry['messages']->setError(sprintf($this->i18n('error_auth_max_concurrent_users2'), implode(', ', $logged_users_names)));
                        }
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_auth_login_invalid_details'), 'username, password');
                    }
                }
            }

            if ($error) {
                //log the failed attempts to session
                $this->logFailedAttempt($this->registry);

                $this->registry['messages']->setError($this->i18n('error_auth_login_failed'), '', -1);
            }
        } else {
            //check the authorization status and display corresponding message
            switch($this->registry['authStatus']) {
            case 2:
                $this->registry['messages']->setError($this->i18n('error_auth_login_failed'));
                $this->registry['messages']->setError($this->i18n('error_auth_expired'));
                break;
            case 3:
                $this->registry['messages']->setError($this->i18n('error_auth_login_failed'));
                $this->registry['messages']->setError($this->i18n('error_auth_intrusion'));
                break;
            }
        }

        return true;
    }

    /**
     * Logout from the system
     */
    private function _logout() {
        $is_logout = Auth::logout($this->registry);

        if ($is_logout) {
            $this->registry['messages']->setMessage($this->i18n('message_auth_logout_success'), '', -1);
            if (!Auth::$is_rest) {
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        // Redirect to login page
        if (!Auth::$is_rest) {
            // Preserve the page from which we logged out
            $params = [];
            if (!empty($_SERVER['HTTP_REFERER'])
                    && \Nzoom\Navigation::isUrlFromCurrentNzoom($_SERVER['HTTP_REFERER'])
                    && \Nzoom\Navigation::isUrlRedirectable($_SERVER['HTTP_REFERER'])) {
                $params['redirect_url'] = $_SERVER['HTTP_REFERER'];
            }
            $this->redirect($this->module, 'login', $params);
            return $is_logout;
        }

        require_once PH_MODULES_DIR . 'auth/viewers/auth.logout.viewer.php';
        http_response_code($is_logout ? 200 : 401);
    }

    /**
     * Check if the user has logged in
     */
    private function _ping() {
        if ($this->registry['validLogin']) {
            $this->model = $this->registry['currentUser'];
            return true;
        } else {
            return false;
        }
    }

    /**
     * Check if the current user has not been active for max_inactive_time minutes
     *
     * @return void
     */
    private function checkActivity(): void
    {
        // If the user is NOT logged in
        if (!$this->registry['validLogin'] || !$this->registry['currentUser']) {
            // Send 0, so the frontend to redirect the user to the logout page, which then will redirect him to the login page.
            $this->completeAction(null, json_encode(['status' => 'expired']));
            return;
        }

        // Get the inactivity limit
        $maxInactiveTimeInSeconds = 60 * (int)$this->registry['config']->getParam('users', 'max_inactive_time');

        // If there's no limit for inactivity
        if ($maxInactiveTimeInSeconds <= 0) {
            // Return empty result to the frontend, which then should do nothing (just leave the user where he is)
            $this->completeAction(null, json_encode(['status' => 'active']));
            return;
        }

        // Prepare some activity variables
        $lastActivityTimeInSeconds = strtotime($this->registry['currentUser']->getLastActivity());
        $alertBeforeInactiveIntervalInSeconds = 60 * (int)$this->registry['config']->getParam('users', 'alert_before_inactive_interval');
        $activityExpireTimeInSeconds = $lastActivityTimeInSeconds + $maxInactiveTimeInSeconds;
        $currentTimeInSeconds = time();

        // If the time to alert before activity expire has come
        $alertBeforeActivityExpireTimeInSeconds = $activityExpireTimeInSeconds - $alertBeforeInactiveIntervalInSeconds;
        if ($alertBeforeActivityExpireTimeInSeconds <= $currentTimeInSeconds) {
            // Calculate time left until activity expire
            $minutesLeftUntilActivityExpire = ceil(($activityExpireTimeInSeconds - $currentTimeInSeconds) / 60);

            // If there's still time until activity expire
            if ($minutesLeftUntilActivityExpire > 0) {
                $response = [
                    'status'      => 'expiring',
                    'minutesLeft' => $minutesLeftUntilActivityExpire,
                ];
            } else {
                $response = ['status' => 'expired'];
            }
            $this->completeAction(null, json_encode($response));
            return;
        }

        // If there's nothing to do, return empty result to the frontend, which will then do nothing (just leave the user where he is)
        $this->completeAction(null, json_encode(['status' => 'active']));
    }

    /**
     * Refresh the user's last activity
     *
     * @return void
     */
    private function refreshLastActivity(): void
    {
        // If no valid login
        if (!$this->registry['validLogin']) {
            // Return that the activity has expired
            $this->completeAction(401, json_encode(['status' => 'expired']));
            return;
        }

        // Try refreshing the user's last activity
        if (empty($this->registry['currentUser'])
                || !$this->registry['currentUser']->getLastActivity()
                || !$this->registry['currentUser']->updateLastActivityDate()) {
            // Return that refreshing the user's activity has failed
            $this->completeAction(400, json_encode(['status' => 'failed']));
            return;
        }

        // Return that refreshing the user's activity is successful
        $this->completeAction(null, json_encode(['status' => 'ok']));
    }

    /**
     * Lost password retrieval procedure
     */
    private function _lostPassword() {
        $request = &$this->registry['request'];
        $session = &$this->registry['session'];

        //do not allow lost password retrieval if the user has already been logged in
        if ($this->registry['validLogin']) {
            $this->redirect('index', 'frontend');
        }

        if ($request->isPost()) {
            $error = false;

            if (!$request->get('captcha') || $request->get('captcha') != $session->get('nzoom_login_captcha')) {
                //do nothing unless the captcha code is correct!!!
                $this->registry['messages']->setError($this->i18n('error_auth_enter_captcha2'), 'captcha');
                $this->registry['messages']->setError($this->i18n('error_auth_enter_captcha3'), 'captcha');
                $this->registry['messages']->setError($this->i18n('error_auth_lost_password_failed'), '', -1);
                return false;
            }

            //the form is submitted,
            //check if username and email are specified
            if (Validator::validEmail($request->get('email'))) {
                //get users with the designated email
                $filters = array('where' => array('u.email = \'' . $request->get('email') . '\'', 'u.active=1'),
                                 'sanitize' => true);
                $users = Users::search($this->registry, $filters);

                if (empty($users)) {
                    $this->registry['messages']->setError($this->i18n('error_auth_lost_password_invalid_details'), 'email, username');
                    $error = true;
                } elseif (count($users) > 1 && !$request->get('username')) {
                    $users_list = array();
                    foreach($users as $user) {
                        $users_list[] = array(
                            'option_value'  => General::encrypt($user->get('username'), 'lostpass', 'xtea'),
                            'label'         => $user->get('firstname') . ' ' . $user->get('lastname'),
                            'active_option' => $user->get('active')
                        );
                    }
                    $this->registry['users'] = $users_list;
                    $this->registry['messages']->setError($this->i18n('error_auth_lost_password_invalid_email2'), 'email, username');
                    $error = true;
                }
            } else {
                $error = true;
                $this->registry['messages']->setError($this->i18n('error_auth_lost_password_invalid_details'), 'email, username');
            }

            if (!$error) {
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';


                //get user for the specified details
                $filters = array('where' => array('u.email = \'' . $request->get('email') . '\'', 'u.active=1'),
                                 'sanitize' => true);
                if ($request->get('username')) {
                    $username = General::decrypt($request->get('username'), 'lostpass', 'xtea');
                    $filters['where'][] = 'u.username = \'' . $username . '\'';
                }
                $user = Users::searchOne($this->registry, $filters);

                if ($user) {
                    if ($user->get('auth') == 'ldap') {
                        // password for LDAP users cannot be restored
                        // DO NOT report the actual reason to the user because this information is confidential
                        $this->registry['messages']->setError($this->i18n('error_auth_lost_password_failed'));
                    } else {
                        //send email with login details
                        $mailer = new Mailer($this->registry, 'lost_password_retrieval');

                        //sha1 is the default algorithm, but the settings may define another one
                        if ($this->registry['config']->getParam('users', 'password_encryption')) {
                            $password_encryption = $this->registry['config']->getParam('users', 'password_encryption');
                        } else {
                            $password_encryption = 'sha1';
                        }

                        //generate random password
                        $generated_password = General::generateRandomPwd();
                        $generated_password_encrypted = General::encrypt($generated_password, '', $password_encryption);

                        //hash is used to verify the confirmation link
                        $hash = sha1($user->get('id') . '_' . $generated_password_encrypted);

                        $login_link = sprintf('%s://%s%sindex.php?%s=%s&%s=%s&h=%s',
                            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                            $_SERVER["HTTP_HOST"],
                            PH_BASE_URL,
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], 'confirm_password',
                            $hash);

                        //define the placeholders for this event
                        $mailer->placeholder->add('to_email', $user->get('email'));
                        $mailer->placeholder->add('user_name', $user->get('firstname') . ' ' . $user->get('lastname'));
                        $mailer->placeholder->add('user_email', $user->get('email'));
                        $mailer->placeholder->add('user_username', $user->get('username'));
                        $mailer->placeholder->add('user_password', $generated_password);
                        $mailer->placeholder->add('login_link', $login_link);

                        $mailer->template['model_name'] = $user->modelName;
                        $mailer->template['model_id'] = $user->get('id');

                        $result = $mailer->send();
                        if (!@in_array($user->get('email'), $result['erred'])) {
                            //update the new password in the user's record
                            $query = 'UPDATE users SET confirmation_password="' . $generated_password_encrypted . '" WHERE id=' . $user->get('id');
                            $this->registry['db']->Execute($query);

                            //password is sent, show proper message
                            $this->registry['messages']->setMessage($this->i18n('message_auth_lost_password_success'));
                            $this->registry['messages']->setMessage($this->i18n('message_auth_try_login'));
                            $this->registry['messages']->insertInSession($this->registry);

                            //redirect to login page
                            $this->redirect($this->module, 'login');
                        } else {
                            $this->registry['messages']->setError($this->i18n('error_auth_lost_password_failed'));
                            $this->registry['messages']->setError($mailer->error);
                        }
                    }
                } else {
                    $this->registry['messages']->setError($this->i18n('error_auth_lost_password_failed'));
                    $this->registry['messages']->setError($this->i18n('error_auth_lost_password_invalid_details'), 'email, username');
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_auth_lost_password_failed'), '', -1);
            }
        }

    }

    /**
     * Verify and confirm restored password
     * If successful logs the user and redirects to the change password page
     * If NOT successful redirects the user to the login page
     */
    private function _confirmPassword() {
        $request = &$this->registry['request'];
        $db = &$this->registry['db'];

        //do not allow confirmation if the user has already been logged in
        if ($this->registry['validLogin']) {
            $this->redirect('index', 'frontend');
        }

        // the regular expression checks whether the h string is a valid base64 hash
        // against SQL injection attempts
        if ($request->isRequested('h') && preg_match('#^[a-zA-Z0-9+/]+={0,2}$#', $request->isRequested('h'))) {
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';


            $hash = $request->get('h');

            //get user for the specified details
            $filters = array('where'=> array(
                                        'SHA1(CONCAT(u.id, "_", confirmation_password)) = "' . $hash . '"',
                                        'confirmation_password!=""',
                                     ),
                            'sanitize' => true);
            $user = Users::searchOne($this->registry, $filters);

            if (!empty($user)) {
                //copy the new password and clear the temp password
                $query =
                    'UPDATE users SET password="' . $user->get('confirmation_password') . '", confirmation_password=NULL, password_last_changed=now()' . "\n" .
                    'WHERE SHA1(CONCAT(id, "_", confirmation_password)) = "' . $hash . '"';

                $db->Execute($query);
                if ($db->Affected_Rows() == 1) {
                    $this->registry['messages']->setMessage($this->i18n('message_auth_lost_password_confirmation_success'));
                    $this->registry['messages']->setMessage($this->i18n('message_auth_lost_password_confirmation_success2'));
                    $this->registry['messages']->insertInSession($this->registry);

                    //store the username and the encrypted pass in the the request so that the AUTH module could automatically log the user in
                    $request->set('username', $user->get('username'));
                    $request->set('encrypted_password', $user->get('confirmation_password'));

                    //automatically login the user with the new password
                    $is_valid_login = Auth::login($this->registry);

                    //redirect the user to the change password action
                    if ($is_valid_login) {
                        $this->redirect('users', 'password');
                    }
                }
            }
        }

        $this->registry['messages']->setError($this->i18n('error_auth_lost_password_confirmation_failed'));
        $this->registry['messages']->insertInSession($this->registry);
        $this->redirect($this->module, 'login');
    }

    /*
     * Checks the session and returns true if the failed attempt rate is reached
     * Like 5 attempts in 1 hour
     * See settings: max_failed_attempts, max_failed_attempts_timeout
     *
     * @return bool
     */
    public static function isMaxAttemptRateReached($registry): bool
    {
        $session = $registry['session'];
        $conf = $registry['config'];

        $maxFailedAttempts = $conf->getParam('users', 'max_failed_attempts');
        $maxFailedAttemptsTimeOut = $conf->getParam('users', 'max_failed_attempts_timeout');

        $failedAttempts = (int) $session->get('attempts', 'failed_login');
        $lastAttempts = (int) $session->get('last_attempt', 'failed_login');

        if ($failedAttempts >= $maxFailedAttempts &&
            time() - $lastAttempts < $maxFailedAttemptsTimeOut) {
            return true;
        }

        return false;
    }

    /**
     * Logs the failed attempt in session
     */
    private static function logFailedAttempt($registry): void
    {
        $conf = $registry['config'];
        $session = $registry['session'];

        $maxFailedAttempts = $conf->getParam('users', 'max_failed_attempts');
        $maxFailedAttemptsTimeOut = $conf->getParam('users', 'max_failed_attempts_timeout');

        $lastAttempts = (int) $session->get('last_attempt', 'failed_login');
        $failedAttempts = (int) $session->get('attempts', 'failed_login');

        $failedAttempts += 1;

        // Check if the time is not set or if the timeout has elapsed and the counter should be reset
        if ($lastAttempts && time() - $lastAttempts > $maxFailedAttemptsTimeOut) {
            $failedAttempts = 1;
        }

        $session->set('attempts', $failedAttempts, 'failed_login', true);

        // Only set the time of last attempt if the maximum is not reached to allow elapsing
        if ($failedAttempts <= $maxFailedAttempts) {
            $session->set('last_attempt', time(), 'failed_login', true);
        }
    }
}
?>
