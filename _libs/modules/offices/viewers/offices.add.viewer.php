<?php

class Offices_Add_Viewer extends Viewer {
    public $template = 'add.html';

    public function prepare() {
        $this->model = $this->registry['office'];
        $this->data['office'] = $this->model;

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s', 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

    }

    public function prepareTitleBar() {
        $title = $this->i18n('offices');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('offices_add_new');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action);

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
