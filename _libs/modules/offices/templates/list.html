<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=offices&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="offices" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=offices" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#offices_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.description.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.description.link}">{#offices_description#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#date#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$offices item='office'}
      {strip}
      {capture assign='info'}
        <strong>{#offices_name#|escape}:</strong> {$office->get('name')|escape}<br />
        <strong>{#offices_description#|escape}:</strong> {$office->get('description')|mb_truncate|escape}<br />
        <strong>{#added#|escape}:</strong> {$office->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$office->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$office->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$office->get('modified_by_name')|escape}<br />
        {if $office->isDeleted()}<strong>{#deleted#|escape}:</strong> {$office->get('deleted')|date_format:#date_mid#|escape}{if $office->get('deleted_by_name')} {#by#|escape} {$office->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$office->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $office->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$office->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$office->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="4" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$office disabled='edit,delete,view'}
          </td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$office->get('active')} t_inactive{/if}{if $office->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$office->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($office->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($office->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$office->get('id')}">{$office->get('name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.description.isSorted}">{$office->get('description')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.added.isSorted}">{$office->get('added')|date_format:#date_short#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$office}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="6">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html session_param=$session_param|default:$pagination.session_param exclude='multiedit'}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
