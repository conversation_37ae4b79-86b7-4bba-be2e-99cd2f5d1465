  <table border="0" cellpadding="3" cellspacing="3">
    <tr>
      <td class="labelbox"><a name="error_module_name"><label for="module_name"{if $messages->getErrors('module_name')} class="error"{/if}>{help label='module_name'}</label></a></td>
      <td class="required">{#required#}</td>
      <td>
        <select name="module_name" id="module_name" class="selbox{if !$available_action.options.add_modules} missing_records{elseif !$current_add_module} undefined{/if}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); changeModuleActions(this, 'action_name');" title="{#helps_module_name#|escape}">
          {if !empty($available_action.options.add_modules)}
            <option value="" class="undefined"{if !$current_add_module} selected="selected"{/if}>[{#please_select#|escape}]</option>
          {else}
            <option value="" class="missing_records"{if !$current_add_module} selected="selected"{/if}>[{#no_select_records#|escape}]</option>
          {/if}
          {foreach from=$available_action.options.add_modules item=add_module}
            <option value="{$add_module.option_value}"{if $add_module.option_value eq $current_add_module} selected="selected"{/if}>{$add_module.label|escape}</option>
          {/foreach}
        </select>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><a name="error_action_name"><label for="action_name"{if $messages->getErrors('action_name')} class="error"{/if}>{help label='action_name'}</label></a></td>
      <td class="required">{#required#}</td>
      <td>
        <select name="action_name" id="action_name" class="selbox missing_records" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="{#helps_action_name#|escape}">
          <option value="" selected="selected">[{#no_select_records#|escape}]</option>
        </select>
      </td>
    </tr>
    <tr>
      <td colspan="3">
        <button type="submit" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go" title="{$available_action.label}"{if $available_action.confirm} onclick="return confirmAction('{$available_action.name}', submitForm, this);"{/if}>{$available_action.options.label}</button>
      </td>
    </tr>
  </table>