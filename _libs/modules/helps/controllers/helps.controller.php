<?php

class Helps_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Help';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Helps';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit'
    );

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit', 'translate'
    );

    /**
     * Holds list of all modules
     */
    public $helpModules = array();

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_change_depending_options':
            $this->_changeDependingOptions();
            break;
        case 'search':
            $this->_search();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($request->isPost()) {
            $help = Helps::buildModel($this->registry);
            //build the model from the POST
            if ($help->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_helps_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_helps_add_failed'), '', -1);
            }
        } else {
            if (! $request->get('module_name') || ! $request->get('action_name')) {
                if (! $request->get('module_name')) {
                    // no module selected
                    $this->registry['messages']->setError($this->i18n('error_not_selected_module_name'), 'module_name');
                }
                if (! $request->get('action_name')) {
                    //no action selected
                    $this->registry['messages']->setError($this->i18n('error_not_selected_action_name'), 'action_name');
                }
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            } else {
                //create an empty help model
                $help = Helps::buildModel($this->registry);
            }
        }

        if (!empty($help)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('help', $help->sanitize());
        }

        return true;
    }

    /**
     * Edit a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $help = Helps::buildModel($this->registry);
            if ($help->save()) {
                //show message 'message_helps_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_helps_edit_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_helps_edit_failed'), '', -1);
                //register the model, with all the posted details
                $this->registry->set('help', $help);
            }

        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('h.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));

            $help = Helps::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($help);
        }

        if (!empty($help)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('help')) {
                $this->registry->set('help', $help->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_help'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters['where'] = array('h.id = ' . $id );
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }

        $help = Helps::searchOne($this->registry, $filters);

        if (!empty($help)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($help);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('help')) {
                $this->registry->set('help', $help->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_help'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Helps::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $help = Helps::buildModel($this->registry);

            if ($help->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_helps_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_helps_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('h.id = ' . $id), 'model_lang' => $request->get('model_lang'));
            $help = Helps::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($help);
        }

        if (!empty($help)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('help', $help->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_help'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete patterns
        $result = Helps::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Gets available actions for specified module
     */
    private function _changeDependingOptions() {
        $request = &$this->registry['request'];

        $module_name = $request->get('module_name');
        $records = array();
        if ($module_name) {
            // prepare help modules (get available actions only for specified $module_name)
            $this->getHelpModules($module_name);

            foreach ($this->helpModules[$module_name] as $action_name) {
                $records[] = array(
                    'option_value' => $action_name,
                    'label'        => Helps::getActionLabel($this->registry, $module_name, $action_name)
                );
            }

            usort($records, array('Helps', '_labelSort'));
        }

        print json_encode($records);
    }

    /**
     * Sets custom actions definitions
     *
     * @param array $action_defs - actions definitions
     * @return array - available actions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['add'])) {
            // prepare help modules
            $this->getHelpModules();

            //prepare modules
            $add_modules = array();
            foreach ($this->helpModules as $key => $actions_names) {
                $add_modules[] = array(
                    'label' => $this->i18n('menu_' . $key),
                    'option_value' => $key
                );
            }

            usort($add_modules, array('Helps', '_labelSort'));

            $actions['add']['options'] = array('label' => $this->i18n('add'));
            $actions['add']['options']['add_modules'] = $add_modules;
            $actions['add']['options']['add_actions'] = array();
            $actions['add']['ajax_no'] = 1;
            $actions['add']['template'] = PH_MODULES_DIR . 'helps/templates/' . '_action_add.html';
        }

        return $actions;
    }

    /**
     * Sets custom actions definitions
     *
     * @param array $action_defs - actions definitions
     * @return array - available actions
     */
    public function getActionOptions($action_name = '') {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions(array($action_name));

        //get permissions of the currently logged user
        $this->getUserPermissions();

        $_optgroups_add_modules = array();

        // prepare help modules
        $this->getHelpModules();

        foreach ($this->helpModules as $key => $actions_names) {
            $_optgroups_add_modules[] = array(
                'label' => $this->i18n('menu_' . $key),
                'option_value' => $key
            );
        }

        usort($_optgroups_add_modules, array('Helps', '_labelSort'));

        if (isset($actions['add']) && !empty($_optgroups_add_modules)) {
            //prepare add options
            $add_options = array (
                array (
                    'custom_id' => 'module_name_',
                    'name' => 'module_name',
                    'type' => 'dropdown',
                    'label' => $this->i18n('helps_module_name'),
                    'help' => $this->i18n('helps_module_name'),
                    'options' => $_optgroups_add_modules,
                    'value' => ($this->registry['request']->get('module_name')) ?
                                $this->registry['request']->get('module_name') : ''),
            );
            $actions['add']['options'] = $add_options;
        }

        return $actions;
    }

    /**
     * Sets custom after actions definitions
     *
     * @param array $action_defs - actions definitions
     * @return array - available actions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions($action_defs);

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['add'])) {
            //prepare modules and actions
            $add_modules = array();
            $add_actions = array();

            // prepare help modules
            $this->getHelpModules();

            foreach ($this->helpModules as $key => $actions_names) {
                $add_modules[] = array(
                    'label' => $this->i18n('menu_' . $key),
                    'option_value' => $key
                );
            }

            usort($add_modules, array('Helps', '_labelSort'));

            if (!empty($add_modules[0]['option_value'])) {
                foreach ($this->helpModules[$add_modules[0]['option_value']] as $action_name) {
                    $add_actions[] = array(
                        'option_value' => $action_name,
                        'label'        => Helps::getActionLabel($this->registry, $add_modules[0], $action_name)
                    );
                }

                usort($add_actions, array('Helps', '_labelSort'));

                $please_select_option = array(
                    'option_value' => '',
                    'label' => sprintf('[%s]', $this->i18n('please_select')),
                    'class_name' => 'undefined'
                );
                array_unshift($add_actions, $please_select_option);
            }

            $add_options = array(
                array (
                    'custom_id'     => 'module_name_____',
                    'name'          => 'aa1_module_name',
                    'type'          => 'dropdown',
                    'required'      => 1,
                    'label'         => $this->i18n('helps_module_name'),
                    'help'          => $this->i18n('helps_module_name'),
                    'options'       => $add_modules,
                    'onchange'      => 'changeModuleActions(this, \'action_name____\')'
                ),
                array(
                    'custom_id'     => 'action_name____',
                    'name'          => 'aa1_action_name',
                    'type'          => 'dropdown',
                    'required'      => 1,
                    'label'         => $this->i18n('helps_action_name'),
                    'help'          => $this->i18n('helps_action_name'),
                    'options'       => $add_actions,
                    'custom_class'  => 'undefined'
                )
            );

            $actions['add']['options'] = $add_options;
            $actions['add']['ajax_no'] = 1;
        } else {
            unset($actions['add']);
        }

        return $actions;
    }

    /**
     * Gets available actions that help texts can be added for
     * for specified module name or for all and sets them as property of controller.
     *
     * @param string $module - optional; module(+controller) name
     * @return array - array with results; module name as key, array of actions as value
     */
    public function getHelpModules($module = '') {

        if (empty($this->helpModules)) {
            $this->helpModules = Helps::getHelpModules($this->registry, $module);
        }

        return $this->helpModules;
    }
}

?>
