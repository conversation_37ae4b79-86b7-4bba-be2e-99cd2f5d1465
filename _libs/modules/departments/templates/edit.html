<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="departments" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$department->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$department->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$department->get('name')|escape}" title="{#departments_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_ancestor"><label for="ancestor"{if $messages->getErrors('ancestor')} class="error"{/if}>{help label='ancestor'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="ancestor" id="ancestor" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="dropdownTypingSearch(this, event);" title="{#departments_ancestor#|escape}">
{foreach from=$departments_tree item='item'}
              {if (!$item->isDeleted() && $item->isActivated()) || $item->get('id') eq $department->get('ancestor')}
              <option value="{$item->get('id')}"{if $item->get('id') eq $department->get('ancestor')} selected="selected"{/if}{if $item->isDeleted() || !$item->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}{if array_key_exists($item->get('id'), $departments_descendants)} disabled="disabled"{/if}>{$item->get('name')|indent:$item->get('level'):"-"}</option>
              {/if}
{/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_path"><label for="path"{if $messages->getErrors('path')} class="error"{/if}>{help label='path'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
{foreach from=$departments_parents item='item'}
           &raquo; {$item->get('name')|escape}
{/foreach}
          </td>
        </tr>
        {include file='input_text.html'
                 name='position'
                 custom_id='position'
                 standalone=false
                 required=0
                 value=$department->get('position')
                 restrict='insertOnlyDigits'
                 custom_class='short'
                 label=#departments_position#
                 help=#help_departments_position#
        }
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#departments_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$department->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_manager"{if $messages->getErrors('manager')} class="error"{/if}><label for="manager">{help label='manager'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox{if !$department->get('manager')} undefined{/if}" name="manager" id="manager" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="{#departments_manager#|escape}">
              <option value="" class="undefined">[{#please_select#|escape}]</option>
{foreach from=$users item='user'}
              <option value="{$user.id}"{if $user.id eq $department->get('manager')} selected="selected"{/if}>{$user.firstname|escape} {$user.lastname|escape}</option>
{/foreach}
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='users'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $department->get('users')}{$department->get('users')|@count}{else}0{/if}
            <input type="hidden" name="count_users" id="count_users" value="{if $department->get('users')}{$department->get('users')|@count}{else}0{/if}" />
          </td>
        </tr>
{include file=`$templatesDir`_users.html}
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$department}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
