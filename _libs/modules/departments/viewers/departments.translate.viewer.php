<?php

class Departments_Translate_Viewer extends Viewer {
    public $template = 'translate.html';

    public function prepare() {
        $this->model = $this->registry['department'];
        $this->data['department'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s', 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare users to select the manager
        $filters['where'] = array('d1.id = ' . $this->model->get('id'));
        $this->data['users'] = Departments::getUsers($this->registry, $filters);

        //prepare department tree
        require_once(PH_MODULES_DIR . 'departments/models/departments.factory.php');
        $this->data['departments_tree'] = Departments::getTree($this->registry);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare descendants
        $this->data['departments_descendants'] = Departments::getTreeDescendants($this->registry, array('where' => array('d1.id = ' . $this->model->get('id')),
                                                                                                        'sanitize' => true));
        //prepare parents
        $this->data['departments_parents'] = Departments::getTreeParents($this->registry, $this->model->get('id'));

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('d.id = ' . $this->model->get('id')),
                         'model_lang' => $basic_model_lang,
                         'sanitize' => true);
        $this->data['base_model'] = Departments::searchOne($this->registry, $filters);

    }

    public function prepareTitleBar() {
        $title = $this->i18n('departments');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('departments_translate');
        $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s&amp;model_lang=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'), $this->model->get('model_lang'));

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }

}

?>
