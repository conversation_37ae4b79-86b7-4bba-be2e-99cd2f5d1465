<?php

class Departments_Add_Viewer extends Viewer {
    public $template = 'add.html';

    public function prepare() {
        $this->model = $this->registry['department'];
        $this->data['department'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s', 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare department tree
        require_once(PH_MODULES_DIR . 'departments/models/departments.factory.php');
        $departments_tree = Departments::getTree($this->registry);
        $this->data['departments_tree'] = Users::sanitizeModels($departments_tree);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);


    }

    public function prepareTitleBar() {
        $title = $this->i18n('departments');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('departments_add_new');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action);

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
