<?php

class Finance_Companies_Offices_Viewer extends Viewer {
    public $template = 'companies_offices.html';

    public function prepare() {
        $this->model = $this->registry['finance_company'];
        $this->data['company'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare offices
        require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'));
        $offices = Offices::search($this->registry, $filters);
        $_options_offices = array();
        foreach($offices as $office) {
            $_options_offices[] = array(
                'active_option' => $office->get('active'),
                'label' => $office->get('name'),
                'option_value' => $office->get('id'));
        }
        $offices = array (
            'custom_id' => 'offices',
            'name' => 'offices',
            'type' => 'dropdown',
            'label' => $this->i18n('company_office'),
            'help' => $this->i18n('company_office'),
            'options' => $_options_offices,
            'value' => $this->model->get('office'),
            //if the document has not number and the counter requires office code, then make the office required field
            'required' => (!$this->model->isActivated() && $this->model->counter->get('office_code')) ? 1 : 0);
        $this->data['offices'] = $offices;
        if ($this->model->get('offices')) {
            $company_offices = $this->model->get('offices');
        } else {
            $company_offices = array(0);
        }
        $this->data['company_offices'] = $company_offices;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_companies_offices_company');
        $this->data['title'] = $title;
    }
}

?>
