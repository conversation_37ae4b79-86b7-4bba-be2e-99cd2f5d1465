<?php

class Finance_Transfers_Audit_Viewer extends Viewer {

    public $template = '_audit.html';

    public function prepare() {
        require_once PH_MODULES_DIR . 'finance/models/finance.transfers.audit.php';

        // prepare model audit
        $history = Finance_Transfers_History::getData(
            $this->registry,
            array(
                'h_id' => $this->registry['request']->get('audit'),
                'model' => 'Finance_Transfer'
            ));
        if ($history) {
            $this->data['audit'] = Finance_Transfers_Audit::getData(
                $this->registry,
                array(
                    'parent_id' => $this->registry['request']->get('audit'),
                    'model_name' => 'Finance_Transfer',
                    'action_type' => $history[0]['action_type']
                ));
            $this->data['audit_title'] = $this->i18n('audit_vars', array(
                $this->i18n('finance_transfer_audit')
            ));
            $this->data['audit_legend'] = $this->i18n('audit_legend', array(
                $history[0]['user_name'],
                date('d.m.Y, H:i', strtotime($history[0]['h_date']))
            ));
        }

        $this->setFrameset('frameset_blank.html');
    }
}

?>
