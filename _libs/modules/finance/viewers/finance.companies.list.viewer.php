<?php

class Finance_Companies_List_Viewer extends Viewer {
    public $template = 'companies_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.companies.factory.php';

        $filters = Finance_Companies::saveSearchParams($this->registry);

        list($companies, $pagination) = Finance_Companies::pagedSearch($this->registry, $filters);
        $this->models = $this->data['companies'] = $companies;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_companies');
        $this->data['title'] = $title;
    }
}

?>
