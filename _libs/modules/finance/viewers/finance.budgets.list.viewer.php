<?php

class Finance_Budgets_List_Viewer extends Viewer {
    public $template = 'budgets_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.budgets.factory.php';

        //view only models with allowed cashboxes and bank accounts - ????
        $filters = Finance_Budgets::saveSearchParams($this->registry);

        list($finance_budgets, $pagination) = Finance_Budgets::pagedSearch($this->registry, $filters);

        $this->data['finance_budgets'] = $finance_budgets;
        $this->data['pagination'] = $pagination;

        //action that will be executed on model when its table row is clicked
        $this->data['row_link_action'] = $this->registry['config']->getParam('finance_budgets', 'row_link_action');

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_budgets');
        $this->data['title'] = $title;
    }
}

?>
