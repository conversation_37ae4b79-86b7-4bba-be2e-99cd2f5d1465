<?php

class Finance_Repayment_Plans_Add_Viewer extends Viewer {
    public $template = 'repayment_plans_add.html';

    public function prepare() {
        $this->model = $this->registry['finance_repayment_plan'];
        $this->data['repayment_plan'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        //prepare companies
        require_once $this->modelsDir . 'finance.companies.factory.php';
        $filters = array('model_lang' => $this->model->get('lang'),
                         'sanitize' => true);
        $companies = Finance_Companies::search($this->registry, $filters);
        $this->data['companies'] = $companies;

        if (empty($companies)) {
            $controller = ucfirst($this->module) . '_' .
                implode('_', array_map('ucfirst', explode('_', $this->controller))) . '_Controller';
            $controller = new $controller($this->registry);
            $this->registry['messages']->setError($this->i18n('error_finance_no_companies'));
            $this->registry['messages']->insertInSession($this->registry);
            $controller->redirect($this->module, 'list', '', $this->controller);
        }

        //prepare customer
        if ($customer_id = $this->model->get('customer')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->data['repayment_plan']->set('customer_code', $customer->get('code'), true);
                $this->data['repayment_plan']->set('customer_name', $customer_name, true);
                $this->data['repayment_plan']->set('customer', $customer_id, true);
                $this->data['repayment_plan']->set('customer_is_company', $customer->get('is_company'), true);
            }
        }

        $this->data['shared_amount'] = 0;
        $this->data['unshared_amount'] = sprintf('%.2f', $this->model->get('all_amount') - $this->data['shared_amount']);

        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        $currencies = Finance_Currencies::getAvailableCurrencies($this->registry);
        $this->data['currencies'] = $currencies;

        if ($this->registry['configGroupPatterns']) {
            $this->data['configGroupPatterns'] = $this->registry['configGroupPatterns'];
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_repayment_plans_add');
        $this->data['title'] = $title;
    }
}

?>
