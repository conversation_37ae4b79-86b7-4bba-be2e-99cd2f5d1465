<?php

class Finance_Counters_Add_Viewer extends Viewer {
    public $template = 'counters_add.html';

    public function prepare() {
        $this->model = $this->registry['finance_counter'];
        $this->data['counter'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $model_name = $this->model->get('model') ? $this->model->get('model') : 'Finance_Incomes_Reason';

        if (preg_match('#^Finance_((Incomes|Expenses)_Reason|Warehouses_Document|Annulment)$#', $model_name)) {
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeTypes = Finance_Documents_Types::search($this->registry,
                                                            array('where' => array('fdt.model="' . $model_name . '"')));
            $options = array();
            foreach ($financeTypes as $type) {
                $options[] = array('label' => $type->get('name'),
                                   'option_value' => $type->get('id'),
                                   'active_option' => $type->get('active'));
            }
            $this->data['document_types'] = $options;
            unset($financeTypes);
        }

        // prepare companies
        require_once $this->modelsDir . 'finance.companies.factory.php';
        $filters = array('sanitize' => true,
                         'model_lang' => $this->model->get('model_lang'),
                         'where' => array('fc.active=1'));
        $available_companies = Finance_Companies::search($this->registry, $filters);
        $companies = array();
        foreach ($available_companies as $company) {
            $companies[] = array(
                'option_value'  => $company->get('id'),
                'label'         => $company->get('name'),
                'active_option' => $company->get('active')
            );
        }
        $this->data['companies'] = $companies;

        if (empty($companies)) {
            $controller = ucfirst($this->module) . '_' .
                implode('_', array_map('ucfirst', explode('_', $this->controller))) . '_Controller';
            $controller = new $controller($this->registry);
            $this->registry['messages']->setError($this->i18n('error_finance_no_companies'));
            $this->registry['messages']->insertInSession($this->registry);
            $controller->redirect($this->module, 'list', '', $this->controller);
        }

        $company = '';
        if ($this->model->get('company')) {
            $company = $this->model->get('company');
        } elseif ($this->registry['currentUser']->get('default_company')) {
            foreach ($companies as $cmp) {
                if ($cmp['option_value'] == $this->registry['currentUser']->get('default_company')) {
                    $company = $cmp['option_value'];
                    $this->model->set('company', $company, true);
                    break;
                }
            }
        }
        if (!$company && count($companies) == 1) {
            $company = $companies[0]['option_value'];
            $this->model->set('company', $company, true);
        }

        require_once $this->modelsDir . 'finance.dropdown.php';
        // prepare offices
        //get depending offices
        $params = array('company' => $company, 'lang' => $this->model->get('model_lang'));
        require_once PH_MODULES_DIR . 'finance/models/finance.counters.factory.php';
        $offices = Finance_Counters::getCountersOffices($this->registry, $params);
        $this->data['offices'] = $offices;
        $this->data['first_option'] = array(
            'label' => $this->i18n('finance_counters_bank_account_cashbox_independent'),
            'option_value' => '0',
            'active_option' => true);

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_counters_add');
        $this->data['title'] = $title;
    }
}

?>
