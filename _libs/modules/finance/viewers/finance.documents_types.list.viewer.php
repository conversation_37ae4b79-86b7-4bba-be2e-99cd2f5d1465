<?php

class Finance_Documents_Types_List_Viewer extends Viewer {
    public $template = 'documents_types_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.documents_types.factory.php';
        $filters = Finance_Documents_Types::saveSearchParams($this->registry);
        $filters['sanitize'] = true;
        list($finance_documents_types, $pagination) = Finance_Documents_Types::pagedSearch($this->registry, $filters);

        $this->data['finance_documents_types'] = $finance_documents_types;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_documents_types');
        $this->data['title'] = $title;
    }
}

?>
