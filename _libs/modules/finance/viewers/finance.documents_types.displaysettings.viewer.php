<?php

class Finance_Documents_Types_Displaysettings_Viewer extends Viewer {
    public $template = 'documents_types_settings.html';

    public function prepare() {

        //prepare controls options
        $controls_options = array();
        $controls_options['alignment']      = Dropdown::getHTMLAllignments(array($this->registry));
        $controls_options['hidden']         = Dropdown::getGT2DependAll(array($this->registry));
        $controls_options['readonly']       = Dropdown::getGT2DependAll(array($this->registry));
        $controls_options['printable']      = Dropdown::getYesNo(array($this->registry));
        $controls_options['required']       = Dropdown::getRequiredOptions(array($this->registry));
        $controls_options['html_measures']  = Dropdown::getHTMLMeasures();
        $controls_options['type']           = Dropdown::getVarsTypes(array($this->registry));
        $controls_options['js_filter']      = Dropdown::getVarsValidateFilters();
        $controls_options['agregate']       = Dropdown::getGT2Agregates(array($this->registry));

        $this->data['controls_options']     = $controls_options;

        //set supported langs for the models
        $this->data['model_langs'] = $this->registry['config']->getParamAsArray('i18n', 'model_langs');

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups_tree'] = array_values(Groups::getTree($this->registry));
        $this->registry['include_tree'] = true;

        if ($this->isMain) {
            $this->data['settings_types'] = Dropdown::getGT2Types(array($this->registry));

            //set submit link
            $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'], $this->module,
                                $this->registry['controller_param'], $this->controller,
                                $this->registry['action_param'], $this->action,
                                $this->action, $this->model->get('id'));
            $this->data['submitLink'] = $this->submitLink;

            $this->prepareTranslations();

            $this->prepareTitleBar();
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_documents_types_edit');
        $this->data['title'] = $title;
    }
}

?>
