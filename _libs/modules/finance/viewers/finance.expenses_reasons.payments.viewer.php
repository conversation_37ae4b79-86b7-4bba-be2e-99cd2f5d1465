<?php

class Finance_Expenses_Reasons_Payments_Viewer extends Viewer {
    public $template = 'expenses_reasons_payments.html';

    public function prepare() {
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s&amp;selected_tab=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'),
                            $this->model->get('selected_tab'));
        $this->data['submitLink'] = $this->submitLink;

        // get the total label
        $query = 'SELECT fi.content FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fi ' . "\n" .
                 '  ON (fm.id=fi.parent_id AND content_type="label" AND fi.lang="' . $this->model->get('lang') . '")' . "\n" .
                 'WHERE fm.model="' . $this->model->modelName . '" AND fm.model_type=' . $this->model->get('type') . ' AND fm.name="total_with_vat"' . "\n";
        $this->data['total_with_vat_label'] = $this->registry['db']->GetOne($query);

        $this->data['selected_tab'] = $this->model->get('selected_tab');

        if ($this->registry['request']->get('use_ajax') == 1) {
            $this->setFrameset(PH_MODULES_DIR . 'finance/templates/_expenses_reasons_payments.html');
            $this->prepareHelpPanel();
        } else {
            $this->prepareTranslations();
            $this->prepareTitleBar();
        }
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('finance_expenses_reasons_payments'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
