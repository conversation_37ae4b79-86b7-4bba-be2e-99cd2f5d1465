<?php

class Finance_Recurring_Payments_Audit_Viewer extends Viewer {

    public $template = '_audit.html';

    public function prepare() {
        require_once PH_MODULES_DIR . 'finance/models/finance.recurring_payments.audit.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.recurring_payments.history.php';

        // prepare model audit
        $history = Finance_Recurring_Payments_History::getData(
            $this->registry,
            array(
                'h_id' => $this->registry['request']->get('audit'),
                'model' => 'Finance_Recurring_Payment'
            ));
        if ($history) {
            $filters = array(
                'where' => array(
                    'frp.id = ' . $history[0]['model_id']
                ),
                'sanitize' => true
            );
            $model = Finance_Recurring_Payments::searchOne($this->registry, $filters);
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeType = Finance_Documents_Types::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        'fdt.id=' . $model->get('type')
                    )
                ));
            $model->set('gt2_model_name', $financeType->get('model'), true);

            $this->data['audit'] = Finance_Recurring_Payments_Audit::getData(
                $this->registry,
                array(
                    'parent_id' => $this->registry['request']->get('audit'),
                    'model_name' => $model->modelName,
                    'gt2_model_name' => $model->get('gt2_model_name'),
                    'model_type' => $model->get('type'),
                    'action_type' => $history[0]['action_type']
                ));
            $this->data['audit_title'] = $this->i18n('audit_vars', array(
                $this->i18n('finance_recurring_payment_audit')
            ));
            $this->data['audit_legend'] = $this->i18n('audit_legend', array(
                $history[0]['user_name'],
                date('d.m.Y, H:i', strtotime($history[0]['h_date']))
            ));
        }

        $this->setFrameset('frameset_blank.html');
    }
}

?>
