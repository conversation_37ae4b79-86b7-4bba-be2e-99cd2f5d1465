<?php

class Finance_Warehouses_Documents_Search_Viewer extends Viewer {
    public $template = 'warehouses_documents_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.warehouses_documents.factory.php';

        $filters = Finance_Warehouses_Documents::saveSearchParams($this->registry, array(), 'search_');

        $customize = $customize_company = array();
        $found = 0;
        if (!empty($filters['where'])) {
            foreach ($filters['where'] as $where) {
                if (preg_match('/fwd\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/fwd\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'type', 'value' => $val);
                    $found++;

                    //get type for multi actions
                    $type = $val;
                }
                if (preg_match('/fdt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/fdt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'section', 'value' => $val);
                    $found++;
                }
                if (preg_match('/fwd\.company\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/fwd\.company\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize_company[] = $val;
                }
            }
        } elseif (!empty($filters['hidden_type'])) {
            $customize = array('name' => 'type', 'value' => $filters['hidden_type']);
            $found++;
        } elseif (!empty($filters['hidden_type_section'])) {
            $customize = array('name' => 'section', 'value' => $filters['hidden_type_section']);
            $found++;
        }
        if ($found == 1 && $customize) {
            $this->setCustomTemplate($customize);
        } else {
            $this->setCustomTemplate();
        }
        if ($found > 1) {
            unset($type);
        }

        // modelFields contains visible columns
        if (!empty($this->modelFields)) {

            $filters['get_fields'] = $this->modelFields;

            if (in_array('tags', $this->modelFields)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }

            $assignment_fields = array('owner', 'responsible', 'observer', 'decision');
            if (array_intersect($assignment_fields, $this->modelFields)) {
                //set flag to get assignments for current model
                $this->registry->set('getAssignments', true, true);
            }
        }

        list($finance_warehouses_documents, $pagination) = Finance_Warehouses_Documents::pagedSearch($this->registry, $filters);

        $this->data['finance_warehouses_documents'] = $finance_warehouses_documents;
        $this->data['pagination'] = $pagination;

        // prepare available tags for multitagging
        $this->prepareMultitagOptions(!empty($type) ? $type : null);

        if (!empty($type)) {
            // prepare print patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array('where' => array(
                                              'p.model = \'Finance_Warehouses_Document\'',
                                              'p.model_type = \'' . $type . '\'',
                                              'p.active = 1',
                                              'p.format="pdf"',
                                              'p.list = 0'),
                                      'sort' => array('p.position != 0 DESC', 'p.company ASC', 'p.position ASC', 'p.id ASC'),
                                      'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            if (count($customize_company) == 1) {
                $filters_patterns['where'][] = '(p.company=\'' . $customize_company[0] . '\' OR p.company=\'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters_patterns);

            $patterns_grouped = array();
            foreach ($patterns as $pattern) {
                $cn = $pattern->get('company_name') ?: '';
                if (!isset($patterns_grouped[$cn])) {
                    $patterns_grouped[$cn] = array();
                }
                $patterns_grouped[$cn][] = array(
                    'id'   => $pattern->get('id'),
                    'name' => $pattern->get('name')
                );
            }

            if (count($patterns_grouped) > 1 && array_key_exists('', $patterns_grouped)) {
                $patterns_grouped = array($this->i18n('finance_documents_types_all_companies') => $patterns_grouped['']) + $patterns_grouped;
                unset($patterns_grouped['']);
            }
            $this->data['patterns_grouped'] = $patterns_grouped;
        }

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        if (isset($this->title)) {
            $title = $this->title;
        } else {
            $title = $this->i18n('finance_warehouses_documents');
        }
        if (isset($this->subtitle)) {
            $subtitle = $this->subtitle;
        } else {
            $subtitle = '';
        }

        $this->data['title'] = $title;
        $this->data['subtitle'] = $subtitle;
    }
}

?>
