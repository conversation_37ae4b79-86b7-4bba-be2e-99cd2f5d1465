<?php

class Finance_Invoices_Templates_List_Viewer extends Viewer {
    public $template = 'invoices_templates_list.html';
    public $filters = array();

    public function prepare() {

        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';

        $filters = Finance_Invoices_Templates::saveSearchParams($this->registry);
        //ignore invoices that has background work at the moment
        $filters['where'][] = 'fit.issue_lock = "0000-00-00 00:00:00"';
        list($templates, $pagination) = Finance_Invoices_Templates::pagedSearch($this->registry, $filters);
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        //get all the patterns for invoices (used for the PDF preview)
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('(p.model = "Finance_Incomes_Reason" AND ' .
                                          '(p.model_type = \'' . PH_FINANCE_TYPE_INVOICE . '\' OR p.model_type = \'' . PH_FINANCE_TYPE_PRO_INVOICE . '\') OR ' .
                                          'p.model = "Finance_Expenses_Reason" AND ' .
                                          'CONVERT(p.model_type, SIGNED INTEGER) > \'' . PH_FINANCE_TYPE_MAX . '\')',
                                          'p.active = 1',
                                          'p.list = 0'),
                         'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                         'sanitize' => true);
        $patterns = Patterns::search($this->registry, $filters);
        $this->data['patterns'] = array();
        foreach($patterns as $pattern) {
            if ($pattern->get('model_type') == PH_FINANCE_TYPE_INVOICE) {
                $this->data['patterns']['invoice'][] = array('id'            => $pattern->get('id'),
                                                             'name'          => $pattern->get('name'),
                                                             'for_printform' => $pattern->get('for_printform'),
                                                             'company'       => $pattern->get('company'));
            } elseif ($pattern->get('model_type') == PH_FINANCE_TYPE_PRO_INVOICE) {
                $this->data['patterns']['proforma_invoice'][] = array('id'            => $pattern->get('id'),
                                                                      'name'          => $pattern->get('name'),
                                                                      'for_printform' => $pattern->get('for_printform'),
                                                                      'company'       => $pattern->get('company'));
            } elseif ($pattern->get('model_type') > PH_FINANCE_TYPE_MAX) {
                $this->data['patterns'][$pattern->get('model_type')][] = array('id'            => $pattern->get('id'),
                                                                               'name'          => $pattern->get('name'),
                                                                               'for_printform' => $pattern->get('for_printform'),
                                                                               'company'       => $pattern->get('company'));
            }
        }

        //get a copy of messages as during the invoice issue they will be flushed
        $messages = clone $this->registry['messages'];
        $this->registry['messages']->flush();
        //prepare gt2 tables for invoices
        foreach ($templates as $key => $template) {
            $template_id = $template->get('id');
            $db = $this->registry['db'];
            $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                     '  ON fit.id = fiti.parent_id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                     '  ON co.id = fit.contract_id' . "\n" .
                     'WHERE fiti.invoice_id = 0' . "\n" .
                     '  AND co.subtype="contract" AND co.status="closed"' . "\n" .
                     '  AND fit.deleted_by = 0' . "\n" .
                     '  AND fiti.id=' . $template_id;
            $records = $db->GetAssoc($query);

            if (!empty($records)) {
                require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
                $results = Finance_Invoices_Templates::issueInvoices($this->registry, $records, true);

                $info = reset($results);

                //check if any errors occurred
                if (!empty($info['errors'])) {
                    //get errors for contract
                    $info['errors'] = array_shift($info['errors']);
                    //remove first error for the contract num info only
                    array_shift($info['errors']);
                    $this->data['tpl_errors'][$key] = $info['errors'];
                    continue;
                }

                //there should be only one invoice in the results array, but just to be sure loop them all
                foreach($info['invoices'] as $invoice) {
                    if (is_object($invoice)) {
                        $gt2 = $invoice->get('grouping_table_2');
                        $templates[$key]->set('grouping_table_2', $gt2, true);
                        if (empty($gt2['plain_values'])) {
                            $gt2['plain_values']['total'] = $gt2['plain_values']['total_with_vat'] = $gt2['plain_values']['currency'] = '';
                        }
                        $templates[$key]->set('total', $gt2['plain_values']['total'], true);
                        $templates[$key]->set('total_with_vat', $gt2['plain_values']['total_with_vat'], true);
                        $templates[$key]->set('currency', $gt2['plain_values']['currency'], true);
                    }
                }
            }
        }
        //restore original messages
        $this->data['messages'] = $messages;

        $this->data['templates'] = $templates;

        // if the "issue" buttons should be enabled or not
        $this->data['issue_access'] = Finance_Invoices_Templates::checkIssueInvoicesAccess($this->registry);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_invoices_templates');
        $this->data['title'] = $title;
    }
}

?>
