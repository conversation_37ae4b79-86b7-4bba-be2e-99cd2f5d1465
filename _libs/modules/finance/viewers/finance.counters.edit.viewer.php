<?php

class Finance_Counters_Edit_Viewer extends Viewer {
    public $template = 'counters_edit.html';

    public function prepare() {
        $this->model = $this->registry['finance_counter'];
        $this->data['counter'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        if (preg_match('#^Finance_((Incomes|Expenses)_Reason|Warehouses_Document|Annulment)$#', $this->model->get('model'))) {
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeType = Finance_Documents_Types::searchOne($this->registry,
                                                              array('where' => array('fdt.id="' . $this->model->get('model_type') . '"'),
                                                                    'sanitize' => true));
            $type_name = $financeType->get('name');
            unset($financeType);
        } elseif ($this->model->get('model') == 'Finance_Payment') {
            $type_name = $this->i18n('finance_counters_model_type_' . $this->model->get('model_type'));
        } elseif ($this->model->get('model') == 'Finance_Transfer') {
            $type_name = $this->i18n('finance_counters_model_type_transfer');
        }
        $this->data['type_name'] = $type_name;

        // prepare companies
        require_once $this->modelsDir . 'finance.companies.factory.php';
        $filters = array('sanitize' => true,
                         'model_lang' => $this->model->get('model_lang'));
        $available_companies = Finance_Companies::search($this->registry, $filters);
        $companies = array();
        foreach ($available_companies as $company) {
            $companies[] = array(
                'option_value'  => $company->get('id'),
                'label'         => $company->get('name'),
                'active_option' => $company->get('active')
            );
        }
        $this->data['companies'] = $companies;

        $company = $this->model->get('company');

        require_once $this->modelsDir . 'finance.dropdown.php';

        // prepare offices
        require_once $this->modelsDir . 'finance.dropdown.php';
        // prepare offices
        //get depending offices
        $params = array('company' => $company, 'lang' => $this->model->get('model_lang'));
        require_once PH_MODULES_DIR . 'finance/models/finance.counters.factory.php';
        $offices = Finance_Counters::getCountersOffices($this->registry, $params);
        $this->data['offices'] = $offices;
        $this->data['first_option'] = array(
            'label' => $this->i18n('finance_counters_bank_account_cashbox_independent'),
            'option_value' => '0',
            'active_option' => true);
        $this->data['offices'] = $offices;

        // prepare containers
        $_optgroups = array();
        if ($company) {
            $office = $this->model->get('office');

            $params = array($this->registry,
                            'lang' => $this->model->get('model_lang'),
                            'company_id' => $company,
                            'office_id' => $office);
            $cashboxes = Finance_Dropdown::getCashboxes($params);

            $params = array($this->registry,
                            'lang' => $this->model->get('model_lang'),
                            'company_id' => $company,
                            'office_id' => $office);
            $bank_accounts = Finance_Dropdown::getBankAccounts($params);

            foreach($cashboxes as $cashbox) {
                // Define OPTGROUP parameter
                $_optgroups[$this->i18n('finance_counters_cashbox')][] = array(
                    'label' => $cashbox['label'],
                    'option_value' => 'cashbox_' . $cashbox['option_value'],
                    'active_option' => $cashbox['active_option']);
            }

            foreach($bank_accounts as $bank_account) {
                // Define OPTGROUP parameter
                $_optgroups[$this->i18n('finance_counters_bank_account')][] = array(
                    'label' => $bank_account['label'],
                    'option_value' => 'bank_account_' . $bank_account['option_value'],
                    'active_option' => $bank_account['active_option']);
            }

            $first_option_set = array(
                'label' => $this->i18n('finance_counters_bank_account_cashbox_independent'),
                'option_value' => '0',
                'active_option' => true);
            $this->data['first_option_set'] = $first_option_set;
        }
        $this->data['containers'] = $_optgroups;

        $this->data['container_id'] =
            strpos($this->model->get('container_id'), '_') ?
            strpos($this->model->get('container_id'), '_') :
            $this->model->get('container_type') . '_' . $this->model->get('container_id');

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_counters_edit');
        $this->data['title'] = $title;
    }
}

?>
