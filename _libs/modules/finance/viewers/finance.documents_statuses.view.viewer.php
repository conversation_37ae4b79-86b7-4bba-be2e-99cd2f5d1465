<?php

class Finance_Documents_Statuses_View_Viewer extends Viewer {
    public $template = 'documents_statuses_view.html';

    public function prepare() {
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array('g.id = ' . $this->model->get('group'),
                                              'g.deleted IS NOT NULL'));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }

        //prepare documents types
        require_once(PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php');
        $filters = array(
            'sanitize'  => true,
            'where'     => array('fdt.id="' . $this->model->get('doc_type') . '"')
        );
        $doc_type = Finance_Documents_Types::searchOne($this->registry,$filters);
        $model_name = General::singular2plural(strtolower($doc_type->get('model')));
        $model_name = $this->registry['translater']->translate($model_name);
        $this->data['type_name'] = $model_name . ' - ' . $doc_type->get('name');

        $statuses = array('opened' => $this->i18n('finance_documents_status_opened'),
                          'locked' => $this->i18n('finance_documents_status_locked'),
                          'finished' => $this->i18n('finance_documents_status_finished'));
        $this->data['status_name'] = $statuses[$this->model->get('status')];

        $this->prepareTranslations();

        $this->prepareTitleBar();

    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_documents_statuses_view');
        $this->data['title'] = $title;
    }
}

?>
