<?php

class Finance_Analysis_Items_View_Viewer extends Viewer {
    public $template = 'analysis_items_view.html';

    /**
     * The fields which will switch the additional variables in the search panel
     */
    private $_searchAdditionalVarsSwitch = array('nomenclatures' => 'n.type',
                                                 'customers' => 'c.type',
                                                 'projects' => 'p.type');

    public function prepare() {
        $registry = &$this->registry;
        $request = &$registry['request'];

        $this->model = $this->registry['finance_analysis_item'];
        $this->data['analysis_item'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        //prepare group tree
        //require_once(PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php');
        $analysis_items_tree = Finance_Analysis_Items::getTree($this->registry,
                                    array('where'=>array(
                                        'fai.type="' . $this->model->get('type') . '"')));
        $this->data['analysis_items'] = Finance_Analysis_Items::sanitizeModels($analysis_items_tree);

        $elements_factory = $this->model->get('elements_factory');
        if ($elements_factory) {
            // get module (and controller) from elements factory
            $matches = preg_split('/_/', $elements_factory, 2);
            $module = $matches[0];
            $controller = isset($matches[1]) ? $matches[1] : $matches[0];
            $params = array('module' => $module, 'controller' => $controller);
            //set variable to be used for search filters
            $this->data['module_name'] = $module . '_' . $controller;

            // set temporary routing parameters while processing saved search params
            $real_action = $this->registry['action'];

            $this->registry->set('action', 'getoptions', true);

            $session_filters = $this->model->get('elements_filters');
            unset($this->model->properties['elements_filters']);

            //get search definitions for selected module and controller from the data base
            require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');

            $switch_additional = isset($this->_searchAdditionalVarsSwitch[$module]) ?
                                       $this->_searchAdditionalVarsSwitch[$module] : false;

            // extract values of model types and names of all other search fields from session filters
            $params = $params + Filters::extractSearchFields($session_filters);

            //prepare advanced search definitions
            $registry['translater']->loadFile(PH_MODULES_DIR . $module . '/i18n/' . $registry['lang'] . '/' . $module . '.ini');
            list($advanced_search, $system_fields, $saved_filters) = Filters::getAdvancedSearchDefinitions($registry, $params);

            //prepare the basic sort definitions for optgroup
            $system_fields['sort'] = array('basic_vars' => $system_fields['sort']);

            //get additional variables if session filters contain model types and module has additional vars
            $additional_search = array();
            if (!empty($params['model_types']) && $switch_additional) {
                $request->set('model_types', $params['model_types'], 'all', true);
                $additional_search = Filters::getAdditionalSearchDefs($registry, $params);

                $additional_sortables = Filters::getAdditionalSortDefs($registry, array('module' => $module));
                if ($additional_sortables) {
                    $system_fields['sort']['additional_vars'] = $additional_sortables;
                }

                $request->remove('model_types');
            }

            if (!empty($system_fields['sort']['basic_vars'])) {
                uasort($system_fields['sort']['basic_vars'], array('Filters', 'searchSort'));
            }
            if (!empty($system_fields['sort']['additional_vars'])) {
                uasort($system_fields['sort']['additional_vars'], array('Filters', 'searchSort'));
            }

            // restore routing parameters
            $this->registry->set('action', $real_action, true);

            $this->data['search_fields']['basic_vars'] = $advanced_search;
            $this->data['advanced_search_options'] = json_encode($advanced_search);
            $this->data['system_fields'] = $system_fields;
            $this->data['switch_additional'] = $switch_additional;
            $this->data['search_fields']['additional_vars'] = $additional_search;
            $this->data['additional_search_options'] = json_encode($additional_search);
            $this->data['saved_filters'] = $saved_filters;

            $this->data['params'] = $params;
            $this->data['session_filters'] = $session_filters;
        }

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array('g.id = ' . $this->model->get('group'),
                                              'g.deleted IS NOT NULL'));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('finance_analysis_items_view_' . $this->model->get('type'));
    }
}

?>
