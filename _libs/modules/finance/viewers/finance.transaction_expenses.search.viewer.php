<?php

class Finance_Transaction_Expenses_Search_Viewer extends Viewer {
    public $template = 'transaction_expenses_list.html';
    public $filters = array();


    public function prepare() {
        require_once $this->modelsDir . 'finance.transaction_expenses.factory.php';

        $filters = Finance_Transaction_Expenses::saveSearchParams($this->registry, array(), 'search_');

        list($transaction_expenses, $pagination) = Finance_Transaction_Expenses::pagedSearch($this->registry, $filters);
        $this->data['transaction_expenses'] = $transaction_expenses;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_transaction_expenses');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
