<?php

class Finance_Recurring_Payments_Add_Viewer extends Viewer {
    public $template = 'recurring_payments_add.html';

    public function prepare() {
        $this->model = $this->registry['finance_recurring_payment'];
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        //prepare companies, offices, cashboxes/bank accounts
        require_once $this->modelsDir . 'finance.dropdown.php';
        $params = array($this->registry,
                        'lang' => $this->model->get('model_lang'),
                        //'company_id' => $this->model->get('company'),
                        'payment_direction' => ($this->model->get('reason_type') == 'Finance_Incomes_Reason' ? 'incomes' : 'expenses'),
                        'active' => 1,
                        'payment_type' => $this->model->get('type_payment_way')
        );
        $this->data['companies_data'] = Finance_Dropdown::getCompaniesData($params);

        //prepare customer
        $customer_id = 0;
        if ($this->model->get('customer')) {
            $customer_id = $this->model->get('customer');
        }

        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->model->set('customer_code', $customer->get('code'), true);
                $this->model->set('customer_name', $customer_name, true);
                $this->model->set('customer', $customer_id, true);
                $this->model->set('customer_is_company', $customer->get('is_company'), true);
                if (!$this->model->get('trademark')) {
                    $this->model->set('trademark', $customer->get('main_trademark'), true);
                }
            }
        }
        if (!$customer_id) {
            // clear all related properties
            $this->model->set('customer_code', '', true);
            $this->model->set('customer_name', '', true);
            $this->model->set('customer', '', true);
            $this->model->set('customer_is_company', '', true);
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare trademark
        $trademark_id = $this->model->get('trademark');
        if ($trademark_id) {
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array(
                'sanitize' => true,
                'model_lang' => $this->model->get('model_lang'),
                'where' => array(
                    'n.deleted IS NOT NULL',
                    'n.id = ' . $trademark_id
                ),
                //flag to search in customers trademarks
                'session_param' => 'filter_trademark_nomenclature'
            );
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->model->set('trademark', $trademark_id, true);
                $this->model->set('trademark_name', $nomenclature->get('name'), true);
            } else {
                $trademark_id = 0;
            }
        }
        if (!$trademark_id) {
            // clear all related properties
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare project
        if ($this->model->get('project')) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('p.id = ' . $this->model->get('project'),
                                              'p.deleted IS NOT NULL'));
            $project = Projects::searchOne($this->registry, $filters);
            if ($project) {
                $this->model->set('project_code', $project->get('code'), true);
                $this->model->set('project_name', $project->get('name'), true);
            }
        }

        $this->data['recurrence_types'] = Finance_Dropdown::getRecurrenceTypes(array($this->registry));
        $offsets = array('daily' => 1, 'monthlyByDate' => 31, 'weekly' => 7, 'yearly' => 365);
        if ($this->model->get('recurrence_type')) {
            $recurrence_type = $this->model->get('recurrence_type');
        } else {
            $recurrence_type = 'monthlyByDate';
        }
        $this->data['offsets'] = range(1, $offsets[$recurrence_type]);

        if ($this->model->get('reason_type') == 'Finance_Incomes_Reason') {
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.model = \'Finance_Incomes_Reason\'',
                                              'p.model_type = \'' . PH_FINANCE_TYPE_INVOICE . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->model->get('lang'),
                             'sanitize' => true);
            $patterns = Patterns::search($this->registry, $filters);
            $this->data['patterns'] = $patterns;
        }

        //prepare employee
        $employee_id = 0;
        if ($this->model->get('employee1')) {
            $employee_id = $this->model->get('employee1');
        }
        if ($employee_id) {
            require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
            $filters = array('sanitize' => true,
                             'where' => array('c.type = ' . PH_CUSTOMER_EMPLOYEE, 'c.id = ' . $employee_id));
            $employee = Customers::searchOne($this->registry, $filters);
            if ($employee) {
                $this->model->set('employee1_code', $employee->get('code'), true);
                $this->model->set('employee1_name', $employee->get('name') . ' ' . $employee->get('lastname'), true);
                $this->model->set('employee1', $employee_id, true);
            } else {
                $this->model->set('employee1', '', true);
            }
        }
        $this->data['autocomplete_employee_filters'] = array('<type>' => (string)PH_CUSTOMER_EMPLOYEE);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_recurring_payments_add');
        $this->data['title'] = $title;
    }
}

?>
