<?php

class Finance_Transfers_Search_Viewer extends Viewer {
    public $template = 'transfers_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.transfers.factory.php';

        $filters = Finance_Transfers::saveSearchParams($this->registry, array(), 'search_');

        list($finance_transfers, $pagination) = Finance_Transfers::pagedSearch($this->registry, $filters);
        $this->data['finance_transfers'] = $finance_transfers;
        $this->data['pagination'] = $pagination;

        // prepare available tags for multitagging
        $this->prepareMultitagOptions();

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_transfers');
        $this->data['title'] = $title;
    }
}

?>
