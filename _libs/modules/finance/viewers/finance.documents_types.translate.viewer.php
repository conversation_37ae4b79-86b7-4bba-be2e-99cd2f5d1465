<?php

class Finance_Documents_Types_Translate_Viewer extends Viewer {
    public $template = 'documents_types_translate.html';

    public function prepare() {
        $this->model = $this->registry['finance_documents_type'];
        $this->data['finance_documents_type'] = $this->model;

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare department tree
        require_once(PH_MODULES_DIR . 'departments/models/departments.factory.php');
        $this->data['departments'] = Departments::getTree($this->registry);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('fdt.id = ' . $this->model->get('id')),
                         'model_lang' => $basic_model_lang,
                         'sanitize' => true);
        $this->data['base_model'] = Finance_Documents_Types::searchOne($this->registry, $filters);
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_documents_types_translate');
        $this->data['title'] = $title;
    }
}

?>
