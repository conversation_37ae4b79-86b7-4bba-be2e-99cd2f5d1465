<?php

class Finance_Incomes_Reasons_RepaymentData_Viewer extends Viewer {
    public $template = '_repayment_plans_invoice_row.html';

    public function prepare() {
        require_once $this->modelsDir . 'finance.incomes_reasons.factory.php';

        $request = &$this->registry['request'];

        $filters = array('where' => array('fir.id=' . $request->get('repaymentdata'),
                                          'fir.annulled_by=0',
                                          'fir.active=1'));
        if ($this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $this->registry['request']->get('model_lang');
        }
        $reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $reason->getPaidAmount();
        if ($reason->get('type') > PH_FINANCE_TYPE_MAX) {
            $invoices_amount = $reason->getInvoicedAmount();
            if ($invoices_amount + $reason->get('paid_amount') < $reason->get('total_with_vat')) {
                $reason->set('unpaid_amount', $reason->get('total_with_vat') - $invoices_amount - $reason->get('paid_amount'));
            }
        } elseif ($reason->get('type') == PH_FINANCE_TYPE_INVOICE) {
            if ($reason->get('paid_amount') < $reason->get('total_with_vat')) {
                $reason->set('unpaid_amount', $reason->get('total_with_vat') - $reason->get('paid_amount'));
            }
        }

        $incomes_reason = array(
            'id' => $reason->get('id'),
            'name' => $reason->get('name'),
            'unpaid_amount' => $reason->get('unpaid_amount'),
            'total_with_vat' => $reason->get('total_with_vat'),
            'date_of_payment' =>  $reason->get('date_of_payment'),
            'issue_date' =>  $reason->get('issue_date'),
            'type' =>  $reason->get('type'),
            'num' =>  $reason->get('num'),
            'status_modified' =>  $reason->get('status_modified'),
            'currency' =>  $reason->get('currency'),
            );

        $this->data['ref'] = $incomes_reason;

        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        $currencies = Finance_Currencies::getAvailableCurrencies($this->registry);
        $this->data['currencies'] = $currencies;
    }
}

?>
