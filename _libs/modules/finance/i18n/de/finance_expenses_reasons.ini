finance_expenses_reason_audit = Kostenbeleg
finance_expenses_reasons = Kostenbelege
finance_expenses_reason = Kostenbeleg
finance_expenses_reasons_correct_reason = Korrektur
finance_expenses_reasons_credit_notice = eingehende Gutschrift
finance_expenses_reasons_debit_notice = Eingehende Lastschrift
finance_expenses_reasons_invoice = Eingehende Rechnung
finance_expenses_reasons_proforma_invoice = Eingehende Proforma-Rechnung
finance_expenses_reasons_name = Betreff
finance_expenses_reasons_num = Nummer
finance_expenses_reasons_currency = Währung
finance_expenses_reasons_company_data = Kasse/Konto
finance_expenses_reasons_customer = Vertragspartner
finance_expenses_reasons_project = Projekt
finance_expenses_reasons_phase = Phase
finance_expenses_reasons_office = Büro
finance_expenses_reasons_company = Unternehmen
finance_expenses_reasons_type = Typ
finance_expenses_reasons_department = Abteilung
finance_expenses_reasons_group = Gruppe
finance_expenses_reasons_description = Beschreibung
finance_expenses_reasons_finish = Beenden
finance_expenses_reasons_cashbox = Kasse
finance_expenses_reasons_bank_account = Konto
finance_expenses_reasons_container_id = Kasse/Konto
finance_expenses_reasons_employee = Mitarbeiter
finance_expenses_reasons_amount = Betrag
finance_expenses_reasons_paid_amount = Bis zu diesem Zeitpunkt bezahlter Betrag
finance_expenses_reasons_remaining_amount = zu zahlender Restbetrag
finance_expenses_reasons_invoice_num = Rechnung №
finance_expenses_reasons_date_of_payment = Fälligkeit
finance_expenses_reasons_issue_date = Datum des Dokuments
finance_expenses_reasons_fiscal_event_date = Datum des Steuerbestandes
finance_expenses_reasons_distributed = Verteilt
finance_expenses_reasons_tags = Tags
finance_expenses_reasons_action_email = E-Mails
finance_expenses_reasons_added = Datum des Hinzufügens
finance_expenses_reasons_added_by = Hinzugefügt von
finance_expenses_reasons_modified = Datum der Änderung
finance_expenses_reasons_modified_by = Geändert von
finance_expenses_reasons_reason = Grund für die Ausstellung einer Gut-/Lastschrift
finance_expenses_reasons_article = Artikel
finance_expenses_reasons_name_num = [Nummer] Betreff
finance_expenses_reasons_customer_name_code = [Kode] Vertragspartner
finance_expenses_reasons_project_name_code = [Kode] Projekt
finance_expenses_reasons_accounting_period = Bilanzierung für den Zeitraum
finance_expenses_reasons_admit_VAT_credit = Anerkennung einer Steuergutschrift
finance_expenses_reasons_total_no_vat_reason_text = Grund zu Nichterhebung von MwSt.
finance_expenses_reasons_total_vat_rate = MwSt.-Satz
finance_expenses_reasons_payment_type = Zahlungsart
finance_expenses_reasons_total_vat = MwSt.-Betrag
finance_expenses_reasons_payment_status = Zahlungsstatus
finance_expenses_reasons_handovered_status = Übergabe-Status
finance_expenses_reasons_total_with_vat = Gesamtbetrag inkl. MwSt.
finance_expenses_reasons_total = Insgesamt
finance_expenses_reasons_total_amount_with_vat = Gesamtbetrag (inkl. MwSt.)
finance_expenses_reasons_total_paid_amount = Bezahlt (insgesamt)
finance_expenses_reasons_total_remaining_amount = zu zahlender Betrag (insgesamt)
finance_expenses_reasons_direct_paid_amount = bezahlt (Grund)
finance_expenses_reasons_amount_to_be_paid = bezahlt (Grund)
finance_expenses_reasons_advance = Anzahlung
finance_expenses_reasons_for_invoice = Für Rechnung #
finance_expenses_reasons_fin_field_1 = Freies Feld 1
finance_expenses_reasons_fin_field_2 = Freies Feld 2
finance_expenses_reasons_allocated_status = Kosten auf Lieferungen verteilen
finance_expenses_reasons_allocated_status_disabled = Unterliegt nicht
finance_expenses_reasons_allocated_status_enabled = Um zu Verteilung
finance_expenses_reasons_allocated_status_allocated = Verteilt

finance_expenses_reasons_add = %s hinzufügen
finance_expenses_reasons_addcorrect = Korrektur hinzufügen
finance_expenses_reasons_addpayment = Zahlung hinzufügen
finance_expenses_reasons_edit = %s  editieren
finance_expenses_reasons_view = %s einsehen
finance_expenses_reasons_translate =  %s übersetzen
finance_expenses_reasons_attachments = Dateien zu %s
finance_expenses_reasons_assign = Zuweisungen von %s
finance_expenses_reasons_generate = Datei nach Vorlage aus %s generieren
finance_expenses_reasons_history = Historie von %s
finance_expenses_reasons_history_activity = Aktivität
finance_expenses_reasons_relatives = Beziehungen von %s
finance_expenses_reasons_payments = Bilanz von %s
finance_expenses_reasons_distribution = Zuweisung von %s
finance_expenses_reasons_communications = Kommunikation
finance_expenses_reasons_comments = Kommentare
finance_expenses_reasons_emails = E-Mails
finance_expenses_reasons_generated_files = generierte Dateien
finance_expenses_reasons_generated_revisions = neue hinzufügen oder überschreiben
finance_expenses_reasons_generated_add_new = neue hinzufügen
finance_expenses_reasons_pattern_variables = Bitte die Daten aus der Vorlage ausfüllen!
finance_expenses_reasons_pattern_modify = Letzte Bearbeitungen der Vorlage
finance_expenses_reasons_generated_get_revision = Daten aus Version nehmen
finance_expenses_reasons_generated_save_revision = Version ersetzen
finance_expenses_reasons_generate_revision_title = Name der Version
finance_expenses_reasons_generate_revision_description = Beschreibung der Version
finance_expenses_reasons_generated_filename = Datei
finance_expenses_reasons_generated_revision = Version
finance_expenses_reasons_generated_pattern = Vorlage
finance_expenses_reasons_generated_description = Beschreibung
finance_expenses_reasons_generated_added = Hinzugefügt am
finance_expenses_reasons_generated_added_by = Hincugefügt von
finance_expenses_reasons_file_not_exist = Datei gelösch oder beschädigt
finance_expenses_reasons_payment = Zahlung
finance_expenses_reasons_payment_amount = Zahlungsbetrag
finance_expenses_reasons_payment_paid = Nach diesem Kostenbeleg bezahlter Betrag
message_finance_expenses_reasons_edit_success = %s erfolgreich editiert
message_finance_expenses_reasons_add_success = %s erfolgreich hinzugefügt
message_finance_expenses_reasons_translate_success = %s erfolgreich übersetzt
message_finance_expenses_reasons_addcorrect_success = Berichtigung erfolgreich erstellt!
message_finance_expenses_reasons_quantity_change_success = Die Mengen in %s wurden verändert!
message_finance_expenses_reasons_distribute_success = %s erfolgreich verteilt
message_finance_expenses_reasons_allocate_costs_success = %s erfolgreich auf Lieferungen verteilt
message_finance_expenses_reasons_delete_allocate_costs_success = %s erfolgreich Verteilung auf Lieferungen etfernt
error_no_such_finance_expenses_reason = Kein Kostenbeleg mit dieser Identifikationsnummer
error_finance_expenses_reasons_add_failed = Fehler beim Hinzufügen von %s
error_finance_expenses_reasons_edit_failed = Fehler beim Editieren von %s
error_finance_expenses_reasons_translate_failed = Fehler bei der Übersetzung von %s
error_finance_expenses_reasons_addcorrect_failed = Fehler bei Erstellung einer Korrektur zu %s!
error_finance_expenses_reasons_invoice_no_quantity = Nicht ausreichende Mengen zu %s!
error_finance_expenses_reasons_handover_no_quantity = Nicht ausreichende Mengen zu %s!
error_finance_expenses_reasons_distribute_failed = Fehler bei der Verteilung von %s
error_finance_expenses_reasons_allocate_costs_failed = Fehler bei der Verteilung von %s auf Lieferungen
error_no_finance_expenses_reasons_or_annulled = keine Kostenbelege gewählt oder ein der gewählten Belege wurde annulliert.
error_finance_expenses_reasons_different_types = Die gewählten Kostenbelege haben unterschiedliche Typen.
error_finance_expenses_reasons_multiprint_failed = Fehler beim mehrfachen Druck von %s
error_invoice_num_not_unique = Ein <span onclick="window.location='[link]'" style="font-weight: bold">[type_name]</span> von diesem Vertragspartner und mit dieser Nummer existiert bereits. Bitte eine andere Nummer wählen!
finance_expenses_reasons_handover_direction = Art
finance_expenses_reasons_handover_direction_incoming = Übernahmeprotokoll
finance_expenses_reasons_handover_direction_outgoing = Übergabeprotokoll
error_finance_expenses_reasons_handover_failed_incoming = Fehler bei der Warenannahme
error_finance_expenses_reasons_handover_failed_outgoing = Fehler bei der Warenübergabe
message_finance_expenses_reasons_handover_success_incoming = Ware erfolgreich angenommen. Anzahl der erstellten Protokolle
message_finance_expenses_reasons_handover_success_outgoing = Ware erfolgreich übergeben. Anzahl der erstellten Protokolle
finance_expenses_reasons_date_of_handover = Datum der Annahme
finance_expenses_reasons_addhandover_incoming = Warenannahme
finance_expenses_reasons_addhandover_outgoing = Warenübergabe
error_finance_expenses_reasons_no_counter = Kein Zähler für Typ "%s", Unternehmen "%s" und Geschäftsstelle "%s"!
error_finance_expenses_reasons_no_customer_specified = Bitte %s wählen!
error_finance_expenses_reasons_no_container_id_specified = Bitte %s wählen!
error_finance_expenses_reasons_no_issue_date_specified = Bitte %s spezifizieren!
error_finance_expenses_reasons_no_employee_specified = Bitte %s wählen!
error_finance_expenses_reasons_no_type_specified = Bitte Typ wählen!
error_finance_expenses_reasons_invalid_type = Ein ungültiger oder nicht aktiver Typ wurde gewählt!
error_finance_expenses_reasons_no_company_specified = Bitte Unternehmen wählen!
error_no_invoice_num = Bitte %s eingeben!
error_payment_before_issue = Das Datum im Feld %s darf nicht vor dem Datum im Feld %s liegen!
error_finance_expenses_reasons_before_min_issue_date = %s kann nicht vor dem jüngsten Datum in den Proforma-Rechnungen liegen: %s!
finance_expenses_reasons_distribution_not_saved_legend = Die Verteilung ist noch nicht gespeichert. Die Daten aus der Standardverteilung liegen den ausgefüllten Beträgen zugrunde.
finance_expenses_reasons_distribution_outdated_legend = Die Verteilung der Elemente ist geändert worden. Verteilung der Datensatz muss wieder gespeichert werden.
warning_finance_expenses_reasons_distribution_deleted = Aufgrund Korrektur [reason_type_name] sollte wieder verteilt werden.
finance_expenses_reasons_log_add = %s fügt %s hinzu (Betrag: %s)
finance_expenses_reasons_log_system_add = %s fügt %s hinzu (Betrag: %s)
finance_expenses_reasons_log_edit = %s editiert %s (Betrag: %s)
finance_expenses_reasons_log_annul = %s annulliert %s (Betrag: %s)
finance_expenses_reasons_log_assign = %s weist %s zu (Betrag: %s)
finance_expenses_reasons_log_translate = %s übersetzt %s (Betrag: %s)
finance_expenses_reasons_log_status = %s ändert den Status von %s (Betrag: %s)
finance_expenses_reasons_log_multistatus = %s ändert den Status von %s (Betrag: %s)
finance_expenses_reasons_log_generate = %s generiert eine Datei für %s durch Verwendung der Vorlage "%s"%s
finance_expenses_reasons_log_system_generate = %s generiert eine Datei für %s durch Verwendung der Vorlage "%s"%s
finance_expenses_reasons_log_generate_delete = %s löscht eine erstellte Datei für %s (Datei: %s)
finance_expenses_reasons_log_print = %s druckt %s durch Verwendung der Vorlage "%s"%s
finance_expenses_reasons_log_multiprint = %s druckt %s durch Verwendung der Vorlage "%s"%s
finance_expenses_reasons_log_modified_attachments = %s ändert die angehängte Datei für %s (Datei: %s)
finance_expenses_reasons_log_modified_gen = %s ändert eine erstellte Datei für %s (Datei: %s)
finance_expenses_reasons_log_add_attachments = %s fügt eine angehängte Datei für %s (Datei: %s)
finance_expenses_reasons_log_export = %s exportiert eine Datei für %s (Datei: %s)
finance_expenses_reasons_log_addcorrect = %s fügt Korrektur zu %s hinzu (Betrag: %s)
finance_expenses_reasons_log_addhandover = %s erstellt Übergabeprotokoll %s (Betrag: %s)
finance_expenses_reasons_log_distribute = %s verteilt %s (Betrag: %s)
finance_expenses_reasons_log_system_distribute = %s verteilt %s (Betrag: %s)
finance_expenses_reasons_log_allocate_costs = %s teilt Lieferungen %s zu (Betrag: %s)
finance_expenses_reasons_log_delete_allocate_costs = %s entfernt die Verteilung zu Lieferungen von %s (Betrag: %s)
finance_expenses_reasons_log_tag = %s ändert Tags von %s (Betrag: %s)
finance_expenses_reasons_log_multitag = %s ändert Tags von %s (Betrag: %s)
finance_expenses_reasons_log_email = %s sendet eine E-Mail für Kostenbeleg
finance_expenses_reasons_log_receive_email = %s receives expense document e-mail
finance_expenses_reasons_log_receive_email_detailed = There is an email from %s sent
finance_expenses_reasons_log_add_comment = %s fügt einen Kommentar für Kostenbeleg hinzu
finance_expenses_reasons_log_edit_comment = %s editiert einen Kommentar für Kostenbeleg
finance_expenses_reasons_log_addpayment = %s fügt eine Zahlung zu %s hinzu (Betrag: %s)
finance_expenses_reasons_log_addinvoice = %s fügt eine Rechnung zu %s hinzu (Betrag: %s)
finance_expenses_reasons_log_addproformainvoice = %s fügt eine Proforma-Rechnung zu %s hinzu (Betrag: %s)
finance_expenses_reasons_log_addcredit = %s fügt eine Gutschrift zu %s hinzu (Betrag: %s)
finance_expenses_reasons_log_adddebit = %s fügt eine Lastschrift zu %s hinzu (Betrag: %s)
finance_expenses_reasons_log_empty = %s entfernt eine Zahlung für %s (Betrag: %s)
finance_expenses_reasons_log_payments = %s editiert eine Bilanz von %s (Betrag: %s)
finance_expenses_reasons_log_activate = %s aktiviert %s (Betrag: %s)
finance_expenses_reasons_log_deactivate = %s deaktiviert %s (Betrag: %s)
finance_expenses_reasons_log_create = %s erstellt %s durch %s
finance_expenses_reasons_log_transform = %s transformiert %s (Betrag: %s) aus %s
finance_expenses_reasons_logtype_add = Hinzufügen
finance_expenses_reasons_logtype_edit = Editieren
finance_expenses_reasons_logtype_annul = Annuliieren
finance_expenses_reasons_logtype_assign = Zuweisung
finance_expenses_reasons_logtype_translate = Übersetzung
finance_expenses_reasons_logtype_status = Status ändern
finance_expenses_reasons_logtype_multistatus = Status mehrfach ändern
finance_expenses_reasons_logtype_generate = Datei erstellen
finance_expenses_reasons_logtype_generate_delete = Datei löschen
finance_expenses_reasons_logtype_print = Druck
finance_expenses_reasons_logtype_multiprint = mehrfacher Druck
finance_expenses_reasons_logtype_modified_attachments = Datei modifizieren
finance_expenses_reasons_logtype_modified_gen = Datei ändern
finance_expenses_reasons_logtype_add_attachments = Datei hinzufügen
finance_expenses_reasons_logtype_export = Exportieren
finance_expenses_reasons_logtype_addcorrect = Neubewertung zum Grund
finance_expenses_reasons_logtype_addhandover = Übergabeprotokoll erstellen
finance_expenses_reasons_logtype_distribute = Verteilung
finance_expenses_reasons_logtype_allocate_costs = Auf Lieferungen verteilen
finance_expenses_reasons_logtype_delete_allocate_costs = Verteilung auf Lieferungen entfernen
finance_expenses_reasons_logtype_tag = Tags ändern
finance_expenses_reasons_logtype_multitag = Tags mehrfach ändern
finance_expenses_reasons_logtype_email = E-Mail senden
finance_expenses_reasons_logtype_receive_email = Received e-mail
finance_expenses_reasons_logtype_add_comment = Kommentar hinzufügen
finance_expenses_reasons_logtype_edit_comment = Kommentar editieren
finance_expenses_reasons_logtype_addpayment = Zahlung hinzufügen
finance_expenses_reasons_logtype_addinvoice = Rechnung hinzufügen
finance_expenses_reasons_logtype_addproformainvoice = Proforma-Rechnung hinzufügen
finance_expenses_reasons_logtype_addcredit = Gutschrift hinzufügen
finance_expenses_reasons_logtype_adddebit = Lastschrift hinzufügen
finance_expenses_reasons_logtype_empty = Zahlung entfernen
finance_expenses_reasons_logtype_payments = Bilanz editieren
finance_expenses_reasons_logtype_activate = Aktivieren
finance_expenses_reasons_logtype_deactivate = Deaktivieren
finance_expenses_reasons_logtype_create = Erstellen
finance_expenses_reasons_logtype_transform = Transformieren
finance_expenses_reasons_status = Status
finance_expenses_reasons_substatus = Substatus
finance_expenses_reasons_after_action_add_unfinished = Hinzufügen
finance_expenses_reasons_after_action_edit_unfinished = Editieren
finance_expenses_reasons_after_action_finish = Beenden
finance_expenses_reasons_after_action_payment = Bezahlen
finance_expenses_reasons_action_add = hinzufügen
finance_expenses_reasons_action_edit = editieren
help_finance_expenses_reasons_after_action_unfinished = Mit dieser Taste wird der Eintrag [action], dabei kann er weiter editiert werden.
help_finance_expenses_reasons_after_action_finish = Mit dieser Taste wird der Eintrag [action] und Sie werden ihn beenden – eine Bearbeitung wird nicht mehr möglich sein.
help_finance_expenses_reasons_after_action_payment = Mit dieser Taste wird der Eintrag [action] und Sie erstellen eine Zahlung, die den ganzen Wert des Eintrags deckt.
help_finance_expenses_reasons_phase = Phase zum gewählten Projekt
help_finance_expenses_reasons_total_amount_with_vat = Gesamtbetrag des Kostenbelegs
help_finance_expenses_reasons_total_paid_amount = Der ganze bezahlte Betrag zum Kostengrund. Beinhaltet sowohl die Direktzahlungen zum Zahlungsgrund, als auch die Zahlungen zu der damit verbundenen eingehenden Proforma-Rechnung.
help_finance_expenses_reasons_direct_paid_amount = Direkt bezahlter Betrag zum aktuellen Kostenbeleg.
help_finance_expenses_reasons_amount_to_be_paid = Der Betrag, der zum gegenwärtigen Kostenbeleg direkt bezahlt werden kann.
help_finance_expenses_reasons_cd_reason = Ein Grund für die Ausstellung einer Gut-/Lastschrift muss gem. Gesetz unbedingt ausgefüllt werden.
message_finance_expenses_reasons_edit_payment_success = Bilanz von %s erfolgreich editiert
error_finance_expenses_reasons_edit_payment_failed = Fehler beim Editieren der Bilanz von %s
error_finance_expenses_reason_preview = Keine Daten für den Kostenbeleg
error_accounting_period_before_issue_date = Das Buchungsdatum kann nicht vor dem Ausstellungsdatum des Dokuments liegen
error_no_accounting_period = Bitte %s wählen!
error_accounting_period_out_of_allowed = Ungültige Wahl für %s!
error_allocated_status_not_allowed = Kostenbeleg enthält keinen Artikel, dessen Kosten den Lieferungen zugeordnet werden können!
error_delivery_no_commodities = Kostenbeleg enthält keine Waren oder es können keine Übergaben dafür erstellt werden!
error_delivery_no_nom_price_updates = Kostenbeleg aktualisiert Artikelpreise nicht!
errors_nom_price_updates_invalid = Ungültige oder fehlende Daten zur Artikelpreisaktualisierung aus ausgewählten Lieferungen!
error_average_weighted_delivery_price_negative = Das System hat einen negativen gewichteten Lieferpreis für %s berechnet!
warning_average_weighted_delivery_price_modified_by_user = Der gewichtete endgültige Versandpreis für %s wird nicht aktualisiert, da er nach dem Lieferdatum manuell geändert wurde!
message_finance_expenses_reasons_addpayment_success = Zahlung erfolgreich hinzugefügt
error_finance_expenses_reasons_addpayment_failed = Fehler beim Hinzufügen der Zahlung
error_finance_expenses_reason_empty_gt2 = Die Daten in der Tabelle des Kostenbelegs sind nicht ausgefüllt
error_finance_expenses_reasons_allocated_amount_total = Die Summe der eingegebenen Beträge darf die nicht verteilte Summe aus dem Kostenbeleg nicht überschreiten!
error_finance_expenses_reasons_allocated_amount_row = Der für jede Zeile eingegebene Betrag darf den nicht verteilten Betrag nicht überschreiten!
finance_expenses_reasons_issue_invoice = Die hinzugefügte Rechnung wird mit diesem Dokument identisch sein und kann nicht editiert werden.<br />Möchten Sie eine Rechnung hinzufügen?
finance_expenses_reasons_proforma = Proforma-Rechnung
finance_expenses_reasons_addinvoice = Rechnung hinzufügen
finance_expenses_reasons_addcredit = Gutschrift hinzufügen
finance_expenses_reasons_adddebit = Lastschrift hinzufügen
error_finance_expenses_reasons_invoice_failed = Fehler beim Erstellen einer Rechnung!
error_finance_expenses_reasons_proforma_invoice_failed = Fehler beim Erstellen einer Proforma-Rechnung!
error_finance_expenses_reasons_article_not_exists = Der Artikel %s (Zeile %d) existiert nicht in der Rechnung!
error_finance_expenses_reasons_positive_price = Der Preis oder die Menge für Artikel %s (Zeile %d) in der Gutschrift soll eine negative Zahl sein!
error_finance_expenses_reason_issue_date = Das Ausstellungsdatum des neuen Dokuments kann nicht vor dem Ausstellungsdatum des Elterndokuments liegen!
error_finance_expenses_reasons_incomes_reason_payment = Eine eingehende Rechnung kann nicht hinzugefügt werden, da in der Bilanz dieses Dokuments ein nicht verteilter Grund für Einnahme vorliegt!
error_finance_expenses_reasons_paid_relative = Die gewählte Aktion wird zur Überzahlung beim Kostenbeleg führen!<br />Bitte die verteilten Zahlungen zu %s entfernen!
error_finance_expenses_reasons_empty_rows = Ein leeres Dokument oder ein Dokument mit einem Nullendbetrag von diesem Typ kann nicht beendet werden!
error_finance_expenses_reason_overpaid = Zugewiesenen Betrag an das Lastendokument (%.2f) sollte ihre Gesamtmenge (%s) nicht überschreiten. Um die festgelegten Änderungen vorzunehmen, reduzieren Sie <a href="%s" target="_blank">die zugewiesene Betrag, um Dokument</a>.
error_finance_expenses_reasons_multiaddinvoice_different_proformas = Die Daten der gewählten eingehenden Proforma-Rechnungen unterscheiden sich oder für manche Daten kann keine Rechnung hinzugefügt werden!
error_finance_expenses_reasons_multiaddinvoice_currency = Bitte Währung für die Rechnung wählen!
warning_finance_expenses_reasons_multiaddinvoice_currency_rates = Bitte Umrechnungskurs zur gewählten Währung für die Beträge der Proforma-Rechnungen wählen!
error_finance_expenses_reasons_multiaddinvoice_currency_rates = Bitte einen gültigen Wert für den Umrechnungskurs zur gewählten Währung der Beträge aus den Proforma-Rechnungen wählen!
error_finance_expenses_reasons_multiaddinvoice_paid_proforma_currency = Bitte die verteilten Zahlungen zu folgenden Proforma-Rechnungen, die eine unterschiedliche Währung im Vergleich zur Währung der Rechnung haben, entfernen: %s!
warning_document_with_the_same_num_exists = """Ein Dokument mit dieser Nummer existiert bereits!
Sind Sie sicher, dass Sie den Vorgang fortsetzen möchten?"""
message_finance_expenses_reasons_invoice_success = Die Rechnung wurde erfolgreich erstellt!
message_finance_expenses_reasons_proforma_invoice_success = Die Proforma-Rechnung wurde erfolgreich erstellt!
