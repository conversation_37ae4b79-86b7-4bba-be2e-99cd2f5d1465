finance_repayment_plans = Repayment schedules
finance_repayment_plans_company = Company
finance_repayment_plans_customer = Contractor
finance_repayment_plans_name = About
finance_repayment_plans_type = Type
finance_repayment_plans_currency = Currency
finance_repayment_plans_amount = Amount
finance_repayment_plans_unpaid_amount = Amount outstanding
finance_repayment_plans_office = Office
finance_repayment_plans_location = Location
finance_repayment_plans_description = Description
finance_repayment_plans_deadline = Deadline
finance_repayment_plans_paid_amount = Paid amount
finance_repayment_plans_last_payment = Last payment
finance_repayment_plans_add_more = Add more
finance_repayment_plans_type_documents = Documents
finance_repayment_plans_issue_date = Issue date
finance_repayment_plans_relatives_parent = Created from
finance_repayment_plans_relative_name = Related record
finance_repayment_plans_invoice_list = RP
finance_repayment_plans_added = Added date
finance_repayment_plans_added_by = Added by
finance_repayment_plans_modified = Modified date
finance_repayment_plans_modified_by = Modified by

finance_repayment_plans_add = Add repayment schedule
finance_repayment_plans_edit = Edit repayment schedule
finance_repayment_plans_view = View repayment schedule
finance_repayment_plans_history = Repayment schedule History

finance_repayment_plans_status = Status
finance_repayment_plans_status_opened = Offered
finance_repayment_plans_status_locked = Agreed upon
finance_repayment_plans_status_finished = Finished

finance_repayment_plans_paid = Paid
finance_repayment_plans_unpaid = Unpaid
finance_repayment_plans_overdue_paid = Paid overdue
finance_repayment_plans_overdue_unpaid = Unpaid overdue

finance_repayment_plans_shared_amount =Allocated amount
finance_repayment_plans_unshared_amount = Remaining

finance_repayment_plans_log_edit = %s edits repayment schedule to the amount of %s %s
finance_repayment_plans_log_add = %s adds repayment schedule to the amount of %s %s
finance_repayment_plans_log_setstatus = %s changes status of repayment schedule
finance_repayment_plans_log_payment = %s allocates payment on repayment schedule to the amount of %s %s
finance_repayment_plans_log_empty = %s remove payment from repayment schedule to the amount of %s %s
finance_repayment_plans_logtype_add = Add
finance_repayment_plans_logtype_edit = Edit
finance_repayment_plans_logtype_setstatus = Status
finance_repayment_plans_logtype_payment = Payment
finance_repayment_plans_logtype_empty = Remove payment

confirm_link_incomes_reasons = Click "OK" to confirm that you want to relate the records!
alert_link_incomes_reasons = Please select document!

message_finance_repayment_plan_edit_success = Repayment schedule edited successfully
message_finance_repayment_plan_add_success = Repayment schedule added successfully
message_finance_repayment_plan_status_success = Status of repayment schedule changed successfully

warning_finance_repayment_plan_exist = Existing repayment schedule for this contractor

error_no_such_finance_repayment_plan = No repayment schedule with this number
error_finance_repayment_plan_add_failed = Error adding repayment schedule
error_finance_repayment_plan_edit_failed = Error editing repayment schedule
error_finance_repayment_plan_status_failed = Error changing status of repayment schedule
error_no_repayment_plan_amount_specified = No installments defined
error_all_amount = Installments must be equal to total amount
error_edit_notallowed_locked_finished = Finished and agreed upon repayment schedules cannot be edited.
error_no_repayment_plans_issue_date = No issue date defined
error_no_customer_specified = Please enter contractor!
error_no_company_specified = Please select company!
