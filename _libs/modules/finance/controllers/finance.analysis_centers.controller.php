<?php
require_once PH_MODULES_DIR . 'finance/models/finance.analysis_centers.factory.php';

class Finance_Analysis_Centers_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Finance_Analysis_Center';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Finance_Analysis_Centers';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'add', 'view', 'edit'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'add', 'view', 'edit', 'translate'
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        //check if details are submitted via POST
        if ($this->registry['request']->isPost()) {
            //build the model from the POST
            $analysis_center = Finance_Analysis_Centers::buildModel($this->registry);

            if ($analysis_center->save()) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_analysis_center_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_analysis_center_add_failed'), '', -2);
            }
        } else {
            //create empty analysis center model
            $analysis_center = Finance_Analysis_Centers::buildModel($this->registry);
        }

        if (!empty($analysis_center)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_analysis_center', $analysis_center->sanitize());
        }

        return true;
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $analysis_center = Finance_Analysis_Centers::buildModel($this->registry);

            if ($analysis_center->save()) {
                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_analysis_center_edit_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_finance_analysis_center_edit_failed'), '', -2);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fac.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $analysis_center = Finance_Analysis_Centers::searchOne($this->registry, $filters);
        }

        if (!empty($analysis_center)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_analysis_center')) {
                $this->registry->set('finance_analysis_center',  $analysis_center->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_analysis_center'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fac.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $analysis_center = Finance_Analysis_Centers::searchOne($this->registry, $filters);

        if (!empty($analysis_center)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_analysis_center')) {
                $this->registry->set('finance_analysis_center',  $analysis_center->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_analysis_center'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $analysis_center = Finance_Analysis_Centers::buildModel($this->registry);
            $analysis_center->set('id', $id, true);

            if ($analysis_center->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_finance_analysis_center_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_analysis_center_translate_failed'), '', -2);
            }
        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('fac.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $analysis_center = Finance_Analysis_Centers::searchOne($this->registry, $filters);
        }

        if (!empty($analysis_center)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_analysis_center', $analysis_center->sanitize());
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_analysis_center'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactivates the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Finance_Analysis_Centers::changeStatus($this->registry, $ids, $status);

        if ($result) {
            if ($result != count($ids)) {
                //set warning
                $text = ($this->action == 'activate') ?
                          $this->i18n('warning_activate_notallowed') :
                          $this->i18n('warning_deactivate_notallowed');
                $this->registry['messages']->setWarning($text, '', -1);
            }
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setWarning($text, '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        } elseif (!is_array($ids)) {
            //convert $ids to array
            if (preg_match('#,#', $ids)) {
                $ids = preg_split('#\s*,\s*#', $ids);
            } else {
                $ids = array($ids);
            }
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete items
        $result = Finance_Analysis_Centers::delete($this->registry, $ids);

        if ($result) {
            if ($result != count($ids)) {
                //set warning
                $this->registry['messages']->setWarning($this->i18n('warning_delete_notallowed'), '', -1);
            }
            //delete successful
            $this->registry['messages']->setMessage($this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setWarning($this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        } elseif (!is_array($ids)) {
            //convert $ids to array
            if (preg_match('#,#', $ids)) {
                $ids = preg_split('#\s*,\s*#', $ids);
            } else {
                $ids = array($ids);
            }
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Finance_Analysis_Centers::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage($this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError($this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);
        if ($this->model && $this->model->get('id')) {

        }

        return $actions;
    }
}

?>
