<?php

require_once('finance.analysis_items.model.php');

/**
 * Finance_Analysis_Items model class
 */
Class Finance_Analysis_Items extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Finance_Analysis_Item';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Recursively rebuilds tree of analysis_items and assigns the new left and right values
     *
     * @param object $registry - the main registry
     * @param string $type - income or expense
     * @param int $parent - the id of the parent of the (sub)tree to be rebuild
     * @param int $left - the left position of the tree node
     * @return int $right - the right position of the tree node + 1
     */
    public static function rebuildTree(&$registry, $type, $parent = 0, $left = 1) {
        // the right value of this node is the left value + 1
        $right = $left+1;

        // get all children of this node and sort them by position, then by name ASCENDING
        $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS faii18n ' . "\n" .
                 '  ON (fai.id=faii18n.parent_id AND faii18n.lang="' . $registry['lang'] . '")' . "\n" .
                 'WHERE fai.ancestor=' . $parent . ' AND fai.`type`="'. $type . '"' . "\n" .
                 'ORDER BY fai.position, faii18n.name';

        $children = $registry['db']->GetCol($query);

        foreach ($children as $id) {
            // recursive execution of this function for each
            // child of this node
            // $right is the current right value, which is
            // incremented by the rebuild_tree function
            $right = self::rebuildTree($registry, $type, $id, $right);
        }

        // we've got the left value, and now that we've processed
        // the children of this node we also know the right value
        if ($parent) {
            $update = 'UPDATE ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . "\n" .
                      'SET `left`=' . $left . ', `right`=' . $right . "\n" .
                      'WHERE id=' . $parent;
            $registry['db']->Execute($update);
        }

        // return the right value of this node + 1
        return $right+1;
    }

    /**
     * Gets entire tree
     *
     * @param object $registry - the main registry
     * @param array $filters - filters to apply in the search
     * @return array tree -  array of the constructed tree
     */
    public static function getTree(&$registry, $filters=array()) {
        //$filters['where'][] = 'fain.id = 0';
        return self::getTreeDescendants($registry, $filters);
    }

    /**
     * Gets tree or subtree for the defined parent node and constructs it as array
     *
     * @param object $registry - the main registry
     * @param array $filters - filters to apply in the search
     * @return array tree -  array of the constructed tree
     */
    public static function getTreeDescendants(&$registry, $filters = array()) {

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $sql['select'] = 'SELECT fai.*, faii18n.*, fai2i18n.name AS ancestor_name, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS faii18n' . "\n" .
                       '  ON (fai.id=faii18n.parent_id AND faii18n.lang="' . $model_lang . '")' . "\n" .
                        //ancestor info
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS fai2i18n' . "\n" .
                       '  ON (fai.ancestor=fai2i18n.parent_id AND fai2i18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to analysis_item to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fai.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fai.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fai.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        $sql['where'] = $where . "\n";
                      //. '  AND fai.`left` BETWEEN fain.`left` AND fain.`right`';

        $sql['order'] = 'ORDER BY fai.`left` ASC' . "\n";

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //Add parent node
        $query = 'SELECT MAX(fai.right) as max_right ' . "\n" .
                        'FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai ' . "\n" .
                        $where;
        $record = $registry['db']->GetOne($query);
        if ($record) {
            $right = $record + 1;
        } else {
            $right = 2;
        }
        $parent_array = array('id' => 0, 'left' => 1 ,'right' => $right);
        array_unshift($records, $parent_array);

        // start with an empty $right stack
        $right = array();

        // construct the tree
        foreach ($records as $idx => $record) {
            // only check stack if there is one
            if (count($right) > 0) {
                // check if we should remove a node from the stack
                while ($right[count($right)-1] < $record['right']) {
                    array_pop($right);
                }
            }

            // calculate the level(indentation) of the node
            $records[$idx]['level'] = count($right);

           // add this node to the stack
           $right[] = $record['right'];
        }

        //Remove parent node
        array_shift($records);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        $list = array();
        foreach ($models as $model) {
            $list[$model->get('id')] = $model;
        }

        return $list;
    }

    /**
     * Gets the ids of the tree or subtree for the defined parent node
     *
     * @param object $registry - the main registry
     * @param int $parent - the id of the parent node
     * @return array ids -  array of the ids of the tree or subtree
     */
    public static function getTreeDescendantsIds(&$registry, $parent = 1) {

        $sql['select'] = 'SELECT fai.id' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fain, ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai ' . "\n";

        $sql['where'] = 'WHERE fain.id=' . $parent . "\n" .
                        '  AND fai.`left` BETWEEN fain.`left` AND fain.`right`' . "\n" .
                        '  AND fain.type=fai.type';

        $sql['order'] = 'ORDER BY fai.`left` ASC' . "\n";

        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        if (empty($ids)) {
            $ids = array();
        }

        return $ids;
    }

    /**
     * Gets the ids of the ancestors for the defined node
     *
     * @param object $registry - the main registry
     * @param int $node - the id of the node
     * @return array ids -  array of the ids of the ancestors
     */
    public static function getTreeParentsIds(&$registry, $node = 1) {

        $sql['select'] = 'SELECT fai.id' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fain, ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai ' . "\n";

        $sql['where'] = 'WHERE fain.id=' . $node . "\n" .
                        '  AND fai.`left` < fain.`left` AND fai.`right` > fain.`right`' . "\n" .
                        '  AND fain.type=fai.type';

        $sql['order'] = 'ORDER BY fai.`left` ASC' . "\n";

        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        if (empty($ids)) {
            $ids = array();
        }

        return $ids;
    }

    /**
     * Gets ancestor tree or subtree for the defined node and constructs it as array
     *
     * @param object $registry - the main registry
     * @param int $node_id - the id of the node
     * @param array $filters - search filters
     * @return array tree -  array of the constructed tree
     */
    public static function getTreeParents(&$registry, $node_id, $filters = array()) {
        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $sql['select'] = 'SELECT fai.*, faii18n.*, fai2i18n.name AS ancestor_name, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fain, ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS faii18n' . "\n" .
                       '  ON (fai.id=faii18n.parent_id AND faii18n.lang="' . $model_lang . '")' . "\n" .
                        //ancestor info
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS fai2i18n' . "\n" .
                       '  ON (fai.ancestor=fai2i18n.parent_id AND fai2i18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to analysis_item to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fai.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fai.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fai.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        $sql['where'] = 'WHERE fain.id=' . $node_id . "\n" .
                        '  AND fai.`left` < fain.`left` AND fai.`right` > fain.`right`' .
                        '  AND fain.type=fai.type';

        //group clause
        $sql['group_by'] = 'GROUP BY fai.id' . "\n";

        $sql['order'] = 'ORDER BY fai.`left` ASC' . "\n";

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        // start with an empty $right stack
        $right = array();

        // construct the tree
        foreach ($records as $idx => $record) {
            // only check stack if there is one
            if (count($right) > 0) {
                // check if we should remove a node from the stack
                while ($right[count($right)-1] < $record['right']) {
                    array_pop($right);
                }
            }

            // calculate the level(indentation) of the node
            $records[$idx]['level'] = count($right);

           // add this node to the stack
           $right[] = $record['right'];
        }

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        $list = array();
        foreach ($models as $model) {
            $list[$model->get('id')] = $model;
        }

        return $list;
    }

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = 'ORDER BY ' . implode(', ', $filters['sort']);
            /*if (!preg_match('#d.active#', $sort)) {
                $sort = 'ORDER BY d.active DESC, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }*/
        } else {
            $sort = 'ORDER BY, fai.left ASC';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT (fai.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS faii18n' . "\n" .
                       '  ON (fai.id=faii18n.parent_id AND faii18n.lang="' . $model_lang . '")' . "\n" .
                        //ancestor info
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS fai2i18n' . "\n" .
                       '  ON (fai.ancestor=fai2i18n.parent_id AND fai2i18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to analysis_item to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fai.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fai.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fai.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        //where clause
        $sql['where'] = $where;

        //department clause
        $sql['department_by'] = 'GROUP BY fai.id' . "\n";

        //order by clause

        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {
        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = 'ORDER_BY ' . implode(', ', $filters['sort']);
            /*if (!preg_match('#d.active#', $sort)) {
                $sort = 'ORDER BY d.active DESC, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }*/
        } else {
            $sort = 'ORDER BY fai.left ASC';
        }

        //select clause
        $sql['select'] = 'SELECT fai.*, faii18n.*, fai2i18n.name AS ancestor_name, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, gi18n.name as group_name, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS faii18n' . "\n" .
                       '  ON (fai.id=faii18n.parent_id AND faii18n.lang="' . $model_lang . '")' . "\n" .
                        //ancestor info
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_I18N . ' AS fai2i18n' . "\n" .
                       '  ON (fai.ancestor=fai2i18n.parent_id AND fai2i18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to group
                       'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                       '  ON (fai.group=gi18n.parent_id AND gi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to analysis_item to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fai.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fai.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to analysis_item to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fai.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        //where clause
        $sql['where'] = $where;

        //department clause
        $sql['department_by'] = 'GROUP BY fai.id' . "\n";

        //order by clause

        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(fai.id) AS total';
                $sql['from1'] = '';
                $sql['limit'] = '';
                $sql['group_by'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, $filters = array()) {

        $where[] = 'WHERE (';

        $current_user_id = $registry['currentUser']->get('id');

        if (isset($filters['where'])) {
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.fai. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\(\)/', '1', $where);

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array()) {
        $sessionParam = strtolower('search_' . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        if ($model->get('module_name')) {
            @list($module, $controller) = explode('_', $model->get('module_name'), 2);
        }

        //get search filters definitions
        if ($model->get('search_fields')) {
            $filters['search_fields'] = $model->get('search_fields');
            $model->unsetProperty('search_fields');
        }
        if ($model->get('compare_options')) {
            $filters['compare_options'] = $model->get('compare_options');
            $model->unsetProperty('compare_options');
        }
        if ($model->get('values')) {
            $filters['values'] = $model->get('values');
            $model->unsetProperty('values');
        }
        if ($model->get('values_formatted')) {
            $filters['values_formatted'] = $model->get('values_formatted');
            $model->unsetProperty('values_formatted');
        }
        if ($model->get('values_autocomplete')) {
            $filters['values_autocomplete'] = $model->get('values_autocomplete');
            $model->unsetProperty('values_autocomplete');
        }
        if ($model->get('logical_operator')) {
            $filters['logical_operator'] = $model->get('logical_operator');
            $model->unsetProperty('logical_operator');
        }
        if ($model->get('date_period')) {
            $filters['date_period'] = $model->get('date_period');
            $model->unsetProperty('date_period');
        }
        if ($model->get('sort')) {
            $filters['sort'] = $model->get('sort');
            $model->unsetProperty('sort');
        }
        if ($model->get('order')) {
            $filters['order'] = $model->get('order');
            $model->unsetProperty('order');
        }
        if ($model->get('display')) {
            $filters['display'] = $model->get('display');
            $model->unsetProperty('display');
        }

        if (isset($filters)) {
            $model->set('elements_filters', $filters, true);
        }

        return $model;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']       = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']     = sprintf("modified=now()");
        $set['modified_by']  = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_FINANCE_ANALYSIS_ITEMS);

        if (!$deleted) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_FINANCE_ANALYSIS_ITEMS);

        if (!$restored) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }
}

?>
