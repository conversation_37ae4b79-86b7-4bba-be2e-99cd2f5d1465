<?php

require_once('finance.analysis_centers.model.php');

/**
 * Finance_Analysis_Centers model class
 */
Class Finance_Analysis_Centers extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Finance_Analysis_Center';

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);

            $sort = 'ORDER BY ' . $sort;

        } else {
            $sort = 'ORDER BY fac.active desc';
        }
        $sort .= ', fac.id DESC';

        $sql['select'] = 'SELECT DISTINCT(fac.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS . ' AS fac ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_I18N . ' AS faci18n' . "\n" .
                       '  ON (fac.id=faci18n.parent_id AND faci18n.lang="' . $model_lang . '")' . "\n";

        if (preg_match('#ui18n1\.firstname#', $sort) || isset($filters['field']) && (preg_match('#ui18n1\.firstname#', $filters['field']) || !$filters['field'])) {
            //relate to users_i18n to fetch added_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (fac.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort) || isset($filters['field']) && (preg_match('#ui18n2\.firstname#', $filters['field']) || !$filters['field'])) {
            //relate to users_i18n to fetch modified_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (fac.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n3\.firstname#', $sort) || isset($filters['field']) && (preg_match('#ui18n3\.firstname#', $filters['field']) || !$filters['field'])) {
            //relate to users_i18n to fetch deleted_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                            '  ON (fac.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#fat\.#', $sort) || isset($filters['field']) && (preg_match('#fat\.#', $filters['field']) || !$filters['field'])) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES . ' AS fat' . "\n" .
                            '  ON fac.type=fat.id' . "\n";
        }
        if (preg_match('#fati18n\.name#', $sort) || isset($filters['field']) && (preg_match('#fati18n\.name#', $filters['field']) || !$filters['field'])) {
            //relate to fin_analysis_centers_types_i18n to fetch main center name
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES_I18N . ' AS fati18n' . "\n" .
                            '  ON (fac.type=fati18n.parent_id AND fati18n.lang="' . $lang . '")' . "\n";
        }

        $sql['where'] = $where . "\n";

        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';
        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        if (!empty($filters['paginate']) && !empty($ids)) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(DISTINCT fac.id) AS total';
                $sql['order'] = '';
                $sql['limit'] = '';
                if (!empty($sql_for_sort)) {
                    $sql['from'] = preg_replace('#' . preg_quote($sql_for_sort) . '#', '', $sql['from']);
                }
                $query = implode("\n", $sql);
                $filters['total'] = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $filters['total'] = count($ids);
            }
        }

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        $sql = array('select' => '',
                     'from' => '',
                     'where' => '',
                     'group' => '',
                     'order' => '',
                     'limit' => '');

        if ($registry->get('getOneRequested')) {
            //one model is searched(searchOne)
            //so getIds is not needed
            $ids = self::constructWhere($registry, $filters);
        } else {
            $ids = self::getIds($registry, $filters, $sql);
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $sql['select'] = 'SELECT DISTINCT(fac.id), fac.*, faci18n.*, fac.id AS order_idx,' . "\n" .
                         '  fat.kind, fati18n.name as type_name, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, gi18n.name as group_name, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS . ' AS fac' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_I18N . ' AS faci18n' . "\n" .
                       '  ON (fac.id=faci18n.parent_id AND faci18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to finance analysis centers type
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES . ' AS fat' . "\n" .
                       '  ON fac.type=fat.id' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS_TYPES_I18N . ' AS fati18n' . "\n" .
                       '  ON (fac.type=fati18n.parent_id AND fati18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to groups
                       'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                       '  ON (fac.group=gi18n.parent_id AND gi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to users_i18n to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fac.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to users_i18n to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fac.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to users_i18n to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fac.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        if (is_array($ids) && count($ids)) {
            //ids are returned form getIds so search and sort by them
            $sql['where'] = 'WHERE fac.id in ('.@implode(',',$ids).')';
            $sql['order'] = 'ORDER BY find_in_set(order_idx, "'.@implode(',',$ids).'")';
            $sql['limit'] = '';
        } elseif ($registry->get('getOneRequested') && !empty($ids)) {
            //one model is searched(searchOne)
            $sql['where'] = $ids;
            $sql['order'] = '';
            $sql['limit'] = 'LIMIT 1';
        } else {
            //getIds returned empty result the search will not be performed
            if (!empty($filters['paginate'])) {
                return array(array(), 0);
            } else {
                return array();
            }
        }
        $sql['group'] = 'GROUP BY fac.id';

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            $results = array($models, $filters['total']);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, $filters = array()) {
        $where[] = 'WHERE (';

        $current_user_id = $registry['currentUser']->get('id');
        $rights = $registry['currentUser']->get('rights');
        $module = 'finance';
        $controller = 'analysis_centers';
        $current_right = $registry['module'] == $module && $registry['controller'] == $controller
                             && isset($rights[$module . '_' . $controller][$registry['action']]) ?
                             $rights[$module . '_' . $controller][$registry['action']] : '';

        //additional 'where' for hiding not allowed models
        if ($current_user_id && $current_right && $current_right != 'all') {
            if ($current_right == 'mine') {
                $where[] = "fac.added_by=$current_user_id AND ";
            } elseif ($current_right == 'group') {
                $user_groups = $registry['currentUser']->get('groups');
                $where[] = "(fac.added_by=$current_user_id" .
                            (count($user_groups) ? ' OR fac.`group` IN (' . implode(',', $user_groups).')' : '') . ") AND ";
            } elseif ($current_right == 'none' || $current_right == '') {
                $where[] = ' 0 AND ';
            }
        }

        if (isset($filters['where'])) {
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#fac.deleted#', $where)) {
            $where .= ' AND fac.deleted = 0';
        }

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {

        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Builds a model object
     */
    public static function buildModelIndex(&$registry, $index) {
        $model = self::buildFromRequestIndex($registry, self::$modelName, $index);

        return $model;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $filters['where'] = array('fac.id IN (' . implode(',', $ids) . ')');
        $filters['sanitize'] = true;
        $models = self::search($registry, $filters);
        $modifiable_ids = array();
        $finance_analysis_types_ids = array();
        $count_modified = 0;

        foreach ($models as $model) {
            if ($model->checkPermissions($status, 'finance_analysis_centers')) {
                $modifiable_ids[] = $model->get('id');
                $finance_analysis_types_ids[] = $model->get('type');
                if (in_array($model->get('id'), $ids)) {
                    $count_modified++;
                }
            }
        }

        if ($count_modified) {
            $where = array();
            $where[] = General::buildClause('id', $modifiable_ids);

            //INSERT INTO THE MAIN TABLE OF THE MODEL
            $set = array();
            $set['status']       = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
            $set['modified']     = sprintf("modified=now()");
            $set['modified_by']  = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

            //query to insert into the main table
            $query = 'UPDATE ' . DB_TABLE_FINANCE_ANALYSIS_CENTERS . "\n" .
                     'SET ' . implode(', ', $set) . "\n" .
                     'WHERE ' . implode(' AND ', $where);

            //start transaction
            $db->StartTrans();
            $db->Execute($query);

            if ($status == 'deactivate') {
                //delete default distribution of types (main centers) of deactivated centers
                $finance_analysis_types_ids = array_unique($finance_analysis_types_ids);
                $filters = array('sanitize' => true,
                                 'where' => array('fat.id IN (' . implode(', ', $finance_analysis_types_ids) . ')'));
                require_once PH_MODULES_DIR . 'finance/models/finance.analysis_types.factory.php';
                $finance_analysis_types = Finance_Analysis_Types::search($registry, $filters);
                foreach ($finance_analysis_types as $finance_analysis_type) {
                    $finance_analysis_type->deleteElementsDefaultDistributionValues();
                }
            }

            //the transaction status could be checked only before CompleteTrans()
            $dbTransError = $db->HasFailedTrans();

            //complete the transaction (commit/rollback whether SQL failed or not)
            $db->CompleteTrans();

            //the result is true if there is no transaction error
            $result = !$dbTransError;
        }

        return $count_modified;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        $filters['where'] = array('fac.id IN (' . implode(', ', $ids) . ')');
        $filters['sanitize'] = true;
        $models = self::search($registry, $filters);
        $modifiable_ids = array();
        $finance_analysis_types_ids = array();
        $count_modified = 0;

        foreach ($models as $model) {
            if ($model->checkPermissions('delete', 'finance_analysis_centers')) {
                $modifiable_ids[] = $model->get('id');
                $finance_analysis_types_ids[] = $model->get('type');
                if (in_array($model->get('id'), $ids)) {
                    $count_modified++;
                }
            }
        }

        if ($count_modified) {
            //start transaction
            $db->StartTrans();

            //multiple deletion is part of the transaction
            $deleted = self::deleteMultiple($registry, $modifiable_ids, DB_TABLE_FINANCE_ANALYSIS_CENTERS);

            if (!$deleted) {
                $db->FailTrans();
            }

            //delete default distribution of types (main centers) of deleted centers
            $finance_analysis_types_ids = array_unique($finance_analysis_types_ids);
            $filters = array('sanitize' => true,
                             'where' => array('fat.id IN (' . implode(', ', $finance_analysis_types_ids) . ')'));
            require_once PH_MODULES_DIR . 'finance/models/finance.analysis_types.factory.php';
            $finance_analysis_types = Finance_Analysis_Types::search($registry, $filters);
            foreach ($finance_analysis_types as $finance_analysis_type) {
                $finance_analysis_type->deleteElementsDefaultDistributionValues();
            }

            //the transaction status could be checked only before CompleteTrans()
            $dbTransError = $db->HasFailedTrans();

            //complete the transaction (commit/rollback whether SQL failed or not)
            $db->CompleteTrans();

            //the result is true if there is no transaction error
            $result = !$dbTransError;
        }

        return $count_modified;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_FINANCE_ANALYSIS_CENTERS);

        if (!$restored) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }
}

?>
