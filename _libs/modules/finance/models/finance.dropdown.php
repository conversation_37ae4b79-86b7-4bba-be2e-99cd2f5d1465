<?php

class Finance_Dropdown extends Dropdown {

    /**
     * Get offices for selected company or all offices
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getCompanyOffices($params = array()) {

        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $company_id = !empty($params['company_id']) ? $params['company_id'] : '';
        $db = $registry['db'];
        $query = 'SELECT DISTINCT fco.office, oi18n.name, o.active' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_COMPANIES_OFFICES . ' AS fco' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_OFFICES . ' AS o' . "\n" .
                 '  ON fco.office=o.id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                 '  ON fco.office=oi18n.parent_id AND oi18n.lang="' . $lang . '"' . "\n" .
                 'WHERE o.deleted=0' . (!empty($params['active']) ? ' AND o.active=1' : '') . "\n" .
                 '  AND ' . ($company_id ? 'fco.company=' . $company_id : '1') . "\n";
        if ($registry['currentUser']->get('id') != PH_AUTOMATION_USER) {
            $offices_ids = $registry['currentUser']->get('finance_offices');
            if ($offices_ids) {
                $query .= '  AND fco.office in (' . implode(',', $offices_ids) . ')';
            } else {
                $query .= '  AND 0';
            }
        }

        // Set order
        if (isset($params['order'])) {
            $order_by = $params['order'];
        } else {
            $order_by = 'oi18n.name ASC';
        }
        $query .= "\n" .
                  'ORDER BY ' . $order_by;

        $offices = $db->GetAll($query);
        $offices_options = array();
        if (!empty($params['add_select'])) {
            $offices_options[] = array('label' => sprintf('[%s]', $registry['translater']->translate('please_select')),
                                       'option_value' => '',
                                       'active_option' => true,
                                       'class_name' => 'undefined');
        }
        foreach ($offices as $office) {
            $offices_options[] = array('label' => ($office['name'] ? $office['name'] : ''),
                                       'option_value' => $office['office'],
                                       'active_option' => $office['active']);
        }

        return $offices_options;
    }

    /**
     * Prepare data for building dropdown filter
     * Retrieve only companies and offices that are consistent with the current user permisions
     * @param array $registry
     * @return array
     */
    public static function getCompaniesOffices($params = array()) {
        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $companies_ids = $registry['currentUser']->get('finance_companies');
        $offices_ids = $registry['currentUser']->get('finance_offices');
        $options = array();
        if (!empty($companies_ids) && !empty($offices_ids)) {
            $companies_ids = implode(', ', $companies_ids);
            $offices_ids = implode(', ', $offices_ids);
            $query = "
            SELECT fc.id AS company_id, fci.name as company_name, fco.office AS office_id, oi.name as office_name
            FROM " . DB_TABLE_FINANCE_COMPANIES . " AS fc
            JOIN " . DB_TABLE_FINANCE_COMPANIES_I18N . " AS fci
              ON(fci.parent_id = fc.id AND fci.lang='{$lang}' AND fc.active=1 AND fc.deleted_by=0)
            JOIN " . DB_TABLE_FINANCE_COMPANIES_OFFICES . " AS fco
              ON(fco.company=fc.id AND fco.company IN({$companies_ids}) AND fco.office IN($offices_ids) )
            JOIN " . DB_TABLE_OFFICES . " AS o
              ON(o.id=fco.office AND o.active=1 AND o.deleted_by=0)
            JOIN " . DB_TABLE_OFFICES_I18N . " AS oi
              ON(oi.parent_id=o.id AND oi.lang='{$lang}')  ORDER BY company_name, company_id, office_name";
            $offices = $registry['db']->GetAll($query);

            $added_companies = array();
            foreach ($offices as $office) {
                $company_id = $office['company_id'];
                if (!in_array($company_id, $added_companies)) {
                    // Create company option
                    $options[] = array(
                        'label' => $office['company_name'],
                        'option_value' => $office['company_id'],
                        'active_option' => true
                    );
                    $added_companies[] = $company_id;
                }

                // Create office option
                $options[] = array(
                    'label' => "&nbsp;&nbsp;&nbsp;&nbsp;" . $office['office_name'],
                    'option_value' => $office['company_id'] . '_' . $office['office_id'],
                    'active_option' => true
                );
            }
        }
        return $options;
    }

    /**
     * Get companies data
     *
     * @param array $params - params as array, the element with index 0 is always the registry
     * Parameters may include:
     * - company_id - array or comma-separated list of ids of companies;
     * - office_id - array or comma-separated list of ids of offices;
     * - cashboxes - array or comma-separated list of ids of cashboxes;
     * - bank_accounts - array or comma-separated list of ids of bank accounts;
     * - payment_type - 'cash' or 'bank';
     * - payment_direction - 'incomes', 'expenses' or 'transfers';
     * - active - if true, get only active options (all 3 components should be active);
     * - associative - if set and true, option keys are option values, otherwise arrays are index.
     * @return array $records[]['option_value'],$records[]['label'] - result of the operation
     */
    public static function getCompaniesData($params = array()) {

        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $company_id = array();
        $office_id = array();
        $cashboxes = array();
        $bank_accounts = array();
        $payment_type = !empty($params['payment_type']) ? $params['payment_type'] : '';
        $payment_direction = !empty($params['payment_direction']) ? $params['payment_direction'] : '';
        $active = !empty($params['active']) ? true : false;

        // check if there are companies specified
        if (!empty($params['company_id'])) {
            if (is_array($params['company_id'])) {
                $company_id = $params['company_id'];
            } else {
                $company_id = preg_split('/\s*,\s*/', $params['company_id']);
            }
        }

        // check if there are offices specified
        if (!empty($params['office_id'])) {
            if (is_array($params['office_id'])) {
                $office_id = $params['office_id'];
            } else {
                $office_id = preg_split('/\s*,\s*/', $params['office_id']);
            }
        }

        // check if there are cashboxes specified
        if (!empty($params['cashboxes'])) {
            if (is_array($params['cashboxes'])) {
                $cashboxes = $params['cashboxes'];
            } else {
                $cashboxes = preg_split('/\s*,\s*/', $params['cashboxes']);
            }
        }

        // check if there are bank accounts specified
        if (!empty($params['bank_accounts'])) {
            if (is_array($params['bank_accounts'])) {
                $bank_accounts = $params['bank_accounts'];
            } else {
                $bank_accounts = preg_split('/\s*,\s*/', $params['bank_accounts']);
            }
        }

        // prepare additional conditions based on permissions of user and parameters passed to method
        $additional_where = '';

        if ($registry['currentUser']->get('id') != PH_AUTOMATION_USER) {
            // permissions by companies
            $companies_ids = $registry['currentUser']->get('finance_companies');
            // get an intersection of permitted and specified companies
            if ($companies_ids && !empty($company_id)) {
                $companies_ids = array_intersect($companies_ids, $company_id);
            }
            if ($companies_ids) {
                $additional_where .= ' AND fc.id IN (' . implode(',', $companies_ids) . ')';
            } else {
                $additional_where .= ' AND 0';
            }

            // permissions by offices
            $offices_ids = $registry['currentUser']->get('finance_offices');
            // get an intersection of permitted and specified offices
            if ($offices_ids && $office_id) {
                $offices_ids = array_intersect($offices_ids, $office_id);
            }
            if ($offices_ids) {
                $additional_where .= ' AND o.id IN (' . implode(',', $offices_ids) . ')';
            } else {
                $additional_where .= ' AND 0';
            }

            // add conditions for cashbox and bank account queries
            $additional_where_cb = $additional_where_ba = $additional_where;

            if (empty($payment_type) || strpos($payment_type, 'cash') !== false) {
                // permissions by cashboxes
                $finance_cashboxes = $registry['currentUser']->get('finance_cashboxes');

                //get cashboxes with 'add' right
                $cashboxes_ids = array();
                if ($payment_direction == 'incomes' && !empty($finance_cashboxes['PKO']['add'])) {
                    $cashboxes_ids = $finance_cashboxes['PKO']['add'];
                } elseif ($payment_direction == 'expenses' && !empty($finance_cashboxes['RKO']['add'])) {
                    $cashboxes_ids = $finance_cashboxes['RKO']['add'];
                } elseif ($payment_direction == 'transfers' && !empty($finance_cashboxes['transfer']['add'])) {
                    $cashboxes_ids = $finance_cashboxes['transfer']['add'];
                }
                // get an intersection of permitted and specified cashboxes
                if ($cashboxes_ids && $cashboxes) {
                    $cashboxes_ids = array_intersect($cashboxes_ids, $cashboxes);
                }
                if ($cashboxes_ids) {
                    $additional_where_cb .= ' AND fcb.id IN (' . implode(',', $cashboxes_ids) . ')';
                } else {
                    $additional_where_cb .= ' AND 0';
                }
            }

            if (empty($payment_type) || strpos($payment_type, 'bank') !== false) {
                // permissions by bank accounts
                $finance_bank_accounts = $registry['currentUser']->get('finance_bank_accounts');

                //get bank accounts with 'add' right
                $bank_accounts_ids = array();
                if ($payment_direction == 'incomes' && !empty($finance_bank_accounts['BP']['add'])) {
                    $bank_accounts_ids = $finance_bank_accounts['BP']['add'];
                } elseif ($payment_direction == 'expenses' && !empty($finance_bank_accounts['PN']['add'])) {
                    $bank_accounts_ids = $finance_bank_accounts['PN']['add'];
                } elseif ($payment_direction == 'transfers' && !empty($finance_bank_accounts['transfer']['add'])) {
                    $bank_accounts_ids = $finance_bank_accounts['transfer']['add'];
                }
                // get an intersection of permitted and specified bank accounts
                if ($bank_accounts_ids && $bank_accounts) {
                    $bank_accounts_ids = array_intersect($bank_accounts_ids, $bank_accounts);
                }
                if ($bank_accounts_ids) {
                    $additional_where_ba .= ' AND fba.id IN (' . implode(',', $bank_accounts_ids) . ')';
                } else {
                    $additional_where_ba .= ' AND 0';
                }
            }
        } else {
            // do not check permissions, just add parameters (if any)
            $additional_where .= (!empty($company_id) ? ' AND fc.id IN (' . implode(',', $company_id) . ')' : '') .
                                 (!empty($office_id) ? ' AND o.id IN (' . implode(',', $office_id) . ')' : '');

            // add parameters (if any) for cashbox and bank account queries
            $additional_where_cb = $additional_where .
                                   (!empty($cashboxes) ? ' AND fcb.id IN (' . implode(',', $cashboxes) . ')' : '');
            $additional_where_ba = $additional_where .
                                   (!empty($bank_accounts) ? ' AND fba.id IN (' . implode(',', $bank_accounts) . ')' : '');
        }

        $companies_data = array();

        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');
        $db = &$registry['db'];

        $sequence = array('cash', 'bank');
        if (!empty($payment_type)) {
            //valid values for payment_type are: cash, bank, cash_bank, bank_cash
            $sequence = explode('_', $payment_type);
        }

        foreach($sequence as $ptype) {
            if ($ptype == 'cash' && !preg_match('/AND 0/', $additional_where_cb)) {
                //get company + office + cashbox options
                $query = 'SELECT fco.company, fci18n.name AS company_name, fc.active AS company_active, ' . "\n" .
                    '  fco.office, oi18n.name AS office_name, o.active AS office_active, ' . "\n" .
                    '  fcb.id AS container_id, fcbi18n.name AS container_name, fcb.active AS container_active ' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_COMPANIES_OFFICES . ' AS fco' . "\n" .
                    // join to companies
                    'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES . ' AS fc' . "\n" .
                    '  ON fco.company=fc.id AND fc.deleted=0' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                    '  ON fco.company=fci18n.parent_id AND fci18n.lang="' . $lang . '"' . "\n" .
                    // join to offices
                    'LEFT JOIN ' . DB_TABLE_OFFICES . ' AS o' . "\n" .
                    '  ON fco.office=o.id AND o.deleted=0' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                    '  ON fco.office=oi18n.parent_id AND oi18n.lang="' . $lang . '"' . "\n" .
                    // join to cashboxes
                    'LEFT JOIN ' . DB_TABLE_FINANCE_CASHBOXES . ' AS fcb' . "\n" .
                    '  ON fco.company=fcb.company AND fco.office=fcb.office AND fcb.deleted=0' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_FINANCE_CASHBOXES_I18N . ' AS fcbi18n' . "\n" .
                    '  ON fcbi18n.parent_id=fcb.id AND fcbi18n.lang="' . $lang . '"' . "\n" .
                    'WHERE fcb.id IS NOT NULL' . $additional_where_cb . "\n" .
                    ($active ? '  AND fc.active=1 AND o.active=1 AND fcb.active=1' . "\n" : '') .
                    'ORDER BY fc.position, oi18n.name, container_name';
                $records = $db->GetAll($query);

                $idx = 0;
                $prev_company = '';
                foreach ($records as $record) {
                    if ($prev_company != $record['company_name']) {
                        $idx = isset($companies_data[$record['company_name']]) ? count(
                            $companies_data[$record['company_name']]
                        ) : 0;
                        $prev_company = $record['company_name'];
                    }
                    $option_value = $record['company'] . '_' . $record['office'] . '_cash_' . $record['container_id'];
                    $companies_data[$record['company_name']][(!empty($params['associative']) ? $option_value : $idx++)] =
                        array(
                            'label' => ($record['office_name'] ? $record['office_name'] : '') . ' [' . $record['container_name'] . ']',
                            'option_value' => $option_value,
                            'active_option' => ($record['company_active'] && $record['office_active'] && $record['container_active'] ? 1 : 0)
                        );
                }
            }

            if ($ptype == 'bank' && !preg_match('/AND 0/', $additional_where_ba)) {
                //get company + office + bank account options
                $query = 'SELECT fco.company, fci18n.name AS company_name, fc.active AS company_active, ' . "\n" .
                    '  fco.office, oi18n.name AS office_name, o.active AS office_active, fba.cheque,' . "\n" .
                    '  fba.id AS container_id, fbai18n.name AS container_name, fba.active AS container_active ' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_COMPANIES_OFFICES . ' AS fco' . "\n" .
                    // join to companies
                    'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES . ' AS fc' . "\n" .
                    '  ON fco.company=fc.id AND fc.deleted=0' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                    '  ON fco.company=fci18n.parent_id AND fci18n.lang="' . $lang . '"' . "\n" .
                    // join to offices
                    'LEFT JOIN ' . DB_TABLE_OFFICES . ' AS o' . "\n" .
                    '  ON fco.office=o.id AND o.deleted=0' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                    '  ON fco.office=oi18n.parent_id AND oi18n.lang="' . $lang . '"' . "\n" .
                    // join to bank accounts
                    'LEFT JOIN ' . DB_TABLE_FINANCE_BANK_ACCOUNTS . ' AS fba' . "\n" .
                    '  ON fco.company=fba.company AND (fco.office=fba.office OR fba.office=0) AND fba.deleted=0' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N . ' AS fbai18n' . "\n" .
                    '  ON fbai18n.parent_id=fba.id AND fbai18n.lang="' . $lang . '"' . "\n" .
                    'WHERE fba.id IS NOT NULL' . $additional_where_ba . "\n" .
                    ($active ? '  AND fc.active=1 AND o.active=1 AND fba.active=1' . "\n" : '') .
                    'ORDER BY fc.position, oi18n.name, container_name';
                $records = $db->GetAll($query);

                $idx = 0;
                $prev_company = '';
                foreach ($records as $record) {
                    if ($prev_company != $record['company_name']) {
                        $idx = isset($companies_data[$record['company_name']]) ? count(
                            $companies_data[$record['company_name']]
                        ) : 0;
                        $prev_company = $record['company_name'];
                    }
                    $option_value = $record['company'] . '_' . $record['office'] . '_bank_' . $record['container_id'];
                    $companies_data[$record['company_name']][(!empty($params['associative']) ? $option_value : $idx++)] =
                        array(
                            'label' => ($record['office_name'] ? $record['office_name'] : '') . ' [' . $record['container_name'] . ']',
                            'option_value' => $option_value,
                            'active_option' => ($record['company_active'] && $record['office_active'] && $record['container_active'] ? 1 : 0)
                        );
                    //if we are in incomes or expenses reasons and it's allowed for this bank account
                    //to have cheque payments, we add another option for it
                    if ($record['cheque'] && in_array(
                            $registry['controller'],
                            array('incomes_reasons', 'expenses_reasons')
                        )) {
                        $option_value = $record['company'] . '_' . $record['office'] . '_cheque_' . $record['container_id'];
                        $companies_data[$record['company_name']][(!empty($params['associative']) ? $option_value : $idx++)] =
                            array(
                                'label' => ($record['office_name'] ? $record['office_name'] : '') . ' [' . $record['container_name'] . '] (' .
                                    $registry['translater']->translate('payment_type_cheque') . ')',
                                'option_value' => $option_value,
                                'active_option' => ($record['company_active'] && $record['office_active'] && $record['container_active'] ? 1 : 0)
                            );
                    }
                }
            }
        }

        return $companies_data;
    }

    /**
     * Get warehouse data
     *
     * @param array $params - params as array, the element with index 0 is always the registry
     * Parameters may include:
     * - company_id - array or comma-separated list of ids of companies;
     * - office_id - array or comma-separated list of ids of offices;
     * - warehouse_id - array or comma-separated list of ids of warehouses;
     * - active - if true, get only active options (all 3 components should be active).
     * @return array $records[]['option_value'],$records[]['label'] - result of the operation
     */
    public static function getWarehouseData($params) {
        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $company_id = array();
        $office_id = array();
        $warehouse_id = array();
        $active = !empty($params['active']) ? true : false;

        // check if there are companies, offices or warehouses specified as filters
        foreach (array('company_id', 'office_id', 'warehouse_id') as $param) {
            if (!empty($params[$param])) {
                if (is_array($params[$param])) {
                    $$param = $params[$param];
                } else {
                    $$param = preg_split('#\s*,\s*#', $params[$param]);
                }
            }
        }

        // prepare additional conditions based on permissions of user and parameters passed to method
        $additional_where = '';

        if ($registry['currentUser']->get('id') != PH_AUTOMATION_USER) {
            // permissions by companies
            $companies_ids = $registry['currentUser']->get('finance_companies');
            // get an intersection of permitted and specified companies
            if ($companies_ids && !empty($company_id)) {
                $companies_ids = array_intersect($companies_ids, $company_id);
            }
            if ($companies_ids) {
                $additional_where .= ' AND fc.id IN (' . implode(',', $companies_ids) . ')';
            } else {
                $additional_where .= ' AND 0';
            }

            // permissions by offices
            $offices_ids = $registry['currentUser']->get('finance_offices');
            // get an intersection of permitted and specified offices
            if ($offices_ids && $office_id) {
                $offices_ids = array_intersect($offices_ids, $office_id);
            }
            if ($offices_ids) {
                $additional_where .= ' AND o.id IN (' . implode(',', $offices_ids) . ')';
            } else {
                $additional_where .= ' AND 0';
            }

            // no permissions by warehouses yet
            if ($warehouse_id) {
                $additional_where .= ' AND fwh.id IN (' . implode(',', $warehouse_id) . ')';
            }
        } else {
            // do not check permissions, just add parameters (if any)
            $additional_where .= (!empty($company_id) ? ' AND fc.id IN (' . implode(',', $company_id) . ')' : '') .
                                 (!empty($office_id) ? ' AND o.id IN (' . implode(',', $office_id) . ')' : '') .
                                 (!empty($warehouse_id) ? ' AND fwh.id IN (' . implode(',', $warehouse_id) . ')' : '');
        }

        $warehouses_data = array();

        $db = &$registry['db'];

        if (!preg_match('/AND 0/', $additional_where)) {
            //get company + office + warehouse options
            $query = 'SELECT fci18n.name AS company_name,' . "\n" .
                     '  CONCAT(' . "\n" .
                     '      IF(oi18n.name IS NOT NULL, oi18n.name, ""),' . "\n" .
                     '      " [",' . "\n" .
                     '      IF(fwhi18n.name IS NOT NULL, fwhi18n.name, ""),' . "\n" .
                     '      "]",' . "\n" .
                     '      IF(fwd.id IS NOT NULL, CONCAT("(", "' . $registry["translater"]->translate('finance_warehouses_inspection_lock') . '",")"), "")) AS label,' . "\n" .
                     '  CONCAT_WS(\'_\', fco.company, fco.office, fwh.id) AS option_value, fwd.id as locker,' . "\n" .
                     '  IF(fc.active=1 AND o.active=1 AND fwh.active=1, 1, 0) AS active_option, fwd.status as locker_status' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_COMPANIES_OFFICES . ' AS fco' . "\n" .
                     // join to companies
                     'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES . ' AS fc' . "\n" .
                     '  ON fco.company=fc.id AND fc.deleted=0' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                     '  ON fco.company=fci18n.parent_id AND fci18n.lang="' . $lang . '"' . "\n" .
                     // join to offices
                     'LEFT JOIN ' . DB_TABLE_OFFICES . ' AS o' . "\n" .
                     '  ON fco.office=o.id AND o.deleted=0' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                     '  ON fco.office=oi18n.parent_id AND oi18n.lang="' . $lang . '"' . "\n" .
                     // join to warehouses
                     'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES . ' AS fwh' . "\n" .
                     '  ON fco.company=fwh.company AND fco.office=fwh.office AND fwh.deleted=0' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_I18N . ' AS fwhi18n' . "\n" .
                     '  ON fwhi18n.parent_id=fwh.id AND fwhi18n.lang="' . $lang . '"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                     '  ON fwd.id = fwh.locked AND fwd.annulled_by = 0 AND fwd.status != "finished"' . "\n" .
                     'WHERE fwh.id IS NOT NULL' . $additional_where . "\n" .
                     ($active ? '  AND fc.active=1 AND o.active=1 AND fwh.active=1' . "\n" : '') .
                     'ORDER BY fc.position ASC, fco.office ASC, fwhi18n.name ASC, fwh.id ASC';
            $records = $db->GetAll($query);

            // company name as optgroup, other fields as option
            foreach ($records as $record) {
                $warehouses_data[array_shift($record)][] = $record;
            }
        }

        return $warehouses_data;
    }

    /**
     * Get warehouses managers for specified warehouse
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getWarehousesEmployees($params = array()) {

        $registry = $params[0];
        $db = &$registry['db'];
        $query = 'SELECT fw.employees' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_WAREHOUSES . ' AS fw' . "\n" .
                 'WHERE id = \'' . $params['warehouse'] . '\'';
        $employees = $db->GetOne($query);

        if ($employees) {
            $query = 'SELECT c.id as option_value, IFNULL(CONCAT(ci18n.name, \' \', ci18n.lastname), \'\') as label, c.active AS active_option' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                     '  ON c.id = ci18n.parent_id AND ci18n.lang = \'' . self::defineModelLang($params) . '\'' . "\n" .
                     'WHERE c.deleted=0' . (!empty($params['active']) ? ' AND c.active=1' : '') . "\n" .
                     '  AND c.id IN (' . $employees . ')' . "\n" .
                     'ORDER BY c.id IN (' . $employees . ')';
            $employees = $db->GetAll($query);
        } else {
            $employees = array();
        }

        return $employees;
    }

    /**
     * Get bank accounts for certain company
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getBankAccounts($params = array()) {

        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $company_id = isset($params['company_id']) ? $params['company_id'] : 0;
        $office_id = (isset($params['office_id']) ? $params['office_id'] : 0);
        $db = $registry['db'];
        $query = 'SELECT DISTINCT id, name, active ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_BANK_ACCOUNTS . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N . "\n" .
                 '  ON id=parent_id AND lang="' . $lang . '"' . "\n" .
                 'WHERE deleted=0' . (!empty($params['active']) ? ' AND active=1' : '') .
                 ' AND ' . ($company_id ? 'company=' . $company_id : '1') .
                 ' AND ' . ($office_id ? '(office=' . $office_id . ' OR office=0)' : '1') .
                 (isset($params['where']) ? ' AND ' . $params['where'] : '') . "\n" .
                 'ORDER BY name ASC';
        $bank_accounts = $db->GetAll($query);
        $bank_accounts_options = array();
        foreach ($bank_accounts as $bank_account) {
            $bank_accounts_options[] = array('label' => ($bank_account['name'] ? $bank_account['name'] : ''),
                                             'option_value' => $bank_account['id'],
                                             'active_option' => $bank_account['active']);
        }

        return $bank_accounts_options;
    }

    /**
     * Get cashboxes for certain company and office
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getCashboxes($params = array()) {

        $registry = $params[0];
        $lang = self::defineModelLang($params);
        $company_id = isset($params['company_id']) ? $params['company_id'] : 0;
        $office_id = isset($params['office_id']) ? $params['office_id'] : 0;
        $db = $registry['db'];
        $query = 'SELECT DISTINCT id, name, active ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_CASHBOXES . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_CASHBOXES_I18N . "\n" .
                 '  ON id=parent_id AND lang="' . $lang . '"' . "\n" .
                 'WHERE deleted=0' . (!empty($params['active']) ? ' AND active=1' : '') .
                 ' AND '. ($company_id ? 'company=' . $company_id : '1') .
                 ' AND '. ($office_id ? 'office=' . $office_id : '1') .
                 (isset($params['where']) ? ' AND ' . $params['where'] : '') . "\n" .
                 'ORDER BY name ASC';
        $cashboxes = $db->GetAll($query);
        $cashboxes_options = array();
        foreach ($cashboxes as $cashbox) {
            $cashboxes_options[] = array('label' => ($cashbox['name'] ? $cashbox['name'] : ''),
                                         'option_value' => $cashbox['id'],
                                         'active_option' => $cashbox['active']);
        }

        return $cashboxes_options;
    }

    /**
     * Get all models for finance documents types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getFinanceDocumentsTypesModels($params = array()) {
        $registry = $params[0];

        $models = array('Finance_Incomes_Reason',
                        'Finance_Expenses_Reason',
                        'Finance_Warehouses_Document',
                        'Finance_Invoices_Template',
                        'Finance_Annulment');
        $options = array();
        foreach ($models as $model) {
            $options[] = array('option_value' => $model,
                               'label' => $registry['translater']->translate(strtolower($model)));
        }

        return $options;
    }

    /**
     * Get finance documents types for adding (ToDo: check if this is not obsolete!!!)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getDocumentsModels($params = array()) {

        $registry = $params[0];

        $models = $registry['config']->getParamAsArray('finance', 'finance_documents_models');

        $options = array();
        foreach ($models as $model) {
            $options[] = array('label' => $registry['translater']->translate(strtolower($model)),
                               'option_value' => $model);
        }

        return $options;
    }

    /**
     * Gets financial documents models, which sections can be created for.
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getDocumentsModelsForSections($params = array()) {

        $registry = $params[0];

        $models = $registry['config']->getParamAsArray('finance', 'finance_documents_models_for_sections');

        $options = array();
        foreach ($models as $model) {
            $options[] = array('label' => $registry['translater']->translate(strtolower($model)),
                               'option_value' => $model);
        }

        return $options;
    }

    /**
     * Gets financial models, which use counters.
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getFinanceCountersModels($params = array()) {
        $registry = $params[0];

        $models = array(
            'Finance_Payment',
            'Finance_Transfer',
            'Finance_Incomes_Reason',
            'Finance_Expenses_Reason',
            'Finance_Warehouses_Document',
            'Finance_Annulment'
        );
        $options = array();
        foreach ($models as $model) {
            $options[] = array('label' => $registry['translater']->translate(strtolower($model)),
                               'option_value' => $model);
        }

        return $options;
    }

    /**
     * Gets financial documents statuses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getStatuses($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');

        $statuses = array(
            'opened'    => array('label' => $registry['translater']->translate('finance_documents_status_opened'),
                                 'option_value' => 'opened'),
            'locked'    => array('label' => $registry['translater']->translate('finance_documents_status_locked'),
                                 'option_value' => 'locked'),
            'finished'  => array('label' => $registry['translater']->translate('finance_documents_status_finished'),
                                 'option_value' => 'finished')
        );

        $model_types = array();
        if (!empty($params['model_types'])) {
            $model_types = array_map('intval', $params['model_types']);

            // special behaviour for statuses of warehouse documents
            $model = isset($params['model']) ? $params['model'] :
                General::plural2singular($registry['module'] . '_' . $registry['controller']);
            if (strtolower($model) == 'finance_warehouses_document') {
                if (count($model_types) == 1) {
                    $model_types = reset($model_types);
                    $statuses_type = array();
                    foreach ($statuses as $status => $status_option) {
                        // add status options only for existing labels
                        $status_option['label'] = $registry['translater']->translate('finance_documents_status_' . $status . '_' . $model_types);
                        if ($status_option['label']) {
                            $statuses_type[$status] = $status_option;
                        }
                    }
                    // if there are custom labels for type
                    if ($statuses_type) {
                        $statuses = $statuses_type;
                    }
                    unset($statuses_type);
                }
                return $statuses;
            }
        } else {
            return $statuses;
        }

        $sql = array('select' => '', 'from' => '', 'where' => '');

        $sql['where'] = 'WHERE fds.lang = \'' . $registry['lang'] . "'\n" .
                        '  AND fds.doc_type = \'' . array_shift($model_types) . "'\n" .
                        '  AND fds.deleted_by = 0' . "\n" .
                        '  AND fds.active=1';

        $sql['select'] = 'SELECT fds.status, CONCAT(\'substatus_\', fds.id) AS option_value, ' . "\n" .
                         '       CONCAT(\'&nbsp;&nbsp;&bull;&nbsp;\', fds.name) AS label,' . "\n" .
                         '       fds.name AS extended_value';
        $sql['from']   = 'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_STATUSES . ' AS fds'. "\n";

        foreach ($model_types as $key => $model_type) {
            $sql['from'] .= 'JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_STATUSES . ' AS fds' . $key . "\n" .
                            '  ON fds.status=fds' . $key . '.status AND fds.name=fds' . $key .'.name AND fds' . $key . '.doc_type = \'' . $model_type . '\'' . "\n" .
                            '     AND fds' . $key . '.deleted_by=0 AND fds' . $key . '.active=1' . "\n";
        }
        $query = implode("\n", $sql);
        $records = $registry['db']->getAll($query);

        $sub_statuses = array();
        foreach ($records as $key => $record) {
            $status = $record['status'];
            unset($record['status']);
            $sub_statuses[$status][] = $record;
        }

        if (!empty($sub_statuses)) {
            $result = array();
            foreach ($statuses as $status => $value) {
                if (isset($sub_statuses[$status])) {
                    $result = array_merge($result, array($value), $sub_statuses[$status]);
                } else {
                    $result[] = $value;
                }
            }
        } else {
            return $statuses;
        }

        return $result;
    }

    /**
     * Gets payment types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getFinancePaymentsTypes($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_payments.ini');

        $types = array(array('label'=> $registry['translater']->translate('finance_payments_type_PKO'),
                             'option_value' => 'PKO'),
                       array('label'=> $registry['translater']->translate('finance_payments_type_RKO'),
                             'option_value' => 'RKO'),
                       array('label'=> $registry['translater']->translate('finance_payments_type_BP'),
                             'option_value' => 'BP'),
                       array('label'=> $registry['translater']->translate('finance_payments_type_PN'),
                             'option_value' => 'PN'),
                       array('label'=> $registry['translater']->translate('finance_payments_type_TR'),
                             'option_value' => 'TR')
                      );

        return $types;
    }

    /**
     * Get payment directions
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return multitype:multitype:string NULL- result of the operation
     */
    public static function getFinancePaymentsDirection(array $params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_payments.ini');

        return array(
            array('label' => $registry['translater']->translate('finance_payments_direction_income'), 'option_value' => '1'),
            array('label' => $registry['translater']->translate('finance_payments_direction_expense'), 'option_value' => '-1'),
        );
    }

    /**
     * Gets invoice templates types (advance, normal, finishing invoice)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getInvoicesTemplatesTypes($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');

        $types = array(0 => array('label' => $registry['translater']->translate('contracts_advance_invoice'),
                                  'option_value' => PH_FINANCE_TYPE_INVOICE_TEMPLATE_ADVANCE . ''),
                       1 => array('label' => $registry['translater']->translate('contracts_invoice'),
                                  'option_value' => PH_FINANCE_TYPE_INVOICE_TEMPLATE_NORMAL . ''),
                       2 => array('label' => $registry['translater']->translate('contracts_finish_invoice'),
                                  'option_value' => PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL . ''));

        return $types;
    }

    /**
     * Get user options for "observer" field in invoice templates
     *
     * @param array $params
     * @return mixed[][] - dropdown options
     */
    public function getInvoicesTemplatesResponsibles($params) {
        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'contracts/i18n/' . $registry['lang'] . '/contracts.ini');
        return Contracts_Dropdown::getResponsibles($params);
    }

    /**
     * Gets repayment plans statuses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getRepaymentPlansStatuses($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_repayment_plans.ini');

        $statuses = array('opened' => array('label'=> $registry['translater']->translate('finance_repayment_plans_status_opened'),
                                            'option_value' => 'opened'),
                          'locked' => array('label'=> $registry['translater']->translate('finance_repayment_plans_status_locked'),
                                            'option_value' => 'locked'),
                          'finished' => array('label'=> $registry['translater']->translate('finance_repayment_plans_status_finished'),
                                              'option_value' => 'finished'),
                    );

        return $statuses;
    }

    /**
     * Gets payment statuses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getPaymentsStatuses($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_payments.ini');

        $statuses = array('added' => array('label'=> $registry['translater']->translate('finance_payments_status_added'),
                                            'option_value' => 'added'),
                          'finished' => array('label'=> $registry['translater']->translate('finance_payments_status_finished'),
                                              'option_value' => 'finished')
                    );

        return $statuses;
    }

    /**
     * Gets statuses for budgets.
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getBudgetsStatuses($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_budgets.ini');

        $statuses = array('preparation' => array('label'=> $registry['translater']->translate('finance_budgets_status_preparation'),
                                                 'option_value' => 'preparation'),
                          'progress'    => array('label'=> $registry['translater']->translate('finance_budgets_status_progress'),
                                                 'option_value' => 'progress'),
                          'approved'    => array('label'=> $registry['translater']->translate('finance_budgets_status_approved'),
                                                 'option_value' => 'approved'),
                          'obsolete'    => array('label'=> $registry['translater']->translate('finance_budgets_status_obsolete'),
                                                 'option_value' => 'obsolete')
                    );

        return $statuses;
    }

    /**
     * Gets methods for budgets
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getBudgetsMethods($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_budgets.ini');

        $methods  = array('bottomup' => array('label'=> $registry['translater']->translate('finance_budgets_method_bottomup'),
                                              'option_value' => 'bottomup'),
                          'topdown'  => array('label'=> $registry['translater']->translate('finance_budgets_method_topdown'),
                                              'option_value' => 'topdown')
                    );

        return $methods;
    }

    /**
     * Gets transaction expenses operations (withdraw, transfer, receive, deposit)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getTransactionExpensesOperations($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_transaction_expenses.ini');

        $ops = array(array('label'=> $registry['translater']->translate('finance_transaction_expenses_operation_withdraw'),
                           'option_value' => 'withdraw'),
                     array('label'=> $registry['translater']->translate('finance_transaction_expenses_operation_transfer'),
                           'option_value' => 'transfer'),
                     array('label'=> $registry['translater']->translate('finance_transaction_expenses_operation_receive'),
                           'option_value' => 'receive'),
                     array('label'=> $registry['translater']->translate('finance_transaction_expenses_operation_deposit'),
                           'option_value' => 'deposit')
                    );
        return $ops;
    }

    /**
     * Gets payment types (bank, cash, cheque)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getPaymentsTypes($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');

        $types = array(array('label' => $registry['translater']->translate('finance_payment_type_bank'),
                             'option_value' => 'bank'),
                       array('label' => $registry['translater']->translate('finance_payment_type_cash'),
                             'option_value' => 'cash')
                 );
        if ($registry['controller'] != 'transaction_expenses') {
            $types[] = array('label' => $registry['translater']->translate('finance_payment_type_cheque'),
                             'option_value' => 'cheque');
        }
        return $types;
    }

    /**
     * Gets bank account types
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getBankAccountsTypes($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_bank_accounts.ini');

        $types = array(array('label'=> $registry['translater']->translate('finance_bank_accounts_type_account'),
                             'option_value' => 'account'),
                       array('label'=> $registry['translater']->translate('finance_bank_accounts_type_debitcard'),
                             'option_value' => 'debitcard'),
                       array('label'=> $registry['translater']->translate('finance_bank_accounts_type_creditcard'),
                             'option_value' => 'creditcard'),
                       array('label'=> $registry['translater']->translate('finance_bank_accounts_type_creditline'),
                             'option_value' => 'creditline'),
                       array('label'=> $registry['translater']->translate('finance_bank_accounts_type_overdraft'),
                             'option_value' => 'overdraft')
                      );
        return $types;

    }

    /**
     * Gets recurring periods (monthly, weekly, yearly, daily)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getRecurrenceTypes($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');

        $options = array();
        $options[] = array('label' => $registry['translater']->translate('finance_monthlyByDate'),
                           'option_value' => 'monthlyByDate');
        $options[] = array('label' => $registry['translater']->translate('finance_weekly'),
                           'option_value' => 'weekly');
        $options[] = array('label' => $registry['translater']->translate('finance_yearly'),
                           'option_value' => 'yearly');
        $options[] = array('label' => $registry['translater']->translate('finance_daily'),
                           'option_value' => 'daily');

        return $options;
    }

    /**
     * Gets analysis types (income, expense, both)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getAnalysisTypesKinds($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_analysis_types.ini');

        $options = array();
        $options[] = array('label' => $registry['translater']->translate('finance_analysis_types_kind_income'),
                           'option_value' => 'income');
        $options[] = array('label' => $registry['translater']->translate('finance_analysis_types_kind_expense'),
                           'option_value' => 'expense');
        $options[] = array('label' => $registry['translater']->translate('finance_analysis_types_kind_both'),
                           'option_value' => 'both');

        return $options;
    }

    /**
     * Gets distribution statuses for finance incomes/expenses reasons
     * (no distribution, distributed, not distributed).
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getDistributionStatuses($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');

        $options = array(array('label' => $registry['translater']->translate('finance_distribution_none'),
                               'option_value' => PH_FINANCE_DISTRIBUTION_NONE),
                         array('label' => $registry['translater']->translate('finance_distribution_yes'),
                               'option_value' => PH_FINANCE_DISTRIBUTION_YES),
                         array('label' => $registry['translater']->translate('finance_distribution_no'),
                               'option_value' => PH_FINANCE_DISTRIBUTION_NO)
                        );

        return $options;
    }

    /**
     * Gets finance documents payment statuses (unpaid, partially paid, thoroughly paid, there will be no payment, invoiced)
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getDocsPaymentStatuses($params = array()) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');

        $options = array(array('label' => $registry['translater']->translate('finance_payment_status_unpaid'),
                               'option_value' => 'unpaid'),
                         array('label' => $registry['translater']->translate('finance_payment_status_partial'),
                               'option_value' => 'partial'),
                         array('label' => $registry['translater']->translate('finance_payment_status_paid'),
                               'option_value' => 'paid'),
                         array('label' => $registry['translater']->translate('finance_payment_status_nopay'),
                               'option_value' => 'nopay')
        );

        if (isset($params['model_types'])) {
            $model_types = array_values($params['model_types']);
            $model_types = array_unique($model_types);
            if (count($model_types) == 1) {
                $selected_model_type = reset($model_types);
                if ($selected_model_type == PH_FINANCE_TYPE_PRO_INVOICE || $selected_model_type == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                    $options[] = array(
                        'label' => $registry['translater']->translate('finance_payment_status_invoiced'),
                        'option_value' => 'invoiced'
                    );
                }
            }
        }

         return $options;
    }

    /**
     * Gets income/expense items tree
     *
     * @param array params - parameters to specify search:
     * - type - income/expese;
     * - item_type - income/expese (alternative parameter);
     * - exclude_root - flag whether to exclude root node of tree;
     * - leaves_active_only - flag whether only leaf nodes are active options.
     * @return array $records[]['option_value'],$records[]['label']- result of the operation
     */
    public static function getFinanceAnalysisItems($params = array()) {
        $registry = $params[0];

        $type = 'income';
        if (!empty($params['type'])) {
            $type = $params['type'];
        } elseif (!empty($params['item_type'])) {
            $type = $params['item_type'];
        }
        $filters = array(
            'model_lang' => self::defineModelLang($params),
            'where' => array(
                "fai.type = '{$type}'",
            ),
        );

        // GET ITEMS
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';
        $analysis_items = Finance_Analysis_Items::getTree($registry, $filters);

        if (!empty($params['exclude_root'])) {
            array_shift($analysis_items);
            $level_correction = -1;
        } else {
            $level_correction = 0;
        }

        $indent_char = '-';
        if (preg_match('#view.*|generate|print#', $registry['action'])) {
            $indent_char = '';
        }

        $records = array();
        foreach ($analysis_items as $analysis_item) {
            $records[] = array(
                'label' => sprintf('%s%s', str_repeat($indent_char, $analysis_item->get('level') + $level_correction), $analysis_item->get('name')),
                'option_value' => $analysis_item->get('id'),
                'active_option' => (!$analysis_item->isActivated() || $analysis_item->isDeleted() || (!empty($params['leaves_active_only']) && ($analysis_item->get('right') - $analysis_item->get('left')) > 1)) ? 0 : 1,
            );
        }

        return $records;
    }

    /**
     * Gets available finance invoice statuses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getInvoiceStatuses($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_incomes_reasons.ini');

        $ops = array(array('label'=> $registry['translater']->translate('finance_incomes_reasons_invoice_status_not_invoicable'),
                           'option_value' => 'not_invoicable'),
                     array('label'=> $registry['translater']->translate('finance_incomes_reasons_invoice_status_not_invoiced'),
                           'option_value' => 'not_invoiced'),
                     array('label'=> $registry['translater']->translate('finance_incomes_reasons_invoice_status_partial'),
                           'option_value' => 'partial'),
                     array('label'=> $registry['translater']->translate('finance_incomes_reasons_invoice_status_invoiced'),
                           'option_value' => 'invoiced')
        );
        return $ops;
    }

    /**
     * Gets available finance handovered statuses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getHandoveredStatuses($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');

        $ops = array(array('label'=> $registry['translater']->translate('finance_handovered_none'),
                           'option_value' => 'none'),
                     array('label'=> $registry['translater']->translate('finance_handovered_not'),
                           'option_value' => 'not'),
                     array('label'=> $registry['translater']->translate('finance_handovered_partial'),
                           'option_value' => 'partial'),
                     array('label'=> $registry['translater']->translate('finance_handovered_full'),
                           'option_value' => 'full')
        );
        return $ops;
    }

    /**
     * Gets allocated costs statuses of expenses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label'] - result of the operation
     */
    public static function getExpensesAllocatedStatuses($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_expenses_reasons.ini');

        return array(
            array(
                'label'=> $registry['translater']->translate('finance_expenses_reasons_allocated_status_disabled'),
                'option_value' => 'disabled',
            ),
            array(
                'label'=> $registry['translater']->translate('finance_expenses_reasons_allocated_status_enabled'),
                'option_value' => 'enabled',
            ),
            array(
                'label'=> $registry['translater']->translate('finance_expenses_reasons_allocated_status_allocated'),
                'option_value' => 'allocated',
            ),
        );
    }

    /**
     * Gets available payments distribution statuses
     *
     * @param array $params- params as array, the element with index 0 is always the registry
     * @return array $records[]['option_value'], $records[]['label']- result of the operation
     */
    public static function getPaymentsDistributionStatuses($params) {

        $registry = $params[0];
        $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_payments.ini');

        $ops = array(array('label'=> $registry['translater']->translate('finance_payments_distribution_status_not_distributed'),
                           'option_value' => 'not_distributed'),
                     array('label'=> $registry['translater']->translate('finance_payments_distribution_status_partial'),
                           'option_value' => 'partial'),
                     array('label'=> $registry['translater']->translate('finance_payments_distribution_status_distributed'),
                           'option_value' => 'distributed'),
        );
        return $ops;
    }
}

?>
