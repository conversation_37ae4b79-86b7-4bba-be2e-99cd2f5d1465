<?php

require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.model.php';

/**
 * Finance_Invoices_Templates model class
 */
class Finance_Invoices_Templates extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Finance_Invoices_Template';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 5;

    /**
     * Defines the size of chunks for invoices issue
     */
    private static $_invoices_chunk = 50;

    public static $alias = 'fit';

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @param array $sql -
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            $sort = 'ORDER BY ' . $sort;
        } else {
            $sort = 'ORDER BY fit.id DESC';
        }

        if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere'] ||
            isset($filters['get_info_id']) && $filters['get_info_id']) {
            $sql['select'] = 'SELECT DISTINCT(fiti.id)';
        } else {
            $sql['select'] = 'SELECT DISTINCT(fit.id)';
        }

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                       '  ON (fit.id=fiti18n.parent_id AND fiti18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                       '  ON fiti.parent_id = fit.id' . "\n" .
                       'JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                       '   ON co.id = fit.contract_id AND co.deleted_by = 0' . "\n";

        if (preg_match('#fdd(i18n)?\.#', $where) || preg_match('#fdd(i18n)?\.#', $sort)) {
            //relate to departments
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                            '  ON fdd.model = "' . self::$modelName .  '" AND fdd.model_id = fit.id' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS fddi18n' . "\n" .
                            '  ON fdd.id = fddi18n.parent_id AND fddi18n.lang = "' . $model_lang . '"'. "\n";
        }

        if (preg_match('#fdti18n\.name#', $sort)) {
            //relate to companies
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                            '  ON fdti18n.parent_id = fit.type AND fdti18n.lang = "' . $model_lang . '"' . "\n";
        }
        if (preg_match('#ci18n\.name#', $sort) || isset($filters['field']) && (preg_match('#ci18n\.name#', $filters['field']) || !$filters['field'])) {
            //relate to customers
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (fit.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")'. "\n";
        }
        // process search by trademark keyword in the simple search
        if (preg_match('#fit\.trademark\s*LIKE\s*#', $where)) {
            $where = preg_replace('#fit\.trademark#', 'tm_ss_ni18n.name', $where);
            //relate to nomenclatures
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS tm_ss_ni18n' . "\n" .
                            '  ON (fit.trademark=tm_ss_ni18n.parent_id AND tm_ss_ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ni18n\.name#', $sort) || preg_match('#ni18n\.name#', $where)) {
            //relate to nomenclatures
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                            '  ON (fit.trademark=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#pi18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'pi18n.name') {
            //relate to projects
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                            '  ON (fit.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#fci18n\.name#', $sort)) {
            //relate to companies
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                            '  ON (fit.company=fci18n.parent_id AND fci18n.lang="' . $model_lang . '")'. "\n";
        }
        if (preg_match('#oi18n\.name#', $sort)) {
            //relate to offices
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                           '  ON (fit.office=oi18n.parent_id AND oi18n.lang="' . $model_lang . '")'. "\n";
        }
        if (preg_match('#container_name#', $sort) || preg_match('#fbai18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'container_name') {
            //relate to bank accounts i18n
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N . ' AS fbai18n' . "\n" .
                            '  ON (fit.container_id=fbai18n.parent_id AND fit.payment_type=\'bank\' AND fbai18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#container_name#', $sort) || preg_match('#fcbi18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'container_name') {
            //relate to cashboxes i18n
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_CASHBOXES_I18N . ' AS fcbi18n' . "\n" .
                            '  ON (fit.container_id=fcbi18n.parent_id AND fit.payment_type=\'cash\' AND fcbi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#container_name#', $where)) {
            $where = preg_replace('#container_name#', 'IFNULL(fcbi18n.name, fbai18n.name)', $where);
        }
        if (preg_match('#container_name#', $sort)) {
            $sort = preg_replace('#(container_name)#', 'IFNULL(fcbi18n.name, fbai18n.name)', $sort);
        }
        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to users_i18n to fetch added_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (fit.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to users_i18n to fetch modified_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (fit.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }

        if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere'] ||
            isset($filters['get_info_id']) && $filters['get_info_id']) {
            $where .= ' AND fiti.id IS NOT NULL AND fiti.id != \'\'';
        }
        if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere']) {
            $where .= ' AND fiti.observer_response = "none"';
        }

        if (!empty($filters['search_fcp_placeholder'])) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_VARIABLES_META . ' AS vm ' . "\n" .
                            '  ON vm.model="Contract" AND vm.name="default_financial_contact_person"' . "\n" .
                            '  AND vm.id=(SELECT id FROM ' . DB_TABLE_VARIABLES_META . "\n" .
                            '      WHERE `model`="Contract" AND `name`="default_financial_contact_person" ' . "\n" .
                            '      AND (model_type=co.type OR model_type=0) ' . "\n" .
                            '      ORDER BY model_type DESC LIMIT 1)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_VARIABLES_CSTM . ' AS vcstm ' . "\n" .
                            '  ON vm.id=vcstm.var_id AND vcstm.model_id=\'0\' AND vm.model=vcstm.model';
        }

        $sql['where'] = $where . "\n";

        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';
        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        if (!empty($filters['paginate']) && !empty($ids)) {
            //get the total count
            if ($sql['limit']) {
                if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere'] ||
                    isset($filters['get_info_id']) && $filters['get_info_id']) {
                    $sql['select'] = 'SELECT COUNT(DISTINCT fiti.id) AS total';
                } else {
                    $sql['select'] = 'SELECT COUNT(DISTINCT fit.id) AS total';
                }
                $sql['order'] = '';
                $sql['limit'] = '';
                $query = implode("\n", $sql);
                $filters['total'] = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $filters['total'] = count($ids);
            }
        }

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        $sql = array('select' => '',
                     'from' => '',
                     'where' => '',
                     'group' => '',
                     'order' => '',
                     'limit' => '');

        if (!isset($filters['getEmpty']) || isset($filters['getEmpty']) && !$filters['getEmpty']) {
            if ($registry->get('getOneRequested')) {
                //one model is searched(searchOne)
                //so getIds is not needed
                $ids = self::constructWhere($registry, $filters);
            } else {
                $ids = self::getIds($registry, $filters, $sql);
            }
        } else {
            $ids = 'WHERE 1';
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $prec = $registry['config']->getSectionParams('precision');
        $prTotal = $prec['gt2_total'];
        $prTotalVat = $prec['gt2_total_vat'];
        $prTotalWithVat = $prec['gt2_total_with_vat'];

        if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere'] ||
            isset($filters['get_info_id']) && $filters['get_info_id']) {
            $sql['select'] = 'SELECT fit.*, fiti.parent_id as template_id, fiti.id, fiti.id as order_idx,' . "\n" .
                             '  fiti.observer_response, fit.issue_date, fiti.issue_date as template_issue_date,';
        } else {
            $sql['select'] = 'SELECT DISTINCT(fit.id), fit.*, fit.id as order_idx, fit.employee AS employee1,';
        }
        $sql['select'] .= "fiti18n.*, fci18n.name as company_name, u0.invoice_code,                           
                            CAST(ROUND(fit.total_without_discount, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_without_discount,                          
                            CAST(ROUND(fit.total_discount_value, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_discount_value,                          
                            CAST(ROUND(fit.total_discount_percentage, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_discount_percentage,                          
                            CAST(ROUND(fit.total_surplus_value, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_surplus_value,                          
                            CAST(ROUND(fit.total_surplus_percentage, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_surplus_percentage,                          
                            CAST(ROUND(fit.total, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total,                          
                            CAST(ROUND(fit.total_vat, {$prTotalVat}) AS DECIMAL(25,{$prTotalVat})) AS total_vat,                          
                            CAST(ROUND(fit.total_with_vat, {$prTotalWithVat}) AS DECIMAL(25,{$prTotalWithVat})) AS total_with_vat,                          
                            oi18n.name as office_name, '{$model_lang}' as model_lang,                           
                            IF (fit.payment_type = 'bank', fbai18n.name, fcbi18n.name) AS container_name,                          
                            CONCAT(ci18n.name, ' ', ci18n.lastname) as customer_name, c.code AS customer_code,                           
                            pi18n.name AS project_name, p.code AS project_code,                           
                            IF (fit.trademark > 0, fit.trademark, '') AS trademark, ni18n.name AS trademark_name,                           
                            IF (fit.trademark > 0, n.code, '') AS trademark_code,                          
                            CONCAT(ci18n2.name, ' ', ci18n2.lastname) as employee_name,                          
                            CONCAT(ui18n1.firstname, ' ', ui18n1.lastname) as added_by_name,                           
                            CONCAT(ui18n2.firstname, ' ', ui18n2.lastname) as modified_by_name ";
        if (!empty($filters['search_fcp_placeholder'])) {
            $sql['select'] .= ', IF(fit.observer = "[financial_contact_person]", ' . "\n" .
                              '  IF(co.self_financial = "[default_financial_contact_person]", vcstm.value, co.self_financial), ' . "\n" .
                              '  fit.observer) AS observer ' . "\n";
        }

        //from clause
        if (!isset($filters['getEmpty']) || isset($filters['getEmpty']) && !$filters['getEmpty']) {
            //normal search
            $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n";
        } else {
            //we need an empty model for transformation from documents
            $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS noneed' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                           '  ON fit.id IS NULL'. "\n";
        }
        $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                       '  ON (fit.id=fiti18n.parent_id AND fiti18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                       '  ON (fit.type=fdti18n.parent_id AND fdti18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                       '  ON (fit.company=fci18n.parent_id AND fci18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                       '  ON (fit.office=oi18n.parent_id AND oi18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N . ' AS fbai18n' . "\n" .
                       '  ON (fit.container_id=fbai18n.parent_id AND fit.payment_type=\'bank\' AND fbai18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_CASHBOXES_I18N . ' AS fcbi18n' . "\n" .
                       '  ON (fit.container_id=fcbi18n.parent_id AND fit.payment_type=\'cash\' AND fcbi18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to customer
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                       '  ON (fit.customer=c.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (fit.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                       //related trademarks
                        'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                        '  ON (fit.trademark=n.id)' . "\n" .
                        //related trademarks i18n
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       '  ON (fit.trademark=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to project
                       'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                       '  ON (fit.project=p.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                       '  ON (fit.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to employee
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n2' . "\n" .
                       '  ON (fit.employee=ci18n2.parent_id AND ci18n2.lang="' . $model_lang . '")' . "\n" .
                        //relate to users_i18n to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fit.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to users_i18n to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fit.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to GT2 details
                       'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                       '  ON fdd.model = "' . self::$modelName .  '" AND fdd.model_id = fit.id' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS fddi18n' . "\n" .
                       '  ON fdd.id = fddi18n.parent_id AND fddi18n.lang = "' . $model_lang . '"' . "\n" .
                       //relate to users to fetch invoice code of observer user
                       'LEFT JOIN ' . DB_TABLE_USERS . ' AS u0' . "\n" .
                       '  ON u0.id=fit.observer' . "\n";
        if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere'] ||
            isset($filters['get_info_id']) && $filters['get_info_id']) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                            '   ON fit.id = fiti.parent_id' . "\n";
        }

        if (!empty($filters['search_fcp_placeholder'])) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co ' . "\n" .
                            '  ON co.id=fit.contract_id' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_VARIABLES_META . ' AS vm ' . "\n" .
                            '  ON vm.model="Contract" AND vm.name="default_financial_contact_person"' . "\n" .
                            '  AND vm.id=(SELECT id FROM ' . DB_TABLE_VARIABLES_META . "\n" .
                            '      WHERE `model`="Contract" AND `name`="default_financial_contact_person" ' . "\n" .
                            '      AND (model_type=co.type OR model_type=0) ' . "\n" .
                            '      ORDER BY model_type DESC LIMIT 1)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_VARIABLES_CSTM . ' AS vcstm ' . "\n" .
                            '  ON vm.id=vcstm.var_id AND vcstm.model_id=\'0\' AND vm.model=vcstm.model';
        }

        if (is_array($ids) && count($ids)) {
            //ids are returned form getIds so search and sort by them
            if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere'] ||
                isset($filters['get_info_id']) && $filters['get_info_id']) {
                $sql['where'] = 'WHERE fiti.id in ('.@implode(',',$ids).')';
            } else {
                $sql['where'] = 'WHERE fit.id in ('.@implode(',',$ids).')';
            }
            $sql['order'] = 'ORDER BY find_in_set(order_idx, "'.@implode(',',$ids).'")';
            $sql['limit'] = '';
        } elseif ($registry->get('getOneRequested') && !empty($ids)) {
            //one model is searched(searchOne)
            $sql['where'] = $ids;
            $sql['order'] = '';
            $sql['limit'] = 'LIMIT 1';
        } else {
            //getIds returned empty result the search will not be performed
            if (!empty($filters['paginate'])) {
                return array(array(), 0);
            } else {
                return array();
            }
        }

        if (isset($filters['withouth_observer_answere']) && $filters['withouth_observer_answere'] ||
            isset($filters['get_info_id']) && $filters['get_info_id']) {
            $sql['group'] = 'GROUP BY fiti.id';
        } else {
            $sql['group'] = 'GROUP BY fit.id';
        }

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            $results = array($models, $filters['total']);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    public static function prepareRightsFilters(&$registry, &$filters) {
        $current_user_id = $registry['currentUser']->get('id');
        $rights = $registry['currentUser']->get('rights');
        $module = 'finance';
        $controller = 'invoices_templates';
        if ($registry['action'] == 'dashlet') {
            $action = 'search';
        } elseif (preg_match('#^(export|printlist|approve_send|approve|disapprove)$#', $registry['action'])) {
            if (!empty($filters['session_param']) && $filters['session_param'] == 'search_' . strtolower(self::$modelName)) {
                $action = 'search';
            } else {
                $action = 'list';
            }
        } else {
            $action = $registry['action'];
        }
        $current_right = $registry['module'] == $module && $registry['controller'] == $controller
        && isset($rights[$module . '_' . $controller][$action]) ?
        $rights[$module . '_' . $controller][$action] : '';

        // additional 'where' for hiding not allowed models
        if ($current_user_id && $current_right && $current_right != 'all') {
            if ($current_right == 'mine' || $current_right == 'group') {
                // applied only when action is list, search or dashlet
                if (!empty($filters['search_fcp_placeholder'])) {
                    $filters['where'][] = "fit.observer='$current_user_id' OR " . "\n" .
                            "(fit.observer='[financial_contact_person]' AND co.self_financial=$current_user_id OR " . "\n" .
                            "(co.self_financial = '[default_financial_contact_person]' AND vcstm.value='$current_user_id'))";
                }
            } elseif ($current_right == 'none' || $current_right == '') {
                $filters['where'][] = '0';
            }
        }
    }

    /**
     * Construct the where clause
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return string - the prepared WHERE clause
     */
    public static function constructWhere(&$registry, $filters = array()) {
        $where = array();
        $where[] = 'WHERE (';

        $current_user_id = $registry['currentUser']->get('id');
        $rights = $registry['currentUser']->get('rights');
        $module = 'finance';
        $controller = 'invoices_templates';
        if ($registry['action'] == 'dashlet') {
            $action = 'search';
        } elseif (preg_match('#^(export|printlist|approve_send|approve|disapprove)$#', $registry['action'])) {
            if (!empty($filters['session_param']) && $filters['session_param'] == 'search_' . strtolower(self::$modelName)) {
                $action = 'search';
            } else {
                $action = 'list';
            }
        } else {
            $action = $registry['action'];
        }
        $current_right = $registry['module'] == $module && $registry['controller'] == $controller
                             && isset($rights[$module . '_' . $controller][$action]) ?
                             $rights[$module . '_' . $controller][$action] : '';

        // additional 'where' for hiding not allowed models
        if ($current_user_id && $current_right && $current_right != 'all') {
            if ($current_right == 'mine' || $current_right == 'group') {
                // applied only when action is list, search or dashlet
                if (!empty($filters['search_fcp_placeholder'])) {
                    $where[] = "fit.observer='$current_user_id' OR " . "\n" .
                               "(fit.observer='[financial_contact_person]' AND co.self_financial=$current_user_id OR " . "\n" .
                               "(co.self_financial = '[default_financial_contact_person]' AND vcstm.value='$current_user_id'))) AND (";
                }
            } elseif ($current_right == 'none' || $current_right == '') {
                $where[] = ' 0) AND (';
            }
        }

        $query = 'SELECT name FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE model = "GT2_Sample" AND multilang = 1';
        $ml_vars = $registry['db']->GetCol($query);

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $key_where = array();
                $fields = array();
                if (is_array($filters['field'])) {
                    $fields = $filters['field'];
                    $filters['field'] = implode(', ', $filters['field']);
                } else {
                    $fields[] = $filters['field'];
                }
                foreach ($fields as $field) {
                    $key_where[] = General::buildClause($field, trim($filters['key']), true, 'like');
                }
            } else {
                //search in all fields
                $key_where = array();
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
            }
            $where[] = '(' . implode(" OR \n\t", $key_where) . ')';
            if (!empty($filters['hidden_type']) && $filters['hidden_type']) {
                $where[] = 'AND (fit.type = \'' . urldecode($filters['hidden_type']) . '\')';
            }
            if (!empty($filters['where'])) {
                $where[count($where)-1] .= ' AND ';
            }
        } elseif (isset($filters['where'])) {
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (preg_match('#^a(__)+.*#',$filter)) {
                    //additional var is requested
                    //so parse the filter definition
                    list($field, $operator, $value, $logical) = parent::parseFilter($filter);
                    $field_r = preg_replace('#^a(__)+#', '', $field);
                    if (in_array($field_r, $ml_vars)) {
                        $filter = preg_replace('#'. $field . '#', 'fddi18n.' . $field_r, $filter);
                    } else {
                        $filter = preg_replace('#'. $field . '#', 'fdd.' . $field_r, $filter);
                    }
                }
                $matches = null;
                if (preg_match('/^fit.observer\s*=\s*(\d+)\s*$/', $filter, $matches) && !empty($filters['search_fcp_placeholder'])) {
                    $user_id = $matches[1];
                    $filter .= ' OR (fit.observer = "[financial_contact_person]" AND (co.self_financial = ' . $user_id . ' OR ' .
                               '(co.self_financial = "[default_financial_contact_person]" AND vcstm.value = ' . $user_id . ')))';
                }

                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);

        if (!preg_match('#fit.deleted#', $where)) {
            $where .= ' AND fit.deleted_by = 0';
        }

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        if ($registry['request']->isRequested('type') && $registry['request']->get('type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'fit.type = \'' . urldecode($registry['request']->get('type')) . '\'';
        } elseif ($registry['request']->isRequested('hidden_type') && $registry['request']->get('hidden_type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'fit.type = \'' . urldecode($registry['request']->get('hidden_type')) . '\'';
        } elseif ($registry['request']->isRequested('type') && !$registry['request']->get('type')) {
            $filters['display'] = $registry['session']->get($sessionParam)['display'] ?? '';
            $registry['session']->remove($sessionParam);
        }

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);
        if ($sessionPrefix == 'list_' || $sessionPrefix == 'search_' || preg_match('#^dashlets_\d+_$#', $sessionPrefix)) {
            //removed fit.observer filter - get rights from role
            //$search['where'][] = 'fit.observer = ' . $registry['currentUser']->get('id');
            $search['withouth_observer_answere'] = true;
            $search['search_fcp_placeholder'] = true;
        }

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Builds a model object
     */
    public static function buildModelIndex(&$registry, $index) {
        $model = self::buildFromRequestIndex($registry, self::$modelName, $index);

        return $model;
    }

    /**
     * gets status of a document
     */
    public static function getModelStatus(&$registry, $id) {
        $db = $registry['db'];
        $query = 'SELECT status FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES .
                 ' WHERE id="' . $id . '"';
        $stuses = $db->GetAll($query);
        $current_status = $stuses[0]['status'];

        return $current_status;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_FINANCE_INVOICES_TEMPLATES);

        if (!$restored) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_FINANCE_INVOICES_TEMPLATES);

        if (!$deleted) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Gets default financial contact person for contract type or for all
     * contract types.
     * Make sure value matches an existing and active user in installation.
     *
     * @param Registry $registry - the main registry
     * @param string $contract_type - contract type to search for
     * @param string $get_inactive - get user even if inactive/deleted - false by default
     * @return string - id of user or an empty string if not set
     */
    public static function getDefaultFinancialContactPerson(Registry &$registry, $contract_type = '', $get_inactive = false) {
        $query = "
            SELECT vcstm.value
            FROM " . DB_TABLE_VARIABLES_META . " AS vm
            JOIN " . DB_TABLE_VARIABLES_CSTM . " AS vcstm
              ON vm.id = vcstm.var_id
                AND vm.model = vcstm.model
                AND vcstm.model_id = 0
            JOIN " . DB_TABLE_USERS . " AS u
              ON u.id = vcstm.value
            WHERE vm.model = 'Contract'
              AND vm.model_type IN (0, '{$contract_type}')
              AND vm.name = 'default_financial_contact_person'" . ($get_inactive ? '' : "
              AND u.active = 1
              AND u.deleted_by = 0") . "
            ORDER BY vm.model_type DESC
            LIMIT 0, 1";
        return $registry['db']->GetOne($query);
    }

    /**
     * Approve/disapprove issuing of invoices from templates for contracts
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of records from fin_invoices_templates_info table
     * @param string $action - approve or approve_send or disapprove or issue or issue_send
     * @param mixed $check_observer_response - if observer response value specified, check if the templates have already been approved
     * @return array - number of updated models, number of errors, number of already changed models
     */
    public static function approval(&$registry, $ids, $action, $check_observer_response = false) {
        $db = &$registry['db'];

        if (empty($ids)) {
            return false;
        }

        if ($action != 'disapprove') {
            //check for requested issue_date
            if ($registry['request']->get('issue_date')) {
                $issue_date = $registry['request']->get('issue_date');
                if ($issue_date < date('Y-m-d') && !$registry['currentUser']->checkRights('finance_invoices_templates','approve_issue_date')) {
                    //current user hasn't rights to set issue date in the past
                    $result = array('err_invalid_issue_date_before' => 1);
                    return $result;
                } elseif ($issue_date > date('Y-m-d') && !$registry['currentUser']->checkRights('finance_invoices_templates','approve_future_issue_date')) {
                    //current user hasn't rights to set issue date in the future
                    $result = array('err_invalid_issue_date_after' => 1);
                    return $result;
                }
            }

            // if invoices should be approved + issued
            if (preg_match('/^issue(_send)?$/', $action)) {

                if (!($registry['currentUser']->checkRights('finance_incomes_reasons' . PH_FINANCE_TYPE_INVOICE, 'add') ||
                $registry['currentUser']->checkRights('finance_incomes_reasons' . PH_FINANCE_TYPE_PRO_INVOICE, 'add'))) {
                    //current user has no rights to add invoices and proformas
                    $result = array('err_issue' => 1);
                    return $result;
                }

                if (empty($issue_date)) {
                    // if issue date is not specified, set today
                    $issue_date = date('Y-m-d');
                } elseif ($issue_date < date('Y-m-d') &&
                !($registry['currentUser']->checkRights('finance_incomes_reasons' . PH_FINANCE_TYPE_INVOICE, 'issue_date') ||
                $registry['currentUser']->checkRights('finance_incomes_reasons' . PH_FINANCE_TYPE_PRO_INVOICE, 'issue_date'))) {
                    //current user has no rights to set issue date of invoices and proformas in the past
                    $result = array('err_invalid_issue_date_before' => 1);
                    return $result;
                } elseif ($issue_date > date('Y-m-d') &&
                !($registry['currentUser']->checkRights('finance_incomes_reasons' . PH_FINANCE_TYPE_INVOICE, 'future_issue_date') ||
                $registry['currentUser']->checkRights('finance_incomes_reasons' . PH_FINANCE_TYPE_PRO_INVOICE, 'future_issue_date'))) {
                    //current user has no rights to set issue date of invoices and proformas in the future
                    $result = array('err_invalid_issue_date_after' => 1);
                    return $result;
                }
            }
        }

        //initialize results
        //each of these arrays is associative,
        //containing template id as index and element name and issue date of the template
        $msg_updated            = array();
        $err_updated            = array();
        $err_invalid_issue_date = array();
        $err_changed_already    = array();

        //check if the templates have been already approved
        //or if they are locked at the moment(another operation is executed)
        $query = 'SELECT fiti.id, fiti18n.name, fiti.issue_date ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                 '  ON fiti.parent_id = fit.id AND fit.issue_lock != "0000-00-00 00:00:00"' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                 '  ON fiti18n.parent_id = fit.id AND fiti18n.lang="' . $registry['lang'] . '"' . "\n" .
                 'WHERE fiti.id IN (' . implode(', ' , $ids) . ')' . "\n" .
                 ($check_observer_response ? ('  AND fiti.observer_response!="' . $check_observer_response . '"') : "");
        $err_changed_already = $db->GetAssoc($query);
        if (!empty($err_changed_already)) {
            //remove those ids that have been already approved
            $ids = array_diff($ids, array_keys($err_changed_already));
            if (empty($ids)) {
                //nothing left to approve, get out of here
                return array('err_changed_already' => $err_changed_already);
            }
        }

        if ($action != 'disapprove') {
            $set = array();
            //check for requested issue_date
            if (!empty($issue_date)) {
                $set['issue_date'] = sprintf("issue_date='%s'", $issue_date);
            }

            //get counters for invoices templates with assigned counter (counter > 0)
            //ToDo: check if some of the counters are deactivated or deleted
            $query = 'SELECT DISTINCT fiti.id, fit.counter, fiti.issue_date, fiti18n.name' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                     '  ON (fiti.parent_id = fit.id)' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                     '  ON (fiti18n.parent_id = fit.id AND fiti18n.lang="'.$registry['lang'].'")' . "\n" .
                     'WHERE fit.counter > 0 AND fiti.id IN (' . implode(', ' , $ids) . ')';
            $templates_counters = $db->GetAssoc($query);

            //get the default counters for each of the invoices templates with counter = 0
            //(this means that the default counter should be used)
            //first get the templates with counter 0
            $query = 'SELECT fiti.id, fit.proforma, fit.company, fit.office, fiti.issue_date, fiti18n.name ' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                     '  ON (fiti.parent_id = fit.id)' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                     '  ON (fiti18n.parent_id = fit.id AND fiti18n.lang="'.$registry['lang'].'")' . "\n" .
                     'WHERE fit.counter = 0 AND ' . "\n" .
                     '      fiti.id IN (' . implode(', ' , $ids) . ')';
            $templates_with_default_counter = $db->GetAssoc($query);

            if (!empty($templates_with_default_counter)) {
                //there are some templates with counter 0 (they are using the default counter)
                $default_counters = array();
                foreach ($templates_with_default_counter as $template_id => $inv_template) {
                    //the key of the default counters is office and company,
                    //because the counters depend on these two properties
                    $key = $inv_template['company'] . '|' . $inv_template['office'];

                    if (!isset($default_counters[$key])) {
                        //get the default counter for this company and office
                        $query = 'SELECT fc.id FROM ' . DB_TABLE_FINANCE_COUNTERS . ' AS fc' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_FINANCE_COUNTERS_OFFICES . ' AS fco' . "\n" .
                                 '  ON fco.parent_id = fc.id' . "\n" .
                                 'WHERE fc.model = "Finance_Incomes_Reason" AND' . "\n" .
                                 '      fc.model_type = "' . (($inv_template['proforma']) ? PH_FINANCE_TYPE_PRO_INVOICE : PH_FINANCE_TYPE_INVOICE) . '" AND' . "\n" .
                                 '      (fco.office_id = "' . $inv_template['office'] . '" OR fco.office_id=0) AND' . "\n" .
                                 '      fc.company = "' . $inv_template['company'] . '" AND' . "\n" .
                                 '      fc.active = 1 AND fc.deleted_by=0' . "\n" .
                                 'ORDER BY fco.office_id DESC, fc.`default` DESC' . "\n" .
                                 'LIMIT 0, 1' . "\n";
                        $default_counters[$key] = $db->GetOne($query);
                    }
                    //assign the counter to the template
                    $templates_counters[$template_id] = array(
                        'counter'    => $default_counters[$key],
                        'issue_date' => $inv_template['issue_date'],
                        'name'       => $inv_template['name'],
                    );
                }
            }

            if (!empty($templates_counters)) {
                $counters_ids = array();
                foreach ($templates_counters as $template_id => $template_data) {
                    if ($template_data['counter'] > 0) {
                        if (!in_array($template_data['counter'], $counters_ids)) {
                            $counters_ids[] = $template_data['counter'];
                        }
                    } else {
                        // No counter for this template. Remove id and template data and raise an error.
                        unset($templates_counters[$template_id]);
                        $ids = array_diff($ids, array($template_id));
                        $err_updated[$template_id] = $template_data;
                    }
                }

                if ($counters_ids) {
                    //get max issue dates for invoices with these counters
                    $query_max_ids = 'SELECT MAX(fir2.id)' . "\n" .
                                     'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir2' . "\n" .
                                     'JOIN ' . DB_TABLE_FINANCE_COUNTERS . ' fc ' . "\n" .
                                     ' ON  fc.id = fir2.counter AND fir2.counter IN (' . implode(',', $counters_ids) . ')' . "\n" .
                                     '     AND fc.formula LIKE "%[num]%" ' . "\n" .
                                     '     AND fir2.num LIKE CONCAT("%", LPAD(fc.next_number-1, fc.leading_zeroes, "0"), "%")' . "\n" .
                                     'GROUP BY fir2.counter';
                    $max_ids = $db->GetCol($query_max_ids);
                    $counters_issue_dates = array();
                    if ($max_ids) {
                        $query = 'SELECT fir1.counter, fir1.type, fir1.id, fir1.issue_date as last_issue_date' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir1' . "\n" .
                                 'WHERE fir1.id IN (' . implode(',', $max_ids) . ')';
                        $counters_issue_dates = $db->GetAssoc($query);
                    }

                    $err_invalid_issue_date = array();
                    foreach ($templates_counters as $template_id => $template_data) {
                        $counter_id = $template_data['counter'];
                        if (!empty($issue_date) && !empty($counters_issue_dates[$counter_id]['last_issue_date']) && $issue_date < $counters_issue_dates[$counter_id]['last_issue_date']) {
                            //issue date request to change
                            //the template is not valid because the requested issue date is before the last issue date for this counter
                            $err_invalid_issue_date[$template_id] = array(
                                'name'            => $template_data['name'],
                                'issue_date'      => $issue_date,
                                'last_issue_date' => $counters_issue_dates[$counter_id]['last_issue_date'],
                                'fin_document_id' => $counters_issue_dates[$counter_id]['id'],
                                'fin_document_type' => $counters_issue_dates[$counter_id]['type'],
                            );
                        } elseif (empty($issue_date) && $template_data['issue_date'] < $counters_issue_dates[$counter_id]['last_issue_date']) {
                            //issue date not to changed!
                            //the template is not valid because the issue date recorded in the DB is before the last issue date for this counter
                            $err_invalid_issue_date[$template_id] = array(
                                'name'            => $template_data['name'],
                                'issue_date'      => $template_data['issue_date'],
                                'last_issue_date' => $counters_issue_dates[$counter_id]['last_issue_date'],
                                'fin_document_id' => $counters_issue_dates[$counter_id]['id'],
                                'fin_document_type' => $counters_issue_dates[$counter_id]['type'],
                            );
                        }
                    }

                    //remove the erred templates from the list of requested ids
                    $ids = array_diff($ids, array_keys($err_invalid_issue_date));
                }
            }
        }

        if (empty($ids)) {
            $result = array('err_updated'            => $err_updated,
                            'err_invalid_issue_date' => $err_invalid_issue_date,
                            'err_changed_already'    => $err_changed_already);
            return $result;
        }

        $result = array('msg_updated'            => $msg_updated,
                        'err_updated'            => $err_updated,
                        'err_invalid_issue_date' => $err_invalid_issue_date,
                        'err_changed_already'    => $err_changed_already);

        //UPDATE THE INFO TABLE OF THE MODEL
        $val = array();
        switch ($action) {
            case 'disapprove':
                $val['cancel'] = $ids;
                break;
            case 'approve':
            case 'issue':
                /*$query = 'SELECT fiti.id FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . " AS fiti\n" .
                         'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . " AS fit\n" .
                         '  ON fiti.parent_id = fit.id' . "\n" .
                         'WHERE fit.auto_send > 0 AND fiti.id IN (' . implode(', ' , $ids) . ')';
                $auto_send = $db->GetCol($query);
                if (!empty($auto_send)) {
                    $val['issue_send'] = $auto_send;
                }
                $val['issue'] = array_diff($ids, $auto_send);*/
                $val['issue'] = $ids;
                break;
            case 'approve_send':
            case 'issue_send':
                $val['issue_send'] = $ids;
                break;
        }

        foreach ($val as $k => $v) {
            if (empty($v)) {
                continue;
            }
            $set['observer_response'] = sprintf("observer_response='%s'", $k);
            $set['response_id'] = sprintf("response_id=%d", $registry['currentUser']->get('id'));
            $set['response_date'] = "response_date=now()";

            //query to insert into the main table
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . "\n" .
                     'SET ' . implode(', ', $set) . "\n" .
                     'WHERE id IN (' . implode(', ', $v) . ')';

            $db->Execute($query);

            if ($db->ErrorMsg()) {
                if (preg_match('#^Duplicate entry#', $db->ErrorMsg()) && !empty($issue_date)) {
                    $query = 'SELECT fiti.id, fiti18n.name, "'.$issue_date.'" as issue_date, "'.$issue_date.'" as last_issue_date, ' . "\n" .
                             '  (SELECT MAX(fiti1.invoice_id) FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti1' . "\n" .
                             '   WHERE fiti1.invoice_id > 0 AND fiti1.parent_id = fiti.parent_id AND fiti1.issue_date = "' . $issue_date . '") AS fin_document_id, ' . "\n" .
                             PH_FINANCE_TYPE_INVOICE . ' AS fin_document_type' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                             'INNER JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                             '  ON (fiti.parent_id = fit.id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                             '  ON (fiti18n.parent_id = fit.id AND fiti18n.lang="'.$registry['lang'].'")' . "\n" .
                             'WHERE fiti.id IN (' . implode(', ' , $v) . ')';
                    $err_invalid_issue_date = array_merge($err_invalid_issue_date, $db->GetAssoc($query));

                } else {
                    $query = 'SELECT fiti.id, fiti18n.name, fiti.issue_date ' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                             'INNER JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                             '  ON (fiti.parent_id = fit.id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                             '  ON (fiti18n.parent_id = fit.id AND fiti18n.lang="'.$registry['lang'].'")' . "\n" .
                             'WHERE fiti.id IN (' . implode(', ' , $v) . ')';
                    $err_updated = array_merge($err_updated, $db->GetAssoc($query));
                }
            } else {
                $query = 'SELECT fiti.id AS idx, fiti18n.name, fiti.issue_date ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                         'INNER JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                         '  ON (fiti.parent_id = fit.id)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n' . "\n" .
                         '  ON (fiti18n.parent_id = fit.id AND fiti18n.lang="'.$registry['lang'].'")' . "\n" .
                         'WHERE fiti.id IN (' . implode(', ' , $v) . ')';
                // we need to preserve the keys (info ids)
                $msg_updated = $msg_updated + $db->GetAssoc($query);

            }
        }

        $result = array('msg_updated'            => $msg_updated,
                        'err_updated'            => $err_updated,
                        'err_invalid_issue_date' => $err_invalid_issue_date,
                        'err_changed_already'    => $err_changed_already);

        return $result;
    }

    /**
     * Gets info for invoice that will be issued from invoice template
     *
     * @param object $registry - the main registry
     * @param integer $id - id of record in info table of invoice templates
     * @return array - info for invoice that will be issued
     */
    public static function getTemplatesForIssue($registry, $id) {

        $db = &$registry['db'];
        $records = array();

        //get templates that have to be issued
        $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date, fiti.observer_response,' . "\n" .
                 '       fiti.from, fiti.to' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                 '  ON fit.id = fiti.parent_id AND fit.issue_lock = "0000-00-00 00:00:00"' . "\n" .
                 'WHERE (fiti.observer_response NOT IN("none", "cancel")' . "\n" .
                 '   OR fiti.observer_response = "cancel" AND fiti.`from` = "0000-00-00") AND fiti.invoice_id = 0 AND fit.deleted_by = 0 AND fiti.id = ' . $id;
        $records = $db->GetAssoc($query);

        if (!$records) {
            $id = $registry['request']->get($registry['action']);
            //this record is already added in the info table
            $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date, fiti.observer_response,' . "\n" .
                     '       fiti.from, fiti.to' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                     '  ON fit.id = fiti.parent_id AND fit.issue_lock = "0000-00-00 00:00:00"' . "\n" .
                     'WHERE (fiti.observer_response NOT IN("none", "cancel")' . "\n" .
                     '   OR fiti.observer_response = "cancel" AND fiti.`from` = "0000-00-00") AND fiti.invoice_id = 0 AND fit.deleted_by = 0 AND fiti.parent_id = ' . $id . "\n" .
                     'ORDER BY issue_date DESC' . "\n" .
                     'LIMIT 1';
            $records = $db->GetAssoc($query);
        }

        return $records;
    }

    /**
     * Function to issue invoices from templates
     *
     * @param Registry $registry - the main registry
     * @param array $records - array with invoices templates ids with index fetched from the fin_invoices_templates_info table
     * @param bool $preview - preview of issue invoice (true), or really issue the invoice (false)
     * @param bool $return $issue_results - if issue results should be prepared and returned
     * @return array - information for issued invoices and occurred errors
     */
    public static function issueInvoices(&$registry, $records, $preview = false, $return_issue_results = true) {

        $errors = array();
        $db = &$registry['db'];
        require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php');
        $registry->set('get_old_vars', true, true);

        $issued_invoices = array();

        //lock all the templates that will be issued
        $locked_templates = array();
        foreach ($records as $info_id => $data) {
            $locked_templates[] = $data['id'];
        }
        if (!empty($locked_templates) && !$preview) {
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET issue_lock = NOW()' . "\n" .
                     'WHERE id IN (' . implode(', ', $locked_templates) . ')';
            $db->Execute($query);
        }

        $records = array_chunk($records, self::$_invoices_chunk, true);
        foreach ($records as $chunk) {
            foreach ($chunk as $info_id => $data) {

                $db->StartTrans();

                $date_from = $date_to = '';

                $filters = array('where' => array('fit.id = ' . $data['id']));
                if ($registry->get('force_deleted')) {
                    $filters['where'][] = 'fit.deleted_by IS NOT NULL';
                }
                /** @var Finance_Invoices_Template $model */
                $model = self::searchOne($registry, $filters);
                if (!$model || get_class($model) != 'Finance_Invoices_Template') {
                    $db->FailTrans();
                    $db->CompleteTrans();
                    continue;
                }

                //get the contract
                require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                $filters = array('where' => array('co.id = ' . $model->get('contract_id')));
                /** @var Contract $contract */
                $contract = Contracts::searchOne($registry, $filters);
                if (!$contract) {
                    $db->FailTrans();
                    $db->CompleteTrans();
                    continue;
                }

                //if invoice template observer is placeholder "[financial_contact_person]",
                //replace it with id of self financial contact person of contract.
                if ($model->get('observer') == '[financial_contact_person]') {
                    $self_fin_data = $contract->getSelfFinancialData();
                    $model->set('observer', ($self_fin_data && !empty($self_fin_data['active']) ? $self_fin_data['id'] : ''), true);
                }

                //prepare some invoice properties
                if (in_array($model->get('type'), array(PH_FINANCE_TYPE_INVOICE_TEMPLATE_ADVANCE,
                                                         PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL,
                                                         PH_FINANCE_TYPE_INVOICE_TEMPLATE_NORMAL))) {
                    //if we are here we have NOT expenses reasons model
                    $invoice = new Finance_Incomes_Reason($registry, $model->getAll());
                    if ($model->get('proforma')) {
                        $invoice->set('type', PH_FINANCE_TYPE_PRO_INVOICE, true);
                    } else {
                        $invoice->set('type', PH_FINANCE_TYPE_INVOICE, true);
                    }
                    if ($model->get('type') == PH_FINANCE_TYPE_INVOICE_TEMPLATE_ADVANCE) {
                        $invoice->set('advance', 1, true);
                    }
                } else {
                    //expenses reasons will not be issued through this procedure
                    //unlock template
                    if (!$preview) {
                        $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET issue_lock = "0000-00-00 00:00:00"' . "\n" .
                                 'WHERE id = ' . $model->get('id');
                        $db->Execute($query);
                    }
                    $db->CompleteTrans();
                    continue;
                }

                $invoice->set('id', '', true);
                $invoice->set('link_to', $contract->get('id'), true);
                $invoice->set('link_to_model_name', 'Contract', true);
                $invoice->set('status', 'finished', true);
                $invoice->set('substatus', '', true);
                $invoice->set('custom_counter', $model->get('counter'), true);
                $invoice->set('auto_issue', $model->get('auto_issue'), true);
                $invoice->set('issue_date', $data['issue_date'], true);

                // when issuing from crontab, set user that approved issuing of
                // current invoice from template as 'custom_modified_by' and 'issue_by'
                if ($registry->get('crontab_issue_invoices')) {
                    $invoice->set('custom_modified_by', $data['response_id'], true);
                    $invoice->set('issue_by', $data['response_id'], true);
                    $invoice->set('invoice_code', $data['invoice_code'], true);
                } else {
                    // set current user as 'issue_by'
                    $invoice->set('issue_by', $registry['currentUser']->get('id'), true);
                    $invoice->set('invoice_code', $registry['currentUser']->get('invoice_code'), true);
                }
                // set 'distributed' property
                $invoice->set('distributed', PH_FINANCE_DISTRIBUTION_NO, true);
                // set group and department from type settings
                $invoice->setGroup($contract->get('group'));
                $invoice->setDepartment($contract->get('department'));

                $model->getGT2Vars();
                $gt2 = $model->get('grouping_table_2');

                $invoice->getGT2Vars();

                $gt2_new = $invoice->get('grouping_table_2');

                $invoice_info = array(
                    'invoice_id'    => '',
                    'total'         => $invoice->get('total'),
                    'total_with_vat'=> $invoice->get('total_with_vat'),
                    'issue_date'    => $invoice->get('issue_date'),
                    'issued_at'     => '',
                    'sent_at'       => '',
                    'send'          => ($model->get('auto_send') ? true : false),
                    'observer'      => $model->get('observer'),
                    'template_id'   => $model->get('id'),
                    'contract_id'   => $model->get('contract_id'),
                    'contract_num'  => $contract->get('num'),
                    'contract_name' => $contract->get('name'),
                );

                //validate observer
                //this can happen when taking observer from financial contact person of contract, which is not set or does not correspond to a valid user.
                if (!$model->get('observer') && (!empty($data['observer_response']) && $data['observer_response'] != 'cancel' || empty($data['observer_response']))) {
                    $db->FailTrans();
                    $db->CompleteTrans();

                    $registry['messages']->setError($registry['translater']->translate('error_finance_invoices_observer'));

                    self::saveIssueResults($registry, $invoice_info, $preview);
                    //unlock template
                    if (!$preview) {
                        $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET issue_lock = "0000-00-00 00:00:00"' . "\n" .
                                 'WHERE id = ' . $model->get('id');
                        $db->Execute($query);
                    }
                    continue;
                }
                //$model_old_status = $model->get('status');
                //check if the invoice is recurrent
                if ($model->get('recurrent')) {
                    if (!empty($data['from']) && $data['from'] > '0000-00-00' && !empty($data['observer_response'])) {
                        $date_from = $data['from'];
                        if ($model->get('periods_start') == $date_from) {
                            $model->set('status', 'opened', true);
                        } else {
                            $model->set('status', 'locked', true);
                        }
                        if (preg_match('#^issue#', $data['observer_response'])) {
                            //issue once disapproved invoice after next invoice
                            //we need to calculate the invoice issue date to be used in the messages
                            $issue_disapproved = true;
                        }
                    } else {
                        //get last date the invoice is issued to
                        $query = 'SELECT MAX(`to`) FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . "\n" .
                                 'WHERE parent_id = ' . $model->get('id') . ' AND advanced = "none"' . "\n" .
                                 'AND (invoice_id > 0 OR observer_response = "cancel" AND `from` > "0000-00-00")';
                        $date_from = $db->GetOne($query);

                        //prepare dates for the invoice's period
                        if (!$date_from || preg_match('#0000\-00\-00#', $date_from)) {
                            //the invoice is the first one for the period
                            $date_from = $model->get('periods_start');
                        } else {
                            $date_from = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($date_from)));
                        }
                    }

                    list($gt2_new['values'], $date_to) = $model->calculateRecurrence(array('date_from' => $date_from));

                    if ($gt2_new['values'] === false) {
                        //something is wrong with the recurrent invoice templates
                        $db->FailTrans();
                        $db->CompleteTrans();

                        $registry['messages']->setError($registry['translater']->translate('error_empty_gt2'));
                        self::saveIssueResults($registry, $invoice_info, $preview);
                        //unlock template
                        if (!$preview) {
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET issue_lock = "0000-00-00 00:00:00"' . "\n" .
                                     'WHERE id = ' . $model->get('id');
                            $db->Execute($query);
                        }
                        continue;
                    }
                } else {
                    $gt2_new['values'] = $gt2['values'];
                    $date_to = $date_from = $model->get('issue_date');
                    foreach ($gt2_new['values'] as $k => $v) {
                        $gt2_new['values'][$k]['row_index'] = 1;
                    }
                    $params = array(
                        'gt2_values' => $gt2_new['values'],
                        'calculated_price' => $gt2_new['calculated_price'],
                    );
                    $gt2_new['values'] = $model->processAdvancedInvoices($params);
                }

                // when issuing a previously cancelled invoice, calculate
                // issue_date according to reference date point and template settings
                if (!empty($issue_disapproved)) {
                    if ($model->get('issue_date_point') == 'start') {
                        $date_point = $date_from;
                    } else {
                        $date_point = $date_to;
                    }
                    if ($model->get('issue_date_period_type') == 'working') {
                        include_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                        $next_issue_date = Calendars_Calendar::calcDateOnWorkingDays($registry, $date_point,
                                                $model->get('issue_date_count'), $model->get('issue_date_direction'));
                    } else {
                        if ($model->get('issue_date_direction') == 'after') {
                            $sign = '+';
                        } else {
                            $sign = '-';
                        }
                        $next_issue_date = sprintf('%s %s %s', $sign,
                                         $model->get('issue_date_count'), 'day');
                        $next_issue_date = General::strftime('%Y-%m-%d', strtotime($next_issue_date, strtotime($date_point)));
                    }

                    $model->set('next_issue_date', $next_issue_date, true);
                }
                // observer response is empty when it is some preview/export/generate type of action
                if (!empty($data['observer_response']) && $data['observer_response'] != 'cancel' || empty($data['observer_response'])) {
                    //calculate indexes if present
                    $gt2_new['values'] = self::getIndexedValues($registry, $gt2_new['values']);
                    $params_gt2_info_data = array(
                        'model_name'   => $contract->modelName,
                        'model_type'   => $contract->get('type'),
                        'model_lang'   => $invoice->get('model_lang'));
                    self::getGT2InfoContractData($contract, $params_gt2_info_data);
                    self::createGT2InfoData($registry, $gt2_new['values'], $params_gt2_info_data);
                    $gt2_new['values'] = self::changeGT2RowsCurrency($registry, $gt2_new['values'], $gt2['calculated_price'], $model->get('currency'), $model->get('issue_currency'));
                    $invoice->set('currency', $model->get('issue_currency'), true);
                    $gt2_new['currency'] = $model->get('issue_currency');
                    //set GT2
                    $invoice->set('grouping_table_2', $gt2_new, true);
                    $invoice->calculateGT2();
                    if ($invoice->get('total_with_vat') == 0.00 && !$model->get('contain_advance')) {
                        $invoice->set('status', 'locked', true);
                        $invoice->set('annulled_by', PH_AUTOMATION_USER, true);
                        $gt2_new['values'] = $gt2_new['plain_values'] = array();
                        $invoice->set('grouping_table_2', $gt2_new, true);
                        $invoice_info['cancel'] = true;
                    }
                    $invoice->set('table_values_are_set', true, true);
                } else {
                    $invoice_info['cancel'] = true;
                }
                $invoice_info['total'] = $invoice->get('total');
                $invoice_info['total_with_vat'] = $invoice->get('total_with_vat');
                $invoice_info['currency'] = $invoice->get('currency');
                $invoice_info['issue_date'] = $invoice->get('issue_date');

                if ($preview) {
                    //return the invoice
                    //this is used for previews only

                    $invoice_info['issue_date'] = General::strftime('%d.%m.%Y', $invoice->get('issue_date'));

                    //set a fake number of the invoice
                    $invoice->set('num', '&lt;number&gt;', true);

                    //fix customer name, address, eik, vat_num
                    $query = 'SELECT IF(c.is_company=1, c.eik, c.ucn) AS eik, ' . "\n" .
                             '  c.in_dds AS vat_num, ' . "\n" .
                             '  IF(c.is_company=1, ci.company_name, CONCAT(ci.name, " ", ci.lastname)) AS name, ' . "\n" .
                             '  IF(c.is_company=1, ci.registration_address, ci.address_by_personal_id) AS address, ' . "\n" .
                             '  IF(c.is_company=1, ci.mol, CONCAT(ci.name, " ", ci.lastname)) AS received_by' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . ' AS c ' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci' . "\n" .
                             '  ON (c.id=ci.parent_id AND ci.lang="' . $invoice->get('model_lang') . '")' . "\n" .
                             'WHERE c.id=' .  $invoice->get('customer');
                    $customer_info = $registry['db']->GetRow($query);
                    if ($customer_info) {
                        $invoice->set('fiscal_customer_name', $customer_info['name'], true);
                        $invoice->set('fiscal_customer_address', $customer_info['address'], true);
                        $invoice->set('fiscal_customer_eik', $customer_info['eik'], true);
                        $invoice->set('fiscal_customer_vat_num', $customer_info['vat_num'], true);
                        $invoice->set('fiscal_customer_mol', $customer_info['received_by'], true);
                    }

                    //this data is used for preview of the invoice only
                    $invoice->set('added_by', $model->get('observer'), true);
                    $invoice->set('modified_by', $model->get('observer'), true);
                    $invoice->set('status_modified_by', $model->get('observer'), true);

                    //get the name of the observer and set it to the invoice
                    $query = 'SELECT CONCAT(firstname, " ", lastname) FROM ' . DB_TABLE_USERS_I18N . ' WHERE parent_id="' . $model->get('observer') . '" AND lang="' . $invoice->get('model_lang') . '"';
                    $observer_name = $registry['db']->GetOne($query);
                    if ($observer_name) {
                        $invoice->set('added_by_name', $observer_name, true);
                        $invoice->set('modified_by_name', $observer_name, true);
                        $invoice->set('status_modified_by_name', $observer_name, true);
                    }

                    if ($invoice->isDefined('date_of_payment_count')) {
                        if ($invoice->get('date_of_payment_count') !== '') {
                            if ($invoice->get('date_of_payment_point') == 'issue') {
                                $date_start = General::strftime('%Y-%m-%d');
                                if ($invoice->get('date_of_payment_period_type') == 'working') {
                                    require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                                    $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($invoice->registry, $date_start,
                                        $invoice->get('date_of_payment_count'), 'after');
                                } else {
                                    $date_of_payment = General::strftime('%Y-%m-%d',
                                        strtotime('+' . $invoice->get('date_of_payment_count') . ' day', strtotime($date_start)));
                                }
                             } else {
                                 $date_of_payment = array('count := ' . $invoice->get('date_of_payment_count'),
                                                          'period_type := ' . $invoice->get('date_of_payment_period_type'),
                                                          'period := day',
                                                          'direction := after',
                                                          'point := receive');
                                 $date_of_payment = implode('\n', $date_of_payment);
                             }
                            $invoice->set('date_of_payment', $date_of_payment, true);
                        } else {
                            $invoice->set('date_of_payment', General::strftime('%Y-%m-%d'), true);
                        }
                    }
                    if (!$invoice->get('issue_date')) {
                        $invoice->set('issue_date', General::strftime('%Y-%m-%d'), true);
                    }
                    $invoice->set('fiscal_event_date', $invoice->get('issue_date'), true);

                    //add the invoice object to the list of issued invoices
                    //no preparation of the errors and warnings needed - this is just a preview
                    $invoice->set('id', $info_id, true);
                    // !!!IMPORTANT !!! when we have preview it is for ONE AND ONLY ONE INVOICE
                    $issued_invoices[$model->get('observer')]['invoices'][] = $invoice->sanitize();
                } else {
                    if (!empty($data['observer_response']) && $data['observer_response'] != 'cancel' || empty($data['observer_response'])) {
                        //save the invoice
                        $invoice->set('periods_start', $date_from, true);
                        $invoice->set('periods_end', $date_to, true);

                        if (!$invoice->save()) {
                            //error saving the invoice
                            $registry['messages']->setError($registry['translater']->translate('finance_invoices_templates_error_saving_invoice'));
                            $db->FailTrans();
                            $db->CompleteTrans();

                            self::saveIssueResults($registry, $invoice_info, $preview);
                            //unlock template
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET issue_lock = "0000-00-00 00:00:00"' . "\n" .
                                     'WHERE id = ' . $model->get('id');
                            $db->Execute($query);
                            continue;
                        }

                        $invoice->set('fiscal_event_date', $invoice->get('issue_date'), true);
                        if ($invoice->get('status') == 'finished' && $invoice->get('id')) {
                            //translate invoice
                            self::getGT2InfoContractData($contract, $invoice);
                            $error = !$invoice->autoTranslate();
                        }
                    }

                    if (empty($error) && $invoice->get('id') || !empty($data['observer_response']) && $data['observer_response'] == 'cancel') {

                        if (empty($data['observer_response']) || $data['observer_response'] != 'cancel') {
                            //get already saved invoice as we will need customer's fiscal data
                            $filters = array('where' => array('fir.id = ' . $invoice->get('id'),
                                /*'fir.annulled_by = 0'*/));

                            require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php');
                            $invoice = Finance_Incomes_Reasons::searchOne($registry, $filters);
                            $registry->set('get_old_vars', true, true);
                            $invoice->getGT2Vars();
                        }

                        $invoice_info['invoice_id'] = $invoice->get('id');
                        $invoice_info['issued_at'] = date('Y-m-d H:i:s');

                        $set = array();
                        $set['invoice_id'] = 'invoice_id = ' . intval($invoice->get('id'));
                        $set['invoice_date'] = 'invoice_date = \'' . General::strftime('%Y-%m-%d', time()) . '\'';
                        if ($date_from && $date_to) {
                            $set['date_from'] = sprintf('`from` = \'%s\'', $date_from);
                            $set['date_to'] = sprintf('`to` = \'%s\'', $date_to);
                        }

                        //update templates info table with the invoice ID and dates
                        $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' SET' . "\n" .
                                 implode(",\n", $set) . "\n" .
                                 'WHERE id = ' . $info_id;

                        $db->Execute($query);

                        $model->updateAfterIssue(array('from' => $data['from'], 'date_to' => $date_to));

                        if (!empty($data['observer_response']) && $data['observer_response'] != 'cancel' || empty($data['observer_response'])) {
                            //check if we have to send this invoice by mail to the customer
                            if ($model->get('auto_send') && $invoice->get('status') == 'finished' && $data['observer_response'] == 'issue_send') {
                                // mark invoices with "to be sent" flag
                                $invoice_info['send'] = true;

                                if ($registry['issue_one_invoice_only']) {
                                    // only one invoice will be issued - generate and send this invoice
                                    $invoice->set('contract', $contract->get('id'), true);
                                    $mails = $invoice->sendToCustomer($model->get('auto_send'), $model->get('pattern'));

                                    if (!empty($mails['erred'])) {
                                        $registry['messages']->setError($registry['translater']->translate('error_sending_mail'));
                                    } elseif (!empty($mails['sent'])) {
                                        $invoice_info['sent_at'] = date('Y-m-d H:i:s');
                                    } else {
                                        $registry['messages']->setError($registry['translater']->translate('warning_no_financial_contact'));
                                    }
                                }
                            } else {
                                // mark that invoice will not be sent
                                $invoice_info['send'] = false;
                            }

                            $action_type = $invoice->get('type') == PH_FINANCE_TYPE_INVOICE ? 'addinvoice' : 'addproformainvoice';

                            //write history for contract and for issued incomes/expenses reason
                            $old_model = new Finance_Incomes_Reason($registry);
                            $old_model->set('type', $invoice->get('type'), true);
                            $registry->set('get_old_vars', true, true);
                            $old_model->getGT2Vars();
                            $old_model->sanitize();

                            $invoice_id = $invoice->get('id');
                            $invoice_status = $invoice->get('status');
                            $invoice->set('id', $contract->get('id'), true);
                            $invoice->set('status', $contract->get('status'), true);

                            require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
                            $audit_parent = Contracts_History::saveData($registry, array('action_type' => $action_type,
                                                                                         'model' => $invoice,
                                                                                         'new_model' => $invoice,
                                                                                         'old_model' => $contract
                                                                                   ));

                            $invoice->set('id', $invoice_id, true);
                            $invoice->set('status', $invoice_status, true);

                            require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                            $audit_parent = Finance_Incomes_Reasons_History::saveData($registry,
                                                                                      array('action_type' => 'add',
                                                                                            'new_model' => $invoice,
                                                                                            'old_model' => $old_model
                                                                                      ));

                            //auto-distribute invoice (proforma invoices are not distributed!!!)
                            if ($invoice->get('status') == 'finished' && $invoice->get('type') != PH_FINANCE_TYPE_PRO_INVOICE && $invoice->saveDistributionAuto()) {
                                $invoice->set('distributed', PH_FINANCE_DISTRIBUTION_YES, true);
                                $invoice->updateDistributed();

                                //write history
                                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                                $audit_parent = Finance_Incomes_Reasons_History::saveData($registry,
                                                                                          array('action_type' => 'distribute',
                                                                                                'new_model' => $invoice,
                                                                                                'old_model' => $old_model
                                                                                          ));
                                $invoice_info['distributed'] = 1;
                            }

                            unset($old_model);
                            unset($invoice);
                        }
                    } else {
                        $invoice_info['parent_id'] = $invoice->get('id');
                    }

                    if (empty($invoice_info['cancel'])) {
                        //prepare the error messages to be sent to the supervisor (observer)
                        self::saveIssueResults($registry, $invoice_info, $preview);
                    }
                    //unlock templates one by one after issue of each template
                    $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET issue_lock = "0000-00-00 00:00:00"' . "\n" .
                             'WHERE id = ' . $model->get('id');
                    $db->Execute($query);
                }
                $db->CompleteTrans();
            }
        }

        // prepare results if really issuing or if there are errors in preview mode
        // !!!IMPORTANT !!! when we have preview it is for ONE AND ONLY ONE INVOICE
        if ($return_issue_results && (!$preview || empty($issued_invoices))) {
            $issued_invoices = self::prepareIssueResults($registry, $preview, !$registry['crontab_issue_invoices_skip_grouping']);
        }

        return $issued_invoices;
    }

    /**
     * Saves results of invoice creation (or its failure):
     * - in session, if preview mode or when user is manually issuing a single invoice
     * - in database, if issuing invoices in mass-mode
     *
     * @param Registry $registry - the main registry
     * @param array $params - data to save
     * @param bool $preview - flag whether invoice creation is simulated in preview mode (true) or it is real (false)
     * @return bool - result of the operation
     */
    public static function saveIssueResults(&$registry, $params, $preview) {

        //get issue errors and warnings for the invoice
        $params['errors'] = $registry['messages']->getErrors();
        $params['warnings'] = $registry['messages']->getWarnings();
        $registry['messages']->flush();

        if ($preview || $registry->get('issue_one_invoice_only')) {
            $issue_results = $registry['session']->get('current_issue_results');
            if (empty($issue_results)) {
                $issue_results = array();
            }

            $params['origin'] = 'session';
            $issue_results[] = $params;
            $registry['session']->set('current_issue_results', $issue_results, '', true);
            return true;
        }

        $errors = "";
        if (!empty($params['errors'])) {
            $errors = base64_encode(serialize($params['errors']));
        }
        $warnings = "";
        if (!empty($params['warnings'])) {
            $warnings = base64_encode(serialize($params['warnings']));
        }


        //save data
        $query = 'INSERT INTO ' . DB_TABLE_CRONTAB_ISSUE_RESULTS . ' SET' . "\n" .
                 'observer = "' . $params['observer'] . '",' . "\n" .
                 'template_id = "' . $params['template_id'] . '",' . "\n" .
                 'invoice_id = "' . $params['invoice_id'] . '",' . "\n" .
                 'total = "' . $params['total'] . '",' . "\n" .
                 'total_with_vat = "' . $params['total_with_vat'] . '",' . "\n" .
                 'issue_date = "' . $params['issue_date'] . '",' . "\n" .
                 'issued_at = "' . $params['issued_at'] . '",' . "\n" .
                 'send = "' . $params['send'] . '",' . "\n" .
                 'sent_at = "' . $params['sent_at'] . '",' . "\n" .
                 (!empty($params['distributed']) ? 'distributed = ' . $params['distributed'] . ',' : 'distributed = 0,') . "\n" .
                 (!empty($params['cancel']) ? 'cancel = 1,' : 'cancel = 0,') . "\n" .
                 'errors = "' . $errors . '",' . "\n" .
                 'warnings = "' . $warnings . '"';
        $registry['db']->Execute($query);

        return !$registry['db']->HasFailedTrans();
    }

    /**
     * Prepare results from issue invoices procedure.
     *
     * @param Registry $registry - the main registry
     * @param bool $preview - true when in preview mode, false when issuing invoices
     * @param bool $group_by_observer - false when results are taken for notifying customers, true when results are taken form notifying observers
     * @return array - data for issued_invoices
     */
    public static function prepareIssueResults(&$registry, $preview, $group_by_observer = true) {

        if ($preview || $registry->get('issue_one_invoice_only')) {
            // in preview mode and manual single invoice issue mode
            // results are stored in session - get them from there
            $results = $registry['session']->get('current_issue_results');
            $registry['session']->remove('current_issue_results');

            if (!$preview) {
                // prepare some more data for notification
                foreach ($results as $k => $inv) {
                    // format dates
                    if (!empty($results[$k]['issue_date'])) {
                        $results[$k]['issue_date'] = date_create($results[$k]['issue_date'])->format('d.m.Y');
                    }
                    if (!empty($results[$k]['issued_at'])) {
                        $results[$k]['issued_at'] = date_create($results[$k]['issued_at'])->format('d.m.Y H:i:s');
                    }
                    if (!empty($results[$k]['sent_at'])) {
                        $results[$k]['sent_at'] = date_create($results[$k]['sent_at'])->format('d.m.Y H:i:s');
                    }
                    if (!empty($inv['invoice_id'])) {
                        // get data from model
                        $model = Finance_Incomes_Reasons::searchOne(
                            $registry,
                            array(
                                'where' => array('fir.id = \'' . $inv['invoice_id'] . '\''),
                                'sanitize' => true
                            )
                        );
                        if ($model) {
                            $results[$k] = $results[$k] + array(
                                'distributed' => $model->get('distributed'),
                                'customer' => $model->get('customer'),
                                'customer_name' => $model->get('customer_name'),
                                'issue_currency' => $model->get('currency'),
                                'name' => $model->get('name'),
                                'model_name' => $model->modelName,
                                'type' => $model->get('type'),
                                'type_name' => $model->get('type_name'),
                                'company' => $model->get('company'),
                                'company_name' => $model->get('company_name'),
                            );
                        }
                        unset($model);
                    }
                }
            }
        } else {
            // the multi-step issuing process logic is that:
            // the non-grouped issue results are needed for file generation and for sending to customers;
            // the grouped issue results are needed for sending to observers after the previous has been done
            if ($group_by_observer) {
                // get results to send to observers
                $where = "ir.processed = '0000-00-00 00:00:00'";
            } else {
                // get results to send to customers
                $where = "ir.sent_at = '0000-00-00 00:00:00' AND ir.invoice_id > 0 AND ir.send = 1 AND ir.errors = '' AND ir.warnings = ''";
            }
            $query = 'SELECT ir.id as idx, ir.observer, ir.template_id, ir.invoice_id, ir.total, ir.total_with_vat,' . "\n" .
                     '  DATE_FORMAT(TIMESTAMP(ir.issue_date), "%d.%m.%Y") as issue_date, ir.send, ir.cancel,' . "\n" .
                     '  DATE_FORMAT(TIMESTAMP(ir.issued_at), "%d.%m.%Y %H:%i:%s") as issued_at, ir.distributed,' . "\n" .
                     '  DATE_FORMAT(TIMESTAMP(ir.sent_at), "%d.%m.%Y %H:%i:%s") as sent_at, ir.errors, ir.warnings,' . "\n" .
                     '  fit.customer, fit.contract_id, fit.company, fit.issue_currency,' . "\n" .
                     '  fiti.name, "Finance_Incomes_Reason" as model_name, TRIM(CONCAT(ci.name, " ", ci.lastname)) as customer_name,' . "\n" .
                     '  IF (fit.proforma, ' . PH_FINANCE_TYPE_PRO_INVOICE . ', ' . PH_FINANCE_TYPE_INVOICE . ') as `type`,' . "\n" .
                     '  fci.name as company_name, co.num as contract_num, coi.name as contract_name' . "\n" .
                     'FROM ' . DB_TABLE_CRONTAB_ISSUE_RESULTS . ' ir' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' fit' . "\n" .
                     '  ON ir.template_id = fit.id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' fiti' . "\n" .
                     '  ON fiti.parent_id = fit.id AND fiti.lang = "' . $registry['lang'] . '"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' ci' . "\n" .
                     '  ON ci.parent_id = fit.customer AND ci.lang = "' . $registry['lang'] . '"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' fci' . "\n" .
                     '  ON fci.parent_id = fit.company AND fci.lang = "' . $registry['lang'] . '"' . "\n" .
                     'JOIN ' . DB_TABLE_CONTRACTS . ' co' . "\n" .
                     '  ON fit.contract_id = co.id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' coi' . "\n" .
                     '  ON coi.parent_id = co.id AND coi.lang = "' . $registry['lang'] . '"' . "\n" .
                     'WHERE ' . $where;
            $results = $registry['db']->GetAssoc($query);
        }

        $query = 'SELECT parent_id, name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                 'WHERE parent_id IN (' . PH_FINANCE_TYPE_PRO_INVOICE . ', ' . PH_FINANCE_TYPE_INVOICE . ')' . "\n" .
                 '  AND lang = "' . $registry['lang'] . '"';
        $types = $registry['db']->GetAssoc($query);

        $issued_invoices = array();

        foreach ($results as $idx => $r) {
            if (!empty($r['type'])) {
                $r['type_name'] = $types[$r['type']];
            }
            if (empty($r['origin']) || $r['origin'] != 'session') {
                if (!empty($r['errors'])) {
                    $r['errors'] = unserialize(base64_decode($r['errors']));
                }
                if (!empty($r['warnings'])) {
                    $r['warnings'] = unserialize(base64_decode($r['warnings']));
                }
            }

            if ($group_by_observer) {
                if (!isset($issued_invoices[$r['observer']])) {
                    $issued_invoices[$r['observer']] = array(
                        'invoices' => array(),
                        'errors'   => array(),
                        'warnings' => array(),
                        'processed' => array(),
                    );
                }
                // if $idx is a real id from results table
                if (!($preview || $registry->get('issue_one_invoice_only'))) {
                    $issued_invoices[$r['observer']]['processed'][] = $idx;
                }
                //get files and emails
                if (!empty($r['invoice_id']) && !empty($r['send']) && !empty($r['sent_at'])) {
                    $query = 'SELECT es.recipient, es.recipient_name, f.id as file_id, f.filename' . "\n" .
                             'FROM ' . DB_TABLE_EMAILS_SENTBOX . ' es' . "\n" .
                             //for now we have only one attachment so JOIN files table directly
                             'LEFT JOIN ' . DB_TABLE_FILES . ' f' . "\n" .
                             '  ON f.id = es.attachments' . "\n" .
                             'WHERE es.model = "' . $r['model_name'] . '" AND es.model_id = ' . $r['invoice_id'];
                    $data = $registry['db']->GetAll($query);
                    $r['recipients_info'] = array();
                    foreach ($data as $d) {
                        if (!empty($d['file_id'])) {
                            $r['sent_files'][$d['file_id']] = $d['filename'];
                        }
                        unset($d['file_id']);
                        unset($d['filename']);
                        $r['recipients_info'][] = $d;
                    }
                }

                $c_link = sprintf('http://%s%s?%s=%s&amp;%s=%s&amp;%s=%s',
                        $_SERVER['HTTP_HOST'], $_SERVER['PHP_SELF'], $registry['module_param'],
                        'contracts', 'contracts', 'view', 'view', $r['contract_id']);
                $c_link = '<a href="' . $c_link . '"><strong>%s</strong></a>';

                $t_link = sprintf('http://%s%s?%s=%s&amp;%s=%s&amp;%s=%s#%s',
                        $_SERVER['HTTP_HOST'], $_SERVER['PHP_SELF'], $registry['module_param'],
                        'contracts', 'contracts', 'viewfinance', 'viewfinance',
                        $r['contract_id'], $r['template_id']);
                $t_link = '<a href="' . $t_link . '"><strong>%s</strong></a>';

                if (!empty($r['warnings'])) {
                    if (!isset($issued_invoices[$r['observer']]['warnings'][$r['contract_id']])) {
                        $issued_invoices[$r['observer']]['warnings'][$r['contract_id']] = array(
                            sprintf($registry['translater']->translate('warnings_invoices_for_contract'),
                                    sprintf($c_link, $r['contract_num'] ? $r['contract_num'] : $r['conrtact_name'])));
                    }
                    $issued_invoices[$r['observer']]['warnings'][$r['contract_id']][] =
                    sprintf($registry['translater']->translate('warning_invoices_for_date'),
                            sprintf($t_link, $r['issue_date'])) . '<br />' . implode('<br />',  $r['warnings']);
                }

                if (!empty($r['errors'])) {
                    if (!isset($issued_invoices[$r['observer']]['errors'][$r['contract_id']])) {
                        $issued_invoices[$r['observer']]['errors'][$r['contract_id']] = array(
                                sprintf($registry['translater']->translate('error_invoices_for_contract'),
                                sprintf($c_link, $r['contract_num'] ? $r['contract_num'] : $r['conrtact_name'])));
                    }
                    $issued_invoices[$r['observer']]['errors'][$r['contract_id']][] =
                        sprintf($registry['translater']->translate('error_invoices_for_date'),
                            sprintf($t_link, $r['issue_date'])) . '<br />' . implode('<br />',  $r['errors']);
                    $r['issued_at'] = '';
                    $r['issue_date'] = '';
                }

                $issued_invoices[$r['observer']]['invoices'][] = $r;
            } else {
                $issued_invoices[$idx] = $r;
            }
        }

        return $issued_invoices;
    }

    /**
     * Send notification to observers or current user after invoices have been issued
     * (from crontab, manually or when agreement enters into force or is annulled)
     *
     * @param Registry $registry - the main registry
     * @param array $issue_results - results, grouped per user to be notified
     * @param array $errors - collect errors from issuing invoices and sending mails
     * @param array $erred_templates - collect ids of erred templates
     */
    public static function sendNotification(&$registry, $issue_results, &$errors = array(), &$erred_templates = array()) {

        if (!$issue_results) {
            return;
        }

        $lang = $registry['lang'];
        $db = &$registry['db'];
        // base host for URLs in e-mails
        $base_host = $registry['config']->getParam('crontab', 'base_host');
        // interface language for multiprint URLs
        $send_lang = $registry['config']->getParam('crontab', 'invoices_send_lang') ?: $registry['lang'];

        // notification template
        $template = 'invoices_issue';
        $not_users = Users::getUsersNoSend($registry, $template);

        // supervisor user to notify for all that will not reach another user
        $dfcp = Finance_Invoices_Templates::getDefaultFinancialContactPerson($registry) ?: PH_USERS_ADMIN;
        // always notify supervisor user
        $not_users = array_diff($not_users, array($dfcp));

        // get user data
        $user_ids = array_keys($issue_results);
        $user_ids = array_merge($user_ids, array($dfcp));

        $user_ids = array_diff($user_ids, $not_users);
        $user_info = array();
        if ($user_ids) {
            $query = 'SELECT u.id AS idx, u.email,' . "\n" .
                     'CONCAT(ui18n.firstname, " ", ui18n.lastname) AS full_name' . "\n" .
                     'FROM ' . DB_TABLE_USERS . ' AS u ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                     '  ON (u.id=ui18n.parent_id AND ui18n.lang="' . $lang . '")' . "\n" .
                     'WHERE u.id IN (' . implode(',', $user_ids) . ') AND u.active=1 AND u.deleted_by=0';
            $user_info = $db->GetAssoc($query);
        }

        // prepare common URLs
        $invoices_base_url      = sprintf('%s/index.php?%s=contracts&contracts=',
                                          $base_host, $registry['module_param']);
        $customers_base_url     = sprintf('%s/index.php?%s=customers&customers=view&view=',
                                          $base_host, $registry['module_param']);
        //url-s for distribution for incomes and expenses
        $d_incomes_base_url     = sprintf('%s/index.php?%s=finance&%s=incomes_reasons&incomes_reasons=distribute&distribute=',
                                          $base_host, $registry['module_param'], $registry['controller_param']);
        $d_expenses_base_url    = sprintf('%s/index.php?%s=finance&%s=expenses_reasons&expenses_reasons=distribute&distribute=',
                                          $base_host, $registry['module_param'], $registry['controller_param']);
        $incomes_base_url       = sprintf('%s/index.php?%s=finance&%s=incomes_reasons&',
                                          $base_host, $registry['module_param'], $registry['controller_param']);
        $themes_base_url        = sprintf('%s/_libs/themes/', $base_host);
        //url-s for multiprint for incomes and expenses
        $mp_incomes_base_url    = sprintf('%s/index.php?%s=finance&%s=incomes_reasons&incomes_reasons=multiprint&lang=%s',
                                          $base_host, $registry['module_param'], $registry['controller_param'], $send_lang);
        $mp_expenses_base_url   = sprintf('%s/index.php?%s=finance&%s=expenses_reasons&expenses_reasons=multiprint&lang=%s',
                                          $base_host, $registry['module_param'], $registry['controller_param'], $send_lang);

        $mailer = new Mailer($registry, $template);

        foreach ($issue_results as $user_id => $issue_result_info) {

            // collect error messages if such have occurred
            if (!empty($issue_result_info['errors'])) {
                $occurred_errors = array();
                foreach ($issue_result_info['errors'] as $contract => $contract_errors) {
                    $occurred_errors[$contract] = implode("\n", $contract_errors);
                }
                $user_errors = ' ' . implode("\n", $occurred_errors);
                $user_errors = preg_replace('#\<br \/\>#', '', $user_errors);
                $errors[] = 'Errors occurred while issuing invoices for observer user with ID ' . $user_id . ':' . "\n" . $user_errors;

                //get erred templates ids
                foreach ($issue_result_info['invoices'] as $k => $v) {
                    $erred_templates[] = $v['template_id'];
                }
            }

            if (empty($issue_result_info['invoices'])) {
                //do not send empty emails
                continue;
            }

            // template observer is current user (but not when it is background_send / crontab action)
            if ($user_id == $registry['currentUser']->get('id') && $registry['action'] != 'background_send' && $registry['module'] != 'crontab') {
                continue;
            }

            // in case observer user is invalid/inactive/not receiving notifications, notify dfcp
            if (in_array($user_id, $not_users) || !array_key_exists($user_id, $user_info)) {
                $errors[] = "Email for issued invoices not sent to observer user with ID: $user_id, sending to user with ID: $dfcp instead";
                $user_id = $dfcp;
            }
            $user = isset($user_info[$user_id]) ? $user_info[$user_id] : array();
            if (!empty($user['email'])) {
                $mailer->placeholder->add('to_email', $user['email']);
                $mailer->placeholder->add('user_name', $user['full_name']);

                // prepare the table with issued invoices
                $issuedInvoicesViewer = new Viewer($registry);
                $issuedInvoicesViewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_issued_invoices_email.html');

                $issuedInvoicesViewer->data['invoices_base_url'] = $invoices_base_url;
                $issuedInvoicesViewer->data['customers_base_url'] = $customers_base_url;
                $issuedInvoicesViewer->data['d_incomes_base_url'] = $d_incomes_base_url;
                $issuedInvoicesViewer->data['d_expenses_base_url'] = $d_expenses_base_url;
                $issuedInvoicesViewer->data['incomes_base_url'] = $incomes_base_url;
                $issuedInvoicesViewer->data['themes_base_url'] = $themes_base_url;

                $issuedInvoicesViewer->data['issued_invoices'] = $issue_result_info['invoices'];
                $issuedInvoicesViewer->loadCustomI18NFiles(PH_MODULES_DIR . 'crontab/i18n/' . $lang . '/crontab.ini');
                $invoices = $issuedInvoicesViewer->fetch();

                $invoices = preg_replace('/(\n|\r)/', '', $invoices);
                $mailer->placeholder->add('finance_issued_invoices', $invoices);

                // prepare links for multiprint
                $multiprintLinksViewer = new Viewer($registry);
                $multiprintLinksViewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_issued_invoices_multiprint_links.html');
                $multiprintLinksViewer->loadCustomI18NFiles(PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance.ini');

                $multiprintLinksViewer->data['incomes_base_url'] = $mp_incomes_base_url;
                $multiprintLinksViewer->data['expenses_base_url'] = $mp_expenses_base_url;

                $issued_records = array();
                $model_types = array();
                $companies = array('0');
                foreach ($issue_result_info['invoices'] as $invoice_info) {
                    // get all ids of successfully issued invoices (or expenses reasons),
                    // grouped by type and company of financial document
                    if ($invoice_info['issued_at']) {
                        // filter patterns by model type and company
                        if (!in_array($invoice_info['type'], $model_types)) {
                            $model_types[] = $invoice_info['type'];
                        }
                        if (!in_array($invoice_info['company'], $companies)) {
                            $companies[] = $invoice_info['company'];
                        }
                        $ukey = $invoice_info['company'] . '^' . $invoice_info['type'];
                        if (!isset($issued_records[$ukey])) {
                            $issued_records[$ukey] = array(
                                'ids'           => array(),
                                'patterns'      => array(),
                                'model_name'    => $invoice_info['model_name'],
                                'company'       => $invoice_info['company'],
                                'company_name'  => $invoice_info['company_name'],
                                'type'          => $invoice_info['type'],
                            );

                            //get type name
                            if ($registry['action'] == 'background_send' ||
                            !in_array($invoice_info['type'], array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE))) {
                                $issued_records[$ukey]['type_name'] = $invoice_info['type_name'];
                            } else {
                                $issued_records[$ukey]['type_name'] =
                                    $invoice_info['type'] == PH_FINANCE_TYPE_INVOICE ?
                                    $registry['translater']->translate('finance_invoice') :
                                    $registry['translater']->translate('finance_proformainvoice');
                                if (!$issued_records[$ukey]['type_name']) {
                                    $issued_records[$ukey]['type_name'] = $invoice_info['type_name'];
                                }
                            }
                        }
                        $issued_records[$ukey]['ids'][] = $invoice_info['invoice_id'];
                    }
                }

                if ($issued_records) {
                    $query = 'SELECT p.id, CONCAT(p.company, \'^\', p.model_type) AS ukey, pi18n.name' . "\n" .
                             'FROM ' . DB_TABLE_PATTERNS . ' AS p' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PATTERNS_I18N . ' AS pi18n' . "\n" .
                             '  ON p.id=pi18n.parent_id AND pi18n.lang="' . $lang . '"' . "\n" .
                             'WHERE p.model IN ("Finance_Incomes_Reason", "Finance_Expenses_Reason") ' . "\n" .
                             '  AND p.model_type IN (\'' . implode('\', \'', $model_types) . '\') ' . "\n" .
                             '  AND p.company IN (\'' . implode('\', \'', $companies) . '\')' . "\n" .
                             '  AND p.format="pdf" AND p.active=1 AND p.list=0 AND p.for_printform=0 AND p.deleted_by=0' . "\n" .
                             'ORDER BY p.position ASC';
                    $patterns = $db->GetAll($query);

                    //if no patterns whatsoever, nothing can be printed
                    if (!$patterns) {
                        $issued_records = array();
                    } else {
                        //get all pattern ids for each type
                        foreach ($patterns as $pattern) {
                            if (strpos($pattern['ukey'], '0^') === 0) {
                                // pattern for all companies - get all issued records from this type
                                $regex = '/' . preg_quote(substr($pattern['ukey'], 1)) . '$/';
                                foreach ($issued_records as $ir_ukey => $record_info) {
                                    if (preg_match($regex, $ir_ukey)) {
                                        if (!isset($issued_records[$pattern['ukey']])) {
                                            $issued_records[$pattern['ukey']] = $record_info;
                                            // no company
                                            $issued_records[$pattern['ukey']]['company'] = '';
                                            $issued_records[$pattern['ukey']]['company_name'] = '';
                                            // reset pattern array
                                            $issued_records[$pattern['ukey']]['patterns'] = array();
                                        } else {
                                            // collect all ids
                                            $issued_records[$pattern['ukey']]['ids'] =
                                                array_merge($issued_records[$pattern['ukey']]['ids'], $record_info['ids']);
                                        }
                                        // pattern for all records from this type
                                        $issued_records[$pattern['ukey']]['patterns'][$pattern['id']] = $pattern['name'];
                                    }
                                }
                            } elseif (isset($issued_records[$pattern['ukey']])) {
                                // pattern for specific company
                                $issued_records[$pattern['ukey']]['patterns'][$pattern['id']] = $pattern['name'];
                            }
                        }
                        ksort($issued_records);
                    }
                }

                //instead of comma-separated list of all ids pass filters (from_id, to_id)
                // pass type to get only issued records of this type
                // pass company when printing with company-specific pattern
                foreach ($issued_records as $ukey => $type_info) {
                    sort($type_info['ids']);
                    $multiprint_filters = array(
                        'from_id'   => $type_info['ids'][0],
                        'to_id'     => $type_info['ids'][count($type_info['ids'])-1],
                        'type'      => $type_info['type']
                    );
                    if ($type_info['company']) {
                        $multiprint_filters['company'] = $type_info['company'];
                    }
                    $multiprint_filters = http_build_query($multiprint_filters);
                    $issued_records[$ukey]['ids_enc'] = rawurlencode(General::encrypt($multiprint_filters, '', 'gzip'));
                }

                $multiprintLinksViewer->data['issued_records'] = $issued_records;

                $multiprintLinks = $multiprintLinksViewer->fetch();
                $multiprintLinks = preg_replace('/(\n|\r)/', '', $multiprintLinks);
                $mailer->placeholder->add('finance_issued_invoices_multiprint_links', $multiprintLinks);
                //send email
                $result = $mailer->send();

                if (!empty($result['erred']) && in_array($user['email'], $result['erred'])) {
                    $errors[] = 'Error sending e-mail to ' . $user['email'] . ' for issued invoices!';
                }
            } else {
                $errors[] = 'Error! User with ID ' . $user_id . ' does not have an e-mail!';
            }

            // mark that observer has been notified of these results, update observer in record if mail sent to dfcp
            if (!empty($issue_result_info['processed'])) {
                $query = 'UPDATE ' . DB_TABLE_CRONTAB_ISSUE_RESULTS .
                         ' SET processed = NOW(), observer = \'' . $user_id . '\'' . "\n" .
                         'WHERE id IN (' . implode(', ', $issue_result_info['processed']) . ')';
                $db->Execute($query);
            }
        }
    }

    /**
     * IMPORTANT!!!
     * This method checks if background work (started by current user)
     * for issuing invoices is running for this installation
     * and if YES, denies starting of another job for it

     * @param object $registry - the main registry
     * @return bool - if true access is allowed, if false access is forbidden
     */
    public static function checkIssueInvoicesAccess(&$registry) {

        $request = &$registry['request'];

        //exclude the process in order not to kill itself
        if ($request->get('background_mode')) {
            return true;
        }

        $output = null;
        if (preg_match('#WIN#i', PHP_OS)) {
            //get active processes in WINDOWS with their command line parameters
            exec('wmic PROCESS get Commandline', $output);
        } else {
            exec('ps -e -F|grep -i wget', $output);
        }

        $regex1 = '#crontab=issue_invoices#';
        //do it for this installation only (in case there are more than one installations on the server)
        //IMPORTANT: sometimes the installations are access from address different from that in settings > crontab > base_host
        $location1 = sprintf('%s://%s%s?%s=crontab',
                        (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                        $_SERVER["HTTP_HOST"], $_SERVER['PHP_SELF'],
                        Router::MODULE_PARAM);
        $regex2 = '#' . preg_quote($location1) . '#';
        $location2 = sprintf('%s/index.php?%s=crontab',
                        $registry['config']->getParam('crontab', 'base_host'),
                        Router::MODULE_PARAM);
        $regex3 = '#' . preg_quote($location2) . '#';
        foreach ($output as $row) {
            if (preg_match($regex1, $row) && (preg_match($regex2, $row) || preg_match($regex3, $row)) &&
                preg_match('#original_user=' . $registry['currentUser']->get('id') . '#', $row)) {

                return false;
            }
        }

        return true;
    }

    /**
     * Get relations for template
     *
     * @param integer $id - id of invoice template
     * @param array $relations - relations between GT2 rows of template and contract
     * @return mixed - relations between GT2 rows of template and contract
     */
    public static function getRelationsRows($id, &$relations) {

        $db = $GLOBALS['registry']['db'];

        $query = 'SELECT link_to, rows_links FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                 'WHERE parent_id=' . $id . ' AND parent_model_name="Finance_Invoices_Template"' . "\n" .
                 '  AND link_to_model_name="Contract"';
        $records = $db->GetAssoc($query);

        $relations = array();
        foreach ($records as $id => $record) {
            $rows = preg_split('#\n|\r|\r\n#', $record);
            foreach ($rows as $row) {
                if (!preg_match('#\d+\s*=>\s*-?\d+#', $row)) continue;
                $row = preg_split('#\s*=>\s*#', $row, 2);
                if (isset($relations[$row[1]])) {
                    $idx = $row[1];
                    $row[1] .= ' => ' . $relations[$row[1]];
                    unset($relations[$idx]);
                }
                $relations[$row[0]] = $row[1];
            }
        }

        return $relations;
    }

    /**
     * Get templates currencies
     * @param mixed $registry
     * @param array $ids - ids of invoice templates
     * @return array (<id> => array('currency' => <currency1>, 'issue_currency' => <currency2>))
     */
    public static function getCurrencies(&$registry, $ids) {

        if (empty($ids)) {
            return array();
        }

        if (!is_array($ids)) {
            $ids = array($ids);
        }

        $query = 'SELECT id, currency, issue_currency ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . "\n" .
                 'WHERE id IN (' . implode(', ', $ids) . ')';
        return $registry['db']->GetAssoc($query);

    }

    /**
     * Get stored configurations for templates
     * @param mixed $registry
     * @return array - configurations
     */
    public static function getConfigurations(&$registry) {

        $query = 'SELECT id AS option_value, name AS label' . "\n" .
                 'FROM ' . DB_TABLE_CONFIGURATOR . "\n" .
                 'WHERE model="Finance_Invoices_Template"';

        return $registry['db']->GetAll($query);
    }

    /**
     * Get stored configurations for templates
     * @param mixed $registry
     * @param string $action - action name (load, save, delete)
     * @param string $config - config name or id
     * @param bool $is_custom
     * @return array - configurations
     */
    public static function manageConfiguration(&$registry, $action, $config, $is_custom = false) {

        $result = false;
        if ($action == 'save') {
            $fields = $registry['request']->getAll('post');
            $fields = General::slashesEscape(serialize($fields));
            if ($is_custom) {
                $query = 'INSERT INTO ' . DB_TABLE_CONFIGURATOR . ' SET' . "\n" .
                         'name = \'' . General::slashesEscape($config) . '\',' . "\n" .
                         'model = \'Finance_Invoices_Template\',' . "\n" .
                         'params = \'' . $fields . '\',' . "\n" .
                         'added = NOW(),' . "\n" .
                         'added_by = ' . $registry['currentUser']->get('id') . "\n" .
                         'ON DUPLICATE KEY UPDATE params = \'' . $fields . '\'';
            } else {
                $query = 'UPDATE ' . DB_TABLE_CONFIGURATOR . ' SET' . "\n" .
                         'params = \'' . $fields . '\'' . "\n" .
                         'WHERE id = ' . $config;
            }
            $registry['db']->Execute($query);
            if (!$registry['db']->ErrorMsg()) {
                $result = true;
            }
        } elseif ($action == 'delete') {
            $query = 'DELETE FROM ' . DB_TABLE_CONFIGURATOR . ' WHERE id = ' . $config;
            $registry['db']->Execute($query);
            if (!$registry['db']->ErrorMsg()) {
                $result = true;
            }
        } elseif ($action == 'load') {
            $query = 'SELECT params FROM ' . DB_TABLE_CONFIGURATOR . ' WHERE id = ' . $config;
            $result = $registry['db']->GetOne($query);
            $result = unserialize($result);
        }

       return $result;
    }

    public static function getAlias($module = '', $controller = '', $action = '') {
        if ($action == 'export' || $action == 'printlist') {
            return self::$alias . 'i';
        } else {
            return self::$alias;
        }
    }

    /**
     * Prepare gt2 tables for invoices that WOULD be issued
     *
     * @param Registry $registry - the main registry
     * @param array $models - search filters
     * @return array $preparedModels - prepared with GT2
     */
    public static function preparePreviewInvoicesGT2(Registry $registry, array $models) {
        $preparedModels = array();

        //prepare gt2 tables for invoices
        foreach ($models as $key => $template) {
            $preparedModels[$key] = $template;

            $template_id = $template->get('id');
            $db = $registry['db'];
            $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date' . "\n" .
                'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                'LEFT JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                '  ON fit.id = fiti.parent_id' . "\n" .
                'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                '  ON co.id = fit.contract_id' . "\n" .
                'WHERE fiti.invoice_id = 0' . "\n" .
                '  AND co.subtype="contract" AND co.status="closed"' . "\n" .
                '  AND fit.deleted_by = 0' . "\n" .
                '  AND fiti.id=' . $template_id;
            $records = $db->GetAssoc($query);

            if (!empty($records)) {
                //IMPORTANT: set preview argument to TRUE
                $results = self::issueInvoices($registry, $records, true);

                $info = reset($results);

                //check if any errors occurred
                if (!empty($info['errors'])) {
                    continue;
                }

                //there should be only one invoice in the results array, but just to be sure loop them all
                foreach ($info['invoices'] as $invoice) {
                    if (is_object($invoice)) {
                        $gt2 = $invoice->get('grouping_table_2');
                        $preparedModels[$key]->set('grouping_table_2', $gt2, true);
                        $preparedModels[$key]->set('total', $gt2['plain_values']['total'], true);
                        $preparedModels[$key]->set('total_with_vat', $gt2['plain_values']['total_with_vat'], true);
                        $preparedModels[$key]->set('currency', $gt2['plain_values']['currency'], true);
                    }
                }
                //check if fiscal event date has been assigned already
                $records = reset($records);
                if (!empty($records['fiscal_event_date']) && $records['fiscal_event_date'] > '0000-00-00') {
                    $preparedModels[$key]->set('fiscal_event_date', $records['fiscal_event_date'], true);
                }
            }
        }

        return $preparedModels;
    }
}

?>
