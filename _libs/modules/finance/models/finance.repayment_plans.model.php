<?php

/**
 * Finance_Repayment_Plan model class
 */
Class Finance_Repayment_Plan extends Model {
    public $modelName = 'Finance_Repayment_Plan';

    public $counter;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if (!$this->get('customer')) {
            $this->raiseError('error_no_customer_specified', 'customer');
        }

        if (!$this->get('company')) {
            $this->raiseError('error_no_company_specified', 'company');
        }

        if (!$this->get('issue_date')) {
            $this->raiseError('error_no_repayment_plans_issue_date', 'issue_date');
        }

        if (!is_array($this->get('amount'))) {
            $this->raiseError('error_no_repayment_plan_amount_specified', 'amount');
        } elseif (!is_array($this->get('unpaid')) || round(array_sum($this->get('amount')),2) != round(array_sum($this->get('unpaid')),2)) {
            $this->raiseError('error_all_amount', 'unpaid');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //start transaction
        $db->StartTrans();
/*
        //query to update old plans
        if ($this->get('active')) {
            $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                  'SET active=0, deleted=now(), deleted_by=' . $this->registry['currentUser']->get('id') . "\n" .
                  'WHERE company = ' . $this->get('company') .  ' AND customer = ' . $this->get('customer');
            $db->Execute($query);
            $set['active'] = 'active=1';
        } else {
            $set['active'] = 'active=0';
        }
*/
        //query to insert the main table
        $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                  'SET ' . implode(', ', $set);
        $db->Execute($query);

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        $referers = $this->get('referers');
        foreach ($referers as $referer) {
            $insert = array('parent_id='.$this->get('id'), 'incomes_id='.$referer);
            $query = 'INSERT IGNORE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . "\n" .
                      'SET ' . implode(', ', $insert);
            $db->Execute($query);
        }

        $amount = $this->get('amount');
        $description = $this->get('description');
        foreach ($this->get('deadline') as $key => $deadline) {
            $insert = array('parent_id='.$this->get('id'),
                        'deadline='."'$deadline'",
                        'amount='.$amount[$key],
                        'description="'.$description[$key].'"');
            $query = 'INSERT IGNORE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                      'SET ' . implode(', ', $insert);
            $db->Execute($query);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        $set = $this->prepareMainData();

        //query to update old plans
        /*$query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                  'SET active=0 ' . "\n" .
                  'WHERE company = ' . $this->get('company') .  ' AND customer = ' . $this->get('customer');
        $db->Execute($query);*/
/*
        //query to update old plans
        if ($this->get('active')) {
            $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                  'SET active=0, deleted=now(), deleted_by=' . $this->registry['currentUser']->get('id') . "\n" .
                  'WHERE company = ' . $this->get('company') .  ' AND customer = ' . $this->get('customer') . "\n" .
                  'AND id != ' . $this->get('id');
            $db->Execute($query);
            $set['active'] = 'active=1';
        } else {
            $set['active'] = 'active=0';
        }
*/
        //query to insert the main table
        $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id = ' . $this->get('id');
        $db->Execute($query);

        $referers = $this->get('referers');
        $query = 'DELETE FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . "\n" .
                 ' WHERE parent_id = ' . $this->get('id');
        $db->Execute($query);
        foreach ($referers as $referer) {
            $insert = array('parent_id='.$this->get('id'), 'incomes_id='.$referer);
            $query = 'INSERT IGNORE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . "\n" .
                      'SET ' . implode(', ', $insert);
            $db->Execute($query);
        }

        $query = 'DELETE FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                 ' WHERE parent_id = ' . $this->get('id');
        $db->Execute($query);
        $amount = $this->get('amount');
        $description = $this->get('description');
        foreach ($this->get('deadline') as $key => $deadline) {
            $insert = array('parent_id='.$this->get('id'),
                        'deadline='."'$deadline'",
                        'amount='.$amount[$key],
                        'description="'.$description[$key].'"');
            $query = 'INSERT IGNORE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                      'SET ' . implode(', ', $insert);
            $db->Execute($query);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        $set['company']  = sprintf("company=%d", $this->get('company'));
        $set['customer'] = sprintf("customer=%d", $this->get('customer'));
        $set['modified'] = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        $set['issue_date'] = sprintf("issue_date='%s'", $this->get('issue_date'));

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("`active`='%s'", $this->get('active'));
        } else {
            $set['active'] = "`active`=1";
        }

        return $set;
    }

    public function setStatus() {
        $flag_error = false;
        $flag_error_substatus = false;

        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //$permission_unlock = $this->checkPermissions('setstatus_unlock');
        $permission_unlock = false;

        if (!$this->get('status')) {
            $flag_error = true;
        } else {
            if ($this->get('id')) {
                $id_reason = $this->get('id');
            }
            $current_status = Finance_Repayment_Plans::getModelStatus($this->registry, $id_reason);
            $status_info = explode ('_', $this->get('status'));
            $status_name = $status_info[0];
            if ($current_status == 'locked' && $status_name == 'opened') {
                if (! $permission_unlock) {
                    $flag_error = true;
                }
            } else if ($current_status == 'finished' && ($status_name == 'opened' || $status_name == 'locked')) {
                $flag_error = true;
            }

            //takes the status properties based
            //if the status is defined in a dropdown it's a single
            // value containing the main status and the id of the substatus if such is defined
            @ $status_properties = explode('_', $this->get('status'));
            $new_status = $status_properties[0];

            if ($this->get('substatus')) {
                $substatus_properties = explode('_', $this->get('substatus'));
                if ($substatus_properties[0] != $new_status) {
                    $flag_error_substatus = true;
                } else {
                    $new_substatus = $substatus_properties[1];
                }
            } elseif (isset($status_properties[1])) {
                $new_substatus = $status_properties[1];
            }
        }

        if ($flag_error || $flag_error_substatus) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            if (isset($current_substatus)) {
                $this->set('substatus', $current_substatus, true);
            }
            if ($flag_error) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }
            if ($flag_error_substatus) {
                $this->raiseError('error_invalid_substatus_change', 'substatus', -3);
            }
            $db->CompleteTrans();
            return false;
        }

        $set['status'] = sprintf("`status`='%s'", $new_status);
        if (isset($new_substatus)) {
            $set['substatus'] = sprintf("`substatus`=%d", $new_substatus);
        } else {
            $set['substatus'] = "`substatus`=0";
        }

        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($new_status == 'locked') {
            $set = array();
            $set['status'] = sprintf("`status`='%s'", 'finished');
            $set['substatus'] = "`substatus`=" . PH_FINANCE_TRANSFERRED_STATUS;
            $set['modified'] = sprintf("`modified`=now()");
            $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
            $set['status_modified'] = sprintf("`status_modified`=now()");
            $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
            $query1 = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE status="locked" AND id!=' . $this->get('id');
            $db->Execute($query1);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * get unpaid incomes
     *
     * @return array - data
     */
    public function getUnpaidIncomes() {
        $db = $this->registry['db'];
        $query = 'SELECT fir.id FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . ' as frpi ' . "\n" .
                  'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir ON fir.id=frpi.incomes_id' . "\n" .
                  'WHERE frpi.parent_id=' . $this->get('id') . ' AND fir.payment_status != "paid" AND fir.annulled_by=0';
        $ids = $db->GetCol($query);

        $all_amount = 0;
        if ($ids) {
            require_once 'finance.incomes_reasons.factory.php';
            $filters = array();
            $filters['where'] = array('fir.id in (' . implode(',',$ids) .')', 'fir.annulled_by = 0');
            $filters['model_lang'] = $this->get('lang');
            $reasons = Finance_Incomes_Reasons::search($this->registry, $filters);
            $incomes_reasons = array();
            foreach ($reasons as $key => $reason) {
                $reason->getPaidAmount();
                if ($reason->get('type') > PH_FINANCE_TYPE_MAX) {
                    $invoices_amount = $reason->getInvoicedAmount();
                    if ($invoices_amount + $reason->get('paid_amount') < $reason->get('total_with_vat')) {
                        $reason->set('unpaid_amount', $reason->get('total_with_vat') - $invoices_amount - $reason->get('paid_amount'));
                        $incomes_reasons[] = array(
                            'id' => $reason->get('id'),
                            'name' => $reason->get('name'),
                            'unpaid_amount' => $reason->get('unpaid_amount'),
                            'total_with_vat' => $reason->get('total_with_vat'),
                            'date_of_payment' =>  $reason->get('date_of_payment'),
                            'type' =>  $reason->get('type'),
                            'num' =>  $reason->get('num'),
                            'status_modified' =>  $reason->get('status_modified'),
                            'issue_date' =>  $reason->get('issue_date'),
                            'currency' =>  $reason->get('currency'),
                            );
                        $all_amount += $reason->get('unpaid_amount');
                    }
                } elseif ($reason->get('type') == PH_FINANCE_TYPE_INVOICE) {
                    if ($reason->get('paid_amount') < $reason->get('total_with_vat')) {
                        $reason->set('unpaid_amount', $reason->get('total_with_vat') - $reason->get('paid_amount'));
                        $incomes_reasons[] = array(
                            'id' => $reason->get('id'),
                            'name' => $reason->get('name'),
                            'unpaid_amount' => $reason->get('unpaid_amount'),
                            'total_with_vat' => $reason->get('total_with_vat'),
                            'date_of_payment' =>  $reason->get('date_of_payment'),
                            'type' =>  $reason->get('type'),
                            'num' =>  $reason->get('num'),
                            'status_modified' =>  $reason->get('status_modified'),
                            'issue_date' =>  $reason->get('issue_date'),
                            'currency' =>  $reason->get('currency'),
                            );
                        $all_amount += $reason->get('unpaid_amount');
                    }
                }
            }
            $this->set('referers', $incomes_reasons, true);
        }
        $this->set('all_amount', $all_amount, true);

        return;
    }

    /**
     * get unpaid incomes
     *
     * @return array - data
     */
    public function getPlansData($paid = false, $assoc = false) {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];
        $query = 'SELECT deadline, amount, description, paid_amount, last_payment' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . ' as frpa ' . "\n" .
                  'WHERE frpa.parent_id=' . $this->get('id') .
                  ($paid?'':' AND frpa.amount != frpa.paid_amount') . "\n" .
                  'ORDER BY deadline ASC';
        $data = $db->GetAll($query);
        if ($sanitize_after) {
            $this->sanitize();
        }
        if (!$assoc) {
            foreach ($data as $key => $values) {
                $data[$key] = array_values($values);
            }
            return $data;
        }
        return $data;

    }

    /**
     * check amounts
     *
     * @return bool
     */
    public function checkPlansData() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];
        $query = 'SELECT SUM(amount) ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . ' as frpa ' . "\n" .
                  'WHERE frpa.parent_id=' . $this->get('id');
        $amount  = $db->GetOne($query);

        $query = 'SELECT fir.id FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . ' as frpi ' . "\n" .
                  'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir ON fir.id=frpi.incomes_id ' . "\n" .
                  'WHERE frpi.parent_id=' . $this->get('id') . ' AND fir.payment_status != "paid" AND fir.annulled_by=0';
        $ids = $db->GetCol($query);

        $all_amount = 0;
        if ($ids) {
            require_once 'finance.incomes_reasons.factory.php';
            $filters = array();
            $filters['where'] = array('fir.id in (' . implode(',',$ids) .')', 'fir.annulled_by = 0');
            $filters['model_lang'] = $this->get('lang');
            $reasons = Finance_Incomes_Reasons::search($this->registry, $filters);
            $incomes_reasons = array();
            foreach ($reasons as $key => $reason) {
                $reason->getPaidAmount();
                if ($reason->get('type') > PH_FINANCE_TYPE_MAX) {
                    $invoices_amount = $reason->getInvoicedAmount();
                    if ($invoices_amount + $reason->get('paid_amount') < $reason->get('total_with_vat')) {
                        $all_amount += $reason->get('total_with_vat') - $invoices_amount - $reason->get('paid_amount');
                    }
                } elseif ($reason->get('type') == PH_FINANCE_TYPE_INVOICE) {
                    if ($reason->get('paid_amount') < $reason->get('total_with_vat')) {
                        $all_amount += $reason->get('total_with_vat') - $reason->get('paid_amount');
                    }
                }
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        if (sprintf("%.2F", $amount) != sprintf("%.2F", $all_amount)) {
            return false;
        } else {
            return true;
        }
    }

    /*
     * Function for taking the related records from first level
     * It used from custom outlooks
     *
     * @return array $relatives - all related records from first level
     */
    public function getFirstLevelRelatedRecords() {
        $this->unsanitize();

        // query to take the related incomes reasons
        $query = 'SELECT fir.id as related_id, fir.num as fir_num, firi18n.name as fir_name, fir_type.name as fir_type_name ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . ' AS frp' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON (frp.incomes_id=fir.id)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
                 '  ON (firi18n.parent_id=fir.id AND firi18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fir_type' . "\n" .
                 '  ON (fir.type=fir_type.parent_id AND fir_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'WHERE frp.parent_id="' . $this->get('id') . '" AND fir.annulled_by=0';

        $records = $this->registry['db']->GetAll($query);

        // forms the array with related records
        $relatives = array();
        foreach ($records as $key => $record) {
            $relatives[] = array(
                'module'        => 'finance',
                'controller'    => 'incomes_reasons',
                'id'            => $record['related_id'],
                'num'           => $record['fir_num'],
                'note'          => $record['fir_name'] . ' (' . $record['fir_type_name'] . ')'
            );
        }
        $this->sanitize();

        return $relatives;
    }
}

?>
