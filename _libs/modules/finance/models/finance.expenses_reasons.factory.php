<?php

require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.model.php';

/**
 * Finance_Expenses_Reasons model class
 */
Class Finance_Expenses_Reasons extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Finance_Expenses_Reason';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            $sort = 'ORDER BY ' . $sort;
        } else {
            $sort = 'ORDER BY fer.active DESC';
        }
        $sort .= ', fer.id DESC';

        $sql['select'] = 'SELECT DISTINCT(fer.id)' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
                       'JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                       '  ON (fer.type=fdt.id AND fdt.active=1 AND fdt.deleted=0)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS_I18N . ' AS feri18n' . "\n" .
                       '  ON (fer.id=feri18n.parent_id AND feri18n.lang="' . $model_lang . '")' . "\n";
        if (preg_match('#fdd(i18n)?\.#', $where) || preg_match('#fdd(i18n)?\.#', $sort)) {
            //relate to gt2 details
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                            '  ON fdd.model = "' . self::$modelName .  '" AND fdd.model_id = fer.id' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS fddi18n' . "\n" .
                            '  ON fdd.id = fddi18n.parent_id AND fddi18n.lang = "' . $model_lang . '"' . "\n";
        }
        if (preg_match('#fdti18n\.name#', $sort) || isset($filters['field']) && ($filters['field'] == 'fdti18n.name' || !$filters['field'])) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                            '  ON (fer.type=fdti18n.parent_id AND fdti18n.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#fci18n\.name#', $sort) || isset($filters['field']) && ($filters['field'] == 'fci18n.name' || !$filters['field'])) {
            //relate to companies
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                            '  ON (fer.company=fci18n.parent_id AND fci18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#\bc\.#', $where)) {
            //relate to customers
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                            '  ON fer.customer=c.id';
        }
        if (preg_match('#ci18n\.(last)?name#', $where) || preg_match('#ci18n\.name#', $sort) || isset($filters['field']) && (preg_match('#ci18n\.name#', $filters['field']) || !$filters['field'])) {
            //relate to customers
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (fer.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n";
        }
        // process search by trademark keyword in the simple search
        if (preg_match('#fer\.trademark\s*LIKE\s*#', $where)) {
            $where = preg_replace('#fer\.trademark#', 'tm_ss_ni18n.name', $where);
            //relate to nomenclatures
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS tm_ss_ni18n' . "\n" .
                            '  ON (fer.trademark=tm_ss_ni18n.parent_id AND tm_ss_ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ni18n\.name#', $sort) || preg_match('#ni18n\.name#', $where)) {
            //relate to nomenclatures
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                            '  ON (fer.trademark=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#pi18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'pi18n.name') {
            //relate to projects
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                            '  ON (fer.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#oi18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'oi18n.name') {
            // relate to offices
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                            '  ON (fer.office=oi18n.parent_id AND oi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#container_name#', $sort) || preg_match('#fbai18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'container_name') {
            //relate to bank accounts i18n
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N . ' AS fbai18n' . "\n" .
                            '  ON (fer.container_id=fbai18n.parent_id AND fer.payment_type=\'bank\' AND fbai18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#container_name#', $sort) || preg_match('#fcbi18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'container_name') {
            //relate to cashboxes i18n
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_CASHBOXES_I18N . ' AS fcbi18n' . "\n" .
                            '  ON (fer.container_id=fcbi18n.parent_id AND fer.payment_type=\'cash\' AND fcbi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#container_name#', $where)) {
            $where = preg_replace('#container_name#', 'IFNULL(fcbi18n.name, fbai18n.name)', $where);
        }
        if (preg_match('#container_name#', $sort)) {
            $sort = preg_replace('#(container_name)#', 'IFNULL(fcbi18n.name, fbai18n.name)', $sort);
        }
        if (preg_match('#depi18n\.name#', $sort)) {
            //relate to departments
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS depi18n' . "\n" .
                            '  ON (fer.department=depi18n.parent_id AND depi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to users_i18n to fetch added_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (fer.added_by=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to users_i18n to fetch modified_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (fer.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ui18n3\.firstname#', $sort)) {
            //relate to users_i18n to fetch annulled_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                            '  ON (fer.annulled_by=ui18n3.parent_id AND ui18n3.lang="' . $model_lang . '")' . "\n";
        }
        // process search by tags keyword in the simple search
        if (preg_match('#tags\d*\.tag_id\s*LIKE\s*#', $where)) {
            $where = preg_replace('#tags\d*\.tag_id#', 'ti18n_ss.name', $where);
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tags_ss' . "\n" .
                            '  ON (tags_ss.model=\'' . self::$modelName . '\' AND tags_ss.model_id=fer.id)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n_ss' . "\n" .
                            '  ON (tags_ss.tag_id=ti18n_ss.parent_id AND ti18n_ss.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match_all('#tags\d+\.tag_id\s*!?=\s*(\'\d*\')#', $where, $matches)) {
            //relate to expenses reasons tags
            foreach ($matches[0] as $key => $value) {
                $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tags' . $key . "\n" .
                                '  ON (tags' . $key . '.model=\'' . self::$modelName . '\' AND tags' . $key . '.model_id=fer.id AND tags' . $key . '.tag_id=' . $matches[1][$key] . ')' . "\n";
            }
        }
        if (preg_match_all('#fda\d*\.assigned_to#', $where, $matches)) {
            //relate to finance documents assignments
            foreach ($matches[0] as $key => $value) {
                $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_ASSIGNMENTS . ' AS fda' . $key . "\n" .
                                '  ON (fda' . $key . '.parent_id=fer.id AND fda' . $key . '.model="Finance_Expenses_Reason")' . "\n";
            }
        }
        if (preg_match('#\bf\.id#', $where) || preg_match('#\bf\.id#', $sort)) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FILES . ' AS f' . "\n" .
                            '  ON f.model=\'' . self::$modelName . '\' AND f.model_id=' . self::getAlias(self::$modelName, '') . '.id AND f.deleted_by=0' . "\n" .
                            Files::getAdditionalWhere($registry) . "\n";
        }
        if (preg_match_all('#\b(f\.filename)([^"\']+)((["\']).*?(?<!\\\)\4)#', $where, $matches)) {
            // matches keys: 0 - the whole expression, 1 - var_name, 2 - comparison operator, 3 - searched value with surrounding quotes, 4 - the quote character
            foreach ($matches[0] as $key => $value) {
                $sql['from'] .= preg_replace('#\bf(\.|\s)#', "f$key$1",
                                'LEFT JOIN ' . DB_TABLE_FILES . ' AS f' . "\n" .
                                '  ON f.model=\'' . self::$modelName . '\' AND f.model_id=' . self::getAlias(self::$modelName, '') . '.id AND f.deleted_by=0' . "\n" .
                                Files::getAdditionalWhere($registry)) . "\n";
                $negative_search = preg_match('#!=|NOT\s+LIKE#', $matches[2][$key]);
                // replace in WHERE clause only once (the last parameter is 1)
                $where = preg_replace('#' . preg_quote($matches[0][$key], '#') . '#', ($negative_search ? "f$key.id IS NULL" : "fi18n$key.parent_id IS NOT NULL"), $where, 1);
                if ($negative_search) {
                    $falias = 'f_';
                    $sql['from'] .= "  AND f$key.id IN (SELECT $falias$key.id FROM " . DB_TABLE_FILES . " AS $falias$key" . "\n" . '  ';
                } else {
                    $falias = 'f';
                }
                $matches[0][$key] =
                    preg_replace('#^f\.#', $falias . $key . '.', $matches[1][$key]) .
                    ($negative_search ? preg_replace('#!|NOT\s+#', '', $matches[2][$key]) : $matches[2][$key]) .
                    $matches[3][$key];
                // expand search by name into filename, name and description fields
                $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS ' . $falias . 'i18n' . $key . "\n" .
                                '  ON ' . $falias . 'i18n' . $key . '.parent_id=' . $falias . $key . '.id AND ' . $falias . 'i18n' . $key . '.lang=\'' . $model_lang . '\'' . "\n" .
                                ($negative_search ? '  WHERE ' . $falias . $key . '.model=\'' . self::$modelName . '\'' . "\n" : '') .
                                '    AND (' . implode(' OR ', array(
                                    $matches[0][$key],
                                    preg_replace('#^' . $falias . $key . '\.filename#', $falias . 'i18n' . $key . '.name', $matches[0][$key]),
                                    preg_replace('#^' . $falias . $key . '\.filename#', $falias . 'i18n' . $key . '.description', $matches[0][$key]))) .
                                    ')' .($negative_search ? ')' : '') . "\n";
            }
        }

        $sql['where'] = $where . "\n";

        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';
        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(DISTINCT fer.id) AS total';
                $sql['order'] = '';
                $sql['limit'] = '';
                $query = implode("\n", $sql);
                $filters['total'] = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $filters['total'] = count($ids);
            }
        }

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        $sql = array('select' => '',
                     'from' => '',
                     'where' => '',
                     'group' => '',
                     'order' => '',
                     'limit' => '');

        if (!isset($filters['getEmpty']) || isset($filters['getEmpty']) && !$filters['getEmpty']) {
            if ($registry->get('getOneRequested')) {
                //one model is searched(searchOne)
                //so getIds is not needed
                $ids = self::constructWhere($registry, $filters);
            } else {
                $ids = self::getIds($registry, $filters, $sql);
            }
        } else {
            $ids = 'WHERE 1';
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $prec = $registry['config']->getSectionParams('precision');
        $prTotal = $prec['gt2_total'];
        $prTotalVat = $prec['gt2_total_vat'];
        $prTotalWithVat = $prec['gt2_total_with_vat'];

        $sql['select'] = "SELECT DISTINCT(fer.id), fer.*, feri18n.*, fci18n.name as company_name, fer.id AS order_idx, 
                           TRIM(CONCAT(ci18n.name, ' ', ci18n.lastname)) as customer_name,
                           feri18n.customer_name as fiscal_customer_name, feri18n.customer_address as fiscal_customer_address,
                           fer.eik as fiscal_customer_eik, fer.vat_num as fiscal_customer_vat_num,
                           CAST(ROUND(fer.total_without_discount, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_without_discount,
                           CAST(ROUND(fer.total_discount_value, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_discount_value,
                           CAST(ROUND(fer.total_discount_percentage, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_discount_percentage,
                           CAST(ROUND(fer.total_surplus_value, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_surplus_value,
                           CAST(ROUND(fer.total_surplus_percentage, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total_surplus_percentage,
                           CAST(ROUND(fer.total, {$prTotal}) AS DECIMAL(25,{$prTotal})) AS total,
                           CAST(ROUND(fer.total_vat, {$prTotalVat}) AS DECIMAL(25,{$prTotalVat})) AS total_vat,
                           CAST(ROUND(fer.total_with_vat, {$prTotalWithVat}) AS DECIMAL(25,{$prTotalWithVat})) AS total_with_vat,
                           fci18n.mol as company_mol, fc.VAT_number as company_vat_number,
                           oi18n.name as office_name, '{$model_lang}' as model_lang,
                           depi18n.name AS department_name, gi18n.name AS group_name, fdt.type_section,
                           fds.name AS substatus_name, fds.icon_name, fdti18n.name as type_name,
                           c.code AS customer_code,
                           IF (fer.trademark > 0, fer.trademark, '') AS trademark, ni18n.name AS trademark_name,
                           IF (fer.trademark > 0, n.code, '') AS trademark_code,
                           p.code AS project_code, pi18n.name AS project_name, si18n.name AS phase_name,
                           fer.employee AS employee1, CONCAT(ci18n2.name,  ' ', ci18n2.lastname) AS employee_name,
                           CONCAT(ui18n1.firstname, ' ', ui18n1.lastname) as added_by_name,
                           CONCAT(ui18n2.firstname, ' ', ui18n2.lastname) as modified_by_name,
                           CONCAT(ui18n3.firstname, ' ', ui18n3.lastname) as annulled_by_name ";

        //from clause
        if (!isset($filters['getEmpty']) || isset($filters['getEmpty']) && !$filters['getEmpty']) {
            //normal search
            $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                           'JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                           '  ON (fer.type=fdt.id AND fdt.active=1 AND fdt.deleted=0)' . "\n";
        } else {
            //we need an empty model for transformation from documents
            $sql['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS noneed' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                           '  ON fer.id IS NULL' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                           '  ON (fer.type=fdt.id AND fdt.active=1 AND fdt.deleted=0)' . "\n";
        }
        $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS_I18N . ' AS feri18n' . "\n" .
                       '  ON (fer.id=feri18n.parent_id AND feri18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_STATUSES . ' AS fds' . "\n" .
                       '  ON (fds.id=fer.substatus AND fds.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fdti18n' . "\n" .
                       '  ON (fer.type=fdti18n.parent_id AND fdti18n.lang="' . $lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES . ' AS fc' . "\n" .
                       '  ON (fer.company=fc.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                       '  ON (fer.company=fci18n.parent_id AND fci18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                       '  ON (fer.office=oi18n.parent_id AND oi18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                       '  ON (fer.customer=c.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                       //related trademarks
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       '  ON (fer.trademark=n.id)' . "\n" .
                       //related trademarks i18n
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       '  ON (fer.trademark=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to project
                       'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                       '  ON (fer.project=p.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                       '  ON (fer.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_STAGES_I18N . ' AS si18n' . "\n" .
                       '  ON (fer.phase=si18n.parent_id AND si18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to document employee
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n2' . "\n" .
                       '  ON (fer.employee=ci18n2.parent_id AND ci18n2.lang="' . $model_lang . '")' . "\n" .
                       //relate to document group
                       'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                       '  ON (fer.group=gi18n.parent_id AND gi18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to document department
                       'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS depi18n' . "\n" .
                       '  ON (fer.department=depi18n.parent_id AND depi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to users_i18n to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (fer.added_by=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n" .
                        //relate to users_i18n to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (fer.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $model_lang . '")' . "\n" .
                        //relate to users_i18n to fetch annulled by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (fer.annulled_by=ui18n3.parent_id AND ui18n3.lang="' . $model_lang . '")' . "\n";

        // collect all conditional (conditional for list/search/filter) fields to get
        $select_conditional = array();
        // collect all conditional (conditional for list/search/filter) table joins
        $from_conditional = array();

        if (empty($filters['get_fields']) || in_array('container_id', $filters['get_fields'])) {
            $select_conditional[] = 'IF (fer.payment_type = \'bank\', fbai18n.name, fcbi18n.name) AS container_name';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N . ' AS fbai18n' . "\n" .
                '  ON (fer.container_id=fbai18n.parent_id AND fer.payment_type=\'bank\' AND fbai18n.lang="' . $model_lang . '")' . "\n" .
                'LEFT JOIN ' . DB_TABLE_FINANCE_CASHBOXES_I18N . ' AS fcbi18n' . "\n" .
                '  ON (fer.container_id=fcbi18n.parent_id AND fer.payment_type=\'cash\' AND fcbi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (!empty($filters['get_fields']) && in_array('comments', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT comments.id) AS comments';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_COMMENTS . ' AS comments' . "\n" .
                '  ON comments.model = \'' . self::$modelName . '\' AND comments.model_id = fer.id' .
                ($registry['currentUser'] && $registry['currentUser']->get('is_portal') ? ' AND comments.is_portal = "1"' : '') . "\n";
        }

        if (!empty($filters['get_fields']) && in_array('emails', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT esb.code) AS emails';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_EMAILS_SENTBOX . ' AS esb' . "\n" .
                '  ON esb.model = \'' . self::$modelName . '\' AND esb.model_id = fer.id AND esb.`system` = 0 AND esb.resent_mail_id = 0' . "\n";
        }

        if (!empty($filters['get_fields']) && in_array('history_activity', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT fh.h_id) AS history_activity';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_FINANCE_HISTORY . ' AS fh' . "\n" .
                '  ON fh.model = \'' . self::$modelName . '\' AND fer.id = fh.model_id AND fh.action_type IN (\'' . implode('\', \'', History::$activity_actions) . '\')' . "\n";
        }

        if ($select_conditional) {
            $sql['select'] .= ', ' . "\n" . implode(', ' . "\n", $select_conditional);
        }

        if ($from_conditional) {
            $sql['from'] .= "\n" . implode('', $from_conditional);
        }

        if (is_array($ids) && count($ids)) {
            //ids are returned form getIds so search and sort by them
            $sql['where'] = 'WHERE fer.id in ('.@implode(',',$ids).')';
            $sql['order'] = 'ORDER BY find_in_set(order_idx, "'.@implode(',',$ids).'")';
            $sql['limit'] = '';
        } elseif ($registry->get('getOneRequested') && !empty($ids)) {
            //one model is searched(searchOne)
            $sql['where'] = $ids;
            $sql['order'] = '';
            $sql['limit'] = 'LIMIT 1';
        } else {
            //getIds returned empty result the search will not be performed
            if (!empty($filters['paginate'])) {
                return array(array(), 0);
            } else {
                return array();
            }
        }
        $sql['group'] = 'GROUP BY fer.id';

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            $results = array($models, $filters['total']);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @return string - the prepared where clause
     */
    public static function constructWhere(&$registry, &$filters = array()) {
        $where = array();
        $where[] = 'WHERE (';

        $model_types = array();

        self::prepareRightsFilters($registry, $filters, $model_types);

        $query = 'SELECT name FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE model = "GT2_Sample" AND multilang = 1';
        $ml_vars = $registry['db']->GetCol($query);

        $assignments_index = 0;
        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $key_where = array();
                $fields = array();
                if (is_array($filters['field'])) {
                    $fields = $filters['field'];
                    $filters['field'] = implode(', ', $filters['field']);
                } else {
                    $fields[] = $filters['field'];
                }
                foreach ($fields as $field) {
                    $key_where[] = General::buildClause($field, trim($filters['key']), true, 'like');
                }
            } else {
                //search in all fields
                $key_where = array();
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
            }
            $where[] = '(' . implode(" OR \n\t", $key_where) . ') AND ';
            if (!empty($filters['hidden_type_section']) && $filters['hidden_type_section']) {
                $where[] = '(fdt.type_section = \'' . urldecode($filters['hidden_type_section']) . '\') AND ';
            } elseif (!empty($filters['hidden_type']) && $filters['hidden_type']) {
                $where[] = '(fer.type = \'' . urldecode($filters['hidden_type']) . '\') AND ';
            }
        }
        if (isset($filters['where'])) {
            $tags_index = 0;
            $current_user_id = $registry['currentUser']->get('id');
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (preg_match('#^a(__)+.*#', $filter)) {
                    //additional var is requested
                    //so parse the filter definition
                    list($field, $operator, $value, $logical) = parent::parseFilter($filter);
                    $field_r = preg_replace('#^a(__)+#', '', $field);
                    if (in_array($field_r, $ml_vars)) {
                        $filter = preg_replace('#'. $field . '#', 'fddi18n.' . $field_r, $filter);
                    } else {
                        $filter = preg_replace('#'. $field . '#', 'fdd.' . $field_r, $filter);
                    }
                }
                if (preg_match('#^fda\.assigned_to#', $filter)) {
                    $assignment = preg_replace('#[^\s]*(\s)+assignment_([^\s]*)(\s)+.*#i', '$2', $filter);
                    $assignment = constant('PH_ASSIGNMENTS_' . strtoupper($assignment));

                    // check if the no user assigned filter is selected
                    if (preg_match('#no_user_assigned#', $filter)) {
                        // gets the logical operatior
                        $logical = preg_replace('#.*\s+(AND|OR)\s*$#', '$1', $filter);

                        // prepare the query to take the unassigned of this assignment type documents
                        $query_not_assigned = 'SELECT DISTINCT parent_id' . "\n" .
                                              'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_ASSIGNMENTS . "\n" .
                                              'WHERE model="Finance_Expenses_Reason" AND assignments_type="' . $assignment . '"' . "\n";
                        $not_assigned_ids = $registry['db']->GetCol($query_not_assigned);

                        // construct the filter
                        $filter = 'fer.id NOT IN (' . (! empty($not_assigned_ids) ? implode(', ', $not_assigned_ids) : 0) . ') ' . $logical;
                    } else {
                        // default construction of the filter for a user
                        $filter = preg_replace('#(\s)+assignment_[^\s]*#i', '', $filter);
                        $filter = 'fda.assignments_type = ' . $assignment . ' AND ' . $filter;
                        $filter = preg_replace('#fda\.#', 'fda' . $assignments_index . '.', $filter);
                        $assignments_index ++;
                    }
                }
                if (preg_match('#^fer\.accounting_period#', $filter)) {
                    list($field, $operator, $value, $logical) = parent::parseFilter($filter);
                    $value = preg_replace('#-\d{2}$#', '-01', $value);
                    $filter = sprintf('%s %s %s', $field, sprintf($operator, $value), $logical);
                }
                if (preg_match('#^tags\.tag_id#', $filter)) {
                    $filter = preg_replace('#tags\.#', 'tags' . $tags_index . '.', $filter);
                    if (preg_match('#!=#', $filter)) {
                        $filter = '(' . preg_replace('#(\'\d*\')#', '$1 OR tags' . $tags_index . '.tag_id IS NULL)', $filter);
                    }
                    $tags_index++;
                }
                // apply replacement only for search definitions, not for filters set in the code
                if (preg_match("#fer\.active\s*=\s*'\s*(=\s*0|IS\s+NOT\s+NULL)\s*'#i", $filter)) {
                    $filter = preg_replace("#=\s*'(.*)'#", '$1', $filter);
                }
                if (preg_match('#fer\.payment_type\s+(!?=)\s+\'(bank|cheque)\'#', $filter, $matches)) {
                    $filter = preg_replace('#((fer\.)payment_type\s+)(!?=)\s+\'(bank|cheque)\'#', ($matches[1] != '=' ? 'NOT ' : '') . '($1= \'bank\' AND $2cheque = ' . ($matches[2] == 'bank' ? 0 : 1) . ')', $filter);
                }

                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);

        if (!preg_match('#fer\.type#', $where) && !preg_match('#fdt\.type_section#', $where) &&
        !preg_match('#fer\.id#', $where) && !preg_match('#^((sub|ajax_side)panel|ajax_select)$#', $registry['action'])) {
            $where .= ' AND fer.type > ' . PH_FINANCE_TYPE_MAX;
        }
        if (!preg_match('#fer\.id#', $where) && !preg_match('#fer\.active#', $where)) {
            $where .= ' AND fer.active = 1';
        }

        return $where;
    }

    /**
     * Adds additional conditions to WHERE clause based on current user's permissions for action
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @param array $model_types - this array stores the types of documents (their ids), not used yet
     * @return bool
     */
    private static function prepareRightsFilters(&$registry, &$filters, &$model_types) {
        $current_user_id = $registry['currentUser']->get('id');
        $rights = $registry['currentUser']->get('rights');
        $module = 'finance';
        $controller = 'expenses_reasons';

        // get action to check permissions for according to action in registry
        if ($registry['action'] == 'filter' || $registry['action'] == 'dashlet') {
            $action = 'search';
        } elseif ($registry['action'] == 'subpanel' || $registry['action'] == 'ajax_sidepanel') {
            $action = 'list';
        } elseif ($registry['action'] == 'export' || $registry['action'] == 'printlist') {
            if (!empty($filters['session_param']) && $filters['session_param'] == 'search_' . strtolower(self::$modelName)) {
                $action = 'search';
            } else {
                $action = 'list';
            }
        } else {
            $action = $registry['action'];
        }

        // When the action is ajax_select and there is a flag to check the permissions of the current user
        if ($action == 'ajax_select' && isset($filters['check_user_permissions']) && $filters['check_user_permissions'] == true) {
            // Use the same permissions as the search action
            $action = 'search';
        }

        // permissions for types are checked only for multiple actions
        // NOTE: to allow autocomplete filter by additional variables accumulating $model_types allow 'ajax_select' action
        //       $model_types array is used to search by those additional variables
        if ($action != 'list' && $action != 'search' && $action != 'ajax_select') {
            return true;
        }

        $doc_types_filters = array();

        require_once PH_MODULES_DIR . 'finance/models/finance.documents_sections.factory.php';
        // searches the filters for sections or types
        if (isset($filters['where'])) {
            $model_types = array();

            // use reflection to get static property of class
            $controller_name = General::singular2plural(self::$modelName) . '_Controller';
            require_once PH_MODULES_DIR . $module . '/controllers/' . $module . '.' . $controller . '.controller.php';
            $reflection_class = new ReflectionClass($controller_name);
            $prop = 'searchAdditionalVarsSwitch';
            $static_props = $reflection_class->getStaticProperties();
            $searchAdditionalVarsSwitch = array_key_exists($prop, $static_props) !== false ? $static_props[$prop] : '';
            unset($reflection_class);

            // keep logical operator from previous iteration
            $prev_logical_operator = '';
            // flag whether previous iteraton was positive search by type or section
            $prev_was_type_or_section = false;
            // flag whether there is a positive search by type or section in an OR clause
            $type_or_section_in_OR_clause = false;

            foreach ($filters['where'] as $key => $filter_where) {
                if (preg_match('/=\s*$/', $filter_where)) {
                    //clear the empty filters
                    unset($filters['where'][$key]);
                    continue;
                }

                //make sure all the types are fetched from the filters and only then manage the rest of the filters
                //this is necessary because the search by additional vars needs ALL the types
                if (preg_match('#^' . preg_quote($searchAdditionalVarsSwitch) . '#', $filter_where)) {
                    $parsed_filter = self::parseFilter($filter_where);

                    if (preg_match('#^=#', $parsed_filter[1]) && $parsed_filter[2] !== '') {
                        //check if the compare operator is EXACTLY "equals to" (=)
                        $model_types[] = $parsed_filter[2];
                    }
                }

                list($filter_name, $filter_compare, $filter_value, $logical_operator) = self::parseFilter($filter_where);
                if ($filter_name == 'fer.type' && !preg_match('#(^| *)NOT( *)#', $filter_compare) && !preg_match('#(^| *)!=( *)#', $filter_compare)) {
                    $filtered_values = array();
                    $all_values = preg_split('# *, *#', $filter_value);
                    foreach ($all_values as $single_value) {
                        $filtered_values[] = $single_value;
                    }

                    // if a positive search is found, then all the
                    // searched types are set in the separate array
                    $doc_types_filters[] = array(
                        'types'             => $filtered_values,
                        'idx'               => $key,
                        'logical_operator'  => $logical_operator
                    );

                    if ($prev_logical_operator == 'OR' && !$prev_was_type_or_section) {
                        $type_or_section_in_OR_clause = true;
                    }
                    $prev_was_type_or_section = true;
                } else if ($filter_name == 'fdt.type_section' && !preg_match('#(^| *)NOT( *)#', $filter_compare) && !preg_match('#(^| *)!=( *)#', $filter_compare)) {
                    $filtered_values = array();
                    $all_values = preg_split('# *, *#', $filter_value);
                    foreach ($all_values as $single_value) {
                        $filtered_values[] = $single_value;
                    }

                    // if a positive search for section is found, then all
                    // active types in the section are found
                    $section_types = Finance_Documents_Sections::searchSectionWithTypesAdded($registry, $filtered_values);

                    $section_types_ids = array();
                    foreach ($section_types as $s_type) {
                        $section_types_ids[] = $s_type['type_id'];
                    }

                    $doc_types_filters[] = array(
                        'types'             => $section_types_ids,
                        'idx'               => $key,
                        'logical_operator'  => $logical_operator
                    );

                    if ($prev_logical_operator == 'OR' && !$prev_was_type_or_section) {
                        $type_or_section_in_OR_clause = true;
                    }
                    $prev_was_type_or_section = true;
                } else {
                    // not positive search by type or section
                    if ($prev_logical_operator == 'OR' && $prev_was_type_or_section) {
                        $type_or_section_in_OR_clause = true;
                    }
                    $prev_was_type_or_section = false;
                }
                // keep last logical operator for next iteration
                $prev_logical_operator = $logical_operator;
            }

            $inactive_model_types = array();
            if (!empty($doc_types_filters)) {
                if ($type_or_section_in_OR_clause) {
                    // take all inactive types
                    $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES .
                             ' WHERE model="Finance_Expenses_Reason" AND active=0 OR deleted!=0';
                    $inactive_model_types = $registry['db']->GetCol($query);
                } elseif ($model_types) {
                    // take inactive types from model types
                    $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES .
                             ' WHERE id IN (' . implode(', ', $model_types) . ') AND active=0 OR deleted!=0';
                    $inactive_model_types = $registry['db']->GetCol($query);
                }

                // if search by types, check if they are active and keep only active ones in types filters
                if ($model_types && $inactive_model_types) {
                    foreach ($doc_types_filters as $idx => $ctf) {
                        $active_model_types = array_diff($ctf['types'], $inactive_model_types);
                        if ($active_model_types) {
                            $doc_types_filters[$idx]['types'] = $active_model_types;
                        } else {
                            $doc_types_filters[$idx]['types'] = array('');
                        }
                    }
                }
            }
        }

        // if there are no filters set for a section or a type,
        // expense documents of all active user-defined types are taken
        if (empty($doc_types_filters) && empty($filters['skip_permissions_check'])) {
            $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES .
                     ' WHERE model="Finance_Expenses_Reason" AND active=1 AND deleted=0';
            // for subpanel, sidepanel, autocompleter search - take system types as well
            if (!preg_match('#^((sub|ajax_side)panel|ajax_select)$#', $registry['action']) && empty($filters['autocompleter_search'])) {
                $query .= ' AND id > ' . PH_FINANCE_TYPE_MAX;
            }
            $all_document_types_ids = $registry['db']->GetCol($query);
            $doc_types_filters[] = array(
                'types'             => $all_document_types_ids,
                'idx'               => '',
                'logical_operator'  => 'AND'
            );
        }

        if (!empty($doc_types_filters)) {
            foreach ($doc_types_filters as $dtf) {
                $current_rights_where = array();
                foreach ($dtf['types'] as $dt) {
                    $filter_where_new_value = array();
                    $current_right = ($registry['module'] == $module || (isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == $module)) &&
                                     isset($rights[$module . '_' . $controller . $dt][$action]) ? $rights[$module . '_' . $controller . $dt][$action] : '';

                    //additional 'where' for hiding not allowed models
                    if ($current_user_id && $current_right) {
                        if ($current_right == 'all') {
                            $current_rights_where[] = "fer.type='$dt'";
                        } elseif ($current_right == 'mine') {
                            $current_rights_where[] = "((fer.user_permissions LIKE('%,$current_user_id,%') OR fer.added_by=$current_user_id) AND fer.type='$dt')";
                        } elseif ($current_right == 'group') {
                            $user_groups = $registry['currentUser']->get('groups');
                            $user_departments = $registry['currentUser']->get('departments');
                            $current_rights_where[] = "((fer.user_permissions like('%,$current_user_id,%') OR fer.added_by=$current_user_id" .
                                        (count($user_groups) ? ' OR fer.`group` IN (' . implode(',', $user_groups) . ')' : '') .
                                        (count($user_departments) ? ' OR fer.department IN (' . implode(',', $user_departments) . ')' : '') .
                                        ") AND fer.type='$dt')";
                        } elseif ($current_right == 'none') {
                            $current_rights_where[] = "0";
                        }
                    }
                }
                if (!empty($current_rights_where)) {
                    $changed_filter = '(' . implode(' OR ' . "\n", $current_rights_where) . ') ' . ($dtf['logical_operator'] ? $dtf['logical_operator'] : 'AND');
                    if ($dtf['idx'] !== '') {
                        $filters['where'][$dtf['idx']] = $changed_filter;
                    } else {
                        if (! isset($filters['where'])) {
                            $filters['where'] = array();
                        }
                        array_unshift($filters['where'], $changed_filter);
                    }
                }
            }
        }

        return true;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {

        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        if ($registry['request']->isPost() && ($registry['action'] == 'subpanel' ||
        preg_match('#^' . General::plural2singular($registry['module']) . '\d+_ajax_$#', $sessionPrefix))) {
            // saving filters for records in subpanel
            // do not process filters switch keys if found in request
        } elseif ($registry['request']->isRequested('type_section') && $registry['request']->get('type_section')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'fdt.type_section = \'' . urldecode($registry['request']->get('type_section')) . '\'';
        } elseif ($registry['request']->isRequested('type') && $registry['request']->get('type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'fer.type = \'' . urldecode($registry['request']->get('type')) . '\'';
        } elseif ($registry['request']->isRequested('hidden_type_section') && $registry['request']->get('hidden_type_section')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'fdt.type_section = \'' . urldecode($registry['request']->get('hidden_type_section')) . '\'';
        } elseif ($registry['request']->isRequested('hidden_type') && $registry['request']->get('hidden_type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'fer.type = \'' . urldecode($registry['request']->get('hidden_type')) . '\'';
        } elseif ($registry['request']->isRequested('type') && $registry['request']->isRequested('type_section') &&
                  !$registry['request']->get('type') && !$registry['request']->get('type_section')) {
            $filters['display'] = $registry['session']->get($sessionParam)['display'] ?? '';
            $registry['session']->remove($sessionParam);
        }

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Builds a model object
     */
    public static function buildModelIndex(&$registry, $index) {
        $model = self::buildFromRequestIndex($registry, self::$modelName, $index);

        return $model;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool|int - result of the operation - false on error, true if all were updated, N if some were updated, -1 if none were updated
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        //start transaction
        $db->StartTrans();

        // collect all (de)activated models as re-use them when writing history
        $old_models = array();
        // ids of corrections that were (de)activated indirectly
        $correct_ids = array();
        // ids of recods that cannot be (de)activated
        $invalid_ids = array();

        foreach ($ids as $idx => $model_id) {
            $model = self::searchOne($registry, array('where' => array('fer.id = ' . $model_id)));
            if ($model && $model->checkChangeActivePermissions($status)) {
                // model has correction documents
                if ($correct_reasons = $model->get('correct_reasons')) {
                    foreach ($correct_reasons as $reason) {
                        $old_models[$reason->get('id')] = clone $reason;
                        $corr_idx = array_search($reason->get('id'), $invalid_ids);
                        if ($corr_idx !== false) {
                            unset($invalid_ids[$corr_idx]);
                            $ids[] = $reason->get('id');
                        } else {
                            $correct_ids[] = $reason->get('id');
                        }
                    }
                }
                $model->unsetProperty('correct_reasons', true);
                unset($correct_reasons);

                $old_models[$model_id] = clone $model;
                $old_models[$model_id]->sanitize();
            } else {
                $corr_idx = array_search($ids[$idx], $correct_ids);
                if ($corr_idx === false) {
                    $invalid_ids[] = $ids[$idx];
                    unset($ids[$idx]);
                } else {
                    unset($correct_ids[$corr_idx]);
                }
            }
            unset($model);
        }

        if (!empty($ids)) {
            $ids = array_merge($ids, $correct_ids);
            $where = array();
            $where[] = General::buildClause('id', $ids);

            //UPDATE THE MAIN TABLE OF THE MODEL
            $set = array();
            $set['status']      = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
            $set['modified']    = sprintf("modified=now()");
            $set['modified_by'] = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

            //query to update the main table
            $query = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                     'SET ' . implode(', ', $set) . "\n" .
                     'WHERE ' . implode(' AND ', $where);
            $db->Execute($query);

            foreach ($ids as $model_id) {
                $old_model = $old_models[$model_id];
                $new_model = clone $old_model;
                $new_model->set('active', ($status == 'activate' ? 1 : 0), true);

                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                Finance_Expenses_Reasons_History::saveData($registry,
                                                          array('new_model' => $new_model,
                                                                'old_model' => $old_model,
                                                                'action_type' => $status));
            }
        }

        unset($old_models);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result && count($invalid_ids)) {
            // get the actual number of ids that were checked by user
            $result = count(array_diff($ids, $correct_ids)) ?: -1;
        }

        return $result;
    }

    /**
     * gets status of a document
     */
    public static function getModelStatus(&$registry, $id) {
        $db = $registry['db'];
        $query = 'SELECT status FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS .
                 ' WHERE id="' . $id . '"';
        $stuses = $db->GetAll($query);
        $current_status = $stuses[0]['status'];

        return $current_status;
    }

    /**
     * Change status of multiple models
     *
     * @param object $registry - the main registry
     * @param Controller $controller - controller object
     * @return boolean|int - result of operation: false on error, true if all were updated, N if some were updated, -1 if none were updated
     */
    public static function multiStatus(&$registry, $controller) {
        $request = &$registry['request'];

        $status = $request->get('multistatusSelect');
        $substatus = '';
        if (preg_match('#_#', $status)) {
            $substatus = $status;
            $status = explode('_', $status);
            $status = $status[0];
        }
        if (empty($status)) {
            return false;
        }

        //IDs of models to edit
        $ids = $request->get('items');

        $db = &$registry['db'];
        $db->StartTrans();

        $old_model = $new_models = array();
        foreach ($ids as $id) {
            $filters = array('where' => array('fer.id = ' . $id),
                             'model_lang' => $registry->get('lang'));
            $old_document = Finance_Expenses_Reasons::searchOne($registry, $filters);

            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($registry, $filters);
            $finance_expenses_reason->set('status', $status, true);
            $finance_expenses_reason->set('substatus', $substatus, true);

            if ($finance_expenses_reason->setStatus()) {
                $new_document = Finance_Expenses_Reasons::searchOne($registry, $filters);
                $temp_model = clone $new_document;
                $new_models[] = $temp_model->sanitize();
                $old_model[] = $old_document->sanitize();
                unset($temp_model);
                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                $audit_parent = Finance_Expenses_Reasons_History::saveData($registry,
                                                                           array('action_type' => 'multistatus',
                                                                                 'new_model' => $new_document,
                                                                                 'old_model' => $old_document));

                if ($request->get('multistatus_comment')) {
                    require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                    $comment = Comments::buildModel($registry);

                    $comment->set('content', $request->get('multistatus_comment'), true);
                    $comment->set('subject', $registry['translater']->translate('finance_status_change_comment'), true);
                    $comment->set('model', 'Finance_Expenses_Reason', true);
                    $comment->set('model_id', $id, true);
                    $comment->set('is_portal', ($request->get('is_portal') ? '1' : '0'), true);
                    $comment->unsetProperty('id', true);

                    if ($comment->save()) {
                        $comment->slashesStrip();

                        $comment->saveHistory($new_document);

                        //show corresponding message
                        $registry['messages']->setMessage($registry['translater']->translate('message_finance_comments_add_success'), '', -1);
                    } else {
                        //some error occurred
                        //show corresponding error(s)
                        $registry['messages']->setError($registry['translater']->translate('error_comments_add_failed'), '', -1);
                    }
                }

                //$new_document->set('comment', $comment, true);

                //send status notification
                //$controller->sendStatusNotification($new_document, $audit_parent);

            }
            unset($finance_expenses_reason);
            unset($new_document);
            unset($old_document);
        }

        //set models for automations
        $controller->old_model = $old_model;
        $controller->new_models = $new_models;

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result && count($ids) > count($new_models)) {
            $result = count($new_models) ?: -1;
        }

        return $result;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param object $registry - the main registry
     * @param object $controller - controller object
     * @return bool - result of operation
     */
    public static function multiPrint(&$registry, $controller) {
        $db = $registry['db'];
        $request = &$registry['request'];

        $model_lang = $request->isRequested('model_lang') ? $request->get('model_lang') : $registry['lang'];

        //IDs of models to print
        $ids = $request->get('items');

        $db->StartTrans();

        //set time limit and memory limit
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        //get models
        $filters = array('where' => array('fer.id IN (' . implode(', ', $ids) . ')',
                                          'fer.annulled_by = 0'),
                         'model_lang' => $model_lang,
                         'sanitize' => true);
        $finance_expenses_reasons = Finance_Expenses_Reasons::search($registry, $filters);

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1'),
                         'model_lang' => $model_lang,
                         'sanitize' => true);
        $pattern = Patterns::searchOne($registry, $filters);

        $pattern_modify_dates = array();
        $pattern_modify_dates[] = $pattern->get('modified');
        if ($pattern->get('header') || $pattern->get('footer')) {
            require_once PH_MODULES_DIR . 'patterns/models/patterns.parts.factory.php';
            if ($pattern->get('header')) {
                $filters_header = array(
                    'where'     => array('pp.id ="' . $pattern->get('header') . '"'),
                    'model_lang'=> $registry->get('model_lang'),
                    'sanitize'  => true
                );
                $header = Patterns_Parts::searchOne($registry, $filters_header);
                $pattern_modify_dates[] = $header->get('modified');
            }
            if ($pattern->get('footer')) {
                $filters_footer = array(
                    'where'     => array('pp.id ="' . $pattern->get('footer') . '"'),
                    'model_lang'=> $registry->get('model_lang'),
                    'sanitize'  => true
                );
                $footer = Patterns_Parts::searchOne($registry, $filters_footer);
                $pattern_modify_dates[] = $footer->get('modified');
            }
        }
        rsort($pattern_modify_dates);
        $latest_pattern_modify_date = reset($pattern_modify_dates);

        $query_type_name_plural = 'SELECT name_plural FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                                  'WHERE parent_id=' . $pattern->get('model_type') . ' AND lang="' . $model_lang . '"';
        $type_name_plural = $db->getOne($query_type_name_plural);
        if (!$type_name_plural) {
            $type_name_plural = $registry['translater']->translate(strtolower(General::singular2plural(self::$modelName)));
        }

        //check if already having generated files for models with selected pattern (get latest version)
        $query_files = 'SELECT model_id AS idx, f.*' . "\n" .
                       'FROM ' . DB_TABLE_FILES . ' AS f ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON f.id=fi18n.parent_id AND fi18n.lang="' . $model_lang . '"' . "\n" .
                       'WHERE model="' . self::$modelName . '" AND model_id IN (' . implode(', ', $ids) . ') AND pattern_id=' . $pattern_id . ' AND origin="generated" AND deleted_by=0 AND fi18n.lang="' . $model_lang . '"' . "\n" .
                       '  AND revision=(SELECT MAX(revision) FROM ' . DB_TABLE_FILES . ', ' . DB_TABLE_FILES_I18N . "\n" .
                       '  WHERE id=parent_id AND model="' . self::$modelName . '" AND model_id=f.model_id AND origin="generated" AND deleted_by=0 AND lang="' . $model_lang . '")' . "\n" .
                       'GROUP BY model_id' . "\n" .
                       'ORDER BY model_id ASC';
        $generated_files = $db->getAssoc($query_files);

        $registry->set('get_old_vars', true, true);

        $file_ids = array();
        foreach ($finance_expenses_reasons as $finance_expenses_reason) {
            if ($db->HasFailedTrans()) {
                //something is wrong, do not continue at all
                break;
            }
            //$old_model[] = $finance_expenses_reason;
            if (!array_key_exists($finance_expenses_reason->get('id'), $generated_files) ||
                $generated_files[$finance_expenses_reason->get('id')]['added'] < $latest_pattern_modify_date ||
                (array_key_exists($finance_expenses_reason->get('id'), $generated_files) && !file_exists($generated_files[$finance_expenses_reason->get('id')]['path']))) {
                //registry needed for operations with model
                $finance_expenses_reason->unsanitize();

                //get the GT2 vars
                $finance_expenses_reason->getGT2Vars(false);

                $patterns_vars = $finance_expenses_reason->getPatternsVars();
                $finance_expenses_reason->extender = new Extender();
                $finance_expenses_reason->extender->model_lang = $model_lang;
                $finance_expenses_reason->extender->module = 'Finance';
                foreach ($patterns_vars as $key => $value) {
                    $finance_expenses_reason->extender->add($key, $value);
                }

                //generate file with selected pattern
                if ($file_id = $finance_expenses_reason->generatePDF()) {
                    //save history
                    require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                    Finance_Expenses_Reasons_History::saveData($registry,
                                                               array('action_type' => 'generate',
                                                                     'model' => $finance_expenses_reason,
                                                                     'pattern' => $pattern->get('id'),
                                                                     'generated_file' => $file_id));
                    $file_ids[$finance_expenses_reason->get('id')] = $file_id;
                }

                $finance_expenses_reason->sanitize();
            } else {
                $file_ids[$finance_expenses_reason->get('id')] = $generated_files[$finance_expenses_reason->get('id')]['id'];
            }

            //$temp_model = clone $finance_expenses_reason;
            //$new_models[] = $temp_model->sanitize();
            //unset($temp_model);
        }

        //set models for automations
        //$controller->old_model = $old_model;
        //$controller->new_models = $new_models;

        if ($file_ids) {
            $query_files = 'SELECT path' . "\n" .
                           '  FROM ' . DB_TABLE_FILES . "\n" .
                           '  WHERE id IN (' . implode(', ', $file_ids) . ')';
            $files = $db->getCol($query_files);

            //prepare the page properties
            if ($pattern->get('landscape')) {
                $page_orientation = 'L';
                $page_format_width = 841.82;
                $page_format_height = 598.63;
            } else {
                $page_orientation = 'P';
                $page_format_width = 598.63;
                $page_format_height = 841.82;
            }
            $page_format = array($page_format_width, $page_format_height);

            //merge files and send result file to browser
            $pdf = new Pdf_Merge($page_orientation, 'pt', $page_format);
            $pdf->setFiles($files);
            $pdf->concat();
            //remove any notices or warnings
            ob_clean();
            $pdf->Output_fdpf(sprintf('%s_%s_multiprint.pdf',
                                      General::strftime($registry['translater']->translate('date_short')),
                                      preg_replace('#\s#', '_', Transliterate::convert($type_name_plural))), 'D');

            //write lightweight history
            $current_user_id = $registry['currentUser']->get('id');
            $query_history = 'INSERT INTO ' . DB_TABLE_FINANCE_HISTORY . ' (model, model_id, h_date, action_type, user_id, data, lang) VALUES' . "\n";
            $history_records = array();
            foreach ($file_ids as $model_id => $file_id) {
                $data = serialize(array('pattern' => $pattern_id, 'generated_file' => $file_id));
                $history_records[] = sprintf('(\'%s\', %d, NOW(), \'multiprint\', %d, \'%s\', \'%s\')',
                                             self::$modelName, $model_id, $current_user_id, $data, $model_lang);
            }
            $query_history .= implode(",\n", $history_records);
            $db->Execute($query_history);

        } else {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * TEMPORARY METHOD
     * Update field `user_permissions` for all finance expenses reasons
     */
    public static function updateUserPermissions(&$registry) {
        $db = $registry['db'];
        $query = 'SELECT fer.id, fer.user_permissions, fda.assigned_to' . "\n" .
                 '  FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_ASSIGNMENTS . ' AS fda' . "\n" .
                 '  ON fer.id=fda.parent_id AND fda.model="Finance_Expenses_Reason"' . "\n" .
                 '  WHERE fda.assigned_to IS NOT NULL AND fda.assignments_type IN (0,1,2,3)' . "\n" .
                 '  ORDER BY fer.id';
        $records = $db->GetAll($query);
        $update_arr = array();
        foreach ($records as $record) {
            $update_arr[$record['id']][] = $record['assigned_to'];
        }
        foreach ($update_arr as $k => $record) {
            $update_arr[$k] = array_unique($record);
        }
        foreach ($update_arr as $k => $record) {
            $query = "UPDATE " . DB_TABLE_FINANCE_EXPENSES_REASONS .
                     " SET user_permissions = '," . implode(',', $record) . ",' WHERE id=$k";
            $db->Execute($query);
        }
    }

    /**
     * Creates incoming invoice from proforma or incoming invoice/proforma from expenses reason
     *
     * @param object $registry - the main registry
     * @param mixed $ids - array of IDs of parent documents or a single ID
     * @return bool - result of operation
     */
    public static function issueInvoice(&$registry, $ids = array()) {

        if (empty($ids)) {
            return false;
        }
        if (!is_array($ids)) {
            $ids = array($ids);
        }

        $request = &$registry['request'];
        $issue_date = $request->get('issue_date');
        if (!$issue_date) {
            $issue_date = General::strftime('%Y-%m-%d');
        }

        $db = &$registry['db'];

        $db->StartTrans();

        // get the models from the DB
        $filters = array();
        $filters['where'] = array('fer.id IN (' . implode(', ', $ids) . ')');
        $models = self::search($registry, $filters);
        $result = array();

        //now invoice can be created from ONE AND ONLY ONE parent document
        //TODO - create one invoice from more than one document
        foreach ($models as $model) {

            $invoice = clone $model;

            $invoice->unsetProperty('id', true);
            $invoice->unsetProperty('num', true);
            //distribution flag is set in the expense reason model
            $invoice->unsetProperty('distributed', true);
            $invoice->unsetProperty('allocated_status', true);
            if ($request->get('proforma')) {
                $invoice->set('type', PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE, true);
                $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N .
                         ' WHERE parent_id = ' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE . ' AND lang="' . $registry['lang'] . '"';
            } else {
                $invoice->set('type', PH_FINANCE_TYPE_EXPENSES_INVOICE, true);
                $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N .
                         ' WHERE parent_id = ' . PH_FINANCE_TYPE_EXPENSES_INVOICE . ' AND lang="' . $registry['lang'] . '"';
            }

            $invoice->set('type_name', $registry['db']->GetOne($query), true);
            $invoice->set('issue_date', $issue_date, true);
            if ($request->isRequested('date_of_payment')) {
                $invoice->set('date_of_payment', $request->get('date_of_payment'), true);
            }
            if ($request->isRequested('invoice_num')) {
                $invoice->set('invoice_num', $request->get('invoice_num'), true);
            }
            if ($request->get('admit_VAT_credit') && !$request->get('proforma')) {
                $invoice->set('admit_VAT_credit', $request->get('admit_VAT_credit'), true);
                if ($request->isRequested('accounting_month')) {
                    $invoice->set('accounting_month', $request->get('accounting_month'), true);
                }
                if ($request->isRequested('accounting_year')) {
                    $invoice->set('accounting_year', $request->get('accounting_year'), true);
                }
            } else {
                $invoice->set('admit_VAT_credit', false, true);
            }

            $invoice->set('link_to', $model->get('id'), true);
            $invoice->set('link_to_model_name', $model->modelName, true);
            // set name to an empty string (it will be set automatically before save using type name and parent name)
            $invoice->set('parent_name', $model->get('name'), true);
            $invoice->set('name', '', true);
            $invoice->set('status', 'finished', true);
            $invoice->set('substatus', '', true);
            // set default values for group and department
            $invoice->setGroup($invoice->get('group'));
            $invoice->setDepartment($invoice->get('department'));

            $model->getGT2Vars();
            $gt2 = $model->get('grouping_table_2');
            $invoice->getGT2Vars();

            $gt2_new = $invoice->get('grouping_table_2');
            $gt2_new['values'] = $gt2['values'];

            $invoice->set('grouping_table_2', $gt2_new, true);
            $invoice->set('table_values_are_set', true, true);

            if ($invoice->save()) {

                $filters = array('where' => array('fer.id = ' . $invoice->get('id'), 'fer.annulled_by = 0'));
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($registry, $filters);
                $registry->set('get_old_vars', true, true);
                $finance_expenses_reason->getGT2Vars();

                $new_id = $finance_expenses_reason->get('id');
                $finance_expenses_reason->set('id', $model->get('id'), true);

                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                $audit_parent = Finance_Expenses_Reasons_History::saveData($registry,
                    array('action_type' => $request->get('proforma') ? 'addproformainvoice' : 'addinvoice',
                          'new_model' => $finance_expenses_reason,
                          'old_model' => $model
                ));

                $finance_expenses_reason->set('id', $new_id, true);
                $old_model = new Finance_Expenses_Reason($registry);
                $old_model->set('type', $finance_expenses_reason->get('type'), true);
                $registry->set('get_old_vars', true, true);
                $old_model->getGT2Vars();
                $old_model->sanitize();
                $audit_parent = Finance_Expenses_Reasons_History::saveData($registry,
                    array('action_type' => 'add',
                          'new_model' => $finance_expenses_reason,
                          'old_model' => $old_model
                ));

                unset($finance_expenses_reason);
                unset($old_model);
            }

            $result = $invoice->get('id');
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        if (!$dbTransError) {
            return $result;
        } else {
            return false;
        }
    }

    /**
     * Updates currency rate field of specified models with specified value.
     * Method is used for saving conversion rate when adding incoming invoice
     * to multiple proformas in different currencies.
     *
     * @param object $registry - the main registry
     * @param mixed $ids - array of IDs of parent documents or a single ID
     * @param double $currency_rate - conversion rate between currencies of models
     * @return bool - result of operation
     */
    public static function updateCurrencyRate(&$registry, $ids = array(), $currency_rate = 1) {
        if (empty($ids) || empty($currency_rate)) {
            return false;
        }
        if (!is_array($ids)) {
            $ids = array($ids);
        }

        $db = &$registry['db'];

        $query = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                 'SET currency_rate = ' . $currency_rate . "\n" .
                 'WHERE id IN (' . implode(', ', $ids) . ')';
        $db->Execute($query);

        return !$db->HasFailedTrans();
    }

    /**
     * Function to check if there is an expenses document
     * of specified type, issued by the same customer and having the same number
     *
     * @param object $registry - the main registry
     * @param integer $id - id of current expense document
     * @param string $type - type of document
     * @param string $num - invoice_num of document
     * @return string - id of found document or empty string if no match found
     */
    public static function checkNum($registry, $id, $type, $num) {
        $query = "SELECT fer1.id FROM " . DB_TABLE_FINANCE_EXPENSES_REASONS . " AS fer\n" .
                 "JOIN " . DB_TABLE_FINANCE_EXPENSES_REASONS . " AS fer1\n" .
                 "    ON fer.customer = fer1.customer AND fer1.type = '$type' AND fer1.invoice_num = '$num'\n".
                 "WHERE fer.id = $id";
        return $registry['db']->GetOne($query);
    }


    /**
     * Get relations rows between specified expenses reason and records of specified model.
     * IMPORTANT: Relations between cloned reasons are not returned.
     *
     * @param int $id - id of expenses reason to get relations for
     * @param array $relations - relations records
     * @param string $model - model of related records to get
     * @return array - relations records
     */
    public static function getRelationsRows($id, &$relations, $model = '') {

        $db = $GLOBALS['registry']['db'];
        if (!$model) {
            $model = self::$modelName;
        }

        // do not search further when parent model is 'Finance_Expenses_Reason'
        // and type of current model is > 100
        $query = 'SELECT type FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                'WHERE id = ' . $id;
        if ($model == self::$modelName && $db->GetOne($query) > PH_FINANCE_TYPE_MAX) {
            return $relations;
        }

        $query = 'SELECT link_to, rows_links, changes ' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
            'WHERE parent_id=' . $id . ' AND parent_model_name="' . self::$modelName . '"' . "\n" .
            '  AND link_to_model_name="' . $model . '"';
        $records = $db->GetAssoc($query);

        $relations = array();
        foreach ($records as $id => $record) {
            // search further only when parent model is 'Finance_Expenses_Reason'
            if ($model == self::$modelName) {
                self::getRelationsRows($id, $relations);
            }
            $rows = preg_split('#\n|\r|\r\n#', $record['rows_links']);
            foreach ($rows as $row) {
                if (!preg_match('#\d+\s*=>\s*-?\d+#', $row)) continue;
                $row = preg_split('#\s*=>\s*#', $row, 2);
                if (isset($relations['links'][$row[1]])) {
                    $idx = $row[1];
                    $row[1] .= ' => ' . $relations['links'][$row[1]];
                    unset($relations['links'][$idx]);
                }
                $relations['links'][$row[0]] = $row[1];
            }

            $rows = preg_split('#\n|\r|\r\n#', $record['changes']);
            foreach ($rows as $row) {
                if (!preg_match('#\d+\s*=>\s*.+#', $row)) continue;
                $row = preg_split('#\s*=>\s*#', $row, 2);
                if (!isset($relations['changes'][$row[0]])) {
                    $relations['changes'][$row[0]] = array();
                }
                $relations['changes'][$row[0]][$id] = $row[1];
            }
        }

        return $relations;
    }
}

?>
