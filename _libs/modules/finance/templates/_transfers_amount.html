{** ALLOWED PARAMETERS:
 * prefix   - from or to
 *}

{capture assign='prefix_currency'}{$prefix}_currency{/capture}
{capture assign='prefix_rate'}{$prefix}_rate{/capture}
{capture assign='prefix_amount'}{$prefix}_amount{/capture}
<span id="{$prefix}_rate_data" style="margin-left: 5px;{if $finance_transfer->get($prefix_currency) eq $finance_transfer->get('currency')} display: none;{/if}">
  <input type="hidden" id="{$prefix}_currency" name="{$prefix}_currency" value="{$finance_transfer->get($prefix_currency)}" />
  <a id="error_{$prefix_rate}"></a>
  <label class="labelbox{if $messages->getErrors($prefix_rate)} error{/if}" for="{$prefix}_rate" style="padding: 0 5px;">{#finance_transfers_rate#|escape} <span id="{$prefix}_from_currency">{if $prefix eq 'from'}{$finance_transfer->get('from_currency')}{else}{$finance_transfer->get('currency')}{/if}</span>/<span id="{$prefix}_to_currency">{if $prefix eq 'from'}{$finance_transfer->get('currency')}{else}{$finance_transfer->get('to_currency')}{/if}</span>:</label>
  <input type="text" name="{$prefix}_rate" id="{$prefix}_rate" class="txtbox short hright" onkeypress="return changeKey(this, event, insertOnlyFloats);" onkeyup="calculateContainerAmount(this); removeClass(this, 'refreshed'); reacalculateCurrentContainerAmount('{$prefix}');" title="{#finance_transfers_rate#|escape}" value="{$finance_transfer->get($prefix_rate)}" />
  <label class="labelbox" for="{$prefix}_amount" style="padding: 0 5px;">{help label_content=#finance_transfers_amount# text_content=#help_finance_transfers_container_amount#}</label>
  <input type="text" name="{$prefix}_amount" id="{$prefix}_amount" class="txtbox short hright distinctive" readonly="readonly" title="{#finance_transfers_amount#|escape}" value="{$finance_transfer->get($prefix_amount)}" />
</span>
