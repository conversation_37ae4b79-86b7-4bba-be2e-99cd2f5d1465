<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;controller=companies&amp;{$action_param}={$action}&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="finance_company" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=companies" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#finance_companies_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.VAT_number.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.VAT_number.link}">{#finance_companies_VAT_number#|escape}</div></td>
          <td class="t_caption t_border {$sort.mol.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.mol.link}">{#finance_companies_mol#|escape}</div></td>
          <td class="t_caption t_border {$sort.country.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.country.link}">{#finance_companies_country#|escape}</div></td>
          <td class="t_caption t_border {$sort.city.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.city.link}">{#finance_companies_city#|escape}</div></td>
          <td class="t_caption t_border {$sort.post_code.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.post_code.link}">{#finance_companies_postal_code#|escape}</div></td>
          <td class="t_caption t_border {$sort.position.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.position.link}">{#finance_companies_position#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$companies item='company'}
      {strip}
      {capture assign='info'}
        <strong>{#finance_companies_name#|escape}:</strong> {$company->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$company->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$company->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$company->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$company->get('modified_by_name')|escape}<br />
        {if $company->isDeleted()}<strong>{#deleted#|escape}:</strong> {$company->get('deleted')|date_format:#date_mid#|escape}{if $company->get('deleted_by_name')} {#by#|escape} {$company->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$company->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $company->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if !$company->get('active')} t_inactive{/if}{if $company->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$company->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($company->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($company->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=companies&amp;{$action_param}=view&amp;view={$company->get('id')}">{$company->get('name')|escape}</a></td>
          <td class="t_border hright {$sort.VAT_number.isSorted}">{$company->get('VAT_number')|escape}</td>
          <td class="t_border {$sort.mol.isSorted}">{$company->get('mol')|escape}</td>
          <td class="t_border {$sort.country.isSorted}">{$company->get('country_name')|escape}</td>
          <td class="t_border {$sort.city.isSorted}">{$company->get('city')|escape}</td>
          <td class="t_border hright {$sort.post_code.isSorted}">{$company->get('post_code')|escape}</td>
          <td class="t_border hright {$sort.position.isSorted}">{$company->get('position')|escape}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$company}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="10"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
