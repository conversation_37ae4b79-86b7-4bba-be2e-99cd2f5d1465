  <div class="{if !empty($audit.gt2)} has_gt2{/if}">
    {include file=`$theme->templatesDir`_audit.html}
    {*<h1><img src="{$theme->imagesUrl}list.png" border="0" alt="{$audit_title|default:#audit_vars#|escape}" /> {$audit_title|default:#audit_vars#|escape}</h1>
    <h2>{$audit_legend|escape}</h2>
    <div class="audit clear">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_header t_table_border">
        <tr>
          <td colspan="4" class="t_caption3 strong legend">{#data#|escape}</td>
        </tr>
        <tr>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 20px;"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 150px;"><div class="t_caption_title">{#var_name#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 200px;"><div class="t_caption_title">{#var_value#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap" style="width: 200px;"><div class="t_caption_title">{#old_value#|escape}</div></td>
        </tr>
        {counter name='audit_counter' start=0 assign='audit_counter'}
        {foreach name='i' from=$audit.vars item='var'}
          <tr class="{if !empty($audit.modelName) && $audit.modelName == 'Finance_Warehouse'}{cycle values='t_odd2,t_even2' name='audit_vars'}{else}{cycle values='t_odd,t_even' name='audit_vars'}{/if} vtop">
            <td class="t_border hright">{counter name='audit_counter' print=true}</td>
            <td class="t_border">{$var.var_label|escape|default:'&nbsp;'}</td>
            <td class="legend t_border">{if $var.label}{$var.label|nl2br}{elseif is_array($var.field_value)}{include file=`$theme->templatesDir`_gt2_batch_view.html no_row=true val=$var.field_value idx=`$smarty.foreach.i.iteration`_new}{else}{$var.field_value|nl2br|default:'&nbsp;'}{/if}</td>
            <td class="legend">{if is_array($var.old_value)}{include file=`$theme->templatesDir`_gt2_batch_view.html no_row=true val=$var.old_value idx=`$smarty.foreach.i.iteration`_old}{else}{$var.old_value|nl2br|default:'&nbsp;'}{/if}</td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="4">{#no_changes_made#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="4"></td>
        </tr>
      </table>
    </div>*}
  </div>
  {if !empty($audit.gt2)}
    {include file=`$theme->templatesDir`_gt2_audit.html gt2_audit=$audit.gt2}
  {/if}