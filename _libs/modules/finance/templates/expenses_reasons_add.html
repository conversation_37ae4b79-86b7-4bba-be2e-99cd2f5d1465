<h1>{$title|escape}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

      {include file=`$theme->templatesDir`actions_box.html}

      <form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post" onsubmit="return gt2calc('submit_gt2');">
      <input type="hidden" name="model_lang" id="model_lang" value="{$finance_expenses_reason->get('model_lang')|default:$lang}" />
      <input type="hidden" name="type" id="type" value="{$finance_expenses_reason->get('type')}" />
      <input type="hidden" name="original_company" id="original_company" value="{$finance_expenses_reason->get('company')}" />
      {if $finance_expenses_reason->get('transform_params')}
        <input type="hidden" name="transform_params" value="{$finance_expenses_reason->get('transform_params')|escape}" />
        <input type="hidden" name="after_action" value="view" />
      {/if}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td class="t_footer"></td>
        </tr>
        <tr>
          <td>
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            {foreach from=$finance_expenses_reason->get('layouts_details') key='lkey' item='layout'}

              <tr{if !$layout.view || !$layout.visible || ($lkey eq 'accounting_period' && $finance_expenses_reason->isDefined('admit_VAT_credit') && !$finance_expenses_reason->get('admit_VAT_credit'))} style="display: none;"{/if}{if $lkey eq 'accounting_period' && $layout.view && $layout.visible} class="accounting_period_row"{/if}>
                <td colspan="3" class="t_caption3 pointer">
                  <div class="floatr index_arrow_anchor">
                    <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                  </div>
                  <div class="layout_switch" onclick="toggleViewLayouts(this)" id="finance_expenses_reason_{$layout.keyword}_switch">
                    <a name="finance_expenses_reason_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                  </div>
                </td>
              </tr>

              {if $lkey eq 'type'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_type"><label for="type"{if $messages->getErrors('type')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{#required#}</td>
                <td>
                  {$finance_expenses_reason->get('type_name')|escape}
                </td>
              </tr>
              {elseif $lkey eq 'name'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $layout.edit}
                    <input type="text" class="txtbox" name="name" id="name" value="{$finance_expenses_reason->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                  {else}
                    {mb_truncate_overlib text=$finance_expenses_reason->get('name')|escape|default:"&nbsp;"}
                    <input type="hidden" name="name" id="name" value="{$finance_expenses_reason->get('name')|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'customer'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_customer"><label for="customer"{if $messages->getErrors('customer')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{#required#}</td>
                <td>
                  {capture assign='ac_readonly'}{if $layout.edit && !$multiaddinvoice}0{else}1{/if}{/capture}
                  {include file=`$theme->templatesDir`input_autocompleter.html
                           name='customer'
                           autocomplete_type='customers'
                           autocomplete_var_type='basic'
                           autocomplete_buttons='add search clear'
                           value=$finance_expenses_reason->get('customer')
                           value_code=$finance_expenses_reason->get('customer_code')
                           value_name=$finance_expenses_reason->get('customer_name')
                           readonly=$ac_readonly
                           width=266
                           standalone=true
                           label=$layout.name
                           help=$layout.description
                  }
                </td>
              </tr>
              {elseif $lkey eq 'trademark'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_trademark"><label for="trademark"{if $messages->getErrors('trademark')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {capture assign='ac_readonly'}{if $layout.edit && !$multiaddinvoice}0{else}1{/if}{/capture}
                  {include file=`$theme->templatesDir`input_autocompleter.html
                           name='trademark'
                           autocomplete_type='nomenclatures'
                           autocomplete_var_type='basic'
                           autocomplete_buttons='search clear'
                           value=$finance_expenses_reason->get('trademark')
                           value_name=$finance_expenses_reason->get('trademark_name')
                           readonly=$ac_readonly
                           width=244
                           standalone=true
                           label=$layout.name
                           help=$layout.description
                  }
                </td>
              </tr>
              {elseif $lkey eq 'project'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_project"><label for="project"{if $messages->getErrors('project')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {capture assign='ac_readonly'}{if $layout.edit}0{else}1{/if}{/capture}
                  {include file=`$theme->templatesDir`input_autocompleter.html
                           name='project'
                           autocomplete_type='projects'
                           autocomplete_var_type='basic'
                           autocomplete_buttons='add search clear'
                           value=$finance_expenses_reason->get('project')
                           value_code=$finance_expenses_reason->get('project_code')
                           value_name=$finance_expenses_reason->get('project_name')
                           readonly=$ac_readonly
                           width=266
                           standalone=true
                           label=$layout.name
                           help=$layout.description
                  }
                  {if !$ac_readonly}
                    <span class="help" {help label='expenses_reasons_phase' popup_only='1'}>&nbsp;</span>
                    {include file='input_dropdown.html'
                             standalone=true
                             name='phase'
                             options=$phases
                             no_select_records_label=$smarty.config.project_phase
                             first_option_label=$smarty.config.project_phase
                             width=100
                             value=$finance_expenses_reason->get('phase')
                             label=#finance_expenses_reasons_phase#
                    }
                  {else}
                    {if $finance_expenses_reason->get('phase')} <span class="labelbox">{help label_content=#finance_expenses_reasons_phase#}</span> {$finance_expenses_reason->get('phase_name')|escape}{/if}
                    <input type="hidden" name="phase" id="phase" value="{$finance_expenses_reason->get('phase')|default:0}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'invoice_num'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_invoice_num"><label for="invoice_num"{if $messages->getErrors('invoice_num')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{if $type->get('mandatory_num')}{#required#}{else}&nbsp;{/if}</td>
                <td>
                  {if $layout.edit}
                    {include file='input_text.html'
                             standalone=true
                             name='invoice_num'
                             index=0
                             value=$finance_expenses_reason->get('invoice_num')
                             required=0
                             text_align='left'
                             readonly=0
                             hidden=0
                             width=200
                             label=$layout.name
                             help=$layout.description
                    }
                  {else}
                    {$finance_expenses_reason->get('invoice_num')|escape}
                    <input type="hidden" name="invoice_num" id="invoice_num" value="{$finance_expenses_reason->get('invoice_num')|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'company_data'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_company_data"><label for="company_data"{if $messages->getErrors('company_data')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{#required#}</td>
                <td>
                  {if $layout.edit}
                    {capture assign='company_data_value'}{if $finance_expenses_reason->get('company_data') && $finance_expenses_reason->get('company_data') ne '0_0_0_0'}{$finance_expenses_reason->get('company_data')}{/if}{/capture}
                    {include file='input_dropdown.html'
                             standalone=true
                             name='company_data'
                             index=0
                             optgroups=$companies_data
                             value=$company_data_value
                             required=1
                             really_required=1
                             readonly=0
                             hidden=0
                             width=200
                             label=$layout.name
                             onchange="updateAvailableQuantities(this.value);"
                             help=$layout.description
                    }
                  {else}
                    {if $finance_expenses_reason->get('company_data')}
                      {foreach from=$companies_data key='company_name' item='company_data'}
                        {foreach from=$companies_data.$company_name item='data'}
                          {if $data.option_value eq $finance_expenses_reason->get('company_data')}<span class="strong">{$company_name|escape}</span> {$data.label|escape}{/if}
                        {/foreach}
                      {/foreach}
                    {/if}
                    <input type="hidden" name="company_data" id="company_data" value="{$finance_expenses_reason->get('company_data')}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'issue_date'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_issue_date"><label for="issue_date"{if $messages->getErrors('issue_date')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{#required#}</td>
                <td>
                  {if $layout.edit}
                    {include file="input_date.html"
                             standalone=true
                             name='issue_date'
                             value=$finance_expenses_reason->get('issue_date')
                             width=200
                             show_calendar_icon=true
                             label=$layout.name
                             help=$layout.description
                             onchange=recalculatePaymentDate();
                    }
                  {else}
                    {$finance_expenses_reason->get('issue_date')|escape|date_format:#date_short#}
                    <input type="hidden" name="issue_date" id="issue_date" value="{$finance_expenses_reason->get('issue_date')|escape}" />
                  {/if}
                  {if $multiaddinvoice && $finance_expenses_reason->get('min_issue_date')}
                    <input type="hidden" name="min_issue_date" id="min_issue_date" value="{$finance_expenses_reason->get('min_issue_date')|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'fiscal_event_date' && in_array($finance_expenses_reason->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_fiscal_event_date"><label for="fiscal_event_date"{if $messages->getErrors('fiscal_event_date')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $layout.edit}
                    {include file="input_date.html"
                             standalone=true
                             name='fiscal_event_date'
                             value=$finance_expenses_reason->get('fiscal_event_date')
                             width=200
                             show_calendar_icon=true
                             label=$layout.name
                             help=$layout.description
                    }
                  {else}
                    {$finance_expenses_reason->get('fiscal_event_date')|escape|date_format:#date_short#}
                    <input type="hidden" name="fiscal_event_date" id="fiscal_event_date" value="{$finance_expenses_reason->get('fiscal_event_date')|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'date_of_payment'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_date_of_payment"><label for="date_of_payment"{if $messages->getErrors('date_of_payment')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                {if $finance_expenses_reason->get('type') le $smarty.const.PH_FINANCE_TYPE_MAX && $finance_expenses_reason->get('type') ne $smarty.const.PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON}
                  <td class="required">{#required#}</td>
                {else}
                  <td class="unrequired">&nbsp;</td>
                {/if}
                <td>
                  {if $layout.edit}
                    {capture assign=dop_onchange}if ($('dop_changed')) $('dop_changed').value = 1;{/capture}
                    {include file="input_date.html"
                             standalone=true
                             name='date_of_payment'
                             value=$finance_expenses_reason->get('date_of_payment')
                             width=200
                             show_calendar_icon=true
                             label=$layout.name
                             help=$layout.description
                             onchange=$dop_onchange
                    }
                    <input type="hidden" name="dop_changed" id="dop_changed" value="" />
                  {else}
                    {$finance_expenses_reason->get('date_of_payment')|escape|date_format:#date_short#}
                    <input type="hidden" name="date_of_payment" id="date_of_payment" value="{$finance_expenses_reason->get('date_of_payment')|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'admit_VAT_credit' && in_array($finance_expenses_reason->get('type'), array($smarty.const.PH_FINANCE_TYPE_EXPENSES_INVOICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE))}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_admit_VAT_credit"><label for="admit_VAT_credit"{if $messages->getErrors('admit_VAT_credit')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{#required#}</td>
                <td>
                  {if $layout.edit}
                    <input type="radio" name="admit_VAT_credit" id="admit_VAT_credit_0" onclick="var ap_rows = $$('.accounting_period_row'); for (var i = 0; i < ap_rows.length; i++) ap_rows[i].style.display = this.checked ? 'none' : '';" value="0"{if $finance_expenses_reason->isDefined('admit_VAT_credit') && !$finance_expenses_reason->get('admit_VAT_credit')} checked="checked"{/if} />
                    <label for="admit_VAT_credit_0">{#no#}</label>
                    <input type="radio" name="admit_VAT_credit" id="admit_VAT_credit_1" onclick="var ap_rows = $$('.accounting_period_row'); for (var i = 0; i < ap_rows.length; i++) ap_rows[i].style.display = this.checked ? '' : 'none';" value="1"{if !$finance_expenses_reason->isDefined('admit_VAT_credit') || $finance_expenses_reason->get('admit_VAT_credit')} checked="checked"{/if} />
                    <label for="admit_VAT_credit_1">{#yes#}</label>
                  {else}
                    {if $finance_expenses_reason->isDefined('admit_VAT_credit') && !$finance_expenses_reason->get('admit_VAT_credit')}{#no#}{else}{#yes#}{/if}
                    <input type="hidden" name="admit_VAT_credit" id="admit_VAT_credit" value="{if $finance_expenses_reason->isDefined('admit_VAT_credit') && !$finance_expenses_reason->get('admit_VAT_credit')}0{else}1{/if}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'accounting_period' && in_array($finance_expenses_reason->get('type'), array($smarty.const.PH_FINANCE_TYPE_EXPENSES_INVOICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE))}
              <tr id="finance_expenses_reason_{$layout.keyword}" {if $finance_expenses_reason->isDefined('admit_VAT_credit') && !$finance_expenses_reason->get('admit_VAT_credit') || ($layout.visible && $layout.cookie eq 'off') || !$layout.view}style="display: none;"{/if}{if $layout.view} class="accounting_period_row"{/if}>
                <td class="labelbox"><a name="error_accounting_period"><label for="accounting_period"{if $messages->getErrors('accounting_period')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="required">{#required#}</td>
                <td>
                  {if $finance_expenses_reason->get('accounting_month')}
                    {assign var=accounting_month value=$finance_expenses_reason->get('accounting_month')}
                  {else}
                    {assign var=accounting_month value=$smarty.now|date_format:'%m'}
                  {/if}
                  {if $finance_expenses_reason->get('accounting_year')}
                    {assign var=accounting_year value=$finance_expenses_reason->get('accounting_year')}
                  {else}
                    {assign var=accounting_year value=$smarty.now|date_format:'%Y'}
                  {/if}
                  {if $layout.edit}
                    <select name="accounting_month" id="accounting_month" class="selbox short" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#month#|escape}">
                    {section start=1 loop=13 name=month}
                      <option value="{$smarty.section.month.index}"{if $accounting_month eq $smarty.section.month.index} selected="selected"{/if}>{$smarty.section.month.index}</option>
                    {/section}
                    </select>
                    {math assign=year_start equation=x-y x=$smarty.now|date_format:'%Y' y=1}
                    {math assign=year_end equation=x+y x=$smarty.now|date_format:'%Y' y=1}
                    <select name="accounting_year" id="accounting_year" class="selbox short" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#year#|escape}">
                    {section start=$year_start loop=$year_end name=year}
                      <option value="{$smarty.section.year.index}"{if $accounting_year eq $smarty.section.year.index} selected="selected"{/if}>{$smarty.section.year.index}</option>
                    {/section}
                    </select>
                  {else}
                    {$accounting_month}.{$accounting_year}
                    <input type="hidden" name="accounting_month" id="accounting_month" value="{$accounting_month}" />
                    <input type="hidden" name="accounting_year" id="accounting_year" value="{$accounting_year}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'allocated_status'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_{$layout.keyword}"><label for="{$layout.keyword}"{if $messages->getErrors("`$layout.keyword`")} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <input type="hidden" name="allocated_status" id="allocated_status_hidden" value="{if $layout.edit && $finance_expenses_reason->checkUpdateAllocatedStatus()}disabled{else}{$finance_expenses_reason->get('allocated_status')|default:'disabled'|escape}{/if}" />
                  {if $finance_expenses_reason->get('allocated_status') eq 'allocated'}
                    {#finance_expenses_reasons_allocated_status_allocated#|escape}
                  {else}
                    <input type="checkbox" name="allocated_status" id="allocated_status" value="{if $layout.edit}enabled{else}{$finance_expenses_reason->get('allocated_status')|escape}{/if}"{if $finance_expenses_reason->get('allocated_status') eq 'enabled'} checked="checked"{/if}{if !($layout.edit && $finance_expenses_reason->checkUpdateAllocatedStatus())} disabled="disabled"{/if} title="{$layout.name|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'description'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $layout.edit}
                    <textarea class="areabox" name="description" id="description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$finance_expenses_reason->get('description')|escape}</textarea>
                  {else}
                    {$finance_expenses_reason->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                    <input type="hidden" name="description" id="description" value="{$finance_expenses_reason->get('description')|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'department'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_department"><label for="department"{if $messages->getErrors('department')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $layout.edit}
                    <select class="selbox{if !$finance_expenses_reason->get('department')} undefined{/if}" name="department" id="department" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);" title="{$layout.name|escape}">
                      <option value="" class="undefined">[{#please_select#|escape}]</option>
                      {foreach from=$departments item='item'}
                        {if (!$item->isDeleted() && $item->isActivated()) || $item->get('id') eq $finance_expenses_reason->get('department')}
                        <option value="{$item->get('id')}"{if $item->get('id') eq $finance_expenses_reason->get('department')} selected="selected"{/if}{if $item->isDeleted() || !$item->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{if $item->isDeleted() || !$item->isActivated()}*&nbsp;{/if}{$item->get('name')|indent:$item->get('level'):"-"}</option>
                        {/if}
                      {/foreach}
                    </select>
                  {else}
                    {foreach from=$departments item='item'}
                      {if $item->get('id') eq $finance_expenses_reason->get('department')}
                        {$item->get('name')|indent:$item->get('level'):"-"}
                        <input type="hidden" name="department" id="department" value="{$item->get('id')}" />
                      {/if}
                    {/foreach}
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'employee'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_employee1"><label for="employee1"{if $messages->getErrors('employee1')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $layout.edit}
                    {include file=`$theme->templatesDir`input_autocompleter.html
                             name='employee1'
                             autocomplete_type='customers'
                             stop_customer_details=1
                             autocomplete_var_type='basic'
                             autocomplete_buttons='search clear'
                             value=$finance_expenses_reason->get('employee1')
                             value_name=$finance_expenses_reason->get('employee1_name')
                             filters_array=$autocomplete_employee_filters
                             width=244
                             standalone=true
                             label=$layout.name
                             help=$layout.description
                    }
                  {else}
                    {$finance_expenses_reason->get('employee1_name')|escape}
                    <input type="hidden" name="employee1" id="employee1" value="{$finance_expenses_reason->get('employee1')|escape|default:0}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'fin_field_1'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_fin_field_1"><label for="fin_field_1"{if $messages->getErrors('fin_field_1')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $layout.edit}
                    {include file=`$theme->templatesDir`input_text.html
                             name='fin_field_1'
                             value=$finance_expenses_reason->get('fin_field_1')
                             standalone=true
                             width=200
                             label=$layout.name
                             help=$layout.description
                    }
                  {else}
                    {$finance_expenses_reason->get('fin_field_1')|escape|default:"&nbsp;"}
                    <input type="hidden" name="fin_field_1" id="fin_field_1" value="{$finance_expenses_reason->get('fin_field_1')|escape}" />
                  {/if}
                </td>
              </tr>
              {elseif $lkey eq 'fin_field_2'}
              <tr id="finance_expenses_reason_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
                <td class="labelbox"><a name="error_fin_field_2"><label for="fin_field_2"{if $messages->getErrors('fin_field_2')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $layout.edit}
                    {include file=`$theme->templatesDir`input_textarea.html
                             name='fin_field_2'
                             value=$finance_expenses_reason->get('fin_field_2')
                             standalone=true
                             width=200
                             label=$layout.name
                             help=$layout.description
                    }
                  {else}
                    {$finance_expenses_reason->get('fin_field_2')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                    <input type="hidden" name="fin_field_2" id="fin_field_2" value="{$finance_expenses_reason->get('fin_field_2')|escape}" />
                  {/if}
                </td>
              </tr>
              {/if}
            {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td>
            <table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
              <tr>
                <td colspan="3" style="padding: 5px;">
                  {include file=`$theme->templatesDir`_gt2_edit.html
                           model=$finance_expenses_reason
                  }
                </td>
              </tr>
              <tr>
                <td colspan="3" style="padding: 15px;">
                  {strip}
                  <input type="hidden" name="finance_after_action" id="finance_after_action" value="" />
                  <input type="hidden" name="submit_gt2" id="submit_gt2" />
                  <input type="hidden" name="payment_date" id="payment_date" value="{$finance_expenses_reason->get('payment_date')}" />
                  <input type="hidden" name="payment_container" id="payment_container" value="{$finance_expenses_reason->get('payment_container')}" />
                  <input type="hidden" name="payment_container_rate" id="payment_container_rate" value="{$finance_expenses_reason->get('container_rate')}" />
                  <input type="hidden" name="payment_container_amount" id="payment_container_amount" value="{$finance_expenses_reason->get('container_amount')}" />
                  <input type="hidden" name="payment_container_currency" id="payment_container_currency" value="{$finance_expenses_reason->get('container_currency')}" />
                  <input type="hidden" name="payment_reason" id="payment_reason" value="{$finance_expenses_reason->get('payment_reason')}" />
                  {foreach from=$after_action_options item='fin_action' name='ai' key='ak'}
                    <button type="submit" name="saveButton1" id="submit_gt2_{$ak}" class="button" onclick="{if $fin_action.option_value eq 'payment'}completeReasonsPaymentData(this, ''); return false;{else}$('finance_after_action').value='{$fin_action.option_value}';{/if}" title="{$fin_action.description|escape}">{$fin_action.label|escape}</button>
                  {/foreach}
                  {include file=`$theme->templatesDir`cancel_button.html}
                  {/strip}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      {include file=`$theme->templatesDir`help_box.html}
      {include file=`$theme->templatesDir`system_settings_box.html object=$finance_expenses_reason}
      {include file=`$theme->templatesDir`after_actions_box.html}
      </form>
      </div>
    </td>
  </tr>
</table>