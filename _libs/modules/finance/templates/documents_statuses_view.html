<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<input type="hidden" name="id" id="id" value="{$finance_documents_status->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$finance_documents_status->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='documents_statuses_doc_type'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{$type_name|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_statuses_status'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{$status_name|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_statuses_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$finance_documents_status->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_statuses_sequence'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">{$finance_documents_status->get('sequence')|escape}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_statuses_requires_comment'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{capture assign='current_requires_comment_label'}required_statuses_option_{$finance_documents_status->get('requires_comment')}{/capture}
            {$smarty.config.$current_requires_comment_label|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_statuses_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">{$finance_documents_status->get('description')|escape|mb_wordwrap|url2href|default:"&nbsp;"}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_statuses_icon'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $finance_documents_status->get('icon_name')}
              <img src="{$smarty.const.PH_FINANCE_DOCUMENTS_STATUSES_URL}{$finance_documents_status->get('icon_name')}" alt="" />
            {/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$finance_documents_status}
</div>
