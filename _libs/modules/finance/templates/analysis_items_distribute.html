<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="finance_analysis_items" id="finance_analysis_items" action="{$submitLink}" method="post" onsubmit="if ( !checkItemElementsPercentage()){ldelim}alert('{#error_total_elements_percentage#}');return false;{rdelim} else return true;">
<input type="hidden" name="model_lang" id="model_lang" value="{$analysis_item->get('model_lang')|default:$lang}" />
<input type="hidden" name="type" id="type" value="{$analysis_item->get('type')}" />
<input type="hidden" name="id" id="id" value="{$analysis_item->get('id')}" />
<input type="hidden" name="name" id="name" value="{$analysis_item->get('name')}" />
<input type="hidden" name="elements_title" id="elements_title" value="{$analysis_item->get('elements_title')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='analysis_items_name'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$analysis_item->get('name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='analysis_items_elements'}</td>
          <td colspan="2">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3" class="nopadding t_v_border">
            <table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
              <tr>
                <td colspan="3" class="hright t_caption3_title">
                  {#finance_analysis_items_distributed_percentage#|escape}: <span id="distributed_percentage" class="{if $distributed_percentage ne 100}red{else}green{/if}">{$distributed_percentage}</span> % {#finance_analysis_items_remaining_percentage#|escape}: <span id="remaining_percentage" class="{if $distributed_percentage ne 100}red{else}green{/if}">{$remaining_percentage}</span> %
                </td>
              </tr>
              <tr>
                <td class="t_caption3 t_border" width="15"><div class="t_caption3_title">{#num#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{$analysis_item->get('elements_title')|escape}</div></td>
                <td class="t_caption3" width="80"><div class="t_caption3_title">{#finance_analysis_items_percentage#|escape}</div></td>
              </tr>
              {array assign='js_methods' eval='array(\'onkeyup\' => \'recalculateItemElementsPercentage()\')'}
              {assign var='elements' value=$analysis_item->get('elements')}
              {assign var='elements_names' value=$analysis_item->get('elements_names')}
              {assign var='percentage' value=$analysis_item->get('percentage')}
              {counter start=0 name='item_counter' print=false}
              {foreach name='i' from=$elements item='element' key='k'}
              <tr class="{cycle values='t_odd,t_even'} vtop">
                <td class="t_border hright">{counter name='item_counter' print=true}</td>
                <td class="t_border">
                  {$elements_names.$k|escape}
                  {include file='input_hidden.html'
                          standalone=true
                          name='elements_names'
                          index=$smarty.foreach.i.iteration
                          value=$elements_names.$k}
                  {include file='input_hidden.html'
                          standalone=true
                          name='elements'
                          index=$smarty.foreach.i.iteration
                          value=$element}
                </td>
                <td>
                  {include file='input_text.html'
                          standalone=true
                          name='percentage'
                          custom_class='percentage'
                          index=$smarty.foreach.i.iteration
                          value=$percentage.$k
                          label=$smarty.config.finance_analysis_items_percentage
                          restrict='insertOnlyFloats'
                          js_methods=$js_methods
                          text_align='right'}
                </td>
              </tr>
              {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#distribute#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{*include file=`$theme->templatesDir`system_settings_box.html object=$analysis_item exclude='groups,is_portal'*}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
