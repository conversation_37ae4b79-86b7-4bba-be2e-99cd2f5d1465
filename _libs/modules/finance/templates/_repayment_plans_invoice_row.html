  <tr id="income_{$ref.id}">
    <td>
      {if $hide_del}
        {$hide_del}
      {else}
      <img src="{$theme->imagesUrl}small/delete.png" alt="{#delete#|escape}" title="{#delete#|escape}" onclick="return confirmAction('delete_row', function(el) {ldelim}recalculateUnpaid('income_{$ref.id}','referers');{rdelim}, this);" class="pointer" />
      {/if}
    </td>
    <td>
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view={$ref.id}">{$ref.num}</a>
      <input type="hidden" name="referers[]" id="ref{$ref.id}" value="{$ref.id}" />
    </td>
    <td>
    {if $ref.type eq $smarty.const.PH_FINANCE_TYPE_INVOICE}
      {#finance_invoice#}
    {else}
      {#finance_incomes_reason#}
    {/if}
    </td>
    <td>
      {$ref.name}
    </td>
    <td>
    {if $ref.issue_date ne '0000-00-00'}
      {$ref.issue_date|date_format:#date_short#}
    {else}
      -
    {/if}
    </td>
    <td>
    {if $ref.date_of_payment ne '0000-00-00'}
      {$ref.date_of_payment|date_format:#date_short#}
    {else}
      -
    {/if}
    </td>
    <td class="hright">
      {$currencies[$ref.currency].prefix} {$ref.total_with_vat|string_format:"%.2f"} {$currencies[$ref.currency].suffix}
    </td>
    <td class="hright">
      {$currencies[$ref.currency].prefix} {$ref.unpaid_amount|string_format:"%.2f"} {$currencies[$ref.currency].suffix}
      <input type="hidden" name="unpaid[]" value="{$ref.unpaid_amount}" />
    </td>
  </tr>
