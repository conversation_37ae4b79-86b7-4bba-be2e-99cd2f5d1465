    <table border="1" cellpadding="3" cellspacing="0">
      <tr>
        <th nowrap="nowrap">{#num#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.name|default:#finance_expenses_reasons_name#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.customer|default:#finance_expenses_reasons_customer#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.type|default:#finance_type#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.total|default:#gt2_total#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.total_with_vat|default:#gt2_total_with_vat#|escape}</th>
        <th nowrap="nowrap">{#finance_expenses_reasons_tags#|escape}</th>
      </tr>
    {counter start=0 name='item_counter' print=false}
    {foreach name='i' from=$finance_expenses_reasons item='expenses_reason'}
      <tr>
        <td>{counter name='item_counter' print=true}</td>
        <td>{$expenses_reason->get('name')|escape}</td>
        <td>{$expenses_reason->get('customer_name')|escape|default:"&nbsp;"}</td>
        <td>{$expenses_reason->get('type_name')|escape}</td>
        <td align="right">{$expenses_reason->get('total')|escape} {$expenses_reason->get('currency')|escape}</td>
        <td align="right">{$expenses_reason->get('total_with_vat')|escape} {$expenses_reason->get('currency')|escape}</td>
        <td>
          {if $expenses_reason->get('model_tags')|@count gt 0 && $expenses_reason->checkPermissions('tags_view')}
            {foreach from=$expenses_reason->get('model_tags') item='tag' name='ti'}
              {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
            {/foreach}
          {else}
            &nbsp;
          {/if}
        </td>
      </tr>
    {foreachelse}
      <tr>
        <td colspan="7">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    </table>
