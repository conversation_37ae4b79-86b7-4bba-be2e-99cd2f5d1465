<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;controller=warehouses_documents&amp;warehouses_documents={$action}&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="finance_warehouses_documents" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=warehouses_documents" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#finance_warehouses_documents_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{$basic_vars_labels.customer|default:#finance_warehouses_documents_customer#|escape}</div></td>
          <td class="t_caption t_border {$sort.warehouse.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.warehouse.link}">{$basic_vars_labels.warehouse|default:#finance_warehouses_documents_warehouse#|escape}</div></td>
          <td class="t_caption t_border {$sort.total.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.total.link}">{$basic_vars_labels.total|default:#finance_warehouses_documents_total#|escape}</div></td>
          <td class="t_caption t_border {$sort.total_with_vat.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.total_with_vat.link}">{$basic_vars_labels.total_with_vat|default:#finance_warehouses_documents_total_with_vat#|escape}</div></td>
          <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#finance_warehouses_documents_status#|escape}</div></td>
          <td class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.tags.link}">{#finance_warehouses_documents_tags#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$finance_warehouses_documents item='warehouses_document'}
      {strip}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#finance_warehouses_documents_name#|escape}:</strong> {$warehouses_document->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$warehouses_document->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$warehouses_document->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$warehouses_document->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$warehouses_document->get('modified_by_name')|escape}<br />

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$warehouses_document->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $warehouses_document->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='status_label_type'}finance_documents_status_{$warehouses_document->get('status')}_{$warehouses_document->get('type')}{/capture}
      {capture assign='status_label_type'}{$smarty.config.$status_label_type}{/capture}
      {capture assign='warehouses_document_status'}
        {if $status_label_type}
          {$status_label_type}
        {elseif $warehouses_document->get('status') eq 'opened'}
          {#help_finance_documents_status_opened#}
        {elseif $warehouses_document->get('status') eq 'locked'}
          {#help_finance_documents_status_locked#}
        {elseif $warehouses_document->get('status') eq 'finished'}
          {#help_finance_documents_status_finished#}
        {/if}
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if $warehouses_document->get('annulled_by')} t_strike{/if}{if !$warehouses_document->get('active')} t_inactive{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});"
                   type="checkbox"
                   name='items[]'
                   value="{$warehouses_document->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($warehouses_document->get('id'), $selected_items.ids) ||
                       (@$selected_items.select_all eq 1 && @!in_array($warehouses_document->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright" nowrap="nowrap">
            {if $warehouses_document->get('files_count')}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=warehouses_documents&amp;warehouses_documents=attachments&amp;attachments={$warehouses_document->get('id')}">
              <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                   onmouseover="showFiles(this, '{$module}', '{$controller}', {$warehouses_document->get('id')})"
                   onmouseout="mclosetime()" />
            </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.name.isSorted}">
            {if $warehouses_document->get('type') == $smarty.const.PH_FINANCE_TYPE_HANDOVER}
              {capture assign='direction_title'}finance_warehouses_documents_direction_{$warehouses_document->get('direction')}{/capture}
              <img src="{$theme->imagesUrl}warehouses_{if $warehouses_document->get('direction') eq 'incoming'}in{else}out{/if}.png" alt="{$smarty.config.$direction_title|escape}" title="{$smarty.config.$direction_title|escape}" />
            {elseif $warehouses_document->get('type') == $smarty.const.PH_FINANCE_TYPE_COMMODITIES_RESERVATION}
              <img src="{$theme->imagesUrl}commodities_reservation.png" alt="" />
            {elseif $warehouses_document->get('type') == $smarty.const.PH_FINANCE_TYPE_COMMODITIES_RELEASE}
              <img src="{$theme->imagesUrl}commodities_release.png" alt="" />
            {/if}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=warehouses_documents&amp;{$action_param}=view&amp;view={$warehouses_document->get('id')}">{$warehouses_document->get('name')|default:$warehouses_document->get('type_name')|escape}</a>
          </td>
          <td class="t_border {$sort.customer.isSorted}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$warehouses_document->get('customer')}" title="{#view#|escape}: {$warehouses_document->get('customer_name')|escape}">{$warehouses_document->get('customer_name')|escape|default:"&nbsp;"}</a>
          </td>
          <td class="t_border {$sort.warehouse.isSorted}">{$warehouses_document->get('warehouse_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.total.isSorted}">{$warehouses_document->get('total')|escape}</td>
          <td class="t_border {$sort.total_with_vat.isSorted}">{$warehouses_document->get('total_with_vat')|escape}</td>
          <td class="t_border {$sort.status.isSorted}">
            {capture assign='popup_and_onclick'}{popup text=$warehouses_document_status|escape caption=#help_finance_documents_status#|escape width=250}{/capture}
            {if $warehouses_document->get('status') eq 'opened'}
              <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $warehouses_document->get('status') eq 'locked'}
              <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $warehouses_document->get('status') eq 'finished'}
              <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            {capture assign='status_param'}finance_documents_status_{$warehouses_document->get('status')}{/capture}
            <span {$popup_and_onclick}>{$status_label_type|default:$smarty.config.$status_param}</span>
          </td>
          <td class="t_border {$sort.tags.isSorted}" {if $warehouses_document->getModelTags() && $warehouses_document->get('available_tags_count') gt 0 && $warehouses_document->checkPermissions('tags_view') && $warehouses_document->checkPermissions('tags_edit')} onclick="changeTags({$warehouses_document->get('id')}, 'finance', 'warehouses_documents')" style="cursor: pointer;" title="{#tags_change#}"{/if}>
            {if $warehouses_document->get('model_tags')|@count gt 0 && $warehouses_document->checkPermissions('tags_view')}
              {foreach from=$warehouses_document->get('model_tags') item='tag' name='ti'}
                <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$warehouses_document}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="10"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html
               exclude='multiedit,delete,restore,activate,deactivate'
               tags=$tags
               include='tags,multiprint'
               session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
