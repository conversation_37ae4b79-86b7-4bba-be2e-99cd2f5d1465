        {counter start=0 print=false}
          <table cellspacing="0" cellpadding="0" border="0" class="t_table">
            <tr>
              <td class="t_caption3 t_border" nowrap="nowrap" width="10"><div class="t_caption3_title">{#num#}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_to_distribute'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_amount'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_distributed_amount'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_distributed_amount_from_document'}</div></td>
              <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_remaining_distributed_amount'}</div></td>
              <td class="t_caption3" nowrap="nowrap"><div class="t_caption3_title">{help label='payments_distribute'}</div></td>
            </tr>
            {capture assign='paid_var'}paid_amount_{$finance_expenses_reason->modelName}_{$finance_expenses_reason->get('id')}{/capture}
            {foreach name='i' from=$finance_expenses_reason->get('payments') item='payment'}
              {strip}
              {capture assign='payment_info'}
                <strong>{#finance_payments_num#|escape}:</strong> {$payment->get('num')|escape}<br />
                {if $payment->get('reason')}<strong>{#finance_payments_reason#|escape}:</strong> {$payment->get('reason')|escape}<br />{/if}
                <strong>{#added#|escape}:</strong> {$payment->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$payment->get('added_by_name')|escape}<br />
                <strong>{#modified#|escape}:</strong> {$payment->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$payment->get('modified_by_name')|escape}<br />
              {/capture}
              {/strip}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border hright">{$smarty.foreach.i.iteration} </td>
              <td class="t_border" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=payments&amp;payments=view&amp;view={$payment->get('id')}"{if $show_repayment_plans} style="color:red"{/if}{help label_content=$payment->getTypeName() text_content=$payment_info popup_only=1}>{$payment->getTypeName()} {$payment->get('num')}</a></td>
              <td class="t_border hright" nowrap="nowrap">{$payment->get('amount')} {$finance_expenses_reason->get('currency')|escape}</td>
              <td class="t_border hright" nowrap="nowrap">{$payment->get('paid_amount')|string_format:"%.2f"} {$finance_expenses_reason->get('currency')|escape}</td>
              <td class="t_border hright" nowrap="nowrap">{$payment->get($paid_var)|string_format:"%.2f"} {$finance_expenses_reason->get('currency')|escape}
              {if $payment->get($paid_var) gt 0}<img onclick="return confirmAction('delete', function() {ldelim} updateBalance('', 'payment', '{$submitLink}&amp;empty={$payment->get('id')}'); {rdelim}, this, '{#finance_payments_confirm_empty#|escape:'quotes'|escape}');" src="{$theme->imagesUrl}small/delete2.png" width="10" height="10" border="0" alt="{#delete#|escape}" title="{#delete#|escape}" class="pointer" /> 
              {/if}
              </td>
              <td class="t_border hright" nowrap="nowrap">{math equation="x - y" x=$payment->get('amount') y=$payment->get('paid_amount') assign='pay_amount' format="%.2F"}{$pay_amount} {$finance_expenses_reason->get('currency')|escape}
              </td>
              <td class="hright" nowrap="nowrap">
                {if $finance_expenses_reason->get('remaining_amount') gt '0' && $pay_amount gt '0'}
                <input type="text" class="pricebox small relatives_payments" name="relatives_payments[{$payment->get('id')}]" id="relatives_payments_{counter}" value="{$payment->get('suggest_amount')|string_format:'%.2f'}" onkeyup="recalculatePaymentAmount()" onkeypress="return changeKey(this, event, insertOnlyFloats);" /> {$finance_expenses_reason->get('currency')|escape}
                  {assign var=show_submit value=1}
                {else}
                  -
                {/if}
              </td>
            </tr>
            {foreachelse}
            <tr>
              <td colspan="7" class="error">{#finance_payments_no_payments_customer#}</td>
            </tr>
            {/foreach}
            <tr>
              <td colspan="7">
              {if $show_submit}
                <button type="button" name="saveButton1" class="button" onclick="updateBalance(this.form, 'payment', '{$submitLink}');">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
              {/if}
              {if $show_repayment_plans}
                <span class="red">*{#help_finance_document_in_repayment_plans#}</span>
              {/if}
              </td>
            </tr>
          </table>
