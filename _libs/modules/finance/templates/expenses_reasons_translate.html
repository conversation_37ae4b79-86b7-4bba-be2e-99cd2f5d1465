<h1>{$title|escape}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance_expenses_reasons" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$finance_expenses_reason->get('id')}" />
<input type="hidden" name="type" id="type" value="{$finance_expenses_reason->get('type')}" />
<input type="hidden" name="company" id="company" value="{$finance_expenses_reason->get('company')}" />
<input type="hidden" name="office" id="office" value="{$finance_expenses_reason->get('office')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$finance_expenses_reason->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="vtop">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td colspan="3">
            {#message_translatable_items#|escape}
          </td>
          <td class="vtop t_border divider_cell" rowspan="23">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="2">&nbsp;</td>
          {capture assign='source_lang'}lang_{$finance_expenses_reason->get('model_lang')}{/capture}
          <td><img src="{$theme->imagesUrl}flags/{$finance_expenses_reason->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
          <td>&nbsp;</td>
          {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
          <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
        </tr>

      {foreach from=$finance_expenses_reason->getLayoutsDetails() key='lkey' item='layout'}
        {if $lkey eq 'name'}
        {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
        <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <input type="text" class="txtbox distinctive" name="name" id="name" value="{if !($layout.view && $layout.edit)}{$base_model->get('name')|escape}{else}{$finance_expenses_reason->get('name')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'description'}
        {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
        <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <textarea class="areabox distinctive" name="description" id="description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('description')|escape}{else}{$finance_expenses_reason->get('description')|escape}{/if}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_description" id="copy_description" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_description" id="bm_description" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('description')|escape}</textarea>
          </td>
        </tr>
        {elseif $lkey eq 'fin_field_1'}
        {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
        <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_fin_field_1"><label for="fin_field_1"{if $messages->getErrors('fin_field_1')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <input type="text" class="txtbox distinctive" name="fin_field_1" id="fin_field_1" value="{if !($layout.view && $layout.edit)}{$base_model->get('fin_field_1')|escape}{else}{$finance_expenses_reason->get('fin_field_1')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_fin_field_1" id="copy_fin_field_1" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_fin_field_1" id="bm_fin_field_1" value="{$base_model->get('fin_field_1')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'fin_field_2'}
        {if ($layout.view && $layout.edit)}{counter assign='translate_fields_count'}{/if}
        <tr{if !($layout.view && $layout.edit)} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_fin_field_2"><label for="fin_field_2"{if $messages->getErrors('fin_field_2')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <textarea class="areabox distinctive" name="fin_field_2" id="fin_field_2" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('fin_field_2')|escape}{else}{$finance_expenses_reason->get('fin_field_2')|escape}{/if}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_fin_field_2" id="copy_fin_field_2" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_fin_field_2" id="bm_fin_field_2" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('fin_field_2')|escape}</textarea>
          </td>
        </tr>
        {/if}
      {/foreach}

        {if in_array($finance_expenses_reason->get('type'), array($smarty.const.PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))}
        {counter assign='translate_fields_count'}
        <tr>
          <td class="labelbox"><a name="error_reason"><label for="reason"{if $messages->getErrors('reason')} class="error"{/if}>{help label_content=#finance_incomes_reasons_reason# text_content=#help_finance_incomes_reasons_cd_reason#}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            <textarea class="areabox distinctive" name="reason" id="reason" title="{#finance_incomes_reasons_reason#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$finance_expenses_reason->get('reason')|escape}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_reason" id="copy_reason" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_reason" id="bm_reason" title="{#finance_incomes_reasons_reason#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('reason')|escape}</textarea>
          </td>
        </tr>
        {/if}
        <tr>
          <td colspan="3">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#translate#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
          <td>&nbsp;</td>
          <td colspan="2">
            {if $translate_fields_count}
            <button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
            {/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$finance_expenses_reason}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
