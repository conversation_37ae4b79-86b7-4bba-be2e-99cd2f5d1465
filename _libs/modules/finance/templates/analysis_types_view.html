<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='analysis_types_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$analysis_type->get('name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='analysis_types_type'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {capture assign='kind_name'}finance_analysis_types_kind_{$analysis_type->get('kind')}{/capture}
            {$smarty.config.$kind_name}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='analysis_types_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$analysis_type->get('description')|escape|mb_wordwrap|url2href|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$analysis_type exclude='is_portal'}
</div>
