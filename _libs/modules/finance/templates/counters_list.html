<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;controller=counters&amp;{$action_param}={$action}&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="finance_counter" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=counters" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#finance_counters_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.model.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.model.link}">{#finance_counters_model#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_counters_type#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_counters_formula#|escape}</div></td>
          <td class="t_caption t_border {$sort.company.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.company.link}">{#finance_counters_company#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_counters_offices#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$counters item='counter'}
      {strip}
      {capture assign='info'}
        <strong>{#finance_counters_name#|escape}:</strong> {$counter->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$counter->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$counter->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$counter->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$counter->get('modified_by_name')|escape}<br />
        {if $counter->isDeleted()}<strong>{#deleted#|escape}:</strong> {$counter->get('deleted')|date_format:#date_mid#|escape}{if $counter->get('deleted_by_name')} {#by#|escape} {$counter->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$counter->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $counter->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if !$counter->get('active')} t_inactive{/if}{if $counter->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$counter->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($counter->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($counter->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=counters&amp;{$action_param}=view&amp;view={$counter->get('id')}">{$counter->get('name')|escape}</a></td>
          <td class="t_border {$sort.model.isSorted}" nowrap="nowrap">
            {capture assign='model_name'}finance_counters_model_{$counter->get('model')}{/capture} 
            {$smarty.config.$model_name}
          </td>
          <td class="t_border" nowrap="nowrap">
          {if $counter->get('model') eq 'Finance_Payment'}
            {capture assign='model_type'}finance_counters_model_type_{$counter->get('model_type')}{/capture} 
            {$smarty.config.$model_type}
            {$type_name}
          {elseif $counter->get('model_type')}
            {assign var='model_type' value=$counter->get('model_type')}
            {$type_names.$model_type}
          {/if}
          </td>
          <td class="t_border">{$counter->get('formula')|escape|default:'&nbsp;'}</td>
          <td class="t_border {$sort.company.isSorted}" nowrap="nowrap">
            {if !$counter->get('company')}
              {#finance_counters_company_independent#}
            {else}
              {$counter->get('company_name')|escape}
            {/if}
          </td>
          <td class="t_border" nowrap="nowrap">
            {foreach from=$counter->get('offices') item=office name=i}
              {$office.name}{if !$smarty.foreach.i.last}<br />{/if}
            {/foreach}
          </td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$counter exclude='delete'}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="9">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="9"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
