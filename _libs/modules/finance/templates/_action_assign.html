{if $available_action.show_form}
  <form method="get" action="{$smarty.server.PHP_SELF}">
    <input type="hidden" name="{$available_action.module_param}" value="{$available_action.module}" />
    {if $available_action.controller_param}
      <input type="hidden" name="{$available_action.controller_param}" value="{$available_action.controller}" />
      <input type="hidden" name="{$available_action.controller}" value="{$available_action.action}" />
    {else}
      <input type="hidden" name="{$available_action.module}" value="{$available_action.action}" />
    {/if}
    {if $available_action.model_id}
      <input type="hidden" name="{$available_action.action}" value="{$available_action.model_id}" />
    {/if}
    {if $available_action.model_lang}
      <input type="hidden" name="model_lang" value="{$available_action.model_lang}" />
    {/if}
    {if $available_action.a_type}
      <input type="hidden" name="a_type" value="{$available_action.a_type}" />
    {/if}
    {if $available_action.name eq 'search' || $available_action.name eq 'filter'}
      <input type="hidden" name="{$available_action.session_param}" value="1" />
      <input type="hidden" name="{$available_action.name}_module" value="{$available_action.module}" />
      <input type="hidden" name="{$available_action.name}_controller" value="{$available_action.controller}" />
      {if $event}
      <input type="hidden" name="event" value="{$event}" />
      {/if}
      {if $form_name}
      <input type="hidden" name="form_name" value="{$form_name}" />
      {/if}
    {/if}
{/if}
   
  <table border="0" cellpadding="3" cellspacing="3" width="100%" class="t_layout_table">
    <tr>
      <td style="vertical-align: top;">
        <table cellpadding="0" cellspacing="0" border="0" class="t_layout_table">
          <tr>
            <td class="databox" nowrap="nowrap">
                {assign var='users' value=$available_action.users}
                {include file='input_checkbox_group.html'
                  standalone=true
                  name=$users.name
                  label=$users.label
                  help=$users.help
                  optgroups=$users.optgroups
                  value=$users.value
                  scrollable=$users.scrollable
                  count_options=$users.count_options
                  max_options=$users.max_options
                  index='assign'}
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <button type="submit" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go" title="{$available_action.label}">{$available_action.options.label}</button>
      </td>
    </tr>
  </table>

{if $available_action.show_form}
  </form>
{/if}
