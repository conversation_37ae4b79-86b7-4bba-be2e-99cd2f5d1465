<table cellpadding="0" border="0" cellspacing="0" width="100%" class="t_table t_list">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
    {foreach from=$columns item='column'}
    <td class="t_caption t_border {$sort.$column.class}" nowrap="nowrap">
      <div class="t_caption_title" onclick="{$sort.$column.link}">
        {capture assign='column_name'}finance_{if !preg_match('#^owner|responsible|observer|decision$#', $column)}expenses_reasons_{/if}{$column}{/capture}
        {$basic_vars_labels.$column|default:$smarty.config.$column_name|escape}
      </div>
    </td>
    {/foreach}
    <td class="t_caption" nowrap="nowrap">
      &nbsp;
    </td>
  </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {array assign='background_colors'}
      {foreach name='i' from=$finance_expenses_reasons item='expenses_reason'}
      {strip}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#finance_expenses_reasons_name#|escape}:</strong> {$expenses_reason->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$expenses_reason->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$expenses_reason->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$expenses_reason->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$expenses_reason->get('modified_by_name')|escape}<br />

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$expenses_reason->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $expenses_reason->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='expenses_reason_status'}
        {if $expenses_reason->get('status') eq 'opened'}
          {#help_finance_documents_status_opened#}
        {elseif $expenses_reason->get('status') eq 'locked'}
          {#help_finance_documents_status_locked#}
        {elseif $expenses_reason->get('status') eq 'finished'}
          {#help_finance_documents_status_finished#}
        {/if}
        {if $expenses_reason->get('substatus_name')}
          <br />
          {#help_finance_documents_substatus#}{$expenses_reason->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
      {assign var='background_style' value=''}
      {assign var='background_properties' value=$expenses_reason->getBackgroundColor()}
      {if !empty($background_properties)}
        {array_push array='background_colors' new_item=$background_properties.background_color key=$background_properties.definition}
        {capture assign='background_style'} style="background-color: #{$background_properties.background_color}; color: #{$background_properties.text_color};"{/capture}
      {/if}
      {include file="`$theme->templatesDir`row_link_action.html" object=$expenses_reason assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
    <tr class="{if $background_style}t_row{else}{cycle values='t_odd,t_even'}{/if}{if $expenses_reason->get('annulled_by')} t_strike{/if}{if !$expenses_reason->get('active')} t_inactive{/if}"{$background_style}>
      <td class="t_border hright" nowrap="nowrap">
        {if $expenses_reason->get('files_count')}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=expenses_reasons&amp;expenses_reasons=attachments&amp;attachments={$expenses_reason->get('id')}">
            <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                 onmouseover="showFiles(this, '{$module}', '{$controller}', {$expenses_reason->get('id')})"
                 onmouseout="mclosetime()" />
          </a>
        {/if}
        {counter name='item_counter' print=true}
      </td>
      {foreach from=$columns item='column'}
      {if in_array($column, array('comments', 'emails', 'history_activity'))}
        {include file="`$smarty.const.PH_MODULES_DIR`/outlooks/templates/td/default_`$column`.html" single=$expenses_reason}
      {else}
      <td class="t_border {$sort.$column.isSorted}{if in_array($column, array('fiscal_total_vat','total_vat_rate','total_vat','fiscal_total','fiscal_total_with_vat','total_with_vat','total_remaining_amount','total_paid_amount','total'))} hright{/if}{if !in_array($column, array('status', 'tags', 'owner', 'responsible', 'observer', 'decision'))} {$row_link_class}{/if}"{if !in_array($column, array('status', 'tags', 'owner', 'responsible', 'observer', 'decision'))}{$row_link}{/if}>
      {if $column eq 'total_vat_rate'}
        {$expenses_reason->get($all_columns.$column)|escape|default:"0.00"} %
      {elseif preg_match('#^total((_with)?_vat)?$#', $column)}
        {$expenses_reason->get($all_columns.$column)|escape} {$expenses_reason->get('currency')|escape}
      {elseif $column eq 'total_remaining_amount'}
        {if $expenses_reason->get('type') ne $smarty.const.PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON && !($expenses_reason->get('type') eq $smarty.const.PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE and $expenses_reason->get('payment_status') eq 'invoiced')}
          {math equation=x-y x=$expenses_reason->get('total_with_vat')|string_format:"%.2F" y=$expenses_reason->getFullPaidAmount() format="%.2F"} {$expenses_reason->get('currency')|escape}
        {else}
          &nbsp;
        {/if}
      {elseif $column eq 'total_paid_amount'}
        {if $expenses_reason->get('type') ne $smarty.const.PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON && !($expenses_reason->get('type') eq $smarty.const.PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE and $expenses_reason->get('payment_status') eq 'invoiced')}
          {$expenses_reason->getFullPaidAmount()|string_format:"%.2F"} {$expenses_reason->get('currency')|escape}
        {else}
          &nbsp;
        {/if}
      {elseif $column eq 'status'}
        {capture assign='popup_and_onclick'}
          {popup text=$expenses_reason_status|escape caption=#help_finance_documents_status#|escape width=250}{if !$expenses_reason->get('annulled_by') && $expenses_reason->checkPermissions('setstatus')} onclick="changeStatus({$expenses_reason->get('id')}, 'finance', 'expenses_reasons')" style="cursor:pointer;"{/if}
        {/capture}
        {if $expenses_reason->get('substatus_name')}
          {if $expenses_reason->get('icon_name')}
            <img src="{$smarty.const.PH_FINANCE_DOCUMENTS_STATUSES_URL}{$expenses_reason->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
          {else}
            {if $expenses_reason->get('status') eq 'opened'}
              <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $expenses_reason->get('status') eq 'locked'}
              <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {elseif $expenses_reason->get('status') eq 'finished'}
              <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
          {/if}
          <span {$popup_and_onclick}>{$expenses_reason->get('substatus_name')|escape}</span>
        {else}
          {if $expenses_reason->get('status') eq 'opened'}
            <img src="{$theme->imagesUrl}documents_opened.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
          {elseif $expenses_reason->get('status') eq 'locked'}
            <img src="{$theme->imagesUrl}documents_locked.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
          {elseif $expenses_reason->get('status') eq 'finished'}
            <img src="{$theme->imagesUrl}documents_closed.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
          {/if}
          {capture assign='status_param'}finance_documents_status_{$expenses_reason->get('status')}{/capture}
          <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
        {/if}
      {elseif $column eq 'tags'}
        <div{if $expenses_reason->getModelTags() && $expenses_reason->get('available_tags_count') gt 0 && $expenses_reason->checkPermissions('tags_view') && $expenses_reason->checkPermissions('tags_edit')} style="padding: 3px 0 3px 0; cursor: pointer;" onclick="changeTags({$expenses_reason->get('id')}, 'finance', 'expenses_reasons')" title="{#tags_change#}"{/if}>
          {if $expenses_reason->get('model_tags')|@count gt 0 && $expenses_reason->checkPermissions('tags_view')}
            {foreach from=$expenses_reason->get('model_tags') item='tag' name='ti'}
              <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
            {/foreach}
          {else}
            &nbsp;
          {/if}
        </div>
      {elseif in_array($column, array('owner', 'responsible', 'observer', 'decision'))}
        {include file=`$templatesDir`_assignments_dashlet.html a_type=assignments_`$column` model=$expenses_reason}
      {elseif $column eq 'name_num'}
        &#91;{$expenses_reason->get('num')|escape|default:"&nbsp;"}&#93; {$expenses_reason->get('name')|escape|default:"&nbsp;"}
      {elseif $column eq 'customer_name_code'}
        &#91;{$expenses_reason->get('customer_code')|escape|default:"&nbsp;"}&#93; {$expenses_reason->get('customer_name')|escape|default:"&nbsp;"}
      {elseif $column eq 'trademark_name_code'}
        {if $expenses_reason->get('trademark')}&#91;{$expenses_reason->get('trademark_code')|escape|default:"&nbsp;"}&#93; {$expenses_reason->get('trademark_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}
      {elseif $column eq 'project_name_code'}
        {if $expenses_reason->get('project')}&#91;{$expenses_reason->get('project_code')|escape|default:"&nbsp;"}&#93; {$expenses_reason->get('project_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}
      {elseif $column eq 'payment_type'}
        {capture assign='payment_type'}finance_payment_type_{if $expenses_reason->get('cheque')}cheque{else}{$expenses_reason->get('payment_type')}{/if}{/capture}
        {$smarty.config.$payment_type|escape}
      {elseif $column eq 'added' || $column eq 'modified'}
        {$expenses_reason->get($all_columns.$column)|date_format:#date_mid#|escape}
      {elseif $column eq 'admit_VAT_credit'}
        {if $expenses_reason->get('admit_VAT_credit')}{#yes#}{else}{#no#}{/if}
      {elseif $column eq 'accounting_period'}
        {if $expenses_reason->get('accounting_period') && $expenses_reason->get('accounting_period') > '0000-00-00'}{$expenses_reason->get('accounting_period')|date_format:'%B %Y'}{/if}
      {elseif $column eq 'payment_status'}
        {if $expenses_reason->get('type') neq $smarty.const.PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON}
          {capture assign='payment_status'}finance_payment_status_{$expenses_reason->get('payment_status')}{/capture}
          {$smarty.config.$payment_status|escape}
        {else}
          &nbsp;
        {/if}
      {elseif $column eq 'handovered_status'}
        {capture assign='handovered_status'}finance_handovered_{$expenses_reason->get('handovered_status')}{/capture}
        {$smarty.config.$handovered_status|escape|default:"&nbsp;"}
      {elseif $column eq 'allocated_status'}
        {capture assign='allocated_status'}finance_expenses_reasons_allocated_status_{$expenses_reason->get('allocated_status')}{/capture}
        {$smarty.config.$allocated_status|escape|default:"&nbsp;"}
      {elseif $column eq 'issue_date' || $column eq 'date_of_payment'}
        {$expenses_reason->get($all_columns.$column)|date_format:#date_short#|escape}
      {elseif $column eq 'fiscal_event_date'}
        {if $expenses_reason->get('fiscal_event_date') ne 0 && in_array($expenses_reason->get('type'), array($smarty.const.PH_FINANCE_TYPE_EXPENSES_INVOICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, $smarty.const.PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))}
          {$expenses_reason->get($all_columns.$column)|date_format:#date_short#|escape}
        {else}
          -
        {/if}
      {elseif $column eq 'description'}
        {$expenses_reason->get('description')|escape|nl2br|url2href|default:"&nbsp;"}
      {else}
        {$expenses_reason->get($all_columns.$column)|escape|default:"&nbsp;"}
      {/if}
      </td>
      {/if}
      {/foreach}
      <td class="hcenter" nowrap="nowrap">
        {include file=`$theme->templatesDir`single_actions_list.html object=$expenses_reason}
      </td>
    </tr>
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="{math equation='count+2' count=$columns|@count}">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td colspan="{math equation='count+2' count=$columns|@count}" class="t_footer"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;controller=expenses_reasons&amp;expenses_reasons=dashlet&amp;dashlet={$dashlet_id}&amp;page={/capture}
{capture assign='container'}content_dashlet_{$dashlet_id}{/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  target=$container
  link=$link
  use_ajax=1
  hide_rpp=1
  hide_stats=1
}
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
