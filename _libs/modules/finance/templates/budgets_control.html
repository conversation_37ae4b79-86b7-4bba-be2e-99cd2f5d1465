<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}
{assign var='income_count' value=$finance_budget->get('income')|@count}
{assign var='expense_count' value=$finance_budget->get('expense')|@count}

<input type="hidden" name="id" id="id" value="{$finance_budget->get('id')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
  <tr>
    <td class="nopadding">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='budgets_company'}</td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            {$finance_budget->get('company_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='budgets_year'}</td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            {$finance_budget->get('year')}
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="legend_table" style="margin: 0 10px;">
        <tr>
          <td class="legend">
            {#finance_budgets_control_info#}
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td nowrap="nowrap" class="m_header_menu t_table nopadding" style="border-bottom: 1px solid #DDDDDD;">
      <ul>
        {if $income_count}<li><span{if $sel_tab != 'expense'} class="selected"{/if}><a onclick="toggleFinanceBudgetsItemsType(this)" id="income_switch" class="pointer" title="{#finance_budgets_income#|escape}"><img src="{$theme->imagesUrl}incomes.png" width="16" height="16" alt="" title="" border="0" />{if $currentUser->getPersonalSettings('interface', 'action_labels')}{#finance_budgets_income#|escape}{/if}</a></span></li>{/if}
        {if $expense_count}<li><span{if !$income_count || $sel_tab == 'expense'} class="selected"{/if}><a onclick="toggleFinanceBudgetsItemsType(this)" id="expense_switch" class="pointer" title="{#finance_budgets_expense#|escape}"><img src="{$theme->imagesUrl}expenses.png" width="16" height="16" alt="" title="" border="0" />{if $currentUser->getPersonalSettings('interface', 'action_labels')}{#finance_budgets_expense#|escape}{/if}</a></span></li>{/if}
      </ul>
    </td>
  </tr>
  <tr id="income_row"{if !$income_count || $sel_tab == 'expense'} style="display: none"{/if}>
    <td>
      {include file=`$templatesDir`_budgets_data.html analysis_items=$finance_budget->get('income') items_type='income'}
    </td>
  </tr>
  <tr id="expense_row"{if ($income_count && $sel_tab != 'expense') || !$expense_count || $sel_tab == 'income'} style="display: none"{/if}>
    <td>
      {include file=`$templatesDir`_budgets_data.html analysis_items=$finance_budget->get('expense') items_type='expense'}
    </td>
  </tr>
  <tr>
    <td>
      <div id="item_container">
        {if $analysis_item}
          {include file=`$templatesDir`_budgets_data_item.html}
        {/if}
      </div>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{*include file=`$theme->templatesDir`system_settings_box.html object=$finance_budget exclude='is_portal'}
{include file=`$theme->templatesDir`after_actions_box.html*}
</div>
