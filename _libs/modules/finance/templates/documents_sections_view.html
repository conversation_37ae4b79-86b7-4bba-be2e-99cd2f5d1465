<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='documents_sections_for_model'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">{capture assign='model_param'}{$finance_documents_section->get('model')|mb_lower}{/capture}{$smarty.config.$model_param|default:$smarty.config.all}</td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_sections_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$finance_documents_section->get('name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_sections_position'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$finance_documents_section->get('position')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_sections_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$finance_documents_section->get('description')|escape|mb_wordwrap|url2href|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='documents_sections_icon'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {if $finance_documents_section->get('icon_name')}
              <img src="{$smarty.const.PH_FINANCE_DOCUMENTS_SECTIONS_URL}{$finance_documents_section->get('icon_name')}" alt="" />
            {/if}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$finance_documents_section}
</div>
