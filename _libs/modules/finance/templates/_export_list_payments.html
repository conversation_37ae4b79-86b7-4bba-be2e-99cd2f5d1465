    <table border="1" cellpadding="3" cellspacing="0">
      <tr>
        <th nowrap="nowrap">{#num#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.num|default:#finance_payments_num#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.issue_date|default:#finance_payments_issue_date#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.type|default:#finance_payments_type#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.company_data|default:#finance_payments_container_id#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.container_code|default:#finance_payments_container_code#|escape}</th>
        <th nowrap="nowrap">{#finance_payments_company#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.customer|default:#finance_payments_customer#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.amount|default:#finance_payments_amount#|escape}</th>
        <th nowrap="nowrap">{$basic_vars_labels.currency|default:#finance_payments_currency#|escape}</th>
      </tr>
    {counter start=0 name='item_counter' print=false}
    {foreach name='i' from=$finance_payments item='payment'}
      <tr>
        <td>{counter name='item_counter' print=true}</td>
        <td>{if $payment->get('num')}{$payment->get('num')|escape}{else}{#finance_payments_unfinished_payment#|escape}{/if}</td>
        <td>{if $payment->get('issue_date') != '0000-00-00'}{$payment->get('issue_date')|date_format:#date_short#}{/if}</td>
        <td>{$payment->get('type_name')}</td>
        <td>{$payment->get('container_name')}</td>
        <td>{$payment->get('container_code')}</td>
        <td>{$payment->get('company_name')}</td>
        <td>{$payment->get('customer_name')}</td>
        <td align="right">{$payment->get('amount')}</td>
        <td align="right">{$payment->get('currency')}</td>
      </tr>
    {foreachelse}
      <tr>
        <td colspan="10">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    </table>