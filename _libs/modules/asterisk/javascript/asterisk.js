/**
 * Initiates asterisk call
 */
function initiateAsteriskCall(element) {
    var phone_number_elements = Element.select(element, '.asterisk_contact_button_number');

    if (phone_number_elements.length > 0) {
        phone_number = phone_number_elements[0].innerHTML;

        if (confirm(i18n['messages']['confirm_asterisk_call'] + phone_number + '?')) {
            phone_number = trim(phone_number);
            var prefix_plus = phone_number.match(/^\+/);
            phone_number = phone_number.replace(/[^0-9]/gi, '');

            if (phone_number) {
                Effect.Center('loading');
                Effect.Appear('loading');

                if (prefix_plus) {
                    phone_number = '00' + phone_number;
                }

                var opt = {
                    method: 'get',
                    onSuccess: function(t) {
                        if (!checkAjaxResponse(t.responseText)) {
                            return;
                        }
                        var result = eval('(' + t.responseText + ')');
                        Effect.Fade('loading');
                        if (result.dial_link) {
                            redirect(result.dial_link);
                        }
                    },
                    on404: function(t) {
                        alert('Error 404: location "' + t.statusText + '" was not found.');
                    },
                    onFailure: function(t) {
                        alert('Error ' + t.status + ' -- ' + t.statusText);
                    }
                };
                var url = env.base_url + '?' + env.module_param + '=asterisk&asterisk=asterisk_call&call_number=' + phone_number;

                new Ajax.Request(url, opt);
            } else {
                alert(i18n['messages']['no_phone_number_to_be_dialed']);
            }
        }
    } else {
        alert(i18n['messages']['no_phone_number_to_be_dialed']);
    }
}
