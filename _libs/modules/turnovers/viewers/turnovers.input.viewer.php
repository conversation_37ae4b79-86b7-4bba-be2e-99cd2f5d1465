<?php

class Turnovers_Input_Viewer extends Viewer {
    public $template = 'input.html';

    public function prepare() {

        if ($this->model->get('customer')) {
            //get customer data
            $customer_id = $this->model->get('customer');
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->data['input_customer_name'] = $customer_name;
            }
        }
        if ($this->model->get('trademark')) {
            //get trademark data
            $trademark_id = $this->model->get('trademark');
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('n.deleted IS NOT NULL',
                                              'n.id = ' . $trademark_id));
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->data['input_trademark_name'] = $nomenclature->get('name');
            }
        }

        //prepare autocompleters for customer and trademerk for action list template
        $trademark_autocomplete_ = $this->prepareAutocompleteParams(array('buttons' => 'search',
                                               'fill_options' => array('$trademark1 => <trademark>',
                                                                       '$trademark1_autocomplete => <name>',
                                                                       '$customer1 => <customer>',
                                                                       '$customer1_autocomplete => [<customer_code>] <customer_name>',
                                                                       '$customer1_oldvalue => [<customer_code>] <customer_name>'),
                                         ),
                                         array('buttons' => 'search',
                                               'fill_options' => array('$customer1 => <id>',
                                                                       '$customer1_autocomplete => [<code>] <name> <lastname>',
                                                                       '$trademark1 => <main_trademark>',
                                                                       '$trademark1_autocomplete => <main_trademark_name>',
                                                                       '$trademark1_oldvalue => <main_trademark_name>'),
                                         ));
        $this->data['customer_autocomplete1'] = $this->data['customer_autocomplete'];
        $this->data['trademark_autocomplete1'] = $this->data['trademark_autocomplete'];

        //prepare autocompleters for customer and trademerk for action input template
        $this->prepareAutocompleteParams();

        //get turnovers currency
        $currency = $this->registry['config']->getParam('turnovers', 'currency');
        if (empty($currency)) {
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $currency = Finance_Currencies::getMain($this->registry);
        }
        $this->data['currency'] = $currency;

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('turnovers_input');
        $this->data['title'] = $title;
    }
}

?>
