<?php

class Groups_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Group';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Groups';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'add', 'view', 'edit');

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'add', 'view', 'edit', 'translate');

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax',
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'ajax':
            $this->_ajax();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $group = Groups::buildModel($this->registry);

            if ($group->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_groups_add_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_groups_add_failed'),'',-1);
            }
        } else {
            //create empty user model
            $group = Groups::buildModel($this->registry);
        }

        if (!empty($group)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('group', $group->sanitize());
        }

        return true;
    }


    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $group = Groups::buildModel($this->registry);

            if ($group->save()) {
                //show message 'message_groups_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_groups_edit_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_groups_edit_failed'),'',-1);
                //register the model, with all the posted details
                $this->registry->set('group', $group);
            }

        } elseif ($id) {
            // the model from the DB
            $filters = array('where' => array('g.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $group = Groups::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($group);
        }

        if (!empty($group)) {
            //get group members
            $group->getUsers();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('group')) {
                $this->registry->set('group',  $group->sanitize());
            }
        } else {
            //show error 'no such record'
            //$error[] = $groupsPref->getVar('error_no_such_group');
            $this->registry['messages']->setError($this->i18n('error_no_such_group'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $group = Groups::buildModel($this->registry);

            if ($group->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_groups_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_groups_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('g.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $group = Groups::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($group);
        }

        if (!empty($group)) {
            //get group members
            $group->getUsers();

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('group', $group->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_group'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('g.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
        $group = Groups::searchOne($this->registry, $filters);

        if (!empty($group)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($group);

            //get group members
            $group->getUsers();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('group')) {
                $this->registry->set('group', $group->sanitize());
            }
        } else {
            //show error 'no such record'
            //$error[] = $groupsPref->getVar('error_no_such_group');
            $this->registry['messages']->setError($this->i18n('error_no_such_group'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * AJAX queries
     */
    private function _ajax() {
        $request = &$this->registry['request'];

        //get the requested subaction
        $subaction = $request->get($this->action);

        switch($subaction) {
        case 'parents':
            $result = Groups::getTreeParentsIds($this->registry, $request->get('id'));
            break;
        case 'children':
            $result = Groups::getTreeDescendantsIds($this->registry, $request->get('id'));
            break;
        default:
            exit;
        }

        $result = json_encode($result);

        print 'var result = ' . $result;

        exit;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Groups::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete groups
        $result = Groups::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Groups::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;
        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Groups::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

}

?>
