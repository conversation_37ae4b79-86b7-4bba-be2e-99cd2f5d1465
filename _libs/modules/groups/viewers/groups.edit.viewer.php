<?php

class Groups_Edit_Viewer extends Viewer {
    public $template = 'edit.html';

    public function prepare() {
        $this->model = $this->registry['group'];
        $this->data['group'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s', 
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare users to select the manager
        $filters['where'] = array('g.id = ' . $this->model->get('id'));
        $this->data['users'] = Groups::getUsers($this->registry, $filters);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups_tree'] = Groups::getTree($this->registry);

        //prepare descendants
        $this->data['groups_descendants'] = Groups::getTreeDescendants($this->registry, array('where' => array('gn.id = ' . $this->model->get('id')),
                                                                                                        'sanitize' => true));

        //prepare parents
        $this->data['groups_parents'] = Groups::getTreeParents($this->registry, $this->model->get('id'));

    }

    public function prepareTitleBar() {
        $title = $this->i18n('groups');
        $href = sprintf('%s=%s', $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('groups_edit');
        $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s&amp;model_lang=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'), $this->model->get('model_lang'));

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }

}

?>
