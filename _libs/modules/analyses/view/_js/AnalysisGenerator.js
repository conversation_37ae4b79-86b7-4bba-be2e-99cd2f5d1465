class AnalysisGenerator
{
    analysisId;
    analysisUniqid;
    getAnalysisData = false;
    filtersForm;
    analysisContainer;
    displayObjectsContainers;

    constructor(analysisId, analysisUniqid) {
        if (typeof analysisId !== 'string' || analysisId === '') {
            throw new Error('Analysis ID is required!');
        }
        this.analysisId = analysisId;

        if (typeof analysisUniqid !== 'string' || analysisUniqid === '') {
            throw new Error('Analysis uniqid is required!');
        }
        this.analysisUniqid = analysisUniqid;
    }

    init(getAnalysisData = false) {
        // Try to get the filters form
        const filtersForm = this.getFiltersForm();

        // TODO: Better check if there should be a filters form (this just checks if there is, but the analysis settings tell if there should be)
        this.getAnalysisData = (getAnalysisData === true || !filtersForm);

        if (filtersForm) {
            this.prepareFiltersForm(filtersForm);
        }

        if (this.getAnalysisData) {
            this.loadDisplayObjects();
        }
    }

    prepareFiltersForm(filtersForm) {
        // If there's a filters form, check if it's valid
        if (!(filtersForm instanceof HTMLFormElement)) {
            throw new Error(`Invalid form: ${filtersForm}`);
        }

        // If the analysis data should be taken
        if (this.getAnalysisData) {
            // Change the browser's URL
            filtersForm.addEventListener('submit', (event) => {
                const pageUrl = buildFormUrl(filtersForm, (env.base_host + env.base_url));
                // TODO: Use history.pushState and take care about the data, when the user tried to go back (i.e. restore the state)
                history.replaceState({}, '', pageUrl);

                // Clear the analysis messages, because the form was submitted by the user
                if (event.submitter) {
                    this.clearAnalysisMessages();
                }
            });

            // Prevent default submit action
            // TODO: Not cool, check if removing the onsubmit is still needed
            filtersForm.setAttribute('onsubmit', '');
            filtersForm.addEventListener('submit', (e) => {
                e.preventDefault();
                return false;
            });

            // TODO: Check if this should be uncommented or removed
            //filtersForm.dataset.generated = '1';
        }
    }

    clearAnalysisMessages() {
        document.querySelector(`#analysis-messages-container-${this.analysisUniqid}`).innerHTML = '';
    }

    loadDisplayObjects() {
        const displayObjectsContainers = this.getDisplayObjectsContainers();
        if (!displayObjectsContainers) {
            throw new Error(`No display objects containers for analysis with ID: ${this.analysisId} and uniqid: ${this.analysisUniqid}!`);
        }

        displayObjectsContainers.forEach((displayObjectContainer) => {
            if (typeof displayObjectContainer.dataset.displayObjectName === 'undefined') {
                throw new Error('No display object name for:', displayObjectContainer);
            }
            const displayObjectName = displayObjectContainer.dataset.displayObjectName;

            if (typeof displayObjectContainer.dataset.displayObjectMode === 'undefined') {
                throw new Error(`No mode defined for display object "${displayObjectName}"!`);
            }
            let displayObjectClassName;
            switch (displayObjectContainer.dataset.displayObjectMode.toLowerCase()) {
                case 'pivot':
                    displayObjectClassName = AnalysisDisplayObjectPivot;
                    break;
                case 'grid':
                    displayObjectClassName = AnalysisDisplayObjectGrid;
                    break;
                default:
                    throw new Error(`Unrecognized mode for display object "${displayObjectName}"!`);
            }

            const displayObjectDataSourceBaseUrl = (
                typeof displayObjectContainer.dataset.displayObjectDataSourceBaseUrl === 'undefined'
                    || displayObjectContainer.dataset.displayObjectDataSourceBaseUrl === ''
                ? null
                : displayObjectContainer.dataset.displayObjectDataSourceBaseUrl
            );

            const displayObjectDataSourceMode = (
                typeof displayObjectContainer.dataset.displayObjectDataSourceMode === 'undefined'
                    || displayObjectContainer.dataset.displayObjectDataSourceMode === ''
                ? null
                : displayObjectContainer.dataset.displayObjectDataSourceMode
            );

            const displayObject = new displayObjectClassName(
                this.analysisId,
                displayObjectName,
                this.analysisUniqid,
                window[`analysisDisplayObjectSettings_${this.analysisUniqid}_${displayObjectName}`],
                displayObjectDataSourceBaseUrl,
                displayObjectDataSourceMode,
                this.getFiltersForm()
            );
            displayObject.attachTo(displayObjectContainer);
        });
    }

    getFiltersForm() {
        if (typeof this.filtersForm == 'undefined') {
            this.filtersForm = document.querySelector(`#analysis-form-${this.analysisUniqid}`);
        }
        return this.filtersForm;
    }

    getAnalysisContainer() {
        if (typeof this.analysisContainer == 'undefined') {
            this.analysisContainer = document.querySelector(`#analysis-${this.analysisUniqid}`);
        }
        return this.analysisContainer;
    }

    getDisplayObjectsContainers() {
        if (typeof this.displayObjectsContainers == 'undefined') {
            const analysisContainer = this.getAnalysisContainer();
            if (!analysisContainer) {
                throw new Error(`No container for analysis with ID: ${this.analysisId} and uniqid: ${this.analysisUniqid}!`);
            }
            this.displayObjectsContainers = analysisContainer.querySelectorAll('.display_object_container');
        }
        return this.displayObjectsContainers;
    }
}
