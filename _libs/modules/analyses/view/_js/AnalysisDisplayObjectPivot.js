class AnalysisDisplayObjectPivot extends AnalysisDisplayObject {
    pivotviewReports;
    pivotReportsPersonalSettingName;
    getPivotviewReportsWorking = false;

    constructor (
            analysisId,
            displayObjectName,
            analysisUniqid,
            displayObjectSettings,
            dataSourceBaseUrl = null,
            dataSourceMode = null,
            filtersForm  = null
    ) {
        super(
            analysisId,
            displayObjectName,
            analysisUniqid,
            displayObjectSettings,
            dataSourceBaseUrl,
            dataSourceMode,
            filtersForm
        );

        this.pivotReportsPersonalSettingName = `analysis_${this.analysisId}_pivot_${this.displayObjectName}_reports`;
    }

    build() {
        this.componentObject = new ej.pivotview.PivotView(this.setDefaultSettings(this.displayObjectSettings));

        // TODO: Catch errors (check if actionFailure is already defined or attach event listener, which will be executed always, no matter what display settings are prepared)
        // this.componentObject.actionFailure = (e) => {
        //     // Catch errors (for example: too long URI, when using too many component filters)
        // };

        // TODO: Better way to check if an analysis uses a data manager
        if (this.dataSourceBaseUrl) {
            if (this.dataSourceMode === 'data_manager') {
                this.loadDataSourceDataManager();
            } else if (this.dataSourceMode === 'data') {
                this.loadDataSourceData().then(messages => {
                    this.processDataSourceDataMessages(messages);
                });
            } else {
                console.error(`Invalid data source mode: ${this.dataSourceMode}`);
            }
        }

        if (this.pivotUseReports()) {
            this.loadPivotviewReports()
                .then(() => {
                    this.componentObject.appendTo(this.componentContainer);
                })
                .catch(error => {
                    console.error(error);
                });
        } else {
            this.componentObject.appendTo(this.componentContainer);
        }
    }

    setDataSource(dataSource) {
        // Preserve configuration
        const columns = this.componentObject.dataSourceSettings.columns;
        const rows = this.componentObject.dataSourceSettings.rows;
        const values = this.componentObject.dataSourceSettings.values;
        const filters = this.componentObject.dataSourceSettings.filters;

        this.componentObject.dataSourceSettings.dataSource = dataSource;

        // Restore configuration
        this.componentObject.dataSourceSettings.columns = columns;
        this.componentObject.dataSourceSettings.rows = rows;
        this.componentObject.dataSourceSettings.values = values;
        this.componentObject.dataSourceSettings.filters = filters;
    }

    showSpinner() {
        ej.popups.createSpinner({ target: this.componentContainer });
        ej.popups.showSpinner(this.componentContainer);
    }

    hideSpinner() {
        ej.popups.hideSpinner(this.componentContainer);
    }

    setDefaultSettings(displaySettings) {
        if (typeof displaySettings.toolbar !== 'undefined') {
            if (displaySettings.toolbar.includes('New') && typeof displaySettings.newReport === 'undefined') {
                displaySettings.newReport = this.newReport.bind(this);
            }
            if ((displaySettings.toolbar.includes('Save')
                        || displaySettings.toolbar.includes('SaveAs'))
                    && typeof displaySettings.saveReport === 'undefined') {
                displaySettings.saveReport = this.saveReport.bind(this);
            }
            if (displaySettings.toolbar.includes('Rename') && typeof displaySettings.removeReport === 'undefined') {
                displaySettings.removeReport = this.removeReport.bind(this);
            }
            if (displaySettings.toolbar.includes('Remove') && typeof displaySettings.renameReport === 'undefined') {
                displaySettings.renameReport = this.renameReport.bind(this);
            }
            if (displaySettings.toolbar.includes('Load')) {
                if (typeof displaySettings.fetchReport === 'undefined') {
                    displaySettings.fetchReport = this.fetchReport.bind(this);
                }
                if (typeof displaySettings.loadReport === 'undefined') {
                    displaySettings.loadReport = this.loadReport.bind(this);
                }
            }
        }

        return displaySettings;
    }

    pivotUseReports() {
        if (typeof this.componentObject.toolbar === 'undefined'
                || !Array.isArray(this.componentObject.toolbar)) {
            return false;
        }
        return ['New', 'Save', 'SaveAs', 'Rename', 'Remove', 'Load']
            .some(action => this.componentObject.toolbar.includes(action));
    }

    getLocalPivotviewReports() {
        if (typeof this.pivotviewReports === 'undefined') {
            return false;
        }
        return this.pivotviewReports;
    }
    setLocalPivotviewReports(pivotviewReports) {
        this.pivotviewReports = pivotviewReports;
    }

    loadPivotviewReports() {
        return new Promise((resolve, reject) => {
            const localPivotviewReports = this.getLocalPivotviewReports();
            if (localPivotviewReports !== false) {
                resolve();
                return;
            }

            if (this.getPivotviewReportsWorking) {
                setTimeout(() => {
                    this.loadPivotviewReports()
                        .then(resolve)
                        .catch(reject);
                }, 100);
                return;
            }

            this.getPivotviewReportsWorking = true;

            const urlPivotGetReports = this.buildActionUrl('users', 'get_personal_setting');
            urlPivotGetReports.searchParams.append('section', this.module);
            urlPivotGetReports.searchParams.append('name', this.pivotReportsPersonalSettingName);

            fetch(urlPivotGetReports)
                .then(response => response.text())
                .then((data) => {
                    if (data === '') {
                        data = [];
                    } else {
                        data = JSON.parse(data);
                    }

                    this.setLocalPivotviewReports(data);
                    resolve()
                })
                .catch(error => {
                    // TODO: Error message
                    console.error(error);
                    reject(error);
                })
                .finally(() => {
                    this.getPivotviewReportsWorking = false;
                });
        });
    }

    toolbarReportButtonPromise(saveReportsPromise, btnContainer) {
        saveReportsPromise.then(() => {
            btnContainer.classList.add('toolbar_btn_success')
            setTimeout(() => {
                btnContainer.classList.remove('toolbar_btn_success')
            }, 5000)
        }).catch(() => {
            btnContainer.classList.add('toolbar_btn_failed')
        });

        return saveReportsPromise;
    }

    saveReports(pivotviewReports) {
        return new Promise((resolve, reject) => {
            const urlPivotSaveReports = this.buildActionUrl('users', 'set_personal_setting');
            const pivotReportFormData = new FormData();
            pivotReportFormData.append('section', this.module);
            pivotReportFormData.append('name', this.pivotReportsPersonalSettingName);
            pivotReportFormData.append('value', JSON.stringify(pivotviewReports));
            fetch(urlPivotSaveReports, {
                    method: 'post',
                    body: pivotReportFormData
            })
                // .then(response => response.text())
                .then(response => {
                    if (response.ok) {
                        this.setLocalPivotviewReports(pivotviewReports);
                        resolve();
                    } else {
                        const errMsg = 'Failed to save reports!';
                        console.error(errMsg);
                        reject(new Error(errMsg));
                    }
                }).catch(error => {
                    console.error(error);
                    reject(error);
                });
        });
    }

    modifyReportsList(reportsModifierCallback) {
        const saveReportsPromise = this.saveReports(
            reportsModifierCallback(this.getLocalPivotviewReports())
        );

        if (typeof this.componentObject.toolbarModule === 'undefined'
                || typeof this.componentObject.toolbarModule.toolbar === 'undefined'
                || typeof this.componentObject.toolbarModule.toolbar.activeEle === 'undefined') {
            return;
        }

        const btnContainer = this.componentObject.toolbarModule.toolbar.activeEle.closest('.e-toolbar-item');
        if (!btnContainer) {
            return;
        }

        btnContainer.classList.add('toolbar_btn_loading');
        this.toolbarReportButtonPromise(saveReportsPromise, btnContainer)
            .finally(() => {
                btnContainer.classList.remove('toolbar_btn_loading')
            });
    }
    newReport(args) {
        this.componentObject.setProperties({
            dataSourceSettings: {
                columns: [],
                rows: [],
                values: [],
                filters: []
            }
        }, false);
    }

    // Save reports
    // This action is triggered even at initial, to ensure that the default
    // report (coming from the JS settings) is saved wherever we save the reports
    saveReport(args) {
        if (!args.report || !args.reportName || args.reportName === '') {
            return;
        }

        this.modifyReportsList((pivotviewReports) => {
            let report = JSON.parse(args.report);
            report.dataSourceSettings.dataSource = [];
            report.pivotValues = [];
            args.report = JSON.stringify(report);

            let reportExists = false;
            for (let i = 0; i < pivotviewReports.length; i++) {
                if (pivotviewReports[i].reportName === args.reportName) {
                    pivotviewReports[i].report = args.report;
                    reportExists = true;
                    break;
                }
            }
            if (!reportExists) {
                pivotviewReports.push(args);
            }

            return pivotviewReports;
        });
    }

    renameReport(args) {
        this.modifyReportsList((pivotviewReports) => {
            // Remove existing report with the new name of the current report
            if (args.isReportExists) {
                for (let i = 0; i < pivotviewReports.length; i++) {
                    if (pivotviewReports[i].reportName === args.rename) {
                        pivotviewReports.splice(i, 1);
                        break;
                    }
                }
            }

            // Rename the current report
            for (let i = 0; i < pivotviewReports.length; i++) {
                if (pivotviewReports[i].reportName === args.reportName) {
                    pivotviewReports[i].reportName = args.rename;
                    break;
                }
            }

            return pivotviewReports;
        });
    }

    removeReport(args) {
        this.modifyReportsList((pivotviewReports) => {
            for (let i = 0; i < pivotviewReports.length; i++) {
                if (pivotviewReports[i].reportName === args.reportName) {
                    pivotviewReports.splice(i, 1);
                    break;
                }
            }

            return pivotviewReports;
        });
    }

    fetchReport(args) {
        let reportsNames = [];

        const localPivotviewReports = this.getLocalPivotviewReports();
        if (localPivotviewReports !== false) {
            localPivotviewReports.map(function (pivotviewReport) {
                reportsNames.push(pivotviewReport.reportName);
            });
        }

        args.reportName = reportsNames;
    }

    loadReport(args) {
        const pivotviewReports = this.getLocalPivotviewReports();
        for (let i = 0; i < pivotviewReports.length; i++) {
            if (pivotviewReports[i].reportName === args.reportName) {
                let report = JSON.parse(pivotviewReports[i].report);
                report.dataSourceSettings.dataSource = this.componentObject.dataSourceSettings.dataSource;
                this.componentObject.dataSourceSettings = report.dataSourceSettings;
                break;
            }
        }
    }

    /**
     * @todo: Not sure if this combined way of getting all fields is correct.
     */
    getFieldsSettings() {
        let fieldsSettings = [];
        if (!this.componentObject.hasOwnProperty('dataSourceSettings')) {
            return fieldsSettings;
        }
        for (const settingName in this.componentObject.dataSourceSettings) {
            if (!['columns', 'fieldMapping', 'filters', 'rows', 'values'].includes(settingName)) {
                return;
            }
            this.componentObject.dataSourceSettings[settingName].forEach(setting => {
                // Collect fields from FieldOptionsModel[] type models
                // TODO: Not sure if there's a better way to check if filtering is allowed
                fieldsSettings.push(this.buildFieldSettingsElement(setting.name??undefined, setting.dataType??undefined, true));
            });
        }
        if (this.componentObject.dataSourceSettings.hasOwnProperty('formatSettings')) {
            this.componentObject.dataSourceSettings.formatSettings.forEach(field => {
                // Collect fields from FormatSettingsModel[] type models
                // TODO: Not sure if there's a better way to check if filtering is allowed
                fieldsSettings.push(this.buildFieldSettingsElement(field.name??undefined, field.type??undefined, true));
            });
        }

        return fieldsSettings;
    }

    buildDataManager () {
        const dataManager = super.buildDataManager();

        dataManager.adaptor = new AnalysisDataAdaptor(this.getFieldsSettings.bind(this));

        return dataManager;
    }

    getRowDataByTarget(targetElement) {
        throw new Error(`Method "${this.getRowDataByTarget.name}" is not supported yet!`);
    }
}
