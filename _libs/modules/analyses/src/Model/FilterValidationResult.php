<?php

namespace Module\Analyses\Model;

class FilterValidationResult
{
    private bool $valid;
    private ?string $errorLevel;
    private ?string $messageText;
    private ?string $filterName;

    // TODO: The message could be an object containing the data to generate it,
    //       but it's not yet parsed through Smarty and it gets parsed when the getMessageText() method is called.
    //       This optimizes execution by deferring it until the final moment.
    public function __construct(bool $valid, ?int $errorLevel = null, ?string $messageText = null, ?string $filterName = null)
    {
        $this->setValid($valid);
        $this->setErrorLevel($errorLevel);
        $this->setMessageText($messageText);
        $this->setFilterName($filterName);
    }

    private function setValid(bool $valid): void
    {
        $this->valid = $valid;
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    private function setErrorLevel(?string $errorLevel): void
    {
        $this->errorLevel = $errorLevel;
    }

    public function getErrorLevel(): ?string
    {
        return $this->errorLevel;
    }

    private function setMessageText(?string $messageText): void
    {
        $this->messageText = $messageText;
    }

    public function getMessageText(): ?string
    {
        return $this->messageText;
    }

    public function hasMessages() : bool
    {
        return isset($this->messageText);
    }

    private function setFilterName(?string $filterName): void
    {
        $this->filterName = $filterName;
    }

    public function getFilterName(): ?string
    {
        return $this->filterName;
    }
}
