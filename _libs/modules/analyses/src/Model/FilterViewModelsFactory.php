<?php

namespace Module\Analyses\Model;

use Exception;
use InvalidArgumentException;

// TODO: Move all factories outside the Models directory
class FilterViewModelsFactory
{
    /**
     * @param array $filtersParams
     * @param array|null $filtersValues
     * @return FilterViewModel[]
     * @throws Exception
     */
    public function __invoke(array $filtersParams, array $filtersValues = null): array
    {
        return $this->createFilterViewModels($filtersParams, $filtersValues);
    }

    /**
     * @param array $filtersParams
     * @param array|null $filtersValues
     * @return FilterViewModel[]
     * @throws Exception
     */
    private function createFilterViewModels(array $filtersParams, array $filtersValues = null): array
    {
        $filterViewModels = [];
        foreach ($filtersParams as $filterParams) {
            try {
                $filterType = $filterParams['type'];
                $filterName = $filterParams['name'];

                switch ($filterType) {
                    case 'autocompleter':
                        $filterViewModel = FilterViewModelAutocompleter::create($filterName, $filterParams, $filtersValues);
                        break;
                    default:
                        $filterViewModel = FilterViewModel::create($filterType, $filterName, $filterParams, $filtersValues);
                        break;
                }

                if (isset($filterParams['sub_filters'])) {
                    $subFilterValue = null;
                    if (isset($filterParams['sub_filters']['name']) && isset($filtersValues[$filterParams['sub_filters']['name']])) {
                        $subFilterValue = $filtersValues[$filterParams['name']];
                    }
                    $filterViewModel->setSubFilterViewModels(
                        $this->createFilterViewModels($filterParams['sub_filters'], $subFilterValue)
                    );
                }
                $filterViewModels[] = $filterViewModel;
            } catch (InvalidArgumentException $e) {
                throw new Exception("Failed to create filter!", $e->getCode(), $e);
            }
        }

        return $filterViewModels;
    }
}
