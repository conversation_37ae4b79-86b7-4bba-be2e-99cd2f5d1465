<?php

namespace Module\Analyses\Model;

use Exception;
use Registry;

// TODO:

/**
 * @todo Resolve multiple definitions of the \Stringable interface.
 * @todo Add support for placeholder values of type object, int, etc.
 */
abstract class AbstractPlaceholder implements \Stringable, \ArrayAccess, \Countable, \Iterator
{
    private static Registry $registry;

    private const TYPE = 'value';

    private string $name;

    /**
     * @var mixed
     */
    private $value;

    /**
     * @var mixed
     */
    protected $processedValue;

    private array $keys;

    private int $position = 0;

    public function __construct(string $name, $value)
    {
        $this->setName($name);
        $this->setValue($value);
    }

    public static function setRegistry(Registry $registry): void
    {
        self::$registry = $registry;
    }

    protected static function getRegistry(): Registry
    {
        return self::$registry;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getType() : string
    {
        return self::TYPE;
    }

    /**
     * @param mixed $value
     * @return void
     */
    private function setValue($value): void
    {
        $this->value = $value;
    }

    /**
     * @return mixed
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * @param mixed $processedValue
     * @return void
     */
    protected function setProcessedValue($processedValue): void
    {
        $this->processedValue = $processedValue;
    }

    /**
     * @return mixed
     */
    protected function getProcessedValue()
    {
        if (isset($this->processedValue)) {
            return $this->processedValue;
        }

        // By default, no processing is done.
        $this->setProcessedValue($this->getValue());

        return $this->processedValue;
    }

    private function setKeys(array $keys): void
    {
        $this->keys = $keys;
    }

    private function getKeys(): array
    {
        if (!isset($this->keys)) {
            $this->keys = array_keys($this->getProcessedValue());
        }

        return $this->keys;
    }

    /*
     * String implementation.
     */
    public function __toString()
    {
        return (string)$this->getProcessedValue();
    }

    /*
     * Array implementation.
     */
    public function offsetExists($offset): bool
    {
        return array_key_exists($offset, $this->getProcessedValue());
    }

    public function offsetGet($offset)
    {
        return $this->getProcessedValue()[$offset] ?? null;
    }

    public function offsetSet($offset, $value): void
    {
        $processedValue = $this->getProcessedValue();
        $keys = $this->getKeys();

        if (is_null($offset)) {
            $processedValue[] = $value;
            $keys[] = array_key_last($processedValue);
        } else {
            $processedValue[$offset] = $value;
            if (!in_array($offset, $keys)) {
                $keys[] = $offset;
            }
        }

        $this->setProcessedValue($processedValue);
        $this->setKeys($keys);
    }

    /**
     * @param $offset
     * @return void
     * @throws Exception
     */
    public function offsetUnset($offset): void
    {
        $processedValue = $this->getProcessedValue();
        unset($processedValue[$offset]);
        $this->setProcessedValue($processedValue);

        $keys = $this->getKeys();
        $offsetKey = array_search($offset, $keys);
        if ($offsetKey === false) {
            throw new Exception("Offset \"{$offset}\" not found!");
        }
        unset($keys[$offsetKey]);
        $this->setKeys(array_values($keys));
    }

    public function current()
    {
        return $this->offsetGet($this->position);
    }

    public function next(): void
    {
        ++$this->position;
    }

    public function key()
    {
        return $this->getKeys()[$this->position];
    }

    public function valid(): bool
    {
        return $this->offsetGet($this->position) !== null;
    }

    public function rewind(): void
    {
        $this->getKeys();
        $this->position = 0;
    }

    public function count(): int
    {
        return count($this->getKeys());
    }
}
