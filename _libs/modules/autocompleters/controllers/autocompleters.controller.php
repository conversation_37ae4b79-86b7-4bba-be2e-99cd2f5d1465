<?php

class Autocompleters_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Autocompleter';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Autocompleters';

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_select',
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'ajax_select':
                $this->_select();
                break;
            case 'execute_ac_on_select_method':
                $this->_executeACOnSelectMethod();
                break;
        }
    }

    /**
     * Selects certain items by specified parameter.
     * This method prints unordered list and exits
     */
    public function _select($autocomplete = array()) {
        $request = &$this->registry['request'];

        $autocomplete = array(
            'search'             => ($request->get('search') ? $request->get('search') : array()),
            'sort'               => ($request->get('sort') ? $request->get('sort') : array()),
            'suggestions_format' => $request->get('suggestions'),
            'fill_options'       => $request->get('fill_options'),
            'filters'            => ($request->isRequested('filters') ? $request->get('filters') : array()),
            'unique'             => ($request->isRequested('unique') ? $request->get('unique') : ''),
            'execute_after'      => ($request->isRequested('execute_after') ? $request->get('execute_after') : ''),
            'additional_where'   => array(),
            'type'               => 'autocompleters',
            'plugin'             => array(
                'name'   => $request->get('plugin'),
                'class'  => ($request->get('plugin') ? str_replace(' ', '_', ucwords(str_replace('_', ' ', $request->get('plugin')))) . '_' : "") . 'Autocompleters',
                'method' => $request->get('plugin_search'),
                'params' => $request->get('plugin_params'),
            )
        );
        parent::_select($autocomplete);
        exit;
    }

    /**
     * Method to execute the custom function for completing fields after an autocomplete item is selected
     */
    public function _executeACOnSelectMethod() {
        $request = $this->registry['request'];

        $autocomplete = json_decode($request->get('autocomplete'), true);
        $data = json_decode($request->get('data'), true);

        // define plugin file
        $plugin_file = PH_MODULES_DIR . sprintf('autocompleters/plugins/%s/models/%s.autocompleters.factory.php', $autocomplete['plugin'], $autocomplete['plugin']);

        // define lang file
        $lang_file = PH_MODULES_DIR . sprintf('autocompleters/plugins/%s/i18n/%s/autocompleters.ini', $autocomplete['plugin'], $this->registry['lang']);

        // if no plugin exists in the file system then the further execution is skipped
        if (file_exists($plugin_file)) {
            require_once($plugin_file);
            if (file_exists($lang_file)) {
                // load lang file
                $this->loadI18NFiles($lang_file);
            }

            // define class name
            $class_name = ($autocomplete['plugin'] ? str_replace(' ', '_', ucwords(str_replace('_', ' ', $autocomplete['plugin']))) . '_' : "") . 'Autocompleters';

            $method_name = !empty($autocomplete['on_select']) && class_exists($class_name) && method_exists($class_name, $autocomplete['on_select']) ? $autocomplete['on_select'] : '';

            // call the method and get the processed results
            if ($method_name) {
                $data = $class_name::$method_name($this->registry, $autocomplete, $data);
            }
        }

        echo json_encode($data);
        exit;
    }
}

?>
