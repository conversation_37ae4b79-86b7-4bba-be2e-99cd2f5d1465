<h1>{$title|escape}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        {include file=`$theme->templatesDir`actions_box.html}
        {include file=`$theme->templatesDir`translate_box.html}
        {include file=`$theme->templatesDir`_submenu_actions_box.html}

        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              {if $project->checkPermissions('addtimesheet')}
                <div class="abs_div stopwatch_div" style="visibility: hidden;">
                  <button type="button" name="stopwatch" id="stopwatch" class="button" title="{#stop_watch#|escape}" onclick="confirmAction('stop_watch', function(el) {ldelim} stopWatch(el, 'project', {$project->get('id')}); {rdelim}, this)" style="{if !$project->get('startwatch')}display: none;{/if}"><img src="{$theme->imagesUrl}stopwatch.png" width="16" height="16" alt="" border="0" /></button><button type="button" name="startwatch" id="startwatch" class="button" title="{#start_watch#|escape}" onclick="confirmAction('start_watch', function(el) {ldelim} startWatch(el, 'project', {$project->get('id')}); {rdelim}, this)" style="{if $project->get('startwatch')}display: none;{/if}"><img src="{$theme->imagesUrl}startwatch.png" width="16" height="16" alt="" border="0" /></button>
                </div>
              {/if}
              {assign var='layouts_vars' value=$project->get('vars')}
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              {foreach from=$project->get('layouts_details') key='lkey' item='layout'}
                {if $layout.view}

                {if $layout.system || array_key_exists($layout.id, $layouts_vars)}
                <tr{if !$layout.visible} style="display: none;"{/if}>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                    </div>
                    <div class="layout_switch" {if $layout.system}onclick="toggleViewLayouts(this)" id="project_{$layout.keyword}_switch"{else}onclick="toggleLayouts(this)" id="layout_{$layout.id}_switch"{/if}>
                      <a name="project_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                    </div>
                  </td>
                </tr>
                {/if}

                {if $lkey eq 'name'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {include file=`$templatesDir`_info.html assign='info'}
                    <span {popup text=$info|escape caption=#system_info#|escape width=250}>{mb_truncate_overlib text=$project->get('name')|escape|default:"&nbsp;"}</span>
                  </td>
                </tr>
                {elseif $lkey eq 'type'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {$project->get('type_name')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'customer'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$project->get('customer')}" title="{#view#|escape}: {$project->get('customer_name')|escape}">{$project->get('customer_name')|escape|default:"&nbsp;"}</a>
                  </td>
                </tr>
                {elseif $lkey eq 'trademark'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $project->get('trademark')}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$project->get('trademark')}" title="{#view#|escape}: {$project->get('trademark_name')|escape}">{$project->get('trademark_name')|escape|default:"&nbsp;"}</a>
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'code'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {$project->get('code')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'num'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$project->get('num')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'manager'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{#required#}</td>
                  <td>
                    {if $manager && !$manager->isActivated()}
                      <span class="inactive_option" title="{#inactive_option#}"> *{$project->get('manager_name')|escape|default:'&nbsp;'}</span>
                    {else}
                      {$project->get('manager_name')|escape|default:'&nbsp;'}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'status'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {capture assign='status_name'}projects_status_{$project->get('status')}{/capture}
                    <div class="projects_status {$project->get('status')}{if $project->get('status') == 'finished'}{if $project->get('finished') === '1'}_success{elseif $project->get('finished') === '0'}_failed{/if}{/if}">{$smarty.config.$status_name|escape}
                      {if $project->get('stage')}
                        - {mb_truncate_overlib text=$project->get('stage_name') length=50}
                      {/if}
                      {if $project->get('status') != 'finished'}
                        {if !$project->checkStages()}
                          {if $project->checkPermissions('setstatus') && $layout.edit}
                            <a style="white-space: nowrap;" href="#" onclick="toggleActionOptions($('setstatus_action'));return false;">{#project_setstatus#|escape}</a>
                          {/if}
                        {else}
                          {if $project->checkPermissions('phases') && $layout.edit}
                            <a style="white-space: nowrap;" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=phases&amp;phases={$project->get('id')}">{#project_setstatus#|escape}</a>
                          {/if}
                        {/if}
                      {/if}
                    </div>
                  </td>
                </tr>
                {elseif $lkey eq 'finished_project'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {if $project->get('finished') === '0'}{#projects_substatus_finished_failed#|escape}{elseif $project->get('finished') === '1'}{#projects_substatus_finished_success#|escape}{else}&nbsp;{/if}
                  </td>
                </tr>
                {elseif $lkey eq 'date_start'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {$project->get('date_start')|date_format:#date_short#|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'date_end'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $project->get('date_end') ne '0000-00-00 00:00:00'}
                      {$project->get('date_end')|date_format:#date_short#|escape}
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'priority'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {foreach from=$priority_options item='option'}
                      {if $option.option_value eq $project->get('priority')}{$option.label|escape}{/if}
                    {/foreach}
                  </td>
                </tr>
                {elseif $lkey eq 'parent_project'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {if $parent_project}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$parent_project->get('id')}" title="{#view#|escape}: {$parent_project->get('name')|escape}">
                        <span{if !$parent_project->isActivated()} class="inactive_option" title="{#inactive_option#}"> *{else}>{/if}{mb_truncate_overlib text=$parent_project->get('name')|escape|default:"&nbsp;"}</span>
                      </a>
                    {else}
                      &nbsp;
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'referers'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {if $project->get('referers')}
                      {foreach name='i' from=$project->get('referers') key='ref_id' item='ref'}
                        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$ref_id}" target="_blank">{$ref.name|escape}{if $ref.origin eq 'cloned'} <span class="legend">({#projects_cloned#})</span>{/if}</a><br />
                      {/foreach}
                    {/if}
                  </td>
                </tr>
                {elseif $lkey eq 'finished_part'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {$project->get('finished_part')} %
                  </td>
                </tr>
                {elseif $lkey eq 'budget' && $project->get('available_planned_budget')}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$project->get('budget')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'work_period' && $project->get('available_working_hours')}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    {$project->get('work_period')|escape}
                  </td>
                </tr>
                {elseif $lkey eq 'description'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {$project->get('description')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                  </td>
                </tr>
                {elseif $lkey eq 'notes'}
                <tr id="project_{$layout.keyword}"{if $layout.visible && $layout.cookie eq 'off'} style="display: none;"{/if}>
                  <td class="labelbox">{help label_content=$layout.name|escape text_content=$layout.description|escape}</td>
                  <td class="required">{if in_array($layout.keyword, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
                  <td>
                    {$project->get('notes')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
                  </td>
                </tr>
                {elseif array_key_exists($layout.id, $layouts_vars)}
                <!-- Project Additional Vars -->
                {include file=`$templatesDir`_view_vars.html}
                {/if}

                {/if}
              {/foreach}
                {if $project->get('buttons')}
                  <tr>
                    <td colspan="3">&nbsp;</td>
                  </tr>
                  <tr>
                    <td colspan="3">
                      {strip}
                        {foreach from=$project->get('buttons') item='button'}
                          {include file=`$theme->templatesDir`input_button.html
                                  label=$button.label
                                  standalone=true
                                  name=$button.name
                                  source=$button.source
                                  disabled=$button.disabled
                                  hidden=$button.hidden
                                  width=$button.width
                                  height=$button.height}
                        {/foreach}
                      {/strip}
                    </td>
                  </tr>
                {/if}
              </table>
            </td>
          </tr>
        </table>
        {include file=`$theme->templatesDir`help_box.html}
        {include file=`$theme->templatesDir`system_settings_box.html object=$project}
      </div>
    </td>
    {if isset($side_panels)}
    <td class="side_panel_container">
      {include file=`$theme->templatesDir`_side_panel_options.html}
      {include file=`$theme->templatesDir`side_panels_box.html}
    </td>
    {/if}
  </tr>
</table>
<br />
<br />
{if isset($side_panels) && in_array('related_records', $side_panels) && $related_records_modules}
<table border="0" cellpadding="0" cellspacing="0" class="subpanel_container">
  <tr>
    <td>
      {if $smarty.cookies.projects_selected_related_tab && in_array($smarty.cookies.projects_selected_related_tab, $related_records_modules)}
        {assign var='rel_type' value=$smarty.cookies.projects_selected_related_tab}
      {else}
        {assign var='rel_type' value=$related_records_modules.0}
      {/if}
      <input type="hidden" id="rel_type" name="rel_type" value="{$rel_type}" />
      <a name="related_subpanel_project{$project->get('id')}"></a>
      {include file=`$theme->templatesDir`related_records_actions_box.html}
      <div class="m_header_m_menu scroll_box_container">
        {foreach from=$related_records_modules item=module key=model}
          <div id="{$session_params.$module}" class="rel_tab{if $rel_type eq $module} loaded{else}" style="display: none;{/if}">
            {if $rel_type eq $module}
              <script type="text/javascript">
                ajaxUpdater({ldelim}
                  link: '{$related.$module}',
                  target: '{$session_params.$module}',
                  execute_after: function() {ldelim} removeClass($('related_records_action_tabs'), 'hidden'); {rdelim} 
                {rdelim});
              </script>
            {/if}
          </div>
        {/foreach}
      </div>
    </td>
  </tr>
</table>
{/if}
