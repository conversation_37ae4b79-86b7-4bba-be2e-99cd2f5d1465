<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="projects_stages" id="projects_stages" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$project->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$project->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        {include file=`$templatesDir`_info_header.html}
        {include file='input_dropdown.html'
                 name=deadline_type
                 value=$project->get('deadline_type')
                 options=$stages_deadline_options
                 required=true
                 disabled=$project->get('started_project')
                 readonly=$project->get('started_project')
                 label=#projects_deadline#
                 onchange='showRequiredDeadlineElement(this, \'stages_by_statuses\')'}
        {if $project->get('started_project')}
          <tr>
            <td colspan="3" class="nopadding">
              {if $action eq 'revision'}
                <div id="projects_stages_by_statuses">{include file=`$templatesDir`_revision_stages_list.html exclude_messages=true}</div>
              {else}
                {include file=`$templatesDir`_stages_started_project.html}
              {/if}
            </td>
          </tr>
        {else}
          <tr>
            <td colspan="3" class="nopadding">
              <div id="projects_stages_by_statuses" style="width: 100%;">{include file=`$templatesDir`_stages_prepare_project.html}</div>
            </td>
          </tr>
          <tr>
            <td colspan="3">
            {strip}
              <button type="button" name="calculate" class="button" onclick="calculateDeadlines('projects_stages_by_statuses', 'projects_stages', 0); return false;">{#calculate#|escape}</button>
              <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>
              {include file=`$theme->templatesDir`cancel_button.html}
            {/strip}
            </td>
          </tr>
        {/if}
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
