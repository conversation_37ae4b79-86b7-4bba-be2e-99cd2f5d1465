<?php

class Projects_Add_Viewer extends Viewer {
    public $template = 'add.html';

    public function prepare() {

        // structure the layout vars into two-dimensional array
        $this->model->getLayoutVars();

        // prepare layout index
        $this->prepareLayoutIndex();

        // placeholder id of model
        $this->data['model_id'] = $this->model->get('model_id') ?: time();

        // prepare saved configurations
        $this->prepareSavedConfigurations();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            'type', $this->model->get('type'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        $date_start = array (
            'custom_id' => 'date_start',
            'name' => 'date_start',
            'type' => 'date',
            'value' => ($this->model->get('date_start')));
        $this->data['date_start'] = $date_start;

        $date_end = array (
            'custom_id' => 'date_end',
            'name' => 'date_end',
            'type' => 'date',
            'value' => ($this->model->get('date_end')));
        $this->data['date_end'] = $date_end;

        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';

        //prepare the default assignments that could be added in My nZoom
        $filters_assign = array();
        $filters_assign['type'] = $this->model->get('type');

        $default_assignments = $this->registry['currentUser']->getDefaultAssignments('projects', $filters_assign);

        //prepare assignments array
        if ($this->registry['request']->isPost() || empty($default_assignments)) {
            $assignments_type = $this->model->get('assignments_type');
            $departments_assignments = $this->model->get('departments_assignments');
            $users_assignments = $this->model->get('users_assignments');
        } else {
            $assignments_type = '';
            $departments_assignments = array();
            $users_assignments = array();
            $department_users_ids = array();
            foreach ($default_assignments as $default_assign) {
                $assignments_type = $default_assign['assignment_type'];
                if ($default_assign['assignment_type'] == 'Departments') {
                    $departments_assignments[] = $default_assign['assignee'];

                    //get department members if the assignment type is Departments
                    $department_user_members = Departments::getUsersIds($this->registry, array('where' => array('d.id =' . $default_assign['assignee'])));
                    if ($department_user_members) {
                        $department_users_ids = array_merge($department_users_ids, $department_user_members);
                    }
                } else if ($default_assign['assignment_type'] == 'Users') {
                    $users_assignments[] = $default_assign['assignee'];
                }
            }
            if (!empty($department_users_ids)) {
                //remove the duplicating users from the department members
                $users_assignments = array_unique($department_users_ids);
            }
        }

        //prepare users array
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('sanitize' => true,
                         'model_lang' => $this->model->get('lang'),
                         'where' => array('u.active=1'));
        $users_obj = Users::search($this->registry, $filters);

        $users_options = array();
        foreach ($users_obj as $obj) {
            $users_options[] = array(
                'active_option' => $obj->get('active'),
                'label'         => $obj->get('firstname') . ' ' . $obj->get('lastname'),
                'value'         => $obj->get('id'));
        }
        $this->data['manager'] = $users_options;

        //prepare users array
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('sanitize' => true,
                         'model_lang' => $this->model->get('lang'),
                         'where' => array('u.hidden = 0',
                                          'u.active = 1'),
                         'sort' => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'));
        $users_obj = Users::search($this->registry, $filters);

        $users_optgroups = array('normal_users' => array(), 'portal_users' => array());
        $m_value = array();
        foreach ($users_obj as $obj) {
            if (is_array($users_assignments) && in_array($obj->get('id'), $users_assignments)) {
                $m_value[] = $obj->get('id');
            }

            $optgroup_label = ($obj->get('is_portal')) ? 'portal_users' : 'normal_users';
            $users_optgroups[$optgroup_label][] = array(
                    'label'        => $obj->get('firstname') . ' ' . $obj->get('lastname'),
                    'option_value' => $obj->get('id'),
                    'value'        => (is_array($users_assignments) && in_array($obj->get('id'), $users_assignments)) ? $obj->get('id') : '');
        }

        if (!empty($users_optgroups['normal_users'])) {
            usort($users_optgroups['normal_users'], array('parent', 'sortAssignments'));
        }
        if (!empty($users_optgroups['portal_users'])) {
            usort($users_optgroups['portal_users'], array('parent', 'sortAssignments'));
        }
        $users = array (
            'name'     => 'users_assignments',
            'type'     => 'checkbox_group',
            'label'    => $this->i18n('projects_assignments'),
            'help'     => $this->i18n('projects_assignments'),
            'optgroups'  => $users_optgroups,
            'controll_all' => true,
            'value'    => $m_value);
        $this->data['users'] = $users;

        //prepare departments array
        $filters = array('model_lang' => $this->model->get('lang'));
        $departments_obj = Departments::getTree($this->registry, $filters);

        $departments_options = array();
        $m_value = array();
        foreach ($departments_obj as $obj) {
            $departments_options[] = array(
                'level'        => $obj->get('level'),
                'label'        => $obj->get('name'),
                'option_value' => $obj->get('id'),
                'active'       => $obj->isActivated(),
                'deleted'      => $obj->isDeleted(),
                'value'        => (isset($departments_assignments[$obj->get('id')]) ? $obj->get('id') : ''));
            if (is_array($departments_assignments) && in_array($obj->get('id'), $departments_assignments)) {
                $m_value[] = $obj->get('id');
            }
        }
        $departments = array (
            'name'     => 'departments_assignments',
            'type'     => 'checkbox_tree',
            'label'    => $this->i18n('projects_assignments'),
            'help'     => $this->i18n('projects_assignments'),
            'options'  => $departments_options,
            'controll_all' => true,
            'value'    => $m_value);
        $this->data['departments_assignments'] = $departments;

        $this->data['assignments_type'] = $assignments_type;
        $this->registry['include_tree'] = true;

        require_once PH_MODULES_DIR . 'projects/models/projects.dropdown.php';
        $this->data['priority_options'] = Projects_Dropdown::getPriorities(array($this->registry));

        //options for project's progress in percents
        foreach (range(0, 100, 10) as $i) {
            $this->data['finished_part_options'][] = array('value' => $i, 'label' => $i . ' %');
        }

        $parent_project_id = $this->model->get('parent_project');
        if ($parent_project_id) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'sanitize' => true,
                             'where' => array('p.id = ' . $parent_project_id));
            $parent_project = Projects::searchOne($this->registry, $filters);
            if ($parent_project) {
                $this->model->set('parent_project_name', $parent_project->get('name'), true);
                $this->model->set('parent_project_code', $parent_project->get('code'), true);
            } else {
                $parent_project_id = 0;
            }
        }
        if (!$parent_project_id) {
            // clearall related properties
            $this->model->set('parent_project_code', '', true);
            $this->model->set('parent_project_name', '', true);
            $this->model->set('parent_project', '', true);
        }

        //prepare customer
        $customer_id = $this->model->get('customer');
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->model->set('customer_code', $customer->get('code'), true);
                $this->model->set('customer_name', $customer_name, true);
                $this->model->set('customer', $customer_id, true);
                if (!$this->model->get('trademark')) {
                    $this->model->set('trademark', $customer->get('main_trademark'), true);
                }
            } else {
                $customer_id = 0;
            }
        }
        if (!$customer_id) {
            // clear all related properties
            $this->model->set('customer_code', '', true);
            $this->model->set('customer_name', '', true);
            $this->model->set('customer', '', true);
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare trademark
        $trademark_id = $this->model->get('trademark');
        if ($trademark_id) {
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('n.deleted IS NOT NULL',
                                              'n.id = ' . $trademark_id));
            //flag to search in customers trademarks
            $filters['session_param'] = 'filter_trademark_nomenclature';
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->model->set('trademark', $trademark_id, true);
                $this->model->set('trademark_name', $nomenclature->get('name'), true);
            } else {
                $trademark_id = 0;
            }
        }
        if (!$trademark_id) {
            // clear all related properties
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        // get additional required fields
        $fields = $this->registry['config']->getParamAsArray($this->module, 'validate_' . $this->model->get('type'));
        $this->data['required_fields'] = array_filter($fields, function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; });

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('projects_add_new'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
