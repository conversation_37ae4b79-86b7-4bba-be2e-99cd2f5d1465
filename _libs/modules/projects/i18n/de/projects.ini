project = Projekt
projects = Projekte
projects_name = Name
projects_customer = Vertragspartner
projects_description = Beschreibung
projects_notes = Notizen
projects_status = Status
projects_status_phase = Status/Phase
projects_documents = Dokumente
projects_tasks = Aufgaben
projects_code = Kode
projects_num = Nummer
projects_status_active = Aktiv
projects_status_closed = Geschlossen
projects_type = Typ
projects_type_section = Abschnitt
projects_date_start = Beginn
projects_date_end = Ende
projects_priority = Priorität
projects_parent_project = Subprojekt von
projects_referers = Bezug zu Projekten
projects_manager = Leiter
projects_assignments = Teilnehmer
projects_assignments_help = (Sie können nur Abteilung/en oder nur einen oder mehrere Benutzer wählen)
projects_finished_part = % Durchführung
projects_budget = Geplantes Budget
projects_work_period = Arbeitskraftstunden
projects_relatives_action = Bezug zu anderen Projekten
projects_group = Gruppe
projects_charge_in = Supervisor
projects_count_documents = Dokumente
projects_count_tasks = Aufgaben
projects_status_btn = Status
project_setstatus = Status wechseln
projects_comment = Kommentar
projects_status_change_comment = Status wechseln
projects_finished_project = Beendet
projects_remind = Erinnerung
projects_activity = Tätigkeiten
projects_active = Aktivieren
projects_added_attachments = hinzugefügte Dateien
projects_deleted_attachments = Gelöschte Dateien
projects_file_not_exist = Diese Datei existiert nicht!
projects_all_projects = alle Projekte
projects_name_code = [Kode] Name
projects_customer_name_code = [Kode] Vertragspartner
projects_action_email = E-Mails
projects_communications = Kommunikation
projects_comments = Kommentare
projects_emails = E-Mails
projects_minitasks = Mini-Aufgaben
projects_model_id = Um Datensatz
projects_deadline = Frist
projects_assigned_to = Auftragnehmer
projects_severity = Priorität
projects_tags = Tags
projects_pattern = Vorlage

projects_generated_filename = Datei
projects_generated_add_new = Neu hinzufügen
projects_generated_revision = Version
projects_generate_revision_title = Name der Version
projects_generate_revision_description = Beschreibung der Version
projects_generated_get_revision = Daten aus Version nehmen
projects_generated_save_revision = Version ersetzen

projects_date = Datum
projects_added = Hinzugefügt am
projects_added_by = hinzugefügt von
projects_modified = geändert am
projects_modified_by = geändert von

projects_add_new = %s hinzufügen
projects_edit = %s editieren
projects_view = %s einsehen
projects_translate = %s übersetzen
projects_add_legend = Projektdaten hinzufügen
projects_system_settings = Systemeinstellungen
projects_relatives = Links von %s
projects_attachments = Dateien zu %s
projects_generate = Datei gem. Vorlage von %s erstellen
projects_assign = Zuweisung von %s
projects_timesheets = Berichte zu %s
projects_history = Historie von %s
projects_history_activity = Aktivität
projects_history_legend2 = Falls Sie eine ausführliche Beschreibung der für das Projekt vorgenommenen Bearbeitung einsehen möchten, einfach die von ihnen gewünschte Zeile auf der unteren Tabelle anklicken. Die Daten für die gewählte Zeile erscheinen in der zweiten Tabelle. Erscheinen keine Daten in der zweiten Tabelle, so heisst das, dass diese Tätigkeit nicht geprüft wurde.
projects_assign_title = Zuweisungen für
projects_assign_users_title = Zuweisungen für Benutzer
projects_assign_departments_title = Zuweisungen für Abteilungen
projects_assign_btn = Zuweisen
projects_assign_change = Zuweisungen ändern
projects_assigned = Teilnehmer
projects_manipulate_phases_data = Phase bearbeiten
projects_stage_deadline_durability = Geplanter Termin/Dauer
projects_stage_deadline = Geplanter Termin
projects_stage_started_at_from = Angefangen am/von
projects_stage_ended_at_from = Beendet am/von
projects_stage_date_finish = beenden am
projects_stage_finished_stage = beendete Phase
projects_stage_active_stage =
projects_stage_actual_durability_late = gedauert
projects_stage_expired = Projektphase abgelaufen
projects_stage_expired_legend = Phase abgelaufen! Frist der Phase ist abgelaufen am
projects_stage_calculated_deadline_note =

projects_activity_name = Name
projects_activity_description = Beschreibung
projects_activity_finished_at = beendet am
projects_activity_finished_at_by = beendet am/von
projects_past_activities_info = Tätigkeiten zu früheren Phasen
projects_activities_finished = beendet
projects_activities_not_finished = nicht beendet

projects_days = Tage
projects_hours = Stunden

projects_add_customer = neuer Vertragspartner hinzufügen
projects_search_customer = Suche/Wahl eines Vertragspartners

projects_stage = Phase
projects_stages = Phasen von %s
projects_create = Erstellen
projects_document_type = Typ des Dokuments
projects_task_type = Aufgabentyp
projects_document = Dokument
projects_task = Aufgabe
projects_event = Ereignis
projects_minitask = Mini-Aufgabe
projects_documents_incoming = eingehende
projects_documents_outgoing = ausgehende
projects_documents_internal = interne
projects_stage_current = gegenwärtige Phase
projects_substage_current = gegenwärtige Teilphase
projects_stage_finished = beendet
projects_stage_last_finished = Das Projekt ist beendet
projects_stages_all_finished = Alle Projektphasen sind beendet
projects_next_stage = nächste Phase
projects_stage_new = keine angefangene Phase
projects_stage_in = Das Projekt befindet sich nicht in Phase
projects_start_stage = Erste Phase starten
projects_start_next_stage =
projects_no_deadline = kein Termin
projects_no_deadline_date = Termin (Datum)
projects_no_deadline_days_hours = Frist (Tage, Stunden)
projects_start_stage_date = Anfangsdatum der ersten Phase
projects_start_next_stage_date =
projects_stage_current_responsible = Supervisor
projects_final_deadline = Schlussfrist
projects_total_working_hours = Insgesamt
projects_predicted_resources = für das Projekt vorgesehene Ressourcen
projects_stage_finish = Phase beenden
projects_stage_revision = Revision
projects_late = Verspätung des Projekts
projects_over_deadline = Die Phase ist abgelaufen
projects_before_deadline = Die Phase wurde vor der Schlussfrist beendet.
projects_add_project = neues Projekt hinzufügen
projects_search_project = Projektsuche/-wahl
projects_stage_complete_activity = Tätigkeiten beenden
projects_stages_days =
projects_stages_hours =

projects_reminder_email = mit e-mail
projects_reminder_toaster = online
projects_reminder_both = beides
projects_reminder_event_name = Erinnerung an Projekt %s

projects_status_planning = Vorbereitung auf Durchführung
projects_status_progress = Vorbereitung
projects_status_control = Kontrolle
projects_status_finished = beenden am
projects_substatus_finished_success = erfolgreich beendet
projects_substatus_finished_failed = fehlgeschlagen

projects_all_finished = alle beendeten
projects_all_finished_success = erfolgreich beendete
projects_all_finished_failed = fehlgeschlagene

projects_stages_deadline_planning = Vorbereitung auf Durchführung
projects_stages_deadline_progress = Durchführung
projects_stages_deadline_control = Kontrolle
projects_stages_deadline_finished = Beenden
projects_stages_limit_hours_planning = Vorbereitung auf Durchführung
projects_stages_limit_hours_progress = Durchführung
projects_stages_limit_hours_control = Kontrolle
projects_stages_limit_hours_finished = Beenden
projects_stages_limit_days_planning = Vorbereitung auf Durchführung
projects_stages_limit_days_progress = Durchführung
projects_stages_limit_days_control = Kontrolle
projects_stages_limit_days_finished = Beenden
projects_stages_name_planning = Vorbereitung auf Durchführung
projects_stages_name_progress = Durchführung
projects_stages_name_control = Kontrolle
projects_stages_name_finished = Beenden

projects_log_add = %s fügt ein Projekt hinzu
projects_log_edit = %s editiert ein Projekt
projects_log_translate = %s übersetzt ein Projekt
projects_log_managevars = %s editiert Projektdaten
projects_log_activate = %s aktiviert ein Projekt
projects_log_deactivate = %s deaktiviert ein Projekt
projects_log_delete = %s löscht ein Projekt
projects_log_restore = %s wiederherstellt ein Projekt
projects_log_clone = %s klont ein Projekt von %s ("%s")
projects_log_stages = %s plant Phasen/Subphasen für Projekt
projects_log_start_stages = %s startet Projektphasen
projects_log_start_stage = %s startet nächste Projektphase
projects_log_finish_stage = %s endet Projektphase
projects_log_revision_project = %s überprüft die Projektphasen
projects_log_status = %s ändert den Projektstatus (Status: %s)
projects_log_assign = %s ordnet ein Projekt zu
projects_log_add_attachments = %s fügt eine angehängte Datei für Projekt hinzu
projects_log_del_attachments = %s löscht eine angehängte Datei für Projekt
projects_log_multistatus = %s ändert den Projektstatus (Status: %s)
projects_log_tag = %s ändert Tags des Projekts
projects_log_multitag = %s ändert Tags des Projekts
projects_log_email = %s sendet eine E-Mail für Projekt
projects_log_receive_email = %s receives project e-mail
projects_log_receive_email_detailed = There is an email from %s sent
projects_log_add_comment = %s fügt einen Kommentar für Projekt hinzu
projects_log_edit_comment = %s editiert einen Kommentar für Projekt
projects_log_add_minitask = %s fügt eine Mini-Aufgabe zum Projekt hinzu
projects_log_edit_minitask = %s bearbeitet eine Mini-Aufgabe zum Projekt
projects_log_status_minitask = %s wechselt den Status der Mini-Aufgabe zum Projekt
projects_log_multistatus_minitask = %s wechselt den Status der Mini-Aufgabe zum Projekt
projects_log_generate = %s erstellt eine Datei für ein Projekt durch Verwendung der Vorlage "%s"%s
projects_log_generate_delete = %s löscht eine erstellte Datei für Projekt (Status: %s, %s)
projects_log_print = %s druckt ein Projekt durch Verwendung der Vorlage "%s"%s
projects_log_multiprint = %s druckt ein Projekt durch Verwendung der Vorlage "%s"%s
projects_log_add_timesheet = %s berichtete %d Minuten
projects_log_edit_timesheet = %s berichtete %d Minuten

projects_logtype_add = Hinzufügen
projects_logtype_edit = Editieren
projects_logtype_translate = Übersetzung
projects_logtype_managevars = Daten editieren
projects_logtype_activate = Aktivieren
projects_logtype_deactivate = Deaktivieren
projects_logtype_delete = Löschen
projects_logtype_restore = Wiederherstellen
projects_logtype_clone = Klonen
projects_logtype_stages = Phasen
projects_logtype_start_stages = Phasen starten
projects_logtype_start_stage = Phasen
projects_logtype_finish_stage = Phasen
projects_logtype_revision_project = Projekt überprüfen
projects_logtype_status = Status ändern
projects_logtype_assign = Zuweisen
projects_logtype_add_attachments = Datei hinzufügen
projects_logtype_del_attachments = Datei löschen
projects_logtype_multistatus = Status mehrfach ändern
projects_logtype_tag = Tags ändern
projects_logtype_multitag = Tags mehrfach ändern
projects_logtype_email = E-Mail senden
projects_logtype_receive_email = Received e-mail
projects_logtype_add_comment = Kommentar hinzufügen
projects_logtype_edit_comment = Kommentar editieren
projects_logtype_add_minitask = Mini-Aufgabe hinzufügen
projects_logtype_edit_minitask = Mini-Aufgabe editieren
projects_logtype_status_minitask = Status der Mini-Aufgabe wechseln
projects_logtype_multistatus_minitask = Status der Mini-Aufgabe mehrfach ändern
projects_logtype_generate = Datei erstellen
projects_logtype_generate_delete = Datei löschen
projects_logtype_print = Druck
projects_logtype_multiprint = Mehrfach drucken
projects_logtype_add_timesheet = Bericht hinzufügen
projects_logtype_edit_timesheet = Bericht editieren

projects_system_task_type = Bericht über
projects_no_system_task = Berichte über das angegebene Projekt können nicht hinzugefügt werden

projects_cloned = geklont

projects_customers_info = Angaben zum Vertragspartner
projects_main_contact_person = Hauptkontaktperson
projects_customers_contacts = Kontaktdaten
projects_last_customers_records = Letzte %s zum Vertragspartner

projects_add_custom_stage = Phase für dieses Projekt hinzufügen

projects_show_full_assignments_list = Vollständige Liste der Zuweisungen anzeigen
projects_hide_full_assignments_list = Vollständige Liste der Zuweisungen ausblenden

audit_vars = Historie der am Projekt vorgenommenen Bearbeitungen
audit_legend = Nähere Angaben zu der von %s am %s vorgenommenen Änderung

var_type = Typ
var_name = Name
var_value = Text
old_value = Vorheriger Text

message_projects_add_success = %s erfolgreich hinzugefügt.
message_projects_edit_success = %s erfolgreich bearbeitet.
message_projects_translate_success = %s erfolgreich übersetzt.
message_projects_clone_success = %s erfolgreich geklont.
message_projects_assign_success = %s erfolgreich zugewiesen.
message_projects_status_success = Status von %s erfolgreich gewechselt.

message_projects_stage_add_success = Phasendaten erfolgreich hinzugefügt.
message_projects_stage_edit_success = Phasendaten erfolgreich bearbeitet.
message_projects_stages_edit_success = Daten der gewählten Phasen erfolgreich bearbeitet.
message_projects_stage_finish_success = Phase erfolgreich beendet.
message_projects_stage_revisioned = Phasen erfolgreich überprüft.

error_projects_stage_finish_failed = Die Phase wurde nicht beendet!
error_projects_stage_date_finish_missing = Bitte Datum des Beendens der Phase eingeben!
error_projects_stage_date_finish_future = Datum und Uhrzeit, die vor dem gegenwärtigen Datum liegen, eingeben!
error_projects_stage_date_finish_before_start = Bitte Datum des Beendens, das nach dem Beginn der Phase liegt, eingeben!
error_projects_stage_date_start_before_previous_finish =
error_projects_complete_at_least_one_stage =
error_projects_stage_start_date_missing =
error_projects_stage_start_date_mismatch =
error_projects_stage_revisioned_failed = AUSFÄLLE Überarbeitungsphase für das Projekt!

message_projects_substage_add_success = Daten über Subphase erfolgreih hinzugefügt.
message_projects_substage_edit_success = Daten über Subphase erfolgreih bearbeitet.
message_projects_substage_finish_success = Subphase erfolgreich beendet.

message_projects_stages_vars_saved = Die Daten der Phase/Subphase wurden erfolgreich gespeichert!
message_projects_stages_move_next_substage = Erfolgreicher Übergang zur nächsten Subphase.
message_projects_stages_move_next_stage = Erfolgreicher Übergang zur nächsten Phase.

message_projects_comments_add_success = Kommentar erfolgreich hinzugefügt.
error_comments_add_failed = Kommentar wurde nicht hinzugefügt!

message_projects_warning_over_deadline = Die Fristen der Phasen übersteigen die in den Projekt angegebenen Fristen!
message_projects_warning_over_budget = Das Budget der Phasen übersteigt das für das Projekt vorgesehene Budget!
message_projects_warning_over_working_hours = Die Summe der Arbeitskraftstunden für die Phasen übersteigt die für das Projekt vorgesehenen Arbeitskraftstunden!

message_projects_stage_activities_finish_success = Die Phasentätigkeiten sind erfolgreich beendet!
message_projects_warning_no_activities_to_be_finished = Sie haben keine zu beendenden Tätigkeiten gewählt!
error_projects_stage_activities_finish_failed = Die Phasentätigkeiten sind nicht beendet!

message_projects_reminder_edit_success = Erinnerung erfolgreich editiert
message_projects_reminder_add_success = Erinnerung erfolgreich hinzugefügt
error_projects_reminder_failed = Fehler beim Speichern der Erinnerung

message_projects_file_deleted_success = Datei erfolgreich gelöscht!
error_projects_file_deleted_failed = Fehler beim Löschen der Datei!

message_projects_email_sent_success = Mitteilungsschreiben %s erfolgreich gesendet
error_projects_send_email = Mitteilungsschreiben %s nicht erfolgreich gesendet

message_projects_multistatus_success = Status von %s erfolgreich verändert.
error_projects_multistatus_failed = Status von %s NICHT ERFOLGREICH verändert.
warning_projects_change_status_not_all = Der Status einiger %s wurde nicht gewechselt, weil ein Statuswechsel ungültig ist, es gibt nicht beendete Mini-Aufgaben oder Sie besitzen keine Rechte für Statuswechsel.

warning_projects_relative_child = Kann mit einem geerbten Projekt nicht verbunden werden

error_projects_multiprint_failed = Fehler beim mehrfachen Druck von %s
error_projects_print_invalid_pattern = Keine Vorlage bzw. eine nicht existierende Vorlage wurde gewählt

error_print_no_default_pattern = Keine Vorlage ausgewählt
error_projects_print_document = Die Datei kann aufgrund eines Fehlers beim Erstellen eines Dokuments nicht gedruckt werden

error_different_types = Die gewählten Projekte haben unterschiedliche Typen
error_no_projects_or_deleted = Keine Projekte sind gewählt oder ein der gewählte Projekte ist gelöscht
error_projects_uncompleted_documents = Der Datensatz kann nicht beendet werden, solange zu ihm gehörende nicht beendete Dokumente vorliegen!
error_projects_uncompleted_tasks = Der Datensatz kann nicht beendet werden, solange zu ihm gehörende nicht beendete Aufgaben vorliegen!
error_projects_uncompleted_minitasks = Der Datensatz kann nicht beendet werden, solange zu ihm gehörende nicht beendete Mini-Aufgaben vorliegen!
error_projects_uncompleted_events = Der Datensatz kann nicht beendet werden, solange zu ihm gehörende nicht beendete Ereignisse vorliegen!

projects_stage_started_notify = für angefangene Phase von %s
projects_stage_finished_started_notify = für Beenden der Phase von %s und Starten einer neuen Phase
projects_stage_finished_notify = für beendete Phase von %s
projects_stages_finished_notify = für beendete Phasen von %s
projects_project_status_notify = für veränderten Status von %s
projects_project_assign_notify = für Zuweisung als Teilnehmer bei %s

error_projects_add_failed = %s NICHT ERFOLGREICH hinzugefügt.
error_projects_edit_failed = %s NICHT ERFOLGREICH bearbeitet
error_projects_translate_failed = %s NICHT ERFOLGREICH übersetzt
error_projects_clone_failed = %s NICHT ERFOLGREICH geklont.
error_projects_assign_failed = Fehler bei der Zuweisung von %s.
error_projects_status_failed = Status von %s nicht erfolgreich gewechselt.

error_no_such_project = Dieser Datensatz ist nicht verfügbar für Sie!
error_invalid_type = Ein ungültiger oder nicht aktiver Typ wurde gewählt!
error_no_available_projects_types = Sie besitzen keine Rechte zum Hinzufügen eines Projekttyps!
error_no_name_specified = Bitte %s eingeben!
error_no_customer = Bitte %s wählen!
error_invalid_trademark = Bitte einen gültigen Wert für %s wählen oder eingeben!
error_no_manager = %s wählen!
error_no_code = %s eingeben!
error_code_not_unique = Dieser %s wird bereits verwendet. Bitte einen anderen %s eingeben!
error_no_trademark = Bitte %s wählen!
error_no_date_start = Ungültiges Datum für %s!
error_no_date_end = Ungültiges Datum für %s!
error_no_priority = %s wählen!
error_no_parent_project = %s wählen!
error_no_finished_part = %s wählen!
error_no_description = %s eingeben!
error_no_notes = %s eingeben!
error_no_counter_for_this_project_type = Für das gewählte Projekttyp wurde kein Zähler gesetzt!

error_date_start = Leeres oder ungültiges Feld "%s"
error_date_start_end = Das Datum für "%s" muss vor dem Datum für "%s" liegen.
error_date_end = Leeres oder ungültiges Feld "%s"

error_projects_stages_edit_failed = Phasen zu %s NICHT ERFOLGREICH bearbeitet.
error_projects_stage_finished = Beendete Projektphase. Die darin enthaltenen Daten können nicht editiert werden.
error_projects_no_such_stage = Solch eine Phase existiert zum Projekt nicht
error_projects_no_stages = Das Projekt hat keine Phasen.
error_projects_stages_sequences = Phasen mit denselben laufenden Nummern können nicht eingegeben werden!
error_projects_start_stage_date = Das Datum und die Uhrzeit für Projektbeginn müssen vor den gegenwärtigen Datum und Uhrzeit liegen!
error_projects_complete_start_stage_date = Bitte Datum für Beginn der ersten Projektphase eingeben!
error_projects_date_filled = [%s - %s] Schlussfrist ist nicht eingegeben!
error_projects_date_invalid = Ungültiges Datum! Bitte ein Datu nach dem gegenwärtigen Datum eingeben!
error_projects_hour_filled = Eine Frist mit Tagen und Uhrzeiten wurde nicht eingegeben!
error_projects_start_no_phases = Ein Projekt kann nur nach Wahl einer Phase gestartet werden!
error_stages_days_invalid_format = Gültige Datn für die Anzahl der Tage sind nicht ausgefüllt! Bitte eine Ganzzahl größer als die Null eingeben!
error_stages_hours_invalid_format = [%s - %s] Gültige Stundenanzahl ist nicht eingegeben! Bitte eine ganze Zahl größer oder gleich der 0 und kleiner als 24 eingeben!
error_projects_latest_date_invalid = [%s - %s] Falsche Reihenfolge der Daten!
error_projects_previously_missed_date = [%s - %s] Eine vorherige Frist ist versäumt!
error_missed_stage_name = [%s] Kein Phasenname eingegeben!

message_projects_generate_success = Datei erfolgreich generiert!
error_projects_generate_document = Datei NICHT ERFOLGREICH generiert!
error_projects_generate_invalid_pattern = Keine Vorlage bzw. Eine nicht existierende Vorlage wurde gewählt

confirm_link_projects = Sind Sie sicher, dass Sie die gewählten Projekte verbinden möchten?\n "OK" zur Bestätigung klicken.

alert_link_projects = Bitte ein Projekt als Verbindung wählen.
#alert_link_projects2 = Bitte wählen Sie nur ein Projekt zum Anschluss!

error_projects_delete_related_models = Folgende Projekte können aufgrund bestehender verbundener Modelle nicht gelöscht werden: %s

opened = Geöffnet
locked = Gesperrt
closed_success = Erfolgreich beendet
closed_failed = Nicht erfolgreich beendet

low = niedrig
medium = mittel
high = hoch
#Help SECTION for label info

help_projects_name =
help_projects_customer =
help_projects_description =
help_projects_notes =
help_projects_status = Projektstatus
help_projects_documents =
help_projects_tasks =
help_projects_add_project = Sie können ein Projekt hinzufügen, welches direkt ausgefüllt wird
help_projects_search_project = Bitte das notwendige Projekt suchen und es für dieses Dokument wählen
help_projects_code = Der Projektkode wird bei den Dokumentenzählern verwendet. Wir empfehlen, dass der Kode aus Lateinbuchstaben besteht und höchstens 3-4 Symbole hat.
help_projects_status_planning = Das Projekt hat den Status <b>"VORBEREITUNG AUF DURCHFÜHRUNG"</b>.
help_projects_status_progress = Das Projekt hat den Status <b>"DURCHFÜHRUNG"</b>.
help_projects_status_control = Das Projekt hat den Status <b>"KONTROLLE"</b>.
help_projects_status_finished = Das Projekt hat den Status <b>"BEENDEN"</b>, Änderungen können derzeit nicht vorgenommen werden.
help_projects_past_activities_info = Info über beendete und nicht beendete Tätigkeiten aus früheren Phasen
