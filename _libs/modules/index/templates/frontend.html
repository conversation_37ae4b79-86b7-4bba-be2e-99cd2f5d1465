{* The div has ID which is checked to ensure that this is the index page *}
{* This check is via javascript with the checkAjaxResponse function *}
{* Do not remove this DIV's id *}
<div class="clear" id="index_page_clear_flag"></div>
{counter name='idx' assign='empty_idx' start=0 print=false}
{foreach from=$settings item='position' name='i' key='key'}
{assign var='dashlet' value=$dashlets.$key}
  {if !empty($dashlet)}
    <div id="dashlet_{$dashlet->get('id')}" class="t_table1 draggable {if preg_match('#\d+l#', $position)}drag_left{elseif preg_match('#\d+r#', $position)}drag_right{else}{cycle values='drag_left, drag_right'}{/if}{if $dashlet->get('full_width') eq 1} full_width{/if}" {if $dashlet->get('full_width') eq 1}style="width: 99%"{/if}>
      <div id="title_dashlet_{$dashlet->get('id')}" class="cal_title_bar t_caption2 t_border t_border_left hcenter">
        <div style="padding: 7px 0px; width: auto;" class="layout_switch pointer" id="content_dashlet_{$dashlet->get('id')}_switch" onclick="if (!isSubelementClicked('toggleViewLayouts')) {ldelim} toggleViewLayouts(this); processEmptyElements(true);{rdelim}">
            {capture assign='dashlet_id_cookie'}content_dashlet_{$dashlet->get('id')}_box{/capture}
            <div style="float: left; margin: 4px 5px; padding: 0;" class="switch_{if $smarty.cookies.$dashlet_id_cookie eq 'off'}expand{else}collapse{/if}"></div>
            {if $dashlet->get('description') !== ''}
            <div style="float: left; padding-left: 3px; padding-right: 3px;">
              <img src="{$theme->imagesUrl}info.png" width="16" height="16" border="0" alt="" class="help" {popup text=$dashlet->get('description')|escape:'html'|escape|nl2br|default:'&nbsp;' caption=#system_info#|escape} />
            </div>
            {/if}
            <div id="move_dashlet_{$dashlet->get('id')}" style="float: left; padding-left: 3px;">
              <a href="#">
                <img src="{$theme->imagesUrl}move.png" width="16" height="16" alt="{#draggable#|escape}" title="{#draggable#|escape}" class="icon_button" style="background: none; border: none; margin: 0;"/>
              </a>
            </div>
            {if $dashlet->get('module') ne 'plugin'}
              {if $dashlet->get('module') eq 'reports'}
                <a href="{$SCRIPT_NAME}?{$module_param}=reports&amp;report_type={$dashlet->get('controller')}">
              {else}
                {assign var='dashlet_filters' value=$dashlet->get('filters')}
                <a href="{$SCRIPT_NAME}?{$module_param}={$dashlet->get('module')}&amp;{if $dashlet->get('module') ne $dashlet->get('controller')}controller={$dashlet->get('controller')}&amp;{$dashlet->get('controller')}={else}{$dashlet->get('module')}={/if}search&amp;session_param_prefix=dashlets_{$dashlet->get('id')}{if $dashlet_filters.display}&amp;display={$dashlet_filters.display}{/if}">
              {/if}
                  <span id="title_text_dashlet_{$dashlet->get('id')}" class="t_caption2_title">{$dashlet->get('name')}</span>
                </a>
            {else}
              {if $dashlet->get('controller') eq 'calendar'}
                <a href="{$SCRIPT_NAME}?{$module_param}=calendars">
              {/if}
              <span id="title_text_dashlet_{$dashlet->get('id')}" class="t_caption2_title">{$dashlet->get('name')}</span>
              {if $dashlet->get('controller') eq 'calendar'}
                </a>
              {/if}
            {/if}

        </div>
      </div>
      <div id="content_dashlet_{$dashlet->get('id')}" class="dashlet_loader dashlet_content"{if $smarty.cookies.$dashlet_id_cookie eq 'off'} style="display: none;"{/if}>
        <script type="text/javascript">
          dashletsLoad('content_dashlet_{$dashlet->get('id')}', '{$dashlet->get("module")}', '{$dashlet->get("controller")}', '{$dashlet->get("id")}');
        </script>
      </div>
      <script type="text/javascript">
        new Zapatec.Utils.Draggable({ldelim}container:'dashlet_{$dashlet->get('id')}', 
                                     handler: 'move_dashlet_{$dashlet->get('id')}',
                                     beforeDragInit: function(){ldelim}draggableBeforeDragInit(this);{rdelim},
                                     beforeDragMove: function(){ldelim}draggablebBeforeDragMove(this);{rdelim},
                                     beforeDragEnd: function(){ldelim}draggablebBeforeDragEnd(this);{rdelim},
                                     onDragInit: function(){ldelim}draggableOnDragInit(this);{rdelim},
                                     onDragMove: function(){ldelim}draggablebOnDragMove(this);{rdelim},
                                     onDragEnd : function(){ldelim}draggableOnDragEnd(this);{rdelim}
                                    {rdelim});
      </script>
    </div>
  {/if}
{/foreach}
<div class="clear"></div>
