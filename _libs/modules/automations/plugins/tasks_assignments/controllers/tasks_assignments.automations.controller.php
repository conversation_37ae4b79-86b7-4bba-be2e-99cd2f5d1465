<?php

/**
 * Automations to handle automatic task assigning
 */
class Tasks_Assignments_Automations_Controller extends Automations_Controller {

    /**
     * Create regular tasks based on the customers tags
     *
     * param array $params - parameters for the operation
     * @return bool - the result of the operation
     */
    public function autoAssignByCustomerVars($params) {
        $settings = $this->getSettings($params);

        // get customers type which must be processed
        $customers_types = preg_split('/\s*\,\s*/', $settings['customer_types']);
        $customers_types = array_filter($customers_types);

        // check the customer of the current task
        $sql = 'SELECT `type` FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id`="' . $params['model']->get('customer') . '"';
        $current_customer_type = $this->registry['db']->GetOne($sql);

        if (!in_array($current_customer_type, $customers_types)) {
            // we do not need to process this record
            return false;
        }

        // assignments types list
        $assignments_types = array('owner', 'responsible', 'observer', 'decision');
        $assignments_types = array_fill_keys($assignments_types, array('vars' => array(), 'assignees' => array()));

        $old_task = clone $params['model'];
        $old_task->unsanitize();

        // get the vars for the customer which we will need information from
        foreach ($assignments_types as $assign_type => $assign_data) {
            $old_task->getAssignments($assign_type);
            $settings_key = sprintf('task_%s_assign_%d', $assign_type, $current_customer_type);
            if (empty($settings[$settings_key])) {
                continue;
            }
            $assignments_types[$assign_type]['vars'] = preg_split('/\s*\,\s*/', $settings[$settings_key]);
            $assignments_types[$assign_type]['vars'] = array_filter($assignments_types[$assign_type]['vars']);
        }

        // get the customer model so we can use its additional variables
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $filters = array('where'    => array('c.id = \'' . $params['model']->get('customer') . '\''),
                         'sanitize' => true);
        $customer_model = Customers::searchOne($this->registry, $filters);

        $this->registry->set('get_old_vars', true, true);
        $assoc_vars = $customer_model->getAssocVars();

        $assignee_list = array();
        foreach ($assignments_types as $assign_type => $assign_data) {
            if (empty($assign_data['vars'])) {
                // if vars are not set this type of assignments is ignored
                unset($assignments_types[$assign_type]);
                continue;
            }
            foreach ($assign_data['vars'] as $var) {
                if (!isset($assoc_vars[$var]) || empty($assoc_vars[$var]['value'])) {
                    continue;
                }
                $current_assignees = array($assoc_vars[$var]['value']);
                if (is_array($assoc_vars[$var]['value'])) {
                    $current_assignees = array_filter($assoc_vars[$var]['value']);
                }
                $assignments_types[$assign_type]['assignees'] = array_merge($assignments_types[$assign_type]['assignees'], $current_assignees);
                $assignee_list = array_merge($assignee_list, $current_assignees);
            }
        }

        // check if the assignments are per customer ot per user
        if (!empty($assignee_list) && $settings['ac_search_type'] == 'customers') {
            $sql = 'SELECT `employee`, `id` FROM ' . DB_TABLE_USERS . ' WHERE `employee` IN (' . implode(',', $assignee_list) . ')' . "\n";
            $customer_users_relations = $this->registry['db']->GetAssoc($sql);
            foreach ($assignments_types as $assign_type => $assign_data) {
                foreach ($assign_data['assignees'] as $ak => $assi) {
                    if (isset($customer_users_relations[$assi])) {
                       $assignments_types[$assign_type]['assignees'][$ak] = $customer_users_relations[$assi];
                    } else {
                        unset($assignments_types[$assign_type]['assignees'][$ak]);
                    }
                }
            }
        }

        //set protocol assignments
        foreach ($assignments_types as $assign_type => $assign_data) {
            $params['model']->set('assignments_' . $assign_type, $assign_data['assignees'], true);
        }

        $this->registry['db']->StartTrans();
        $params['model']->unsanitize();
        if ($params['model']->assign(true, true)) {
            $filters = array(
                'where'                  => array("t.id = '{$params['model']->get('id')}'"),
                'model_lang'             => $params['model']->get('model_lang'),
                'sanitize'               => false,
                'skip_assignments'       => true,
                'skip_permissions_check' => true
            );
            $new_task = Tasks::searchOne($this->registry, $filters);

            foreach ($assignments_types as $assignments_type) {
                $new_task->getAssignments($assignments_type);
            }

            Tasks_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'assign',
                    'old_model'   => $old_task,
                    'model'       => $new_task,
                    'new_model'   => $new_task
                )
            );
        } else {
            // ERROR in the assigments
            $this->registry['messages']->setError($this->i18n('error_auto_assign'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }
}

?>
