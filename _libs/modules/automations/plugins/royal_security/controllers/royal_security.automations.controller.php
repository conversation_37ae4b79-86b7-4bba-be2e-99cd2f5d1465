<?php

class Royal_Security_Automations_Controller extends Automations_Controller {

    /**
     * Use incoming documents counter to set custom expenses documents number
     *
     * @param array $params - array of automation settings (fetched from the DB)
     */
    public function changeNumber($params) {
        //get document type (used to create fake document)
        $query = 'SELECT id FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' WHERE counter=1';
        $type = $this->registry['db']->GetOne($query);

        require_once PH_MODULES_DIR . 'documents/models/documents.model.php';
        $doc = new Document($this->registry, array('type' => $type, 'active' => true));
        $number = $doc->getDocFullNum();

        $query = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' SET `num`="'.$number.'" WHERE `id`=' . $params['model']->get('id');
        if ($this->registry['db']->Execute($query)) {
            $doc->counter->increment();
        }

        return true;
    }
}

?>
