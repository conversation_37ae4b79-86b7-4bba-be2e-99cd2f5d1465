plugin_profile = profile
plugin_accessory = accessory
plugin_pane = pane
plugin_material = material
error_no_nom_specified = Row %s: No specified %s
error_no_nom_found = Row %s: No %s with code %s found
error_no_color_found = Row %s: No color with code: %s cannot be added either
error_annulment_not_possible = Annulment of the payment is not possible because it is already distributed in order/s: %s

plugin_automation_cant_define_order_substatus = Not possible to define the substatus of order with ID %d!
plugin_automation_cant_change_order_substatus = Error occurred while trying to change the status of the order with ID %d! Please contact nZoom support!

# crateKssPpp
error_automations_no_quantities_to_create_incomes_reason = No completed quantities for issuing KSS!
error_automations_add_reason_failed = Adding incomes reason failed! Please, contact nZoom support!
warning_automations_create_relations_failed = An error occurred while creating relation between the order and the KSS! Please, contact nZoom support!
warning_automations_add_handover_failed = An error occurred while issuing PPP - not enough available quantities! Please issue the required documents manually when the quantities are available!
warning_automations_tag_incomes_reason_failed = An error occurred while marking the incomes reason with tag for missing PPP! Please, contact nZoom support!
message_automations_documents_issued_successfully = Successfully issued <a href="%s" target="_blank">%s</a> и <a href="%s" target="_blank">%s</a>!
message_crontab_automations_documents_issued_successfully = Successfully issued <a href="%s" target="_blank">%s %s</a> for <a href="%s" target="_blank">%s %s</a>!
warning_automations_handover_articles_not_enough_quantity = Some of the included articles are not available. Handover will be issued only for the available articles.

# unlockOrder
message_automations_annul_ppp_successful = Successfully annulled <a href="%s" target="_blank">protocol %s</a>!
warning_automations_annul_ppp_failed = Annulling <a href="%s" target="_blank">protocol %s</a> failed! Please, annul the protocol  manually!
message_automations_deactivate_kss_successful = Successfully deactivated <a href="%s" target="_blank">KSS %s</a>!
warning_automations_deactivate_kss_failed = Deactivating на <a href="%s" target="_blank">order %s</a> failed! Please, deactivate the order manually!
