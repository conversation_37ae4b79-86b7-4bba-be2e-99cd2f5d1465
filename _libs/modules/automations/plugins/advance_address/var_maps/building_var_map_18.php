<?php

/**
 * Residential buildings
 * object type 18
 * variable from report => variable in nomenclature
 */

return [
    'object_purpose' => 'object_purpose',
    'object_identifier' => 'object_identifier',
    'construction_type' => 'construction_type',
    'floor_count' => 'floor_count',
    'levels_count' => 'levels_count',
    'underground_level' => 'underground_level',
    'construction_year' => 'construction_year',
    'construction_phase' => 'construction_phase',
    'date_uve_rp' => 'date_uve_rp',
    'last_repair_year' => 'last_repair_year',
    'last_repair_year_type' => 'last_repair_year_type',
    'construction_quality' => 'construction_quality',
    'object_functionality' => 'object_functionality',
    'outer_walls' => 'outer_walls',
    'inner_walls_barriers' => 'inner_walls_barriers',
    'roof_construction' => 'roof_construction',
    'object_facade' => 'object_facade',
    'finishing_works' => 'finishing_works',
    'overall_impression' => 'overall_impression',
    'object_maintenance' => 'object_maintenance',
    'number_doorway' => 'number_doorway',
    'access_control' => 'access_control',
    'cultural_monument' => 'cultural_monument',
    'paring_slots' => 'paring_slots',
    'outer_door' => 'outer_door',
    'surrounding_area_state' => 'surrounding_area_state',
    'electric_installation' => 'electric_installation',
    'piping_installation' => 'piping_installation',
    'sewage_installation' => 'sewage_installation',
    'heating_installation' => 'heating_installation',
    'ventilation_air_conditioning' => 'ventilation_air_conditioning',
    'gasification' => 'gasification',
    'security_installation' => 'security_installation',
    'elevator' => 'elevator',
    'fire_installation' => 'fire_installation',
    'bms_installation' => 'bms_installation',
    'structured_cabling' => 'structured_cabling',
    'photovoltaic_installation' => 'photovoltaic_installation',
    'main_source_energy_installation' => 'main_source_energy_installation',
    'green_building_certificate' => 'green_building_certificate',
    'green_building_certificate_kind' => 'green_building_certificate_kind',
    'green_building_certificate_class' => 'green_building_certificate_class',
    'green_building_certificate_num' => 'green_building_certificate_num',
    'green_building_certificate_date' => 'green_building_certificate_date',
    'green_building_certificate_validity' => 'green_building_certificate_validity',
    'energy_efficiency_no_document' => 'energy_efficiency_no_document',
    'energy_efficiency_kind_document' => 'energy_efficiency_kind_document',
    'energy_efficiency_kind_class' => 'energy_efficiency_kind_class',
    'energy_efficiency_num' => 'energy_efficiency_num',
    'energy_efficiency_date' => 'energy_efficiency_date',
    'energy_efficiency_validity_period' => 'energy_efficiency_validity_period',
    'epc' => 'epc',
    'harmful_emissions_generated' => 'harmful_emissions_generated',
    'energy_efficiency_spec_desc' => 'energy_efficiency_spec_desc',
    'spec_desc' => 'spec_desc',
    'harmful_emissions' => 'harmful_emissions',
    'harmful_emissions_info' => 'harmful_emissions_info',
    'above_ground_level' => 'above_ground_level',
    'energy_green_efficiency_source' => 'energy_green_efficiency_source',
];
