<?php

class Itg_Automations_Controller extends Automations_Controller {
    /**
     * Creates expenses (proforma)invoice from incomes one
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function createExpensesInvoice($params) {
        /** @var Finance_Incomes_Reason $base - source model is outgoing invoice or proforma */
        $base = $params['model'];

        //array that stores the relation bewteen company and customer
        // (key is DESTINATION company, value - SOURCE customer, or vice versa)
        $cc_relation = array();
        //array that stores the available company transitions
        //key is SOURCE company, value is array of all possible DESTINATION companies
        $transition_company_rules = array();
        //parse settings
        //first get the relations customer - company
        foreach ($this->settings as $k => $v) {
            if (preg_match('#^invoice_company_(\d+)$#', $k, $matches)) {
                $from_companies = preg_split('#\s*,\s*#', $v);
                foreach($from_companies as $fr) {
                    if (!array_key_exists($fr, $transition_company_rules) || !in_array($matches[1], $transition_company_rules[$fr])) {
                        $transition_company_rules[$fr][] = $matches[1];
                    }
                }
            } elseif (preg_match('#^customer_company_(\d+)$#', $k, $matches)) {
                $cc_relation[$matches[1]] = $v;
            }
        }

        if (!empty($transition_company_rules[$base->get('company')])) {
            //transition for SOURCE company exists
            $from_company = $base->get('company');
        } else {
            //incorect settings (the automation is not set to create invoice)
            return true;
        }

        if (in_array($base->get('customer'), $cc_relation)) {
            //customer is OK
            $to_company = array_search($base->get('customer'), $cc_relation);
        } else {
            //incorrect settings (the selected customer is not related to a company)
            return true;
        }
        if (!in_array($to_company, $transition_company_rules[$from_company])) {
            //transition for DESTINATION company DOES NOT exist
            return true;
        }

        // validate required data for creation of destination record
        if (empty($this->settings['office_company_' . $to_company]) ||
            empty($this->settings['payment_type_company_' . $to_company]) ||
            empty($this->settings['container_company_' . $to_company]) ||
            empty($cc_relation[$from_company])) {
            $this->registry['messages']->setWarning($this->i18n('automation_missing_settings'));
            $this->registry['messages']->insertInSession($this->registry);

            return true;
        }

        $db = &$this->registry['db'];
        $db->StartTrans();

        $container = 0;
        $company_name = '';
        if ($this->settings['payment_type_company_' . $to_company] == 'cash') {
            //get cashbox
            include_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
            $filters = array('where' => array('fcb.id = ' . $this->settings['container_company_' . $to_company]));
            $container = Finance_Cashboxes::searchOne($this->registry, $filters);
            $company_name = $container->get('company_name');
            $container = $container->get('id');
        } else {
            //get bank accounts
            include_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
            $filters = array('where' => array('fba.id IN (' . $this->settings['container_company_' . $to_company] . ')'));
            $containers = Finance_Bank_Accounts::search($this->registry, $filters);
            foreach ($containers as $c) {
                //search for the bank account with the right currency
                if ($c->get('currency') == $base->get('currency')) {
                    $container = $c->get('id');
                    $company_name = $c->get('company_name');
                    break;
                }
            }
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';

        $dest = new Finance_Expenses_Reason($this->registry, $base->getAll());
        $get_old = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $bgt2 = $base->getGT2Vars();

        $dest->unsetProperty('id', true);
        $dest->unsetProperty('num', true);
        //set destination type
        if ($base->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            $dest->set('type', PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE, true);
        } else {
            $dest->set('type', PH_FINANCE_TYPE_EXPENSES_INVOICE, true);
            // get if we have connection for the base invoice with a proforma
            // which has a corresponding expense proforma created from this automation
            $query = 'SELECT fer.id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                     '    ON frr.link_to = fir.id AND fir.type = ' . PH_FINANCE_TYPE_PRO_INVOICE . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' fer' . "\n" .
                     '    ON fer.invoice_num = fir.num AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE . "\n" .
                     'WHERE frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '    AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '    AND frr.parent_id = ' . $base->get('id');
            $eproforma = $db->GetOne($query);
            if ($eproforma) {
                $filters = array('where' => array('fer.id = ' . $eproforma));
                $eproforma = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
                $bgt2 = $eproforma->getGT2Vars();
                // set the expense proforma as parent of the expense invoice
                $dest->set('link_to', $eproforma->get('id'), true);
            }
        }
        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N .
                 ' WHERE parent_id = ' . $dest->get('type') . ' AND lang = "' . $dest->get('model_lang') . '"';
        $type_name = $db->GetOne($query);
        $dest->set('type_name', $type_name, true);
        $dest->set('invoice_num', $base->get('num'), true);
        $dest->set('customer', $cc_relation[$from_company], true);
        $dest->get('customer_name', $dest->get('company_name'), true);
        $dest->set('company', $to_company, true);
        $dest->set('company_name', $company_name, true);
        $dest->set('office', $this->settings['office_company_' . $to_company], true);
        $dest->set('payment_type', $this->settings['payment_type_company_' . $to_company], true);
        $dest->set('container_id', $container, true);
        $dest->set('admit_VAT_credit', 1, true);
        $dest->set('accounting_month', date_create($base->get('issue_date'))->format('n'), true);
        $dest->set('accounting_year', date_create($base->get('issue_date'))->format('Y'), true);
        unset($bgt2['rows']);
        $dest->set('grouping_table_2', $bgt2, true);

        $dest->calculateGT2();
        $dest->set('table_values_are_set', true, true);
        if (!$dest->save()) {
            $db->FailTrans();
        }

        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        if ($result) {
            $view_url = sprintf(
                '%s?%s=finance&amp;%s=expenses_reasons&amp;expenses_reasons=view&amp;view=%d',
                $_SERVER['PHP_SELF'], $this->registry['module_param'],
                $this->registry['controller_param'], $dest->get('id')
            );
            $this->registry['messages']->setMessage($this->i18n('automation_expenses_reason_added', array($view_url, $dest->get('type_name'), $dest->get('company_name'), $dest->get('customer_name'))));
        } else {
            $this->registry['messages']->setWarning($this->i18n('automation_expenses_reason_error', array($dest->get('type_name'))));
        }
        $this->registry['messages']->insertInSession($this->registry);

        return $result;
    }
}

?>
