<?php

trait completeAirConditionersTable
{
    private $params;

    /**
     * Crontab automation to process the replies in the selected inbox
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of operation
     */
    public function createAirConditionerTable($params)
    {
        $this->params = $params;
        $document = clone $params['model'];

        $getOldVars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $assocVars = $document->getAssocVars();

        $climaSufixesFields = array();
        foreach ($this->settings as $key => $value) {
            if (preg_match('/^clima_related_nom_(\d+)$/', $key, $matches)) {
                $climaSufixesFields[] = $matches[1]; // Extract the numeric part
            }
        }

        $rowsCreationSource = [];
        foreach ($climaSufixesFields as $climaSuffix) {
            if (isset($this->settings['clima_related_nom_' . $climaSuffix])) {
                $rowsCreationSource[$this->settings['clima_related_nom_' . $climaSuffix]] = !empty($assocVars[$this->settings['clima_var_prefix'] . $climaSuffix]['value']) ? intval($assocVars[$this->settings['clima_var_prefix'] . $climaSuffix]['value']) : 0;
            }
        }
        $rowsCreationSource = array_filter($rowsCreationSource);

        if (empty($rowsCreationSource)) {
            $this->registry->set('get_old_vars', $getOldVars, true);
            return true;
        }

        $office = $document->get('office');
        $nomsFilter = [];
        foreach ($rowsCreationSource as $nomKind => $nomCount) {
            $nomsFilter[] = sprintf('(n_cstm_type.value="%d" AND n_cstm_office.value="%d")', $nomKind, $office);
        }

        // get the ids of the vars of the related noms
        $nomVars = [
            $this->settings['nom_ac_series_var_external'], $this->settings['nom_ac_series_var_internal'],
            $this->settings['nom_ac_series_var_model_clima'], $this->settings['nom_ac_series_var_office'],
            $this->settings['nom_ac_series_type']
        ];
        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`=' . $this->settings['nom_type_ac_series'] . ' AND `name` IN ("' . implode('","', $nomVars) . '")';
        $nomVars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT n.id, ni18n.name, n_cstm_type.value as `type`, n_cstm_model.value as brand, ' . "\n" .
               '       n_cstm_external.value as external, n_cstm_internal.value as internal, n_cstm_office.value as office' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . $this->registry['lang'] . '" AND n.type="' . $this->settings['nom_type_ac_series'] . '" AND n.active=1 AND n.deleted_by="0")' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_office' . "\n" .
               ' ON (n_cstm_office.model_id=n.id AND n_cstm_office.var_id="' . (!empty($nomVars[$this->settings['nom_ac_series_var_office']]) ? $nomVars[$this->settings['nom_ac_series_var_office']] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_external' . "\n" .
               ' ON (n_cstm_external.model_id=n.id AND n_cstm_external.var_id="' . (!empty($nomVars[$this->settings['nom_ac_series_var_external']]) ? $nomVars[$this->settings['nom_ac_series_var_external']] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_internal' . "\n" .
               ' ON (n_cstm_internal.model_id=n.id AND n_cstm_internal.var_id="' . (!empty($nomVars[$this->settings['nom_ac_series_var_internal']]) ? $nomVars[$this->settings['nom_ac_series_var_internal']] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_model' . "\n" .
               ' ON (n_cstm_model.model_id=n.id AND n_cstm_model.var_id="' . (!empty($nomVars[$this->settings['nom_ac_series_var_model_clima']]) ? $nomVars[$this->settings['nom_ac_series_var_model_clima']] : '') . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_type' . "\n" .
               ' ON (n_cstm_type.model_id=n.id AND n_cstm_type.var_id="' . (!empty($nomVars[$this->settings['nom_ac_series_type']]) ? $nomVars[$this->settings['nom_ac_series_type']] : '') . '")' . "\n" .
               'WHERE ' . implode(' OR ', $nomsFilter) . "\n" .
               'ORDER BY n.id DESC' . "\n";
        $acBrands = $this->registry['db']->GetAll($sql);

        $acData = [];
        foreach ($acBrands as $brand) {
            $key = sprintf('%d_%d', $brand['type'], $brand['office']);
            if (isset($acData[$key])) {
                continue;
            }
            $acData[$key] = array(
                'id'       => $brand['id'],
                'name'     => $brand['name'],
                'type'     => $brand['type'],
                'brand'    => $brand['brand'],
                'external' => $brand['external'],
                'internal' => $brand['internal'],
                'office'   => $brand['office'],
            );
        }

        // find the first empty row of the group table
        $currentAcTypes = $assocVars[$this->settings['document_var_ac_type']]['value'];
        $currentAcTypes = array_filter($currentAcTypes);
        $rowKey = 1;
        if (!empty($currentAcTypes)) {
            $keys = array_keys($currentAcTypes);
            $rowKey = end($keys);
            $rowKey++;
        }


        foreach ($rowsCreationSource as $nomKind => $nomCount) {
            $key = sprintf('%d_%d', $nomKind, $brand['office']);
            if (!isset($acData[$key])) {
                continue;
            }

            for ($i=0; $i<$nomCount; $i++) {
                $assocVars[$this->settings['document_var_ac_type']]['value'][$rowKey] = $acData[$key]['type'];
                $assocVars[$this->settings['document_var_group_id']]['value'][$rowKey] = $acData[$key]['id'];
                $assocVars[$this->settings['document_var_group_name']]['value'][$rowKey] = $acData[$key]['name'];
                $assocVars[$this->settings['document_var_model']]['value'][$rowKey] = $acData[$key]['brand'];
                $assocVars[$this->settings['document_var_external']]['value'][$rowKey] = $acData[$key]['external'];
                $assocVars[$this->settings['document_var_internal']]['value'][$rowKey] = $acData[$key]['internal'];
                $rowKey++;
            }
        }

        // update the model
        $old_document = clone $document;
        $document->set('assoc_vars', $assocVars, true);
        $document->set('vars', array_values($assocVars), true);
        $document->unsanitize();
        $editAll = $this->registry->get('edit_all');
        $this->registry->set('edit_all', true, true);

        if ($document->save()) {
            $filters = array(
                'where' => array('d.id = ' . $document->get('id')),
                'model_lang' => $document->get('model_lang')
            );
            $new_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_document->getVars();
            $this->registry->set('get_old_vars', false, true);
            $params_history = array(
                'action_type' => 'edit',
                'old_model'   => $old_document,
                'model'       => $document,
                'new_model'   => $new_document
            );
            Documents_History::saveData($this->registry, $params_history);
        }
        $this->registry->set('edit_all', $editAll, true);
        $this->registry->set('get_old_vars', $getOldVars, true);

        return true;
    }
}
