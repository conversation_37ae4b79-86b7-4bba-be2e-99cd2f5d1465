<?php

/**
 * Plugin automations for QUEISSER installation
 *
 * @package Automations
 * @subpackage Queisser
 */
class Queisser_Automations_Controller extends Automations_Controller {
    /**
     * Holds batch quantites during an iteration (processing of a single document)
     * @var []
     */
    private $batch_quantities;

    /**
     * Finishes activity documents and creates incomes reasons and outgoing
     * handovers for them
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function finishActivities(array $params) {

        // reason type id
        $type = !empty($this->settings['fir_type_sample']) ? $this->settings['fir_type_sample'] : '';

        $this->registry['translater']->loadFile(array(
            PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini',
            PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance.ini',
            PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance_incomes_reasons.ini',
            PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance_warehouses_documents.ini',
        ));

        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $reason_type = Finance_Documents_Types::searchOne(
            $this->registry,
            array(
                'where' => array(
                    "fdt.id = '{$type}'",
                    "fdt.id > '" . PH_FINANCE_TYPE_MAX . "'",
                    "fdt.model = 'Finance_Incomes_Reason'",
                    "fdt.active = '1'",
                ),
            )
        );
        if (!$reason_type) {
            $this->executionErrors[] = $this->i18n('error_finance_incomes_reasons_invalid_type');
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        // get all non-finished activity documents
        $filters = array(
            'where' => array(
                "d.type = '{$params['start_model_type']}' AND",
                "d.status != 'closed' AND",
                "d.active = '1' AND",
                "d.date < CURDATE() OR",
                "d.date IS NULL AND",
            ),
            'sort' => array('d.added ASC'),
            'sanitize' => true,
        );
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        /** @var Document[] $documents */
        $documents = Documents::search($this->registry, $filters);

        if (!$documents) {
            // done for today, no records found
            //$this->executionWarnings[] = $this->i18n('error_no_items_found');
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }

        // if max_execution_time is other than zero and a large number of results
        // is found, extend max_execution_time
        if (ini_get('max_execution_time') > 0 && count($documents) > 100) {
            set_time_limit(count($documents) + ini_get('max_execution_time'));
        }

        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
        require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php');
        require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';

        // prepare common data to be reused
        $default_reason = array(
            'name' => '',
            'type' => $type,
            'type_name' => $reason_type->get('name'),
            'issue_date' => General::strftime($this->i18n('date_iso_short')),
            'date_of_payment_count' => $reason_type->get('default_date_of_payment_count'),
            'date_of_payment_period_type' => $reason_type->get('default_date_of_payment_period_type'),
            'date_of_payment_point' => $reason_type->get('default_date_of_payment_point'),
            'active' => 1,
            'finance_after_action' => 'finish',
            'company_data' => !empty($this->settings['company_data']) ? $this->settings['company_data'] : '',
        );

        $blank_reason = new Finance_Incomes_Reason(
            $this->registry,
            array(
                'type' => $type,
                'company_data' => $default_reason['company_data'],
            )
        );
        $blank_gt2 = $blank_reason->getGT2Vars();
        $blank_gt2['plain_values']['total_vat_rate'] = $blank_gt2['default_VAT'];
        $blank_reason->sanitize();

        $result = array();
        $original_user = $this->registry['currentUser'];
        $base_host = $this->registry['config']->getParam('crontab', 'base_host');
        $users = array();
        $employee_warehouses = array(0 => 0);

        $doc_link = sprintf(
            '<a href="%s/index.php?%s=documents&amp;documents=view&amp;view=%%d" target="_blank">%%s</a>',
            $base_host,
            $this->registry['module_param']
        );
        $cstm_link = sprintf(
            '<a href="%s/index.php?%s=customers&amp;customers=view&amp;view=%%d" target="_blank">%%s</a>',
            $base_host,
            $this->registry['module_param']
        );

        $ignore_autocomplete_vars = array_flip(array('quantity', 'discount_percentage', 'free_field1'));

        foreach ($documents as $idx => &$document) {
            $document->unsanitize();
            // document id
            $id = $document->get('id');

            // added_by user
            $uid = $document->get('added_by');
            if (empty($users[$uid])) {
                $filters = array(
                    'where' => array(
                        "u.id = '{$uid}'",
                        "u.hidden IS NOT NULL",
                    ),
                    'sanitize' => true,
                );
                $users[$uid] = Users::searchOne($this->registry, $filters);
            }
            $this->registry->set('currentUser', $users[$uid], true);

            // employee - might not be corresponding to user
            $employee = $document->get('employee') ?: 0;
            $wid = 0;

            // collect results per user and per document
            $result[$uid][$id] = array(
                'customer' => $document->get('customer_name'),
                'errors' => array(),
            );

            $gt2 = $document->getGT2Vars();
            $article_ids = array();
            if (!$gt2) {
                $result[$uid][$id]['errors'][] = $this->i18n('no_additional_vars');
            } else {
                // filter GT2 rows
                foreach ($gt2['values'] as $row_id => $row) {
                    if (empty($row) || empty($row['article_id']) || empty($row['quantity'])) {
                        unset($gt2['values'][$row_id]);
                    } elseif (!in_array($row['article_id'], $article_ids)) {
                        $article_ids[] = $row['article_id'];
                    }
                }
                if ($article_ids) {
                    $article_filters = array(
                        'where' => array(
                            "n.id IN (" . implode(",", array_map('intval', $article_ids)) . ")",
                            "n.subtype = 'commodity'",
                        ),
                    );
                    if (!empty($this->settings['nom_types'])) {
                        $article_filters['where'][] = "n.type IN ('" . implode("', '", array_map('intval', preg_split('#\s*,\s*#', $this->settings['nom_types']))) . "')";
                    }
                    $article_ids = Nomenclatures::getIds($this->registry, $article_filters);
                }
                foreach ($gt2['values'] as $row_id => $row) {
                    if (!in_array($row['article_id'], $article_ids)) {
                        unset($gt2['values'][$row_id]);
                    }
                }

                // validate employee and warehouse only when financial/warehouse documents will be created
                if (!empty($gt2['values'])) {
                    if ($employee && !array_key_exists($employee, $employee_warehouses)) {
                        $filters = array(
                            'where' => array(
                                "fwh.employees = '{$employee}'",
                                "fwh.active = '1'",
                            ),
                            'sort' => array(
                                "fwh.id DESC",
                            ),
                            'sanitize' => true,
                        );
                        $employee_warehouses[$employee] = Finance_Warehouses::getIds($this->registry, $filters);
                        $employee_warehouses[$employee] = !empty($employee_warehouses[$employee]) ? reset($employee_warehouses[$employee]) : 0;
                    }

                    if (empty($employee_warehouses[$employee])) {
                        if (!$employee) {
                            // no employee in document
                            $result[$uid][$id]['errors'][] = $this->i18n('error_notEmpty', array('var_label' => $document->getLayoutName('employee', false)));
                        } else {
                            // no warehouse for employee
                            $result[$uid][$id]['errors'][] = $this->i18n('plugin_error_employee_no_warehouse', array($document->get('employee_name')));
                        }
                    } else {
                        $wid = $employee_warehouses[$employee];
                    }

                    // check for any handover
                    $transformed = $document->getTransformedTo();
                    $reason_ids = array();
                    foreach ($transformed as $child) {
                        if ($child['model'] == 'Finance_Incomes_Reason') {
                            $reason_ids[] = $child['parent_id'];
                        }
                    }
                    if ($reason_ids) {
                        $reason_ids = array(
                            'where' => array(
                                "fir.id IN ('" . implode("', '", $reason_ids) . "')",
                                "fir.handovered_status IN ('partial', 'full')",
                            ),
                        );
                        $reason_ids = Finance_Incomes_Reasons::getIds(
                            $this->registry,
                            $reason_ids
                        );
                        if ($reason_ids) {
                            foreach ($reason_ids as $reason_id) {
                                $link = sprintf(
                                    '%s/index.php?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'], 'finance',
                                    $this->registry['controller_param'], 'incomes_reasons',
                                    'incomes_reasons', 'view',
                                    'view', $reason_id
                                );
                                $link = sprintf(
                                    '<a href="%s" target="_blank">%s</a>',
                                    $link,
                                    $reason_type->get('name')
                                );
                                $result[$uid][$id]['errors'][] = $this->i18n('plugin_error_handover_exists', array($link));
                            }
                        }
                    }
                }
            }

            if (!empty($result[$uid][$id]['errors'])) {
                array_unshift($result[$uid][$id]['errors'], $this->i18n(
                    'plugin_errors_for_document',
                    array(
                        sprintf($doc_link, $id, htmlentities($document->getModelTypeName() . ' ' . $document->get('full_num'), ENT_COMPAT, 'UTF-8')),
                        sprintf($cstm_link, $document->get('customer'), htmlentities($document->get('customer_name'), ENT_COMPAT, 'UTF-8')),
                    )
                ));
                $document->sanitize();
                unset($documents[$idx]);
                continue;
            }

            $this->registry['db']->StartTrans();

            // create reason
            $reason = null;
            if (!empty($gt2['values'])) {
                $nomenclatures = $autocomplete_values = array();
                if ($article_ids) {
                    $this->registry->set('nomenclatures_currency', $gt2['plain_values']['currency'], true);
                    /** @var Nomenclature[] $nomenclatures */
                    $nomenclatures = Nomenclatures::search(
                        $this->registry,
                        array(
                            'where' => array(
                                "n.id IN (" . implode(',', array_map('intval', $article_ids)) . ")",
                            ),
                            'sanitize' => true,
                        )
                    );
                    foreach ($nomenclatures as $model) {
                        $autocomplete_values[$model->get('id')] = $model->getAutocompleteValues('article_name', $blank_gt2['vars']);
                        if (empty($autocomplete_values[$model->get('id')])) {
                            unset($autocomplete_values[$model->get('id')]);
                        } else {
                            $autocomplete_values[$model->get('id')] = array_diff_key($autocomplete_values[$model->get('id')], $ignore_autocomplete_vars);
                        }
                    }
                    $this->registry->remove('nomenclatures_currency');
                }

                foreach ($gt2['values'] as $row_id => $row) {
                    if (array_key_exists($row['article_id'], $autocomplete_values)) {
                        foreach ($autocomplete_values[$row['article_id']] as $k => $v) {
                            $gt2['values'][$row_id][$k] = $v;
                        }
                    }
                }
                // save reason with default VAT rate of its type, not the one of document
                $gt2['plain_values']['total_vat_rate'] = $blank_gt2['plain_values']['total_vat_rate'];

                $reason = array_merge(
                    $default_reason,
                    array(
                        'customer' => $document->get('customer'),
                        'customer_name' => $document->get('customer_name'),
                        'original_customer_name' => $document->get('customer_name'),
                        'employee' => $document->get('employee'),
                        'employee_name' => $document->get('employee_name'),
                        'issue_date' => $document->get('date') && $document->get('date') != '0000-00-00' ? $document->get('date') : $default_reason['issue_date'],
                        'fin_field_1' => $document->getVarValue($this->settings['activity_organization_name_var'], true, true),
                    ),
                    $gt2['plain_values']
                );
                $reason = new Finance_Incomes_Reason($this->registry, $reason);
                $reason->setGroup($document->get('group'), $reason_type->get('default_group'));
                $reason->setDepartment($document->get('department'), $reason_type->get('default_department'));
                $reason->set('department_name', $document->get('department_name'), true);
                $reason->set('group_name', $document->get('group_name'), true);

                // prepare GT2
                $reason_gt2 = array_merge(
                    $blank_gt2,
                    array(
                        'values' => $gt2['values'],
                        'plain_values' => $gt2['plain_values'],
                    )
                );
                $reason->set('grouping_table_2', $reason_gt2, true);
                $reason->calculateGT2();
                $reason->set('table_values_are_set', true, true);

                // save relations as if transformation was performed
                $reason->set('link_to', $id, true);
                $reason->set('link_to_model_name', $document->modelName, true);
                $transform_params = serialize(array(
                    'origin_method' => 'Automation',
                    'origin_method_id' => (isset($params['id']) ? $params['id'] : '0'),
                    'origin_model' => $document->modelName,
                    'origin_id' => $id,
                    'origin_full_num' => $document->get('full_num'),
                    'origin_name' => $document->get('name'),
                ));
                $reason->set('transform_params', $transform_params, true);

                // validate available quantites before adding the reason to db
                $messages = $this->checkAddOutgoingHandover($reason, $wid);
                if (!empty($messages)) {
                    $result[$uid][$id]['errors'] = $messages;
                    $this->registry['db']->FailTrans();
                }
            }

            if ($this->registry['db']->HasFailedTrans()) {
                // do nothing more
            } else {
                // otherwise finish document
                $old_document = clone $document;
                $old_document->sanitize();
                $document->set('status', 'closed', true);
                $document->set('substatus', '0', true);

                if ($document->setStatus()) {

                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $document,
                            'action_type' => 'status',
                            'new_model' => $document,
                            'old_model' => $old_document,
                        )
                    );

                    // create reason if necessary
                    if (!empty($reason)) {
                        if ($reason->save()) {
                            // strip slashes because same model will be reused for history save
                            $reason->slashesStrip();
                            // property value is changed during save in order to save fiscal customer name in reason data
                            $reason->set('customer_name', $reason->get('original_customer_name'), true);
                            $reason->unsetProperty('new_handovered_status', true);

                            // update row ids in batch quantities with the new
                            // row ids because they were prepared before the
                            // model was saved in db
                            if ($this->batch_quantities && $reason->get('rows_links')) {
                                $rows_links = $reason->get('rows_links');
                                if (!empty($rows_links['added'])) {
                                    foreach ($rows_links['added'] as $row_id => $parent_row_id) {
                                        if (array_key_exists($parent_row_id, $this->batch_quantities)) {
                                            $this->batch_quantities[$row_id] = $this->batch_quantities[$parent_row_id];
                                            unset($this->batch_quantities[$parent_row_id]);
                                        }
                                    }
                                }
                            }

                            // write history
                            Finance_Incomes_Reasons_History::saveData(
                                $this->registry,
                                array(
                                    'action_type' => 'add',
                                    'new_model' => $reason,
                                    'old_model' => $blank_reason,
                                )
                            );
                            // strip slashes again if escaped during history save
                            if ($reason->slashesEscaped) {
                                $reason->slashesStrip();
                            }

                            // add handover
                            $messages = $this->addOutgoingHandover($reason, $wid);
                            if (!empty($messages)) {
                                $result[$uid][$id]['errors'] = array_merge(
                                    $result[$uid][$id]['errors'],
                                    $messages
                                );
                                $this->registry['db']->FailTrans();
                            }
                        } else {
                            // collect error messages
                            $result[$uid][$id]['errors'] = array_merge(
                                $result[$uid][$id]['errors'],
                                array(
                                    $this->i18n(
                                        'error_finance_incomes_reasons_add_failed',
                                        array($reason->getModelTypeName())
                                    ),
                                )
                            );
                        }
                        $reason->sanitize();
                    }
                } else {
                    $result[$uid][$id]['errors'][] = $this->i18n(
                        'error_documents_status_failed',
                        array($document->getModelTypeName())
                    );
                    $this->registry['db']->FailTrans();
                }
            }

            // collect error messages from registry
            if ($this->registry['messages']->getErrors()) {
                $result[$uid][$id]['errors'] = array_merge(
                    $result[$uid][$id]['errors'],
                    array_values($this->registry['messages']->getErrors())
                );
            }

            if (!empty($result[$uid][$id]['errors'])) {
                $this->registry['db']->FailTrans();
            }

            $res = !$this->registry['db']->HasFailedTrans();

            $this->registry['db']->CompleteTrans();

            if (!$res) {
                array_unshift($result[$uid][$id]['errors'], $this->i18n(
                    'plugin_errors_for_document',
                    array(
                        sprintf($doc_link, $id, htmlentities($document->getModelTypeName() . ' ' . $document->get('full_num'), ENT_COMPAT, 'UTF-8')),
                        sprintf($cstm_link, $document->get('customer'), htmlentities($document->get('customer_name'), ENT_COMPAT, 'UTF-8')),
                    )
                ));
            } else {
                unset($result[$uid][$id]);
                if (empty($result[$uid])) {
                    unset($result[$uid]);
                }
            }
            $document->sanitize();
            // remove processed element from array
            unset($documents[$idx]);
            // reset messages object for next iteration
            $this->registry['messages']->flush();
        }
        // memory cleanup
        unset($document);
        unset($reason);

        $this->registry->set('get_old_vars', $gov, true);
        $this->registry->set('currentUser', $original_user, true);

        if (!empty($result)) {
            // send errors to users
            $template = 'finish_activities_errors';
            $mailer = new Mailer($this->registry, $template);
            foreach ($result as $uid => $data) {
                $errors = array();
                foreach ($data as $f => $d) {
                    $d = implode('<br />', $d['errors']);
                    $errors[] = $d;
                }
                $errors = implode('<br /><br />', $errors);
                $mailer->placeholder->add('finish_activities_errors', $errors);

                if ($users[$uid]->get('email')) {
                    $mailer->placeholder->add('to_email', $users[$uid]->get('email'));
                    $mailer->placeholder->add('user_name', sprintf('%s %s', $users[$uid]->get('firstname'), $users[$uid]->get('lastname')));

                    // send email
                    $result = $mailer->send();
                }
            }
            $result = false;
        } else {
            $result = true;
        }

        $this->updateAutomationHistory($params, 0, intval($result));

        return $result;
    }

    /**
     * Checks if outgoing handover can be added for reason
     *
     * @param Finance_Incomes_Reason $reason - finance incomes reasons model
     * @param int $wid - id of warehouse to add handover for
     * @return array $messages - array of messages if the handover could not be added
     */
    private function checkAddOutgoingHandover(Finance_Incomes_Reason &$reason, $wid) {
        $messages = array();
        $f = clone $reason;
        // GT2 should be set to reason as check is performed before saving it
        $gt2 = $f->get('grouping_table_2') ?: array('values' => array());

        // get warehouse model
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
        $filters = array(
            'where' => array(
                "fwh.id = '{$wid}'",
                "fwh.active = '1'",
            ),
            'sort' => array(
                'fwh.id DESC',
            ),
        );
        $warehouse = Finance_Warehouses::searchOne($this->registry, $filters);

        if (empty($warehouse)) {
            $messages[] = $this->i18n('plugin_error_employee_no_warehouse', array($f->get('employee_name')));
            return $messages;
        } elseif ($warehouse->get('locker') && $warehouse->get('locker_status') == 'opened') {
            // there is an open inspection for warehouse, available quantities are uncertain
            $link = sprintf(
                '%s/index.php?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                $this->registry['config']->getParam('crontab', 'base_host'),
                $this->registry['module_param'], 'finance',
                $this->registry['controller_param'], 'warehouses_documents',
                'warehouses_documents', 'view',
                'view', $warehouse->get('locker')
            );
            $messages[] = $this->i18n('error_finance_warehouse_locked', array($warehouse->get('name'), $link));
            return $messages;
        }

        // find quantities of the selected nomenclatures
        $warehouse_availabilities = array();

        $prec = $this->registry['config']->getSectionParams('precision');

        // shows what quantity from each batch has to be taken
        $this->batch_quantities = $articles_erred = $warehouse_availabilities = array();

        $idx = 0;
        foreach ($gt2['values'] as $row_id => $row) {
            $idx++;
            //do some validation of articles and quantities
            if (!empty($row['article_id']) && !empty($row['quantity'])) {
                if (!isset($warehouse_availabilities[$row['article_id']])) {
                    $warehouse_availabilities[$row['article_id']] = $warehouse->getAvailableQuantity(array('nom_id' => $row['article_id']));
                    $warehouse_availabilities[$row['article_id']] = reset($warehouse_availabilities[$row['article_id']]);
                }
                // check if the quantity is available
                if (empty($warehouse_availabilities[$row['article_id']]['quantity']) || ($warehouse_availabilities[$row['article_id']]['quantity'] - $row['quantity']) < 0) {
                    unset($gt2['values'][$row_id]);
                    if (empty($articles_erred[$row['article_id']])) {
                        $messages[] = sprintf($this->i18n('plugin_no_warehouse_quantity_of_nomenclature'), $row['article_name'], $warehouse->get('name'));
                        $articles_erred[$row['article_id']] = true;
                    }
                } else {
                    // separate the quantity by batches in FIFO style (the user cannot select articles and their batches)
                    $left_article_quantity = $row['quantity'];
                    foreach ($warehouse_availabilities[$row['article_id']]['batch_data'] as $bd => $batch_data) {
                        if ($left_article_quantity > 0) {
                            $current_batch_quantity = 0;
                            if ($batch_data['quantity'] < $left_article_quantity) {
                                // transfer the entire quantity
                                $current_batch_quantity = $batch_data['quantity'];
                                unset($warehouse_availabilities[$row['article_id']]['batch_data'][$bd]);
                            } else {
                                // transfer only the needed quantity
                                $current_batch_quantity = $left_article_quantity;
                                $warehouse_availabilities[$row['article_id']]['batch_data'][$bd]['quantity'] -= $left_article_quantity;
                            }
                            // reduce the total quantity as well so that check if the quantity is available works correctly
                            $warehouse_availabilities[$row['article_id']]['quantity'] -= $current_batch_quantity;
                            $batch_data['quantity'] = $current_batch_quantity;
                            $this->batch_quantities[$row_id][] = $batch_data;
                            $left_article_quantity = round($left_article_quantity - $current_batch_quantity, $prec['gt2_quantity']);
                        } else {
                            // if there is no quantity to be searched, step out of the cycle
                            break;
                        }
                    }
                }
            } elseif (empty($row['article_id']) && empty($row['quantity'])) {
                unset($gt2['values'][$row_id]);
                continue;
            } elseif (empty($row['article_id'])) {
                $messages[] = sprintf($this->i18n('plugin_no_article_selected'), $idx);
            } elseif (empty($row['quantity'])) {
                $messages[] = sprintf($this->i18n('plugin_no_quantity_inserted'), $idx);
            }
        }

        /* if (empty($gt2['values'])) {
            $messages[] = $this->i18n('plugin_no_articles_selected');
        } */

        return $messages;
    }

    /**
     * Adds outgoing handover to incomes reason
     *
     * @param Finance_Incomes_Reason $reason - finance incomes reasons model
     * @param int $wid - id of warehouse to add handover for
     * @return array - array of errors if the handover could not be added, empty array if no errors
     */
    private function addOutgoingHandover(Finance_Incomes_Reason &$reason, $wid) {

        $messages = array();
        $employee = $reason->get('employee');
        $is_portal = $this->registry['currentUser']->get('is_portal');
        $today = General::strftime($this->i18n('date_iso_short'));
        $request = &$this->registry['request'];

        // clear temp data from POST
        if ($request->isPost()) {
            $tmp_post = $request->getAll('post');
            foreach ($tmp_post as $k => $v) {
                $request->remove($k);
            }
        }

        // create outgoing handover, set all necessary data in POST
        $request_params = array(
            'handover_direction' => 'outgoing',
            'name' => '',
            'gt2_requested' => 1,
            'department' => PH_DEPARTMENT_FIRST,
            'group' => PH_ROOT_GROUP,
            'is_portal' => $is_portal,
            'active' => 1
        );
        foreach ($request_params as $k => $v) {
            $request->set($k, $v, 'post', true);
        }
        $request_params_wh = array(
            'from' => $employee,
            'to' => $reason->get('customer_name'),
            'location' => $this->i18n('finance_warehouses_documents_warehouse'),
            'date' => $today,
            'description' => '',
            'notes' => $reason->get('fin_field_1'),
            'status' => 'finished'
        );
        foreach ($request_params_wh as $k => $v) {
            $request->set($k, array($wid => $v), 'post', true);
        }
        $gt2 = $reason->get('grouping_table_2');
        $values_post = array();
        if (empty($gt2['values'])) {
            return $messages;
        }
        foreach ($gt2['values'] as $row_id => $row) {
            if (empty($row) || empty($row['quantity'])) {
                continue;
            }
            $row['warehouse' . $wid . '_quantity'] = $row['total_quantity'] = $row['quantity'];
            //$row['warehouse' . $wid . '_available'] = $row['total_available'] = '';
            if (empty($values_post)) {
                $values_post = array_fill_keys(array_keys($row), array());
            }
            foreach ($row as $k => $v) {
                $values_post[$k][$row_id] = $v;
            }
            if (!empty($this->batch_quantities[$row_id])) {
                // prepare batch data for row
                $request_params_batch = array_fill_keys(
                    array('wh', 'batch', 'quantity', 'expire', 'custom', 'delivery_price', 'currency'),
                    array()
                );
                foreach ($this->batch_quantities[$row_id] as $bidx => $bd) {
                    $request_params_batch['wh'][] = $wid;
                    $request_params_batch['batch'][] = $bd['batch'];
                    $request_params_batch['quantity'][] = $bd['quantity'];
                    $request_params_batch['expire'][] = $bd['expire'];
                    $request_params_batch['custom'][] = $bd['custom'];
                    $request_params_batch['delivery_price'][] = $bd['delivery_price'];
                    $request_params_batch['currency'][] = $bd['currency'];
                }
                foreach ($request_params_batch as $k => $v) {
                    $request->set('article_' . $row_id . '_' . $k, $v, 'post', true);
                }
            }
        }

        foreach ($values_post as $k => $v) {
            if (in_array($k, array(
                'id', 'model', 'model_id', 'current', 'translated', 'translated_by', 'lang', 'parent_id', 'added', 'added_by', 'modified', 'modified_by', 'date_from', 'date_to'
            )) || preg_match('/index|formula/', $k)) {
                continue;
            }
            $request->set($k, $v, 'post', true);
        }
        foreach ($gt2['plain_values'] as $k => $v) {
            $request->set($k, $v, 'post', true);
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
        $params = array(
            'model' => $reason,
            'type' => PH_FINANCE_TYPE_HANDOVER
        );
        $result = Finance_Warehouses_Documents::addMultipleDocuments(
            $this->registry,
            $params
        );
        if (!$result['status']) {
            $messages[] = $this->i18n(
                'error_finance_warehouses_documents_add_failed',
                array(
                    $this->i18n('plugin_outgoing_handover'),
                )
            );
        }

        return $messages;
    }
}
