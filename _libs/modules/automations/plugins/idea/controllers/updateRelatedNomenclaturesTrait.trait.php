<?php

trait updateRelatedNomenclaturesTrait {
    public $automation_params;
    public $updated_noms = array(
        'success' => array(),
        'failed'  => array(),
    );

    /**
     * Function to update the relation of nomenclatures
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function updateRelatedNomenclatures(array $params): bool {
        $this->automation_params = $params;
        $old_model = clone $params['model']->get('old_model');
        $new_model = clone $params['model'];
        $included_tags = array_filter(preg_split('/\s*,\s*/', $this->settings['objects_included_tags']));

        $get_old_vars = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);

        $old_model->getVars();
        $vars_old = $old_model->get('vars');
        $new_model->getVars();
        $vars_new = $new_model->get('vars');
        $old_value = '';
        $new_value = '';
        foreach ($vars_old as $var_idx => $var) {
            if ($var['name'] == $this->settings['building_var']) {
                $old_value = $var['value'];
                break;
            }
        }
        foreach ($vars_new as $var) {
            if ($var['name'] == $this->settings['building_var']) {
                $new_value = $var['value'];
                break;
            }
        }

        // analyze the action
        $complete_into_nom = array();
        $remove_from_nom = array();
        if ($this->registry['action'] == 'add' ||
            $this->registry['action'] == 'activate' ||
            ($this->registry['action'] == 'edit' && $new_model->get('active') && !$old_model->get('active'))) {
            $complete_into_nom[] = $new_value;
        } elseif ($this->registry['action'] == 'deactivate' ||
                  ($this->registry['action'] == 'edit' && !$new_model->get('active') && $old_model->get('active'))) {
            $remove_from_nom[] = $new_value;
            $remove_from_nom[] = $old_value;
        } elseif ($this->registry['action'] == 'edit') {
            if ($old_value != $new_value) {
                $remove_from_nom[] = $old_value;
                $complete_into_nom[] = $new_value;
            }
        }
        if (empty($remove_from_nom) && empty($complete_into_nom)) {
            $this->registry->set('get_old_vars', $get_old_vars, true);
            $this->registry->set('edit_all', $edit_all, true);
            return true;
        }

        $remove_from_nom = array_filter($remove_from_nom);
        $complete_into_nom = array_filter($complete_into_nom);

        $new_model->getTags();
        $model_tags = $new_model->get('tags');
        $current_nom_data = array(
            'id'   => $new_model->get('id'),
            'name' => $new_model->get('name'),
            'tags' => array_intersect($model_tags, $included_tags)
        );

        $this->changeRelatedNom($complete_into_nom, $current_nom_data, 'add');
        $this->changeRelatedNom($remove_from_nom, $current_nom_data, 'delete');

        $this->registry->set('get_old_vars', $get_old_vars, true);
        $this->registry->set('edit_all', $edit_all, true);
        $this->_processMessages();

        return true;
    }

    private function changeRelatedNom($building_noms, $related_nom_data, $action) : void {
        if (empty($building_noms)) {
            return;
        }

        // get the buildings
        $filters = array('where'      => array('n.id IN (' . implode(',', $building_noms) . ')'),
                         'model_lang' => $this->registry['lang']);
        $buildings = Nomenclatures::search($this->registry, $filters);

        foreach ($buildings as $building) {
            $building->unsanitize();
            $building->getVars();
            $vars = $building->get('vars');
            $assoc_vars = $building->getAssocVars();

            // get the grouping index
            if (empty($assoc_vars[$this->settings['building_var_object_id']])) {
                break;
            }
            $grouping_var_index = $assoc_vars[$this->settings['building_var_object_id']]['grouping'];
            $grouping_vars = array();

            foreach ($vars as $idx => $var) {
                if ($var['type'] != 'group' && $var['grouping'] == $grouping_var_index) {
                    $grouping_vars[$var['name']] = $idx;
                }
            }

            $old_building = clone $building;
            $update_building = false;

            // find the searched nom in the grouping table
            $included_objects = $assoc_vars[$this->settings['building_var_object_id']]['value'];
            $searched_keys = array_keys($included_objects, $related_nom_data['id']);
            if ($action == 'add') {
                if (!empty($searched_keys)) {
                    // the nomenclature is already presented so skip it
                    continue;
                }

                // find the rows to insert in
                $insert_row = null;
                foreach ($included_objects as $k_row => $row_val) {
                    $row_empty = true;
                    foreach ($grouping_vars as $grp_var => $grp_idx) {
                        if (!empty($assoc_vars[$grp_var]['value'][$k_row])) {
                            // this row is completed so we can not use it
                            $row_empty = false;
                            break;
                        }
                    }
                    if ($row_empty) {
                        $insert_row = $k_row;
                    }
                }

                if ($insert_row === null) {
                    $rows_keys = array_keys($included_objects);
                    $insert_row = end($rows_keys);
                    $insert_row++;
                }

                foreach ($grouping_vars as $grp_var => $grp_idx) {
                    $new_value = '';
                    if ($grp_var == $this->settings['building_var_object_id']) {
                        $new_value = $related_nom_data['id'];
                    } elseif ($grp_var == $this->settings['building_var_object']) {
                        $new_value = $related_nom_data['name'];
                    } elseif ($grp_var == $this->settings['building_var_object_status']) {
                        $new_value = reset($related_nom_data['tags']);
                    }
                    $assoc_vars[$grp_var]['value'][$insert_row] = $new_value;
                }
                $update_building = true;
            } elseif ($action == 'delete') {
                foreach ($searched_keys as $s_key) {
                    foreach ($grouping_vars as $grp_var => $grp_idx) {
                        if (isset($assoc_vars[$grp_var]['value'][$s_key])) {
                            unset($assoc_vars[$grp_var]['value'][$s_key]);
                            $update_building = true;
                        }
                    }
                }
            }

            if ($update_building) {
                // update the model
                $building->set('assoc_vars', $assoc_vars, true);
                $building->set('vars', array_values($assoc_vars), true);
                $this->registry['db']->StartTrans();
                $key_msg = 'failed';
                if ($building->save()) {
                    $filters = array('n.id=' . $building->get('id'));
                    $new_building = Nomenclatures::searchOne($this->registry,
                                                             array('where'      => $filters,
                                                                   'model_lang' => $this->registry['lang']));
                    $new_building->getVars();
                    Nomenclatures_History::saveData($this->registry,
                                                    array('model'       => $new_building,
                                                          'action_type' => 'edit',
                                                          'new_model'   => $new_building,
                                                          'old_model'   => $old_building));
                    $key_msg = 'success';
                } else {
                    $this->registry['db']->FailTrans();
                }
                $this->updated_noms[$key_msg][] = array(
                    'id'   => $new_building->get('id'),
                    'name' => $new_building->get('name')
                );

                $this->registry['db']->CompleteTrans();
            }
        }
    }

    /**
     * Function to process the messages
     *
     * @return void
     */
    private function _processMessages(): void {
        $messages_set = false;
        foreach ($this->updated_noms as $key_msg => $msg_noms) {
            if (!count($msg_noms)) {
                continue;
            }
            $funcion_msgs = 'setMessage';
            $msg = 'message_automation_updated_related_noms_success';
            if ($key_msg == 'failed') {
                $funcion_msgs = 'setWarning';
                $msg = 'error_automation_updated_related_noms_failed';
            }
            $messages_set = true;
            $link_template = sprintf('%s?%s=nomenclatures&amp;nomenclatures=view&amp;view=', $_SERVER['PHP_SELF'], $this->registry['module_param']);
            $upd_noms_links = array();
            foreach ($msg_noms as $nom_data) {
                $upd_noms_links[] = sprintf('<a href="%s%d" target="_blank">%s</a>', $link_template, $nom_data['id'], $nom_data['name']);
            }
            $this->registry['messages']->$funcion_msgs(sprintf($this->i18n($msg), implode(', ', $upd_noms_links)));
        }
        if ($messages_set) {
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

}
