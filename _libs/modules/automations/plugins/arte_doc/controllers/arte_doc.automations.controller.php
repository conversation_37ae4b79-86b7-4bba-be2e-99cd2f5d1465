<?php

class Arte_Doc_Automations_Controller extends Automations_Controller {

    public function manageContractEvents($params) {


        if ($this->registry['module'] == 'crontab') {
            //continue
        } elseif ($this->action != 'setstatus' || $this->registry['request']->get('status') != 'closed' ||
          $this->registry['request']->get('substatus') == $this->registry['request']->get('current_status_base') . '_' . $this->registry['request']->get('current_substatus_base')) {
            //this action has nothing to do with the automation
            return true;
        }

        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        $docs = array();
        if ($this->registry['module'] == 'documents') {
            if ($this->registry['request']->get('substatus') == $this->settings['substatus_active']) {
                $doc = Documents::searchOne($this->registry, array('where' => array('d.id = ' . $params['model']->get('id'))));
                $doc->getAssocVars();
                $vars = $doc->get('assoc_vars');
                $event_date = false;
                switch ($vars['method_notification__value']['value']) {
                    case 'month_middle':
                        if (date('d') < 15) {
                            $event_date = date('Y-m-15');
                        } elseif (date('d') < date('t')) {
                            $event_date = date('Y-m-t');
                        } elseif (date('m') == 12) {
                            $event_date = (date('Y') + 1) . '-01-15';
                        } else {
                            $event_date = date('Y-') . (date('m') + 1) . '-15';
                        }
                        break;
                    case 'month_end':
                        $event_date = date('Y-m-t');
                        break;
                    case 'trimester_end':
                        $event_date = date_sub(date_add(date_create(date('Y-m-01')), new DateInterval('P3M')), new DateInterval('P1D'))->format('Y-m-d');
                        break;
                    case 'semester_end':
                        $event_date = date_sub(date_add(date_create(date('Y-m-01')), new DateInterval('P6M')), new DateInterval('P1D'))->format('Y-m-d');
                        break;
                    case 'year_end':
                        $event_date = date('Y-12-31');
                        break;
                    default:
                        if ($doc->get('validity_term') > '0000-00-00') {
                            $event_date = date_sub(date_create($doc->get('validity_term')), new DateInterval('P10D'))->format('Y-m-d');
                        } else {
                            $this->registry['messages']->setError($this->i18n('plugin_error_empty_validity_and_period'));
                            return false;
                        }
                        break;
                }
                if ($event_date > $doc->get('validity_term')) {
                    $event_date = $doc->get('validity_term');
                }
                $docs[$doc->get('id')] = array(
                    'date' => $event_date,
                    'customer' => $doc->get('customer'),
                    'num' => $doc->get('full_num'),
                    'validity_term' => $doc->get('validity_term'),
                    'description' => $vars['contract_note__value']['value'],
                );
                if (isset($vars['contract_kind__value'])) {
                    foreach ($vars['contract_kind__value']['options']as $opt) {
                        if ($opt['option_value'] == $vars['contract_kind__value']['value']) {
                            $docs[$doc->get('id')]['type'] = $opt['label'];
                        }
                    }
                } elseif (isset($vars['contract_kind1__value'])) {
                    foreach ($vars['contract_kind1__value']['options']as $opt) {
                        if ($opt['option_value'] == $vars['contract_kind1__value']['value']) {
                            $docs[$doc->get('id')]['type'] = $opt['label'];
                        }
                    }
                }
            } else {
                //we have to remove future events if any
                $query = 'SELECT ea.parent_id FROM ' . DB_TABLE_EVENTS_RELATIVES . " ea\n" .
                         'JOIN ' . DB_TABLE_EVENTS . ' e' . "\n" .
                         ' ON e.id = ea.parent_id AND e.type = ' . $this->settings['event_type'] . "\n" .
                         'WHERE origin = "document" AND link_to = ' . $params['model']->get('id');
                $events = $this->registry['db']->GetCol($query);
                if (!empty($events)) {
                    $docs[$params['model']->get('id')] = array('deactivate' => $events);
                }
            }
        } else {
            //crontab automation has been started

            $subquery = 'SELECT DISTINCT(er.link_to)' . "\n" .
                        'FROM ' . DB_TABLE_EVENTS . ' e' . "\n" .
                        'JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' er' . "\n" .
                        '  ON e.id = er.parent_id AND er.origin = "document"' . "\n" .
                        'WHERE e.type = ' . $this->settings['event_type'] . ' AND e.deleted_by = 0 AND e.event_start > NOW()';
            $subquery = $this->registry['db']->GetCol($subquery);

            //get all documents that are signed and do not have events for future date
            $query = 'SELECT d.id, d.customer, d.full_num as num, d.validity_term, dcstm1.value as description,' . "\n" .
                     'dcstm2.value as method_notification__value, IF(fo3.label, fo3.label, fo4.label) as type' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS . ' d' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' fm1' . "\n" .
                     '  ON fm1.model = "Document" AND fm1.model_type = d.type AND fm1.name = "contract_note__value"' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dcstm1' . "\n" .
                     '  ON dcstm1.model_id = d.id AND dcstm1.var_id = fm1.id' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' fm2' . "\n" .
                     '  ON fm2.model = "Document" AND fm2.model_type = d.type AND fm2.name = "method_notification__value"' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dcstm2' . "\n" .
                     '  ON dcstm2.model_id = d.id AND dcstm2.var_id = fm2.id' . "\n" .
                     'left JOIN ' . DB_TABLE_FIELDS_META . ' fm3' . "\n" .
                     '  ON fm3.model = "Document" AND fm3.model_type = d.type AND fm3.name = "contract_kind__value"' . "\n" .
                     'left JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dcstm3' . "\n" .
                     '  ON dcstm3.model_id = d.id AND dcstm3.var_id = fm3.id' . "\n" .
                     'left JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' fo3' . "\n" .
                     '  ON fo3.parent_name = fm3.name AND fo3.option_value = dcstm3.value' . "\n" .
                     'left JOIN ' . DB_TABLE_FIELDS_META . ' fm4' . "\n" .
                     '  ON fm4.model = "Document" AND fm4.model_type = d.type AND fm4.name = "contract_kind1__value"' . "\n" .
                     'left JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' dcstm4' . "\n" .
                     '  ON dcstm4.model_id = d.id AND dcstm4.var_id = fm4.id' . "\n" .
                     'left JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' fo4' . "\n" .
                     '  ON fo4.parent_name = fm4.name AND fo4.option_value = dcstm4.value' . "\n" .
                     'WHERE d.type IN (' .  $this->settings['model_types'] . ')' . "\n" .
                     '  AND d.validity_term > NOW()' . "\n" .
                     '  AND d.substatus IN (' . preg_replace('#closed_#', '', $this->settings['substatus_active']) . ')' . "\n" .
                     '  AND d.id NOT IN (' . implode(', ', $subquery) . ')';
            $docs = $this->registry['db']->GetAssoc($query);

            foreach ($docs as $did => $data) {
                $event_date = false;
                switch ($data['method_notification__value']) {
                    case 'month_middle':
                        if (date('d') < 15) {
                            $event_date = date('Y-m-15');
                        } elseif (date('d') < date('t')) {
                            $event_date = date('Y-m-t');
                        } elseif (date('m') == 12) {
                            $event_date = (date('Y') + 1) . '-01-15';
                        } else {
                            $event_date = date('Y-') . (date('m') + 1) . '-15';
                        }
                        break;
                    case 'month_end':
                        $event_date = date('Y-m-t');
                        break;
                    case 'trimester_end':
                        $event_date = date_sub(date_add(date_create(date('Y-m-01')), new DateInterval('P3M')), new DateInterval('P1D'))->format('Y-m-d');
                        break;
                    case 'semester_end':
                        $event_date = date_sub(date_add(date_create(date('Y-m-01')), new DateInterval('P6M')), new DateInterval('P1D'))->format('Y-m-d');
                        break;
                    case 'year_end':
                        $event_date = date('Y-12-31');
                        break;
                    default:
                        if ($data['validity_term'] > '0000-00-00') {
                            $event_date = date_sub(date_create($data['validity_term']), new DateInterval('P10D'))->format('Y-m-d');
                        } else {
                            $this->registry['messages']->setError($this->i18n('plugin_error_empty_validity_and_period'));
                            unset($docs[$did]);
                            continue;
                        }
                        break;
                }
                if ($event_date > $data['validity_term']) {
                    $event_date = $data['validity_term'];
                }
                $docs[$did]['date'] = $event_date;
            }
        }

        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.history.php';
        require_once PH_MODULES_DIR . 'events/models/events.audit.php';
        $event_type = Events_Types::searchOne($this->registry, array('where' => array('et.id = ' . $this->settings['event_type'])));

        $errors = false;
        foreach ($docs as $did => $data) {
            $error = false;
            if (!empty($data['deactivate'])) {
                $query = 'UPDATE ' . DB_TABLE_EVENTS . ' SET deleted = NOW(), deleted_by = ' . PH_AUTOMATION_USER . ' WHERE id IN (' . implode(', ', $data['deactivate']) . ')';
                $this->registry['db']->Execute($query);
                continue;
            }
            $description = array(
                sprintf('%s: %s', $this->i18n('plugin_validity_term'), date_create($data['validity_term'])->format('d.m.Y')),
                sprintf('%s: %s', $this->i18n('plugin_contract_type'), $data['type']),
                //sprintf('%s: <a href="%s%s?launch=documents&amp;documents=view&amp;view=%d" target="_blank">%s</a>', $this->i18n('plugin_contract'), $this->registry['config']->getParam('crontab', 'base_host'), $_SERVER['PHP_SELF'], $did, $data['num']),
                sprintf('%s: %s - %s/index.php?launch=documents&amp;documents=view&amp;view=%d', $this->i18n('plugin_contract'), $data['num'], $this->registry['config']->getParam('crontab', 'base_host'), $did),
                sprintf('%s: %s', $this->i18n('plugin_description'), $data['description']),
            );
            $event = array(
                'name' => $event_type->get('name'),
                'description' => implode("\n", $description),
                'type' => $this->settings['event_type'],
                'event_start' => $data['date'],
                'event_end' => $data['date'],
                'allday_event' => 1,
                'customer' => $data['customer'],
            );
            $this->registry['db']->StartTrans();
            $event = new Event($this->registry, $event);
            if ($event->save()) {
                $this->registry->set('getAssignments', true, true);
                //add some history
                $filters = array('where' => array('e.id = ' . $event->get('id')),
                                 'model_lang' => $event->get('model_lang'));
                $new_event = Events::searchOne($this->registry, $filters);
                $old_model = new Event($this->registry);
                $old_model->sanitize();

                Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'add', 'new_model' => $new_event, 'old_model' => $old_model));
                //create relation between the event and the document
                if ($new_event->updateDocumentRelatives(array($did))) {
                    Events_History::saveData($this->registry, array('model' => $new_event, 'action_type' => 'relatives'));
                }

                $old_event = clone $event;
                $event = clone $new_event;
                //set observers
                $observers = preg_split('#\s*,\s*#', $this->settings['users_observers']);
                $new_users = array();
                foreach ($observers as $o) {
                    $new_users[$o] = array('participant_id' => $o, 'ownership' => 'other', 'access' => 'view');
                }
                $event->set('new_users', $new_users, true);
                $old_event->set('customers_participants', array(), true);
                $old_event->set('users_participants', array(), true);
                $old_event->set('users_edit', array(), true);
                $old_event->set('users_view', array(), true);
                $event->unsanitize();
                if ($event->assign()) {
                    $filters = array('where' => array('e.id = ' . $event->get('id')),
                        'model_lang' => $event->get('model_lang'));
                    $new_event = Events::searchOne($this->registry, $filters);

                    Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'assign', 'new_model' => $new_event, 'old_model' => $old_event));
                } else {
                    $error = true;
                }
            } else {
                $error = true;
            }
            if ($error) {
                $this->registry['db']->FailTrans();
            }
            if ($this->registry['db']->HasFailedTrans()) {
                $errors = true;
            }
            $this->registry['db']->CompleteTrans();
        }

        if ($this->registry['module'] == 'crontab') {
            $this->updateAutomationHistory($params, 0, intval(!$errors));
        }

        return !$errors;
    }
}

?>

