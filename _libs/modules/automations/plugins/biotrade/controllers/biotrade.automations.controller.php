<?php

class Biotrade_Automations_Controller extends Automations_Controller {
    /**
     * Vars used for Production requests creation (doCreateProductionRequests)
     */
    private $_productionVars;

    /**
     * Recipes used in a recursive creation of production requests
     */
    private $_recipes;

    /**
     * Update the "assigned" field of all customers wich have selected the current region (i.e. the current nomenclature)
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function updateRegionAgent($params) {
        // Execute the automation only when editing the nomenclature
        if ($this->registry['action'] == 'edit') {
            // Prepare some basic data
            $registry  = $this->registry;
            $db        = $registry['db'];

            // Get the current nomenclature model
            $region = $params['model'];
            if ($region->isSanitized()) {
                $region->unsanitize();
            }

            // Get the customer types set into the automation settings
            $customer_types = array_filter(explode(',', (empty($this->settings['customer_types']) ? '' : preg_replace('/\s+/', '', $this->settings['customer_types']))));

            // Get the region_id fields for the selected customer types (if no customer types are set, then get this fields for all model types)
            $query = 'SELECT `id`' . "\n" .
                     '  FROM `' . DB_TABLE_FIELDS_META . '`' . "\n" .
                     '  WHERE `model` = \'Customer\'' . "\n" .
                     (empty($customer_types) ? '' : '    AND `model_type` IN (\'' . implode('\', \'', $customer_types) . '\')' . "\n") .
                     '    AND `name` = \'region_id\'';
            $fields_region_id = $db->getCol($query);

            // Get the ids of the customers that have selected the current region (i.e. the current nomenclature)
            $query = 'SELECT `model_id`' . "\n" .
                     '  FROM `' . DB_TABLE_CUSTOMERS_CSTM . '`' . "\n" .
                     '  WHERE `var_id` IN (\'' . implode('\', \'', $fields_region_id) . '\')' . "\n" .
                     '    AND `value` = \'' . $params['model_id'] . '\'';
            $customers_ids = $db->GetCol($query);

            // If no customers have selected this region
            if (empty($customers_ids)) {
                // Just exit
                return true;
            }

            // Get the models of the customers
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('where'      => array('c.id IN (\'' . implode('\', \'', $customers_ids) . '\')'),
                             'model_lang' => $region->get('model_lang'),
                             'sanitize'   => false);
            $customers = Customers::search($registry, $filters);

            // Prepare the history classes
            require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
            require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';

            // Go through all customer models
            foreach ($customers as $customer) {
                // Get the old customer model
                $customer_old_model = clone $customer;

                // Set the values for "department" and "assigned" or clear them
                $agent_id = $region->getVarValue('agent_id');
                $customer->set('department', (empty($agent_id) ? '0' : '1'),       true);
                $customer->set('assigned',   (empty($agent_id) ? '0' : $agent_id), true);

                // Save the customer and if it's saved correctly
                if ($customer->save()) {
                    // Get the new model for the current customer
                    $filters            = array('where'    => array('c.id = \'' . $customer->get('id') . '\''),
                                                'sanitize' => true);
                    $customer_new_model = Customers::searchOne($registry, $filters);

                    // Prepare the history vars
                    $history_vars = array('model'       => $customer_new_model,
                                          'action_type' => 'edit',
                                          'new_model'   => $customer_new_model,
                                          'old_model'   => $customer_old_model);

                    // Save this change in the history of the current customer
                    Customers_History::saveData($registry, $history_vars);
                } else {
                    // Raise an error
                    $layout = $customer->getLayoutsDetails('assigned');
                    $url = sprintf('%s?%s=customers&amp;customers=view&amp;view=%d',
                        $_SERVER['PHP_SELF'],
                        $this->registry['module_param'],
                        $customer->get('id'));
                    $registry['messages']->setError($this->i18n('error_update_customer_assigned', array($layout['name'], $customer->get('type_name'), $url, trim($customer->get('name') . ' ' . $customer->get('lastname')))));
                    $registry['messages']->insertInSession($registry);
                }
            }
        }

        return true;
    }

    /**
     * Update the "assigned" field of the current customer according to the selected region
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function updateCustomerAssigned($params) {
        // Execute the automation only when adding/editing the additional data of the customers
        if ($this->registry['request']->isPost() && in_array($this->registry['action'], array('add', 'addquick', 'edit'))) {
            // Prepare some basic data
            $registry  = $this->registry;
            $db        = $registry['db'];

            // Get the current customer model
            $customer = $params['model'];
            if ($customer->isSanitized()) {
                $customer->unsanitize();
            }

            // Prepare the default values for "department" and "assigned"
            $department = '0';
            $assigned   = '0';

            // Get the region id
            $region_id = $customer->getVarValue('region_id');

            // If there is a region id
            if (!empty($region_id)) {
                // Get the region model (i.e. the nomenclature model)
                require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                $filters = array('where'      => array('n.id = \'' . $region_id . '\''),
                                 'model_lang' => $params['model']->get('model_lang'),
                                 'sanitize'   => false);
                $region = Nomenclatures::searchOne($registry, $filters);

                // Get the region agent
                $agent_id = $region->getVarValue('agent_id');

                // If there is a region agent
                if (!empty($agent_id)) {
                    // Set a default value for the "department": All (1)
                    $department = '1';
                    // Set the "assigned" to be the agent from the region
                    $assigned   = $agent_id;
                }
            }

            // Get the old model of the customer
            $customer_old_model = clone $customer;

            // Set the "department" and the "assigned" into the customer model
            $customer->set('department', $department, true);
            $customer->set('assigned',   $assigned,   true);

            // Prepare the history classes
            require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
            require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';

            // Save the customer and if it's saved correctly
            if ($customer->save()) {
                // Get the new model for the current customer
                $filters            = array('where'    => array('c.id = \'' . $customer->get('id') . '\''),
                                            'sanitize' => true);
                $customer_new_model = Customers::searchOne($registry, $filters);

                // Prepare the history vars
                $history_vars = array('model'       => $customer_new_model,
                                      'action_type' => 'edit',
                                      'new_model'   => $customer_new_model,
                                      'old_model'   => $customer_old_model);

                // Save this change in the history of the current customer
                Customers_History::saveData($registry, $history_vars);
            } else {
                // Raise an error
                $layout = $customer->getLayoutsDetails('assigned');
                $registry['messages']->setError($this->i18n('error_update_current_customer_assigned', array($layout['name'], $customer->get('type_name'), $url, trim($customer->get('name') . ' ' . $customer->get('lastname')))));
                $registry['messages']->insertInSession($registry);
            }
        }

        return true;
    }


    /**
     * Validates Production Card (document type 8):
     * 1. One production card only per recipe
     * 2. A material could be used only once in a step
     * 3. The quantities of the materials in the production card should be exactly the same as those in the recipe
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function validateProductionCard($params) {
        $result = true;

        // Execute the automation only when editing the additional data of the document
        if (in_array($this->registry['action'], array('add', 'edit')) && $this->registry['request']->isPost()) {
            //get the recipe
            $recipe_name = $params['model']->getVarValue('prod_recipe_name');
            $recipe_id   = $params['model']->getVarValue('prod_recipe_id');

            //1. One production card only per recipe
            //check if there are other documents type 8 with the recipe selected for this document
            $query = 'SELECT d.id, d.full_num' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                     '  ON dc.var_id=fm.id AND fm.name="prod_recipe_id" AND fm.model="Document" AND fm.model_type=8' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                     '  ON d.id=dc.model_id' . "\n" .
                     'WHERE dc.model_id!="' . $params['model']->get('id') . '" AND' . "\n" .
                     '      dc.value="' . $recipe_id . '"' . ' AND' . "\n" .
                     '      d.active=1 AND d.deleted_by=0 AND d.status!="closed"';
            $doc = $this->registry['db']->GetRow($query);
            if (!empty($doc)) {
                //Raise an error
                $url = sprintf('%s?%s=documents&amp;documents=view&amp;view=%d',
                               $_SERVER['PHP_SELF'],
                               $this->registry['module_param'],
                               $doc['id']);
                $this->registry['messages']->setError($this->i18n('error_production_card_not_unique', array($url, $doc['full_num'], $recipe_name)));
                $this->registry['messages']->insertInSession($this->registry);
                $result = false;
            }

            if (!$recipe_id) {
                //recipe is a mandatory field, the user should fill it first
                //no further action needed until it's filled
                return true;
            }

            //2. A material could be used only once in a step
            //3. The quantities of the materials in the production card should be exactly the same as those in the recipe
            //prepare materials in the production card
            $materials  = $params['model']->getVarValue('article_id');
            $material_n = $params['model']->getVarValue('article_name');
            $quantities = $params['model']->getVarValue('quantity');
            $steps      = $params['model']->getVarValue('Step');
            //sum the quantities by article_id
            $material_quantities = array();
            $material_names = array();
            $step_materials = array();
            $step_error = false;
            if (!$materials) {
                $materials = array();
            }
            foreach($materials as $idx => $id) {
                if (isset($material_quantities[$id])) {
                    $material_quantities[$id] += $quantities[$idx];
                } else {
                    $material_quantities[$id] = $quantities[$idx];
                    $material_names[$id] = $material_n[$idx];
                }
                //check if a step contains a material more than once
                if (!$step_error && !empty($steps[$idx])) {
                    $step = $steps[$idx];
                    if (isset($step_materials[$step]) && in_array($id, $step_materials[$step])) {
                        $step_error = true;
                        $result = false;
                    }
                    $step_materials[$step][] = $id;
                }
            }
            if ($step_error) {
                $this->registry['messages']->setError($this->i18n('error_production_card_step_material'));
                $this->registry['messages']->insertInSession($this->registry);
            }

            //get the recipe materials
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $recipe = Nomenclatures::searchOne($this->registry, array('where' => array('n.id = ' . $recipe_id)));
            $this->registry->set('get_old_vars', true, true);
            $recipe->getVarsForTemplate();
            $this->registry->set('get_old_vars', false, true);
            foreach($recipe->get('vars') as $var) {
                if ($var['name'] == 'product_group') {
                    $recipe_vars = $var;
                    break;
                }
            }
            //define measures
            $measures = array();
            foreach($recipe_vars['material_measure']['options'] as $option) {
                $measures[$option['option_value']] = $option['label'];
            }
            //define the idx of the variables within the grouping table
            $mat_id_idx       = array_search('product_id', $recipe_vars['names']);
            $mat_name_idx     = array_search('product_name', $recipe_vars['names']);
            $mat_measure_idx  = array_search('material_measure', $recipe_vars['names']);
            $mat_quantity_idx = array_search('product_num', $recipe_vars['names']);

            //prepare array with materials in the recipe (sum the quantities by material)
            $recipe_materials = array();
            foreach($recipe_vars['values'] as $idx => $values) {
                $mat_id = $values[$mat_id_idx];
                if (empty($mat_id)) {
                    //empty row
                    continue;
                }
                if (isset($recipe_materials[$mat_id])) {
                    $recipe_materials[$mat_id]['quantity'] += $values[3];
                } else {
                    $recipe_materials[$mat_id] = array(
                        'name'     => $values[$mat_name_idx],
                        'measure'  => !empty($values[$mat_measure_idx]) ? $measures[$values[$mat_measure_idx]] : '',
                        'quantity' => $values[$mat_quantity_idx]
                    );
                }
            }

            //check the quantities
            foreach($recipe_materials as $mat_id => $data) {
                if (!isset($material_quantities[$mat_id]) || $material_quantities[$mat_id] != $data['quantity']) {
                    if (!isset($recipe_url)) {
                        $recipe_url = sprintf('%s?%s=nomenclatures&amp;nomenclatures=view&amp;view=%d',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'],
                            $recipe_id);
                    }
                    $this->registry['messages']->setError($this->i18n('error_production_card_quantities_do_not_match',
                        array($recipe_url, $recipe_name, $data['name'], $data['quantity']  . ' ' . $data['measure'])));
                    $this->registry['messages']->insertInSession($this->registry);
                    $result = false;
                }
            }

            //check if there are some other materials which are not ingredients of the recipe
            foreach($material_quantities as $mat_id => $mat_quantity) {
                if (!isset($recipe_materials[$mat_id])) {
                    //this material is not ingredient of the recipe
                    if (!isset($recipe_url)) {
                        $recipe_url = sprintf('%s?%s=nomenclatures&amp;nomenclatures=view&amp;view=%d',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'],
                            $recipe_id);
                    }
                    $this->registry['messages']->setError($this->i18n('error_production_card_invalid_ingredient',
                        array($recipe_url, $recipe_name, $material_names[$mat_id])));
                    $this->registry['messages']->insertInSession($this->registry);
                    $result = false;
                }
            }
        }

        return $result;
    }

    /**
     * Validates Recipe (nomenclature type 5):
     * 1. Do not allow edit of the nomenclature if the corresponding production card is locked
     * 2. Raise an error if the target quantity is empty
     * 3. Raise a warning to update the production card if the recipe materials and quantities are changed.
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function validateRecipe($params) {
        $result = true;

        // Execute the automation only when editing the nomenclature
        if ($this->registry['action'] == 'edit') {
            // 1. Do not allow edit of the nomenclature if the corresponding production card is locked
            //get the corresponding production card
            $query = 'SELECT d.id, d.full_num, d.status' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                     '  ON dc.var_id=fm.id AND fm.name="prod_recipe_id" AND fm.model="Document" AND fm.model_type=8' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                     '  ON d.id=dc.model_id' . "\n" .
                     'WHERE dc.value="' . $params['model']->get('id') . '" AND' . "\n" .
                     '      d.active=1 AND d.deleted_by=0 AND d.status!="closed"';
            $doc = $this->registry['db']->GetRow($query);
            if (!empty($doc) && $doc['status'] == 'locked') {
                //Raise an error
                $url = sprintf('%s?%s=documents&amp;documents=view&amp;view=%d',
                               $_SERVER['PHP_SELF'],
                               $this->registry['module_param'],
                               $doc['id']);
                $this->registry['messages']->setError($this->i18n('error_production_card_locked', array($url, $doc['full_num'])));
                $this->registry['messages']->insertInSession($this->registry);
                $result = false;
            }

            if ($result && $this->registry['request']->isPost()) {
                //get the recipe materials
                $recipe = $params['model'];
                $recipe->getVarsForTemplate();
                foreach($recipe->get('vars') as $var) {
                    if ($var['name'] == 'product_group') {
                        $recipe_vars = $var;
                        break;
                    }
                }
                //define measures
                $measures = array();
                foreach($recipe_vars['material_measure']['options'] as $option) {
                    $measures[$option['option_value']] = $option['label'];
                }
                //define the idx of the variables within the grouping table
                $mat_id_idx       = array_search('product_id', $recipe_vars['names']);
                $mat_name_idx     = array_search('product_name', $recipe_vars['names']);
                $mat_measure_idx  = array_search('material_measure', $recipe_vars['names']);
                $mat_quantity_idx = array_search('product_num', $recipe_vars['names']);

                //prepare array with materials in the recipe (sum the quantities by material)
                $recipe_materials = array();
                if (!empty($recipe_vars['values'])) {
                    foreach($recipe_vars['values'] as $idx => $values) {
                        $mat_id = $values[$mat_id_idx];
                        if (empty($mat_id)) {
                            //empty row
                            continue;
                        }
                        if (isset($recipe_materials[$mat_id])) {
                            $recipe_materials[$mat_id]['quantity'] += $values[3];
                        } else {
                            $recipe_materials[$mat_id] = array(
                                'name'     => $values[$mat_name_idx],
                                'measure'  => !empty($values[$mat_measure_idx]) ? $measures[$values[$mat_measure_idx]] : '',
                                'quantity' => $values[$mat_quantity_idx]
                            );
                        }
                    }
                }

                //2. Raise an error if the target quantity is empty
                if (!empty($recipe_materials) && !$recipe->getVarValue('product_targetrecipe')) {
                    $this->registry['messages']->setError($this->i18n('error_recipe_target_quantity_empty'));
                    $this->registry['messages']->insertInSession($this->registry);
                    $result = false;
                }

                //3. Raise a warning to update the production card if the recipe materials and quantities are changed.
                if ($result && !empty($doc)) {
                    //prepare materials in the production card
                    require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                    $production_card = Documents::searchOne($this->registry, array('where' => array('d.id = ' . $doc['id'])));
                    $this->registry->set('get_old_vars', true, true);
                    $materials  = $production_card->getVarValue('article_id');
                    $quantities = $production_card->getVarValue('quantity');
                    $this->registry->set('get_old_vars', false, true);
                    //sum the quantities by article_id
                    $material_quantities = array();
                    foreach($materials as $idx => $id) {
                        if (isset($material_quantities[$id])) {
                            $material_quantities[$id] += $quantities[$idx];
                        } else {
                            $material_quantities[$id] = $quantities[$idx];
                        }
                    }

                    $cardNeedsUpdate = false;
                    //check the quantities
                    foreach($recipe_materials as $mat_id => $data) {
                        if (!isset($material_quantities[$mat_id]) || $material_quantities[$mat_id] != $data['quantity']) {
                            //one difference is enough to raise warning
                            $cardNeedsUpdate = true;
                            break;
                        }
                    }
                    //in the code above we checked if quantities and materials in the recipe exist and are the same quantity in the tech map
                    //now check if there is a material in the tech map that is not in the recipe materials
                    $recipeMats = array_keys($recipe_materials);
                    $recipeMissingMats = array_diff($materials,$recipeMats);
                    if ($recipeMissingMats || $cardNeedsUpdate) {
                        $url = sprintf('%s?%s=documents&amp;documents=view&amp;view=%d',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'],
                            $doc['id']);
                        $this->registry['messages']->setWarning($this->i18n('error_production_card_needs_update', array($url, $doc['full_num'])));
                        $this->registry['messages']->insertInSession($this->registry);
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Validates Lot (nomenclature type 15):
     * 1. Check if recipe is really product of recipe subtype
     * 2. One lot only per recipe
     * 3. Check if all products are really products of product subtype
     * 4. All products must contain recipe
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function validateLot($params) {
        $result = true;

        // Execute the automation when adding/editing nomenclature
        if (in_array($this->registry['action'], array('add', 'addquick', 'edit')) && $this->registry['request']->isPost()) {

            $db = $this->registry['db'];

            // get the recipe
            $recipe_name = $params['model']->getVarValue($this->settings['lot_recipe_var']);
            $recipe_id   = $params['model']->getVarValue($this->settings['lot_recipe_id_var']);

            // if there is a recipe filled in
            if ($recipe_id) {
                // 1. Check if it is really a nomenclature of type Product and subtype "recipe"
                $query = 'SELECT n.id' . "\n" .
                         'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nc' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                         '  ON nc.var_id=fm.id AND fm.name="' . $this->settings['product_subtype_var'] . '"' . "\n" .
                         '    AND fm.model="Nomenclature" AND fm.model_type="' . $this->settings['nom_type_product'] . '"' . "\n" .
                         'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                         '  ON n.id=nc.model_id' . "\n" .
                         'WHERE n.id=\'' . $recipe_id . '\'' . "\n" .
                         '  AND n.type=\'' . $this->settings['nom_type_product'] . '\'' . "\n" .
                         '  AND nc.value=\'' . $this->settings['product_subtype_recipe'] . '\'';
                $recipe_id = $db->getOne($query);

                if (!$recipe_id) {
                    $this->registry['messages']->setError($this->i18n('error_lot_recipe', array($recipe_name)), $this->settings['lot_recipe_var']);
                    $result = false;
                } else {
                    // 2. One lot only per recipe
                    // check if there are other nomenclatures of type 15 with the recipe selected for this nomenclature
                    $query = 'SELECT n.id, n.code' . "\n" .
                             'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc' . "\n" .
                             'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                             '  ON nc.var_id=fm.id AND fm.name="' . $this->settings['lot_recipe_id_var'] . '"' . "\n" .
                             '    AND fm.model="Nomenclature" AND fm.model_type="' . $params['start_model_type'] . '"' . "\n" .
                             'JOIN ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
                             '  ON n.id=nc.model_id' . "\n" .
                             'WHERE nc.model_id!="' . $params['model']->get('id') . '" AND' . "\n" .
                             '      nc.value="' . $recipe_id . '"' . ' AND' . "\n" .
                             '      n.active=1 AND n.deleted_by=0';
                    $nom = $db->GetRow($query);

                    if (!empty($nom)) {
                        // Raise an error
                        $url = sprintf('%s?%s=nomenclatures&amp;nomenclatures=view&amp;view=%d',
                                       $_SERVER['PHP_SELF'],
                                       $this->registry['module_param'],
                                       $nom['id']);
                        $this->registry['messages']->setError($this->i18n('error_lot_not_unique', array($url, $nom['code'], $recipe_name)));
                        $result = false;
                    }
                }
            }

            // get products
            $product_name = $params['model']->getVarValue($this->settings['lot_product_var']);
            $product_id = $params['model']->getVarValue($this->settings['lot_product_id_var']);

            // remove empty values, preserve indexes
            $product_id = array_filter($product_id);
            if ($product_id) {
                $product_id_unique = array_unique($product_id);
                // 3. Check if all products are really nomenclatures of type Product and subtype "product"
                $query = 'SELECT n.id' . "\n" .
                         'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nc' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                         '  ON nc.var_id=fm.id AND fm.name="' . $this->settings['product_subtype_var'] . '"' . "\n" .
                         '    AND fm.model="Nomenclature" AND fm.model_type="' . $this->settings['nom_type_product'] . '"' . "\n" .
                         'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                         '  ON n.id=nc.model_id' . "\n" .
                         'WHERE n.id IN (\'' . implode('\', \'', $product_id_unique) . '\')' . "\n" .
                         '  AND n.type=\'' . $this->settings['nom_type_product'] . '\'' . "\n" .
                         '  AND nc.value=\'' . $this->settings['product_subtype_product'] . '\'';
                $product_id_found = $db->getCol($query);

                foreach ($product_id as $row_idx => $pid) {
                    if (!in_array($pid, $product_id_found)) {
                        $this->registry['messages']->setError(
                            $this->i18n('error_lot_product',
                                        array(($row_idx+1),
                                              (isset($product_name[$row_idx]) ? $product_name[$row_idx] : '-'))),
                            $this->settings['lot_product_var']
                        );
                        $result = false;
                    }
                }

                // if there are a recipe and products specified and all are valid
                if ($recipe_id && count($product_id_found) == count($product_id_unique)) {
                    //4. Check if all products contain recipe

                    // get var id just once
                    $query = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                             'WHERE model=\'Nomenclature\'' . "\n" .
                             '  AND model_type=\'' . $this->settings['nom_type_product'] . '\'' . "\n" .
                             '  AND name=\'' . $this->settings['product_article_id_var'] . '\'';
                    $product_id_var_id = $db->GetOne($query);

                    $product_id_material = array();
                    foreach ($product_id_unique as $pid) {
                        // clear sub-products
                        $this->_recipes = array();
                        // collect all sub-products
                        $this->doValidateLot(array(
                            'product_id_var_id' => $product_id_var_id,// var id
                            'recipe_id'         => $recipe_id,        // the recipe we are looking for
                            'product_id'        => $pid,              // current product
                            'level'             => 1                  // level of recursion
                        ));
                        // if product contains recipe as sub-product
                        if (in_array($recipe_id, $this->_recipes)) {
                            $product_id_material[] = $pid;
                        }
                    }

                    foreach ($product_id as $row_idx => $pid) {
                        if (!in_array($pid, $product_id_material)) {
                            $this->registry['messages']->setError(
                                $this->i18n('error_lot_product_not_from_recipe',
                                            array(($row_idx+1),
                                                  $recipe_name,
                                                  (isset($product_name[$row_idx]) ? $product_name[$row_idx] : '-'))),
                                $this->settings['lot_product_var']
                            );
                            $result = false;
                        }
                    }
                }
            }

            // clear extra whitespace
            $this->registry['request']->clearWhitespace('name');

            // if validation has failed, set all error messages in session
            if (!$result) {
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        return $result;
    }

    /**
     * Additional recursive function used by validateLot
     *
     * @param array $params - array containing product_id, recipe_id and level of recursion
     * @return bool         - result of the execution of the method
     */
    public function doValidateLot($params) {
        //stop endless recursions to keep the code safe, this should not happen though
        if ($params['level'] >= 50) {
            $this->registry['messages']->setError($this->i18n('error_production_request_recursion'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        $db = $this->registry['db'];

        $query = 'SELECT nc1.value as id,' . "\n" .
                 //this subquery checks if the material has its own recipe
                 '       (SELECT COUNT(nc3.model_id) FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc3 ' . "\n" .
                 '        WHERE nc3.var_id="' . $params['product_id_var_id'] . '" AND ' . "\n" .
                 '              nc3.model_id=nc1.value AND nc3.value!="") as subrecipes' . "\n" .
                 'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc1' . "\n" .
                 'WHERE nc1.model_id="' . $params['product_id'] . '"' . "\n" .
                 '  AND nc1.var_id="' . $params['product_id_var_id'] . '"' . "\n" .
                 'GROUP BY nc1.value';
        $materials = $db->GetAssoc($query);

        foreach($materials as $mid => $subrecipes) {
            if ($subrecipes > 0) {
                //check for endless recursion
                if (!empty($this->_recipes) && in_array($mid, $this->_recipes)) {
                    $this->registry['messages']->setError($this->i18n('error_production_request_recursion'));
                    return false;
                } else {
                    $this->_recipes[] = $mid;
                }

                if ($mid == $params['recipe_id']) {
                    // recipe is found, no need to continue
                    return true;
                }

                //RECURSE FOR FURTHER CHECK
                $result = $this->doValidateLot(array(
                    'product_id_var_id' => $params['product_id_var_id'],// var id
                    'recipe_id'         => $params['recipe_id'],        // the recipe we are looking for
                    'product_id'        => $mid,                        // current product
                    'level'             => ++$params['level']           // level of recursion
                ));
            }
        }

        return true;
    }

    /**
     * Creates production requests recursively
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function createProductionRequests($params) {

        if ($this->registry['action'] == 'setstatus' && $this->registry['request']->get('substatus') == 'locked_4') {
            //do not allow status change if the article and quantity are empty
            if (!$params['model']->getVarValue('article_id') || !$params['model']->getVarValue('article_quantity')) {
                $this->registry['messages']->setError($this->i18n('error_production_request_empty_article_quantity'));
                $this->registry['messages']->insertInSession($this->registry);
                return false;
            }

            //as per Bug 3853, comment 6: do not create sub request if the subtype of article is packet (value 2)
            $query = 'SELECT nc.value' . "\n" .
                     'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc' . "\n" .
                     'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                     '  ON nc.var_id=fm.id AND fm.name="subtype_product" AND fm.model="Nomenclature" AND fm.model_type=5' . "\n" .
                     'WHERE nc.model_id="' . $params['model']->getVarValue('article_id') . '"';
            $subtype = $this->registry['db']->GetOne($query);
            if ($subtype == 2) {
                //the requested product is a packet (subtype: 2) - do not add subrequests
                return true;
            }

            //change the current user to be the original one so that all the attributes (added, modified, default assignments)
            //are taken from the user, not the automation system
            $this->setOriginalUserAsCurrent();

            //put the entire process in a transaction
            //IMPORTANT: The automation should be before_action with cancel_action_on_fail turned ON
            //           so that the entire process falls back elegantly
            $this->registry['db']->StartTrans();

            $result = $this->doCreateProductionRequests(array(
                'parent_request' => $params['model'],
                'recipe_id'      => $params['model']->getVarValue('article_id'),
                'quantity'       => $params['model']->getVarValue('article_quantity'),
                'level'          => 1,
            ));

            if (!$result || $this->registry['db']->HasFailedTrans()) {
                $this->registry['messages']->setError($this->i18n('error_production_request_creation_failed'));
                $this->registry['messages']->insertInSession($this->registry);
                return false;
            }

            $this->registry['db']->CompleteTrans();

            //restore the current user
            $this->setAutomationUserAsCurrent();
        }

        return true;
    }

    /**
     * Additional recursive function used by createProductionRequests
     *
     * @param array $params - array containing parent_request, recipe_id, quantity and level of recursion
     * @return bool         - result of the execution of the method
     */
    public function doCreateProductionRequests($params) {
        //stop endless recursions to keep the code safe, this should not happen though
        if ($params['level'] >= 50) {
            $this->registry['messages']->setError($this->i18n('error_production_request_recursion'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        //prepare some variables
        if (empty($this->_productionVars)) {
            $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Nomenclature" AND model_type=5 AND name IN ("product_id", "product_num", "product_targetrecipe", "material_measure")';
            $this->_productionVars = $this->registry['db']->GetAssoc($query);
        }

        //get the variables from the parent
        $params['parent_request']->getVars();

        //get the containing recipes (sum the quantities by material)
        $query = 'SELECT nc1.value as id, SUM(nc2.value) as quantity, nc4.value as target_quantity, nc5.value as measure, ni.name, n.`group`,' . "\n" .
                 //this subquery checks if the material has its own recipe
                 '       (SELECT COUNT(nc3.model_id) FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc3 ' . "\n" .
                 '        WHERE nc3.var_id="'.$this->_productionVars['product_id'].'" AND ' . "\n" .
                 '              nc3.model_id=nc1.value AND nc3.value!="") as subrecipes' . "\n" .
                 'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc1' . "\n" .
                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc2' . "\n" .
                 '  ON nc1.var_id=' . $this->_productionVars['product_id'] . ' AND nc2.var_id=' . $this->_productionVars['product_num'] . ' AND nc1.num=nc2.num AND nc1.model_id=nc2.model_id' . "\n" .
                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc4' . "\n" .
                 '  ON nc4.var_id=' . $this->_productionVars['product_targetrecipe'] . ' AND nc4.model_id=nc1.model_id' . "\n" .
                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc5' . "\n" .
                 '  ON nc5.var_id=' . $this->_productionVars['material_measure'] . ' AND nc5.num=nc1.num AND nc5.model_id=nc1.model_id' . "\n" .
                 'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                 '  ON nc1.value=n.id' . "\n" .
                 'JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as ni' . "\n" .
                 '  ON n.id=ni.parent_id AND ni.lang="' . $this->registry['lang'] . '"' . "\n" .
                 'WHERE nc1.model_id=' . $params['recipe_id']. "\n" .
                 'GROUP BY nc1.value';
        $materials = $this->registry['db']->GetAll($query);
        foreach ($materials as $material) {

            //SUBRECIPES FOUND: RECURSIVELY ADD PRODUCTION REQUESTS
            if ($material['subrecipes'] > 0) {
                //CHECK IF CHILD REQUEST SHOULD BE ADDED (check the relations)
                //IMPORTANT: the recipe id is stored in documents_relatives.group_index field, origin is "transformed"
                $query = 'SELECT dr.parent_id' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' as dr ' . "\n" .
                         'JOIN ' . DB_TABLE_DOCUMENTS . ' as d '  . "\n" .
                         '  ON d.id=dr.parent_id AND d.active AND d.deleted_by=0 AND d.status!="closed"'  . "\n" .
                         'WHERE dr.link_to=' . $params['parent_request']->get('id') . ' AND ' . "\n" .
                         '      dr.link_to_model_name="Document" AND ' . "\n" .
                         '      dr.parent_model_name="Document" AND ' . "\n" .
                         '      dr.origin="transformed" AND ' . "\n" .
                         '      dr.group_index="' . $material['id'] . '"';
                $child_production_request_id = $this->registry['db']->GetOne($query);
                if (!$child_production_request_id) {
                    //THE CHILD PRODUCTION REQUEST HAS NOT BEEN ADDED YET, ADD CHILD REQUEST NOW
                    $properties = array(
                        'type'           => $params['parent_request']->get('type'),
                        'custom_num'     => '', //ToDo: define custom number if needed
                        'office'         => $params['parent_request']->get('office'),
                        'employee'       => $params['parent_request']->get('employee'),
                        'media'          => $params['parent_request']->get('media'),
                        'customer'       => $params['parent_request']->get('customer'),
                        'branch'         => $params['parent_request']->get('branch'),
                        'contact_person' => $params['parent_request']->get('contact_person'),
                        'trademark'      => $params['parent_request']->get('trademark'),
                        'contract'       => $params['parent_request']->get('contract'),
                        'project'        => $params['parent_request']->get('project'),
                        'group'          => $material['group'],
                        'department'     => $params['parent_request']->get('department'),
                        'deadline'       => $params['parent_request']->get('deadline'),
                        'validity_term'  => $params['parent_request']->get('validity_term'),
                        'date'           => $params['parent_request']->get('date'),
                        'active'         => $params['parent_request']->get('active'),
                        //i18n
                        'name'           => $this->i18n('production_request_name_pattern', array($material['name'], $params['parent_request']->get('full_num'))),
                        'description'    => '',
                        'notes'          => '',
                    );
                    $child = new Document($this->registry, $properties);
                    if ($child->save()) {
                        $filters = array('where' => array('d.id = ' . $child->get('id')),
                                         'model_lang' => $child->get('model_lang'),
                                         'skip_assignments' => true,
                                         'skip_permissions_check' => true);
                        $new_document = Documents::searchOne($this->registry, $filters);
                        $old_model = new Document($this->registry);
                        $old_model->sanitize();
                        $audit_parent = Documents_History::saveData($this->registry, array('model' => $child, 'action_type' => 'add', 'new_model' => $new_document, 'old_model' => $old_model));


                        //SAVE ADDITIONAL VARS OF THE CHILD
                        $vars = $params['parent_request']->get('vars');

                        //change the var values
                        foreach ($vars as $idx => $var) {
                            switch($var['name']) {
                                case 'article':
                                    $vars[$idx]['value'] = $material['name'];
                                    break;
                                case 'article_id':
                                    $vars[$idx]['value'] = $material['id'];
                                    break;
                                case 'article_quantity':
                                    $vars[$idx]['value'] = round($params['quantity'] * $material['quantity'] / $material['target_quantity'], 2);
                                    break;
                                case 'article_me':
                                    $vars[$idx]['value'] = $material['measure'];
                                    break;
                                default:
                                    //the rest of the vars should be nilled
                                    if (isset($vars[$idx]['value'])) {
                                        $vars[$idx]['value'] = array();
                                    }
                            }
                        }

                        $old_document = clone $new_document;
                        $old_document->getVars();
                        $old_document->sanitize();
                        $child->set('vars', $vars, true);
                        // temporarily allow edit of all layouts
                        $this->registry->set('edit_all', true, true);
                        // update data for additional variables of model
                        if ($child->saveVars()) {
                            $filters = array('where' => array('d.id = ' . $child->get('id')),
                                             'model_lang' => $child->get('model_lang'),
                                             'skip_assignments' => true,
                                             'skip_permissions_check' => true);
                            $new_document = Documents::searchOne($this->registry, $filters);
                            $new_document->getVars();
                            $audit_parent = Documents_History::saveData($this->registry, array('model' => $new_document, 'action_type' => 'edit', 'new_model' => $new_document, 'old_model' => $old_document));
                        }
                        $this->registry->set('edit_all', false, true);


                        //SET STATUS (id: 4)
                        $old_document = clone $new_document;
                        $old_document->sanitize();
                        $child->set('status', 'locked', true);
                        $child->set('substatus', 'locked_4', true);
                        if ($child->setStatus()) {
                            $filters = array('where' => array('d.id = ' . $child->get('id')),
                                             'model_lang' => $child->get('model_lang'),
                                             'skip_assignments' => true,
                                             'skip_permissions_check' => true);
                            $new_document = Documents::searchOne($this->registry, $filters);

                            $audit_parent = Documents_History::saveData($this->registry, array('model' => $child, 'action_type' => 'status', 'new_model' => $new_document, 'old_model' => $old_document));
                        } else {
                            $this->registry['db']->FailTrans();
                        }

                        if (!$this->registry['db']->HasFailedTrans()) {
                            //ASSIGNMENTS (GET DEFAULT FOR THE CURRENT USER)
                            $new_document->defaultAssign(true, false);
                        }

                        //SET RELATIVES RECORD
                        $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_RELATIVES .
                                 '(parent_id, parent_model_name, link_to, link_to_model_name, origin, multi_index, group_index) VALUES ' . "\n" .
                                 '('. $child->get('id') . ', "Document", ' . $params['parent_request']->get('id'). ', "Document", "transformed", 0, ' . $material['id'] . ')';
                        $this->registry['db']->Execute($query);
                    }
                } else {
                    $child = Documents::searchOne($this->registry, array('where' => array('d.id = ' . $child_production_request_id)));
                }

                //check for endless recursion
                if (!empty($this->_recipes) && in_array($material['id'], $this->_recipes)) {
                    $this->registry['messages']->setError($this->i18n('error_production_request_recursion'));
                    $this->registry['messages']->insertInSession($this->registry);
                    return false;
                } else {
                    $this->_recipes[] = $material['id'];
                }

                //RECURSE FOR FURTHER CREATING CHILDREN REQUESTS
                $result = $this->doCreateProductionRequests(array(
                    'parent_request'    => $child,
                    'recipe_id'         => $material['id'],
                    'quantity'          => round($params['quantity']*$material['quantity']/$material['target_quantity'], 2),
                    'level'             => ++$params['level'],
                ));
            }
        }

        return true;
    }

    /**
     * Validate if the entered quantity is an integer when the measure is 'unit'
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function validateCountNumber($params) {
        $result = true;
        if ($this->registry['request']->isPost() && in_array($this->registry['action'], array('add', 'edit'))) {
            $request = $params['model'];
            $request->getVarsForTemplate();

            $measure_value = 0;
            $quantity_value = 0;

            foreach($request->get('vars') as $var) {
                if ($quantity_value && $measure_value) {
                    break;
                } else {
                    if ($var['name'] == $this->settings['quantity_field']) {
                        $quantity_value = $var['value'];
                    } elseif ($var['name'] == $this->settings['measure_field']) {
                        $measure_value = $var['value'];
                    }
                }
            }

            // check the measure value
            if ($measure_value && $measure_value == $this->settings['measure_unit'] && $quantity_value) {
                // check if quantity value is an integer
                $quantity_value = (float)$quantity_value;
                $quantity_value_whole_number = floor($quantity_value);
                if (($quantity_value - $quantity_value_whole_number) > 0) {
                    // if
                    $this->registry['messages']->setError($this->i18n('error_only_whole_quantity_values_allowed'));
                    $this->registry['messages']->insertInSession($this->registry);
                    $result = false;
                }
            }
        }

        return $result;
    }

    /**
     * Remove "Initial supply" tag from Pharmacies (customer of type 2)
     * that have a revenue document of "Sale" type or an invoice issued for
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function removeInitialSupplyTag($params) {
        $result = true;

        $this->registry['db']->StartTrans();

        $query = 'SELECT c.id' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                 'JOIN ' . DB_TABLE_TAGS_MODELS . ' AS ct' . "\n" .
                 '  ON ct.model=\'Customer\' AND c.id=ct.model_id' . "\n" .
                 '    AND c.type=\'' . $params['start_model_type'] . '\'' . "\n" .
                 '    AND ct.tag_id=\'' . $this->settings['tag_id'] . '\'' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON fir.customer=c.id' . "\n" .
                 '    AND fir.type IN (\'' . PH_FINANCE_TYPE_INVOICE . '\', \'' . $this->settings['fir_type_sale'] . '\')' . "\n" .
                 '    AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                 'GROUP BY c.id';
        $customer_ids = $this->registry['db']->GetCol($query);

        if ($customer_ids) {
            $params['tags'] = $this->settings['tag_id'];
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            foreach ($customer_ids as $id) {
                $params['model'] = new Customer($this->registry, array('id' => $id));
                $params['model_id'] = $id;
                $this->removeTags($params);
            }
        }

        $result = !$this->registry['db']->HasFailedTrans();

        $this->registry['db']->CompleteTrans();

        $this->updateAutomationHistory($params, 0, intval($result));

        return $result;
    }

    /**
     * Finishes sells and visits documents and issues handovers for them
     * we will work close with the weekly schedule dashlet
     * @param array $params
     */
    public function finishVisitsAndSells($params) {

        set_time_limit(3600);

        //get dashlet settings
        $query = 'SELECT settings FROM ' . DB_TABLE_DASHLETS_PLUGINS . ' WHERE type = "biotrade_week_schedule"';
        $settings = $this->registry['db']->GetOne($query);

        //create an instance of the custom dashlet model
        require_once PH_MODULES_DIR  . 'dashlets/plugins/biotrade_week_schedule/models/custom.model.php';
        $model = new Custom_Model($settings);

        //load plugin i18n file
        $this->registry['translater']->loadFile(PH_MODULES_DIR  . 'dashlets/plugins/biotrade_week_schedule/i18n/' . $this->registry['lang'] . '/custom.ini');

        //get all not finished visits and sells
        $included_types = array();
        if (!empty($model->visit_type)) {
            $included_types[] = $model->visit_type;
        }
        if (!empty($model->sell_type)) {
            $included_types[] = $model->sell_type;
        }
        $filters = array(
            'where' => array(
                'fir.status != "finished"',
                'fir.type IN ("' . implode('","', $included_types) . '")',
                'fir.active = 1',
                'fir.annulled_by = 0'
            ),
            'sort' => array('fir.added ASC'),
            'sanitize' => true,
        );
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        $firs = Finance_Incomes_Reasons::search($this->registry, $filters);

        $result = array();
        $original_user = $this->registry['currentUser'];
        $base_host = $this->registry['config']->getParam('crontab', 'base_host');
        $users = array();
        foreach ($firs as $idx => &$fir) {
            $fir->unsanitize();
            $this->registry->set('get_old_vars', true, true);
            $fir->getGT2Vars();
            $this->registry->set('get_old_vars', false, true);
            $uid = $fir->get('added_by');
            if (empty($users[$uid])) {
                $filters = array('where' => array('u.id = ' . $uid, 'u.hidden IS NOT NULL'), 'sanitize' => true);
                $users[$uid] = Users::searchOne($this->registry, $filters);
            }
            $this->registry->set('currentUser', $users[$uid], true);

            $this->registry['db']->StartTrans();
            $fir->set('status', 'finished', true);
            $result[$uid][$fir->get('id')] = array(
                    'customer' => $fir->get('customer_name')
            );
            $type_link = sprintf(
                '<a href="%s/index.php?launch=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view=%d">%s</a>',
                $base_host, $fir->get('id'),
                htmlentities($fir->get('type_name'), ENT_COMPAT, 'UTF-8')
            );
            $cstm_link = sprintf(
                '<a href="%s/index.php?launch=customers&amp;customers=view&amp;view=%d">%s</a>',
                $base_host, $fir->get('customer'),
                htmlentities($fir->get('customer_name'), ENT_COMPAT, 'UTF-8')
            );
            if (!$fir->setStatus()) {
                $result[$uid][$fir->get('id')]['errors'] = array($this->i18n('plugin_error_change_status', array($type_link, $cstm_link)));
                $this->registry['db']->FailTrans();
            } else {
                $messages = $model->checkAddOutgoingHandover($fir);
                if (!empty($messages)) {
                    $result[$uid][$fir->get('id')]['errors'] = $messages;
                    $this->registry['db']->FailTrans();
                } else {
                    $messages = $model->addOutgoingHandover($fir);
                    if ($err = $this->registry['messages']->getErrors()) {
                        $messages = array_merge($messages, $err);
                    }
                    if (!empty($messages)) {
                        $result[$uid][$fir->get('id')]['errors'] = $messages;
                        $this->registry['db']->FailTrans();
                    }
                }
            }
            if (!empty($result[$uid][$fir->get('id')]['errors'])) {
                $this->registry['db']->FailTrans();
            }

            $res = !$this->registry['db']->HasFailedTrans();
            $this->registry['db']->CompleteTrans();
            if (!$res) {
                array_unshift($result[$uid][$fir->get('id')]['errors'], $this->i18n('plugin_errors_for_document', array($type_link, $cstm_link)));
            } else {
                unset($result[$uid][$fir->get('id')]);
                if (empty($result[$uid])) {
                    unset($result[$uid]);
                }
            }
            $fir->sanitize();
            // remove processed element from array
            unset($firs[$idx]);

            $this->registry['messages']->flush();
        }
        unset($fir);
        unset($model);
        $this->registry->set('currentUser', $original_user, true);
        if (!empty($result)) {
            //send errors to users
            $template = 'finish_sell_visit_errors';
            $mailer = new Mailer($this->registry, $template);
            foreach ($result as $uid => $data) {
                $errors = array();
                foreach($data as $f => $d) {
                    $d = implode('<br />', $d['errors']);
                    $errors[] = $d;
                }
                $errors = implode('<br /><br />', $errors);
                $mailer->placeholder->add('errors_for_sells_visits', $errors);

                if ($users[$uid]->get('email')) {
                    $mailer->placeholder->add('to_email', $users[$uid]->get('email'));
                    $mailer->placeholder->add('user_name', sprintf('%s %s', $users[$uid]->get('firstname'), $users[$uid]->get('lastname')));

                    //send email
                    $result = $mailer->send();
                }
            }
            $result = false;
        } else {
            $result = true;
        }
        $this->updateAutomationHistory($params, 0, intval($result));
        return $result;
    }

    /**
     * Creates new batches for retested articles via inspection documents
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function articlesRetest($params) {

        if (($this->action != 'setstatus' ||
            $this->registry['request']->get('status') != 'closed' ||
            $params['model']->get('status') == 'closed')) {
            //this action has nothing to do with the automation
            return true;
        }
        $prec = $this->registry['config']->getSectionParams('precision');
        $vars = $params['model']->getAssocVars();
        $this->registry['db']->StartTrans();
        //start to prepare GT2 values for the inspection
        $values = $warehouses_changed = array();
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
        $max_batch = $batch = Finance_Warehouses::getNextBatch($this->registry);
        $batch_mapping = array();
        foreach ($vars['material_name_id']['value'] as $k => $v) {
            if (!$v || empty($vars['batch_name_id']['value'][$k]) || empty($vars['expired_date_new']['value'][$k])) {
                continue;
            }
            //get quantities and other stuff for all warehouses
            $query = 'SELECT wq.*, fb.code as batch_code FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . ' wq' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_BATCHES . ' fb' . "\n" .
                     '  ON fb.id = wq.batch_id' . "\n" .
                     ' WHERE wq.quantity > 0 AND wq.nomenclature_id = ' . $v;/* . ' AND wq.batch_id = ' . $vars['batch_name_id']['value'][$k] .
                     ' AND wq.expire_date = "' . $vars['expired_date']['value'][$k] . '"';*/
            $records = $this->registry['db']->GetAll($query);
            foreach($records as $r => $rec) {
                if (empty($values[$rec['parent_id']][$v])) {
                    $values[$rec['parent_id']][$v] = array(
                        'article_id' => $v,
                        'article_name' => $vars['material_name']['value'][$k],
                        'quantity' => 0,
                        'batch_data' => array(),
                        'last_delivery_price' => $rec['delivery_price'],
                        'price' => 0,
                        'has_batch' => true,
                    );
                }
                $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                    $this->registry['db'],
                    [
                        'parent_id'       => $rec['parent_id'],
                        'batch_id'        => $rec['batch_id'],
                        'expire_date'     => $rec['expire_date'],
                        'serial_number'   => $rec['serial_number'],
                        'cstm_number'     => $rec['cstm_number'],
                        'delivery_price'  => round($rec['delivery_price'], $prec['gt2_rows']),
                        'currency'        => $rec['currency'],
                    ]
                );
                $uk_new = Finance_Warehouses_Documents::buildBatchUniqueKey(
                    $this->registry['db'],
                    [
                        'parent_id'       => $rec['parent_id'],
                        'batch_id'        => "{$rec['batch_id']}_new",
                        'expire_date'     => $rec['expire_date'],
                        'serial_number'   => $rec['serial_number'],
                        'cstm_number'     => $rec['cstm_number'],
                        'delivery_price'  => round($rec['delivery_price'], $prec['gt2_rows']),
                        'currency'        => $rec['currency'],
                    ]
                );
                $bd = array(
                    'warehouse' => $rec['parent_id'],
                    'batch' => $rec['batch_id'],
                    'batch_code' => $rec['batch_code'],
                    'expire' => $rec['expire_date'],
                    'serial' => $rec['serial_number'],
                    'custom' => $rec['cstm_number'],
                    'delivery_price' => $rec['delivery_price'],
                    'currency' => $rec['currency'],
                    'quantity' => $rec['quantity'],
                );
                if (!isset($values[$rec['parent_id']][$v]['batch_data'][$uk])) {
                    $values[$rec['parent_id']][$v]['quantity'] += $rec['quantity'];
                }
                if ($rec['batch_id'] == $vars['batch_name_id']['value'][$k] && $rec['expire_date'] == $vars['expired_date']['value'][$k]) {
                    $bd['quantity'] = 0;
                    $warehouses_changed[] = $rec['parent_id'];
                    // the batch that has been re-tested
                    if (preg_match('#_rt(\d+)$#', $rec['batch_code'], $next)) {
                        $next = (intval($next[1]) + 1);
                    } else {
                        $next = 1;
                    }
                    // in case previously re-tested batch has been re-entered in db with same code
                    $existing_retest_max = $this->registry['db']->GetOne("
                        SELECT MAX(CONVERT(REPLACE(`code`, '{$rec['batch_code']}_rt', ''), SIGNED INT))
                        FROM " . DB_TABLE_FINANCE_BATCHES . "
                        WHERE `code` RLIKE '^{$rec['batch_code']}_rt[0-9]+$'
                    ");
                    // track old to new batch id mapping
                    if ($existing_retest_max && $existing_retest_max >= $next) {
                        $next = $existing_retest_max + 1;
                    }
                    if (!array_key_exists($rec['batch_id'], $batch_mapping)) {
                        $batch_mapping[$rec['batch_id']] = $max_batch++;
                    }
                    $bd_new = array(
                        'warehouse' => $rec['parent_id'],
                        'batch_code' => preg_replace('#_rt\d+$#', '', $rec['batch_code']) . '_rt' . $next,
                        'batch' => $batch_mapping[$rec['batch_id']],
                        'expire' => $vars['expired_date_new']['value'][$k],
                        'serial' => $rec['serial_number'],
                        'custom' => $rec['cstm_number'],
                        'delivery_price' => $rec['delivery_price'],
                        'currency' => $rec['currency'],
                        'quantity' => $rec['quantity'],
                    );
                    if (isset($values[$rec['parent_id']][$v]['batch_data'][$uk_new])) {
                        $this->registry['messages']->setError($this->i18n('plugin_error_batch_several_rows'));
                        $this->registry['db']->FailTrans();
                    }
                    $values[$rec['parent_id']][$v]['batch_data'][$uk] = $bd;
                    $values[$rec['parent_id']][$v]['batch_data'][$uk_new] = $bd_new;
                } elseif (!isset($values[$rec['parent_id']][$v]['batch_data'][$uk])) {
                    //other batch for the same article
                    $values[$rec['parent_id']][$v]['batch_data'][$uk] = $bd;
                }
            }
        }

        //prepare inspection document for each warehouse
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
        require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');

        // load i18n files for warehouse documents
        $lang = $this->registry['lang'];
        $lang_files_finance = array(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance.ini'),
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance_warehouses_documents.ini')
        );
        $this->loadI18NFiles($lang_files_finance);

        $this->registry->set('get_old_vars', true, true);
        foreach($values as $wh => $vals) {
            if (!in_array($wh, $warehouses_changed)) {
                continue;
            }
            //get warehouse company and office
            $query = 'SELECT *' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_WAREHOUSES . "\n" .
                     'WHERE id = ' . $wh;
            $wh = $this->registry['db']->GetRow($query);
            $inspection = array(
                'type' => PH_FINANCE_TYPE_INSPECTION,
                'date' => date('Y-m-d'),
                'department' => 1,
                'group' => 1,
                'currency' => Finance_Currencies::getMain($this->registry),
                'company' => $wh['company'],
                'office' => $wh['office'],
                'warehouse' => $wh['id'],
                'employees' => preg_split('#\s*,\s*#', $wh['employees']),
                'batches_prepared' => true,
                'table_values_are_set' => true
            );
            $inspection = new Finance_Warehouses_Document($this->registry, $inspection);
            $inspection->set('name', $inspection->get('type_name') . ' - ' . $params['model']->get('name'), true);
            $gt2 = $inspection->getGT2Vars();
            $gt2['values'] = array();
            unset($gt2['rows']);
            $i = -1;
            foreach ($vals as $v) {
                $gt2['values'][$i] = $v;
                $i--;
            }
            $gt2['process_batches'] = true;
            $inspection->set('grouping_table_2', $gt2, true);
            $inspection->calculateGT2();
            if ($inspection->save()) {
                //finish the inspection without differences documents
                //as all the quantities are the same
                $inspection->getNum();
                $query = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' SET status = "finished", num = "' . $inspection->get('num') . '" WHERE id = ' . $inspection->get('id');
                $this->registry['db']->Execute($query);

                $filters = array('where' => array('fwd.id = ' . $inspection->get('id')));
                $new = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                $new->getGT2Vars();
                $new->getBatchesData();
                $old = new Finance_Warehouses_Document($this->registry);
                // set type and company in order to prepare GT2 table in old model
                $old->set('type', PH_FINANCE_TYPE_INSPECTION, true);
                $old->set('company', $inspection->get('company'), true);
                $old->getGT2Vars();
                $old->sanitize();
                Finance_Warehouses_Documents_History::saveData($this->registry,
                    array('action_type' => 'add',
                          'new_model' => $new,
                          'old_model' => $old
                    )
                );
                //set new quantities in the warehouse
                $new->setWarehouseQuantities();
                //create relations between the document and the inspection
                $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_RELATIVES . ' SET ' . "\n" .
                         'parent_model_name = "Finance_Warehouses_Document",' . "\n" .
                         'link_to_model_name = "Document",' . "\n" .
                         'link_to = ' . $params['model']->get('id') . ",\n" .
                         'parent_id = ' . $new->get('id') . ",\n" .
                         'origin = "inherited"';
                $this->registry['db']->Execute($query);
                $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' SET ' . "\n" .
                         'parent_model_name = "Finance_Warehouses_Document",' . "\n" .
                         'link_to_model_name = "Document",' . "\n" .
                         'link_to = ' . $params['model']->get('id') . ",\n" .
                         'parent_id = ' . $new->get('id');
                $this->registry['db']->Execute($query);
            } else {
                $this->registry['db']->FailTrans();
            }
        }
        $this->registry->set('get_old_vars', false, true);
        $res = !$this->registry['db']->HasFailedTrans();

        $this->registry['db']->CompleteTrans();

        return $res;
    }
}

?>
