<?php
include_once __DIR__ . '/PsiroValueProcessorInterface.php';
/**
 * /**
 *  Utility class to process names as they are full strings in the PSIRO Db.
 *  Ex: name: YORDAN IVANOV IVANOV, index: 0, returns: YORDAN IVANOV.
 *
 * /
 */
class NameProcessor implements PsiroValueProcessorInterface {
    private $field;
    private $processIdx;

    public function __construct(int $processIdx, string $field)
    {
        $this->processIdx = $processIdx;
        $this->field = $field;
    }

    public function getField()
    {
        return $this->field;
    }

    public function useOnRow(array $psiroRow)
    {
        $value = $psiroRow[$this->field] ?? null;
        if (is_null($value)) {
            return '';
        }

        $lastSpacePosition = strrpos($value, ' ');

        // Split the string into two parts
        $nameSplit = [
            substr($value, 0, $lastSpacePosition),
            substr($value, $lastSpacePosition + 1)
        ];
        return $nameSplit[$this->processIdx];

    }
}
