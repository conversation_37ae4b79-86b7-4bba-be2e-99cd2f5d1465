<?php

trait syncObjectsCustomers {

    public $current_document_model;
    public $old_document_model;
    public $execution_messages = array();

    /**
     * Automation to find any changes in the
     *
     * @param $params - params for the automation
     * @return bool
     */
    public function syncObjectsAndCustomers($params): bool
    {
        $this->current_document_model = clone $params['model'];
        $this->old_document_model = clone $this->current_document_model->get('old_model');


        $old_vars = $this->old_document_model->get('vars');
        $old_vars_names = array_column($old_vars, 'name');
        $old_vars = array_combine($old_vars_names, $old_vars);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $new_vars = $this->current_document_model->getAssocVars();

        // get differences
        $customers_list = array();
        $roles_list = array('owner', 'coowner', 'tenant');
        foreach ($roles_list as $rl) {
            $nom_var_nm = sprintf('nom_%s_var', $rl);
            if (empty($this->settings[$nom_var_nm]) ||
                !isset($new_vars[$this->settings[$nom_var_nm]]) ||
                !isset($old_vars[$this->settings[$nom_var_nm]])) {
                continue;
            }

            $old_role_values = empty($old_vars[$this->settings[$nom_var_nm]]['value']) ? '' : $old_vars[$this->settings[$nom_var_nm]]['value'];
            $old_role_values = array_filter(is_array($old_role_values) ? $old_role_values : array($old_role_values));
            $new_role_values = empty($new_vars[$this->settings[$nom_var_nm]]['value']) ? '' : $new_vars[$this->settings[$nom_var_nm]]['value'];
            $new_role_values = array_filter(is_array($new_role_values) ? $new_role_values : array($new_role_values));

            // check for removed customers
            foreach (array_diff($old_role_values, $new_role_values) as $remv) {
                $customers_list[] = array(
                    'id'     => $remv,
                    'role'   => $rl,
                    'status' => 'removed',
                );
            }
            // check for added customers
            foreach (array_diff($new_role_values, $old_role_values) as $add) {
                $customers_list[] = array(
                    'id'     => $add,
                    'role'   => $rl,
                    'status' => 'added',
                );
            }
        }

        if (empty($customers_list)) {
            return true;
        }

        $this->registry['db']->StartTrans();
        $result = $this->updateRelatedCustomers($customers_list);
        if ($result) {
            $this->registry['messages']->setMessage($this->i18n('message_related_customers_update_successfully'));
        } else {
            $this->registry['messages']->setError($this->i18n('error_related_customers_update_failed'));
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();
        $this->registry['messages']->insertInSession($this->registry);

        $this->registry->set('get_old_vars', $get_old_vars, true);
        return true;
    }

    /*
     *
     */
    private function updateRelatedCustomers($customers_list): bool
    {
        $result = true;

        // group the data by customer
        $customers_data = array();
        foreach ($customers_list as $custm_lst) {
            if (!isset($customers_data[$custm_lst['id']])) {
                $customers_data[$custm_lst['id']] = array();
            }
            $customers_data[$custm_lst['id']][] = array(
                'role'   => $this->settings['customer_role_' . $custm_lst['role']],
                'status' => $custm_lst['status']
            );
        }

        // get the current model of the device
        $filters = array('where' => array('c.id IN (' . implode(',', array_keys($customers_data)) . ')'));
        $customers = Customers::search($this->registry, $filters);

        // list of vars included in the current group
        $grouping_vars_included = array();

        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('edit_all', true, true);
        foreach ($customers as $cstm) {
            $cstm->getVars();
            $old_cstm = clone $cstm;

            $assoc_vars = $cstm->getAssocVars();
            if (empty($grouping_vars_included)) {
                // get the grouping index
                $grp_index = $assoc_vars[$this->settings['customer_object_id_var']]['grouping'];
                foreach ($assoc_vars as $asc_var) {
                    if ($asc_var['grouping'] == $grp_index && $asc_var['type'] != 'group') {
                        $grouping_vars_included[] = $asc_var['name'];
                    }
                }
            }

            $model_changed = false;
            foreach ($customers_data[$cstm->get('id')] as $cstm_nom_status) {
                if ($cstm_nom_status['status'] == 'added') {
                    // define the last active row
                    $active_row = 1;
                    if (!empty($assoc_vars[$this->settings['customer_object_id_var']]['value'])) {
                        $rows_list = array_reverse(array_keys($assoc_vars[$this->settings['customer_object_id_var']]['value']));
                        $active_row = array_shift($rows_list);
                        $active_row++;
                    }
                    $new_row_content = array(
                        $this->settings['customer_object_id_var'] => $this->current_document_model->get('id'),
                        $this->settings['customer_object_name_var'] => $this->current_document_model->get('name'),
                        $this->settings['customer_object_role_var'] => $cstm_nom_status['role'],
                        $this->settings['customer_object_date_var'] => date('Y-m-d H:i:s'),
                    );
                    foreach ($grouping_vars_included as $grp_var) {
                        if (!isset($assoc_vars[$grp_var])) {
                            continue;
                        }
                        $assoc_vars[$grp_var]['value'][$active_row] = isset($new_row_content[$grp_var]) ? $new_row_content[$grp_var] : '';
                        $model_changed = true;
                    }
                } elseif ($cstm_nom_status['status'] == 'removed') {
                    // find the row where the nomenclature is found
                    if (!is_array($assoc_vars[$this->settings['customer_object_id_var']]['value'])) {
                        continue;
                    }
                    $active_row = '';

                    foreach ($assoc_vars[$this->settings['customer_object_id_var']]['value'] as $k_row => $custm_obj) {
                        if ($custm_obj == $this->current_document_model->get('id') &&
                            isset($assoc_vars[$this->settings['customer_object_role_var']]['value'][$k_row]) &&
                            $assoc_vars[$this->settings['customer_object_role_var']]['value'][$k_row] == $cstm_nom_status['role']) {
                            $active_row = $k_row;
                            break;
                        }
                    }
                    if ($active_row === '') {
                        continue;
                    }
                    foreach ($grouping_vars_included as $grp_var) {
                        if (isset($assoc_vars[$grp_var]['value'][$active_row])) {
                            unset($assoc_vars[$grp_var]['value'][$active_row]);
                            $model_changed = true;
                        }
                    }
                }
            }

            if (!$model_changed) {
                continue;
            }

            // edit the customer
            $cstm->set('vars', array_values($assoc_vars), true);
            $cstm->unsanitize();
            if ($cstm->save()) {
                $filters = array(
                    'where'      => array('c.id = ' . $cstm->get('id')),
                    'model_lang' => $cstm->get('model_lang')
                );
                $new_cstm = Customers::searchOne($this->registry, $filters);
                $new_cstm->getVars();

                $params_history = array(
                    'action_type' => 'edit',
                    'old_model'   => $old_cstm,
                    'model'       => $new_cstm,
                    'new_model'   => $new_cstm
                );
                Customers_History::saveData($this->registry, $params_history);
            } else {
                $result = false;
                break;
            }
        }
        $this->registry->set('edit_all', $edit_all, true);

        return $result;
    }
}
