<?php

class ProxyResponse
{
    private string $rawData;
    private array $decodedData;
    private array $records;
    private int $found;
    private string $query;
    /**
     * @var mixed
     */
    private string $error;

    public function __construct(string $rawData)
    {
        $this->rawData = $rawData;
        $this->decodedData = json_decode($rawData, true)[0];
        $this->records = $this->decodedData['records'] ?? [];
        $this->found = $this->decodedData['found'] ?? 0;
        $this->query = $this->decodedData['query'];
        $this->error = $this->decodedData['error'] ?? '';
    }


    public function getRawData(): string {
        return $this->rawData;
    }

    public function getDecodedData(): string {
        return $this->decodedData;
    }

    public function getRecords(): array {
        return $this->records;
    }

    public function getFound(): array {
        return $this->found;
    }

    public function getQuery(): string {
        return $this->query;
    }

    public function getError(): array {
        return $this->error;
    }

    public function hasError(): bool {
        return !empty($this->error);
    }
}
