<?php

/**
 * Object for the parameters of a shape
 * <AUTHOR>
 */
class ShapeParam
{
    private string $id;
    private int $x;
    private int $y;

    /**
     * @param string $id The id of the parameter
     * @param int $x The X coordinate of the parameter
     * @param int $y The Y coordinate of the parameter
     */
    public function __construct(string $id, int $x, int $y)
    {
        $this->id = $id;
        $this->x = $x;
        $this->y = $y;
    }


    /**
     * @return string
     */
    public function getId() :string
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getX() :int
    {
        return $this->x;
    }

    /**
     * @return int
     */
    public function getY() :int
    {
        return $this->y;
    }

}
