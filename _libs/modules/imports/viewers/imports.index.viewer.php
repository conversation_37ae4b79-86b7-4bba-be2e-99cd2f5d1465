<?php
Class Imports_Index_Viewer extends Viewer {
    public $template = 'index.html';

    public function prepare() {
        $current_import_details = '';
        //check for a generated import and sets its name
        if (isset ($this->registry['import_type']['name'])) {
            $current_import_type = $this->registry['import_type']['name'];
        } else {
            $current_import_type = '';
        }

        $imports_types = Imports::search($this->registry);
        $this->data['imports_types'] = $imports_types;

        $lang_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'imports/plugins/',
            'colliers_csv_cameras',
            '/i18n/',
            $this->registry['lang'],
            '/imports.ini');

        if (file_exists($lang_file)) {
            $this->loadCustomI18NFiles($lang_file);
        }

        if ($this->registry['import_failed']) {
            //the import failed, load the import filters again
            $this->data['import_failed'] = $this->registry['import_failed'];
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('imports');
        $this->data['title'] = $title;
    }
}

?>
