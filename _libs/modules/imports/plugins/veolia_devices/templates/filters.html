<table cellpadding="2" cellspacing="2" border="0">
    <tr class="nz-import-common">
        <td class="labelbox"><a name="error_subimport_type"><label for="subimport_type" {if $messages->getErrors('subimport_type')}
            class="error"{/if}>{help label='subimport_type'}</label></a></td>
        <td class="required">{#required#}</td>
        <td nowrap="nowrap">
            {include file=`$theme->templatesDir`input_dropdown.html
            name='subimport_type'
            value=$import_filters.subimport_type
            options=$subimport_type_options
            options_align='horizontal'
            required=true
            standalone=true}
        </td>
    </tr>
    <tr>
        <td class="labelbox"><a name="error_date"><label for="date"{if $messages->getErrors('date')} class="error"{/if}>{help label='date'}</label></a></td>
        <td class="required"><span class="required">*</span></td>
        <td nowrap="nowrap">
            {include file=`$theme->templatesDir`input_date.html
            name='date'
            required=true
            width=200
            value=$import_filters.date
            required=true
            disallow_date_after=$smarty.now|date_format:"Y-%m-%d"
            standalone=true}
        </td>
    </tr>
    <tr  class="nz-import-common">
        <td class="labelbox"><a name="error_report_period"><label for="report_period" {if $messages->getErrors('report_period')}
            class="error"{/if}>{help label='report_period'}</label></a></td>
        <td class="required">{#required#}</td>
        <td nowrap="nowrap">
            {include file=`$theme->templatesDir`input_autocompleter.html
            name='report_period'
            autocomplete_var_type='basic'
            autocomplete=$report_period
            value=$import_filters.report_period
            value_autocomplete=$import_filters.report_period_autocomplete
            required=true
            width=200
            standalone=true}
        </td>
    </tr>
    <tr  class="nz-import-individual" {if empty($import_filters.manufacturer) }style="display: none"{/if}>
        <td class="labelbox"><a name="error_manufacturer"><label for="manufacturer" {if $messages->getErrors('report_period')} class="error"{/if}>{help label='manufacturer'}</label></a></td>
        <td class="required">{#required#}</td>
        <td class="nz-ac-wrapper" nowrap="nowrap" data-siemens={$siemens_id} >
            {include file=`$theme->templatesDir`input_autocompleter.html
            name='manufacturer'
            autocomplete_var_type='basic'
            autocomplete=$manufacturer
            value=$import_filters.manufacturer
            value_autocomplete=$import_filters.manufacturer_autocomplete
            width=200
            standalone=true}
        </td>
    </tr>
    <tr class="nz-import-siemens" {if $import_filters.manufacturer !== $siemens_id }style="display: none"{/if}>
        <td class="labelbox"><a name="error_siemens_type"><label for="siemens_type" {if $messages->getErrors('siemens_type')}
            class="error"{/if}>{help label='siemens_type'}</label></a></td>
        <td class="required">{#required#}</td>
        <td nowrap="nowrap">
            {include file=`$theme->templatesDir`input_dropdown.html
            name='siemens_type'
            value=$import_filters.siemens_type
            options=$siemens_type_options
            options_align='horizontal'
            standalone=true}
        </td>
    </tr>
    <tr  class="nz-import-file-upload">
        <td class="labelbox"><a name="error_upload_file"><label for="upload_file"{if $messages->getErrors('upload_file')} class="error"{/if}>{help label='upload_file'}</label></a></td>
        <td class="required">{#required#}</td>
        <td nowrap="nowrap">
            <input type="file" class="filebox" name="upload_file" id="upload_file_1" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#import#|escape}" />
        </td>
    </tr>
</table>