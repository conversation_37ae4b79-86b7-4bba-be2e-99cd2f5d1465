<?php

return new DeviceMeasurementImportSettings(
    'C',
    'E',
    '2',
    [
        'Номер ТУ',
        'дата',
        'енергия',
    ],
    'Ymd',
    'D',
    [
        'device_id' => 'id',
        'device' => 'name',
        'device_psiro_code' => 'code',
        'indication_old' => 'indication_new',
        'indication_new' => 'E',
        'indication_alert' => '',
        'indication_deviation' => '',
        'device_serial_num' => 'C',
    ],
    1,
    false,
    function (&$registry, $rowData) {
        if (empty($rowData['C'])) {
            return false;
        }

        $filters = [
            'skip_permissions_check' => true,
            'where' => [
                'n.type = 20',
                "ni18n.name = '{$rowData['C']}'"
            ]
        ];
        $search = Nomenclatures::search($registry, $filters);
        if (sizeof($search) > 1) {
            throw new TooManyDevicesFoundException("There is more than one device for number: " . $rowData['C']);
        }

        if (sizeof($search) == 1) {
            return $search[0];
        }

        return false;
    }
);
