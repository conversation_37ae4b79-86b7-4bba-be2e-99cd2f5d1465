<?php

/**
 * Custom Import class
 */
Class Custom_Import extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Import';

    /**
     * import reasons and invoices
     */
    public static function import(&$registry, $params = array()) {
        $file = $_FILES['upload_file'];

        if (!$file || $file['error']) {
            //error no file
            $registry['messages']->setError($registry['translater']->translate('error_no_file'));

            return false;
        }

        //log array
        $log_arr = array();
        //input file encoding
        //$convert_from = 'Windows-1251';
        //convert to new file encoding
        //$convert_to = 'UTF-8';
        //convert to temp utf8 file
        if (empty($convert_from) && empty($convert_to)) {
            $cnt = General::fileGetContents($file['tmp_name']);
            $cnt = @iconv($convert_from, $convert_to, $cnt);
            $temp = tmpfile();
            fwrite($temp, $cnt);
            fseek($temp, 0);
            $handle = $temp;
        } else {
            $handle = fopen($file['tmp_name'], "r");
        }

        $row=0;
        $separator = "\t";
        //read csv data
        while (($data[$row++] = fgetcsv($handle, 0, $separator)) !== FALSE) {
        }
        //unset last row
        unset($data[count($data)-1]);
        //unset($data[count($data)-1]);
        unset($data[0]);
        unset($data[1]);
        if ($temp) {
            fclose($temp);
        }
        $art_ids = array();
        /*foreach ($data as $val) {
            if ($val[0] == 'R' && !in_array($val[3],$art_ids)) {
                $art_ids[] = $val[3];
            }
        }sort($art_ids);trace($art_ids);*/
//trace($data);exit;
        //field's positions
        $csv_fields = array('row_type' => 0, 'name' => 3, 'num' => 6, 'added' => 5, 'date_of_payment' => 5, 'total_with_vat' => 8, 'total_vat' => 9, 'total' => 10, 'eik' => 11, 'customer_name' => 12, 'invoice_num' => 16, 'notice_description' => 18);
        //gt2 field's positions
        $gt2_fields = array('quantity' => 7, 'price' => 8, 'subtotal' => 9, 'article_measure_name' => 6, 'article_description' => 5, 'article' => 4);
        //D row count fields
        $d_count = 15;
        //R row count fields
        $r_count = 11;
        $mult_const = 1;
        //combine data - add row resurce 'R' to document row 'D'
        foreach ($data as $key => $val) {
            if ($val[$csv_fields['row_type']] == 'D') {
                /*if (count($val) != $d_count) {
                    //error count cols in row
                    $err = sprintf($registry['translater']->translate('error_payment_count_cols'), count($csv_fields), $key, implode("\t", $val));
                    $registry['messages']->setError($err);
                    Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                    return false;
                }*/
                $data[$key][$csv_fields['total_with_vat']] = sprintf('%.2f', $mult_const*$data[$key][$csv_fields['total_with_vat']]);
                $data[$key][$csv_fields['total']] = sprintf('%.2f', $mult_const*$data[$key][$csv_fields['total']]);
                $parent_row = $key;
            } elseif ($val[$csv_fields['row_type']] == 'R') {
                /*if (count($val) != $r_count) {
                    //error count cols in row
                    $err = sprintf($registry['translater']->translate('error_payment_count_cols'), count($csv_fields), $key, implode("\t", $val));
                    $registry['messages']->setError($err);
                    Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                    return false;
                }*/
                if (empty($val[5])) {
                    trace(implode("\t",$val));
                }
                if (isset($data[$parent_row])) {
                    $val[$gt2_fields['price']] = sprintf('%.2f', $mult_const*$val[$gt2_fields['price']]);
                    $val[$gt2_fields['subtotal']] = sprintf('%.2f', $mult_const*$val[$gt2_fields['subtotal']]);
                    $data[$parent_row]['R'][] = $val;
                    unset($data[$key]);
                }
            } else {
                //error row type field - must be D or R
                $err = sprintf($registry['translater']->translate('error_invoice_col_row_type'), $val[$csv_fields['row_type']], $key, implode("\t", $val));
                $registry['messages']->setError($err);
                Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                return false;
            }
        }
        //trace($data);
        //trace($data[$parent_row],$parent_row);
//trace('end1');exit;
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.annulments.factory.php';

        $old_reason = new Finance_Incomes_Reason($registry);
        //constants
        $const['customer_type'] = 3;
        $const['company'] = 2;
        $const['currency'] = 'BGN';
        $const['reason_type'] = 101;
        $const['smetka_type'] = 102;
        $const['total_vat_rate'] = 20.00;
        $const['office'] = 2;
        $const['payment_type'] = 'bank';
        $const['container_id'] = 1;
        $const['nomenclatures'] = array('703-1' => 1036, '703-2' => 1034, '703-3' => 1035, '703-5' => 1050, '498-20' => 1045);
        $query = 'SELECT n.id, ni18n.name' . "\n" .
                 'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                 '  ON n.id=ni18n.parent_id AND ni18n.lang="' . $registry['lang'] . '"' . "\n" .
                 'WHERE n.code="001NVR"';
        $nom_no_vat_reason = $db->GetRow($query);
        $const['total_no_vat_reason'] = $nom_no_vat_reason ? $nom_no_vat_reason['id'] : '';
        $const['total_no_vat_reason_text'] = $nom_no_vat_reason ? $nom_no_vat_reason['name'] : '';

        set_time_limit (0);
        $jjj = 0;
        foreach ($data as $key => $val) {
            //if ($key < 10527) continue;
            //if ('2010-01-01' <= date('Y-m-d', strtotime($val[$csv_fields['added']]))) continue;
            if ((++$jjj)%100 == 0) {
                //trace('mem='.memory_get_usage(),$jjj);
                flush();
                ob_flush();
            }
            $is_annulment = 0;
            if ($val[2] == iconv('Windows-1251', 'UTF-8', 'ПРОТОКОЛ')) {
                $is_annulment = PH_FINANCE_TYPE_ANNULMENT;
            }
            $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' WHERE num REGEXP "^0*' . (int)($val[$csv_fields['num']]) . '$"' . ' AND type=' . PH_FINANCE_TYPE_INVOICE;
            $exist_invoice = $db->GetRow($query);
            if ($exist_invoice && empty($val[$csv_fields['invoice_num']])) {
                trace($query,implode("\t", array_slice($val, 0, -1)),'inv');
                continue;
                //exist invoice with this num
                $err = sprintf($registry['translater']->translate('error_invoice_exist_num'), $val[$csv_fields['num']], $key, implode("\t", array_slice($val, 0, -1)));
                $registry['messages']->setError($err);
                Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                return false;
            }
            //if credit or debit notice check existing invoice
            if ($val[$csv_fields['invoice_num']] && $val[$csv_fields['total_with_vat']] != '0.00') {
                $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' WHERE num REGEXP "^0*' . (int)($val[$csv_fields['invoice_num']]) . '$" AND type=' . PH_FINANCE_TYPE_INVOICE;
                $parent_invoice_data = $db->GetRow($query);
                if (empty($parent_invoice_data)) {
                    //exist invoice with this num
                    trace($query,implode("\t", array_slice($val, 0, -1)),'cdn');
                    continue;
                    $err = sprintf($registry['translater']->translate('error_invoice_not_exist_num'), $val[$csv_fields['invoice_num']], $key, implode("\t", array_slice($val, 0, -1)));
                    $registry['messages']->setError($err);
                    Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                        'file' => $file,
                                                        'success' => 0,
                                                        'log' => array('error' => $err)
                                        ));

                    //return false;
                }
            }
            $eik = $val[$csv_fields['eik']];
            if ($eik) {
                //search customer
                $customer = Customers::searchOne($registry, array('where' => array('c.eik REGEXP "^0*' . (int)($eik) . '$"', 'c.deleted >= 0')));
            } else {
                //error eik field
                /*$err = sprintf($registry['translater']->translate('error_invoice_no_eik'), $key, implode("\t", array_slice($val, 0, -1)));
                $registry['messages']->setError($err);
                Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                return false;*/
            }
            if (!$customer) {//trace($val[$csv_fields['customer_name']],$eik);
                trace(implode("\t", array_slice($val, 0, -1)),'cstm');
                continue;
                //get customer info by eik
                $info = array();
                if ($eik) {
                    $info = Finance_Companies::getCompanyInfoByVAT($eik);
                }

                $customer = new Customer($registry);
                $customer->set('id', '', true);
                $customer->set('type', $const['customer_type'], true);
                $customer->set('subtype', 'normal', true);
                $customer->set('is_company', 1, true);
                if ($info) {
                    $customer->set('eik', $info['eik'], true);
                    $customer->set('in_dds', $info['vat'], true);
                    if (!empty($info['name'])) {
                        $customer->set('name', $info['name'], true);
                    } else {
                        $customer->set('name', $val[$csv_fields['customer_name']], true);
                    }
                    $customer->set('address', @$info['address'], true);
                } else {
                    if ($eik) {
                        $customer->set('eik', $eik, true);
                    }
                    $customer->set('name', $val[$csv_fields['customer_name']], true);
                }
                //code_client_Workflow
                //code_deliverer_Workflow
                //add customer
                if ($customer->save()) {
                    $log_arr['customers'][] = $customer->get('id');
                } else {//trace($customer->getAll(),'c');trace($info,'i');
                    //error adding customer
                    $err = sprintf($registry['translater']->translate('error_add_customer'), $key, implode("\t", array_slice($val, 0, -1)));
                    $registry['messages']->setError($err);
                    Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                    return false;
                }
            }

            $properties = array();
            $properties['company'] = $const['company'];
            $properties['office'] = $const['office'];
            $properties['payment_type'] = $const['payment_type'];
            $properties['container_id'] = $const['container_id'];
            $properties['status'] = 'finished';
            $properties['currency'] = $const['currency'];
            //$properties['name'] = $val[$csv_fields['name']];
            $properties['import_num'] = $val[$csv_fields['num']];
            $properties['import_added'] = date('Y-m-d H:i:s', strtotime($val[$csv_fields['added']]));
            $properties['import_issue_date'] = date('Y-m-d H:i:s', strtotime($val[$csv_fields['added']]));
            $properties['fiscal_event_date'] = date('Y-m-d H:i:s', strtotime($val[$csv_fields['added']]));
            $properties['date_of_payment_count'] = '';
            $properties['date_of_payment'] = General::strftime('%Y-%m-%d', strtotime('+5 day', strtotime($properties['import_issue_date'])));
            $properties['import_date_of_payment'] = General::strftime('%Y-%m-%d', strtotime('+5 day', strtotime($properties['import_issue_date'])));
            $properties['import_total_with_vat'] = $val[$csv_fields['total_with_vat']];
            $properties['import_total_vat'] = $val[$csv_fields['total_vat']];
            $properties['total'] = $val[$csv_fields['total']];
            if ($val[$csv_fields['total_with_vat']] != 0 && $val[$csv_fields['total']] == $val[$csv_fields['total_with_vat']]) {
                //without vat
                $properties['total_vat_rate'] = 0.00;
                $properties['total_no_vat_reason'] = $const['total_no_vat_reason'];
                $properties['total_no_vat_reason_text'] = $const['total_no_vat_reason_text'];
            } else {
                $properties['total_vat_rate'] = $const['total_vat_rate'];
            }
            $properties['customer'] = $customer->get('id');
            //$properties['import_customer_name'] = $val[$csv_fields['customer_name']];
            if ($val[$csv_fields['invoice_num']] && $val[$csv_fields['total_with_vat']] != '0.00') {
                if ($is_annulment) {
                    //annulment
                    $properties['type'] = $is_annulment;
                } elseif ($val[$csv_fields['total']] > 0) {
                    //debit notice
                    $properties['type'] = PH_FINANCE_TYPE_DEBIT_NOTICE;
                    $properties['name'] =  'Debit notice from AjurL ' . $customer->get('name');
                } else {
                    //credit notice
                    $properties['type'] = PH_FINANCE_TYPE_CREDIT_NOTICE;
                    $properties['name'] =  'Credit notice from AjurL ' . $customer->get('name');
                }
                $properties['num'] = $val[$csv_fields['num']];
                $properties['description'] = $val[$csv_fields['notice_description']];
                $properties['link_to'] = $parent_invoice_data['id'];
                if ($is_annulment) {
                    $properties['link_to_model_name'] = 'Finance_Incomes_Reason';
                }
            } elseif ($val[2] == iconv('Windows-1251', 'UTF-8', 'СМЕТКА')) {
                $properties['type'] = $const['smetka_type'];
                $properties['num'] = $val[$csv_fields['num']];
            } else {
                $properties['type'] = $const['reason_type'];
                $properties['name'] =  'Invoice from AjurL ' . $customer->get('name');
            }
            if ($properties['import_total_with_vat'] == 0) {
                $properties['payment_status'] = 'paid';
            }

            if ($is_annulment) {
                $reason = new Finance_Annulment($registry, $properties);
            } else {
                //create incomes reason
                $reason = new Finance_Incomes_Reason($registry, $properties);
            }
            $reason->set('id', '', true);
            $grouping_table_2 = $reason->getGT2Vars();
            $grouping_table_2['values'] = array();
            if (isset($val['R']) && count($val['R']) /*&& $val[$csv_fields['total_with_vat']] != 0*/) {
                foreach ($val['R'] as $gt2_row) {
                    //prepare row to gt2 table
                    $row_vals = array_keys($grouping_table_2['vars']);
                    $row_vals = array_combine($row_vals, array_fill(0, count($row_vals), ''));
                    if (is_numeric($gt2_row[$gt2_fields['quantity']]) && $gt2_row[$gt2_fields['quantity']] != 0) {
                        $row_vals['quantity'] = $gt2_row[$gt2_fields['quantity']];
                    } else {
                        $row_vals['quantity'] = 1;
                    }
                    $row_vals['price'] = $gt2_row[$gt2_fields['price']];
                    $row_vals['subtotal'] = $gt2_row[$gt2_fields['subtotal']];
                    $row_vals['subtotal_with_discount'] = $gt2_row[$gt2_fields['subtotal']];
                    $row_vals['subtotal_profit_no_final_discount'] = $gt2_row[$gt2_fields['subtotal']];
                    $row_vals['subtotal_with_vat'] = ($properties['total_vat_rate'] + 100)*$gt2_row[$gt2_fields['subtotal']]/100;
                    $row_vals['subtotal_with_vat_with_discount'] = ($properties['total_vat_rate'] + 100)*$gt2_row[$gt2_fields['subtotal']]/100;
                    if ($properties['type'] > PH_FINANCE_TYPE_MAX && $row_vals['quantity'] < 0 && $row_vals['price'] > 0) {
                        $row_vals['price'] *= -1;
                        $row_vals['quantity'] *= -1;
                    } elseif ($is_annulment) {
                        $row_vals['price'] *= -1;
                        $row_vals['quantity'] *= -1;
                    }


                    /*if ($properties['type'] == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                        $row_vals['subtotal'] *= -1;
                        $row_vals['quantity'] *= -1;
                    }*/

                    require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                    //ToDo - search nomenclature
                    /*$words = explode(' ', mb_strtolower($gt2_row[$gt2_fields['article_description']], 'UTF-8'));
                    $nom_id = 0;
                    foreach ($nom_arr as $nom) {
                        if (array_intersect($words, $nom['words'])) {
                            $nom_id = $nom['id'];
                            break;
                        }
                    }*/
                    //$nom_id = $gt2_row[3];
                    $nom_id = $const['nomenclatures'][$gt2_row[$gt2_fields['article']]];
                    if (!$nom_id) {
                        //error no nomenclature
                        $err = sprintf($registry['translater']->translate('error_no_nomenclature'), $key, implode("\t", array_slice($val, 0, -1)));
                        $registry['messages']->setError($err);
                        Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                        return false;
                    }
                    $nomenclature = Nomenclatures::searchOne($registry, array('where' => array('n.id=' . $nom_id)));
                    if ($nomenclature) {
                         foreach ($grouping_table_2['vars'] as $key2 => $var) {
                            if ($nomenclature->isDefined(str_replace('article_', '', $key2))) {
                                $row_vals[$key2] = $nomenclature->get(str_replace('article_', '', $key2));
                            }
                        }
                    } else {
                        //error no nomenclature
                        $err = sprintf($registry['translater']->translate('error_no_nomenclature'), $key, implode("\t", array_slice($val, 0, -1)));
                        $registry['messages']->setError($err);
                        Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                        return false;
                    }
                    $row_vals['article_measure_name'] = $gt2_row[$gt2_fields['article_measure_name']];
                    $row_vals['article_description'] = $gt2_row[$gt2_fields['article_description']];
                    $grouping_table_2['values'][] = $row_vals;
                }
                $grouping_table_2['rows'] = array();
                $reason->set('grouping_table_2', $grouping_table_2, true);
                $reason->set('table_values_are_set', true, true);
            }
//trace($val[$csv_fields['invoice_num']],$val[$csv_fields['num']]);
            //trace($reason->properties);
            //save incomes reason or credit/debit notice
            if ($reason->save()) {//trace($reason->get('type'),$reason->get('id'));
                if ($is_annulment) {
                    $set = array();
                    $set['annulled'] = sprintf("annulled='%s'", $properties['import_added']);
                    $set['annulled_by'] = sprintf("annulled_by=%d", $registry['currentUser']->get('id'));
                    $query1 = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                              'SET ' . implode(', ', $set) . "\n" .
                              'WHERE id=' . $parent_invoice_data['id'];

                    $db->Execute($query1);
                    continue;
                }
                if ($reason->get('type') == PH_FINANCE_TYPE_DEBIT_NOTICE) {
                    $log_arr['debit_notice'][] = $reason->get('id');
                } elseif ($reason->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                    $log_arr['credit_notice'][] = $reason->get('id');
                } else {
                    $log_arr['reasons'][] = $reason->get('id');
                }
                //save history and audit
                $audit_parent = Finance_Incomes_Reasons_History::saveData($registry,
                                                                        array('action_type' => 'add',
                                                                              'new_model' => $reason,
                                                                              'old_model' => $old_reason
                                                                        ));
                if ($reason->get('type') > PH_FINANCE_TYPE_MAX && $reason->get('type') != $const['smetka_type']) {
                    //prepare invoice
                    $grouping_table_2 = $reason->get('grouping_table_2');
                    $reason->set('type', PH_FINANCE_TYPE_INVOICE, true);
                    $reason->set('link_to', $reason->get('id'), true);
                    $reason->set('id', '', true);
                    $invoice_table_2 = $reason->getGT2Vars();
                    $invoice_table_2['values'] = $grouping_table_2['values'];
                    $invoice_table_2['rows'] = array();
                    $reason->set('grouping_table_2', $invoice_table_2, true);
                    $reason->set('total', $val[$csv_fields['total']], true);
                    //save invoice
                    if ($reason->save()) {
                        $log_arr['invoices'][] = $reason->get('id');
                        //save history and audit
                        $audit_parent = Finance_Incomes_Reasons_History::saveData($registry,
                                                                                array('action_type' => 'add',
                                                                                      'new_model' => $reason,
                                                                                      'old_model' => $old_reason
                                                                                ));
                    } else {
                        //error adding invoice
                        $err = sprintf($registry['translater']->translate('error_add_invoice'), $key, implode("\t", array_slice($val, 0, -1)));
                        $registry['messages']->setError($err);
                        Imports::importLog($registry, array('import_type' => $params['import_type'],
                                                    'file' => $file,
                                                    'success' => 0,
                                                    'log' => array('error' => $err)
                                    ));

                        return false;
                    }
                }

                /*if ($reason->get('type') == PH_FINANCE_TYPE_DEBIT_NOTICE || $reason->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                    //add correct document
                    $filters = array('where' => array ('fir.id = ' . $parent_invoice_data['id']));
                    $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($registry, $filters);
                    $registry->set('get_old_vars', true, true);
                    $finance_incomes_reason->getGT2Vars();
                    if ($reason->get('type') == PH_FINANCE_TYPE_DEBIT_NOTICE) {
                        $credit_debit = 'debit';
                    } else {
                        $credit_debit = 'credit';
                    }
                    $registry['request']->set($credit_debit, $reason->get('id'), 'all', true);
                    $registry['request']->set('create_correct', $credit_debit, 'all', true);
                    $finance_incomes_reason->correctFromInvoice();
                    $registry['request']->set($credit_debit, '', 'all', true);
                    $registry['request']->set('create_correct', '', 'all', true);
                }*/
            } else {
                //error adding reason
                $err = sprintf($registry['translater']->translate('error_add_reason'), $key, implode("\t", array_slice($val, 0, -1)));
                $registry['messages']->setError($err);
                Imports::importLog($registry, array('import_type' => $params['import_type'],
                                            'file' => $file,
                                            'success' => 0,
                                            'log' => array('error' => $err)
                            ));

                return false;
            }
            unset($data[$key]);//exit;
        }
//exit;
        /*if ($reason && $reason->get('num')) {
            //get last import number
            $current_number = sprintf('%d', $reason->get('num'));

            //update invoice counter
            $query = 'UPDATE ' . DB_TABLE_FINANCE_COUNTERS . "\n" .
                    'SET next_number=' . ($current_number + 1) . "\n" .
                    'WHERE id=' . $reason->counter->get('id');

            $result = $db->Execute($query);
        }*/
//return false;exit;
        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result) {
            Imports::importLog($registry, array('import_type' => $params['import_type'],
                                        'file' => $file,
                                        'success' => 1,
                                        'log' => $log_arr
                        ));
            //import success
            $registry['messages']->setMessage($registry['translater']->translate('message_import_success'));
        } else {
            //error import
            $err = $registry['translater']->translate('error_import');
            $registry['messages']->setError($err);
            Imports::importLog($registry, array('import_type' => $params['import_type'],
                                        'file' => $file,
                                        'success' => 0,
                                        'log' => array('error' => $err)
                        ));
        }

        return $result;
    }

}
?>