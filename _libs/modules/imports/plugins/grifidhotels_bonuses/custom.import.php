<?php

/**
 * Custom import of bonuses and deductions for Grifid Hotels
 */
class Custom_Import extends Model_Factory
{
    /**
     * Set allowed file extensions
     */
    public static $allowed_file_extensions = array('xls', 'xlsx');
    public static $params;
    public static $months = [
        'януари' => '01',
        'февруари' => '02',
        'март' => '03',
        'април' => '04',
        'май' => '05',
        'юни' => '06',
        'юли' => '07',
        'август' => '08',
        'септември' => '09',
        'октомври' => '10',
        'ноември' => '11',
        'декември' => '12',
    ];
    /**
     * The registry
     */
    private static Registry $_registry;
    private static array $rowsLog = [];
    private static $rowsLogText;

    public static function import($registry, $params)
    {
        self::$_registry = &$registry;
        self::$params = $params;
        $request = &$registry['request'];
        $db = &$registry['db'];
        set_time_limit(0);
        //ini_set('memory_limit', $params['memory_limit']);

        // Include necessary files for documents (used in this method and in the validate() method too)
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

        // Set a flag for the result
        $result = true;
        $valid = self::validate();
        if (!$valid) {
            $registry['messages']->setError($registry['translater']->translate('error_no_imported_data'), '', -1);
            return false;
        }
        $file = &$_FILES['upload_file'];
        $sr = new Spreadsheet_Manager($file['tmp_name'], array('take_only_data' => true));
        if (!$sr->selectWorksheet(0)) {
            self::error('error_select_worksheet');
            return false;
        }
        $descriptiveData = $sr->readActiveCellsBetweenColumnsByRows('C', 'C', '4', '7');
        $hotel = self::findHotelByName($descriptiveData['C']['4']);
        if (!$hotel) {
            $registry['messages']->setError($registry['translater']->translate('error_no_imported_data'), '', -1);
            $registry['messages']->setError($registry['translater']->translate('error_no_hotel_found'), '', -1);
            return false;
        }

        $unit = self::findUnitForHotel($descriptiveData['C']['5'], $hotel->get('id'));
        if (!$unit) {
            $registry['messages']->setError($registry['translater']->translate('error_no_imported_data'), '', -1);
            $registry['messages']->setError($registry['translater']->translate('error_no_unit_found'), '', -1);
            return false;
        }

        $date = self::getCorrectDate($descriptiveData['C']['6'], $descriptiveData['C']['7']);
        if (!$date) {
            $registry['messages']->setError($registry['translater']->translate('error_no_imported_data'), '', -1);
            $registry['messages']->setError($registry['translater']->translate('error_incorrect_date'), '', -1);
            return false;
        }


        $rawData = $sr->readActiveCellsBetweenColumnsByRows('A', 'F', '11', '', 'row');
        $allCustomerIDs = self::getAllCustomerIDs(array_column($rawData, 'A'));
        $allPositionIDs = self::getAllPositionIDs(array_column($rawData, 'B'), $unit->get('id'));
        $addedBonuses = 0;
        $addeddeductions = 0;
        foreach ($rawData as $row) {
            if (!array_key_exists($row['A'], $allCustomerIDs)) {
                self::logRow('error_no_customer_found', $row['A']);
                continue;
            }

            if (!array_key_exists($row['B'], $allPositionIDs)) {
                self::logRow('error_no_position_found', $row['A']);
                continue;
            }

            if (!empty($row['C']) && is_numeric($row['C'])) {
                $resultBon = self::addBonusForRow($row, $allCustomerIDs[$row['A']], $allPositionIDs[$row['B']], $date, $unit);
                if ($resultBon) {
                    $addedBonuses += 1;
                }
            } else {
                self::logRow('error_incorrect_or_missing_bonus', $row['A']);
            }

            if (!empty($row['E']) && is_numeric($row['E'])) {
                $resultSubt = self::addDeductionForRow($row, $allCustomerIDs[$row['A']], $allPositionIDs[$row['B']], $date, $unit);
                if ($resultSubt) {
                    $addeddeductions += 1;
                }
            } else {
                self::logRow('error_incorrect_or_missing_deduction', $row['A']);
            }
        }
        $result = true;
        $registry['messages']->setMessage(sprintf($registry['translater']->translate('grifidhotels_bonuses_successful_import'), $addedBonuses, $addeddeductions));
        foreach (self::getRowsLog() as $typeKey => $type) {
            $rows = implode(', ', $type['data']);
            $msg = "{$type['name']}: {$rows}";
            $registry['messages']->setError($msg);
        }
        $log = [
            "hotel" => "Хотел: " . $hotel->get('name') . "\n",
            "unit" => "Звено: " . $unit->get('name') . "\n",
            "date" => "Дата: " . $date . "\n",
        ];
        $logdata = array(
            'import_type' => 'grifidhotels_bonuses',
            'file' => $_FILES['upload_file'],
            'success' => intval($result),
            'log' => $log + self::getRowsLogForImportLog(),
        );
        Imports::importLog($registry, $logdata);


        //return false to reload filters
        return false;

    }

    public static function validate()
    {
        // Get the registry
        $registry = &self::$_registry;

        // Get the messages object
        $messages = &$registry['messages'];
        $file = &$_FILES['upload_file'];
        if (empty($file) || $file['error'] == 4) {
            self::error('error_no_file_for_import');
        } elseif (!empty($file['error'])) {
            self::error('error_file_upload');
        } elseif (!in_array(pathinfo($file['name'], PATHINFO_EXTENSION), self::$allowed_file_extensions)) {
            self::error('error_invalid_file', array(pathinfo($file['name'], PATHINFO_EXTENSION), implode(', ', self::$allowed_file_extensions)));
        }

        if ($messages->getErrors()) {
            // Validation fails
            return false;
        }

        return true;
    }

    private static function getRowsLogForImportLog(): array
    {
        if (self::$rowsLogText) {
            return self::$rowsLogText;
        }
        $rowsLogText = [];
        $rowsLog = self::getRowsLog();
        foreach ($rowsLog as $id => $type) {
            $rowsLogText[$type['name']] = [];
            $rowsLogText[$type['name']] = " " . implode(",", $type['data']) . "\n";
        }
        self::$rowsLogText = $rowsLogText;

        return self::$rowsLogText;
    }

    private static function getRowsLog(): array
    {
        return self::$rowsLog;
    }

    static function logRow($type, $data)
    {
        if (!array_key_exists($type, self::$rowsLog)) {
            self::$rowsLog[$type] = [
                'name' => self::$_registry['translater']->translate($type),
                'msgMethod' => 'setError',
                'data' => [],
            ];
        }
        if (!in_array($data, self::$rowsLog[$type]['data'])) {
            self::$rowsLog[$type]['data'][] = $data;
        }
    }

    /**
     * Simple function to add errors into the Messages object
     *
     * @param string $msg_key - name of label to use for translation
     * @param array|string $msg_params - optional values for replacement in label
     */
    static function error($msg_key, $msg_params = array())
    {
        if (!is_array($msg_params)) {
            $msg_params = array($msg_params);
        }
        self::$_registry['messages']->setError(
            vsprintf(self::$_registry['translater']->translate($msg_key), $msg_params)
        );
    }

    static function findHotelByName($name)
    {
        $filters = [
            'where' => [
                "c.type = " . self::$params['hotelType'],
                "c.is_company = 1",
                "ci18n.name = '$name'",
            ],
        ];
        return Customers::searchOne(self::$_registry, $filters);

    }

    static function findUnitForHotel($unitName, $hotel)
    {
        $filters = [
            'where' => [
                "n.type = " . self::$params['unitType'],
                "a__hotel_name_id = $hotel",
                "ni18n.name = '$unitName'",
            ],
            'skip_permissions_check' => '1'
        ];
        return Nomenclatures::searchOne(self::$_registry, $filters);

    }

    static function getCorrectDate($monthName, $year)
    {

        $month = self::$months[mb_strtolower($monthName)] ?? false;
        if (!$month) {
            return false;
        }
        return "$year-$month-01";

    }

    static function getAllCustomerIDs($customerNames)
    {
        $impNames = implode("','", $customerNames);
        $query = <<<SQL
        SELECT CONCAT(ci18n.`name`, ' ', ci18n.`lastname`), ci18n.parent_id
        FROM customers_i18n ci18n
        WHERE CONCAT(ci18n.`name`, ' ', ci18n.`lastname`) IN ('$impNames')
        SQL;
        return self::$_registry['db']->getAssoc($query);

    }

    static function getAllPositionIDs($positionNames, $unitID)
    {
        $impNames = implode("','", $positionNames);
        $query = <<<SQL
        SELECT ni18n.`name`, n.id
        FROM nom n
        JOIN nom_i18n ni18n ON ni18n.`parent_id` = n.id
        LEFT JOIN _fields_meta fm ON fm.`model` = 'Nomenclature' AND fm.model_type = n.`type` AND fm.`name` = 'position_department_id'
        LEFT JOIN nom_cstm nc ON nc.var_id = fm.id AND nc.model_id = n.id
        WHERE ni18n.`name` IN ('$impNames') AND nc.`value` = '$unitID'
        SQL;
        return self::$_registry['db']->getAssoc($query);

    }

    static function addBonusForRow($row, $customer, $position, $date, $unit)
    {
        $action = 'edit';
        $filters = [
            'where' => [
                'd.type = ' . self::$params['bonusDocType'],
                "d.date = '$date'",
                "d.customer = $customer",
                "a__position_department_id = " . $unit->get('id'),
                "a__position_name_id = " . $position,
            ],
            'sanitize' => false,
            'skip_permissions_check' => '1'
        ];
        $bonusDocument = Documents::searchOne(self::$_registry, $filters);

        if (!$bonusDocument) {
            $action = 'add';
            $filters = array('where' => array('dt.id = \'' . self::$params['bonusDocType'] . '\'',
                'dt.active = 1'),
                'sanitize' => true);
            $document_type = Documents_Types::searchOne(self::$_registry, $filters);

            //start building new document
            $bonusDocument = new Document(self::$_registry, array('type' => self::$params['bonusDocType']));
            $old_document = clone $bonusDocument;
            $bonusDocument->set('date', $date);
            $bonusDocument->set('customer', $customer);
            $bonusDocument->set('name', $document_type->get('default_name'));
            $bonusDocument->set('customer_name', $row['A']);
            $currUser = self::$_registry->get('currentUser');
            $bonusDocument->set('employee', $currUser->get('employee'));
            $bonusDocument->set('employee_name', $currUser->get('employee_name'));
            $bonusDocument->set('department', $currUser->get('default_department'));
            $bonusDocument->set('group', $currUser->get('default_group'));
        } else {
            $old_document = clone $bonusDocument;
        }
        $bonusDocument->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . self::$_registry['lang'] . '/documents.ini');
        $assoc = $bonusDocument->getAssocVars();
        $bonusDocument->set('notes', $row['D']);
        $assoc['position_name']['value'] = $row['B'];
        $assoc['position_name_id']['value'] = $position;
        $assoc['total_sum']['value'] = $row['C'];
        if ($action === 'add') {
            $assoc['position_department']['value'] = $unit->get('name');
            $assoc['position_department_id']['value'] = $unit->get('id');
        }
        $bonusDocument->set('assoc_vars', $assoc, true);
        $bonusDocument->set('vars', array_values($assoc), true);
        self::$_registry['db']->StartTrans();
        if ($bonusDocument->save()) {
            $filters = [
                'where' => array(
                    'd.id = ' . $bonusDocument->get('id')
                ),
                'model_lang' => $bonusDocument->get('model_lang'),
                'skip_assignments' => true,
                'skip_permissions_check' => true
            ];
            $new_document = Documents::searchOne(self::$_registry, $filters);
            self::$_registry->set('get_old_vars', true, true);
            $new_document->getVars();
            $new_document->set('status', 'closed', true);
            $new_document->set('substatus', 'closed_3', true);
            $substatus_name = self::$_registry['db']->GetOne('SELECT `name` FROM ' . DB_TABLE_DOCUMENTS_STATUSES . ' WHERE `id`="' . preg_replace('#.*_(\d+)#', '$1', $new_document->get('substatus')) . '" AND `lang`="' . self::$_registry['lang'] . '"');
            $new_document->set('substatus_name', $substatus_name, true);
            $new_document->setStatus();
            Documents_History::saveData(self::$_registry,
                array(
                    'model' => $bonusDocument,
                    'action_type' => $action,
                    'new_model' => $new_document,
                    'old_model' => $old_document
                )
            );

        } else {
            self::$_registry['db']->FailTrans();
        }
        $result = !self::$_registry['db']->HasFailedTrans();
        self::$_registry['db']->CompleteTrans();
        return $result;


    }

    static function addDeductionForRow($row, $customer, $position, $date, $unit)
    {
        $action = 'edit';
        $filters = [
            'where' => [
                'd.type = ' . self::$params['deductionDocType'],
                "d.date = '$date'",
                "d.customer = $customer",
                "a__position_department_id = " . $unit->get('id'),
                "a__position_name_id = " . $position,
            ],
            'sanitize' => false,
            'skip_permissions_check' => '1'
        ];
        $subtractDoc = Documents::searchOne(self::$_registry, $filters);

        if (!$subtractDoc) {
            $action = 'add';
            $filters = array('where' => array('dt.id = \'' . self::$params['deductionDocType'] . '\'',
                'dt.active = 1'),
                'sanitize' => true);
            $document_type = Documents_Types::searchOne(self::$_registry, $filters);

            //start building new document
            $subtractDoc = new Document(self::$_registry, array('type' => self::$params['deductionDocType']));
            $old_document = clone $subtractDoc;
            $subtractDoc->set('date', $date);
            $subtractDoc->set('customer', $customer);
            $subtractDoc->set('name', $document_type->get('default_name'));
            $subtractDoc->set('customer_name', $row['A']);
            $currUser = self::$_registry->get('currentUser');
            $subtractDoc->set('employee', $currUser->get('employee'));
            $subtractDoc->set('employee_name', $currUser->get('employee_name'));
            $subtractDoc->set('department', $currUser->get('default_department'));
            $subtractDoc->set('group', $currUser->get('default_group'));
        } else {
            $old_document = clone $subtractDoc;
        }
        $subtractDoc->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . self::$_registry['lang'] . '/documents.ini');
        $assoc = $subtractDoc->getAssocVars();
        $subtractDoc->set('notes', $row['F']);
        $assoc['position_name']['value'] = $row['B'];
        $assoc['position_name_id']['value'] = $position;
        $assoc['total_sum']['value'] = $row['E'];
        if ($action === 'add') {
            $assoc['position_department']['value'] = $unit->get('name');
            $assoc['position_department_id']['value'] = $unit->get('id');
        }
        $subtractDoc->set('assoc_vars', $assoc, true);
        $subtractDoc->set('vars', array_values($assoc), true);
        self::$_registry['db']->StartTrans();
        if ($subtractDoc->save()) {
            $filters = [
                'where' => array(
                    'd.id = ' . $subtractDoc->get('id')
                ),
                'model_lang' => $subtractDoc->get('model_lang'),
                'skip_assignments' => true,
                'skip_permissions_check' => true
            ];
            $new_document = Documents::searchOne(self::$_registry, $filters);
            self::$_registry->set('get_old_vars', true, true);
            $new_document->getVars();
            $new_document->set('status', 'closed', true);
            $new_document->set('substatus', 'closed_4', true);
            $substatus_name = self::$_registry['db']->GetOne('SELECT `name` FROM ' . DB_TABLE_DOCUMENTS_STATUSES . ' WHERE `id`="' . preg_replace('#.*_(\d+)#', '$1', $new_document->get('substatus')) . '" AND `lang`="' . self::$_registry['lang'] . '"');
            $new_document->set('substatus_name', $substatus_name, true);
            $new_document->setStatus();
            Documents_History::saveData(self::$_registry,
                array(
                    'model' => $subtractDoc,
                    'action_type' => $action,
                    'new_model' => $new_document,
                    'old_model' => $old_document
                )
            );

        } else {
            self::$_registry['db']->FailTrans();
        }
        $result = !self::$_registry['db']->HasFailedTrans();
        self::$_registry['db']->CompleteTrans();
        return $result;


    }
}

