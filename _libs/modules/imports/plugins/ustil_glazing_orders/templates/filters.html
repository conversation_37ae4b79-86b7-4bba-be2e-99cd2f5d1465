<table cellpadding="2" cellspacing="2" border="0">
  <tr>
    <td class="labelbox"><a name="error_upload_file"><label for="upload_file"{if $messages->getErrors('upload_file')} class="error"{/if}>{help label='upload_files'}</label></a></td>
    <td class="required">{#required#}</td>
    <td nowrap="nowrap">
      <input type="file" multiple="multiple" accept="{$accept}" class="filebox" name="upload_file[]" id="upload_file" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#imports_upload_files#|escape}" />
      {include file="input_hidden.html"
              standalone=true
              name='import_key'
              value=$import_filters.import_key|default:''
              custom_class='import_key'
      }
    </td>
  </tr>
</table>
