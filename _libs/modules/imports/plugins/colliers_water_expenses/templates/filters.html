<table cellpadding="2" cellspacing="2" border="0">
  <tr>
    <td class="labelbox"><a name="error_upload_file"><label for="upload_file"{if $messages->getErrors('upload_file')} class="error"{/if}>{help label='upload_files'}</label></a></td>
    <td class="required">{#required#}</td>
    <td nowrap="nowrap">
      <div style="float: left">
        <table cellpadding="0" cellspacing="0" border="0" id="files_tbl">
          <tr style="display: none">
            <td></td>
          </tr>
          <tr>
            <td style="padding-bottom: 3px;">
              <input type="file" class="filebox" style="width: 170px;" name="upload_file[]" id="upload_file_1" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#import#|escape}" />
            </td>
          </tr>
        </table>
      </div>
      <div style="float: left;">
        <div class="t_buttons">
          <div id="files_tbl_plusButton" onclick="addField('files_tbl', 'upload_file')" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
          <div id="files_tbl_minusButton" class="disabled" onclick="removeField('files_tbl')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
        </div>
      </div>
    </td>
  </tr>
</table>
