<?php

/**
 * Custom import of "Product" nomenclatures for installation SELAMORE
 */
Class Custom_Import extends Model_Factory {
    public static $modelName = 'Import';
    public static $allowedExtensions = ['xlsx', 'xls', 'xlsm'];
    private static $columnsLabels;
    private static $registry;
    private static $params;
    private static $labelsRowNum = 1;
    private static $firstDataRowNum = 2;
    private static $firstColLetter = 'A';
    private static $columnsNames = [
        'product_fileupload',
        'supplier_name',
        'product_name_bg',
        'product_supplier_code',
        'name',
        'product_description',
        'category',
        'product_model',
        'product_color_code',
        'product_size',
        'product_measure_name',
        // 'sell_price',
        // 'sell_price_currency',
        // 'product_link_site',
    ];
    private static $columnsMap = [];
    private static $requiredVars = [];
    // Vars forming key for unique recognition of nomenclatures
    // Currently the code expects them to be non-multilang
    private static $keyVars = [];
    private static $textareaVars = [];
    private static $logTypesMsgMethods = [
        'required'     => 'setError',
        'invalid'      => 'setError',
        'unrecognized' => 'setError',
        'duplicated'   => 'setWarning',
        'exist'        => 'setWarning',
        'failed'       => 'setError',
        'added'        => 'setMessage',
    ];
    private static $rowsLog = [];
    private static $rowsLogText = [];
    private static $skipAddingData = false;
    private static $componentProductsRows = [];

    public static function import($registry, $params) {
        self::$registry = $registry;
        self::$params = $params;

        $settings = self::getSettings();

        if (!$registry['currentUser']->checkRights('nomenclatures', 'add')
                || !$registry['currentUser']->checkRights("nomenclatures{$settings['nom_type_product']}", 'add')) {
            $query = "
                SELECT name_plural
                  FROM " . DB_TABLE_NOMENCLATURES_TYPES_I18N . "
                  WHERE parent_id = {$settings['nom_type_product']}
                    AND lang = '{$registry['lang']}'";
            $nomTypeProductsName = $registry['db']->GetOne($query);
            $registry['messages']->setWarning(sprintf($registry['translater']->translate('no_rights_to_add_products'), $nomTypeProductsName));
            return false;
        }

        set_time_limit(0);
        ini_set('memory_limit', $settings['memory_limit']);

        // Prepare key vars
        $keyVars = preg_split('/\s*,\s*/', $settings['key_vars']);
        $keyVars = array_intersect($keyVars, self::$columnsNames);
        self::$keyVars = array_fill_keys($keyVars, null);

        // Prepare vars
        $requiredVars = array_flip(preg_split('/\s*,\s*/', $settings['required_vars']));
        foreach (self::$columnsNames as $colName) {
            // Mapping
            self::$columnsMap[$settings["col_{$colName}"]] = $colName;

            // Required
            if (array_key_exists($colName, $requiredVars)) {
                self::$requiredVars[$colName] = null;
            }
        }
        $columnsNamesList = "'" . implode("', '", self::$columnsNames) . "'";
        $query = "
            SELECT name, name
              FROM " . DB_TABLE_FIELDS_META . "
              WHERE model = 'Nomenclature'
                AND model_type = {$settings['nom_type_product']}
                AND `type` = 'textarea'";
        self::$textareaVars = self::$registry['db']->GetAssoc($query);

        if (empty($_FILES['uploaded_file']) || empty($_FILES['uploaded_file']['tmp_name'])) {
            $registry['messages']->setWarning($registry['translater']->translate('please_select_file'));
            return false;
        } else if (!in_array(pathinfo($_FILES['uploaded_file']['name'], PATHINFO_EXTENSION), self::$allowedExtensions)) {
            $registry['messages']->setWarning($registry['translater']->translate('invalid_file_extension'));
            return false;
        }

        $objPHPExcel = self::loadExcelFile($_FILES['uploaded_file']['tmp_name'], false);
        if (!$objPHPExcel) {
            return false;
        }

        self::loadColumnsLabels($objPHPExcel);

        $fileData = self::getFileData($objPHPExcel);
        if (!$fileData) {
            self::setWarning($registry['translater']->translate('no_data'));
            return false;
        }

        $drawings = self::getDrawings($objPHPExcel);
        $dataForImport = self::prepareDataForImport($fileData, $drawings);

        $newData = self::getNewData($dataForImport);

        if (!self::$skipAddingData && $newData) {
            self::addNewProducts($newData);
        }

        // Log and message
        $result = false;
        $rowsLog = self::getRowsLog();
        $hasAddedRows = array_key_exists('added', $rowsLog);
        $success = (empty($rowsLog) || $hasAddedRows);
        $rowsLogText = self::getRowsLogForImportLog();
        self::log($success, $rowsLogText);
        if (empty($rowsLog)) {
            $registry['messages']->setWarning($registry['translater']->translate('no_data'));
        } else {
            if ($hasAddedRows && count(array_keys($rowsLog)) === 1) {
                $allRowsLinks = [];
                foreach ($rowsLog['added']['rows'] as $addedRow) {
                    $allRowsLinks[] = $addedRow['link'];
                }
                $allRowsLinks = implode(', ', $allRowsLinks);
                $registry['messages']->setMessage(
                    sprintf(
                        $registry['translater']->translate('all_rows_added_successfully'),
                        $allRowsLinks
                    )
                );
            } else {
                foreach ($rowsLog as $typeKey => $type) {
                    // if ($typeKey === 'added') {
                    //     $method = 'setMessage';
                    // } else if ($typeKey === 'invalid') {
                    //     $method = 'setError';
                    // } else if ($hasAddedRows || $typeKey === 'exist' || $typeKey === 'duplicated') {
                    //     $method = 'setWarning';
                    // } else {
                    //     $method = 'setError';
                    // }
                    $method = $type['msgMethod'];
                    $rows = [];
                    foreach ($type['rows'] as $rowNum => $row) {
                        if ($typeKey === 'added' || $typeKey === 'exist') {
                            $rows[] = $row['link'];
                        } else {
                            $rows[] = "{$rowNum}{$row['columnsList']}";
                        }
                    }
                    $rows = implode(', ', $rows);
                    $msg = "{$type['name']}: {$rows}";
                    $registry['messages']->$method($msg);
                }
            }
        }

        return $result;
    }

    private static function getSettings() {
        return self::$params?:[];
    }

    private static function getFileData($objPHPExcel) {
        $fileData = [];

        if (!$objPHPExcel) {
            return $fileData;
        }

        $settings = self::getSettings();
        $sheet = $objPHPExcel->getSheet(0);
        $firstColLetter = self::$firstColLetter;
        $lastColLetter = $sheet->getHighestDataColumn();
        $firstDataRowNum = self::$firstDataRowNum;
        $highestRowNum = $sheet->getHighestDataRow();
        $range = "{$firstColLetter}{$firstDataRowNum}:{$lastColLetter}{$highestRowNum}";

        $fileDataRaw = $sheet->rangeToArray($range, null, true, true, true);
        foreach ($fileDataRaw as $rowNum => $row) {
            $rowKnownColumns = [];
            $rowIsEmpty = true;

            $rowIsComponentProduct = (
                !empty($settings['col_element_supplier_code']) && array_key_exists($settings['col_element_supplier_code'], $row) && trim($row[$settings['col_element_supplier_code']]) !== ''
                || !empty($settings['col_element_name']) && array_key_exists($settings['col_element_name'], $row) && trim($row[$settings['col_element_name']]) !== ''
            );

            if ($rowIsComponentProduct) {
                if (!empty($settings['col_product_description']) && array_key_exists($settings['col_product_description'], $row)) {
                    $row[$settings['col_product_description']] = '';
                }
                if (!empty($settings['col_product_color_code']) && array_key_exists($settings['col_product_color_code'], $row)) {
                    $row[$settings['col_product_color_code']] = '';
                }
                if (!empty($settings['col_product_size']) && array_key_exists($settings['col_product_size'], $row)) {
                    $row[$settings['col_product_size']] = '';
                }
            }

            foreach ($row as $colLetter => $value) {
                // Skip unknown columns
                if (!array_key_exists($colLetter, self::$columnsMap)) {
                    continue;
                }

                $rowKnownColumns[$colLetter] = $value;

                // Mark that the row is not empty
                if ($value !== '') {
                    $rowIsEmpty = false;
                }
            }

            if ($rowIsEmpty) {
                continue;
            }

            $rowKnownColumns['is_component_product'] = $rowIsComponentProduct?'1':'0';
            $fileData[$rowNum] = $rowKnownColumns;
        }

        return $fileData;
    }

    private static function getDrawings($objPHPExcel) {
        $sheet = $objPHPExcel->getSheet(0);
        $drawings = [];
        foreach ($sheet->getDrawingCollection() as $drawing) {
            if ($drawing instanceof PHPExcel_Worksheet_Drawing) {
                $coordinates = $drawing->getCoordinates();
                $drawings[$coordinates][] = $drawing;
            }
        }

        return $drawings;
    }

    private static function prepareDataForImport($fileData, $drawings) {
        $preparedData = [];

        if (empty($fileData)) {
            return $preparedData;
        }

        $settings = self::getSettings();
        $lang = self::$registry['lang'];
        $db = self::$registry['db'];

        $valuesMap = [
            'supplier_name' => [],
        ];
        $assocData = [];
        foreach ($fileData as $rowNum => $row) {
            // Preserve the custom flag for checking if the row is for component product
            $isRowComponentProduct = $row['is_component_product'];

            // Skip unknown columns (they may or may not have been skipped earlier)
            $row = array_intersect_key($row, self::$columnsMap);

            // Skip empty rows (they may or may not have been skipped earlier)
            $columnsWithData = array_filter($row, function ($v) {
                return !is_null($v) && trim($v) !== '';
            });
            if (empty($columnsWithData)) {
                continue;
            }

            $skipRow = false;

            $assocRow = [];
            foreach ($row as $colLetter => $value) {
                // Get the associative name of the column
                $colName = self::$columnsMap[$colLetter];

                // Trim the value
                $value = trim($value);

                // If the row has no data for any of the required columns
                if (array_key_exists($colName, self::$requiredVars) && $value === '') {
                    // Log the row
                    self::logRow('required', $rowNum, $colName);
                    // Skip the entire row
                    $skipRow = true;
                }

                // Collect the column value for the given row
                $assocRow[$colName] = $value;
            }

            // Skip the row
            if ($skipRow) {
                continue;
            }

            // Collect component products rows
            if ($isRowComponentProduct) {
                self::$componentProductsRows[$assocRow['name']][] = $rowNum;
            }

            $assocRow['is_component_product'] = $isRowComponentProduct;
            $assocData[$rowNum] = $assocRow;

            // If the row is successful (i.e. not missing a required data)
            foreach ($assocRow as $colName => $value) {
                if (array_key_exists($colName, $valuesMap)) {
                    // Collect his values which has to be mapped
                    $valuesMap[$colName][$value] = null;
                }
            }
        }

        // // Map: sell_price_currency
        // $valuesMap['sell_price_currency'] = [];
        // $currencies = Finance_Currencies::getAvailableCurrencies(self::$registry);
        // foreach ($currencies as $currencyCode => $currency) {
        //     $valuesMap['sell_price_currency'][$currencyCode] = $currencyCode;
        //     if (!empty($currency['is_main'])) {
        //         $valuesMap['sell_price_currency'][''] = $currencyCode;
        //     }
        // }

        // Map: category
        $query = "
            SELECT ci.name, c.id
              FROM " . DB_TABLE_CATEGORIES . " AS c
              JOIN " . DB_TABLE_CATEGORIES_I18N . " AS ci
                ON (ci.parent_id = c.id
                  AND ci.lang = '{$lang}')";
        $valuesMap['category'] = $db->GetAssoc($query);

        // Map: product_measure_name
        $query = "
            SELECT name, id
              FROM " . DB_TABLE_MEASURES . "
              WHERE lang = '{$lang}'";
        $valuesMap['product_measure_name'] = $db->GetAssoc($query);

        // Map: supplier_name
        if (!empty($valuesMap['supplier_name'])) {
            $suppliersNames = General::slashesEscape(array_keys($valuesMap['supplier_name']));
            $suppliersNamesList = "'" . implode("', '", $suppliersNames) . "'";
            $suppliersTypeSQL = '';
            if (!empty($settings['suppliers_types'])) {
                $suppliersTypes = preg_split('/\s*,\s*/', $settings['suppliers_types']);
                $suppliersTypes = array_filter($suppliersTypes);
                if (count($suppliersTypes) === 1) {
                    $suppliersType = reset($suppliersTypes);
                    $suppliersTypeSQL = " AND c.type = {$suppliersType}";
                } else {
                    $suppliersTypesList = implode(',', $suppliersTypes);
                    $suppliersTypeSQL = "AND c.type IN ({$suppliersTypesList})";
                }
            }
            $query = "
                SELECT TRIM(CONCAT(ci.name, '', ci.lastname)) AS name, c.id
                  FROM " . DB_TABLE_CUSTOMERS . " AS c
                  JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                    ON (NOT c.deleted
                      AND c.active" .
                      ($suppliersTypeSQL ? ("
                      " . $suppliersTypeSQL) : '') . "
                      AND ci.parent_id = c.id
                      AND ci.lang = '{$lang}'
                      AND TRIM(CONCAT(ci.name, '', ci.lastname)) IN ({$suppliersNamesList}))";
            $valuesMap['supplier_name'] = $db->GetAssoc($query);
        }

        // Fill mapped values and collect prepared data
        foreach ($assocData as $rowNum => $row) {
            $skipRow = false;

            // Non-textarea vars should not have new lines
            foreach ($row as $col => $value) {
                if (!array_key_exists($col, self::$textareaVars) && mb_strpos($value, PHP_EOL) !== false) {
                    self::logRow('invalid', $rowNum, $col);
                    $skipRow = true;
                }
            }

            // The photos columns should have no text in it
            if (!empty($row['product_fileupload'])) {
                self::logRow('invalid', $rowNum, 'product_fileupload');
                $skipRow = true;
            } else {
                // Get the drawing
                $photoCoordinates = "{$settings['col_product_fileupload']}{$rowNum}";
                if (array_key_exists($photoCoordinates, $drawings)) {
                    $drawingsCount = count($drawings[$photoCoordinates]);
                    if ($drawingsCount > 1) {
                        self::logRow('invalid', $rowNum, 'product_fileupload');
                        $skipRow = true;
                    } else if ($drawingsCount === 1) {
                        $row['product_fileupload'] = reset($drawings[$photoCoordinates]);
                    }
                }
            }

            // $sellPrice = trim($row['sell_price']);
            // if ($sellPrice === '') {
            //     $sellPrice = '0';
            // }
            // $sellPriceFloat = (float)trim($row['sell_price']);
            // if ((string)$sellPriceFloat !== $sellPrice) {
            //     self::logRow('invalid', $rowNum, 'sell_price');
            //     $skipRow = true;
            // } else {
            //     $row['sell_price'] = $sellPriceFloat;
            // }

            // $sellPriceCurrency = strtoupper(trim($row['sell_price_currency']));
            // if (!array_key_exists($sellPriceCurrency, $valuesMap['sell_price_currency'])) {
            //     self::logRow('unrecognized', $rowNum, 'sell_price_currency');
            //     $skipRow = true;
            // } else {
            //     $row['sell_price_currency'] = $valuesMap['sell_price_currency'][$sellPriceCurrency];
            // }

            if ($row['category'] !== '' && !array_key_exists($row['category'], $valuesMap['category'])) {
                self::logRow('unrecognized', $rowNum, 'category');
                $skipRow = true;
            } else {
                $categoryId = $valuesMap['category'][$row['category']] ?? $settings['nom_product_default_category'];
                if (empty($categoryId)) {
                    $row['categories'] = [1];
                } else {
                    $row['categories'] = Nomenclatures_Categories::getTreeParentsIds(self::$registry, $categoryId);
                    $row['categories'][] = $categoryId;
                }
            }

            if ($row['product_measure_name'] !== '' && !array_key_exists($row['product_measure_name'], $valuesMap['product_measure_name'])) {
                self::logRow('unrecognized', $rowNum, 'product_measure_name');
                $skipRow = true;
            } else {
                $row['product_measure_name'] = ($valuesMap['product_measure_name'][$row['product_measure_name']]??($settings['nom_product_default_product_measure_name']??''));
            }

            if ($row['supplier_name'] !== '' && !array_key_exists($row['supplier_name'], $valuesMap['supplier_name'])) {
                self::logRow('unrecognized', $rowNum, 'supplier_name');
                $skipRow = true;
            } else {
                $row['supplier_id'] = $valuesMap['supplier_name'][$row['supplier_name']]??'';
            }

            if ($row['is_component_product']
                    && (/*$row['name'] !== $row['product_supplier_code']
                        || */count(self::$componentProductsRows[$row['name']]??[]) < 2)) {
                self::logRow('invalid_component_product', $rowNum);
                $skipRow = true;
                self::$skipAddingData = true;
            }

            // Skip the row
            if ($skipRow) {
                continue;
            }

            $preparedData[$rowNum] = $row;
        }

        return $preparedData;
    }

    private static function logRow($type, $rowNum, $colName = '', $nomId = null) {
        if (!array_key_exists($type, self::$rowsLog)) {
            self::$rowsLog[$type] = [
                'name'      => self::$registry['translater']->translate("rows_log_type_{$type}") ?: $type,
                'msgMethod' => self::$logTypesMsgMethods[$type] ?? 'setError',
                'rows'      => [],
            ];
        }
        if (!array_key_exists($rowNum, self::$rowsLog[$type]['rows'])) {
            self::$rowsLog[$type]['rows'][$rowNum] = [
                'columns'     => [],
                'columnsList' => '',
            ];

            if ($nomId) {
                $url = sprintf(
                    '%s?%s=nomenclatures&amp;nomenclatures=view&amp;view=%d',
                    $_SERVER['PHP_SELF'],
                    self::$registry['module_param'],
                    $nomId
                );
                $link = "<a href=\"{$url}\" target=\"_blank\">{$rowNum}</a>";
                self::$rowsLog[$type]['rows'][$rowNum]['link'] = $link;
            }
        }

        $colLabel = self::getColumnLabel($colName);
        if ($colLabel) {
            self::$rowsLog[$type]['rows'][$rowNum]['columns'][$colName] = $colLabel;
        }
        if ($colLabel || empty(self::$rowsLog[$type]['rows'][$rowNum]['columnsList'])) {
            $columnsList = implode(', ', self::$rowsLog[$type]['rows'][$rowNum]['columns']);
            if ($columnsList) {
                $columnsList = " ({$columnsList})";
            }
            self::$rowsLog[$type]['rows'][$rowNum]['columnsList'] = $columnsList;
        }
    }

    private static function getRowsLog() {
        // Move added to beginning of array
        if (array_key_exists('added', self::$rowsLog)) {
            self::$rowsLog = array_merge(['added' => self::$rowsLog['added']], self::$rowsLog);
        }
        return self::$rowsLog;
    }

    private static function getRowsLogForImportLog() {
        if (self::$rowsLogText) {
            return self::$rowsLogText;
        }

        $rowsLogText = [];
        $rowsLog = self::getRowsLog();
        foreach ($rowsLog as $type) {
            $rowsLogText[$type['name']] = [];
            foreach ($type['rows'] as $rowNum => $row) {
                $rowsLogText[$type['name']][] = " {$rowNum}{$row['columnsList']}";
            }
        }
        self::$rowsLogText = $rowsLogText;

        return self::$rowsLogText;
    }

    /**
     * Get the new data
     *
     * @param $dataForImport array Entire data for import
     *
     * @return array
     */
    private static function getNewData($dataForImport) {
        $newData = [];

        if (empty($dataForImport)) {
            return $newData;
        }

        $settings = self::getSettings();
        $db = self::$registry['db'];
        $lang = self::$registry['lang'];

        $keyVarsNames = array_keys(self::$keyVars);
        $dataKeys = [];
        $duplicatedKeys = [];
        foreach ($dataForImport as $rowNum => $row) {
            $key = [$row['is_component_product']];
            foreach ($keyVarsNames as $keyVarName) {
                if (array_key_exists($keyVarName, $row)) {
                    $key[] = $row[$keyVarName];
                } else {
                    $key[] = '';
                }
            }
            $key = implode('|', $key);

            // If the current row key is already collected or is one of the duplicated keys
            if (array_key_exists($key, $dataKeys) || array_key_exists($key, $duplicatedKeys)) {
                // For component products we get only the first row
                if ($row['is_component_product']) {
                    continue;
                }

                // Collect current key as duplicated
                $duplicatedKeys[$key] = $key;

                // If it's collected as data key
                if (array_key_exists($key, $dataKeys)) {
                    // Log the first (original) row as duplicate
                    self::logRow('duplicated', $dataKeys[$key]);
                    //// Remove the current key from the data keys
                    //unset($dataKeys[$key]);
                }

                // Log the current row as duplicate too
                self::logRow('duplicated', $rowNum);
                //// Skip collecting the current row
                //continue;
            }

            $dataKeys[$key] = $rowNum;
        }

        $keyVarsList = "'" . implode("', '", $keyVarsNames) . "'";
        $keyVarsSet = implode(',', $keyVarsNames);
        $query = "
            SELECT id, multilang
              FROM " . DB_TABLE_FIELDS_META . "
              WHERE model = 'Nomenclature'
                AND model_type = {$settings['nom_type_product']}
                AND name IN ({$keyVarsList})
              ORDER BY FIND_IN_SET(name, '{$keyVarsSet}')";
        $keyVars = $db->GetAll($query);
        $dataKeyAliasesList = [];
        $sqlTagComponentProduct = '';
        if (!empty($settings['tag_component_product'])) {
            $dataKeyAliasesList[] = 'IF(tm.tag_id IS NULL, 0, 1)';
            $sqlTagComponentProduct = "
                  LEFT JOIN " . DB_TABLE_TAGS_MODELS . " AS tm
                    ON (tm.model_id = n.id
                      AND tm.model = 'Nomenclature'
                      AND tm.tag_id = {$settings['tag_component_product']})";
        }
        $i = 1;
        $keyVarsSQL = '';
        foreach ($keyVars as $keyVar) {
            $keyVarLang = ($keyVar['multilang'] ? $lang : '');
            $keyVarsSQL .= "
                JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc{$i}
                  ON (nc{$i}.model_id = n.id
                    AND nc{$i}.var_id = {$keyVar['id']}
                    AND nc{$i}.num = 1
                    AND nc{$i}.lang = '{$keyVarLang}')";
            $dataKeyAliasesList[] = "nc{$i}.value";
            $i++;
        }
        $dataKeyAliasesList = implode(", '|', ", $dataKeyAliasesList);

        $dataKeysList = "'" . implode("', '", array_keys($dataKeys)) . "'";
        $query = "
            SELECT CONCAT({$dataKeyAliasesList}) AS data_key, n.id
              FROM " . DB_TABLE_NOMENCLATURES_TYPES . " AS nt
              JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                ON (nt.id = {$settings['nom_type_product']}
                  AND n.type = nt.id
                  AND NOT n.deleted
                  AND n.active){$keyVarsSQL}
              $sqlTagComponentProduct
              HAVING data_key IN ($dataKeysList)";
        $dbKeys = $db->GetAssoc($query);

        foreach ($dataKeys as $dataKey => $rowNum) {
            if (array_key_exists($dataKey, $dbKeys)) {
                self::logRow('exist', $rowNum, '', $dbKeys[$dataKey]);
            } else {
                $newData[$rowNum] = $dataForImport[$rowNum];
            }
        }

        return $newData;
    }

    private static function addNewProducts($newData) {
        if (empty($newData)) {
            return;
        }

        $registry = self::$registry;
        $settings = self::getSettings();
        $db = $registry['db'];

        $registry['translater']->loadFile(PH_MODULES_DIR . 'nomenclatures/i18n/' . $registry['lang'] . '/nomenclatures.ini');
        $nomTypeFilters = [
            'where'    => [
                "nt.id = {$settings['nom_type_product']}",
                'nt.active = 1'
            ],
            'sanitize' => true
        ];
        $nomType = Nomenclatures_Types::searchOne($registry, $nomTypeFilters);
        $nomParamsDefault = [
            'type'      => $nomType->get('id'),
            'lang'      => $registry['lang'],
            'group'     => $nomType->getDefaultGroup()?:PH_ROOT_GROUP,
        ];

        // Set batch data
        $nomParamsDefault['has_batch'] = 1;
        $nomParamsDefault['has_batch_code'] = 1;
        $nomParamsDefault['subtype'] = 'commodity';
        $originalAction = self::$registry->get('action');
        self::$registry->set('action', 'add', true);

        // Prepare to be able to set categories
        self::$registry['request']->set('update_categories', 1, 'all', true);

        foreach ($newData as $rowNum => $rowData) {
            $db->StartTrans();

            $productIsComponent = $rowData['is_component_product'];
            unset($rowData['is_component_product']);

            $nomParams = $nomParamsDefault;
            $nomParams['name'] = $rowData['name'];
            // $nomParams['sell_price'] = $rowData['sell_price'];
            // $nomParams['sell_price_currency'] = $rowData['sell_price_currency'];
            $nomParams['categories'] = $rowData['categories'];
            $nom = new Nomenclature($registry, $nomParams);

            // For product_fileupload: convert drawing to
            if (!empty($rowData['product_fileupload']) && ($rowData['product_fileupload'] instanceof PHPExcel_Worksheet_Drawing)) {
                $drawing = $rowData['product_fileupload'];
                $tmpId = time();

                $drawingContent = '';
                $zipReader = fopen($drawing->getPath(), 'r');
                while (!feof($zipReader)) {
                    $drawingContent .= fread($zipReader, 1024);
                }
                fclose($zipReader);
                $drawingFileName = sprintf(
                    '%s%s_%05d.%s',
                    Files::$importedFilePrefix,
                    $tmpId,
                    $drawing->getImageIndex(),
                    $drawing->getExtension(),
                );
                $drawingCachePath = PH_IMPORTS_CACHE_DIR . uniqid(rand(), true) . '.' . $drawing->getExtension();
                try {
                    file_put_contents($drawingCachePath, $drawingContent);
                } catch (Exception $e){
                }
                if (!file_exists($drawingCachePath)) {
                    $db->FailTrans();
                } else {
                    $drawingFileParams = [
                        'name'     => $drawingFileName,
                        'tmp_name' => $drawingCachePath,
                        'size'     => filesize($drawingCachePath),
                        'type'     => mime_content_type($drawingCachePath),
                        'error'    => '',
                    ];
                    $attachParams = [
                        'name'     => $drawingFileName,
                        'filename' => $drawingFileName,
                    ];

                    //IMPORTANT: set custom model id because the file is still temporary
                    $nom->set('id', $tmpId, true);
                    $drawingFileId = Files::attachFile(self::$registry, $drawingFileParams, $attachParams, $nom);
                    $nom->unsetProperty('id', true);
                    $nom->set('model_id', $tmpId, true);
                    if ($drawingFileId) {
                        $drawingFileSearchParams = [
                            'where'    => ["f.id = {$drawingFileId}"],
                            'sanitize' => true,
                        ];
                        $rowData['product_fileupload'] = Files::searchOne(self::$registry, $drawingFileSearchParams);
                    }
                }
            }

            if (!$db->HasFailedTrans()) {
                $getOldVars = $registry->get('get_old_vars');
                $registry->set('get_old_vars', true, true);
                $nom->getVars();
                $registry->set('get_old_vars', $getOldVars, true);
                $oldNom = clone $nom;
                $nomVars = $nom->get('vars');
                foreach ($nomVars as $varIndex => $var) {
                    if (array_key_exists($var['name'], $rowData)) {
                        $nomVars[$varIndex]['value'] = $rowData[$var['name']];
                    }
                }
                $nom->set('vars', $nomVars, true);
                if ($nom->save()) {
                    $newNomParams = [
                        'where'                  => ["n.id = {$nom->get('id')}"],
                        'skip_permissions_check' => true,
                    ];
                    $newNom = Nomenclatures::searchOne($registry, $newNomParams);
                    $registry->set('get_old_vars', true, true);
                    $newNom->getVars();
                    $registry->set('get_old_vars', $getOldVars, true);
                    $historyId = Nomenclatures_History::saveData(
                        self::$registry,
                        [
                            'action_type' => 'add',
                            'old_model'   => $oldNom,
                            'model'       => $nom,
                            'new_model'   => $newNom,
                        ]
                    );
                    if (!$historyId) {
                        $db->FailTrans();
                    } else {
                        // Execute action automations
                        // Currently there is an automation, which will create the nomenclature code
                        $automationsController = new Automations_Controller($registry);
                        $actionAutomationsResult = $automationsController->executeActionAutomations($oldNom, $newNom, 'add');
                        if (!$actionAutomationsResult) {
                            $db->FailTrans();
                        } elseif (!empty($settings['tag_component_product']) && $productIsComponent) {
                            // Tag product as component product
                            $newNom->getModelTagsForAudit();;
                            $oldNom = clone $newNom;
                            $oldNom->sanitize();
                            $nom = clone $newNom;
                            if ($nom->addTags([$settings['tag_component_product']])) {
                                $newNom = Nomenclatures::searchOne($registry, [
                                    'where'                  => ["n.id = {$nom->get('id')}"],
                                    'skip_permissions_check' => true,
                                ]);
                                $newNom->getModelTagsForAudit();

                                $historyId = Nomenclatures_History::saveData(
                                    self::$registry,
                                    [
                                        'action_type' => 'tag',
                                        'old_model'   => $oldNom,
                                        'model'       => $nom,
                                        'new_model'   => $newNom,
                                    ]
                                );
                                if (!$historyId) {
                                    $db->FailTrans();
                                } else {
                                    // Execute action automations
                                    $automationsController = new Automations_Controller($registry);
                                    $actionAutomationsResult = $automationsController->executeActionAutomations($oldNom, $newNom, 'tag');
                                    if (!$actionAutomationsResult) {
                                        $db->FailTrans();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $hasFailedTrans = $db->HasFailedTrans();
            $db->CompleteTrans();
            if ($hasFailedTrans) {
                self::logRow('failed', $rowNum);
            } else {
                self::logRow('added', $rowNum, '', $nom->get('id'));
            }
        }

        // Remove flag for updating categories
        self::$registry['request']->remove('update_categories');

        // Restore original action
        self::$registry->set('action', $originalAction, true);
    }

    private static function log($success, $log) {
        $logData = [
            'import_type' => 'selamore_products',
            'success'     => $success,
            'log'         => $log,
        ];
        if (!empty($_FILES['uploaded_file']) && isset($_FILES['uploaded_file']['tmp_name'])) {
            $logData['file'] = $_FILES['uploaded_file'];
        }
        Imports::importLog(self::$registry, $logData);
    }

    private static function setWarning($msg) {
        $log = [
            self::$registry['translater']->translate('import_error') => $msg
        ];
        self::log(false, $log);
        self::$registry['messages']->setWarning($msg);
    }

    private static function loadExcelFile($fileName, $dataOnly = true) {
        /** @var PHPExcel $objPHPExcel */
        $objPHPExcel = null;
        if ($fileName) {
            try {
                require_once PH_PHPEXCEL_DIR . 'PHPExcel/IOFactory.php';
                // the factory will select the appropriate reader
                $objReader = PHPExcel_IOFactory::createReaderForFile($fileName);
                // these readers have no specifics in canRead and could handle any file format - we don't want that
                if ($objReader instanceof PHPExcel_Reader_CSV || $objReader instanceof PHPExcel_Reader_HTML) {
                    throw new Exception('Unable to identify a reader for this file');
                }
                if ($dataOnly && method_exists($objReader, 'setReadDataOnly')) {
                    $objReader->setReadDataOnly(true);
                }
                $objPHPExcel = $objReader->load($fileName);
            } catch (Exception $e) {
            }
        }
        return $objPHPExcel;
    }

    private function loadColumnsLabels($objPHPExcel) {
        self::$columnsLabels = [];

        if (!$objPHPExcel) {
            return self::$columnsLabels;
        }

        $sheet = $objPHPExcel->getSheet(0);
        $firstColLetter = self::$firstColLetter;
        $lastColLetter = $sheet->getHighestDataColumn();;
        $labelsRowNum = self::$labelsRowNum;
        $range = "{$firstColLetter}{$labelsRowNum}:{$lastColLetter}{$labelsRowNum}";
        $labelsArray = $sheet->rangeToArray($range, null, true, true, true);
        $labelsRow = reset($labelsArray);
        foreach ($labelsRow as $colLetter => $value) {
            if (array_key_exists($colLetter, self::$columnsMap)) {
                $colName = self::$columnsMap[$colLetter];
                self::$columnsLabels[$colName] = trim($value);
            }
        }
    }

    private function getColumnLabel($colName) {
        return self::$columnsLabels[$colName]??'';
    }
}
