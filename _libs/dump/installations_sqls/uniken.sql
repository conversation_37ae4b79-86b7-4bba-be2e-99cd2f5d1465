###########################################################################
### SQL nZoom Specific Updates Уникен (http://uniken.n-zoom.com/) ###
###########################################################################

######################################################################################
# 2013-08-07 - Added new automation for invoices templates generation for contract type 1

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_bank', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Банкова сметка', 'bg'),
(LAST_INSERT_ID(), 'label', 'Bank account', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '1', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoice_generate_pattern', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Шаблон за печат на фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice generate pattern', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '10', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'proforma_generate_pattern', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Шаблон за печат на проформа', 'bg'),
(LAST_INSERT_ID(), 'label', 'Proforma generate pattern', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '3', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_observer', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Отговорник по фактуриране', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoices observer', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '15', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_payment_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на падеж', 'bg'),
(LAST_INSERT_ID(), 'label', 'Date of payment', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 10\nperiod_type := working\nperiod := day\ndirection := after\npoint := issue', NULL, NULL, NOW(), 1, NOW(), 1, '');


INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_fiscal_event_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на данъчно събитие', 'bg'),
(LAST_INSERT_ID(), 'label', 'Fiscal event date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 0\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_period_rows', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Начин на фактуриране', 'bg'),
(LAST_INSERT_ID(), 'label', 'Period rows invoicing', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '#one_one\n#one_all\nall_one', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoice_email_template', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Шаблон за известяване - Фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice e-mail pattern', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'proforma_email_template', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Шаблон за известяване - Проформа', 'bg'),
(LAST_INSERT_ID(), 'label', 'Proforma e-mail pattern', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoice_first_period', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Фактуриране на първи период', 'bg'),
(LAST_INSERT_ID(), 'label', 'First period invoice', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'partial', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_single_period', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Единичен период на фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice single period', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 1\nperiod := month\nlength := 0', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_first_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на първа фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'First invoice date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 0\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_issue_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на фактуриране', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice issue date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 0\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, NULL, 0, NULL, 1, 'contracts', '', 'before_action', 1, '', 'condition := 1', 'plugin := uniken\r\nmethod := createInvoicesTemplatesService', 'cancel_action_on_fail := 1', 0, 0);

######################################################################################
# 2013-09-13 - Added new automation for payments to set customers MOL

# Added new automation for payments to set customers MOL
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
    VALUES (NULL, NULL, '0', NULL, '1', 'finance', 'payments', 'before_action', '0', '', 'condition := 1', 'plugin := uniken\nmethod := PaidByToMOL', 'cancel_action_on_fail := 1', '0', '0');

######################################################################################
# 2013-09-16 - fixed dummy settings for a dropdown field in GT2 of the contracts type 1
#            - added automation for validation of duplicated contracts for one and the same service-customer pair

# fixed dummy settings for a dropdown field in GT2 of the contracts type 1
UPDATE `_fields_meta` SET `source` = "agregates_available := count\nmethod := getCustomDropdown\ntable := DB_TABLE_NOMENCLATURES_TYPES\ntable_i18n := DB_TABLE_NOMENCLATURES_TYPES_I18N\nlabel := ti18n.name\noption_value := t.id\nwhere := t.id IN (5, 22, 23, 24, 25, 26)\njs_method := onchange => row = this.id.replace(/^.*_(\\d+)$/, '$1');eval('var tmp = params_article_alternative_deliverer_name_' + row + ';');clearAutocompleteItems('article_alternative_deliverer_name_' + row, tmp);\ntext_align := left\npermissions_edit := 1\npermissions_view := 1" WHERE `id` =101492;

# added automation for validation of duplicated contracts for one and the same service-customer pair
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
    VALUES (NULL, NULL, '0', NULL, '1', 'contracts', '', 'before_action', '1', '', 'condition := 1', 'plugin := uniken\nmethod := validatecontractType1', 'cancel_action_on_fail := 1', '0', '0');

######################################################################################
# 2014-07-28 - fixed single period for recurrent invoices templates

# fixed single period for recurrent invoices templates
UPDATE fin_invoices_templates SET single_period = CONCAT(recurrence, '\nlength := 0') WHERE recurrent = 1;

######################################################################################
# 2015-05-27 - Added some automations related to proforma to invoice transformations (bug 4293)
#             - Added dashlet for contracts invoices payments

# Added some automations related to proforma to invoice transformations (bug 4293)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Фактура от проформа при 100% плащане', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '2', '', 'condition := ''[action]'' == ''addpayment'' || ''[action]'' == ''payments'' && !$this->registry->get(''request'')->isRequested(''empty'')', 'plugin := uniken\r\nmethod := proformaToInvoice', NULL, 0, 0, 1),
(NULL, 'Фактура от проформа при 100% плащане', 0, NULL, 1, 'finance', 'payments', 'action', '0', '', 'condition := ''[action]'' == ''balance'' && !$this->registry->get(''request'')->isRequested(''empty'') && $this->registry->get(''request'')->get(''selected_tab'') == ''proforma''', 'plugin := uniken\r\nmethod := proformaToInvoice', NULL, 0, 0, 1),
(NULL, 'Фактура от проформа при 100% плащане', 0, NULL, 1, 'finance', 'incomes_reasons', 'before_action', '0', '', 'condition := ''[action]'' == ''addinvoice''', 'plugin := uniken\r\nmethod := proformaToInvoice', 'cancel_action_on_fail := 1', 0, 0, 1),
(NULL, 'Автоматични проформи от договор', 0, NULL, 1, 'crontab', '', 'before_action', '0', '', 'condition := ''[action]'' == ''issue_invoices''', 'plugin := uniken\nmethod := issueInvoices', NULL, 0, 0, 1),
(NULL, 'Валидация за издаване на проформа по договор', 0, NULL, 1, 'finance', 'invoices_templates', 'before_action', '0', '', 'condition := in_array(''[action]'', array(''issue_invoice'', ''ajax_issue_invoice''))', 'plugin := uniken\r\nmethod := validateContractInvoiceIssue', 'cancel_action_on_fail := 1', 0, 0, 1),
(NULL, 'Валидация за влизане в сила на споразумение по договор', 0, NULL, 1, 'contracts', '', 'before_action', '1', '', 'condition := in_array(''[action]'', array(''setstatus'', ''ajax_check_agreements_differences''))', 'plugin := uniken\r\nmethod := validateContractInvoiceIssue', 'cancel_action_on_fail := 1', 0, 0, 1);

# Added dashlet for contracts invoices payments
INSERT INTO `dashlets_plugins` (`type`, `settings`, `is_portal`, `visible`) VALUES
  ('uniken_proforma_payments', '', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Плащания по фактури от договор', 'Плащания по фактури от договор', 'bg'),
  (LAST_INSERT_ID(), 'Payments for contracts invoices', 'Плащания по фактури от договор', 'en');

######################################################################################
# 2015-07-10 - Added new automation for invoices print form and refactored all other invoices automations (bug 4293)

# PRE-DEPLOYED # Added new automation for invoices print form and refactored all other invoices automations (bug 4293)
# update fin_invoices_templates set pattern = 23 WHERE auto_issue > 0 and proforma = 1 and pattern > 0;
# update fin_invoices_templates set pattern = 24 WHERE auto_issue > 0 and proforma = 0 and pattern > 0;
# INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# (NULL, 'Създаване на печатна форма на фактура / проформа', 0, NULL, 1, 'dashlets', '', 'action', '0', 'invoice_patter := 23\r\nproforma_pattern := 24', 'condition := ''[action]'' == ''custom_action'' && $this->registry->get(''request'')->get(''plugin'') == ''fast_contract_invoice'' && $this->registry->get(''request'')->get(''custom_plugin_action'') == ''fastTemplateIssue''', 'plugin := uniken\r\nmethod := createPrintForm', NULL, 0, 0, 1);

######################################################################################
# 2015-07-21 - Removed automation that stops push into force of agreements and added several other

# PRE-DEPLOYED # Removed automation that stops push into force of agreements and added several other
#delete from automations where module = "contracts" and method like '%validateContractInvoiceIssue%';
#INSERT INTO `nzoom_uniken`.`automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#    (NULL, 'Магии при влизане в сила на ДС', '0', NULL, '1', 'contracts', NULL, 'before_action', '1', '', 'condition := ''[action]'' == ''ajax_check_agreements_differences''', 'plugin := uniken\nmethod := intoForceMagic', 'cancel_action_on_fail := 1', '0', '0', '1');
#INSERT INTO `nzoom_uniken`.`automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#    (NULL, 'Магии при влизане в сила на ДС', '0', NULL, '1', 'contracts', NULL, 'before_action', '1', '', 'condition := ''[action]'' == ''setstatus'' && [request_is_post] && ''[b_status]'' != ''closed'' && $this->registry->get(''request'')->get(''status'') == ''closed'' && $this->registry->get(''request'')->get(''substatus'') != ''closed_2''', 'plugin := uniken\nmethod := intoForceMagic', 'cancel_action_on_fail := 1', '0', '0', '1');
#INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#    (NULL, 'Влизане в сила на споразумение', 0, NULL, 1, 'crontab', '', 'before_action', '0', '', 'condition := ''[action]'' == ''start_contracts_agreements''', 'plugin := uniken\r\nmethod := startContractsAgreements', NULL, 0, 0, 1);

######################################################################################
# 2015-08-18 - Fixed clearAutocompleteItems call to match new function footprint

# Fixed clearAutocompleteItems call to match new function footprint
update _fields_meta set source = "text_align := left\npermissions_edit := 1\npermissions_view := 1\nmethod := getCustomDropdown\ntable := DB_TABLE_NOMENCLATURES_TYPES\ntable_i18n := DB_TABLE_NOMENCLATURES_TYPES_I18N\nlabel := ti18n.name\noption_value := t.id\nwhere := t.id IN (5, 22, 23, 24, 25, 26)\njs_method := onchange => row = this.id.replace(/^.*_(\d+)$/, '$1');eval('var tmp = params_' + $('article_name_' + row).getAttribute('uniqid') + ';');clearAutocompleteItems(tmp, true);" where id IN (101666, 101492);

######################################################################################
# 2015-09-09 - Added new settings for templates generation

# PRE-DEPLOYED #Added new settings for templates generation
#UPDATE  _variables_meta SET name = CONCAT(name,'_current') WHERE name IN ("invoices_first_date", "invoices_issue_date");
#UPDATE _variables_i18n SET content = CONCAT(content,' - текущ период') WHERE parent_id IN (SELECT id FROM _variables_meta WHERE name IN ("invoices_first_date_current", "invoices_issue_date_current")) AND lang = "bg";
#UPDATE _variables_i18n SET content = CONCAT(content,' - current period') WHERE parent_id IN (SELECT id FROM _variables_meta WHERE name IN ("invoices_first_date_current", "invoices_issue_date_current")) AND lang = "en";
#
#INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
#    (NULL, 'Contract', '1', 'invoices_first_date_past', NULL, NULL, 'text', '1', NULL, NULL, NULL);
#INSERT INTO _variables_i18n VALUES
#    (LAST_INSERT_ID(), "label", "Дата на първа фактура - минал период", "bg"),
#    (LAST_INSERT_ID(), "label", "First invoice date - past period", "en");
#INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
#    (NULL, 'Contract', '1', 'invoices_issue_date_past', NULL, NULL, 'text', '1', NULL, NULL, NULL);
#INSERT INTO _variables_i18n VALUES
#    (LAST_INSERT_ID(), "label", "Дата на фактуриране - минал период", "bg"),
#    (LAST_INSERT_ID(), "label", "Invoice date - past period", "en");
#INSERT INTO _variables_cstm
#    SELECT model, 0, id, "count := 1\nperiod_type := calendar\nperiod := day", NULL, NULL, NOW(), 1, NOW(), 1, ""
#    FROM _variables_meta WHERE name IN ("invoices_first_date_past", "invoices_issue_date_past");

######################################################################################
# 2015-10-27 - Added new pattern plugin for invoices/proformas preparation

# Added new pattern plugin for invoices/proformas preparation
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(77, 'Finance_Incomes_Reason', 0, 'uniken', 'prepareInvoices', '', NULL, NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(77, 'Подготовка на фактури/проформи', 'Подготвят се данни за печат на фактури и проформи.', 'bg', NOW()),
(77, 'Preparation for invoices/proforma invoices', 'Prepare data for printing of invoices and proforma invoices.', 'en', NOW());
update `patterns` set `for_printform` = 0, `plugin` = 77 where id in (23,24);
update patterns_i18n set name=replace(name, '(printform)', ''), description=replace(description, '(printform)', ''), content=replace(content, '_printform]', ']')
where parent_id in (23, 24, 25);
delete from automations where method like '%createPrintForm%';

######################################################################################
# 2015-12-07 - All templates for finished contracts are marked deleted

#All templates for finished contracts are marked deleted
UPDATE fin_invoices_templates f
JOIN contracts c
    ON f.contract_id = c.id
SET f.deleted = c.status_modified, f.deleted_by = -1
WHERE c.status = "closed" AND c.substatus = 2;

######################################################################################
# 2015-12-08 - Added new report for paid sums by clients for Uniken installation (UNIKEN)
#            - Added automations notifying of unpaid proforma invoices

# Added new report for paid sums by clients for Uniken installation (UNIKEN)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (335, 'uniken_payments_by_clients', 'customer_subcontractor_type_id := 5\r\n\r\nincluded_nomenclature_type_ids := 5, 22, 23, 24, 25, 26, 27\r\nnomenclature_subcontractor := ongoing_support_id\r\n\r\ncontract_substatus_signed := 1\r\ncontract_substatus_finished := 2\r\n\r\ncontract_total_var := total', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (335, 'Плащания от Клиенти (за подизпълнител)', NULL, NULL, 'bg'),
  (335, 'Payments by clients (by subcontractors)', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 335, 0, 1),
  ('reports', 'export_report', 335, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export_report') AND `model_type` = 335;

# Added automations notifying of unpaid proforma invoices
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Известяване за неплатени проформа фактури (50 дни)', 0, NULL, 1, 'finance', 'finance', 'crontab', '2', 'interval := 50 Day\r\nemail_template := notify_proforma_invoices\r\nnomenclature_type := 29\r\nrecipients := <EMAIL>, <EMAIL>\r\n\r\nperiod := 1 day\r\nstart_time := 07:00\r\nstart_before := 08:00', 'condition :=1', 'plugin := uniken\r\nmethod := notifyOfUnpaidInvoices', NULL, 1, 0, 1),
(NULL, 'Известяване за неплатени проформа фактури (80 дни)', 0, NULL, 1, 'finance', 'finance', 'crontab', '2', 'interval := 80 Day\r\nemail_template := notify_proforma_invoices\r\nnomenclature_type := 29\r\nrecipients := <EMAIL>, <EMAIL>\r\n\r\nperiod := 1 day\r\nstart_time := 07:00\r\nstart_before := 08:00', 'condition :=1', 'plugin := uniken\r\nmethod := notifyOfUnpaidInvoices', NULL, 2, 0, 1);
UPDATE emails SET name='notify_proforma_invoices' WHERE id=1003;
UPDATE emails_i18n SET subject=REPLACE(subject, '50', '[interval]'), body=REPLACE(body, '50', '[interval]') WHERE parent_id=1003;

######################################################################################
# 2015-12-18 - Added report for equipment by subcontractor for Uniken installation (UNIKEN)

# Added report for equipment by subcontractor for Uniken installation (UNIKEN)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(338, 'uniken_equipment_by_subcontractors', 'contract_type := 1\r\ncontract_substatus_signed := 1\r\ncontract_substatus_terminated := 2\r\ncustomer_type_subcontractor := 5\r\nnom_types := 5,22,23,24,25,26,27\r\n\r\ncon_equipment_type := free_text2\r\ncon_equipment_id := article_alternative_deliverer\r\ncon_periodicity := free_text3\r\ncon_producer_name := article_deliverer_name\r\ncon_subtotal := subtotal\r\n\r\nnom_location_name := location_name\r\nnom_ongoing_support_id := ongoing_support_id\r\nnom_ongoing_support := ongoing_support\r\nnom_production_number := production_number\r\nvar_magic_articles := magic_articles', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(338, 'Съоръжения по подизпълнители', NULL, NULL, 'bg'),
(338, 'Equipment by subcontractors', NULL, NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', 338, 0, 1),
(NULL, 'reports', '', 'export', 338, 0, 2);

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = 338;

######################################################################################
# 2015-12-21 - Added report for sending emails for debt collection

# Added report for sending emails for debt collection
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(334, 'bgs_debt_collection_email_sender', '# settings for report filters\r\nfin_documents_types := 1,2,3,101,102\r\ncustomers_types := 2,3,5\r\nupcoming_num_days := 2\r\n\r\n# created document types per company\r\ndocument_type1 := 16\r\ndocument_type2 := \r\ndocument_type3 := \r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(334, 'Писма за дължими суми', '01. Финанси', NULL, 'bg'),
(334, 'Debt collection emails', '01. Financial', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 334, 0, 1);

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` = 'generate_report' AND `model_type` = 334;

######################################################################################
# 2015-12-22 - Updated settings for 'uniken_payments_by_clients' report to contain the id of the magic article

# Updated settings for 'uniken_payments_by_clients' report to contain the id of the magic article
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'contract_total_var := total', 'magic_article_id := 2590') WHERE `type`='uniken_payments_by_clients' AND `settings` NOT LIKE '%magic_article_id%';

######################################################################################
# 2016-01-27 - Added send_to_email param to report the result of the notifyOfUnpaidInvoices (50/80) automation

# Added send_to_email param to report the result of the notifyOfUnpaidInvoices (50/80) automation
UPDATE automations SET settings=CONCAT(settings, '\n\n#report of the result of the automation to:\nsend_to_email := <EMAIL>')
WHERE method LIKE '%notifyOfUnpaidInvoices%' AND settings NOT LIKE '%send_to_email%';

#####################################################################################
# 2016-05-26 - Fixed the issue dates of some invoice templates

# Fixed the issue dates of some invoice templates
UPDATE `fin_invoices_templates` SET `issue_start`='count := 1\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := end' WHERE  `id`=2308;
UPDATE `fin_invoices_templates` SET `issue_date`='count := 1\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := end' WHERE  `id`=2308;
UPDATE `fin_invoices_templates` SET `next_issue_date`='2016-07-01' WHERE  `id`=2308;

UPDATE `fin_invoices_templates` SET `issue_start`='count := 1\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := end' WHERE  `id`=2871;
UPDATE `fin_invoices_templates` SET `issue_date`='count := 1\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := end' WHERE  `id`=2871;
UPDATE `fin_invoices_templates` SET `next_issue_date`='2016-07-01' WHERE  `id`=2871;

#####################################################################################
# 2017-01-13 - Updated settings for 'uniken_payments_by_clients' report to include the type of the included contracts and the var for percentage

# PRE-DEPLOYED # Updated settings for 'uniken_payments_by_clients' report to include the type of the included contracts and the var for percentage
#UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ncontract_type_id := 1\r\ncontract_subcontractor_percentage_var := percentage') WHERE `type`='uniken_payments_by_clients' AND `settings` NOT LIKE '%contract_subcontractor_percentage_var%';

#####################################################################################
# 2017-01-20 - Add automation for sending proforma invoices at 1-st day

# Add automation for sending proforma invoices at 1-st day
DELETE FROM automations
  WHERE module = 'finance'
    AND controller = 'incomes_reasons'
      AND start_model_type = '2'
      AND automation_type = 'crontab'
      AND conditions LIKE '%2225, 3003, 3002, 2592, 2591%'
      AND method = 'method := sendMail';
INSERT INTO automations
  SET name = 'Изпращане на проформи към 1-во число',
    module = 'finance',
    controller = 'incomes_reasons',
    start_model_type = '2',
    automation_type = 'crontab',
    `settings` = 'start_time := 01:00\r\nstart_before := 05:00\r\nemail_template := 1006\r\nattached_files_templates := 23\r\ncustomers_vars := b_customer\r\ncustomers_to_contact_persons_to := 1\r\nnot_system_email := 1',
    conditions = 'where := fir.id IN (SELECT DISTINCT(f.id) FROM fin_incomes_reasons AS f JOIN gt2_details AS g ON (f.annulled_by = 0 AND f.active = 1 AND f.type = 2 AND f.status = \'finished\' AND f.payment_status = \'unpaid\' AND g.model = \'Finance_Incomes_Reason\' AND g.model_id = f.id AND g.article_id IN (2225, 3003, 3002, 2592, 2591) AND DATE(f.issue_date) = DATE(NOW() - INTERVAL 1 MONTH)))',
    method = 'method := sendMail',
    `position` = 3,
    nums = 0;

#####################################################################################
# 2017-01-24 - Deactivate automation for sending proforma invoices

# Deactivate automation for sending proforma invoices
UPDATE automations
  SET active = 0,
    nums = 1
  WHERE module = 'finance'
    AND controller = 'incomes_reasons'
      AND start_model_type = '2'
      AND automation_type = 'crontab'
      AND conditions LIKE '%2225, 3003, 3002, 2592, 2591%'
      AND method = 'method := sendMail';

######################################################################################
# 2017-03-17 - Updated settings for 'uniken_payments_by_clients' report to contain the id of the article for received advanced payment

# Updated settings for 'uniken_payments_by_clients' report to contain the id of the article for received advanced payment
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nreceived_partial_payment := 2736') WHERE `type`='uniken_payments_by_clients' AND `settings` NOT LIKE '%received_partial_payment%';

######################################################################################
# 2018-08-02 - Added free_field1 to the invoices unique key

# Added free_field1 to the invoices unique key
UPDATE _variables_meta vm JOIN _variables_cstm vc  ON vc.model='Contract' and vm.id=vc.var_id and vm.name='invoices_unique_key'
SET vc.`value`='free_text3\narticle_name\nfree_text2\narticle_alternative_deliverer\nfree_field1';

UPDATE gt2_details g
JOIN gt2_details_i18n gi
  ON g.id=gi.parent_id
JOIN contracts co
 ON  g.model='Contract' and g.model_id=co.id and co.type=1
SET free_field1='1970-01-01'
 WHERE g.current=0 AND gi.free_text3='' AND g.free_field1='';

UPDATE fin_incomes_reasons fir
JOIN fin_reasons_relatives frr
 ON fir.id=frr.parent_id AND frr.parent_model_name="Finance_Incomes_Reason"
JOIN contracts co
 ON co.id=frr.link_to AND frr.link_to_model_name="Contract" AND co.type=1
JOIN gt2_details g
 ON g.model="Finance_Incomes_Reason" and g.model_id=fir.id
JOIN gt2_details_i18n gi
 ON g.id=gi.parent_id
SET free_field1='1970-01-01'
 WHERE g.current=0 AND gi.free_text3='' AND g.free_field1='';

UPDATE gt2_details g
JOIN gt2_details_i18n gi
  ON g.id=gi.parent_id
JOIN fin_invoices_templates fit
 ON  g.model='Finance_Invoices_Template' and g.model_id=fit.id
JOIN contracts co
 ON  fit.contract_id=co.id and co.type=1
SET free_field1='1970-01-01'
 WHERE g.current=0 AND gi.free_text3='' AND g.free_field1='';

######################################################################################
# 2019-07-05 - changed the PidToMol to validatePaymentMOL

# Changed the PidToMol to validatePaymentMOL
UPDATE automations SET settings=CONCAT(settings, 'tag_PM_id := 2'), method='plugin := uniken\r\nmethod := validatePaymentMOL', name='Валидация на "Платено от" при добавяне и редакция на ПКО и БП' WHERE id=2;

# Added automation that copis notes to mol when adding payment document
INSERT INTO `automations` VALUES
(NULL, 'Вписване на "Платено от" в "Представляващ" на контрагент при добавяне и редакция на ПКО и БП', 0, NULL, 1, 'finance', 'payments', 'action', '0', 'tag_PM_id := 2', 'condition := 1', 'plugin := uniken\nmethod := setCustomerMolFromPayment', '', 0, 0, 1);

######################################################################################
# 2020-05-18 - Update the advance invoices to contain the id of the related proform invoice

# Update the advance invoices to contain the id of the related proform invoice
UPDATE fin_balance AS fb, fin_incomes_reasons AS fir1, gt2_details AS gt2,
       fin_reasons_relatives AS frr1, contracts AS c, fin_incomes_reasons_i18n AS fir1_i18n,
       fin_reasons_relatives AS frr2,  fin_incomes_reasons AS fir2,
       gt2_details AS gt2_2, nom AS n
SET fir1_i18n.fin_field_2=fir2.id
WHERE fb.paid_to=fir1.id AND fir1.type="1" AND gt2.model="Finance_Incomes_Reason" AND gt2.model_id=fir1.id AND gt2.article_id IN ("2736") AND
      fir1_i18n.lang="bg" AND fir1_i18n.parent_id=fir1.id AND
      frr1.link_to_model_name="Contract" AND frr1.parent_id=fir1.id AND frr1.parent_model_name="Finance_Incomes_Reason" AND
      frr1.link_to=c.id AND c.annulled_by=0 AND c.deleted_by=0 AND c.subtype="contract" AND (c.substatus="1" OR c.substatus="2") AND c.type="1" AND
      frr2.link_to_model_name="Contract" AND frr2.link_to=c.id AND frr2.parent_model_name="Finance_Incomes_Reason" AND
      fir2.type="2" AND frr2.parent_id=fir2.id AND fir2.annulled_by!=0 AND DATE_ADD(fir2.annulled, INTERVAL 1 SECOND)>=fir1.added AND fir1.added>=DATE_SUB(fir2.annulled, INTERVAL 1 SECOND) AND
      gt2_2.model="Finance_Incomes_Reason" AND gt2_2.model_id=fir2.id AND gt2_2.article_id IN ("2590") AND
      n.id=gt2_2.article_alternative_deliverer AND n.active=1 AND n.deleted_by=0 AND n.type IN ("5","22","23","24","25","26","27") AND
      fb.parent_model_name="Finance_Payment" AND fb.paid_to_model_name="Finance_Incomes_Reason";

########################################################################
# 2021-01-29 - Added impulsko import payments plugin

# Added impulsko import payments plugin
INSERT IGNORE INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(20, 'uniken_payments', 'uniken_payments_guessed_tag_id := 40\r\nuniken_payments_default_currency := BGN\r\nuniken_payments_default_payment_type := BP\r\nuniken_payments_default_customer_id := 7756\r\n', 1);

INSERT IGNORE INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(20, 'Импортиране на плащания', NULL, 'bg'),
(20, 'Import of payments', NULL, 'en');

########################################################################
# 2021-08-30 - Updated the existing prepareInvoices patterns plugin
#            - Added new placeholder for the new pattern plugin prepareInvoices

# Updated the existing prepareInvoices patterns plugin
UPDATE `patterns_plugins` SET `settings`='incomes_reason_type_offer := 104' WHERE `model`='Finance_Incomes_Reason' AND `folder`='uniken' AND `method`='prepareInvoices' AND `settings` NOT LIKE '%incomes_reason_type_offer%';

# Added new placeholder for the new pattern plugin prepareInvoices
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`)
  SELECT NULL, 'offer_summary_table', 'Finance_Incomes_Reason', 'basic', 'patterns', NULL, '', 0
    WHERE NOT EXISTS (SELECT id FROM `placeholders` WHERE `varname`='offer_summary_table' AND `model`='Finance_Incomes_Reason');
SET @placeholder_id := (SELECT id FROM `placeholders` WHERE `varname`='offer_summary_table' AND `model`='Finance_Incomes_Reason');
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (@placeholder_id, 'Обобщаваща таблица за оферта', NULL, 'bg'),
  (@placeholder_id, 'Offer summary table', NULL, 'en');

######################################################################################
# 2024-08-30 - Added automations to sync nomenclatures and customers

# Added automation to sync nomenclatures and customers
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT NULL, 'Sync nomenclatures', 0, NULL, 1, 'nomenclatures', NULL, 'action', '0', 'userAgent := uniken-sync\r\nendpoint := https://euroliftcontrol.n-zoom.com/\r\n#endpoint := https://tt.n-zoom.com/euroliftcontrol/\r\nusername := unikensync\r\npassword := 2iqHfsPEuSxM7drWU+tdP9LD9GDxlNFc', 'condition := in_array(\'[action]\', array(\'add\', \'edit\'))\r\ncondition := in_array(\'[b_type]\', array(2,3,4,5,17,18,22,23,24,25,26,27,28,30))', 'method := syncNomenclatures\r\nplugin := uniken', NULL, 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%uniken%' AND `method` LIKE '%syncNomenclatures%');

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT NULL, 'Sync customers', 0, NULL, 1, 'customers', NULL, 'action', '0', 'userAgent := uniken-sync\r\nendpoint := https://euroliftcontrol.n-zoom.com/\r\n#endpoint := https://tt.n-zoom.com/euroliftcontrol/\r\nusername := unikensync\r\npassword := 2iqHfsPEuSxM7drWU+tdP9LD9GDxlNFc', 'condition := in_array(\'[action]\', array(\'add\', \'edit\'))\r\ncondition := in_array(\'[b_type]\', array(2,3,5))', 'method := syncCustomers\r\nplugin := uniken', NULL, 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%uniken%' AND `method` LIKE '%syncCustomers%');

######################################################################################
# 2024-09-13 - Added setttings to sync nomenclatures and customers

UPDATE `automations`
SET settings=CONCAT(settings, '\r\nremote_nomenclature_var_id := uniken_id\r\n\r\ndebug:=')
WHERE `method` LIKE '%uniken%' AND `method` LIKE '%syncNomenclatures%' AND settings NOT LIKE '%remote_nomenclature_var_id%';

UPDATE `automations`
SET settings=CONCAT(settings, '\r\nremote_customer_var_id := uniken_id\r\n\r\ndebug:=')
WHERE `method` LIKE '%uniken%' AND `method` LIKE '%syncCustomers%' AND settings NOT LIKE '%remote_customer_var_id%';

######################################################################################
# 2025-01-16 - Added new settings for 'uniken_equipment_by_subcontractors' report for tags groups
#            - Added new settings for 'uniken_equipment_by_subcontractors' for stops nomenclature vars

# Added new settings for 'uniken_equipment_by_subcontractors' report for tags groups
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ntags_groups := 1,2,3,4,5,6,13,14,15,16') WHERE `type`='uniken_equipment_by_subcontractors' AND `settings` NOT LIKE '%tags_groups%';

# Added new settings for 'uniken_equipment_by_subcontractors' for stops nomenclature vars
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nvar_magic_articles :=', '\r\nnom_stops := stops_name\r\nnom_address := address_name\r\nvar_magic_articles :=') WHERE `type`='uniken_equipment_by_subcontractors' AND `settings` NOT LIKE '%nom_stops%';


