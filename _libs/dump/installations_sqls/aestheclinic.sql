##########################################################################################
### SQL nZoom Specific Updates - Aeshte Clinic (http://aestheclinic.n-zoom.com/) ###
##########################################################################################

######################################################################################
# 2015-10-20 - Added new settings for 'dermavita_schedule' (AESHTECLINIC) dashlet plugins

# Added new settings for 'dermavita_schedule' (AESHTECLINIC) dashlet plugins
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\ndocument_work_leaving_regular_payment := 1') WHERE  `type`='dermavita_schedule' AND `settings` NOT LIKE '%document_work_leaving_regular_payment%';

######################################################################################
# 2015-11-01 - Added new settings for 'dermavita_work_leaving_form' (AESHTECLINIC) dashlet plugins

UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'document_work_leaving_form_tag_unpaid := 4', 'document_work_leaving_form_tag_unpaid := 4\r\ndocument_work_leaving_form_tag_prepaid := 12') WHERE `type`='dermavita_work_leaving_form' AND `settings` NOT LIKE '%document_work_leaving_form_tag_prepaid%';
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\ndocument_work_leaving_form_payment_type_regular := 1\r\ndocument_work_leaving_form_payment_type_prepaid := 2') WHERE  `type`='dermavita_work_leaving_form' AND `settings` NOT LIKE '%document_work_leaving_form_payment_type_prepaid%';

######################################################################################
# 2015-11-17 - Added new settings for 'dermavita_schedule' (AESHTECLINIC) dashlet plugins for creating financial document

# Added new settings for 'dermavita_schedule' (AESHTECLINIC) dashlet plugins for creating financial document
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\n# AESTHE CLINIC specific settings\r\ndocument_work_leaving_form_fin_type := 102\r\ndocument_work_leaving_form_fin_default_office := 1\r\ncustomer_5_company := 3_%d_cash_3\r\ncustomer_7_company := 2_%d_cash_2') WHERE  `type`='dermavita_schedule' AND `settings` NOT LIKE '%document_work_leaving_form_fin_type%';

######################################################################################
# 2015-11-19 - Added automation to equilize work leaving form document with work leaving form financial document

# Added automation to equilize work leaving form document with work leaving form financial document
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Уеднаквяване на GT2 в Обходни листи (посещение)', 0, NULL, 1, 'documents', NULL, 'action', '3', 'work_leaving_form_fin := 102\r\nwork_leaving_form_doc := 3', 'condition := ''[action]'' == ''edit''\r\ncondition := ''[request_is_post]'' == ''1''', 'plugin := aestheclinic\r\nmethod := equalizeWorkLeavingForms\r\n', '', 1, 0, 1),
('Уеднаквяване на GT2 в Обходни листи (приход)', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '102', 'work_leaving_form_fin := 102\r\nwork_leaving_form_doc := 3', 'condition := ''[action]'' == ''edit''\r\ncondition := ''[request_is_post]'' == ''1''', 'plugin := aestheclinic\r\nmethod := equalizeWorkLeavingForms\r\n', '', 1, 0, 1);

######################################################################################
# 2015-11-26 - Added work leaving form dashlet plugin for Aeshte Clinic installation (AESTHECLINIC)

# Added work leaving form dashlet plugin for Aeshte Clinic installation (AESTHECLINIC)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
  (NULL, 'aestheclinic_work_leaving_form', 'fin_document_type_visit_id := 102\r\ncustomer_clinic_type_id := 3\r\ncustomer_patient_type_id := 2\r\n\r\ncustomer_company_5 := 3\r\ncustomer_company_7 := 2\r\ncustomer_office_5 := 1\r\ncustomer_office_7 := 1\r\n\r\navailable_currencies := BGN,EUR,USD', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Обходен лист', 'Обходен лист', 'bg'),
  (LAST_INSERT_ID(), 'Work leaving form', 'Work leaving form', 'en');

######################################################################################
# 2015-11-27 - Added new report: 'aestheclinic_turnovers'

# Added new report: 'aestheclinic_turnovers'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  ('331', 'aestheclinic_turnovers', 'roles_full_rights := 1\r\n\r\nfir_sheet := 102\r\ncustomer_company := 3\r\nnom_cashbox_bank := 10\r\nnom_cosmetics := 8\r\nnom_procedure := 6\r\ndocument_sheet := 3\r\ndocument_payment := 4\r\ndocument_visit := 1\r\nprocedure_types := 6\r\n\r\ntag_system_container := 11\r\ncategories_procedures_sections := 3\r\n\r\ndocument_visit_visit_date := visit_date\r\ndocument_visit_start_hour := visit_start_hour\r\ndocument_payment_sheet_id := document_procedure_id\r\ndocument_payment_container := cash_bank_id\r\ndocument_payment_value := payment_value_parts\r\ndocument_payment_company := cosmetic_company_id\r\n\r\nnom_container_company := cosmetic_company_id\r\nnom_bank_cashbox_current_amount := account_balance\r\n\r\nsheet_tag_paid := 2\r\nsheet_tag_partial := 3\r\nsheet_tag_unpaid := 4\r\nprocedure_types_categories := 13\r\nnom_procedure_category := cat_name_id\r\n\r\nsecond_table_currency := BGN', '0');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('331', 'Обороти', 'bg'),
  ('331', 'Turnovers', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '331', '1'),
  ('reports', 'export', '331', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '331';

######################################################################################
# 2015-11-30 - Removed unnecessary settings for report 'aestheclinic_turnovers'

# Removed unnecessary settings for report 'aestheclinic_turnovers'
UPDATE reports
  SET settings = 'roles_full_rights := 1\r\n\r\nfir_sheet := 102\r\nnom_cosmetics := 8\r\nnom_procedure := 6\r\ndocument_sheet := 3\r\ndocument_visit := 1\r\nprocedure_types := 6\r\n\r\ndocument_visit_visit_date := visit_date\r\ndocument_visit_start_hour := visit_start_hour\r\n\r\nprocedure_types_categories := 13\r\nnom_procedure_category := cat_name_id\r\n\r\nsecond_table_currency := BGN'
  WHERE type = 'aestheclinic_turnovers';

######################################################################################
# 2015-12-04 - Added new report: 'aestheclinic_turnovers_employees'

# Added new report: 'aestheclinic_turnovers_employees'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  ('332', 'aestheclinic_turnovers_employees', 'roles_full_rights := 1\r\n\r\ncustomer_type_employee := 1\r\nfield_cus_employee_clinic_id := clinic_id\r\nfield_cus_employee_comm_nom_id := comm_nom_id\r\nfield_cus_employee_comm_perc_value := comm_perc_value\r\n\r\nfin_incomes_type_sheet := 102\r\nincomes_sheet_payment_statuses := paid,partial,invoiced\r\n\r\ndocument_type_sheet := 3\r\n\r\ndocument_type_visit := 1\r\nfield_doc_visit_visit_date := visit_date\r\n\r\nnomenclature_type_procedure := 6\r\nfield_nom_procedure_cat_name_id := cat_name_id\r\n\r\nnomenclature_type_cosmetics := 8\r\nnomenclature_type_category := 13\r\n\r\ncompany_2_customer := 7\r\ncompany_3_customer := 5', '0');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('332', 'Обороти на служители', 'bg'),
  ('332', 'Employees turnovers', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '332', '1'),
  ('reports', 'export', '332', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '332';

######################################################################################
# 2015-12-10 - Added new report: 'aestheclinic_turnovers_patients'

# Added new report: 'aestheclinic_turnovers_patients'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  ('336', 'aestheclinic_turnovers_patients', 'customer_type_patient := 2\r\n\r\nfin_incomes_type_sheet := 102\r\nincomes_sheet_payment_statuses := paid,partial,invoiced\r\n\r\ndocument_type_sheet := 3\r\n\r\ndocument_type_visit := 1\r\nfield_doc_visit_visit_date := visit_date\r\n\r\nnomenclature_type_procedure := 6\r\nfield_nom_procedure_cat_name_id := cat_name_id\r\n\r\nnomenclature_type_cosmetics := 8\r\nnomenclature_type_category := 13', '0');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('336', 'Обороти на пациенти', 'bg'),
  ('336', 'Patients turnovers', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '336', '1'),
  ('reports', 'export', '336', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '336';

######################################################################################
# 2015-12-23 - Added setting for price range for 'dermavita_schedule' dashlet plugin

# Added setting for price range for 'dermavita_schedule' dashlet plugin
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\ncustomer_5_company', '\r\nnom_price_range := price_range\r\ncustomer_5_company') WHERE  `type`='dermavita_schedule' AND `settings` NOT LIKE '%nom_price_range%';

######################################################################################
# 2016-04-18 - Correct the vars which contains the price range data for 'dermavita_schedule' dashlet plugin

# Correct the vars which contains the price range data for 'dermavita_schedule' dashlet plugin
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\nnom_price_range := price_range', '\r\nnom_price_range := price_range_name\r\nnom_price_range_id := price_range_id') WHERE  `type`='dermavita_schedule' AND `settings` NOT LIKE '%price_range_name%';

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status = ''opened''\r\nwhere := d.substatus = ''7''\r\nwhere := DATE_FORMAT(d.date, ''%Y-%m-%d'') >= CURDATE()' WHERE `id` = 2;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status = ''closed''\r\nwhere := d.substatus IN (8, 11)' WHERE `id` = 3;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_FORMAT(a__made_date_reminder_for_call, ''%Y-%m-%d'') = DATE_ADD(CURDATE(), INTERVAL 1 DAY)' WHERE `id` = 4;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := a__first_visit_yes = ''2''' WHERE `id` = 5;

######################################################################################
# 2016-06-02 - Added settings for 'dermavita_schedule' dashlet plugin for Dermavita deactivation of payments and for automatic completion of medic and medic id in Aesthe Clinic

# Added settings for 'dermavita_schedule' dashlet plugin for Dermavita deactivation of payments and for automatic completion of medic and medic id in Aesthe Clinic
UPDATE `dashlets_plugins` SET `settings` = CONCAT(`settings`, '\r\n\r\n# Payment document vars (Dermavita)\r\ndocument_payment_type_id :=\r\ndocument_payment_document_id :=')  WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%document_payment_type_id%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\n# Schedule validation vars', '\r\nemployee_staff_type := staff_type\r\nemployee_clinic := clinic_id\r\n\r\n# Schedule validation vars') WHERE  `type`='dermavita_schedule' AND `settings` NOT LIKE '%employee_staff_type%';

######################################################################################
# 2016-06-09 - Updated settings for 'dermavita_schedule' dashlet plugin to contain option for setting the duration of single interval in the schedule

# Updated settings for 'dermavita_schedule' dashlet plugin to contain option for setting the duration of single interval in the schedule
UPDATE `dashlets_plugins` SET `settings` = REPLACE(`settings`, 'side_panel_time_interval :=', 'main_panel_hours_interval := 60\r\nside_panel_time_interval :=')  WHERE `type`='dermavita_schedule' AND `settings` NOT LIKE '%main_panel_hours_interval%';

######################################################################################
# 2016-06-16 - Added new automation to synchronize the procedures work leaving form table to procedures data table
#            - Updated settings of the button for triggering the new automation

# Added new automation to synchronize the procedures work leaving form table to procedures data table
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Синхронизиране на таблиците за задължения и данни от преглед', 0, NULL, 1, 'documents', NULL, 'action', '3', 'procedure_nom_type := 6\r\nvisit_data_group_table_to_update := med_group\r\nvisit_data_procedure_id := procedure_name_id\r\nvisit_data_procedure_name := procedure_name\r\nvisit_data_made_by_id := employee_name_id\r\nvisit_data_made_by_name := employee_name\r\nvisit_data_params := med_parameters\r\nvisit_data_price := med_price', 'condition := ''[action]'' == ''edit'' && ''[request_is_post]'' == ''1''  && $request->get(''recalculate_visit_table'')', 'plugin := aestheclinic\r\nmethod := synchronizeVisitTables', NULL, 2, 0, 1);

# Updated settings of the button for triggering the new automation
UPDATE _fields_meta SET `source`='onclick := if (confirm(''Таблицата за задълженията по обходен лист ще бъде синхронизирана с данните за посещението.\\nСигурни ли сте, че искате да продължите?'')) {this.form.action += ''&amp;recalculate_visit_table=1''; $$(''button.button[type=\\''submit\\''][name=\\''saveButton1\\'']'')[0].click();}' WHERE `model`='Document' AND `model_type`=3 AND `name`='fillgrp_button' AND `type`='button';

######################################################################################
# 2016-08-09 - Added additional setting for 'aestheclinic_work_leaving_form' dashlet to contain the var for clinic of the employee

# Added additional setting for 'aestheclinic_work_leaving_form' dashlet to contain the var for clinic of the employee
UPDATE `dashlets_plugins` SET `settings` = CONCAT(`settings`, '\r\n\r\nemployee_clinic := clinic_id') WHERE `type`='aestheclinic_work_leaving_form' AND `settings` NOT LIKE '%employee_clinic%';

######################################################################################
# 2017-01-30 - Fix missing currency from payments to cashboxes where the user did not specify a payment currency

# Fix missing currency from payments to cashboxes where the user did not specify a payment currency
UPDATE fin_payments p, fin_payments_i18n pi18n SET pi18n.note=CONCAT(p.currency, pi18n.note) WHERE pi18n.note!="" AND pi18n.note REGEXP '^ [0-9]+\.[0-9]{2}$' AND p.id=pi18n.parent_id AND p.type='PKO' AND p.annulled_by=0 AND p.status='finished';

######################################################################################
# 2018-11-02 - Added bullet to fix the mismatch between row calculations and total calculations in the finance incomes reason (type 102) and documents (type 3)
#            - Updated automations for equalization of the documents and incomes reasons with deactivation status and additional conditions

# Added bullet to fix the mismatch between row calculations and total calculations in the finance incomes reason (type 102) and documents (type 3)
INSERT INTO `bullets` (`bullet`, `description`, `revision`, `position`, `active`, `modified`, `fired`) VALUES
('aestheFixReasonAndDocumentsMistakenGT2', 'Update the GT2 tables of the incomes reasons and documents which have difference between totals and sums of the rows.', 14832, 0, 1, NOW(), '0000-00-00 00:00:00');

# Updated automations for equalization of the documents and incomes reasons with deactivation status and additional conditions
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nrelated_record_deactivate_status := 15'), `conditions`='condition := ((\'[action]\' == \'edit\') || ((\'[action]\' == \'setstatus\' || \'[action]\' == \'multistatus\') && \'[b_substatus]\' == \'1\' && \'[prev_b_substatus]\' != \'1\'))\r\ncondition := \'[request_is_post]\' == \'1\'' WHERE `method` LIKE '%equalizeWorkLeavingForms%' AND `module`='finance' AND `settings` NOT LIKE '%related_record_deactivate_status%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nrelated_record_deactivate_status := 1'), `conditions`='condition := ((\'[action]\' == \'edit\') || ((\'[action]\' == \'setstatus\' || \'[action]\' == \'multistatus\') && \'[b_substatus]\' == \'15\' && \'[prev_b_substatus]\' != \'1\'))\r\ncondition := \'[request_is_post]\' == \'1\'' WHERE `method` LIKE '%equalizeWorkLeavingForms%' AND `module`='documents' AND `settings` NOT LIKE '%related_record_deactivate_status%';

######################################################################################
# 2018-11-28 - Update dashlets settings for aestheclinic_work_leaving_form dashlet plugin to contain visit substatus paid

# Update dashlets settings for aestheclinic_work_leaving_form dashlet plugin to contain visit substatus paid
UPDATE `dashlets_plugins` SET `settings` = CONCAT(`settings`, '\r\n\r\nvisit_substatus_paid := 14') WHERE `type`='aestheclinic_work_leaving_form' AND `settings` NOT LIKE '%visit_substatus_paid%';
