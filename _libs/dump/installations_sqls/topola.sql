###########################################################################################
### SQL nZoom Specific Updates Топола Скайс Мениджмънт ООД (https://topola.n-zoom.com/) ###
###########################################################################################

########################################################################
# 2023-05-16 - Added new report 'topola_transfer_schedule'

# Added new report 'topola_transfer_schedule'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (452, 'topola_transfer_schedule', 'doc_schedule_type := 8\r\ndoc_schedule_var_direction := transport_direction\r\ndoc_schedule_var_direction_forward := 1\r\ndoc_schedule_var_direction_backward := 2\r\ndoc_schedule_var_start_time := transport_start_time\r\ndoc_schedule_var_vehicle_name := auto_name\r\ndoc_schedule_var_vehicle := auto_id\r\ndoc_schedule_var_capacity := auto_capacity\r\ndoc_schedule_var_driver_name := driver_name\r\ndoc_schedule_var_driver := driver_id\r\ndoc_schedule_var_city_name := city_name\r\ndoc_schedule_var_city := city_id\r\ndoc_schedule_var_stop_name := stop_name\r\ndoc_schedule_var_stop := stop_id\r\ndoc_schedule_var_employee_name := check_employee_name\r\ndoc_schedule_var_employee := check_employee_id\r\n\r\nnom_stop_type := 13\r\nnom_stop_var_position := route_num\r\nnom_stop_var_city := station_city\r\n\r\nnom_cities_type := 14\r\nnom_cities_var_position := route_num\r\n\r\nnom_vehicles_type := 16\r\nnom_vehicles_var_capacity := auto_capacity\r\n\r\nnom_drivers_type := 17\r\n\r\nnom_hours_type := 24\r\n\r\ncustomer_employee_type := 1\r\ncustomer_employee_var_station := station_id', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (452, 'График служебен транспорт', NULL, NULL, 'bg'),
  (452, 'Schedule transport', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '452', '0', '1'),
  ('reports', '', 'export',          '452', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '452';

########################################################################
# 2023-05-25 - Added new report 'topola_checklist_analysis'

# Added new report 'topola_checklist_analysis'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (453, 'topola_checklist_analysis', 'doc_type_checklist := 1\r\ndoc_var_checklist_complex := complex_id\r\ndoc_var_checklist_object := object_id\r\ndoc_var_checklist_verification := verification_id\r\ndoc_var_checklist_activity := activity_verification_id\r\ndoc_var_checklist_activity_priority := activity_priority\r\ndoc_var_checklist_activity_check := verification_checklist\r\ndoc_var_checklist_user := user_finish_id\r\ndoc_var_checklist_start_time := datetime_start\r\ndoc_var_checklist_end_time := datetime_finish\r\ndoc_var_checklist_calc_start_time := event_start\r\n\r\ndoc_type_task := 3\r\ndoc_var_task_complex := complex_id\r\ndoc_var_task_object := object_id\r\ndoc_var_task_verification := verification_id\r\ndoc_var_task_activity := activity_verification_id\r\ndoc_var_task_activity_priority := activity_priority\r\ndoc_var_task_user := user_finish_id\r\ndoc_var_task_start_time := datetime_problem_add\r\ndoc_var_task_end_time := datetime_finish\r\ndoc_var_task_checklist := checklist_id\r\n\r\ndoc_var_status_checked := closed_7\r\n\r\nnom_complex_type := 5\r\n\r\nnom_object_type := 6\r\n\r\nnom_verification_type := 9\r\n\r\nnom_activity_type := 10', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (453, 'Анализ на чек листове и задачи', NULL, NULL, 'bg'),
  (453, 'Checklists and tasks analysis', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '453', '0', '1'),
  ('reports', '', 'export',          '453', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '453';

########################################################################
# 2023-07-05 - Added freeze headers setting in 'topola_transfer_schedule' report

# Added freeze headers setting in 'topola_transfer_schedule' report
UPDATE `reports` SET `settings` = CONCAT('freeze_table_headers := 1\r\n\r\n', `settings`)
WHERE `type` = 'topola_transfer_schedule' AND `settings` NOT LIKE '%freeze_table_headers%';
