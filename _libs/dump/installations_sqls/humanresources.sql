#####################################################################################
### SQL nZoom Specific Updates HUMANRESOURCES (http://humanresources.n-zoom.com/) ###
#####################################################################################

######################################################################################
# 2016-02-18 - Added report 'inventory' to HUMANRESOURCES (Bug 4298)

# PRE-DEPLOYED # Added report 'inventory' to HUMANRESOURCES (Bug 4298)
#INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
#  ('318', 'inventory', 'doc_type_employee_inventory_card := \r\nfield_doc_card_inventory_description_id := inventory_description_id\r\nfield_doc_card_date_received := date_received\r\nfield_doc_card_date_return := date_return\r\n\r\ncus_type_employee := 1\r\nfield_cus_empl_work_place_id := work_place_id\r\nfield_cus_empl_work_place := work_place\r\n\r\nnom_type_inventory := \r\nfield_nom_inv_build_id := build_id\r\nfield_nom_inv_build_name := build_name\r\nfield_nom_inv_room_id := room_id\r\nfield_nom_inv_room_name := room_name\r\nfield_nom_category_inventory_id := category_inventory_id\r\nfield_nom_category_inventory := category_inventory\r\nfield_nom_subcategory_inventory_id := subcategory_inventory_id\r\nfield_nom_subcategory_inventory := subcategory_inventory\r\n\r\nnom_type_building := \r\nnom_type_room := \r\nnom_type_working_place := \r\nnom_type_category_inventory := \r\nnom_type_subcategory_inventory := \r\n', '0');
#INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
#  ('318', 'Инвентар', 'bg'),
#  ('318', 'Inventory', 'en');
#INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
#  ('reports', 'generate_report', '318', '1');
#INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#  SELECT '1', `id`, 'all'
#  FROM `roles_definitions`
#    WHERE `module`     = 'reports'
#      AND `action`     = 'generate_report'
#      AND `model_type` = '318';

######################################################################################
# 2014-02-25 - Added automation for contract's events' management to HUMANRESOURCES (Bug 4075)

# PRE-DEPLOYED # Added automation for contract's events' management to HUMANRESOURCES (Bug 4075)
#INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#(NULL, 'Напомняне към договор', 0, NULL, 1, 'documents', NULL, 'before_action', '41', 'users_observers := 19\nsubstatus_active := closed_107\nevent_type := 5', 'condition := 1', 'plugin := arte_doc\nmethod := manageContractEvents', 'cancel_action_on_fail := 1', 0, 0, 1),
#(NULL, 'Напомняне по договор за период', 0, NULL, 1, 'documents', NULL, 'crontab', '0', 'model_types := 41\nusers_observers := 19\nsubstatus_active := closed_107\nevent_type := 5\nstart_month_date := 01, 16\nstart_time := 01:00\nstart_before := 03:00', 'condition := 1', 'plugin := arte_doc\nmethod := manageContractEvents', NULL, 0, 0, 1);
