#############################################################################################
### SQL nZoom Specific Updates Недвижими имоти (http://realestate.demo.n-zoom.com/) ###
#############################################################################################

######################################################################################
# 2009-11-25 - Added new report - 'estates_partial_share' - for Real Estate installation (1732)

# Added new report - 'estates_partial_share' - for Real Estate installation (1732)
INSERT INTO `reports` (`id`, `type`, `visible`) VALUES 
('104', 'estates_partial_share', '0');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES 
('104', 'Дялово разпределение', NULL , 'bg'), 
('104', 'Partial share', NULL , 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'generate_report', id, 0, 1 FROM `reports` WHERE id="104";
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'export', id, 0, 2 FROM `reports` WHERE id="104";
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=104;

######################################################################################
# 2016-12-07 - Fixed the possible division by zero in the equations of variables

# Fixed the possible division by zero in the equations of variables
UPDATE _fields_meta SET source=REPLACE(source, 'equation := $a/$b', 'equation := ($b != 0) ? $a/$b : 0') WHERE source like '%equation := $a/$b%';
