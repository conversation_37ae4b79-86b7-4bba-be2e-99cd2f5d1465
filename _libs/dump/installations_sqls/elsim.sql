#####################################################################################
### SQL nZoom Specific Updates Елсим Комерс ООД (https://elsim.n-zoom.com/) ###
#####################################################################################

#####################################################################################
# 2017-11-19 - Updated settings of GT2 of "Sale" in order to use alternate unit of measure

# Updated settings of GT2 of "Sale" in order to use alternate unit of measure
START TRANSACTION;

SET @model = 'Finance_Incomes_Reason';
SET @model_type = 103;

SET @name = 'group_table_2';
SET @source = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\ntotals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_surplus_field, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\n\n# functions for AUoM calculations\njavascript := function calcAQ(el, acf) { el = $(el); var prec = env.precision.gt2_quantity; roundAF(el, prec); var qf = el.up(\'tr\').down(\'.quantity\'); acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; qf.value = (parseFloat(el.value) || 0) * acf.value; gt2calc(qf); } function calcAP(el, acf) { el = $(el); var prec = env.precision.gt2_rows; roundAF(el, prec); var pf = el.up(\'tr\').down(\'.price\'); acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; pf.value = (parseFloat(el.value) || 0) / acf.value; gt2calc(pf); } function roundAF(el, prec) { if (el.value.match(/^[^\\.]*\\./)) { var cnt = el.value.replace(/^[^\\.]*\\./, \'\'); if (cnt.length > prec) { var pow = Math.pow(10, prec); el.value = Math.round((parseFloat(el.value) || 0) * pow) / pow; } } } function formatAF(el, prec) { el.value = (parseFloat(el.value) || 0).toFixed(prec); }\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'article_name';
SET @source = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nautocomplete := nomenclatures\nautocomplete_search := <code>, <name>\nautocomplete_suggestions := [<code>] <name>\nautocomplete_fill_options := $article_id => <id>\nautocomplete_fill_options := $article_code => <code>\nautocomplete_fill_options := $article_name => <name>\nautocomplete_fill_options := $article_measure_name => <a__measure_name>\nautocomplete_fill_options := $free_field1 => <a__measure_name>\nautocomplete_fill_options := $free_text1 => <a__measure_name>\nautocomplete_fill_options := $free_field2 => <sell_price>\nautocomplete_fill_options := $free_field3 => 1\nautocomplete_fill_options := $free_field4 => 1.00\nautocomplete_fill_options := $quantity => 1\nautocomplete_fill_options := $price => <sell_price>\nautocomplete_fill_options := $average_weighted_delivery_price => <average_weighted_delivery_price>\nautocomplete_fill_options := $last_delivery_price => <last_delivery_price>\nautocomplete_currency := $currency\nautocomplete_filter := <type> => 14\nautocomplete_clear := 1\nautocomplete_add := 1\nautocomplete_combobox := 1\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'free_text1';
SET @source = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nautocomplete := autocompleters\nautocomplete_plugin_search := customQuery\nautocomplete_plugin_param_product := $article_id\nautocomplete_plugin_param_sql := SELECT id, name, alt_sell_price, alt_quantity, is_main, ROUND(IF(alt_quantity = 0, 0, alt_sell_price/alt_quantity), 2) AS price FROM (SELECT nm.id, nm.name, CONVERT(nc2.value, DECIMAL(11, 2)) AS alt_sell_price, CONVERT(nc3.value, DECIMAL(11, 2)) AS alt_quantity, 0 AS is_main FROM `nom_cstm` AS nc JOIN `_fields_meta` fm ON nc.model_id = <product> AND nc.var_id = fm.id AND fm.model = \'Nomenclature\' AND fm.name IN (\'alt_measure_name\') JOIN nom_measures AS nm ON nc.value = nm.id AND nm.lang = \'[model_lang]\' JOIN `nom_cstm` AS nc2 ON nc.model_id = nc2.model_id AND nc.num = nc2.num JOIN `_fields_meta` fm2 ON nc2.var_id = fm2.id AND fm2.model = \'Nomenclature\' AND fm2.name IN (\'alt_sell_price\') JOIN `nom_cstm` AS nc3 ON nc.model_id = nc3.model_id AND nc.num = nc3.num JOIN `_fields_meta` fm3 ON nc3.var_id = fm3.id AND fm3.model = \'Nomenclature\' AND fm3.name IN (\'alt_quantity\') JOIN `nom_cstm` AS nc4 ON nc.model_id = nc4.model_id AND nc4.num = 1 AND nc4.value != nc.value JOIN `_fields_meta` fm4 ON nc4.var_id = fm4.id AND fm4.model = \'Nomenclature\' AND fm4.name IN (\'measure_name\') WHERE nm.name LIKE \'%<search_string_parts>%\' UNION SELECT nm.id, nm.name, CONVERT(n.sell_price, DECIMAL(11,2)) AS alt_sell_price, 1 AS alt_quantity, 1 AS is_main FROM `nom_cstm` AS nc JOIN `_fields_meta` fm ON nc.var_id = fm.id AND fm.model = \'Nomenclature\' AND fm.name IN (\'measure_name\') JOIN nom_measures AS nm ON nc.value = nm.id AND nm.lang = \'[model_lang]\' JOIN `nom` n ON n.id = nc.model_id AND n.id = <product> WHERE nm.name LIKE \'%<search_string_parts>%\') AS t GROUP BY id ORDER BY is_main DESC, name ASC, id ASC\nautocomplete_fill_options := $free_field1 => <id>\nautocomplete_fill_options := $free_text1 => <name>\nautocomplete_fill_options := $free_field2 => <alt_sell_price>\nautocomplete_fill_options := $free_field3 => <alt_quantity>\nautocomplete_fill_options := $price => <price>\nautocomplete_suggestions := <name>\nautocomplete_clear := 1\nautocomplete_combobox := 1\nautocomplete_combobox_mode := empty\nautocomplete_execute_after := $(\'free_field4_\' + data.row).onkeyup();\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'free_field1';
SET @source = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nmethod := getMeasures\n';
UPDATE `_fields_meta`
SET `type` = 'dropdown',
    `hidden` = 1,
    `readonly` = 1,
    `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @validate = 'js_filter := insertOnlyFloats';

SET @name = 'free_field2';
SET @source = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => calcAP(this, \'free_field3\')\njs_method := onblur => formatAF(this, env.precision.gt2_rows)\n';
UPDATE `_fields_meta`
SET `source` = @source,
    `validate` = @validate
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'free_field3';
UPDATE `_fields_meta`
SET `validate` = @validate
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'free_field4';
SET @source = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => calcAQ(this, \'free_field3\')\njs_method := onblur => formatAF(this, env.precision.gt2_quantity)\n';
UPDATE `_fields_meta`
SET `source` = @source,
    `validate` = @validate
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

COMMIT;

#####################################################################################
# 2017-11-27 - Updated settings of GT2 of "Sale" in order to use alternate unit of measure

# Updated settings of GT2 of "Sale" in order to use alternate unit of measure
START TRANSACTION;

SET @model = 'Finance_Incomes_Reason';
SET @model_type = 103;

SET @name = 'group_table_2';
SET @source = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\ntotals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_surplus_field, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\n\n# functions for AUoM calculations\njavascript := function calcAQ(el, acf) { el = $(el); var prec = env.precision.gt2_quantity; roundAF(el, prec); var qf = el.up(\'tr\').down(\'.quantity\'), acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; qf.value = (parseFloat(el.value) || 0) * acf.value; gt2calc(qf); } function calcAP(el, acf) { el = $(el); var prec = env.precision.gt2_rows; roundAF(el, prec); var pf = el.up(\'tr\').down(\'.price\'), acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; pf.value = (parseFloat(el.value) || 0) / acf.value; gt2calc(pf); } function roundAF(el, prec) { if (el.value.match(/^[^\\.]*\\./)) { var cnt = el.value.replace(/^[^\\.]*\\./, \'\'); if (cnt.length > prec) { var pow = Math.pow(10, prec); el.value = Math.round((parseFloat(el.value) || 0) * pow) / pow; } } } function formatAF(el, prec) { el.value = (parseFloat(el.value) || 0).toFixed(prec); } function setAP(el, apf, acf) { var apf = el.up(\'tr\').down(\'.\' + apf), acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; apf.value = (parseFloat(el.value) || 0) * acf.value; gt2calc(apf); }\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'article_name';
SET @source = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nautocomplete := nomenclatures\nautocomplete_search := <code>, <name>\nautocomplete_suggestions := [<code>] <name>\nautocomplete_fill_options := $article_id => <id>\nautocomplete_fill_options := $article_code => <code>\nautocomplete_fill_options := $article_name => <name>\nautocomplete_fill_options := $article_measure_name => <a__measure_name>\nautocomplete_fill_options := $free_field1 => <a__measure_name>\nautocomplete_fill_options := $free_text1 => <a__measure_name>\nautocomplete_fill_options := $article_height => <sell_price>\nautocomplete_fill_options := $article_weight => 1\nautocomplete_fill_options := $article_width => 1.00\nautocomplete_fill_options := $quantity => 1\nautocomplete_fill_options := $price => <sell_price>\nautocomplete_fill_options := $average_weighted_delivery_price => <average_weighted_delivery_price>\nautocomplete_fill_options := $last_delivery_price => <last_delivery_price>\nautocomplete_currency := $currency\nautocomplete_filter := <type> => 14\nautocomplete_clear := 1\nautocomplete_add := 1\nautocomplete_combobox := 1\n\nautocomplete_plugin := auom\nautocomplete_on_select := preparePrice\nautocomplete_plugin_param_currency := $currency\nautocomplete_plugin_param_sell_price_field := price\nautocomplete_plugin_param_alt_sell_price_field := article_height\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'price';
SET @source = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => gt2calc(this)\njs_method := onchange => setAP(this, \'article_height\', \'article_weight\')\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'article_height';
SET @source = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => gt2calc(this); calcAP(this, \'article_weight\');\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'article_width';
SET @source = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => gt2calc(this); calcAQ(this, \'article_weight\');\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'free_text1';
SET @source = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nautocomplete := autocompleters\nautocomplete_plugin_search := customQuery\nautocomplete_plugin_param_product := $article_id\nautocomplete_plugin_param_sql := SELECT id, name, alt_sell_price, alt_quantity, is_main, ROUND(IF(alt_quantity = 0, 0, alt_sell_price/alt_quantity), 2) AS price FROM (SELECT nm.id, nm.name, CONVERT(nc2.value, DECIMAL(11, 2)) AS alt_sell_price, CONVERT(nc3.value, DECIMAL(11, 2)) AS alt_quantity, 0 AS is_main FROM `nom_cstm` AS nc JOIN `_fields_meta` fm ON nc.model_id = <product> AND nc.var_id = fm.id AND fm.model = \'Nomenclature\' AND fm.name IN (\'alt_measure_name\') JOIN nom_measures AS nm ON nc.value = nm.id AND nm.lang = \'[model_lang]\' JOIN `nom_cstm` AS nc2 ON nc.model_id = nc2.model_id AND nc.num = nc2.num JOIN `_fields_meta` fm2 ON nc2.var_id = fm2.id AND fm2.model = \'Nomenclature\' AND fm2.name IN (\'alt_sell_price\') JOIN `nom_cstm` AS nc3 ON nc.model_id = nc3.model_id AND nc.num = nc3.num JOIN `_fields_meta` fm3 ON nc3.var_id = fm3.id AND fm3.model = \'Nomenclature\' AND fm3.name IN (\'alt_quantity\') JOIN `nom_cstm` AS nc4 ON nc.model_id = nc4.model_id AND nc4.num = 1 AND nc4.value != nc.value JOIN `_fields_meta` fm4 ON nc4.var_id = fm4.id AND fm4.model = \'Nomenclature\' AND fm4.name IN (\'measure_name\') WHERE nm.name LIKE \'%<search_string_parts>%\' UNION SELECT nm.id, nm.name, CONVERT(n.sell_price, DECIMAL(11,2)) AS alt_sell_price, 1 AS alt_quantity, 1 AS is_main FROM `nom_cstm` AS nc JOIN `_fields_meta` fm ON nc.var_id = fm.id AND fm.model = \'Nomenclature\' AND fm.name IN (\'measure_name\') JOIN nom_measures AS nm ON nc.value = nm.id AND nm.lang = \'[model_lang]\' JOIN `nom` n ON n.id = nc.model_id AND n.id = <product> WHERE nm.name LIKE \'%<search_string_parts>%\') AS t GROUP BY id ORDER BY is_main DESC, name ASC, id ASC\nautocomplete_fill_options := $free_field1 => <id>\nautocomplete_fill_options := $free_text1 => <name>\nautocomplete_fill_options := $article_height => <alt_sell_price>\nautocomplete_fill_options := $article_weight => <alt_quantity>\nautocomplete_fill_options := $price => <price>\nautocomplete_suggestions := <name>\nautocomplete_clear := 1\nautocomplete_combobox := 1\nautocomplete_combobox_mode := empty\nautocomplete_execute_after := $(\'article_width_\' + data.row).onkeyup();\n\nautocomplete_plugin := auom\nautocomplete_on_select := preparePrice\nautocomplete_plugin_param_currency := $currency\nautocomplete_plugin_param_sell_price_field := price\nautocomplete_plugin_param_alt_sell_price_field := article_height\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'free_field2';
SET @source = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

SET @name = 'free_field4';
SET @source = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\n';
UPDATE `_fields_meta`
SET `source` = @source
WHERE `model` = @model AND `model_type` = @model_type AND `name` = @name;

COMMIT;

#####################################################################################
# 2019-02-18 - New plugin for route maps

# New plugin for route maps
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`)
VALUES  (86, 'Document', 4, 'elsim', 'prepareRouteMap', '', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`)
VALUES  (86, 'Подготовка на маршрутна карта', '', 'bg', NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`)
VALUES  (86, 'Prepare route map', '', 'en', NOW());

#####################################################################################
# 2019-02-20 - Added print plugin for Label documents

# Added print plugin for Label documents
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(87, 'Document', 17, 'elsim', 'prepareLabels', '', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(87, 'Подготовка за печат на етикети', '', 'bg', NOW()),
(87, 'Prepare labels for printing', '', 'en', NOW());

#####################################################################################
# 2019-02-20 - Placeholders for the new plugin for route maps

# Placeholders for the new plugin for route maps
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`)
VALUES ('plugin_product_order_full_num', 'Document', 'basic', 'patterns', ',13,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`)
VALUES (LAST_INSERT_ID(), 'Поръчка номер', '', 'bg');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`)
VALUES ('operation_tables', 'Document', 'basic', 'patterns', ',13,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`)
VALUES (LAST_INSERT_ID(), 'Таблици с операции', NULL, 'bg');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`)
VALUES ('plugin_product_order_date', 'Document', 'basic', 'patterns', ',13,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`)
VALUES (LAST_INSERT_ID(), 'Срок за изпълнение', NULL, 'bg');

INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`)
VALUES ('plugin_pkk_document', 'Document', 'basic', 'patterns', ',13,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`)
VALUES (LAST_INSERT_ID(), 'ППК №', NULL, 'bg');

#####################################################################################
# 2019-03-14 - Updated settings of GT2 of "Incoming invoice" in order to use alternate unit of measure

# Updated settings of GT2 of "Incoming invoice" in order to use alternate unit of measure
UPDATE `_fields_meta`
SET `source` = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\ntotals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_surplus_field, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\n\n# functions for AUoM calculations\njavascript := function calcAQ(el, acf) { el = $(el); var prec = env.precision.gt2_quantity; roundAF(el, prec); var qf = el.up(\'tr\').down(\'.quantity\'), acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; qf.value = (parseFloat(el.value) || 0) * acf.value; gt2calc(qf); } function calcAP(el, acf) { el = $(el); var prec = env.precision.gt2_rows; roundAF(el, prec); var pf = el.up(\'tr\').down(\'.price\'), acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; pf.value = (parseFloat(el.value) || 0) / acf.value; gt2calc(pf); } function roundAF(el, prec) { if (el.value.match(/^[^\\.]*\\./)) { var cnt = el.value.replace(/^[^\\.]*\\./, \'\'); if (cnt.length > prec) { var pow = Math.pow(10, prec); el.value = Math.round((parseFloat(el.value) || 0) * pow) / pow; } } } function formatAF(el, prec) { el.value = (parseFloat(el.value) || 0).toFixed(prec); } function setAP(el, apf, acf) { var apf = el.up(\'tr\').down(\'.\' + apf), acf = el.up(\'tr\').down(\'.\' + acf); acf.value = parseFloat(acf.value) || 1; apf.value = (parseFloat(el.value) || 0) * acf.value; gt2calc(apf); } function calcAAP(el, asf, apf, aqf) { el = $(el); var prec = env.precision.gt2_rows, asf = el.up(\'tr\').down(\'.\' + asf), apf = el.up(\'tr\').down(\'.\' + apf), aqv = parseFloat(el.up(\'tr\').down(\'.\' + aqf).value) || 0; roundAF(asf, prec); apf.value = aqv != 0 ? ((parseFloat(asf.value) || 0) / aqv).toFixed(prec) : 0; apf.onkeyup(); } function setAS(el, asf, apf, aqf) { var prec = env.precision.gt2_rows, apv = parseFloat(el.up(\'tr\').down(\'.\' + apf).value) || 0, aqv = parseFloat(el.up(\'tr\').down(\'.\' + aqf).value) || 0, as = el.up(\'tr\').down(\'.\' + asf); as.value = (apv * aqv).toFixed(prec); calcAAP(as, asf, apf, aqf); }\n'
WHERE `model` = 'Finance_Expenses_Reason' AND `model_type` = '20' AND `name` = 'group_table_2';

UPDATE `_fields_meta`
SET `source` = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => gt2calc(this)\njs_method := onchange => setAP(this, \'article_height\', \'article_weight\'); setAS(this, \'article_volume\', \'article_height\', \'article_width\');\n'
WHERE `model` = 'Finance_Expenses_Reason' AND `model_type` = '20' AND `name` = 'price';

UPDATE `_fields_meta`
SET `source` = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => gt2calc(this); calcAAP(this, \'article_volume\', \'article_height\', \'article_width\'); calcAQ(this, \'article_weight\');\n'
WHERE `model` = 'Finance_Expenses_Reason' AND `model_type` = '20' AND `name` = 'article_width';

UPDATE `_fields_meta`
SET `source` = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => gt2calc(this); calcAAP(this, \'article_volume\', \'article_height\', \'article_width\');\n'
WHERE `model` = 'Finance_Expenses_Reason' AND `model_type` = '20' AND `name` = 'article_volume';

UPDATE `_fields_meta`
SET `source` = REPLACE(`source`, 'autocomplete_clear := 1', 'autocomplete_execute_after := $(\'article_width_\' + data.row).onkeyup();\nautocomplete_clear := 1')
WHERE `model` = 'Finance_Expenses_Reason' AND `model_type` = '20' AND `name` = 'article_name' AND `source` NOT LIKE '%autocomplete_execute_after%';
