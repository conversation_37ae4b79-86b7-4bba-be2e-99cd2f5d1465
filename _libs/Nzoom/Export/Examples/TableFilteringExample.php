<?php

namespace Nzoom\Export\Examples;

use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;
use Nzoom\Export\DataFactory;

/**
 * Example demonstrating table filtering functionality
 * 
 * This example shows how the ModelTableProvider automatically filters out
 * tables that have only 1 row containing only empty and zero values.
 */
class TableFilteringExample
{
    /**
     * Example 1: Model with mixed tables - some empty, some with data
     */
    public static function mixedTablesExample(\Registry $registry, \Outlook $outlook)
    {
        // Create a mock model with various table scenarios
        $varsForTemplate = [
            'empty_single_row' => [
                'type' => 'grouping',
                'names' => ['item', 'price', 'active'],
                'labels' => ['Item', 'Price', 'Active'],
                'hidden' => [],
                'values' => [
                    ['', 0, false] // Single row with only empty/zero values - WILL BE FILTERED OUT
                ]
            ],
            'non_empty_single_row' => [
                'type' => 'grouping',
                'names' => ['product', 'cost'],
                'labels' => ['Product', 'Cost'],
                'hidden' => [],
                'values' => [
                    ['Laptop', 999.99] // Single row with real data - WILL BE KEPT
                ]
            ],
            'multiple_empty_rows' => [
                'type' => 'grouping',
                'names' => ['category', 'count'],
                'labels' => ['Category', 'Count'],
                'hidden' => [],
                'values' => [
                    ['', 0],  // Multiple rows, even if empty - WILL BE KEPT
                    ['', 0]
                ]
            ],
            'mixed_content_single_row' => [
                'type' => 'grouping',
                'names' => ['name', 'value', 'enabled'],
                'labels' => ['Name', 'Value', 'Enabled'],
                'hidden' => [],
                'values' => [
                    ['Test', 0, false] // Single row with mixed content - WILL BE KEPT
                ]
            ]
        ];

        $model = new \Model(['id' => 123], [], [], $varsForTemplate);
        $models = [$model];

        $dataFactory = new \Nzoom\Export\DataFactory($registry);
        $dataFactory->withModelTableProvider('full_num', 'Document Number', [
            'include_empty_tables' => false // This is the default
        ]);

        $exportData = $dataFactory($models, $outlook);

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('filtered_tables_example.xlsx', 'xlsx', $exportData);

        echo "Export completed. Tables included:\n";
        foreach ($exportData as $record) {
            if ($record->hasTables()) {
                $tableCollection = $record->getTableCollection();
                foreach ($tableCollection->getTables() as $table) {
                    echo "- {$table->getTableName()} ({$table->count()} rows)\n";
                }
            }
        }
        
        echo "\nNote: 'empty_single_row' table was filtered out because it had only 1 row with empty/zero values.\n";
    }

    /**
     * Example 2: Demonstrating what values are considered empty/zero
     */
    public static function emptyValueTypesExample(\Registry $registry, \Outlook $outlook)
    {
        // Values that are considered empty/zero and will cause filtering:
        $emptyValues = [
            null,      // null values
            '',        // empty strings
            0,         // integer zero
            0.0,       // float zero
            false      // boolean false
        ];

        // Values that are NOT considered empty and will prevent filtering:
        $nonEmptyValues = [
            'test',    // non-empty string
            1,         // non-zero integer
            1.5,       // non-zero float
            true,      // boolean true
            [],        // empty array (not considered empty for filtering)
            '0'        // string "0" (not considered empty for filtering)
        ];

        $varsForTemplate = [
            'will_be_filtered' => [
                'type' => 'grouping',
                'names' => ['field1', 'field2', 'field3', 'field4', 'field5'],
                'labels' => ['Field 1', 'Field 2', 'Field 3', 'Field 4', 'Field 5'],
                'hidden' => [],
                'values' => [
                    $emptyValues // Single row with all empty values - WILL BE FILTERED OUT
                ]
            ],
            'will_not_be_filtered' => [
                'type' => 'grouping',
                'names' => ['field1', 'field2'],
                'labels' => ['Field 1', 'Field 2'],
                'hidden' => [],
                'values' => [
                    ['test', 0] // Single row with at least one non-empty value - WILL BE KEPT
                ]
            ]
        ];

        $model = new \Model(['id' => 456], [], [], $varsForTemplate);
        $models = [$model];

        $dataFactory = new \Nzoom\Export\DataFactory($registry);
        $dataFactory->withModelTableProvider();

        $exportData = $dataFactory($models, $outlook);

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('empty_values_example.xlsx', 'xlsx', $exportData);

        echo "Export completed. Only 'will_not_be_filtered' table should be included.\n";
    }

    /**
     * Example 3: Disabling filtering by using include_empty_tables option
     */
    public static function disableFilteringExample(\Registry $registry, \Outlook $outlook)
    {
        $varsForTemplate = [
            'empty_table' => [
                'type' => 'grouping',
                'names' => ['item', 'price'],
                'labels' => ['Item', 'Price'],
                'hidden' => [],
                'values' => [
                    ['', 0] // Single empty row
                ]
            ]
        ];

        $model = new \Model(['id' => 789], [], [], $varsForTemplate);
        $models = [$model];

        $dataFactory = new \Nzoom\Export\DataFactory($registry);
        
        // With include_empty_tables = true, filtering is bypassed
        $dataFactory->withModelTableProvider([
            'include_empty_tables' => true
        ]);

        $exportData = $dataFactory($models, $outlook);

        $adapter = new ExcelExportFormatAdapter($registry, 'customers', 'export');
        $adapter->export('no_filtering_example.xlsx', 'xlsx', $exportData);

        echo "Export completed with include_empty_tables=true. Empty table should be included.\n";
    }
}
