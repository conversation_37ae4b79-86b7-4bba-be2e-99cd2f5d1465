<?php

namespace Nzoom\Export\Entity;

/**
 * Class ExportColumn
 *
 * Represents a column in an export header
 */
class ExportColumn
{
    /**
     * @var string The variable name
     */
    private $varName;

    /**
     * @var string The display label
     */
    private $label;

    /**
     * @var string The data type
     */
    private $type;

    /**
     * @var string The format specification
     */
    private $format;

    /**
     * @var int|null The width of the column (for visual formatting)
     */
    private $width;

    /**
     * @var array Additional styling properties
     */
    private $styles = [];

    /**
     * ExportColumn constructor
     *
     * @param string $varName The variable name
     * @param string $label The display label
     * @param string $type The data type
     * @param string $format The format specification
     * @param int|null $width The width of the column
     * @param array $styles Additional styling properties
     * @throws \InvalidArgumentException If the type is invalid
     */
    public function __construct(
        string $varName,
        string $label,
        string $type = ExportValue::TYPE_STRING,
        string $format = '',
        ?int $width = null,
        array $styles = []
    ) {
        $this->setVarName($varName);
        $this->setLabel($label);
        $this->setType($type);
        $this->setFormat($format);
        $this->setWidth($width);
        $this->setStyles($styles);
    }

    /**
     * Get the variable name
     *
     * @return string The variable name
     */
    public function getVarName(): string
    {
        return $this->varName;
    }

    /**
     * Set the variable name
     *
     * @param string $varName The variable name
     * @return $this
     * @throws \InvalidArgumentException If the variable name is empty
     */
    public function setVarName(string $varName)
    {
        if (empty($varName)) {
            throw new \InvalidArgumentException('Variable name cannot be empty');
        }

        $this->varName = $varName;
    }

    /**
     * Get the display label
     *
     * @return string The display label
     */
    public function getLabel(): string
    {
        return $this->label;
    }

    /**
     * Set the display label
     *
     * @param string $label The display label
     */
    public function setLabel(string $label)
    {
        $this->label = $label;
    }

    /**
     * Get the data type
     *
     * @return string The data type
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Set the data type
     *
     * @param string $type The data type
     * @throws \InvalidArgumentException If the type is invalid
     */
    public function setType(string $type)
    {
        // Validate the type against the valid types in ExportValue
        if (!in_array($type, ExportValue::getValidTypes())) {
            throw new \InvalidArgumentException(sprintf(
                'Invalid type "%s". Valid types are: %s',
                $type,
                implode(', ', ExportValue::getValidTypes())
            ));
        }

        $this->type = $type;
    }

    /**
     * Get the format specification
     *
     * @return string The format specification
     */
    public function getFormat(): string
    {
        return $this->format;
    }

    /**
     * Set the format specification
     *
     * @param string $format The format specification
     * @return $this
     */
    public function setFormat(string $format)
    {
        $this->format = $format;
    }

    /**
     * Get the width of the column
     *
     * @return int|null The width
     */
    public function getWidth(): ?int
    {
        return $this->width;
    }

    /**
     * Set the width of the column
     *
     * @param int|null $width The width
     */
    public function setWidth(?int $width)
    {
        $this->width = $width;
    }

    /**
     * Get the styles
     *
     * @return array The styles
     */
    public function getStyles(): array
    {
        return $this->styles;
    }

    /**
     * Set the styles
     *
     * @param array $styles The styles
     */
    public function setStyles(array $styles)
    {
        $this->styles = $styles;
    }

    /**
     * Add a style
     *
     * @param string $name The style name
     * @param mixed $value The style value
     */
    public function addStyle(string $name, $value)
    {
        $this->styles[$name] = $value;
    }

    /**
     * Validate a value against this column's type
     *
     * @param mixed $value The value to validate
     * @return bool True if valid
     */
    public function validateValue($value): bool
    {
        $exportValue = new ExportValue($value, $this->type, $this->format);
        return $exportValue->validate();
    }

    /**
     * Create an ExportValue for this column
     *
     * @param mixed $value The value
     * @return ExportValue The created ExportValue
     */
    public function createValue($value): ExportValue
    {
        return new ExportValue($value, $this->type, $this->format);
    }
}
