<?php

namespace Nzoom\Export;

use Nzoom\I18n\I18n;
use Nzoom\I18n\I18nAwareTrait;

/**
 * Factory for creating export actions
 */
class ExportActionFactory
{
    use I18nAwareTrait;

    /**
     * @var \Registry
     */
    protected $registry;

    /**
     * @var string
     */
    protected $module;

    /**
     * @var string
     */
    protected $controller;

    /**
     * @var string
     */
    protected $modelName;

    /**
     * @var string
     */
    protected $modelFactoryName;

    /**
     * @var array
     */
    protected $systemExportModules = [];

    /**
     * ExportActionFactory constructor.
     *
     * @param \Registry $registry
     * @param string $module
     * @param string $controller
     * @param string|null $modelName
     * @param string|null $modelFactoryName
     * @param I18n $translator
     */
    public function __construct(
        \Registry $registry,
        string    $module,
        string    $controller,
        ?string   $modelName = null,
        ?string   $modelFactoryName = null,
        I18n     $translator
    )
    {
        $this->registry = $registry;
        $this->module = $module;
        $this->controller = $controller;

        if ($modelName !== null) {
            $this->modelName = $modelName;
        }

        if ($modelFactoryName !== null) {
            $this->modelFactoryName = $modelFactoryName;
        }

        $this->setTranslator($translator);
    }

    /**
     * Set the model name
     *
     * @param string $modelName
     * @return $this
     */
    public function setModelName(string $modelName)
    {
        $this->modelName = $modelName;
        return $this;
    }

    /**
     * Set the model factory name
     *
     * @param string $modelFactoryName
     * @return $this
     */
    public function setModelFactoryName(string $modelFactoryName)
    {
        $this->modelFactoryName = $modelFactoryName;
        return $this;
    }

    /**
     * Magic method to invoke the factory
     *
     * @param string $module_check Module to check permissions for
     * @param array $types
     * @param array $typeSections
     * @return array|null
     */
    public function __invoke(string $module_check, array $types, array $typeSections): ?array
    {
        return $this->createExportAction($module_check, $types, $typeSections);
    }

    /**
     * Create export action
     *
     * @param string $module_check Module to check permissions for
     * @param array $types
     * @param array $typeSections
     * @return array|null
     */
    public function createExportAction(string $module_check, array $types, array $typeSections): ?array
    {
        // Note: Permission checking would require checkActionPermissions and systemExportModules
        // which are not available in this factory context. This should be handled by the caller.

        return [
            'options' => $this->prepareExportOptions($types, $typeSections),
            'ajax_no' => 1,
            'disable_items_before_execute' => 'diselectItemsBeforeMultiAction(this);',
        ];
    }

    /**
     * Prepare export options with plugins where needed
     *
     * This version doesn't use saveSearchParams, instead it takes all necessary parameters as arguments
     *
     * @param array $types Array of available types
     * @param array $sections Array of available sections
     * @param array $filtersHide Filters to hide
     * @return array The prepared array with options for export
     */
    public function prepareExportOptions(
        array $types = [],
        array $sections = [],
        array $filtersHide = ['group_tables'=>false, 'separator'=>true]
    ): array
    {
        // Initialize filter visibility settings
        $filtersHide = $this->initializeFilterVisibility($filtersHide);

        $selectedType = $this->firstOrZero($types);
        $selectedSection = $this->firstOrZero($sections);
        // Build the export options array
        $exportOptions = $this->buildBaseExportOptions($filtersHide, $types, $selectedSection);

        $exportOptions = array_merge(
            $exportOptions,
            $this->getPluginOptions($selectedType, $selectedSection));


        // Add format and delimiter options
        $exportOptions = array_merge(
            $exportOptions,
            $this->getFormatOptions($filtersHide),
            $this->getGroupTablesOptions($filtersHide),
            $this->getDelimiterOptions($filtersHide)
        );

        return $exportOptions;
    }

    /**
     * Initialize filter visibility settings
     *
     * @param array $filtersHide Initial filter visibility settings
     * @return array Updated filter visibility settings
     */
    private function initializeFilterVisibility(array $filtersHide): array
    {
        foreach (\Exports::$filtersToStore as $param) {
            if (!isset($filtersHide[$param])) {
                $filtersHide[$param] = false;
            }
        }

        return $filtersHide;
    }

    /**
     * Get the first element of an array or zero if empty
     *
     * @param array $subject
     * @return int
     */
    private function firstOrZero(array $subject): int
    {
        if (count($subject) === 1) {
            return (int)reset($subject);
        }
        return 0;
    }

    /**
     * Get plugin options for export
     *
     * @param int $selectedType Selected type ID
     * @param int $selectedSection Selected section ID
     * @return array Plugin data including options and settings
     */
    private function getPluginOptions(int $selectedType, int $selectedSection): array
    {
        // Try to get plugins for the listed model and model_type
        $filters = [
            'model' => $this->modelName,
            'model_type' => $selectedType ?: $selectedSection,
            'sanitize' => true
        ];

        // Get all plugins for this model
        $plugins = \Exports::search($this->registry, $filters);

        if (empty($plugins)) {
            return [];
        }
        // Get last used plugin from session
        $lastExportPlugins = $this->registry['session']->get('last_export_plugins');
        $lastExportPluginSessionParam = sprintf(
            '%s_%s_type_%s',
            $this->module,
            $this->controller,
            $selectedType
        );

        $lastExportPlugin = $lastExportPlugins[$lastExportPluginSessionParam] ?? 'standard';

        // Add standard plugin option
        $pluginOptions = [
            [
            'label' => $this->i18n('export_plugin_standard'),
            'option_value' => 'standard'
            ]
        ];

        // Add all plugins to the options
        foreach ($plugins as $plugin) {
            $pluginOptions[] = [
                'label' => $plugin->get('name'),
                'option_value' => $plugin->get('id'),
                //'class_name' => 'export_plugin_' . $plugin->get('id')
            ];
        }

        // Prepare export options for plugins
        return [
            [
                'custom_id' => 'plugin',
                'name' => 'plugin',
                'type' => 'dropdown',
                'required' => 1,
                'onchange' => 'selectExport(this, \'' . implode(',', \Exports::$filtersToStore) . '\')',
                'label' => $this->i18n('export_plugin'),
                'help' => $this->i18n('export_plugin'),
                'options' => $pluginOptions,
                'value' => $lastExportPlugin
            ],
            [
                'custom_id' => 'previous_plugin',
                'name' => 'previous_plugin',
                'type' => 'hidden',
                'hidden' => 1,
                'value' => $lastExportPlugin
            ],
        ];
    }

    /**
     * Build the base export options array
     *
     * @param array $filtersHide Filters to hide
     * @param array $types Available types
     * @param int $selectedSection Selected section ID
     * @return array Base export options
     */
    private function buildBaseExportOptions(array $filtersHide, array $types, int $selectedSection): array
    {
        // Prepare export options
        $exportOptions = [
            [
                'custom_id' => 'export_previous_action',
                'name' => 'export_previous_action',
                'type' => 'hidden',
                'value' => 'list',
                'hidden' => 1,
            ],
            [
                'custom_id' => 'file_name',
                'name' => 'file_name',
                'type' => 'text',
                'required' => 1,
                'label' => $this->i18n('file_name'),
                'help' => $this->i18n('file_name'),
                'value' => $this->modelFactoryName,
                'hidden' => (isset($filtersHide['file_name'])) ? $filtersHide['file_name'] : 0,
            ],
        ];

        // Add type or section ID if needed
        if (count($types) === 1) {
            $exportOptions[] = [
                'name' => 'type_id',
                'type' => 'hidden',
                'value' => reset($types),
                'hidden' => 1,
            ];
        } elseif ($selectedSection) {
            $exportOptions[] = [
                'name' => 'type_section_id',
                'type' => 'hidden',
                'value' => $selectedSection,
                'hidden' => 1,
            ];
        }

        return $exportOptions;
    }

    /**
     * Get format options for export
     *
     * @param array $filtersHide Filters to hide
     * @return array Format options
     */
    private function getFormatOptions(array $filtersHide): array
    {
        return [
            [
                'custom_id' => 'format',
                'name' => 'format',
                'type' => 'dropdown',
                'required' => 1,
                'label' => $this->i18n('file_format'),
                'help' => $this->i18n('file_format'),
                'options' => [
                    [
                        'label' => 'Excel (XLSX)',
                        'option_value' => 'xlsx'
                    ],
                    [
                        'label' => 'Excel (XLS)',
                        'option_value' => 'xls'
                    ],
                    [
                        'label' => 'CSV',
                        'option_value' => 'csv'
                    ],
                ],
                'onchange' => 'toggleExportFormat(this)',
                'value' => 'xlsx',
                'hidden' => (isset($filtersHide['format'])) ? $filtersHide['format'] : 0,
            ]
        ];
    }

    /**
     * Get group tables options for export
     *
     * @param array $filtersHide Filters to hide
     * @return array Group tables options
     */
    private function getGroupTablesOptions(array $filtersHide): array
    {
        return [
            [
                'custom_id' => 'group_tables',
                'name' => 'group_tables',
                'type' => 'radio',
                'required' => 1,
                'options_align' => 'horizontal',
                'label' => $this->i18n('include_group_tables'),
                'help' => $this->i18n('include_group_tables'),
                'options' => [
                    [
                        'label' => $this->i18n('yes'),
                        'option_value' => 1
                    ],
                    [
                        'label' => $this->i18n('no'),
                        'option_value' => 0
                    ],
                ],
                'value' => 0,
                'hidden' => (isset($filtersHide['group_tables'])) ? $filtersHide['group_tables'] : 0,
            ]
        ];
    }

    /**
     * Get delimiter options for CSV export
     *
     * @param array $filtersHide Filters to hide
     * @return array Delimiter options
     */
    private function getDelimiterOptions(array $filtersHide): array
    {
        return [
            [
                'custom_id' => 'separator',
                'name' => 'separator',
                'type' => 'dropdown',
                'required' => 1,
                'label' => $this->i18n('separator'),
                'help' => $this->i18n('separator'),
                'options' => [
                    [
                        'label' => $this->i18n('delimiter_comma'),
                        'option_value' => 'comma'
                    ],
                    [
                        'label' => $this->i18n('delimiter_semicolon'),
                        'option_value' => 'semicolon'
                    ],
                    [
                        'label' => $this->i18n('delimiter_tab'),
                        'option_value' => 'tab'
                    ],
                ],
                'value' => 'comma',
                'hidden' => (isset($filtersHide['separator'])) ? $filtersHide['separator'] : 0,
            ]
        ];
    }
}
