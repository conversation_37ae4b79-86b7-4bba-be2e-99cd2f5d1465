<?php

namespace Nzoom\I18n;

/**
 * Trait for classes that need translation functionality
 *
 * Provides getter and setter for the translator and the i18n method
 */
trait I18nAwareTrait
{
    /**
     * @var I18n
     */
    protected $translator;

    /**
     * Get the translator
     *
     * @return I18n
     */
    public function getTranslator(): I18n
    {
        return $this->translator;
    }

    /**
     * Set the translator
     *
     * @param I18n $translator
     * @return $this
     */
    public function setTranslator(I18n $translator)
    {
        $this->translator = $translator;
        return $this;
    }

    /**
     * Translate a string using the translator
     *
     * @param string $key
     * @return string
     */
    protected function i18n(string $key): string
    {
        return $this->translator->translate($key)??'';
    }
}
