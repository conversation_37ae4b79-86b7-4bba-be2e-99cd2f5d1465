<?php
namespace Nzoom;

class Navigation
{
    const MODULE_PARAM = 'launch';
    const NON_REDIRECTABLE_URL_REGEXP = '/(ajax|i18n|=(auth|addnew|addquick|subpanel|getoptions|insertids|automations|crontab|multi\w+|tags|setstatus|calculate(deadlines)?|franky|savegroupvar|bb|show_available_outlook_options|manage_outlook|dashlets?|filter|audit|manageGT2config|asterisk.*)\b|controller=(branches|contactpersons|phases|timesheets))/';

    public static function buildUrl(array $queryData, string $anchor = ''): string
    {
        return sprintf('%s?%s%s', $_SERVER['PHP_SELF'], http_build_query($queryData), ($anchor !== '' ? "#{$anchor}" : ''));
    }

    public static function redirectToUrl(string $url, int $responseCode = 302): void
    {
        header('Location: ' . $url, true, $responseCode);
        exit;
    }

    public static function redirect(array $queryData, string $anchor = '', int $responseCode = 302): void
    {
        self::redirectToUrl(self::buildUrl($queryData, $anchor), $responseCode);
    }

    public static function buildNzoomUrl(string $module, string $action = null, string $controller = null, array $queryData = [], string $anchor = ''): string
    {
        $urlData = [
            self::MODULE_PARAM => $module,
        ];
        $actionParam = $module;
        if (!is_null($controller)) {
            $urlData['controller'] = $controller;
            $actionParam = $controller;
        }
        if (!is_null($action)) {
            $urlData[$actionParam] = $action;
        }
        return self::buildUrl(array_merge($urlData, $queryData), $anchor);
    }

    public static function redirectToNzoom(string $module, string $action = null, string $controller = null, array $queryData = [], string $anchor = '')
    {
        self::redirectToUrl(self::buildNzoomUrl($module, $action, $controller, $queryData, $anchor));
    }

    /**
     * Check if the given URL doesn't contain some special words/parts, like AJAX or soo
     *
     * @param string $url
     * @return bool
     */
    public static function isUrlRedirectable(string $url): bool
    {
        return !preg_match(self::NON_REDIRECTABLE_URL_REGEXP, $url);
    }

    /**
     * Check if the given URL is from the current nZoom installation
     *
     * @param string $url
     * @return bool
     */
    public static function isUrlFromCurrentNzoom(string $url): bool
    {
        if (!isset($_SERVER['REQUEST_URI'])) {
            return false;
        }

        return parse_url($url, PHP_URL_PATH) === parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    }
}
