<?php

namespace Nzoom\Email\Imap;

use Webklex\PHPIMAP\Exceptions\GetMessagesFailedException;
use Webklex\PHPIMAP\Query\WhereQuery;
use Webklex\PHPIMAP\Support\MessageCollection;

class MessageListPaginated implements \Iterator
{
    private WhereQuery $query;
    private int $messagesPerPage;
    private int $page = 1;
    private bool $reverse = false;
    private int $lastPage;

    public function __construct($query, $messagesPerPage = 10)
    {
        $this->query = $query;
        $this->messagesPerPage = $messagesPerPage;

    }

    /**
     * @param $page
     * @return MessageCollection
     * @throws GetMessagesFailedException
     */
    public function getPage($page)
    {
        return $this->query->limit($this->messagesPerPage, $page)->get();
    }

    /**
     * @return false|float|int
     * @throws GetMessagesFailedException
     */
    public function getLastPage()
    {
        if (!empty($this->lastPage)) {
            return $this->lastPage;
        }

        return $this->lastPage = ceil($this->query->count() / $this->messagesPerPage);
    }

    public function current()
    {
        return $this->getPage($this->page);
    }

    public function next()
    {
        return $this->isReverse() ? --$this->page : ++$this->page;
    }

    public function key()
    {
        return $this->page;
    }

    public function valid()
    {
        return  $this->page >= 1 && $this->page <= $this->getLastPage();
    }

    public function rewind()
    {
        $this->page = $this->isReverse() ? $this->getLastPage() : 1;
    }

    /**
     * @return bool
     */
    public function isReverse(): bool
    {
        return $this->reverse;
    }

    /**
     * @param bool $revers
     */
    public function setReverse(bool $reverse): void
    {
        $this->reverse = $reverse;
        if($this->reverse && $this->key() === 1) {
            $this->rewind();
        }
    }
}
