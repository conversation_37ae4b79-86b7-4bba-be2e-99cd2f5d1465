<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Entity</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">Entity</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="15.76" aria-valuemin="0" aria-valuemax="100" style="width: 15.76%">
           <span class="sr-only">15.76% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">15.76%</div></td>
       <td class="danger small"><div align="right">67&nbsp;/&nbsp;425</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="21.13" aria-valuemin="0" aria-valuemax="100" style="width: 21.13%">
           <span class="sr-only">21.13% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">21.13%</div></td>
       <td class="danger small"><div align="right">30&nbsp;/&nbsp;142</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;7</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportColumn.php.html">ExportColumn.php</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">50.00%</div></td>
       <td class="warning small"><div align="right">15&nbsp;/&nbsp;30</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="37.50" aria-valuemin="0" aria-valuemax="100" style="width: 37.50%">
           <span class="sr-only">37.50% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">37.50%</div></td>
       <td class="warning small"><div align="right">6&nbsp;/&nbsp;16</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportData.php.html">ExportData.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;101</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;24</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportHeader.php.html">ExportHeader.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="15.15" aria-valuemin="0" aria-valuemax="100" style="width: 15.15%">
           <span class="sr-only">15.15% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">15.15%</div></td>
       <td class="danger small"><div align="right">10&nbsp;/&nbsp;66</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="18.18" aria-valuemin="0" aria-valuemax="100" style="width: 18.18%">
           <span class="sr-only">18.18% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">18.18%</div></td>
       <td class="danger small"><div align="right">4&nbsp;/&nbsp;22</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportRecord.php.html">ExportRecord.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="22.22" aria-valuemin="0" aria-valuemax="100" style="width: 22.22%">
           <span class="sr-only">22.22% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">22.22%</div></td>
       <td class="danger small"><div align="right">12&nbsp;/&nbsp;54</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="11.54" aria-valuemin="0" aria-valuemax="100" style="width: 11.54%">
           <span class="sr-only">11.54% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">11.54%</div></td>
       <td class="danger small"><div align="right">3&nbsp;/&nbsp;26</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportTable.php.html">ExportTable.php</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="36.84" aria-valuemin="0" aria-valuemax="100" style="width: 36.84%">
           <span class="sr-only">36.84% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">36.84%</div></td>
       <td class="warning small"><div align="right">14&nbsp;/&nbsp;38</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="38.10" aria-valuemin="0" aria-valuemax="100" style="width: 38.10%">
           <span class="sr-only">38.10% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">38.10%</div></td>
       <td class="warning small"><div align="right">8&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportTableCollection.php.html">ExportTableCollection.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="12.12" aria-valuemin="0" aria-valuemax="100" style="width: 12.12%">
           <span class="sr-only">12.12% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">12.12%</div></td>
       <td class="danger small"><div align="right">8&nbsp;/&nbsp;66</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="23.81" aria-valuemin="0" aria-valuemax="100" style="width: 23.81%">
           <span class="sr-only">23.81% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">23.81%</div></td>
       <td class="danger small"><div align="right">5&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportValue.php.html">ExportValue.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="11.43" aria-valuemin="0" aria-valuemax="100" style="width: 11.43%">
           <span class="sr-only">11.43% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">11.43%</div></td>
       <td class="danger small"><div align="right">8&nbsp;/&nbsp;70</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="33.33" aria-valuemin="0" aria-valuemax="100" style="width: 33.33%">
           <span class="sr-only">33.33% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">33.33%</div></td>
       <td class="danger small"><div align="right">4&nbsp;/&nbsp;12</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 35%</span>
     <span class="warning"><strong>Medium</strong>: 35% to 70%</span>
     <span class="success"><strong>High</strong>: 70% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Mon Jun 23 17:51:38 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
