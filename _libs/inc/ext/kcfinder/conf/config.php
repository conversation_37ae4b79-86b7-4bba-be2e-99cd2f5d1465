<?php
/** This file is part of KCFinder project
  *
  *      @desc Base configuration file
  *   @package KCFinder
  *   @version 3.12
  *    <AUTHOR> <<EMAIL>>
  * @copyright 2010-2014 KCFinder Project
  *   @license http://opensource.org/licenses/GPL-3.0 GPLv3
  *   @license http://opensource.org/licenses/LGPL-3.0 LGPLv3
  *      @link http://kcfinder.sunhater.com
  */

/* IMPORTANT!!! Do not comment or remove uncommented settings in this file
   even if you are using session configuration.
   See http://kcfinder.sunhater.com/install for setting descriptions */

//ToDo: fix KCFinder for production
$_CONFIG = array(


// GENERAL SETTINGS

    'disabled' => defined('KC_BROWSE_PERMISSION') ? !KC_BROWSE_PERMISSION : true,
    'uploadURL' => defined('KC_UPLOADS_URL') ? KC_UPLOADS_URL : '',
    'uploadDir' => defined('KC_UPLOADS_DIR') ? KC_UPLOADS_DIR : '',
    'theme' => "default",

    'types' => array(

    // (F)CKEditor types
        'file'    =>  "jpg jpeg png gif",
        'media'   =>  "swf flv avi mpg mpeg qt mov wmv asf rm",
        'image'   =>  "jpg jpeg png gif",
    ),


// IMAGE SETTINGS

    'imageDriversPriority' => "imagick gmagick gd",
    'jpegQuality' => 90,
    'thumbsDir' => ".thumbs",

    'maxImageWidth' => 0,
    'maxImageHeight' => 0,

    'thumbWidth' => 100,
    'thumbHeight' => 100,

    'watermark' => "",


// DISABLE / ENABLE SETTINGS

    'denyZipDownload' => false,
    'denyUpdateCheck' => false,
    'denyExtensionRename' => false,


// PERMISSION SETTINGS

    'dirPerms' => 0755,
    'filePerms' => 0644,

    'access' => array(

        'files' => array(
            'upload' => defined('KC_UPLOAD_PERMISSION') ? KC_UPLOAD_PERMISSION : false,
            'delete' => defined('KC_UPLOAD_PERMISSION') ? KC_UPLOAD_PERMISSION : false,
            'copy'   => defined('KC_UPLOAD_PERMISSION') ? KC_UPLOAD_PERMISSION : false,
            'move'   => defined('KC_UPLOAD_PERMISSION') ? KC_UPLOAD_PERMISSION : false,
            'rename' => defined('KC_UPLOAD_PERMISSION') ? KC_UPLOAD_PERMISSION : false
        ),

        'dirs' => array(
            'create' => false,
            'delete' => false,
            'rename' => false
        )
    ),

    'deniedExts' => "exe com msi bat cgi pl php phps phtml php3 php4 php5 php6 py pyc pyo pcgi pcgi3 pcgi4 pcgi5 pchi6 lib inc js html",


// MISC SETTINGS

    'filenameChangeChars' => array(/*
        ' ' => "_",
        ':' => "."
    */),

    'dirnameChangeChars' => array(/*
        ' ' => "_",
        ':' => "."
    */),

    'mime_magic' => "",

    'cookieDomain' => $_SERVER['SERVER_NAME'],
    'cookiePath' => "",
    'cookiePrefix' => 'KCFINDER_',


// THE FOLLOWING SETTINGS CANNOT BE OVERRIDED WITH SESSION SETTINGS

    '_normalizeFilenames' => false,
    '_check4htaccess' => true,
    //'_tinyMCEPath' => "/tiny_mce",

    '_sessionVar' => "KCFINDER",
    //'_sessionLifetime' => 30,
    //'_sessionDir' => "/full/directory/path",
    //'_sessionDomain' => ".mysite.com",
    //'_sessionPath' => "/my/path",

    //'_cssMinCmd' => "java -jar /path/to/yuicompressor.jar --type css {file}",
    //'_jsMinCmd' => "java -jar /path/to/yuicompressor.jar --type js {file}",

);

?>
