<?php
/**
 * Smarty plugin
 * @package Smarty
 * @subpackage plugins
 */


/**
 * Converts table with to DOCX table/cell width attributes
 *
 * Type:     modifier<br>
 * Name:     mb_lower<br>
 * Purpose:  convert string to lowercase
 * <AUTHOR> <at bgservice dot net>
 * @param string
 * @return string
 */
function smarty_modifier_convert2docx_width($string, $tag = 'w:tblW')
{

    $size = preg_replace('#([0-9]*).*#', '\1', $string);
    $units = preg_replace('#(px|%)#', '\1', $string);
    switch($units) {
        case '%':
            //https://startbigthinksmall.wordpress.com/2010/01/04/points-inches-and-emus-measuring-units-in-office-open-xml/
            // Fiftieths of a Percent
            $size = intval($size) * 50;
            $units = 'pct';
            break;
        case 'px':
        default:
            //the unit of the width corresponding to pixels in a Word Document is 20th of a point (dxa)
            //http://openxmldeveloper.org/blog/b/openxmldeveloper/archive/2006/07/27/408.aspx
            $size = intval($size) * 20;
            $units = 'dxa';
            break;
    }

    if ($size && $units) {
        return "<{$tag} w:w=\"{$size}\" w:type=\"{$units}\"/>";
    } else {
        return '';
    }
}

?>
