<?php
// $Header: /cvsroot/html2ps/encoding.iso-8859-9.inc.php,v 1.5 2007/01/24 18:55:59 Konstantin Exp $

$GLOBALS['g_iso_8859_9'] = array(
"\x00" => 0x0000,	//	NULL
"\x01" => 0x0001,	//	START OF HEADING
"\x02" => 0x0002,	//	START OF TEXT
"\x03" => 0x0003,	//	END OF TEXT
"\x04" => 0x0004,	//	END OF TRANSMISSION
"\x05" => 0x0005,	//	ENQUIRY
"\x06" => 0x0006,	//	ACKNOWLEDGE
"\x07" => 0x0007,	//	BELL
"\x08" => 0x0008,	//	BACKSPACE
"\x09" => 0x0009,	//	HORIZONTAL TABULATION
"\x0A" => 0x000A,	//	LINE FEED
"\x0B" => 0x000B,	//	VERTICAL TABULATION
"\x0C" => 0x000C,	//	FORM FEED
"\x0D" => 0x000D,	//	CARRIAGE RETURN
"\x0E" => 0x000E,	//	SHIFT OUT
"\x0F" => 0x000F,	//	SHIFT IN
"\x10" => 0x0010,	//	DATA LINK ESCAPE
"\x11" => 0x0011,	//	DEVICE CONTROL ONE
"\x12" => 0x0012,	//	DEVICE CONTROL TWO
"\x13" => 0x0013,	//	DEVICE CONTROL THREE
"\x14" => 0x0014,	//	DEVICE CONTROL FOUR
"\x15" => 0x0015,	//	NEGATIVE ACKNOWLEDGE
"\x16" => 0x0016,	//	SYNCHRONOUS IDLE
"\x17" => 0x0017,	//	END OF TRANSMISSION BLOCK
"\x18" => 0x0018,	//	CANCEL
"\x19" => 0x0019,	//	END OF MEDIUM
"\x1A" => 0x001A,	//	SUBSTITUTE
"\x1B" => 0x001B,	//	ESCAPE
"\x1C" => 0x001C,	//	FILE SEPARATOR
"\x1D" => 0x001D,	//	GROUP SEPARATOR
"\x1E" => 0x001E,	//	RECORD SEPARATOR
"\x1F" => 0x001F,	//	UNIT SEPARATOR
"\x20" => 0x0020,	//	SPACE
"\x21" => 0x0021,	//	EXCLAMATION MARK
"\x22" => 0x0022,	//	QUOTATION MARK
"\x23" => 0x0023,	//	NUMBER SIGN
"\x24" => 0x0024,	//	DOLLAR SIGN
"\x25" => 0x0025,	//	PERCENT SIGN
"\x26" => 0x0026,	//	AMPERSAND
"\x27" => 0x0027,	//	APOSTROPHE
"\x28" => 0x0028,	//	LEFT PARENTHESIS
"\x29" => 0x0029,	//	RIGHT PARENTHESIS
"\x2A" => 0x002A,	//	ASTERISK
"\x2B" => 0x002B,	//	PLUS SIGN
"\x2C" => 0x002C,	//	COMMA
"\x2D" => 0x002D,	//	HYPHEN-MINUS
"\x2E" => 0x002E,	//	FULL STOP
"\x2F" => 0x002F,	//	SOLIDUS
"\x30" => 0x0030,	//	DIGIT ZERO
"\x31" => 0x0031,	//	DIGIT ONE
"\x32" => 0x0032,	//	DIGIT TWO
"\x33" => 0x0033,	//	DIGIT THREE
"\x34" => 0x0034,	//	DIGIT FOUR
"\x35" => 0x0035,	//	DIGIT FIVE
"\x36" => 0x0036,	//	DIGIT SIX
"\x37" => 0x0037,	//	DIGIT SEVEN
"\x38" => 0x0038,	//	DIGIT EIGHT
"\x39" => 0x0039,	//	DIGIT NINE
"\x3A" => 0x003A,	//	COLON
"\x3B" => 0x003B,	//	SEMICOLON
"\x3C" => 0x003C,	//	LESS-THAN SIGN
"\x3D" => 0x003D,	//	EQUALS SIGN
"\x3E" => 0x003E,	//	GREATER-THAN SIGN
"\x3F" => 0x003F,	//	QUESTION MARK
"\x40" => 0x0040,	//	COMMERCIAL AT
"\x41" => 0x0041,	//	LATIN CAPITAL LETTER A
"\x42" => 0x0042,	//	LATIN CAPITAL LETTER B
"\x43" => 0x0043,	//	LATIN CAPITAL LETTER C
"\x44" => 0x0044,	//	LATIN CAPITAL LETTER D
"\x45" => 0x0045,	//	LATIN CAPITAL LETTER E
"\x46" => 0x0046,	//	LATIN CAPITAL LETTER F
"\x47" => 0x0047,	//	LATIN CAPITAL LETTER G
"\x48" => 0x0048,	//	LATIN CAPITAL LETTER H
"\x49" => 0x0049,	//	LATIN CAPITAL LETTER I
"\x4A" => 0x004A,	//	LATIN CAPITAL LETTER J
"\x4B" => 0x004B,	//	LATIN CAPITAL LETTER K
"\x4C" => 0x004C,	//	LATIN CAPITAL LETTER L
"\x4D" => 0x004D,	//	LATIN CAPITAL LETTER M
"\x4E" => 0x004E,	//	LATIN CAPITAL LETTER N
"\x4F" => 0x004F,	//	LATIN CAPITAL LETTER O
"\x50" => 0x0050,	//	LATIN CAPITAL LETTER P
"\x51" => 0x0051,	//	LATIN CAPITAL LETTER Q
"\x52" => 0x0052,	//	LATIN CAPITAL LETTER R
"\x53" => 0x0053,	//	LATIN CAPITAL LETTER S
"\x54" => 0x0054,	//	LATIN CAPITAL LETTER T
"\x55" => 0x0055,	//	LATIN CAPITAL LETTER U
"\x56" => 0x0056,	//	LATIN CAPITAL LETTER V
"\x57" => 0x0057,	//	LATIN CAPITAL LETTER W
"\x58" => 0x0058,	//	LATIN CAPITAL LETTER X
"\x59" => 0x0059,	//	LATIN CAPITAL LETTER Y
"\x5A" => 0x005A,	//	LATIN CAPITAL LETTER Z
"\x5B" => 0x005B,	//	LEFT SQUARE BRACKET
"\x5C" => 0x005C,	//	REVERSE SOLIDUS
"\x5D" => 0x005D,	//	RIGHT SQUARE BRACKET
"\x5E" => 0x005E,	//	CIRCUMFLEX ACCENT
"\x5F" => 0x005F,	//	LOW LINE
"\x60" => 0x0060,	//	GRAVE ACCENT
"\x61" => 0x0061,	//	LATIN SMALL LETTER A
"\x62" => 0x0062,	//	LATIN SMALL LETTER B
"\x63" => 0x0063,	//	LATIN SMALL LETTER C
"\x64" => 0x0064,	//	LATIN SMALL LETTER D
"\x65" => 0x0065,	//	LATIN SMALL LETTER E
"\x66" => 0x0066,	//	LATIN SMALL LETTER F
"\x67" => 0x0067,	//	LATIN SMALL LETTER G
"\x68" => 0x0068,	//	LATIN SMALL LETTER H
"\x69" => 0x0069,	//	LATIN SMALL LETTER I
"\x6A" => 0x006A,	//	LATIN SMALL LETTER J
"\x6B" => 0x006B,	//	LATIN SMALL LETTER K
"\x6C" => 0x006C,	//	LATIN SMALL LETTER L
"\x6D" => 0x006D,	//	LATIN SMALL LETTER M
"\x6E" => 0x006E,	//	LATIN SMALL LETTER N
"\x6F" => 0x006F,	//	LATIN SMALL LETTER O
"\x70" => 0x0070,	//	LATIN SMALL LETTER P
"\x71" => 0x0071,	//	LATIN SMALL LETTER Q
"\x72" => 0x0072,	//	LATIN SMALL LETTER R
"\x73" => 0x0073,	//	LATIN SMALL LETTER S
"\x74" => 0x0074,	//	LATIN SMALL LETTER T
"\x75" => 0x0075,	//	LATIN SMALL LETTER U
"\x76" => 0x0076,	//	LATIN SMALL LETTER V
"\x77" => 0x0077,	//	LATIN SMALL LETTER W
"\x78" => 0x0078,	//	LATIN SMALL LETTER X
"\x79" => 0x0079,	//	LATIN SMALL LETTER Y
"\x7A" => 0x007A,	//	LATIN SMALL LETTER Z
"\x7B" => 0x007B,	//	LEFT CURLY BRACKET
"\x7C" => 0x007C,	//	VERTICAL LINE
"\x7D" => 0x007D,	//	RIGHT CURLY BRACKET
"\x7E" => 0x007E,	//	TILDE
"\x7F" => 0x007F,	//	DELETE
"\x80" => 0x0080,	//	<control>
"\x81" => 0x0081,	//	<control>
"\x82" => 0x0082,	//	<control>
"\x83" => 0x0083,	//	<control>
"\x84" => 0x0084,	//	<control>
"\x85" => 0x0085,	//	<control>
"\x86" => 0x0086,	//	<control>
"\x87" => 0x0087,	//	<control>
"\x88" => 0x0088,	//	<control>
"\x89" => 0x0089,	//	<control>
"\x8A" => 0x008A,	//	<control>
"\x8B" => 0x008B,	//	<control>
"\x8C" => 0x008C,	//	<control>
"\x8D" => 0x008D,	//	<control>
"\x8E" => 0x008E,	//	<control>
"\x8F" => 0x008F,	//	<control>
"\x90" => 0x0090,	//	<control>
"\x91" => 0x0091,	//	<control>
"\x92" => 0x0092,	//	<control>
"\x93" => 0x0093,	//	<control>
"\x94" => 0x0094,	//	<control>
"\x95" => 0x0095,	//	<control>
"\x96" => 0x0096,	//	<control>
"\x97" => 0x0097,	//	<control>
"\x98" => 0x0098,	//	<control>
"\x99" => 0x0099,	//	<control>
"\x9A" => 0x009A,	//	<control>
"\x9B" => 0x009B,	//	<control>
"\x9C" => 0x009C,	//	<control>
"\x9D" => 0x009D,	//	<control>
"\x9E" => 0x009E,	//	<control>
"\x9F" => 0x009F,	//	<control>
"\xA0" => 0x00A0,	//	NO-BREAK SPACE
"\xA1" => 0x00A1,	//	INVERTED EXCLAMATION MARK
"\xA2" => 0x00A2,	//	CENT SIGN
"\xA3" => 0x00A3,	//	POUND SIGN
"\xA4" => 0x00A4,	//	CURRENCY SIGN
"\xA5" => 0x00A5,	//	YEN SIGN
"\xA6" => 0x00A6,	//	BROKEN BAR
"\xA7" => 0x00A7,	//	SECTION SIGN
"\xA8" => 0x00A8,	//	DIAERESIS
"\xA9" => 0x00A9,	//	COPYRIGHT SIGN
"\xAA" => 0x00AA,	//	FEMININE ORDINAL INDICATOR
"\xAB" => 0x00AB,	//	LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
"\xAC" => 0x00AC,	//	NOT SIGN
"\xAD" => 0x00AD,	//	SOFT HYPHEN
"\xAE" => 0x00AE,	//	REGISTERED SIGN
"\xAF" => 0x00AF,	//	MACRON
"\xB0" => 0x00B0,	//	DEGREE SIGN
"\xB1" => 0x00B1,	//	PLUS-MINUS SIGN
"\xB2" => 0x00B2,	//	SUPERSCRIPT TWO
"\xB3" => 0x00B3,	//	SUPERSCRIPT THREE
"\xB4" => 0x00B4,	//	ACUTE ACCENT
"\xB5" => 0x00B5,	//	MICRO SIGN
"\xB6" => 0x00B6,	//	PILCROW SIGN
"\xB7" => 0x00B7,	//	MIDDLE DOT
"\xB8" => 0x00B8,	//	CEDILLA
"\xB9" => 0x00B9,	//	SUPERSCRIPT ONE
"\xBA" => 0x00BA,	//	MASCULINE ORDINAL INDICATOR
"\xBB" => 0x00BB,	//	RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
"\xBC" => 0x00BC,	//	VULGAR FRACTION ONE QUARTER
"\xBD" => 0x00BD,	//	VULGAR FRACTION ONE HALF
"\xBE" => 0x00BE,	//	VULGAR FRACTION THREE QUARTERS
"\xBF" => 0x00BF,	//	INVERTED QUESTION MARK
"\xC0" => 0x00C0,	//	LATIN CAPITAL LETTER A WITH GRAVE
"\xC1" => 0x00C1,	//	LATIN CAPITAL LETTER A WITH ACUTE
"\xC2" => 0x00C2,	//	LATIN CAPITAL LETTER A WITH CIRCUMFLEX
"\xC3" => 0x00C3,	//	LATIN CAPITAL LETTER A WITH TILDE
"\xC4" => 0x00C4,	//	LATIN CAPITAL LETTER A WITH DIAERESIS
"\xC5" => 0x00C5,	//	LATIN CAPITAL LETTER A WITH RING ABOVE
"\xC6" => 0x00C6,	//	LATIN CAPITAL LETTER AE
"\xC7" => 0x00C7,	//	LATIN CAPITAL LETTER C WITH CEDILLA
"\xC8" => 0x00C8,	//	LATIN CAPITAL LETTER E WITH GRAVE
"\xC9" => 0x00C9,	//	LATIN CAPITAL LETTER E WITH ACUTE
"\xCA" => 0x00CA,	//	LATIN CAPITAL LETTER E WITH CIRCUMFLEX
"\xCB" => 0x00CB,	//	LATIN CAPITAL LETTER E WITH DIAERESIS
"\xCC" => 0x00CC,	//	LATIN CAPITAL LETTER I WITH GRAVE
"\xCD" => 0x00CD,	//	LATIN CAPITAL LETTER I WITH ACUTE
"\xCE" => 0x00CE,	//	LATIN CAPITAL LETTER I WITH CIRCUMFLEX
"\xCF" => 0x00CF,	//	LATIN CAPITAL LETTER I WITH DIAERESIS
"\xD0" => 0x011E,	//	LATIN CAPITAL LETTER G WITH BREVE
"\xD1" => 0x00D1,	//	LATIN CAPITAL LETTER N WITH TILDE
"\xD2" => 0x00D2,	//	LATIN CAPITAL LETTER O WITH GRAVE
"\xD3" => 0x00D3,	//	LATIN CAPITAL LETTER O WITH ACUTE
"\xD4" => 0x00D4,	//	LATIN CAPITAL LETTER O WITH CIRCUMFLEX
"\xD5" => 0x00D5,	//	LATIN CAPITAL LETTER O WITH TILDE
"\xD6" => 0x00D6,	//	LATIN CAPITAL LETTER O WITH DIAERESIS
"\xD7" => 0x00D7,	//	MULTIPLICATION SIGN
"\xD8" => 0x00D8,	//	LATIN CAPITAL LETTER O WITH STROKE
"\xD9" => 0x00D9,	//	LATIN CAPITAL LETTER U WITH GRAVE
"\xDA" => 0x00DA,	//	LATIN CAPITAL LETTER U WITH ACUTE
"\xDB" => 0x00DB,	//	LATIN CAPITAL LETTER U WITH CIRCUMFLEX
"\xDC" => 0x00DC,	//	LATIN CAPITAL LETTER U WITH DIAERESIS
"\xDD" => 0x0130,	//	LATIN CAPITAL LETTER I WITH DOT ABOVE
"\xDE" => 0x015E,	//	LATIN CAPITAL LETTER S WITH CEDILLA
"\xDF" => 0x00DF,	//	LATIN SMALL LETTER SHARP S
"\xE0" => 0x00E0,	//	LATIN SMALL LETTER A WITH GRAVE
"\xE1" => 0x00E1,	//	LATIN SMALL LETTER A WITH ACUTE
"\xE2" => 0x00E2,	//	LATIN SMALL LETTER A WITH CIRCUMFLEX
"\xE3" => 0x00E3,	//	LATIN SMALL LETTER A WITH TILDE
"\xE4" => 0x00E4,	//	LATIN SMALL LETTER A WITH DIAERESIS
"\xE5" => 0x00E5,	//	LATIN SMALL LETTER A WITH RING ABOVE
"\xE6" => 0x00E6,	//	LATIN SMALL LETTER AE
"\xE7" => 0x00E7,	//	LATIN SMALL LETTER C WITH CEDILLA
"\xE8" => 0x00E8,	//	LATIN SMALL LETTER E WITH GRAVE
"\xE9" => 0x00E9,	//	LATIN SMALL LETTER E WITH ACUTE
"\xEA" => 0x00EA,	//	LATIN SMALL LETTER E WITH CIRCUMFLEX
"\xEB" => 0x00EB,	//	LATIN SMALL LETTER E WITH DIAERESIS
"\xEC" => 0x00EC,	//	LATIN SMALL LETTER I WITH GRAVE
"\xED" => 0x00ED,	//	LATIN SMALL LETTER I WITH ACUTE
"\xEE" => 0x00EE,	//	LATIN SMALL LETTER I WITH CIRCUMFLEX
"\xEF" => 0x00EF,	//	LATIN SMALL LETTER I WITH DIAERESIS
"\xF0" => 0x011F,	//	LATIN SMALL LETTER G WITH BREVE
"\xF1" => 0x00F1,	//	LATIN SMALL LETTER N WITH TILDE
"\xF2" => 0x00F2,	//	LATIN SMALL LETTER O WITH GRAVE
"\xF3" => 0x00F3,	//	LATIN SMALL LETTER O WITH ACUTE
"\xF4" => 0x00F4,	//	LATIN SMALL LETTER O WITH CIRCUMFLEX
"\xF5" => 0x00F5,	//	LATIN SMALL LETTER O WITH TILDE
"\xF6" => 0x00F6,	//	LATIN SMALL LETTER O WITH DIAERESIS
"\xF7" => 0x00F7,	//	DIVISION SIGN
"\xF8" => 0x00F8,	//	LATIN SMALL LETTER O WITH STROKE
"\xF9" => 0x00F9,	//	LATIN SMALL LETTER U WITH GRAVE
"\xFA" => 0x00FA,	//	LATIN SMALL LETTER U WITH ACUTE
"\xFB" => 0x00FB,	//	LATIN SMALL LETTER U WITH CIRCUMFLEX
"\xFC" => 0x00FC,	//	LATIN SMALL LETTER U WITH DIAERESIS
"\xFD" => 0x0131,	//	LATIN SMALL LETTER DOTLESS I
"\xFE" => 0x015F,	//	LATIN SMALL LETTER S WITH CEDILLA
"\xFF" => 0x00FF	//	LATIN SMALL LETTER Y WITH DIAERESIS
                      );
?>