/viewport-create {                 % => Box
  <<
    /left   0
    /width  0
    /height 0
    /top    0
    /offset 0
    /page-offset 0
    /current-page 1
    /anchors << >>
  >>                               % => Box Viewport

  1 index get-left
  1 index exch
  /left exch put

  1 index get-top
  1 index exch
  /top exch put

  1 index /get-width call-method
  1 index exch
  /width exch put

  1 index get-height
  1 index exch
  /height exch put

  exch pop
} def                              % => Viewport

/viewport-draw-page-border {       % => Viewport
  newpath
  1 setlinewidth
  0 0 0 setrgbcolor
  
  dup viewport-get-left
  1 index viewport-get-bottom
  moveto

  dup viewport-get-left
  1 index viewport-get-top
  lineto

  dup viewport-get-right
  1 index viewport-get-top
  lineto

  dup viewport-get-right
  1 index viewport-get-bottom
  lineto

  closepath

  stroke
  pop
} def

/viewport-get-anchor {             % => Viewport Name
  1 index /anchors get             % => Viewport Name Anchors
  dup 2 index known {              % => Viewport Name Anchors
    1 index get                    % => Viewport Name AnchorData
  } {
    pop /null
  } ifelse                         % => Viewport Name AnchorData

  exch pop
  exch pop
} def

/viewport-get-bottom {             % => Viewport
  dup /top get
  1 index /height get
  sub
  exch pop
} def

/viewport-get-height {
  /height get
} def

/viewport-get-left {
  /left get
} def

/viewport-get-offset-delta {       % => Viewport
  /offset get
} def

/viewport-get-page-offset {
  /page-offset get
} def

/viewport-get-right {              % => Viewport
  dup /left get
  1 index /width get
  add
  exch pop
} def

/viewport-get-top {                % => Viewport
  dup /top get
  exch pop
} def

/viewport-init-page {              % => Viewport
  0 1 index /page-offset get 
  translate

  0 tmargin neg
  translate
 
  pop
} def

/viewport-next-page {              % => Viewport
  dup /page-offset get neg         % => Viewport Offset
  real-page-height sub             % => Viewport Offset1
  1 index /offset get add          % => Viewport Offset2
  neg
  
  1 index exch                     % => Viewport Viewport Offset2 
  /page-offset exch put            % => Viewport 

  dup /top get
  real-page-height sub             % => Viewport Top'
  1 index /offset get add          
  1 index exch
  /top exch put
  
  dup /offset 0 put                % => Viewport

% Increase current page number
  dup /current-page get 1 add
  1 index exch
  /current-page exch put           % => Viewport
  
  pop
} def	

/viewport-put-anchors {            % => Data Viewport 
  exch /anchors exch put
} def

/viewport-put-offset-delta {       % => Delta Viewport
  exch /offset exch put
} def

/viewport-setup-clip {
  dup viewport-get-left
  1 index viewport-get-bottom
  moveto

  dup viewport-get-left
  1 index viewport-get-top
  lineto

  dup viewport-get-right
  1 index viewport-get-top
  lineto

  dup viewport-get-right
  1 index viewport-get-bottom
  lineto

  closepath
  clip

  pop
} def

