% $Header: /cvsroot/html2ps/postscript/text-align.ps,v 1.1 2005/12/18 07:21:38 Konstantin Exp $

% Constants
/max-justify-fraction {0.33} def

/line-length-delta {               % Context Box 
  dup line-length                  % Context Box LineLength
% calculate length line to be expanded
  2 index 2 index
  box-container-get-available-width
  exch sub                         % Context Box Delta
% Quick workaround - line delta should not be less than 0
  0 max
  
  exch pop
  exch pop
} def

/text-align-left {                 % => LastLine Context Box
  pop pop pop
} def

/text-align-center {               % => LastLine Context Box
  2 copy line-length-delta         % => LastLine Context Box Delta
  2 div                            % => LastLine Context Box Delta/2
  1 index get-line                 % => LastLine Context Box Delta/2 Line
  {                                % => LastLine Context Box Delta/2 LineElement
    0 exch
    2 index exch                   % => LastLine Context Box Delta/2 0 Delta/2 LineElement
    /offset call-method           
  } forall                         % => LastLine Context Box Delta/2
  pop pop pop pop
} def

/text-align-right {                % => LastLine Context Box 
  2 copy line-length-delta         % => LastLine Context Box Delta
  1 index get-line                 % => LastLine Context Box Delta Line
  {                                % => LastLine Context Box Delta LineElement
    0 exch
    2 index exch                   % => LastLine Context Box Delta/2 Delta/2 0 LineElement
    /offset call-method
  } forall                         % => LastLine Context Box Delta/2
  pop pop pop pop
} def

/text-align-justify {              % => LastLine Context Box
% last line is never justified
  2 index not {                    % => LastLine Context Box

% If line box contains less that two items, no justification can be done, just return
    dup get-line length 2 ge {     % => LastLine Context Box

% Calculate extra space to be filled by this line
      2 copy line-length-delta     % => LastLine Context Box Delta

% note that if it is the very first line inside the container, 'text-indent' value
% should not be taken into account while calculating delta value
      1 index
      box-container-get-first
      get-uid                      % => LastLine Context Box Delta FirstUID
      2 index get-line
      0 get get-uid                % => LastLine Context Box Delta FirstUID FirstLineUID
      eq {                         % => LastLine Context Box Delta 
        1 index box-generic-calc-text-indent
        sub
      } if                         % => LastLine Context Box Delta

% if line takes less that MAX_JUSTIFY_FRACTION of available space, no justtification should be done
      1 index line-length
      max-justify-fraction mul     % => LastLine Context Box Delta LL*MJF
      1 index ge {                 % => LastLine Context Box Delta
% Calculate offset for each box
        dup 
        2 index get-line length 
        1 sub div                  % => LastLine Context Box Delta Offset
        dup                        % => LastLine Context Box Delta Offset COffset

% Offset all boxes in current line box
        3 index get-line {         % => LastLine Context Box Delta Offset COffset Child
          0 2 index 2 index
          /offset call-method      % => LastLine Context Box Delta Offset COffset Child

          pop
          1 index add              % => LastLine Context Box Delta Offset COffset 
        } forall

        pop pop                    % => LastLine Context Box Delta 
      } if
      
      pop                          % => LastLine Context Box 
    } if
  } if

  pop pop pop
} def
