<?php

/**
 * MappedDrives class
 */
Class Mapped_Drives {
    public $drives = array();

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

    }

    public function getDrives(&$registry, $params = '') {
        $this->drives = $registry['config']->getParam('mapped_drives');
        $lines = preg_split('/(\n|\r|\r\n)/', $registry['currentUser']->get('mapped_drives'));
        foreach($lines as $line) {
            if (trim($line)) {
                list($drive, $path) = preg_split('/\s*\=\s*/', $line);
                $this->drives[$drive] = $path;
            }
        }

        return $this->drives;  
    }
}
?>
