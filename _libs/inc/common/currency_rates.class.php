<?php

class Currency_Rates {

    /**
     * List of supported banks
     */
    private static $_banks = array(
        'BNB' => array(
                    'name' => array('bg'=>'Българска Народна Банка', 'en' =>'Bulgarian National Bank'),
                    'url'  => 'http://www.bnb.bg/Statistics/StExternalSector/StExchangeRates/StERForeignCurrencies/index.htm?download=xml&search=&lang=BG'),
        'ALIANZ' => array(
                    'name' => array('bg'=>'Алианц Банк България', 'en' =>'Allianz Bank Bulgaria'),
                    'url'  => 'http://bank.allianz.bg/index.php?page=currency'),
        'ALPHA' => array(
                    'name' => array('bg'=>'Алфа Банк', 'en' =>'Alpha Bank'),
                    'url'  => 'http://www.alphabank.bg/displaybg.aspx?page=quotes'),
        'BULBANK' => array(
                    'name' => array('bg'=>'УниКредит Булбанк', 'en' =>'UniCredit Bulbank'),
                    'url'  => 'https://www.unicreditbulbank.bg/bg/api/fxrates/'),
        'DSK' => array(
                    'name' => array('bg'=>'Банка ДСК', 'en' =>'DSK Bank'),
                    'url'  => 'https://dskbank.bg/restapi/dsk/GetCurrencyRates/?format=json'),
        'FIB' => array(
                    'name' => array('bg'=>'Първа Инвестиционна Банка', 'en' =>'First Investment Bank'),
                    'url'  => 'https://www.fibank.bg/bg/valutni-kursove'),
        'UBB' => array(
                    'name' => array('bg'=>'Обединена Българска Банка', 'en' =>'United Bulgarian Bank'),
                    'url'  => 'http://www.ubb.bg/Home.aspx'),
        'POSTBANK' => array(
                    'name' => array('bg'=>'Пощенска банка', 'en' =>'Bulgarian Post Bank'),
                    'url'  => 'http://www.postbank.bg'),
        'RAIFFEISEN' => array(
                    'name' => array('bg'=>'Райфайзен Банк', 'en' =>'Raiffeisen Bank (Bulgaria)'),
                    'url'  => 'http://rbb.bg/bg-BG/Corporate_Customers/Investing/07_risk/04_rates/?_all=1'),
        'UNIONBANK' => array(
                    'name' => array('bg'=>'МКБ Юнионбанк', 'en' =>'MKB Unionbank'),
                    'url'  => 'http://www.unionbank.bg'),
    );

    /**
     * Errors that may occur are stored in this array
     */
    public static $errors = array();

    /**
     * The date of the modified currency rates of the bank
     */
    public static $date;

    /**
     * Array with currency rates
     */
    public static $rates = array();

    /**
     * HTML of the bank site to be parsed
     */
    private static $_parseHTML;

    /**
     * This method gets currency rates of one of the supported banks
     *
     * @param string $bank - the designated bank should be one of the banks supported in the class, if no parameter passed gets the entire list of banks
     * @return array - list of all currency rates for bank and cash operations, the date is also included
     */
    public static function getBankInfo($bank, $lang) {
        if ($bank) {
            if (array_key_exists($bank, self::$_banks)) {
                $info = array('url' => self::$_banks[$bank]['url']);
                if (isset(self::$_banks[$bank]['name'][$lang])) {
                    $info['name'] = self::$_banks[$bank]['name'][$lang];
                } else {
                    $info['name'] = self::$_banks[$bank]['name']['bg'];
                }
                return $info;
            }
        } else {
            //get all banks info
            $banks = array();
            foreach (self::$_banks as $code => $info) {
                $banks[$code] = !empty($info['name'][$lang]) ? $info['name'][$lang] : $info['name']['bg'];
            }
            return $banks;
        }

        return false;
    }


    /**
     * This method gets currency rates of one of the supported banks
     *
     * @param string $bank - the designated bank should be one of the banks supported in the class
     * @return array - list of all currency rates for bank and cash operations, the date is also included
     */
    public static function get($bank, $fixing = true) {
        if (!array_key_exists($bank, self::$_banks)) {
            self::$errors[] = 'This bank (' . $bank . ') is not on the list of supported bank!';
            return false;
        }
        $bank_options = self::$_banks[$bank];
        self::$_parseHTML = self::_getBankContent($bank, $bank_options['url']);
        if (empty(self::$_parseHTML)) {
            self::$errors[] = 'This bank (' . $bank_options['name']['en'] . ') is currently offline!';
            return false;
        }

        $result = array();
        $method = '_getRates' . strtoupper($bank);
        if (self::$method()) {
            ksort(self::$rates);

            $result = array(
                'bank'  => $bank,
                'date'  => self::$date,
                'date_formated'  => General::strftime('%d.%m.%Y', strtotime(self::$date)),
                'rates' => self::$rates);
        }

        return $result;
    }

    /**
     * Get content of the bank site url
     *
     * @param string $bankUri
     * @param string $bankUri
     * @return string
     */
    private static function _getBankContent(string $bank, string $bankUri):string {
        $html = '';

        // Timeout in seconds
        $context = stream_context_create(
            array(
                'http' => array(
                    'timeout' => 3,
                    'header'=>"Accept-language: bgn\r\n" .
                        (in_array($bank, ['DSK', 'BULBANK']) ?
                            "User-Agent: Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B334b Safari/531.21.102011-10-16 20:23:10\r\n" :
                            "")

                ),
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            )
        );

        //ToDo: change the error handling with a class
        set_error_handler(
            function ($severity, $message, $file, $line) {
                throw new ErrorException($message, $severity, $severity, $file, $line);
            }
        );

        try {
            $html = General::fileGetContents($bankUri, $context);
        } catch(\Exception $e) {
            General::log(
                //ToDo: fix the logging and/or the fetching of registry
                $GLOBALS['registry'],
                'get_currency_rate',
               "Unable to get currency rates from {$bank} ($bankUri)\n" . $e->getMessage()
            );
        }

        //ToDo: change the error handling with a class
        restore_error_handler();

        return $html;
    }

    /**
     * This method gets All currency rates of all supported banks
     *
     * @return array - list of all currency rates for bank and cash operations, the date is also included
     */
    public static function getAll() {
        $result = array();
        foreach (self::$_banks as $code => $bank_options) {
            $result[$code] = self::get($code);
        }

        return $result;
    }

    /**
     * This method gets currency rates of one of the supported banks
     *
     * @param string $bank - the designated bank should be one of the banks supported in the class
     * @return array - list of all currency rates for bank and cash operations, the date is also included
     */
    public static function getRatesAndFixing($bank) {
        //get the bank rates
        $rates = self::get($bank);

        if (empty($rates)) {
            return false;
        }

        //get the fixing
        $fixing_rates = self::get('BNB');
        if (empty($fixing_rates['rates'])) {
            return $rates;
        }
        foreach ($fixing_rates['rates'] as $code => $rate) {
            $rates['rates'][$code]['fixing'] = @$fixing_rates['rates'][$code]['fixing'];

            //set default currency units 1
            if (empty($rates['rates'][$code]['units'])) {
                $rates['rates'][$code]['units'] = $fixing_rates['rates'][$code]['units'];
            }
            //adjust the units per currency
            if ($rates['rates'][$code]['units'] != $fixing_rates['rates'][$code]['units']) {
                $ratio = $fixing_rates['rates'][$code]['units']/$rates['rates'][$code]['units'];

                $rates['rates'][$code]['buys_bank'] = $rates['rates'][$code]['buys_bank'] * $ratio;
                $rates['rates'][$code]['sells_bank'] = $rates['rates'][$code]['sells_bank'] * $ratio;
                if (isset($rates['rates'][$code]['buys_cash'])) {
                    $rates['rates'][$code]['buys_cash'] = $rates['rates'][$code]['buys_cash'] * $ratio;
                }
                if (isset($rates['rates'][$code]['sells_cash'])) {
                    $rates['rates'][$code]['sells_cash'] = $rates['rates'][$code]['sells_cash'] * $ratio;
                }

                $rates['rates'][$code]['units'] = $fixing_rates['rates'][$code]['units'];
            }

            //format all the rates to the fifth digit after the decimal point
            $rates['rates'][$code]['fixing']     = sprintf('%.6F', $rates['rates'][$code]['fixing']);
            $rates['rates'][$code]['buys_bank']  = sprintf('%.6F', @$rates['rates'][$code]['buys_bank']);
            $rates['rates'][$code]['sells_bank'] = sprintf('%.6F', @$rates['rates'][$code]['sells_bank']);
            if (isset($rates['rates'][$code]['buys_cash'])) {
                $rates['rates'][$code]['buys_cash']  = sprintf('%.6F', $rates['rates'][$code]['buys_cash']);
            }
            if (isset($rates['rates'][$code]['sells_cash'])) {
                $rates['rates'][$code]['sells_cash'] = sprintf('%.6F', $rates['rates'][$code]['sells_cash']);
            }
        }

        return $rates;
    }

    /**
     * This method gets the fixed currency rates of the central Bulgarian National Bank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesBNB() {
        //the EURO is with fixed rate of 1.95583
        self::$rates = array('EUR' => array('units' => 1, 'fixing' => 1.95583));

        $data = FilesLib::xml2array(self::$_parseHTML);

        if (!empty($data)) {
            //remove the first title row
            $data = !empty($data['ROWSET']['ROW']) ? array_slice($data['ROWSET']['ROW'], 1) : array();
            foreach ($data as $item){
                if (isset($item['CURR_DATE']) && empty($date) && preg_match('/(\d*)\.(\d*)\.(\d*)/', $item['CURR_DATE'])) {
                    //get the date
                    $date = self::$date = preg_replace('/(\d*)\.(\d*)\.(\d*)/', "$3-$2-$1 00:00:00", $item['CURR_DATE']);
                }
                if (isset($item['RATIO']) && isset($item['RATE'])) {
                    self::$rates[$item['CODE']] = array(
                        'units'     => intval($item['RATIO']),
                        'fixing'    => floatval($item['RATE']),
                    );
                }
            }
        }

        return true;
    }

    /**
     * This method gets the rates of First Investment Bank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesFIB() {
        //parse the date
        $parsed_string = preg_replace('/.*Валутни курсове.*към\s*(\d*\.\d*\.\d* \d*:\d*).*/smi', "$1", self::$_parseHTML);
        self::$date = preg_replace('/.*?(\d*)\.(\d*)\.(\d*) (\d*):(\d*)/', "$3-$2-$1 $4:$5:00", trim($parsed_string));

        //parse the currencies
        $parsed_string = preg_replace('/.*(<table cellspacing=\"0\" cellpadding=\"0\">.*<\/table>).*/sm', "$1", self::$_parseHTML);

        preg_match_all("/<tr class=\"even\">.*?<\/tr>|<tr>.*?<\/tr>/smi", $parsed_string, $matches, PREG_PATTERN_ORDER);
        $rows = $matches[0];
        //remove header row
        array_shift($rows);
        self::$rates = array();
        foreach ($rows as $row){
            preg_match_all('#.*<td[^\>]*>(.*)</td>.*#Ui', $row, $matches);
            $columns = $matches[1];

            $code = isset($columns[1]) ? trim($columns[1]) : '';
            $units = isset($columns[2]) ? trim($columns[2]) : '';
            $fixing = isset($columns[2]) ? trim($columns[3]) : '';
            $buys = isset($columns[4]) ? trim($columns[4]) : '';
            $sells = isset($columns[5]) ? trim($columns[5]) : '';

            self::$rates[$code]['units']      = intval($units);
            self::$rates[$code]['buys_bank']  = floatval($buys);
            self::$rates[$code]['sells_bank'] = floatval($sells);
            self::$rates[$code]['fixing']      = floatval($fixing);
        }

        return true;
    }

    /**
     * This method gets the rates of Allianz Bank Bulgaria
     *
     * @return bool - result of the operation
     */
    private static function _getRatesALIANZ() {
        //parse the date
        $parsed_string = preg_replace('/.*<tr class=col-tblDark>.*?<font[^>]*>(.*)<\/font><\/span><table.*/smi', "$1", self::$_parseHTML);
        self::$date = preg_replace('/.*?(\d*)\.(\d*)\.(\d*)/', "$3-$2-$1 00:00:00", trim($parsed_string));

        //parse the currencies
        $parsed_string = preg_replace('/.*(<table width=100% border=0 cellspacing=0 cellpadding=0>.*?<\/table>).*/sm', "$1", self::$_parseHTML);
        preg_match_all("/<tr>\s*<td>.*?<\/tr>/smi", $parsed_string, $matches, PREG_PATTERN_ORDER);
        $rows = $matches[0];

        self::$rates = array();
        foreach ($rows as $idx => $row){
            preg_match_all("/<td[^>]*>.*?<\/td>/smi", $row, $matches, PREG_PATTERN_ORDER);
            $columns = $matches[0];

            $code = strip_tags($columns[0]);
            list($buys, $sells) = explode('/', strip_tags($columns[1]));

            if ($idx <=3) {
                self::$rates[$code]['buys_bank']  = floatval($buys);
                self::$rates[$code]['sells_bank'] = floatval($sells);
            } else {
                self::$rates[$code]['buys_cash']  = floatval($buys);
                self::$rates[$code]['sells_cash'] = floatval($sells);
            }
        }

        return true;
    }

    /**
     * This method gets the rates of Alpha Bank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesALPHA() {
        //parse the date
        $parsed_string = preg_replace('/.*<P align=\"?center\"?><FONT class=\"?BODY\"?><STRONG>(.*)<\/STRONG><\/FONT><\/P>.*/smi', "$1", self::$_parseHTML);
        self::$date = preg_replace('/.*?(\d*)\.(\d*)\.(\d*).*?(\d*)\:(\d*)/smi', "$3-$2-$1 $4:$5:00", trim($parsed_string));

        //parse the currencies
        $parsed_string = preg_replace('/.*(<TABLE style="BORDER-TOP-WIDTH: 1px; BORDER-LEFT-WIDTH: 1px".*?<\/TABLE>).*/smi', "$1", self::$_parseHTML);
        preg_match_all("/<tr.*?<\/tr>/smi", $parsed_string, $matches, PREG_PATTERN_ORDER);
        $rows = array_slice($matches[0], 2);

        self::$rates = array();
        foreach ($rows as $idx => $row){
            preg_match_all("/<td[^>]*>.*?<\/td>/smi", $row, $matches, PREG_PATTERN_ORDER);
            $columns = $matches[0];

            $code = preg_replace('/1 (\w*)/', "$1", strip_tags($columns[0]));
            $buys = strip_tags($columns[2]);
            $sells = strip_tags($columns[3]);

            self::$rates[$code]['buys_bank']  = floatval($buys);
            self::$rates[$code]['sells_bank'] = floatval($sells);

            $buys = strip_tags($columns[4]);
            $sells = strip_tags($columns[5]);

            self::$rates[$code]['buys_cash']  = floatval($buys);
            self::$rates[$code]['sells_cash'] = floatval($sells);
        }

        return true;
    }

    /**
     * This method gets the rates of UniCredit Bulbank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesBULBANK() {
        //parse the date
        $response = json_decode(self::$_parseHTML, true);

        if (!$response) {
            return false;
        }
        $dateTime = new DateTime($response['datetime']);
        $timeZone = 'Europe/Sofia';
        //try to get $registry from $GLOBALS and get the timezone from there
        if (isset($GLOBALS['registry']['config'])) {
            $timeZone = $GLOBALS['registry']['config']->getParam('calendars', 'default_timezone');
        }
        $dateTime->setTimezone(new DateTimeZone($timeZone));
        self::$date = $dateTime->format('Y-m-d');

        self::$rates = array();
        foreach ($response['data'] as $row) {
            $code = $row['code'];
            if ($code == 'BGN') {
                continue;
            }
            self::$rates[$code]['units']      = intval($row['nominal']);
            self::$rates[$code]['buys_bank']  = floatval($row['buy_rate']);
            self::$rates[$code]['sells_bank'] = floatval($row['sell_rate']);
            self::$rates[$code]['buys_cash']  = floatval($row['buy_cache_rate']);
            self::$rates[$code]['sells_cash'] = floatval($row['sell_cache_rate']);
            self::$rates[$code]['fixing'] = floatval($row['bnb_rate']);
        }
        return true;
    }

    /**
     * This method gets the rates of DSK Bank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesDSK() {
        $ratesData = json_decode(gzdecode(self::$_parseHTML), true);
        if (!isset($ratesData['Items'])) {
            return false;
        }
        self::$rates = array();
        foreach ($ratesData['Items'] as $row){
            $code = $row['IsoCode'];
            if ($code == 'BGN') continue;
            $timestamp = (int) filter_var($row['UpdatedOn'], FILTER_SANITIZE_NUMBER_INT);
            $timestamp = $timestamp / 1000;
            $date = new DateTime("@$timestamp", new DateTimeZone('UTC'));
            $timeZone = 'Europe/Sofia';
            //try to get $registry from $GLOBALS and get the timezone from there
            if (isset($GLOBALS['registry']['config'])) {
                $timeZone = $GLOBALS['registry']['config']->getParam('calendars', 'default_timezone');
            }
            $date->setTimezone(new DateTimeZone($timeZone));
            self::$date = $date->format('Y-m-d');
            self::$rates[$code]['buys_bank']  = floatval($row['NotesSell']);
            self::$rates[$code]['sells_bank'] = floatval($row['NotesBuy']);
            self::$rates[$code]['buys_cash']  = floatval($row['ChequeBuy']);
            self::$rates[$code]['sells_cash'] = floatval($row['ChequeSell']);
            self::$rates[$code]['fixing'] = floatval($row['BnbFixing']);
        }

        return true;
    }

    /**
     * This method gets the rates of United Bulgarian Bank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesUBB() {
        //parse the date
        //parse the currencies
        preg_match_all('/<div class="currency".*<h2>\s+<label[^>]*>(.*)<\/label>/smi', self::$_parseHTML, $matches, PREG_PATTERN_ORDER);
        self::$date = preg_replace('/(?<=\-)(\d(\-|\s))/', '0$1', preg_replace('#.*(\d{1,2})\.(\d{1,2})\.(\d{4})[^\d]*(\d{2}):(\d{2}):(\d{2}).*#', '$3-$2-$1 $4:$5:$6', $matches[1][0]));

        //parse currencies
        preg_match_all('/<div class="currency".*<dl>(.*)<\/dl>/smi', self::$_parseHTML, $matches, PREG_PATTERN_ORDER);
        $rows = preg_split('#</dd>#', $matches[1][0]);

        self::$rates = array();
        foreach ($rows as $idx => $row) {
            if (!trim($row)) {
                continue;
            }
            list($code, $row) = preg_split('#</dt>#', $row);
            $code = trim(strip_tags($code));
            preg_match_all('#<strong[^>]*>(.*)</strong>#', $row, $matches);

            self::$rates[$code]['sells_bank'] = floatval($matches[1][0]);
            self::$rates[$code]['sells_cash'] = floatval($matches[1][1]);
            self::$rates[$code]['buys_bank']  = floatval($matches[1][2]);
            self::$rates[$code]['buys_cash']  = floatval($matches[1][3]);
        }

        return true;
    }

    /**
     * This method gets the rates of Bulgarian Post Bank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesPOSTBANK() {
        //parse the date
        $parsed_string = preg_replace('/.*<div class="exRateContent">(.*)<div class="exRatesBottom">.*/smiu', "$1", self::$_parseHTML);
        self::$date = preg_replace('/.*<div class="exRateHeadline">.*\s+(\d+)\.(\d+)\.(\d+)<\/div>.*/smiu', "$3-$2-$1 00:00:00", trim($parsed_string));

        //parse the currencies
        $parsed_string = preg_replace('/<div class="exRateHeadline">.*<div class="exRates">/smi', '', $parsed_string);
        preg_match_all('/<div class="exRate\s*[^"]*">(.*)<\/div>\s*<\/div>/smiuU', trim($parsed_string), $matches, PREG_PATTERN_ORDER);
        $rows = $matches[1];

        self::$rates = array();
        foreach ($rows as $idx => $row){
            preg_match_all("/<(div|span)[^>\/]*>.*?<\/[^>]*>/smiu", $row, $matches, PREG_PATTERN_ORDER);
            $columns = $matches[0];

            $code = trim(strip_tags($columns[0]));
            $buys = trim(strip_tags($columns[4]));
            $sells = trim(strip_tags($columns[2]));

            self::$rates[$code]['buys_bank']  = floatval($buys);
            self::$rates[$code]['sells_bank'] = floatval($sells);
        }

        return true;
    }

    /**
     * This method gets the rates of Raiffeisen Bank (Bulgaria)
     *
     * @return bool - result of the operation
     */
    private static function _getRatesRAIFFEISEN() {
        //parse the date
        //the date is parsed further down the code

        //parse the currencies
        preg_match_all('/<tr class="textsml" bgcolor="#FFFFFF">.*?<\/tr>|<tr class="textsml" bgcolor="#FFFCBE">.*?<\/tr>/smi', self::$_parseHTML, $matches, PREG_PATTERN_ORDER);
        $rows = $matches[0];

        self::$rates = array();
        foreach ($rows as $idx => $row){
            preg_match_all("/<td[^>]*>.*?<\/td>/smi", $row, $matches, PREG_PATTERN_ORDER);
            $columns = $matches[0];

            $code = trim(strip_tags($columns[0]));
            $buys = trim(str_replace('-', '', strip_tags($columns[4])));
            $sells = trim(str_replace('-', '', strip_tags($columns[5])));
            //parse the date
            self::$date = trim(strip_tags($columns[8]));

            self::$rates[$code]['buys_bank']  = floatval($buys);
            self::$rates[$code]['sells_bank'] = floatval($sells);

            $buys = trim(str_replace('-', '', strip_tags($columns[6])));
            $sells = trim(str_replace('-', '', strip_tags($columns[7])));

            self::$rates[$code]['buys_cash']  = floatval($buys);
            self::$rates[$code]['sells_cash'] = floatval($sells);
        }

        return true;
    }

    /**
     * This method gets the rates of MKB UnionBank
     *
     * @return bool - result of the operation
     */
    private static function _getRatesUNIONBANK() {
        //parse the date
        preg_match('/<div[^>]*class="WidgetCurrency[^>]*>\s*<h4><span>([^<]*)<\/span>/smi', self::$_parseHTML, $matches);
        $months = array('януари' => '01', 'февруари' => '02', 'март' => '03',
                        'април' => '04', 'май' => '05', 'юни' => '06',
                        'юли' => '07', 'август' => '08', 'септември' => '09',
                        'октомври' => '10', 'ноември' => '11', 'декември' => '12');
        self::$date =  preg_replace_callback('#(\d+)\s+([^\s]*)\s+(\d+).*#',
            function($m) use ($months) {return sprintf('%s-%s-%s 00:00:00', $m[3], $months[$m[2]], $m[1]);}, $matches[1]);

        //parse the currencies (cash)
        preg_match_all('#<div[^>]*id="currency-(bank|cash)"[^>]*>.*?</div>#smi', self::$_parseHTML, $matches, PREG_PATTERN_ORDER);
        $tables = $matches[0];
        $bk = $matches[1];

        self::$rates = array();

        foreach ($tables as $idx => $table){
            preg_match_all('#<tr>.*?</tr>#smi', $table, $rows);
            foreach ($rows[0] as $row) {
                if (preg_match('#<th>#', $row)) {
                    continue;
                }
                $row = preg_split("#\s+#sm",strip_tags($row));
                self::$rates[$row[1]]['buys_' . $bk[$idx]] = floatval($row[2]);
                self::$rates[$row[1]]['sells_' . $bk[$idx]] = floatval($row[3]);
            }
        }

        return true;
    }

    /**
     * This method gets the banks
     *
     * @return array - list of banks
     */
    public static function _getBanks() {
        $banks = self::$_banks;
        unset($banks['BNB']);

        return $banks;
    }
}

?>
