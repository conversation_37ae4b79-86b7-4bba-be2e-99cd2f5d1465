<?php

class Calculator {

    /**
     * Field to hold data for origin of calculated expressions (used for error logging)
     * @var array
     */
    private static $origin = array();
    private static $registry;


    /**
     * Perform calculation upon a model
     * ToDo: take out the manipulation/restore of the request
     *
     * @param $registry
     * @param $model
     * @return bool
     */
    public function calculateModel($registry, $model) {
        /**
         * @var $request Request
         */
        $request = $registry['request'];
        // preserve Request in current state in order to restore it in the end
        $request_original = clone $request;
        //preserve the action as well
        $action_original = $registry['action'];

        $assoc_vars = $model->getAssocVars();

        $buttons_all = $model->getButtons();

        $button_id = 0;
        foreach ($buttons_all as $buttons) {
            foreach ($buttons as $button) {
                if ($button['subtype'] == 'calculate') {
                    $button_id = $button['id'];
                    break 2;
                }
            }
        }
        if (!$button_id) {
            foreach ($assoc_vars as $var) {
                if (empty($var['grouping']) && !empty($var['calculate']) && $var['calculate'] != 3) {
                    $button_id = $var['id'];
                    break;
                }
            }
        }

        if (!$button_id) {
            return false;
        }
        $registry->set('action', 'calculate', true);

        // set in request all the necessary data for calculation
        $request->set('calculate', $button_id, 'get', true);
        $request->set('id', $model->get('id'), 'post', true);

        //set all variables that have not been posted into the request
        foreach($assoc_vars as $var) {
            if (!$request->isRequested($var['name'])) {
                $request->set($var['name'], $var['values'] ?? $var['value'], 'post', true);
            }
        }

        //set all BASIC variables that have not been posted into the request
        $basic = $model->getAll();
        foreach($basic as $varName => $value) {
            if (!in_array($varName, ['vars', 'assoc_vars', 'grouping_table_2'])
                && !$request->isRequested($varName)) {
                $request->set($varName, $value, 'post', true);
            }
        }

        //set BB vars
        $bbVars = $model->get('add_bb_vars');
        if (is_array($bbVars) && !empty($bbVars)) {
            foreach ($bbVars as $bbVar) {
                if (array_key_exists('value', $bbVar)) {
                    $request->set($bbVar['name'], $bbVar['value'], 'post', true);
                }
            }
        }

        // run the calculation
        $calculated = json_decode(Calculator::calculate($registry), true) ?: array();

        foreach($calculated as $k => $v) {
            if (preg_match('#(.*)\[(\d+)\]#', $k, $matches)) {
                $groupVarName = $matches[1];
                if (array_key_exists($groupVarName, $assoc_vars)) {
                    $groupIdx = $matches[2];
                    if (array_key_exists('values', $assoc_vars[$groupVarName])) {
                        $assoc_vars[$groupVarName]['values'][$groupIdx] = $v;
                    } else {
                        $assoc_vars[$groupVarName]['value'][$groupIdx] = $v;
                    }
                }
            } elseif (array_key_exists($k, $assoc_vars)) {
                if (array_key_exists('values', $assoc_vars[$k])) {
                    $assoc_vars[$k]['values'] = $v;
                } else {
                    $assoc_vars[$k]['value'] = $v;
                }
            } else {
                //basic variable
                $model->set($k, $v, true);
            }
        }

        $model->set('vars', array_values($assoc_vars), true);
        $model->set('assoc_vars', $assoc_vars, true);

        // restore request and action
        $registry->set('request', $request_original, true);
        $registry->set('action', $action_original, true);

        return true;
    }

    /**
     *
     *
     * @param Registry $registry
     * @return mixed
     */
    public static function calculate(&$registry) {
        if ($registry['request']->get('change_options') != '') {
            //return '123';
            $result = self::_options($registry);
        } elseif ($registry['request']->get('get_node') != '') {
        /*
            return json_encode(
            array('children'=>
            array(
                array('label'=>1,
                    "sourceType"=>'json/url',
                    "source"=>"index.php?section=documents&documents=calculate&calculate=".$registry['request']->get('calculate')."&get_node=1"),
                array('label'=>2),
                array('label'=>3)
            )));
        */
            $result = self::_getNode($registry);
        } else {
            //check if this is a general calculation button or single button
            $id = $registry['request']->get($registry['action']);
            $query = 'SELECT source FROM ' . DB_TABLE_FIELDS_META . ' WHERE source LIKE "%sequence%" AND id=' . $id;
            $button_source = $registry['db']->GetOne($query);
            if ($button_source) {
                // general button
                $result = array();
                $button_source = General::parseSettings($button_source);
                $sequence = (isset($button_source['sequence'])) ? $button_source['sequence'] : $button_source['sequences'];
                $sequence = preg_split('#\s*,\s*#', $sequence);
                $query = 'SELECT fm2.id ' . "\n" .
                         'FROM ' . DB_TABLE_FIELDS_META . ' as fm1' . "\n" .
                         'JOIN ' . DB_TABLE_FIELDS_META . ' as fm2' . "\n" .
                         '  ON fm1.model=fm2.model AND fm1.model_type=fm2.model_type AND fm2.name IN ("' . implode('", "', $sequence) . '")' . "\n" .
                         'WHERE fm1.id=' . $id . "\n".
                         // This is very important!
                         // Order the variables exactly LIKE the sequence
                         'ORDER BY find_in_set(fm2.name, "' . implode(',', $sequence) . '")';
                $sequence = $registry['db']->GetCol($query);
                $matches = array();
                foreach ($sequence as $fid) {
                    $res = self::_calculate($registry, $fid);
                    if ($res) {
                        foreach ($res as $var => $val) {
                            // IMPORTANT: if result key is in the format: var_name[pos],
                            // where pos contains only digits now but we will allow starting minus sign as well
                            if (preg_match('#^(.*)\[(-?\d+)\]$#', $var, $matches)) {
                                // the variable is array
                                // get the entire array from the request
                                $valr = is_array($registry['request']->get($matches[1])) ? $registry['request']->get($matches[1]) : array();
                                // extract the variable name (which is the key in request where the variable value is stored)
                                $var = $matches[1];
                                // replace only the element of that array at the matching position
                                $valr[$matches[2]] = $val;
                                $val = $valr;
                            }
                            // set calculated values in request so they are available for the next calculations in the sequence
                            $registry['request']->set($var, $val, 'all', true);
                        }
                        $result = array_merge($result, $res);
                    }
                }
                $result = json_encode($result);
            } else {
                // single button (used with input_text with calculate 1 or 3)
                $result = json_encode(self::_calculate($registry, $id));
            }
        }

        return $result;
    }

    /**
     * calculate dropdown options
     *
     * @param Registry registry - the main registry
     * @return string - result of the operation (JSON-encoded array)
     */
    public static function _options(&$registry) {
        $request = $registry['request']->getAll();
        $db = $registry['db'];
        $request = $registry['request'];
        $model_table = $registry['module'];
        $model = ucfirst(General::plural2singular($model_table));

        $sel_name = $request->get('change_options');
        $model_type = $request->get('type');
        if (empty($model_type) && $request->get('id')) {
            $query = 'SELECT type FROM ' . $model_table . ' WHERE id = ' . intval($request->get('id'));
            $model_type = $db->GetOne($query);
        }
        $query = 'SELECT fm.* FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' .
                 'WHERE name="' . $sel_name . '" AND model="' . $model . '"';
        if ($model_type) {
            $query .= ' AND model_type = ' . intval($model_type);
        }

        $records = $db->GetAll($query);
        list($field) = $records;

        self::$origin = array(
            'table' => DB_TABLE_FIELDS_META,
            'field' => 'source',
            'id' => $field['id'],
            'model' => $model,
            'model_id' => $request->get('id'),
        );

        $source_args = array();
        $source_args[0] = $registry;
        $source = $field['source'];
        $func_arr = preg_split('/(\n|\r|\r\n)/', $source);
        foreach ($func_arr as $f_row) {
            //row started with # is comment
            if (empty($f_row) || $f_row[0] == '#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $f_row);
            if ($key == 'method') {
                //expression for calculation
                $source_method = $value;
            } else {
                if (preg_match('#sql\(|request\(#', $value)) {
                    //calculate value - request, sql ...
                    $value = self::calc_value($registry, $source_args, $value);//return $value;
                }
                //array of variable => value parameters to be passed to dropdown method
                $source_args[$key] = $value;
            }
        }
        if (!isset($source_method)) {
            $records = array();
        } else {
            $dropdown = ucfirst($model_table) . '_Dropdown';
            $records = $dropdown::$source_method($source_args);
        }

        return json_encode($records);
    }

    /**
     * Perform calculation for a field (additional variable)
     *
     * @param Registry $registry - the main registry
     * @param int $id - id of additional variable in `_fields_meta` table
     * @return array|bool - associative array with variable names as keys and calculated values as values, or a false value on error
     */
    public static function _calculate(&$registry, $id) {

        $request = $registry['request']->getAll('post');
        $model_id = isset($request['id']) ? $request['id'] : '';

        //initialize the result
        $result = 0;

        if (isset($id) && $id > 0) {
            self::$registry = $registry;
            $db = $registry['db'];
            // get model, model_type and source of variable from DB by id
            $query = 'SELECT fm.* FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' .
                     'WHERE id=' . $id;
            $field = $db->GetRow($query);

            $model_type = $field['model_type'];
            $model = $field['model'];

            $name_matches = array();
            // if calculatable field is in bb, model of field is different
            // from real model name so try to get it from the inner grouping variable
            if ($field['bb'] && preg_match('#(.+)_calc$#', $field['name'], $name_matches)) {
                $model = $db->GetOne(
                    'SELECT model FROM ' . DB_TABLE_FIELDS_META .
                    " WHERE name = '{$name_matches[1]}' AND model_type = '{$model_type}'"
                ) ?: $model;
            }

            self::$origin = array(
                'table' => DB_TABLE_FIELDS_META,
                'field' => 'source',
                'id' => $field['id'],
                'model' => $model,
                'model_id' => $model_id,
            );

            //IMPORTANT: the grouping fields SHOULD not use array_function anymore
            //           even if such has been defined in source settings it would be ignored
            //           The group fields can be used in array functions in NON-group-table field
            if ($field['grouping'] != 0) {
                $update_fields = array($field['name']);
                return self::_reindexGroupTableFields(
                    self::_calculateGroupTableFields($field, $update_fields)
                );
            }

            // get the list of group fields to update
            $update_fields = self::_getGroupFieldsToUpdate($field);

            // in rare occasions the update_fields setting can be omitted
            // update_fields MIGHT NOT be related to array_function
            if (!empty($update_fields)) {
                // evaluate group fields before calculations
                $updated_group_fields = self::_calculateGroupTableFields($field, $update_fields);

                // restore id of main field as it is used in the evaluation of group fields
                self::$origin['id'] = $field['id'];
            }

            // prepare the data for calculations
            // the $data array contains equation, format, update_fields, arguments (equation variables), etc.
            $data = self::_prepareCalculationData($field);

            // perform array function (sum and avg available only)
            // the array function is always related to group tables
            if (!empty($data['array_function'])) {

                //num_rows is used to get the indexes of the group table
                $num_rows = array();
                if (!empty($updated_group_fields)) {
                    // get the num rows, do not need to set it as setting
                    // if the update_fields is set
                    $num_rows = reset($updated_group_fields);
                } elseif ($data['num_rows']) {
                    //get the rows from request (only if update_fields is omitted)
                    $num_rows = self::$registry['request']->get($data['num_rows']);
                }

                //array containing the arguments names to be replaced
                $args_vars = array_keys($data['args']);

                //perform array function (sum and avg available only)
                if (!empty($num_rows)) {
                    foreach ($num_rows as $idx => $not_important) {
                        // arrays with values to replace
                        // push them to group table rows
                        $args_values = array_values($data['args']);
                        foreach ($args_values as $key => $val) {
                            if (is_array($val)) {
                                if (isset($val[$idx]) && $val[$idx]) {
                                    //set row value for replace
                                    $args_values[$key] = $val[$idx];
                                } else {
                                    $args_values[$key] = 0;
                                }
                            } elseif (!$val) {
                                $args_values[$key] = 0;
                            }
                        }

                        if (!empty($data['equation'])) {
                            $row_result = EvalString::evaluate(
                                self::$registry,
                                $data['equation'],
                                array(
                                    'search' => $args_vars,
                                    'replace' => $args_values,
                                    'origin' => self::$origin,
                                ));
                            // exclude some unexpected values
                            if (is_object($row_result) || is_array($row_result) || !is_finite(floatval($row_result))) {
                                $row_result = 0;
                            }
                            // the default array function is sum
                            //sum into the result, the "+" is essential here
                            $result += floatval($row_result);
                        }
                    }

                    // array_function is avg
                    if ($data['array_function'] == 'avg') {
                        $result = $num_rows ? $result / (count($num_rows)) : 0;
                    }
                }

                //perform last equation, AFTER array function has been performed
                if (!empty($data['last_equation'])) {
                    // make sure $args_values has the same number of keys as $args_vars
                    // (if there are no active rows in a grouping table, for example)
                    if (!empty($args_vars) && empty($args_values)) {
                        $args_values = array_values($data['args']);
                    }
                    $args_vars[] = 'equation';
                    if (is_numeric($result)) {
                        $args_values[] = str_replace(',', '.', '' . $result);
                    } else {
                        $args_values[] = $result;
                    }
                    $result = EvalString::evaluate(
                        $registry,
                        $data['last_equation'],
                        array(
                            'search' => $args_vars,
                            'replace' => $args_values,
                            'origin' => self::$origin,
                        ));
                }

                $result = self::format($data['format'] ?? '', $result);

                //return array {field:value}
                if (isset($updated_group_fields)) {
                    $result = array_merge(array($field['name'] => trim($result)), self::_reindexGroupTableFields($updated_group_fields));
                } else {
                    $result = array($field['name'] => trim($result));
                }

                return $result;
            }

            //simple calculation (no array function)
            $args_vars = array_keys($data['args']);
            $args_values = array_values($data['args']);

            if (empty($data['equation'])) {
                $evaluated = '';
            } else {
                // restore id of main field as it is used in the evaluation of group fields
                self::$origin['id'] = $field['id'];

                $evaluated = EvalString::evaluate(
                    self::$registry,
                    $data['equation'],
                    array(
                        'search' => $args_vars,
                        'replace' => $args_values,
                        'origin' => self::$origin,
                    ));
            }

            $evaluated = self::format($data['format'] ?? '', $evaluated);

            // remove whitespaces
            $evaluated = trim($evaluated);

            //always put the name of variable in assoc array
            $result = array($field['name'] => $evaluated);

            return $result;
        }

        return $result;
    }

    /**
     * Parses the field source and returns group fileds to update (update_fields)
     *
     * @param array $field - the variable to be prepared, the array should be as fetched from the fields_meta DB table
     * @return array $update_fields - list of update fields (supposed to be fields in group table)
     */
    private function _getGroupFieldsToUpdate($field) {
        $update_fields = array();

        $source = preg_split('/(\n|\r|\r\n)/', $field['source']);
        foreach ($source as $srow) {
            if (empty($srow) || $srow[0] == '#') {
                //empty or commented row
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $srow);
            if ($key == 'update_fields') {
                //grouping table fields/columns
                $update_fields = preg_split('/\s*,\s*/', $value);
                //IMPORTANT: get the LAST uncommented setting updated_fields found
                // DO NOT break this foreach
            }
        }

        return $update_fields;
    }

    /**
     * Prepares equation, format, update fields and evaluates arguments (equation variables)
     *
     * @param array $field - the variable to be prepared, the array should be as fetched from the fields_meta DB table
     * @return array $data - the calculation array containing equation, update fields, array function, format, etc.
     */
    private function _prepareCalculationData($field) {
        $data = array(
            'equation' => '',
            'last_equation' => '',
            'format' => '',
            //default array function is sum, the another possible is avg
            'array_function' => '',
            'num_rows' => '',
            'update_fields' => array(),
            'args' => array(
                '$model_id' => self::$origin['model_id']
            ),
        );

        $source = preg_split('/(\n|\r|\r\n)/', $field['source']);
        foreach ($source as $srow) {
            if (empty($srow) || $srow[0] == '#') {
                //empty or commented row
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $srow);
            if ($key == 'equation') {
                //expression for calculation
                $data['equation'] = $value;
            } elseif ($key == 'last_equation') {
                $data['last_equation'] = $value;
            } elseif ($key == 'array_function') {
                //aggregate function: sum, avg
                $data['array_function'] = $value;
            } elseif ($key == 'update_fields') {
                //grouping table fields/columns
                $data['update_fields'] = preg_split('/\s*,\s*/', $value);
            } elseif ($key == 'format') {
                //format
                $data['format'] = $value;
            } elseif ($key == 'num_rows') {
                //num_rows it can be omitted when update_fields is used
                //IMPORTANT: num_rows should always be ONE variable name, get the first one if several listed
                $num_rows = preg_split('/\s*,\s*/', $value);
                $data['num_rows'] = trim($num_rows[0]);
            } else {
                //prepare variables (arguments)
                if (strpos($value, '(')) {
                    // set id of the grouping table field
                    // IMPORTANT: it should be restored with the original field id outside this function
                    self::$origin['id'] = $field['id'];
                    //evaluate argument - request, sql ...
                    $value = self::calc_value(self::$registry, $data['args'], $value);
                    if (is_array($value)) {
                        foreach ($value as $i => $val) {
                            if (isset($data['args'][$val])) {
                                $value[$i] = $data['args'][$val];
                            }
                        }
                    } else {
                        if (isset($data['args'][$value])) {
                            $value = $data['args'][$value];
                        }
                    }
                }
                //make sure the empty string is returned as zero
                if (empty($value)) {
                    $value = 0;
                }

                //array of variable => value for replace in $equation
                $data['args'][$key] = $value;
            }
        }

        return $data;
    }

    /**
     * Format result key as field name with row index: var_name[pos],
     * not as field id, because row index and id suffix might not match
     *
     * @param array $group_fields - array calculated group fields
     * @return array $result
     */
    private function _reindexGroupTableFields($group_fields) {
        $result = array();
        if (is_array($group_fields) && !empty($group_fields)) {
            foreach($group_fields as $group_field_name => $rows) {
                foreach($rows as $idx => $val) {
                    // IMPORTANT: format result key as field name with row index: var_name[pos],
                    // not as field id, because row index and id suffix might not match
                    $result[$group_field_name . '[' . ($idx) . ']'] = $val;
                }
            }
        }

        return $result;
    }

    /**
     * Calculates the group table fields
     *
     * @param array $field - the variable that starts the calculations of group fields
     * @param array $update_fields - list of group fields to be updated
     * @return array $results - two dimensional assoc array containing field name and row values as subarray
     */
    private function _calculateGroupTableFields($field, $update_fields) {
        $db = self::$registry['db'];

        // initialize the return variable
        $results = array();

        $name_matches = array();
        //In BB there are inner calculation variables with model BBDocument, BBCustomer, BBNomenclature, etc.
        // if calculatable field is in bb, model of field is different
        // from real model name so try to get it from the inner grouping varaible
        if ($field['bb'] && preg_match('#(.+)_calc$#', $field['name'], $name_matches)) {
            $model_name = $db->GetOne(
                'SELECT model FROM ' . DB_TABLE_FIELDS_META .
                " WHERE name = '{$name_matches[1]}' AND model_type = '{$field['model_type']}'"
            ) ?: $field['model'];
        } else {
            $model_name = $field['model'];
        }

        //get source for grouping fields to update
        //IMPORTANT: the update_fields are not checked whether they are in group tables,
        //           but they are supposed to be such
        $query = 'SELECT fm.* FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' .
                 'WHERE name in ' . "('" . implode("','", $update_fields) . "') " .
                 ' AND model_type=' . "'" . $field['model_type'] . "'" .
                 ' AND model=' . "'" . $model_name . "'" . "\n" .
                 'ORDER BY find_in_set(name, "'.implode(',',$update_fields).'")';
        $group_fields = $db->GetAll($query);

        foreach ($group_fields as $group_field) {
            // prepare calculation data
            $data = self::_prepareCalculationData($group_field);

            //arrays with equation arguments to replace
            $args_vars = array();
            if (!empty($data['args'])) {
                $args_vars = array_keys($data['args']);
            }

            // get $num_rows directly from the request, ignoring the setting num_rows in $data
            $num_rows = self::$registry['request']->get($group_field['name']);
            //$num_rows = self::getGroupNumRows($group_field);

            if (!empty($num_rows)) {
                //evaluate the group field in each row
                foreach ($num_rows as $idx => $not_important) {
                    //prepare arguments (equation variables)
                    $args_values = array_values($data['args']);
                    foreach ($args_values as $key => $val) {
                        if (is_array($val)) {
                            if (isset($val[$idx]) && $val[$idx]) {
                                //set row value for replace
                                $args_values[$key] = $val[$idx];
                            } else {
                                $args_values[$key] = 0;
                            }
                        } elseif (!$val) {
                            $args_values[$key] = 0;
                        }
                    }

                    if (empty($data['equation'])) {
                        $evaluated = '';
                    } else {
                        // set id of group field
                        self::$origin['id'] = $group_field['id'];

                        $evaluated = EvalString::evaluate(
                            self::$registry,
                            $data['equation'],
                            array(
                                'search' => $args_vars,
                                'replace' => $args_values,
                                'origin' => self::$origin,
                            ));
                    }

                    $evaluated = self::format($data['format'] ?? '', $evaluated);

                    // remove whitespaces
                    $evaluated = trim($evaluated);

                    // prepare variables in assoc array
                    $results[$group_field['name']][$idx] = $evaluated;

                }
            }

            // store the array results in the request to be reusable in the rest of the calculations
            // IMPORTANT: do it only if the field contains equation setting
            if (!empty($data['equation']) && isset($results[$group_field['name']])) {
                self::$registry['request']->set($group_field['name'], $results[$group_field['name']], 'all', true);
            }

        }

        return $results;
    }

    /**
     * Get request variable
     *
     * @param Registry $registry
     * @param string $params - name of variable
     * @return mixed - result, request value for params
     */
    public static function calc_request(&$registry, $params) {
        return $registry['request']->get($params);
    }

    /**
     * calculate sql
     *
     * @param Registry $registry
     * @param string $params - SQL query to execute
     * @return string - result of query
     */
    public static function calc_sql(&$registry, $params) {
        $db = $registry['db'];
        $query = $params;//return $query;
        $records = $db->GetOne($query);
        return $records;
    }

    /**
     * calculate value
     *
     * @param Registry $registry
     * @param array $f_args - array variable=>value
     * @param array|string $value
     * @return array|string - value after performed calculations
     */
    public static function calc_value(&$registry, &$f_args, $value) {
        //calculate function(args)
        $val_arr = array();

        foreach ($f_args as $k => $arg) {
            if (!is_array($arg) && !is_object($arg)) {
                if (!is_array($value)) {
                    $value = str_replace($k, $arg, $value);
                } else {
                    //replace single variables in array variable
                    foreach ($value as $idx => $v) {
                        $cnt = 0;
                        $v = str_replace($k, $arg, $v, $cnt);
                        if ($cnt) {
                            $val_arr[$idx] = $value[$idx] = $v;
                        }
                    }
                }
            } elseif (is_array($arg)) {
                //replace for array variables
                foreach ($arg as $subk => $subarg) {
                    if (!is_array($subarg)) {
                        if (is_array($value) && isset($value[$subk])) {
                            $tmp = $value[$subk];
                        } else {
                            $tmp = $value;
                        }
                        $count = 0;
                        $val = str_replace($k, $subarg, $tmp, $count);
                        if ($count) {
                            $val_arr[$subk] = $val;
                        }
                    }
                }
                if (count($val_arr)) {
                    $value = $val_arr;
                }
            }
        }

        if (is_array($value)) {
            //calculate array of values
            foreach ($value as $subk => $subval) {
                self::calc_eval($registry, $value[$subk]);
            }
        } else {
            //calculate value
            self::calc_eval($registry, $value);
        }

        return $value;
    }

    /**
     * Performs evaluation (execution) of the passed string if it starts with an
     * existing method of the class, otherwise does nothing
     *
     * @param Registry $registry - the main registry
     * @param string $value - string to evaluate
     */
    private static function calc_eval(&$registry, &$value = '') {
        $class = __CLASS__;
        if (method_exists($class, 'calc_' . trim(substr($value, 0, strpos($value, '('))))) {
            $value = substr_replace($value, '($registry,', strpos($value, '('), 1);
            $value = EvalString::evaluate(
                $registry,
                "{$class}::calc_{$value}",
                array(
                    'origin' => self::$origin,
                )
            );
        }
    }

    /**
     * get file tree node
     *
     * @param Registry $registry
     * @return string - JSON representation of a value
     */
    public static function _getNode(&$registry) {
        $dir = PH_RESOURCES_DIR . $registry['request']->get('get_node');
        $url_dir = PH_RESOURCES_URL . $registry['request']->get('get_node');

        $dir_content = array('children'=>array());
        if ($handle = opendir($dir)) {
            while (false !== ($file = readdir($handle))) {
                //exclude SVN files and .ht* Apache files
                if ($file != "." && $file != ".." && !preg_match('#svn#', $file) && !preg_match('#^\.ht#', $file)) {
                    if (is_dir($dir . '/' . $file)) {
                        $dir_content['children'][] = array(
                            'label'=>$file,
                            "sourceType"=>'json/url',
                            "source"=>$_SERVER['PHP_SELF']."?".Router::MODULE_PARAM."=files&files=calculate&get_node=".$registry['request']->get('get_node')."/".$file);
                    } else {
                        $dir_content['children'][] = array('label' => '<a href="'.$url_dir.'/'.$file.'">'.$file.'</a>');
                    }
                }
            }
            closedir($handle);
        }
        $result = json_encode($dir_content);

        return $result;
    }

    private static function getGroupNumRows($field)
    {
        return self::$registry['request']->get($field['name']);
        /*
        $query = 'SELECT fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' .
                 'WHERE `grouping` = ' . $field['grouping'] .
                 ' AND model_type=' . "'" . $field['model_type'] . "'" .
                 ' AND model=' . "'" . $field['model'] . "'";
        $groupFieldNames = self::$registry['db']->GetCol($query);

        $numRows = array();
        foreach($groupFieldNames as $grFieldName) {
            $posted = self::$registry['request']->get($grFieldName);
            if (is_array($posted) && count($posted) > count($numRows)) {
                //values are not important!
                $numRows = array_fill_keys(array_keys($posted), '');
            }
        }

        return $numRows;
        */
    }

    /**
     *  Format results
     * @param string $format - sprintf format
     * @param $result
     * @return string
     */
    public static function format(string $format, $result)
    {
        if (empty($format)) {
            return $result;
        }
        if (preg_match('#\%\.(\d+)F#i', $format, $matches)) {
            $precision = (int) $matches[1];
            $result = round($result * pow(10, $precision)) / pow(10, $precision);
        }

        return sprintf($format, (string) $result);
    }
}
