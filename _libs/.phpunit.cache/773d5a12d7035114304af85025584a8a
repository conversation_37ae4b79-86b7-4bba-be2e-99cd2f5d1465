a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:704;s:7:"methods";a:22:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:47:"__construct(Registry $registry, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:41;s:7:"endLine";i:45;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:52;s:7:"endLine";i:59;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:64;s:7:"endLine";i:99;s:3:"ccn";i:10;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:107;s:7:"endLine";i:148;s:3:"ccn";i:10;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:159;s:7:"endLine";i:197;s:3:"ccn";i:4;}s:22:"createTableFromGT2Data";a:6:{s:10:"methodName";s:22:"createTableFromGT2Data";s:9:"signature";s:119:"createTableFromGT2Data(Model $model, string $varName, array $gt2Data, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:208;s:7:"endLine";i:242;s:3:"ccn";i:3;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:133:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:254;s:7:"endLine";i:286;s:3:"ccn";i:6;}s:25:"getOrCreateGT2TableHeader";a:6:{s:10:"methodName";s:25:"getOrCreateGT2TableHeader";s:9:"signature";s:91:"getOrCreateGT2TableHeader(string $tableType, array $vars): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:295;s:7:"endLine";i:325;s:3:"ccn";i:5;}s:21:"sortGT2VarsByPosition";a:6:{s:10:"methodName";s:21:"sortGT2VarsByPosition";s:9:"signature";s:41:"sortGT2VarsByPosition(array $vars): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:333;s:7:"endLine";i:354;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:362;s:7:"endLine";i:369;s:3:"ccn";i:1;}s:27:"convertFieldTypeToValueType";a:6:{s:10:"methodName";s:27:"convertFieldTypeToValueType";s:9:"signature";s:54:"convertFieldTypeToValueType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:377;s:7:"endLine";i:401;s:3:"ccn";i:16;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:409;s:7:"endLine";i:436;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:149:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $types, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:448;s:7:"endLine";i:456;s:3:"ccn";i:3;}s:24:"populateTableFromGT2Data";a:6:{s:10:"methodName";s:24:"populateTableFromGT2Data";s:9:"signature";s:114:"populateTableFromGT2Data(Nzoom\Export\Entity\ExportTable $table, array $values, array $vars, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:466;s:7:"endLine";i:477;s:3:"ccn";i:3;}s:26:"createRecordFromGT2RowData";a:6:{s:10:"methodName";s:26:"createRecordFromGT2RowData";s:9:"signature";s:112:"createRecordFromGT2RowData(array $rowData, array $sortedVars, array $options): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:487;s:7:"endLine";i:506;s:3:"ccn";i:4;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:133:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $options): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:518;s:7:"endLine";i:542;s:3:"ccn";i:5;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:555;s:7:"endLine";i:591;s:3:"ccn";i:15;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:596;s:7:"endLine";i:603;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:608;s:7:"endLine";i:617;s:3:"ccn";i:2;}s:15:"shouldSkipTable";a:6:{s:10:"methodName";s:15:"shouldSkipTable";s:9:"signature";s:61:"shouldSkipTable(Nzoom\Export\Entity\ExportTable $table): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:625;s:7:"endLine";i:636;s:3:"ccn";i:2;}s:15:"isTableRowEmpty";a:6:{s:10:"methodName";s:15:"isTableRowEmpty";s:9:"signature";s:63:"isTableRowEmpty(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:644;s:7:"endLine";i:655;s:3:"ccn";i:3;}s:18:"isValueEmptyOrZero";a:6:{s:10:"methodName";s:18:"isValueEmptyOrZero";s:9:"signature";s:32:"isValueEmptyOrZero($value): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:663;s:7:"endLine";i:703;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:705;s:18:"commentLinesOfCode";i:218;s:21:"nonCommentLinesOfCode";i:487;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:241:{i:41;i:4;i:43;i:5;i:44;i:6;i:54;i:7;i:55;i:7;i:56;i:7;i:57;i:7;i:58;i:7;i:64;i:8;i:66;i:9;i:67;i:10;i:69;i:11;i:70;i:12;i:75;i:13;i:77;i:14;i:79;i:15;i:80;i:16;i:82;i:17;i:85;i:18;i:86;i:19;i:89;i:20;i:91;i:21;i:92;i:22;i:93;i:22;i:94;i:22;i:98;i:23;i:109;i:24;i:115;i:25;i:116;i:26;i:120;i:27;i:121;i:28;i:122;i:29;i:125;i:30;i:128;i:31;i:129;i:32;i:133;i:33;i:134;i:34;i:135;i:35;i:138;i:36;i:140;i:37;i:141;i:38;i:142;i:38;i:143;i:38;i:147;i:39;i:163;i:40;i:164;i:41;i:165;i:42;i:166;i:43;i:167;i:44;i:169;i:45;i:170;i:46;i:174;i:47;i:177;i:48;i:180;i:49;i:181;i:49;i:182;i:49;i:183;i:49;i:184;i:49;i:185;i:49;i:186;i:49;i:189;i:50;i:192;i:51;i:193;i:52;i:196;i:53;i:211;i:54;i:212;i:55;i:214;i:56;i:215;i:57;i:219;i:58;i:222;i:59;i:225;i:60;i:226;i:60;i:227;i:60;i:228;i:60;i:229;i:60;i:230;i:60;i:231;i:60;i:234;i:61;i:237;i:62;i:238;i:63;i:241;i:64;i:254;i:65;i:257;i:66;i:258;i:67;i:262;i:68;i:264;i:69;i:266;i:70;i:267;i:71;i:270;i:72;i:272;i:73;i:273;i:74;i:275;i:75;i:278;i:76;i:279;i:77;i:283;i:78;i:285;i:79;i:298;i:80;i:299;i:81;i:303;i:82;i:306;i:83;i:308;i:84;i:310;i:85;i:311;i:86;i:314;i:87;i:315;i:88;i:317;i:89;i:318;i:90;i:322;i:91;i:324;i:92;i:336;i:93;i:337;i:94;i:338;i:95;i:339;i:96;i:343;i:97;i:346;i:98;i:347;i:99;i:348;i:100;i:349;i:101;i:353;i:102;i:365;i:103;i:366;i:104;i:368;i:105;i:380;i:106;i:381;i:107;i:383;i:108;i:384;i:109;i:386;i:110;i:387;i:111;i:388;i:112;i:389;i:113;i:390;i:114;i:391;i:115;i:392;i:116;i:393;i:117;i:394;i:118;i:395;i:119;i:396;i:120;i:397;i:121;i:399;i:122;i:411;i:123;i:414;i:124;i:415;i:125;i:416;i:126;i:418;i:127;i:422;i:128;i:423;i:129;i:424;i:130;i:426;i:131;i:430;i:132;i:431;i:133;i:435;i:134;i:450;i:135;i:451;i:136;i:452;i:137;i:453;i:138;i:469;i:139;i:471;i:140;i:472;i:141;i:473;i:142;i:474;i:143;i:489;i:144;i:491;i:145;i:493;i:146;i:494;i:147;i:498;i:148;i:499;i:149;i:500;i:150;i:502;i:151;i:505;i:152;i:520;i:153;i:522;i:154;i:524;i:155;i:525;i:156;i:529;i:157;i:531;i:158;i:532;i:159;i:534;i:160;i:536;i:161;i:538;i:162;i:541;i:163;i:557;i:164;i:558;i:165;i:562;i:166;i:563;i:167;i:564;i:168;i:565;i:169;i:566;i:170;i:567;i:171;i:569;i:172;i:571;i:173;i:572;i:174;i:573;i:175;i:574;i:176;i:575;i:177;i:576;i:178;i:578;i:179;i:580;i:180;i:581;i:181;i:583;i:182;i:584;i:183;i:586;i:184;i:587;i:185;i:590;i:186;i:599;i:187;i:600;i:187;i:601;i:187;i:602;i:187;i:608;i:188;i:610;i:189;i:611;i:190;i:616;i:191;i:628;i:192;i:629;i:193;i:632;i:194;i:633;i:195;i:635;i:196;i:646;i:197;i:648;i:198;i:649;i:199;i:650;i:200;i:654;i:201;i:666;i:202;i:667;i:203;i:671;i:204;i:672;i:205;i:676;i:206;i:677;i:207;i:681;i:208;i:682;i:209;i:686;i:210;i:687;i:211;i:688;i:211;i:689;i:211;i:690;i:211;i:691;i:211;i:692;i:211;i:693;i:211;i:694;i:211;i:696;i:212;i:697;i:213;i:702;i:214;}}