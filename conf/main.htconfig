[sys]
system = nZoom
version = 1.7.0
build = $Rev: 7482 $
timezone = +2
lock_records = 0
strong_passwords = 1

[users]
max_concurrent_users = 0
max_concurrent_portal_users = 0
max_inactive_time = 120
alert_before_inactive_interval = 15
allow_multiple_sessions = 1

[themes]
default_theme = Default
default_editor = ckeditor

[i18n]
default_lang = bg
default_timezone = Europe/Sofia
supported_langs = bg, en
model_langs = bg, en, de, ru

[modules]
general_modules = auth::login, index::frontend, documents, tasks, customers, projects, organizer, files, nomenclatures::index, reports, patterns, profile, settings, auth::logout
auth_login_modules = auth::login
index_frontend_modules = index::frontend
documents_modules = documents, documents|types, documents|counters, documents|sections, documents|statuses, documents|medias
tasks_modules = tasks, tasks|sections, tasks|types, tasks|counters, tasks|statuses
customers_modules = customers, customers|sections, customers|types
projects_modules = projects, projects|sections, projects|types, stages, stages|phases
organizer_modules = #, calendars, events, notes, announcements, announcements|types, announcements|categories
files_modules = files
nomenclatures_index_modules = nomenclatures::index, nomenclatures|articles, nomenclatures|categories, nomenclatures|pricelists
reports_modules = reports
patterns_modules = patterns, patterns|parts, outlooks, dashlets, helps, emails
profile_modules = #, users::profile, users::mynzoom, users::password
settings_modules = index::backend, users, roles, departments, groups, offices, layouts, tags
auth_logout_modules = auth::logout

[log]
# Set Log Level:
# 0 = NONE
# 1 = DEBUG
# 2 = INFO
# 3 = WARNING
# 4 = ERROR
level = 1
date_format = Y-m-d H:m:s

[emails]
from_name = nZoom Notification System
from_email = <EMAIL>
replyto_name = PLEASE DO NOT REPLY
replyto_email = <EMAIL>
replyto_imap_server = imap.bgservice.net
replyto_imap_port = 
replyto_imap_password = Phiitai8
do_not_send = 1

[company_info]
company = 
address = 
phone = 
fax = 
url = 
email = 
contact_person = 
bulstat = 
taxnumber = 

[header]
logo = 
info_color = 

[mailer]
type = smtp

#Define smtp details if the mailer is SMTP
#IMPORTANT: remove the SMTP section if the mailer is not SMTP
[smtp]
host = localhost
#user = 
#pass = 

#define sendmail path if the mailer is sendmail
#IMPORTANT: remove the sendmail section if the mailer is not sendmail
[sendmail]
path = /usr/sbin/sendmail

[levenshtein]
similar = 335


